{"name": "qcc-rover-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"unittest:prepare": "cd script/test && sh prepareForUnittest.sh", "debug": "ts-node-dev --no-deps -r tsconfig-paths/register --respawn ./src/main.ts", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "env:encrypt": "dotenvx encrypt", "env:decrypt": "dotenvx decrypt", "start:dev": "yarn build && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:sonar": "eslint \"{src,apps,libs,test}/**/*.ts\" --config .eslintrc.sonar.js --fix --quiet", "test": "jest", "test:list": "jest --onlyChanged --listTests --detectOpenHandles", "test:changes": "jest --only<PERSON>hanged --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch", "test:cov": "jest --coverage --maxConcurrency 5", "test:changed": " MOCK_MESSAGE_QUEUE='true' jest --coverage --onlyChanged --maxConcurrency 1", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand --detectOpenHandles", "test:e2e": "jest --config ./test/jest-e2e.json", "sonar": "sonar-scanner", "test:report": "open ./coverage/report.html && open ./coverage/lcov-report/index.html", "doc": " compodoc -p tsconfig.doc.json -n \"Rover Service\"", "doc:view": "open ./documentation/index.html"}, "dependencies": {"nestjs-throttler-storage-redis": "^0.2.2", "@nestjs/throttler": "^3.0.0", "@dotenvx/dotenvx": "^1.14.0", "@elastic/elasticsearch": "7.x", "@godaddy/terminus": "^4.2.1", "@kezhaozhao/company-search-api": "9.5.5", "@kezhaozhao/dd-platform-service": "0.2.29", "@kezhaozhao/message-queue": "^9.7.6", "@kezhaozhao/nest-mailer": "^9.0.0", "@kezhaozhao/nest-sentinel": "^9.0.0", "@kezhaozhao/nestjs-redis": "^2.0.1", "@kezhaozhao/qcc-logger": "^4.8.22", "@kezhaozhao/qcc-model": "^9.0.0", "@kezhaozhao/qcc-utils": "9.3.38", "@kezhaozhao/saas-auth": "^9.2.46", "@kezhaozhao/saas-bundle-service": "2.6.9", "@kezhaozhao/search-utils": "^9.0.0", "@nestjs/axios": "^2.0.0", "@nestjs/common": "^9.0.0", "@nestjs/core": "^9.0.0", "@nestjs/event-emitter": "^2.0.4", "@nestjs/platform-express": "^9.0.0", "@nestjs/platform-socket.io": "^9.0.0", "@nestjs/swagger": "^6.2.1", "@nestjs/terminus": "^9.2.1", "@nestjs/typeorm": "^9.0.0", "@nestjs/websockets": "^9.0.0", "@sentry/integrations": "6.0.3", "@sentry/node": "6.0.3", "@sentry/tracing": "6.0.3", "@socket.io/redis-adapter": "^8.3.0", "@type-cacheable/core": "^10.1.2", "@type-cacheable/ioredis-adapter": "^11.0.1", "@type-cacheable/lru-cache-adapter": "^11.0.3", "archiver": "^6.0.1", "axios": "0.24.0", "bluebird": "^3.7.2", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cls-hooked": "^4.2.2", "compression": "^1.7.4", "cookie-parser": "^1.4.5", "diff": "^5.1.0", "eslint-plugin-sonarjs": "^0.23.0", "exceljs": "^4.3.0", "handlebars": "^4.7.6", "hbs": "^4.2.0", "helmet": "^6.0.1", "http-proxy": "^1.18.1", "ioredis": "^5.3.2", "is-promise": "^4.0.0", "lodash": "^4.17.15", "log4js": "^6.2.1", "lru-cache": "^7.14.0", "moment": "^2.25.3", "multer": "^1.4.2", "nanoid": "^3.1.22", "nest-schedule": "^0.6.4", "node-cache": "^5.1.0", "node-html-markdown": "^1.3.0", "node-xlsx": "^0.17.2", "object-hash": "^2.2.0", "pulsar-client": "^1.11.1", "reflect-metadata": "^0.1.14", "rimraf": "^3.0.2", "rxjs": "^7.8.0", "socket.io": "^4.7.5", "swagger-ui-express": "^4.1.4", "typeorm": "^0.2.24", "uuid-time": "^1.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@compodoc/compodoc": "^1.1.11", "@faker-js/faker": "^8.4.1", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/ali-oss": "^6.0.7", "@types/express": "^4.17.3", "@types/http-proxy": "^1.17.4", "@types/jest": "25.1.4", "@types/lodash": "^4.14.177", "@types/node": "^13.13.52", "@types/node-xlsx": "^0.15.2", "@types/supertest": "^2.0.8", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^4.2.5", "jest": "^29.7.0", "jest-html-reporters": "^3.1.7", "jest-sonar-reporter": "^2.0.0", "lint-staged": "^9.2.3", "prettier": "^2.0.2", "sonarqube-scanner": "^2.6.0", "supertest": "^4.0.2", "ts-jest": "^29.1.4", "ts-loader": "^9.2.3", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.9.5"}, "jest": {"setupFiles": ["../jest.setup.js"], "forceExit": true, "testResultsProcessor": "jest-sonar-reporter", "moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"allowJs": true}]}, "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^libs/config(|/.*)$": "<rootDir>/libs/config/$1", "^libs/*(|/.*)$": "<rootDir>/libs/$1", "^apps/*(|/.*)$": "<rootDir>/apps/$1"}, "reporters": ["default", ["jest-html-reporters", {"publicPath": "./coverage/html-report", "filename": "index.html", "openReport": false, "title": "Rover Service Test Report", "stripSkippedTest": true}]]}, "jestSonar": {"reportPath": "coverage", "reportFile": "sonar-reporter.xml", "indent": 4}, "resolutions": {"@kezhaozhao/qcc-utils": "9.3.38", "@kezhaozhao/qcc-logger": "^4.8.22", "axios": "0.24.0", "redlock": "5.0.0-beta.2"}}