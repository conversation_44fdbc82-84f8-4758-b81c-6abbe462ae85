INSERT INTO `bundle_fields` (`id`, `name`, `product`, `category`, `count_cycle`, `default_value`, `field_key`) VALUES (NULL, '批量年检数量', 'SAAS_ROVER', '0', '0', '500', 'batchInspectionQuantity');
INSERT INTO `bundle_fields` (`id`, `name`, `product`, `description`, `category`, `count_cycle`, `default_value`, `field_key`) VALUES (NULL, '尽调公司数量', 'SAAS_ROVER', '套餐生命周期内可尽调的公司的数量，同一家公司不收费', '0', '0', '2000', 'diligenceCompanyQuantity');
INSERT INTO `bundle_fields` (`id`, `name`, `product`, `description`, `category`, `count_cycle`, `default_value`, `field_key`) VALUES (NULL, '尽调记录数量', 'SAAS_ROVER', '套餐生命周期内可以尽调的次数(同一家公司一天只算一次)', '0', '0', '200000', 'diligenceHistoryQuantity');
INSERT INTO `bundle_fields` (`id`, `name`, `product`, `category`, `count_cycle`, `default_value`, `field_key`) VALUES (NULL, '尽调报告数量', 'SAAS_ROVER', '0', '0', '20000', 'diligenceReportQuantity');
INSERT INTO `bundle_fields` (`id`, `name`, `product`, `category`, `count_cycle`, `default_value`, `field_key`) VALUES (NULL, '内部黑名单数量', 'SAAS_ROVER', '0', '0', '1000', 'innerBlacklistQuantity');
INSERT INTO `bundle_fields` (`id`, `name`, `product`, `category`, `count_cycle`, `default_value`, `field_key`) VALUES (NULL, '人员数量', 'SAAS_ROVER', '0', '0', '1000', 'personQuantity');
INSERT INTO `bundle_fields` (`id`, `name`, `product`, `category`, `count_cycle`, `default_value`, `field_key`) VALUES (NULL, '第三方数量', 'SAAS_ROVER', '0', '0', '1000', 'thirdPartyQuantity');
