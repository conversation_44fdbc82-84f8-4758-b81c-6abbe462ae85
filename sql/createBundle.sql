INSERT INTO `bundles` (`bundle_id`, `service_code`, `name`, `service_category`, `province`, `type`, `version`, `parameters`, `parameters_setting`, `duration`, `price`, `active`, `create_by`, `update_by`) VALUES (NULL, 'SAAS_ROVER', '卡尔蔡司定制版-测试', 'custom', '', '3', '1', '{\"memberLimit\": 50, \"personQuantity\": 1000, \"thirdPartyQuantity\": 1000, \"innerBlacklistQuantity\": 1000, \"batchInspectionQuantity\": 1000, \"diligenceReportQuantity\": 10000, \"diligenceCompanyQuantity\": 20000, \"diligenceHistoryQuantity\": 2000000}', '{}', '5', '0.00', '1', '0', '0');