<section class='page'>
  <h2 class="heading page-heading">四、基本信息</h2>
{{#if companyInfo.IsOrganism}}
  <h3 class="heading page-heading">4.1、工商信息</h3>
  <table class='plain-table'>
    <colgroup>
      <col style='width: 20%;' />
      <col />
    </colgroup>
    <tbody>
      <tr>
        <th class="tar">企业名称</th>
        <td>{{companyInfo.Name}}</td>
      </tr>
      <tr>
        <th class="tar">英文名称</th>
        <td>{{companyInfo.EnglishName}}</td>
      </tr>
      <tr>
        <th class="tar">曾用名</th>
        <td>{{companyInfo.OriginalName.0.Name}}</td>
      </tr>
      <tr>
        <th class="tar">登记状态</th>
        <td>{{companyInfo.Status}}</td>
      </tr>
      <tr>
        <th class="tar">统一社会信用代码</th>
        <td>{{companyInfo.CreditCode}}</td>
      </tr>
      <tr>
        <th class="tar">{{get_oper_name_by_type companyInfo}}</th>
        <td>{{companyInfo.Oper.Name}}</td>
      </tr>
      <tr>
        <th class="tar">{{get_regist_capi_label companyInfo}}</th>
        <td>{{companyInfo.RegistCapi}}</td>
      </tr>

      
      <tr>
        <th class="tar">社会组织类型</th>
        <td>{{companyInfo.EconKind}}</td>
      </tr>
      <tr>
        <th class="tar">成立日期</th>
        <td>{{date_format companyInfo.StartDate}}</td>
      </tr>
      
      <tr>
        <th class="tar">住所</th>
        <td>{{companyInfo.Address}}</td>
      </tr>
      <tr>
        <th class="tar">业务主管单位</th>
        <td>{{companyInfo.BelongOrg}}</td>
      </tr>
      <tr>
        <th class="tar">登记管理机关</th>
        <td>{{companyInfo.FazhengAuthority}}</td>
      </tr>
      <tr>
        <th class="tar">证书有效期</th>
        <td>{{companyInfo.CertDate}}</td>
      </tr>
      <tr>
        <th class="tar">证书状态</th>
        <td>{{companyInfo.CertificateStatus}}</td>
      </tr>
      <tr>
        <th class="tar">业务范围</th>
        <td>{{companyInfo.Scope}}</td>
      </tr>
      
    </tbody>
  </table>

  


  <h3 class="heading page-heading">4.2、主要人员</h3>
  {{#if companyInfo.IpoEmployees.Result}}
    {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneEmployeesOrg') dataSource=(limit_by companyInfo.IpoEmployees.Result size=10) index=true}}
    {{> risk_footnote total=companyInfo.IpoEmployees.Paging.TotalRecords limit=10}}
  {{else}}
    {{#if companyInfo.Employees.Result}}
      {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneEmployeesOrg') dataSource=(limit_by companyInfo.Employees.Result size=10) index=true}}
      {{> risk_footnote total=companyInfo.Employees.Paging.TotalRecords limit=10}}
    {{else}}
      {{> risk_empty diligenceInfo=diligenceInfo}}
    {{/if}}
  {{/if}}

  <h3 class="heading page-heading">4.3、变更记录</h3>
  {{#if companyInfo.ChangeDiffInfo.ChangeList}}
    {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneChangeDiffInfo') dataSource=(limit_by companyInfo.ChangeDiffInfo.ChangeList size=10) index=true}}
    {{> risk_footnote total=companyInfo.ChangeDiffInfo.TotalCount limit=10}}
  {{else}}
    {{> risk_empty diligenceInfo=diligenceInfo}}
  {{/if}}
  {{!-- 此维度共计  条记录，当前报告中显示 10 条，如需更多请登录官方网站进行查询。 --}}

  <h3 class="heading page-heading">4.4、控制企业</h3>
  {{#if companyInfo.HoldingCompany.Names}}
    {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneHoldingCompany') dataSource=(limit_by companyInfo.HoldingCompany.Names size=10) originalData=companyInfo index=true}}
    {{> risk_footnote total=companyInfo.HoldingCompany.NameCount limit=10}}
  {{else}}
    {{> risk_empty diligenceInfo=diligenceInfo}}
  {{/if}}

{{else}}
  <h3 class="heading page-heading">4.1、工商信息</h3>
  <table class='plain-table'>
    <colgroup>
      <col style='width: 20%;' />
      <col />
    </colgroup>
    <tbody>
      <tr>
        <th class="tar">企业名称</th>
        <td>{{companyInfo.Name}}</td>
      </tr>
      <tr>
        <th class="tar">英文名称</th>
        <td>{{companyInfo.EnglishName}}</td>
      </tr>
      <tr>
        <th class="tar">曾用名</th>
        {{!-- ? --}}
        <td>{{companyInfo.OriginalName.0.Name}}</td>
      </tr>
      <tr>
        <th class="tar">登记状态</th>
        <td>{{companyInfo.ShortStatus}}</td>
      </tr>
      <tr>
        <th class="tar">统一社会信用代码</th>
        <td>{{companyInfo.CreditCode}}</td>
      </tr>
      <tr>
        <th class="tar">纳税人识别代码</th>
        <td>{{companyInfo.TaxNo}}</td>
      </tr>
      <tr>
        <th class="tar">注册号</th>
        <td>{{companyInfo.No}}</td>
      </tr>
      <tr>
        <th class="tar">组织机构代码</th>
        <td>{{companyInfo.OrgNo}}</td>
      </tr>
      <tr>
        <th class="tar">{{get_oper_name_by_type companyInfo}}</th>
        <td>{{companyInfo.Oper.Name}}</td>
      </tr>
      <tr>
        <th class="tar">{{get_regist_capi_label companyInfo}}</th>
        <td>{{companyInfo.RegistCapi}}</td>
      </tr>
      <tr>
        <th class="tar">实缴资本</th>
        <td>{{companyInfo.RecCap}}</td>
      </tr>
      <tr>
        <th class="tar">所属行业</th>
        <td>{{companyInfo.Industry.Industry}}</td>
      </tr>
      <tr>
        <th class="tar">企业类型</th>
        <td>{{companyInfo.EconKind}}</td>
      </tr>
      <tr>
        <th class="tar">成立日期</th>
        <td>{{date_format companyInfo.StartDate}}</td>
      </tr>
      <tr>
        <th class="tar">核准日期</th>
        <td>{{date_format companyInfo.CheckDate}}</td>
      </tr>
      <tr>
        <th class="tar">营业期限</th>
        <td>
          {{date_format companyInfo.TermStart default='***'}} 至 
          {{#if (is_equal companyInfo.TeamEnd 0)}}
            无固定期限
          {{else}}
            {{date_format companyInfo.TeamEnd}}
          {{/if}}
        </td>
      </tr>
      <tr>
        <th class="tar">纳税人资质</th>
        <td>{{companyInfo.TaxpayerType}}</td>
      </tr>
      <tr>
        <th class="tar">登记机关</th>
        <td>{{companyInfo.BelongOrg}}</td>
      </tr>
      <tr>
        <th class="tar">注册地址</th>
        <td>{{companyInfo.Address}}</td>
      </tr>
      <tr>
        <th class="tar">经营范围</th>
        <td>{{companyInfo.Scope}}</td>
      </tr>
    </tbody>
  </table>

  <h3 class="heading page-heading">4.2、股东信息</h3>

  {{#if companyInfo.IpoPartners.Result}}
    {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneIPOPartners') dataSource=(limit_by companyInfo.IpoPartners.Result size=10) index=true}}
    {{> risk_footnote total=companyInfo.IpoPartners.Paging.TotalRecords limit=10}}
  {{else}}
    {{#if companyInfo.Partners.Result}}
      {{> rich_table columns=(get_columns_by_dimension_key 'StandalonePartners') dataSource=companyInfo.Partners.Result index=true}}
      {{> risk_footnote total=companyInfo.Partners.Paging.TotalRecords limit=10}}
    {{else}}
      {{> risk_empty diligenceInfo=diligenceInfo}}
    {{/if}}
  {{/if}}


  <h3 class="heading page-heading">4.3、主要人员</h3>
  {{#if companyInfo.IpoEmployees.Result}}
    {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneEmployees') dataSource=(limit_by companyInfo.IpoEmployees.Result size=10) index=true}}
    {{> risk_footnote total=companyInfo.IpoEmployees.Paging.TotalRecords limit=10}}
  {{else}}
    {{#if companyInfo.Employees.Result}}
      {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneEmployees') dataSource=(limit_by companyInfo.Employees.Result size=10) index=true}}
      {{> risk_footnote total=companyInfo.Employees.Paging.TotalRecords limit=10}}
    {{else}}
      {{> risk_empty diligenceInfo=diligenceInfo}}
    {{/if}}
  {{/if}}

  <h3 class="heading page-heading">4.4、分支机构</h3>
  {{#if companyInfo.Branches.Result}}
    {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneBranches') dataSource=(limit_by companyInfo.Branches.Result size=10) index=true}}
    {{> risk_footnote total=companyInfo.Branches.Paging.TotalRecords limit=10}}
  {{else}}
    {{> risk_empty diligenceInfo=diligenceInfo}}
  {{/if}}
  {{!-- 此维度共计  条记录，当前报告中显示 10 条，如需更多请登录官方网站进行查询。 --}}

  <h3 class="heading page-heading">4.5、变更记录</h3>
  {{#if companyInfo.ChangeDiffInfo.ChangeList}}
    {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneChangeDiffInfo') dataSource=(limit_by companyInfo.ChangeDiffInfo.ChangeList size=10) index=true}}
    {{> risk_footnote total=companyInfo.ChangeDiffInfo.TotalCount limit=10}}
  {{else}}
    {{> risk_empty diligenceInfo=diligenceInfo}}
  {{/if}}
  {{!-- 此维度共计  条记录，当前报告中显示 10 条，如需更多请登录官方网站进行查询。 --}}

  <h3 class="heading page-heading">4.6、实际控制人</h3>
  {{#if (is_equal companyInfo.CountInfo.NACCount 1)}}
    <p class='footnote'>
      公示信息：无实际控制人（来自本公司公告或其他公开数据源）。
    </p>
  {{else}}
    {{#if companyInfo.ActualController}}
      {{#if companyInfo.ActualController.actual}}
        {{> rich_table columns=(get_columns_by_dimension_key companyInfo.ActualController.type) dataSource=companyInfo.ActualController.actual originalData=companyInfo index=true}}
      {{else}}
        {{#if companyInfo.ActualController.yisiActual}}
          {{> rich_table columns=(get_columns_by_dimension_key companyInfo.ActualController.type) dataSource=companyInfo.ActualController.yisiActual originalData=companyInfo index=true}}
        {{else}}
          {{> risk_empty diligenceInfo=diligenceInfo}}
        {{/if}}
      {{/if}}
    {{else}}
      {{> risk_empty diligenceInfo=diligenceInfo }}
    {{/if}}
  {{/if}}

  <h3 class="heading page-heading">4.7、最终受益人</h3>

  <div>
    <div class="h4">
      <span>
        受益所有人{{#if companyInfo.BeneficialOwner.NamesCount}}（{{companyInfo.BeneficialOwner.NamesCount}}）{{/if}}
      </span>
    </div>
  </div>

  {{#if companyInfo.BeneficialOwner.Names }}
    {{> rich_table columns=(get_columns_by_dimension_key 'BeneficialOwner') dataSource=companyInfo.BeneficialOwner.Names index=true}}
  {{else}}
    {{> risk_empty diligenceInfo=diligenceInfo}}
  {{/if}}

  <div>
    <div class="h4">
      <span>
        受益自然人{{#if companyInfo.BeneficialNaturalPerson.NamesCount}}（{{companyInfo.BeneficialNaturalPerson.NamesCount}}）{{/if}}
      </span>
    </div>
  </div>

  {{#if companyInfo.BeneficialNaturalPerson.Names }}
    {{> rich_table columns=(get_columns_by_dimension_key 'BeneficialNaturalPerson') dataSource=companyInfo.BeneficialNaturalPerson.Names index=true}}
  {{else}}
    {{> risk_empty diligenceInfo=diligenceInfo}}
  {{/if}}

  <h3 class="heading page-heading">4.8、控制企业</h3>
  {{#if companyInfo.HoldingCompany.Names}}
    {{> rich_table columns=(get_columns_by_dimension_key 'StandaloneHoldingCompany') dataSource=(limit_by companyInfo.HoldingCompany.Names size=10) originalData=companyInfo index=true}}
    {{> risk_footnote total=companyInfo.HoldingCompany.NameCount limit=10}}
  {{else}}
    {{> risk_empty diligenceInfo=diligenceInfo}}
  {{/if}}
{{/if}}
</section>
