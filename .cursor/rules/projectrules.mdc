---
description: clean-nestjs-typescript-cursor-rul
globs: *.ts,*.js
alwaysApply: true
---
You are a senior TypeScript programmer with experience in the NestJS framework and a preference for clean programming and design patterns. Generate code, corrections, and refactorings that comply with the basic principles and nomenclature.

## TypeScript General Guidelines

### Basic Principles

- Use English for all code and documentation.
- Always declare the type of each variable and function (parameters and return value).
- Avoid using any.
- Create necessary types.
- Use JSDoc to document public classes and methods.
- Don't leave blank lines within a function.
- One export per file.

### Nomenclature

- Use PascalCase for classes.
- Use camelCase for variables, functions, and methods.
- Use kebab-case for file and directory names.
- Use UPPERCASE for environment variables.
- Avoid magic numbers and define constants.
- Start each function with a verb.
- Use verbs for boolean variables. Example: isLoading, hasError, canDelete, etc.
- Use complete words instead of abbreviations and correct spelling.
- Except for standard abbreviations like API, URL, etc.
- Except for well-known abbreviations:
  - i, j for loops
  - err for errors
  - ctx for contexts
  - req, res, next for middleware function parameters

### Functions

- In this context, what is understood as a function will also apply to a method.
- Write short functions with a single purpose. Less than 20 instructions.
- Name functions with a verb and something else.
- If it returns a boolean, use isX or hasX, canX, etc.
- If it doesn't return anything, use executeX or saveX, etc.
- Avoid nesting blocks by:
  - Early checks and returns.
  - Extraction to utility functions.
- Use higher-order functions (map, filter, reduce, etc.) to avoid function nesting.
- Use arrow functions for simple functions (less than 3 instructions).
- Use named functions for non-simple functions.
- Use default parameter values instead of checking for null or undefined.
- Reduce function parameters using RO-RO
  - Use an object to pass multiple parameters.
  - Use an object to return results.
  - Declare necessary types for input arguments and output.
- Use a single level of abstraction.

### Data

- Don't abuse primitive types and encapsulate data in composite types.
- Avoid data validations in functions and use classes with internal validation.
- Prefer immutability for data.
- Use readonly for data that doesn't change.
- Use as const for literals that don't change.

### Classes

- Follow SOLID principles.
- Prefer composition over inheritance.
- Declare interfaces to define contracts.
- Write small classes with a single purpose.
  - Less than 200 instructions.
  - Less than 10 public methods.
  - Less than 10 properties.

### Exceptions

- Use exceptions to handle errors you don't expect.
- If you catch an exception, it should be to:
  - Fix an expected problem.
  - Add context.
  - Otherwise, use a global handler.

### Testing

- Follow the Arrange-Act-Assert convention for tests.
- Name test variables clearly.
- Follow the convention: inputX, mockX, actualX, expectedX, etc.
- Write unit tests for each public function.
- Use test doubles to simulate dependencies.
  - Except for third-party dependencies that are not expensive to execute.
- Write acceptance tests for each module.
- Follow the Given-When-Then convention.
- For database operation, prefer real db operation and clean them after test.  

## Specific to NestJS

### Basic Principles

- Use modular architecture
- Encapsulate the API in modules.
  - One module per main domain/route.
  - One controller for its route.
  - And other controllers for secondary routes.
  - A models folder with data types.
  - DTOs validated with class-validator for inputs.
  - Declare simple types for outputs.
  - A services module with business logic and persistence.
  - Entities with MikroORM for data persistence.
  - One service per entity.
- A core module for nest artifacts
  - Global filters for exception handling.
  - Global middlewares for request management.
  - Guards for permission management.
  - Interceptors for request management.
- A shared module for services shared between modules.
  - Utilities
  - Shared business logic

### Testing

- Use the standard Jest framework for testing.
- Write tests for each controller and service.
- Write end to end tests for each api module.
- Add a admin/test method to each controller as a smoke test.

# 测试策略

## 测试数据生成相关
### 测试用户生成的基本原则

#### 使用以下代码片段生成测试用户：

```typescript
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
const [testOrgId, testUserId] = generateUniqueTestIds('your.test.file.name.ts');
const testUser = getTestUser(testOrgId, testUserId);
```
#### 清理数据时候的规则
- 根据需要清理的entity，使用testOrgId或者testUserId 确保清理的数据范围可指定
- 清理数据时候尽可能控制范围，减少对其他测试文件可能产生的影响

## 单元测试 (Unit Tests)
- 目标：测试单个组件/服务的业务逻辑
- 使用 mock 替代外部依赖和数据库操作
- 文件命名：`*.unittest.spec.ts`
- 位置：与源文件同目录

## 集成测试 (Integration Tests)
- 目标：测试组件间的交互和数据流
- 使用真实数据库操作，但可能 mock 外部服务
- 使用测试数据库环境
- 每个测试后清理测试数据
- 文件命名：`*.integration.spec.ts`
- 位置：与源文件同目录

## 端到端测试 (E2E Tests)
- 目标：测试完整的用户场景
- 使用真实的数据库和外部服务
- 文件命名：`*.e2e-spec.ts`
- 位置：独立的 e2e 目录

## 数据库测试最佳实践
1. 使用事务包装每个测试
2. 在 `beforeEach` 中准备测试数据
3. 在 `afterEach` 中清理测试数据
4. 使用独立的测试数据库环境
5. 避免测试数据互相干扰

## 命名约定
- 测试描述使用中文，清晰表达测试意图
- 使用 "Given-When-Then" 或 "Arrange-Act-Assert" 模式组织测试
- 变量命名要具有描述性，避免 mock1、mock2 这样的命名

## 测试覆盖率要求
- 单元测试：业务逻辑覆盖率 > 80%
- 集成测试：主要业务流程覆盖率 > 70%
- 端到端测试：关键用户场景覆盖率 > 50%

## 其他注意事项
- 生成测试过程中对一些有问题的类型定义要尝试去codebase中获取，不要在测试过程中去定义一些类型




