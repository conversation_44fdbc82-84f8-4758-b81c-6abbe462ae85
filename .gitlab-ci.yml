include:
  - project: 'kezhaozhao/tools/qcc-deployment'
    ref: static
    file: '/gitlab/template.nodejs.v3.yml'
variables:
  SENTRY_DSN: http://<EMAIL>/3
  DEPENDENCIES_IMAGE: harbor-in.greatld.com/kezhaozhao/kzz-node:18-bullseye-slim
  SERVICE_IMAGE_BASE: $HARBOR_REPO/$CI_PROJECT_NAME:base-1.3.3
  NS_PROD: 'rover'
  NS_RELEASE: 'release'
  CLUSTER_PROD: 'rover'
  SONAR_TOKEN: 'sqp_1c627ca69c60b1854ecdf6e7c8d109db9c2eba81'
  MOBILE_NUMBERS: '18626272086'
  SKIP_UNITTEST: 'false'

.tags_job:
  tags:
    - runner_backend_group1

.unittest_reporter_tag:
  tags:
    - idc_runner_ssh_204
