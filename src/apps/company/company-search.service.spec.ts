import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { CompanySearchService } from './company-search.service';
import { intersection, isEqual } from 'lodash';
import { CompanyEntity } from '../../libs/entities/CompanyEntity';
import { CompanySearchEsService } from './company-search-es.service';
import { EsCompanySearchRequest } from './model/EsCompanySearchRequest';
import { SearchMultiSelectionRequest } from './model/SearchMultiSelectionRequest';
import { SearchCertificationRequest } from './model/SearchCertificationRequest';
import { KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { AppTestModule } from '../app/app.test.module';
import { SupplierCustomerWithFreeTextRequest } from './model/MultiMatchCompanyRequest';
import { CompanyDetailService } from './company-detail.service';
import { AxiosRequestConfig } from 'axios';
import { HttpUtilsService } from '../../libs/config/httputils.service';
import { CompanySearchModule } from './company-search.module';
import { EntityManager } from 'typeorm';

jest.setTimeout(60 * 1000);
describe('test batch process', () => {
  let app: INestApplication;
  let companyService: CompanySearchService;
  let companySearchEsService: CompanySearchEsService;
  let companyDetailService: CompanyDetailService;
  let httpUtilsService: HttpUtilsService;
  let entityManager: EntityManager;
  // const testOrgId = 1234;
  // const testUserId = 1;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, CompanySearchModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    companyService = app.get(CompanySearchService);
    companySearchEsService = app.get(CompanySearchEsService);
    companyDetailService = app.get(CompanyDetailService);
    httpUtilsService = app.get(HttpUtilsService);
    await app.init();
    entityManager = moduleFixture.get(EntityManager);
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('getCompanyBusinessInfoMap test', async () => {
    const company = await companyService.getCompanyBusinessInfoMap([
      {
        companyId: '9cce0780ab7644008b73bc2120479d31',
        companyName: '小米科技有限责任公司',
      },
    ]);
    // console.log(company);
  });

  it('getSupplierCustomerWithFreeText', async () => {
    const req = Object.assign(new SupplierCustomerWithFreeTextRequest(), {
      text: '浙江天松医疗器械股份有限公司',
      dataType: 0,
    });
    const newVar = await companyService.getSupplierCustomerWithFreeText(req);
    // // console.log(JSON.stringify(newVar));
    expect(newVar).toBeDefined();
  });

  it('should be defined', async () => {
    const names = ['Meta Platforms, Inc.'];
    const allowType = ['0', '1', '11', '12'];
    const { matchedCompanyInfos, unmatchedNames, matchedNames } = await companyService.matchCompanyInfo(
      names,
      ['id', 'name', 'creditcode', 'regno', 'province', 'areacode', 'address', 'industry', 'subind', 'econkindcode'],
      allowType,
      true,
    );
    expect(matchedCompanyInfos.length).toBe(1);
    // expect(batchImportService).toBeDefined();
    // expect(messageService).toBeDefined();
  });

  it('get company contact', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const res = await companyService.getContact(companyId);
    expect(res).toBeDefined();
  });

  it('check company match', async () => {
    const companyNames = ['小米集團', '台湾威爾比有限公司', '昭平县凤凰乡政府机关单位工会'];
    const includesFields = [
      'id',
      'name',
      'creditcode',
      'regno',
      'province',
      'areacode',
      'address',
      'industry',
      'subind',
      'econkindcode',
      'registcapi',
      'startdatecode',
      'registcapiamount',
      'status',
    ];
    const allowType = ['0', '1', '11', '12'];
    const extraType = ['3', '4', '5'];
    //测试未加过滤条件前
    const response = await companyService.matchCompanyInfo(companyNames, includesFields, allowType);
    expect(response?.matchedNames?.length).toEqual(0);
    //测试未加过滤条件后
    Array.prototype.push.apply(allowType, extraType);
    const response2 = await companyService.matchCompanyInfo(companyNames, includesFields, allowType);
    expect(isEqual(companyNames.sort(), response2?.matchedNames.sort())).toBe(true);
    expect(companyNames?.length).toEqual(response2?.matchedCompanyInfos?.length);
    //测试普通大陆企业
    const baseCompanyNames = ['小米科技有限责任公司'];
    const response3 = await companyService.matchCompanyInfo(baseCompanyNames);
    expect(intersection(response3.matchedNames, baseCompanyNames).length).toBeGreaterThanOrEqual(1);
  });

  it('test deceaseCapiNotice', async () => {
    const data = await companyService.getCompanyCreditList({
      type: 'deceaseCapiNotice', // 减资公告
      keyNo: 'e288fe59cc5709ef759c1745fc7ec5ac', //河南东鼎物流有限公司
      id: 'cb53039b0b5fd49c9e3707ab686cc551',
    } as any);
    expect(data?.Result?.length).toBeGreaterThan(0);
  });

  it('test company search es', async () => {
    //测试 companySearchEsService.companySearch
    const clientRequest: EsCompanySearchRequest = new EsCompanySearchRequest();
    clientRequest.id = ['g82a07fe914ca69ea9290712862c9bef'];
    clientRequest.pageIndex = 1;
    clientRequest.pageSize = clientRequest.id.length;
    const companyName = '广东省机械技师学院';
    const response = await companySearchEsService.companySearch(clientRequest);
    expect(response.Result.length).toBeGreaterThan(0);
  });

  it('test company search client', async () => {
    //测试 companySearchEsService.companySearch
    const companyName = '广东省机械技师学院';
    const companyId = 'g82a07fe914ca69ea9290712862c9bef';
    const response = await companyService.searchCompanyDetail(
      [companyId],
      ['id', 'address', 'insuredcount', 'creditcode', 'commonlist', 'econkind', 'yysramount', 'credit_score', 't_type'],
    );
    expect(response).not.toBeNull();
  });

  it.skip('海外公司 companySearchForQCC', async () => {
    const query: SearchMultiSelectionRequest = Object.assign(new SearchCertificationRequest(), { searchKey: 'Meta Platforms, Inc.' });
    const data = await companyService.companySearchForQcc(query);
    // // console.log(JSON.stringify(data));
    expect(data.Result[0].KeyNo).toBe('l001574ebd3d67ac5ea4f81e3cf3f90b');
  });

  it('大陆企业 companySearchForQCC', async () => {
    const query: SearchMultiSelectionRequest = Object.assign(new SearchCertificationRequest(), { searchKey: '小米科技有限责任公司' });
    const data = await companyService.companySearchForQcc(query);
    expect(data.Result[0].KeyNo).toBe('9cce0780ab7644008b73bc2120479d31');
  });

  it('大陆企业 companyDetailsFromKys', async () => {
    const data = await companyService.getCompanyDetails('g9b3aeba587377afa51bc34760a55f92');
    expect(data.result.name).toBe('福建省广播影视集团');
  });

  it('大陆企业 companyDetailsQcc', async () => {
    const data = await companyService.companyDetailsQcc('9cce0780ab7644008b73bc2120479d31');
    expect(data.Name).toBe('小米科技有限责任公司');
  });

  it('获取企业的  standardCode', async () => {
    const data = await companyService.companyDetailsQcc('baf89be015c1b63b4cfa6cfdbfc45c32');
    expect(data.Name).toBe('莱茵国际生物科技（广东）有限公司');
    expect(data.standardCode[0]).toBe('*********');
  });

  it('test createCompanyInfo', async () => {
    const data = await companyService.createCompanyInfo('c8076b1e8a68e358bda232b2584c2ccb', '北京苏高新经济咨询服务中心', true);
    expect(data.companyId).toBe('c8076b1e8a68e358bda232b2584c2ccb');
  });

  it('test createCompanyInfoBatch', async () => {
    const requestMap = new Map();
    requestMap.set('03d954c73305781db38c99bdaf8fd160', '天津海泽源房地产开发有限公司');
    const response: CompanyEntity[] = await companyService.createCompanyInfoBatch(requestMap);
    expect(response.length).toBe(1);
    expect(response[0].companyId).toEqual('03d954c73305781db38c99bdaf8fd160');
    expect(response[0].name).toEqual('天津海泽源房地产开发有限公司');
  });

  // 调试刷新全量company
  it.skip('test updateCompanyInfo', async () => {
    try {
      await companyService.updateCompanyInfo(0);
    } catch (error) {
      expect(error).toBeUndefined();
    }
  });

  // 调试刷新全量company
  it('test doGetCompanyFinance', async () => {
    try {
      await companyService.doGetCompanyFinance('22fdf2534bd21f6f5d02a6c5b2b7ba6e');
    } catch (error) {
      expect(error).toBeUndefined();
    }
  });

  it('company details standard_code', async () => {
    const data = await companyService.getCompanyDetails('g87f367ab97185a634f1899ed7415ed0');
    expect(data.result.name).toBe('北京北大英华科技有限公司工会委员会');
    expect(data.result?.['standard_code']).toBeUndefined();
  });

  it('大陆企业 companySearchForKys', async () => {
    const data = await companyService.companySearchForKys(
      Object.assign(new KysCompanySearchRequest(), {
        includeFields: ['id', 'credit_score', 'standard_code', 'isvalid', 'listingstatuskw', 'reccap', 'reccapamount'],
        pageIndex: 1,
        pageSize: 10,
        includeInvalid: true,
        filter: {
          ids: ['s70e1a9805f80a0f8191416f7f2c1303'],
        },
      }),
    );
    expect(data.Result[0].id).toBe('s70e1a9805f80a0f8191416f7f2c1303');
  });

  it('test getCaseReasonDescription', async () => {
    const response = await companyDetailService.getCaseReasonDescription(['B05020113']);
    expect(response).not.toBeNull();
  });

  it('test getCompanyDetail', async () => {
    const requestParams: AxiosRequestConfig = {
      method: 'POST',
      url: 'http://nodejs-qcc-backend-data.sit.office.qichacha.com/api/test/testToken',
      data: {},
      headers: {
        'x-request-from-head-app-name': 'kzz-qcc-rover-service',
      },
    };
    const response = await httpUtilsService.sendResquest(requestParams);
    // console.log(JSON.stringify(response));
  });
  it.skip('debug-C端接口token验证test  /api/test/testToken', async () => {
    const url = 'http://nodejs-qcc-backend-data.sit.office.qichacha.com/api/test/testToken';
    const data = {};
    // const requestParams: AxiosRequestConfig = {
    //   method: 'POST',
    //   url: 'http://nodejs-qcc-backend-data.sit.office.qichacha.com/api/test/testToken',
    //   data: {},
    //   headers: {
    //     'x-request-from-head-app-name': 'kzz-qcc-rover-service',
    //   },
    // };
    const response = await httpUtilsService.getRequest(url, data);
    // console.log(JSON.stringify(response));
  });

  it('getCompanyCertificationList test', async () => {
    const req = Object.assign(new SearchCertificationRequest(), {
      keyNo: '6b242b475738f45a4dd180564d029aa9',
      pageIndex: 1,
      pageSize: 10,
      certificateStatus: '1',
    });
    const data = await companyService.getCompanyCertificationList(req);
    expect(data.Result).toBeDefined();
  });
});
