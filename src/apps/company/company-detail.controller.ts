import { ApiCookieAuth, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { RoverSessionGuard } from 'libs/guards/RoverSession.guard';
import { CompanyDetailService } from './company-detail.service';
import { CompanyVerifiedPersonRequest } from './model/CompanyVerifiedPersonRequest';
import { JudicialCaseRequest } from './model/JudicialCaseRequest';
import { GetBondListRequest, GetCoyHisInvestInfoRequest, GetCoyHistoryEmployeeRequest } from '../../libs/model/company/detail/GetBondListRequest';

@ApiTags('公司维度详情')
@Controller('company')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard)
export class CompanyDetailController {
  constructor(private readonly detailService: CompanyDetailService) {}

  @Post('getCoyHisInvestInfo')
  @ApiOperation({ summary: '获取历史股东信息' })
  public async getCoyHisInvestInfo(@Body() request: GetCoyHisInvestInfoRequest) {
    return this.detailService.getCoyHisInvestInfo(request);
  }

  @Post('getCoyHistoryEmployee2InfoByName')
  @ApiOperation({ summary: '获取历史任职信息' })
  public async getCoyHistoryEmployee2InfoByName(@Body() request: GetCoyHistoryEmployeeRequest) {
    const type = request.type;
    if (type == 'HisLegal') {
      // 获取历史法人信息
      return this.detailService.getHisLegalInfoByName(request);
    }
    // 获取主要人员信息
    return this.detailService.getCoyHistoryEmployee2InfoByName(request);
  }

  @Post('getHisLegalInfoByName')
  @ApiOperation({ summary: '获取历史法人信息' })
  public async getHisLegalInfoByName(@Body() request: GetCoyHistoryEmployeeRequest) {
    return this.detailService.getHisLegalInfoByName(request);
  }

  @Post('getBondList')
  @ApiOperation({ summary: '发债主体评级历史信息' })
  public async getBondList(@Body() request: GetBondListRequest) {
    return this.detailService.getBondList(request);
  }

  @Post('get-shixin-detail')
  @ApiOperation({ summary: '失信被执行人详情' })
  public async getShiXinDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsShiXin(request.id);
  }

  @Post('get-assistance-detail')
  @ApiOperation({ summary: '股权冻结详情' })
  public async getAssistanceDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsAssistance(request);
  }

  @Post('get-pledge-detailV2')
  // @ApiQuery({ name: 'pledgeId', type: String, description: 'pledgeId 行记录中的No', required: true })
  @ApiOperation({ summary: '股权出质详情' })
  public async getPledgeDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsPledge(request);
  }

  @Post('get-detail-pledgeV2')
  // @ApiBody({ name: 'id', type: String, description: 'id 行记录中的No', required: true })
  @ApiOperation({ summary: '股权质押详情' })
  public async getDetailPledgeV2(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsMonitorEquityPawn(request.id);
  }

  @Post('get-bank-ruptcy-detail')
  @ApiOperation({ summary: '破产重整详情' })
  public async getBankRuptcyDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsBankRuptcy(request);
  }

  @Post('get-zhixing-detail')
  @ApiOperation({ summary: '被执行人详情' })
  public async getZhiXingDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsZhiXing(request);
  }

  @Post('get-not-allow-entry-detail')
  @ApiOperation({ summary: '获取为准入境详情' })
  public async getNotAllowEntryDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsNotAllowEntry(request);
  }

  @Post('get-tax-notice-detail')
  @ApiOperation({ summary: '获取欠税公告详情' })
  public async getTaxNoticeDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsTaxNotice(request);
  }

  @Post('get-env-penalty-detail')
  @ApiOperation({ summary: '获取环保处罚详情/历史环保处罚详情' })
  public async getEnvPenaltyDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsEnvPenalty(request);
  }

  @Post('get-mpledge-detail')
  @ApiOperation({ summary: '动产抵押详情' })
  public async getMpledgeDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsMpledge(request);
  }

  @Post('get-chattel-seizure-detail')
  @ApiOperation({ summary: '动产查封详情' })
  public async getChattelSeizureDetail(@Body() request: Record<string, any>) {
    return this.detailService.getCompanyDetailsChattelSeizure(request);
  }

  @Post('get-land-mortgage-detail')
  @ApiOperation({ summary: '土地抵押详情' })
  public async getLandMortgageDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsLandMortgage(request);
  }

  @Post('get-tax-detail')
  @ApiOperation({ summary: '获取税收违法详情' })
  public async getTaxDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsTax(request);
  }

  @Post('get-simple-cancellation-detail')
  @ApiOperation({ summary: '简易注销详情' })
  public async getSimpleCancellationDetail(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsSimpleCancellation(request);
  }

  @Post('get-case-list-by-ids')
  @ApiOperation({ summary: '获取司法案件列表' })
  public async getCaseList(@Body() request: Record<string, any>) {
    return this.detailService.caseListByIds(request);
  }

  @Get('Bond/Detail')
  @ApiOperation({ summary: '债券违约详情' })
  @ApiQuery({ name: 'id', type: String, required: true })
  @ApiQuery({ name: 'keyNo', type: String, required: false })
  @ApiQuery({ name: 'type', type: Number, required: false })
  public async getBondDetail(@Query('id') id: string, @Query('keyNo') keyNo?: string, @Query('type') type?: number) {
    return this.detailService.companyDetailsBond(id);
  }

  @Get('Bankruptcy/AnnouncementDetail')
  @ApiOperation({ summary: '破产重整研报详情' })
  @ApiQuery({ name: 'id', type: String, required: true })
  public async getBankruptcyAnnouncementDetail(@Query('id') id: string) {
    return this.detailService.companyDetailsBankRuptcyAnnouncement(id);
  }

  @Get('ProductQualityProblem/doubleRandomCheckDetail')
  @ApiOperation({ summary: '产品质量问题-双随机抽查详情' })
  @ApiQuery({ name: 'ids', type: String, description: '详情id，英文逗号拼接', required: true })
  @ApiQuery({ name: 'keyNo', type: String, description: '公司keyNo', required: true })
  // @ApiQuery({ name: 'id', type: String, description: '详情id', required: true })
  public async getDoubleRandomCheckList(@Query('keyNo') keyNo: string, @Query('ids') ids: string) {
    return this.detailService.doubleRandomCheckList({ keyNo, ids });
  }

  @Get('ProductQualityProblem/medicineDetail')
  @ApiOperation({ summary: '产品质量问题-药品抽检详情' })
  @ApiQuery({ name: 'id', type: String, description: '详情id', required: true })
  // @ApiQuery({ name: 'keyNo', type: String, required: true })
  // @ApiQuery({ name: 'id', type: String, required: true })
  public async getMedicineDetail(@Query('id') id: string) {
    return this.detailService.getMedicineDetail({ id });
  }

  @Get('ProductQualityProblem/productcheckedDetail')
  @ApiOperation({ summary: '产品质量问题-产品抽查详情' })
  @ApiQuery({ name: 'id', type: String, description: '详情id', required: true })
  @ApiQuery({ name: 'keyNo', type: String, required: true })
  // @ApiQuery({ name: 'id', type: String, required: true })
  public async getProductcheckedDetail(@Query('keyNo') keyNo: string, @Query('id') id: string) {
    return this.detailService.getProductcheckedDetail({ keyNo, id });
  }

  @Post('Risk/BillDefaultDetail')
  @ApiOperation({ summary: '票据违约详情' })
  public async getRiskBillDefaultDetail(@Body() request: Record<string, any>) {
    return this.detailService.getRiskBillDefaultDetail(request.id);
  }

  @Get('Risk/GovProcurementIllegal')
  @ApiOperation({ summary: '国央企采购黑名单' })
  public async getRiskGovProcurementIllegalDetail(@Query('id') riskId: string) {
    return this.detailService.getRiskGovProcurementIllegalDetail(riskId);
  }

  @Get('Risk/TaxCallNoticeDetail')
  @ApiOperation({ summary: '国央企采购黑名单' })
  public async getTaxCallNoticeDetail(@Query('id') riskId: string) {
    return this.detailService.getTaxCallNoticeDetail(riskId);
  }

  @Get('Risk/EndExecutionCaseDetail')
  @ApiOperation({ summary: '获取终本案件详情' })
  public async getEndExecutionCaseDetail(@Query('id') id: string) {
    return this.detailService.getEndExecutionCaseDetail(id);
  }

  @Get('get-publicity-detail')
  @ApiOperation({ summary: '获取土地抵押详情' })
  public async getDetailsOfMortgage(@Query('id') id: string) {
    return this.detailService.getDetailsOfMortgage(id);
  }

  @Get('Risk/PublicityDetail')
  @ApiOperation({ summary: '获取土地公示详情' })
  public async getDetailsOfPublicity(@Query('id') id: string) {
    return this.detailService.getDetailsOfPublicity(id);
  }

  @Get('Risk/DuiWaiDetail')
  @ApiOperation({ summary: '获取对外担保详情' })
  public async getDuiWaiDetail(@Query('id') id: string, @Query('companyCode') companyCode: string) {
    return this.detailService.getDuiWaiDetail(companyCode, id);
  }

  @Post('get-list-enliq')
  @ApiOperation({ summary: '监控动态列表-获取注销备案详情' })
  public async companyDetailsMonitorCancellationOfFiling(@Body() request: Record<string, any>) {
    return this.detailService.companyDetailsMonitorCancellationOfFiling(request);
  }

  @Get('Risk/GetRiskDetailV2')
  @ApiOperation({ summary: '获取用户风险动态详情（新多合一）' })
  public async getRiskDetailV2(@Query('riskId') riskId: string) {
    return this.detailService.getRiskDetailV2(riskId);
  }

  @Get('Risk/GetECIRiskDetail')
  @ApiOperation({ summary: '根据riskId获取工商风险详情' })
  public async getECIRiskDetail(@Query('riskId') riskId: string) {
    return this.detailService.getECIRiskDetail(riskId);
  }

  @Get('AdminPenaltyDetail')
  @ApiOperation({ summary: '根据riskId获取行政监管监管处罚详情' })
  public async getAdminPenaltyDetail(@Query('riskId') riskId: string) {
    return this.detailService.getAdminPenaltyDetail(riskId);
  }

  @Get('getTaxDetail')
  @ApiOperation({ summary: '根据 ID 获取税务催报、税务催缴详情' })
  public async getTaxReminderDetail(@Query('id') id: string) {
    return this.detailService.getTaxNoticeDetail(id);
  }

  @Post('getCompanyVerifiedPerson')
  @ApiOperation({ summary: 'b端判断公司人员归属' })
  public async getCompanyVerifiedPerson(@Body() body: CompanyVerifiedPersonRequest) {
    return this.detailService.getCompanyVerifiedPerson(body);
  }

  @Post('stockPledgeDetail')
  @ApiOperation({ summary: '股权质押详情接口' })
  public async getStockPledgeDetail(@Query('id') id: string) {
    return this.detailService.getStockPledgeDetail(id);
  }

  @Post('SingleApp/JudicialCase')
  @ApiOperation({ summary: '司法案件搜索' })
  public async getSingleAppJudicialCase(@Body() param: JudicialCaseRequest) {
    return this.detailService.getSingleAppJudicialCase(param);
  }

  @Get('SocialSecurityTrend')
  @ApiOperation({ summary: '根据公司keyNo获取参保人数趋势分析' })
  async getCompanySocialSecurityTrend(@Query('keyNo') keyNo: string) {
    return this.detailService.getCompanySocialSecurityTrend(keyNo);
  }
}
