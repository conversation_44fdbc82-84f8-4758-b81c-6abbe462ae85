import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { AppTestModule } from '../app/app.test.module';
import { CompanyDetailService } from './company-detail.service';
import { CompanySearchModule } from './company-search.module';
import { CompanyCertificationItem, CompanyCertificationResult } from '../../libs/model/company/certification.model';

jest.setTimeout(300000);
describe('company-detail.service', () => {
  let companyDetailService: CompanyDetailService;
  let entityManager: EntityManager;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, CompanySearchModule],
    }).compile();

    companyDetailService = module.get<CompanyDetailService>(CompanyDetailService);
    entityManager = getManager();
  });

  afterAll(async () => {
    await entityManager.connection.close();
  });

  it(' 获取详情-动产查封 ', async () => {
    const actual = await companyDetailService.getCompanyDetailsChattelSeizure({
      id: '295af8d83aa5417e9b55c9018587b2aa', //唐山重泰矿山机械配件厂
    });
    expect(actual?.Result).toBeDefined();
  });

  it('getCoyHistoryEmployee2InfoByName test ', async () => {
    const actual = await companyDetailService.getHisLegalInfoByName({
      keyNo: '6b242b475738f45a4dd180564d029aa9',
      name: '孙亚芳',
    });
    expect(actual).toBeDefined();
  });

  it('getBondList test ', async () => {
    const actual = await companyDetailService.getBondList({
      id: 'f06ff8a1a03f4d9bbf5d6f3a4a5a071c',
      ratingCompKeyNo: '44222f710ee439b7082aed98982ba5f8',
    });
    expect(actual).toBeDefined();
  });

  it('getCoyHistoryEmployee2InfoByName test ', async () => {
    const actual = await companyDetailService.getCoyHistoryEmployee2InfoByName({
      keyNo: '84c17a005a759a5e0d875c1ebb6c9846',
      name: '刘弘',
    });
    expect(actual).toBeDefined();
  });

  it('getCoyHisInvestInfo test ', async () => {
    const actual = await companyDetailService.getCoyHisInvestInfo({
      keyNo: '84c17a005a759a5e0d875c1ebb6c9846',
      typeKeyNo: '',
      name: '章建平',
    });
    expect(actual).toBeDefined();
  });

  describe('集成测试-getCompanyCertificationListWithStatus', () => {
    let getCompanyCertificationListSpy: jest.SpyInstance;

    beforeEach(() => {
      // 为getCompanyCertificationList方法创建间谍
      getCompanyCertificationListSpy = jest.spyOn(companyDetailService, 'getCompanyCertificationList');
    });

    afterEach(() => {
      getCompanyCertificationListSpy.mockRestore();
    });

    it('应当处理证书代码长度大于9的情况', async () => {
      // 使用三级Code
      const longCode = '025001';
      const param = { keyNo: '6b242b475738f45a4dd180564d029aa9' };

      // Mock第一页返回结果
      const mockResult: CompanyCertificationResult = {
        Status: 200,
        GroupItems: [],
        Result: [
          {
            Id: 'ac52c1546359b98e0cbabe3260c6680d',
            CertificateCode: '025001',
            PartyType: '2',
            ProductName: 'WCDMA/TD-LTE/LTE FDD/5G终端',
            ProductNameV2: ['WCDMA/TD-LTE/LTE FDD/5G终端'],
            CertificateNo: '2025-6205',
            CertificateStatus: '1',
            CertificateStatusDesc: '有效',
            StartDate: 1744300800,
            EndDate: 1840464000,
            EndDateString: '',
            CertificateCodeDesc: '无线电发射设备型号核准证',
            QualificationName: '无线电发射设备型号核准',
            ShowFieldName: '',
            IssuingAuthority: [],
            NameAndKeyno: [
              {
                GroupList: ['mb242b475738f45a4dd180564d029aa9'],
                Name: '华为技术有限公司',
                KeyNo: '6b242b475738f45a4dd180564d029aa9',
                Org: 0,
                OldGroupId: null,
              },
            ],
            QualificationJson: [],
            CommonInfo: [
              {
                Key: '002',
                Value: 'A005,025001',
              },
            ],
            ApprovingAuthority: null,
            AuthenticationItem: null,
          },
        ],
        Paging: {
          PageIndex: 1,
          PageSize: 100,
          TotalRecords: 1,
        },
      };

      // getCompanyCertificationListSpy.mockResolvedValue(mockResult);

      const result = await companyDetailService.getCompanyCertificationListWithStatus(param, longCode, '1');

      // 验证结果和调用
      expect(getCompanyCertificationListSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          pageSize: 100,
          pageIndex: 1,
          certificateCode: '025001',
          keyNo: '6b242b475738f45a4dd180564d029aa9',
        }),
      );
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('ac52c1546359b98e0cbabe3260c6680d');
    });

    it('应当处理证书代码长度小于等于9的情况', async () => {
      // 使用二级Code
      const shortCode = '025001';
      const param = { keyNo: '6b242b475738f45a4dd180564d029aa9' };

      // Mock第一页返回结果
      const mockResult: CompanyCertificationResult = {
        Status: 200,
        GroupItems: [],
        Result: [
          {
            Id: 'test002',
            CertificateCode: shortCode,
            CertificateStatus: '1',
            PartyType: '2',
            ProductName: '测试产品',
            ProductNameV2: ['测试产品'],
            CertificateNo: '123',
            CertificateStatusDesc: '有效',
            StartDate: 1600000000,
            EndDate: 1900000000,
            EndDateString: '',
            CertificateCodeDesc: '测试证书',
            QualificationName: '资质',
            ShowFieldName: '',
            IssuingAuthority: [],
            NameAndKeyno: [],
            QualificationJson: [],
            CommonInfo: [],
            ApprovingAuthority: null,
            AuthenticationItem: null,
          },
        ],
        Paging: {
          PageIndex: 1,
          PageSize: 100,
          TotalRecords: 1,
        },
      };

      getCompanyCertificationListSpy.mockResolvedValue(mockResult);

      const result = await companyDetailService.getCompanyCertificationListWithStatus(param, shortCode, '1');

      // 验证结果和调用
      expect(getCompanyCertificationListSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          certificateCode: shortCode,
          pageSize: 100,
          pageIndex: 1,
        }),
      );
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test002');
    });

    it('应当处理多页结果并合并', async () => {
      const code = '025001';
      const param = { keyNo: '6b242b475738f45a4dd180564d029aa9' };

      // 创建证书项目工厂函数
      const createCertItem = (id: string, status: string): CompanyCertificationItem => ({
        Id: id,
        CertificateCode: code,
        CertificateStatus: status,
        PartyType: '2',
        ProductName: '测试产品',
        ProductNameV2: ['测试产品'],
        CertificateNo: '123',
        CertificateStatusDesc: status === '1' ? '有效' : '无效',
        StartDate: 1600000000,
        EndDate: 1900000000,
        EndDateString: '',
        CertificateCodeDesc: '测试证书',
        QualificationName: '资质',
        ShowFieldName: '',
        IssuingAuthority: [],
        NameAndKeyno: [],
        QualificationJson: [],
        CommonInfo: [],
        ApprovingAuthority: null,
        AuthenticationItem: null,
      });

      // Mock第一页返回结果
      const mockResultPage1: CompanyCertificationResult = {
        Status: 200,
        GroupItems: [],
        Result: [createCertItem('cert001', '1'), createCertItem('cert002', '2'), createCertItem('cert003', '1')],
        Paging: {
          PageIndex: 1,
          PageSize: 3,
          TotalRecords: 5,
        },
      };

      // Mock第二页返回结果
      const mockResultPage2: CompanyCertificationResult = {
        Status: 200,
        GroupItems: [],
        Result: [createCertItem('cert004', '3'), createCertItem('cert005', '1')],
        Paging: {
          PageIndex: 2,
          PageSize: 3,
          TotalRecords: 5,
        },
      };

      // 设置间谍根据参数返回不同结果
      getCompanyCertificationListSpy.mockImplementation((params) => {
        if (params.pageIndex === 1) {
          return Promise.resolve(mockResultPage1);
        } else {
          return Promise.resolve(mockResultPage2);
        }
      });

      // 执行并验证
      const result = await companyDetailService.getCompanyCertificationListWithStatus(param, code, '1');

      // 应该有两次调用，第一页和第二页
      expect(getCompanyCertificationListSpy).toHaveBeenCalledTimes(2);

      // 验证返回的数据 - 应该只有 CertificateStatus 为 '1' 的项目
      expect(result).toHaveLength(3);
      expect(result.map((item) => item.Id)).toEqual(['cert001', 'cert003', 'cert005']);
      expect(result.every((item) => item.CertificateStatus === '1')).toBeTruthy();
    });

    it('应当处理证书状态筛选', async () => {
      const code = '025001';
      const param = { keyNo: '6b242b475738f45a4dd180564d029aa9' };

      // Mock返回结果，包含不同状态的证书
      const mockResult: CompanyCertificationResult = {
        Status: 200,
        GroupItems: [],
        Result: [
          {
            Id: 'valid1',
            CertificateCode: code,
            CertificateStatus: '1',
            PartyType: '2',
            ProductName: '有效证书1',
            ProductNameV2: ['有效证书1'],
            CertificateNo: '123',
            CertificateStatusDesc: '有效',
            StartDate: 1600000000,
            EndDate: 1900000000,
            EndDateString: '',
            CertificateCodeDesc: '测试证书',
            QualificationName: '资质',
            ShowFieldName: '',
            IssuingAuthority: [],
            NameAndKeyno: [],
            QualificationJson: [],
            CommonInfo: [],
            ApprovingAuthority: null,
            AuthenticationItem: null,
          },
          {
            Id: 'invalid1',
            CertificateCode: code,
            CertificateStatus: '2',
            PartyType: '2',
            ProductName: '无效证书',
            ProductNameV2: ['无效证书'],
            CertificateNo: '456',
            CertificateStatusDesc: '无效',
            StartDate: 1600000000,
            EndDate: 1700000000,
            EndDateString: '',
            CertificateCodeDesc: '测试证书',
            QualificationName: '资质',
            ShowFieldName: '',
            IssuingAuthority: [],
            NameAndKeyno: [],
            QualificationJson: [],
            CommonInfo: [],
            ApprovingAuthority: null,
            AuthenticationItem: null,
          },
          {
            Id: 'valid2',
            CertificateCode: code,
            CertificateStatus: '1',
            PartyType: '2',
            ProductName: '有效证书2',
            ProductNameV2: ['有效证书2'],
            CertificateNo: '789',
            CertificateStatusDesc: '有效',
            StartDate: 1600000000,
            EndDate: 1900000000,
            EndDateString: '',
            CertificateCodeDesc: '测试证书',
            QualificationName: '资质',
            ShowFieldName: '',
            IssuingAuthority: [],
            NameAndKeyno: [],
            QualificationJson: [],
            CommonInfo: [],
            ApprovingAuthority: null,
            AuthenticationItem: null,
          },
        ],
        Paging: {
          PageIndex: 1,
          PageSize: 100,
          TotalRecords: 3,
        },
      };

      getCompanyCertificationListSpy.mockResolvedValue(mockResult);

      // 测试获取有效证书
      const validResult = await companyDetailService.getCompanyCertificationListWithStatus(param, code, '1');
      expect(validResult).toHaveLength(2);
      expect(validResult.map((item) => item.Id)).toEqual(['valid1', 'valid2']);

      // 测试获取无效证书
      const invalidResult = await companyDetailService.getCompanyCertificationListWithStatus(param, code, '2');
      expect(invalidResult).toHaveLength(1);
      expect(invalidResult[0].Id).toBe('invalid1');
    });

    it('应当处理API返回为null的情况', async () => {
      const code = '025001';
      const param = { keyNo: '6b242b475738f45a4dd180564d029aa9' };

      // Mock API返回null
      getCompanyCertificationListSpy.mockResolvedValue(null);

      const result = await companyDetailService.getCompanyCertificationListWithStatus(param, code, '1');

      expect(result).toBeNull();
    });

    it('应当返回空数组当没有符合条件的证书状态', async () => {
      const code = '025001';
      const param = { keyNo: '6b242b475738f45a4dd180564d029aa9' };

      // Mock返回结果，但没有匹配状态
      const mockResult: CompanyCertificationResult = {
        Status: 200,
        GroupItems: [],
        Result: [
          {
            Id: 'invalid1',
            CertificateCode: code,
            CertificateStatus: '2',
            PartyType: '2',
            ProductName: '无效证书',
            ProductNameV2: ['无效证书'],
            CertificateNo: '456',
            CertificateStatusDesc: '无效',
            StartDate: 1600000000,
            EndDate: 1700000000,
            EndDateString: '',
            CertificateCodeDesc: '测试证书',
            QualificationName: '资质',
            ShowFieldName: '',
            IssuingAuthority: [],
            NameAndKeyno: [],
            QualificationJson: [],
            CommonInfo: [],
            ApprovingAuthority: null,
            AuthenticationItem: null,
          },
        ],
        Paging: {
          PageIndex: 1,
          PageSize: 100,
          TotalRecords: 1,
        },
      };

      getCompanyCertificationListSpy.mockResolvedValue(mockResult);

      // 搜索状态 '1' 但数据中只有状态 '2'
      const result = await companyDetailService.getCompanyCertificationListWithStatus(param, code, '1');

      // 应返回空数组而不是null
      expect(result).toEqual([]);
    });
  });

  describe('集成测试-getCertSummary', () => {
    it('单元测试-getCertSummary', async () => {
      const result = await companyDetailService.getCertSummary('6b242b475738f45a4dd180564d029aa9');
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      expect(result[0].CertificateCode).toBeDefined();
      expect(result[0].CertificateStatus).toBeDefined();
      expect(result[0].CertificateStatusDesc).toBeDefined();
    });
  });
});
