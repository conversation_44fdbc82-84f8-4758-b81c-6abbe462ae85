import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { Cacheable } from 'type-cacheable';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { SearchCreditRequest } from './model/SearchCreditRequest';
import { flatten, keyBy, map, pick, union, uniq } from 'lodash';
import { CompanyVerifiedPersonRequest } from './model/CompanyVerifiedPersonRequest';
import * as moment from 'moment';
import { JudicialCaseRequest } from './model/JudicialCaseRequest';
import { GetBondListRequest, GetCoyHisInvestInfoRequest, GetCoyHistoryEmployeeRequest } from '../../libs/model/company/detail/GetBondListRequest';
import { CompanyCertificationItem, CompanyCertificationResult } from '../../libs/model/company/certification.model';
import { MonitorCertificationItem } from '../../libs/entities/MonitorCertificationEntity';
import * as Bluebird from 'bluebird';

@Injectable()
export class CompanyDetailService {
  private readonly logger: Logger = QccLogger.getLogger(CompanyDetailService.name);

  constructor(private readonly configService: ConfigService, private readonly redisService: RedisService, private readonly httpUtils: HttpUtilsService) {
    //@ts-ignore useIoRedisAdapter(this.redisService.getClient());
  }

  @Cacheable({ ttlSeconds: 600 })
  public async getPersonAccReport(name: string, idCardMd5: string, token: string, uuid: string) {
    try {
      const url = `http://bigdataapi.ld-hadoop.com/api/personCompany/getPersonAccReport`;
      const response = await this.httpUtils.postRequest(url, { name, idCardMd5, token, uuid });
      return response;
    } catch (e) {
      this.logger.error(`getPersonAccReport err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyDetail(keyNo: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/ECILocal/GetDetail`;
      return await this.httpUtils.getRequest(url, { keyNo });
    } catch (e) {
      this.logger.error(`getCompanyDetail err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyTels(keyNo: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Company/Tels`;
      return await this.httpUtils.postRequest(url, { keyNo });
    } catch (e) {
      this.logger.error(`getCompanyTels err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsShiXin(detailId: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Court/GetShiXinDetail`;
      return await this.httpUtils.getRequest(url, { shiXinId: detailId, isB: true });
    } catch (e) {
      this.logger.error(`companyDetailsShiXin err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsAssistance(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccSearch/Detail/Assistance`;
      data.isB = true;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`companyDetailsAssistance err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsPledge(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/Pledge/DetailV2`;
      return await this.httpUtils.getRequest(url, { pledgeId: data.pledgeId });
    } catch (e) {
      this.logger.error(`companyDetailsPledge err:`, e);
      return null;
    }
  }

  /*** 股权质押* @param data* @returns*/
  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsMonitorEquityPawn(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/Detail/PledgeV2`;
      return await this.httpUtils.postRequest(url, { id });
    } catch (e) {
      this.logger.error(`companyDetailsMonitorEquityPawn err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsBankRuptcy(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/BankRuptcy/Detail`;
      data['isB'] = true;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`companyDetailsBankRuptcy err:`, e);
      return null;
    }
  }

  /*** 被执行人详情* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsZhiXing(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Court/GetZhiXingDetail`;
      //isB标识来自B端请求
      return await this.httpUtils.getRequest(url, { zhiXingId: data.id, isB: true });
    } catch (e) {
      this.logger.error(`companyDetailsZhiXing err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsNotAllowEntry(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccSearch/List/GetDetailOfNotAllowedEntry`;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`companyDetailsNotAllowEntry err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsTaxNotice(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Tax/GetDetailOfOweNotice`;
      data.isB = true;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`companyDetailsTaxNotice err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsEnvPenalty(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/AdminPenalty/DetailNew`;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`companyDetailsEnvPenalty err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsLandMortgage(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/LandMgmt/GetDetailsOfMortgage`;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`companyDetailsLandMortgage err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsMpledge(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/ECILocal/GetMPledgeDetail`;
      data.isB = true;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`companyDetailsMpledge err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyDetailsChattelSeizure(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Risk/GetMovablePropertySeizureDetail`;
      data.isB = true;
      return await this.httpUtils.postRequest(url, data);
    } catch (e) {
      this.logger.error(`getCompanyDetailsChattelSeizure err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsTax(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Tax/GetIllegalDetail`;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`companyDetailsTax err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsSimpleCancellation(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccSearch/Detail/SimpleCancellation`;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`companyDetailsSimpleCancellation err:`, e);
      return null;
    }
  }

  /*** 注销备案列表* @param data* @returns*/
  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsMonitorCancellationOfFiling(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccSearch/List/Enliq`;
      return await this.httpUtils.getRequest(url, { keyNo: data.keyNo, isRiskScan: true, isValid: data?.isValid ?? 1 });
    } catch (e) {
      this.logger.error(`companyDetailsMonitorCancellationOfFiling err:`, e);
      return null;
    }
  }

  /*** 债券违约详情* @param id 详情id*/
  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsBond(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/FinancialInfo/Bond/BondDefaultCourse`;
      return await this.httpUtils.postRequest(url, { relatedSec: id });
    } catch (e) {
      this.logger.error(`companyDetailsBond err:`, e);
      return null;
    }
  }

  /*** 破产重整研报详情* @param id 详情id*/
  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsBankRuptcyAnnouncement(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/BankRuptcy/AnnouncementDetail`;
      return await this.httpUtils.getRequest(url, { id });
    } catch (e) {
      this.logger.error(`companyDetailsBankRuptcyAnnouncement err:`, e);
      return null;
    }
  }

  /*** 根据ids 获取司法案件列表* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async caseListByIds(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Risk/GetCaseListByIds`;
      return await this.httpUtils.postRequest(url, { ids: data.ids.join(','), keyNo: data?.keyNo });
    } catch (e) {
      this.logger.error(`caseListByIds err:`, e);
      return null;
    }
  }

  /*** 获取产品质量问题-双随机检查详情* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async doubleRandomCheckList(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccSearch/List/DoubleRandomCheck`;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`doubleRandomCheckList err:`, e);
      return null;
    }
  }

  /*** 获取产品质量问题-药品抽检详情* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async getMedicineDetail(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/Drug/SpotCheck`;
      return await this.httpUtils.getRequest(url, data);
    } catch (e) {
      this.logger.error(`getMedicineDetail err:`, e);
      return null;
    }
  }

  /*** 获取产品质量问题-产品抽查详情* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async getProductcheckedDetail(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/ProductCheckInfo`;
      return await this.httpUtils.postRequest(url, data);
    } catch (e) {
      this.logger.error(`getProductcheckedDetail err:`, e);
      return null;
    }
  }

  /*** 获取税务非正常户详情 * @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async getTaxUnnormals(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Risk/GetTaxUnnormals`;
      return await this.httpUtils.postRequest(url, data);
    } catch (e) {
      this.logger.error(`getTaxUnnormals err:`, e);
      return null;
    }
  }

  /*** 票据违约详情* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async getRiskBillDefaultDetail(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Risk/BillDefaultDetail`;
      return await this.httpUtils.postRequest(url, { id });
    } catch (e) {
      this.logger.error(`getRiskBillDefaultDetail err:`, e);
      return null;
    }
  }

  /*** 国央企采购黑名单* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async getRiskGovProcurementIllegalDetail(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/BlackList/Detail`;
      return await this.httpUtils.getRequest(url, { id });
    } catch (e) {
      this.logger.error(`getRiskGovProcurementIllegalDetail err:`, e);
      return null;
    }
  }

  /*** 税务催缴公告详情* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async getTaxCallNoticeDetail(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/GovNotice/Detail`;
      return await this.httpUtils.getRequest(url, { id });
    } catch (e) {
      this.logger.error(`getTaxCallNoticeDetail err:`, e);
      return null;
    }
  }

  /*** 获取终本案件详情* @param data*/
  @Cacheable({ ttlSeconds: 600 })
  public async getEndExecutionCaseDetail(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Court/GetEndExecutionCaseDetail`;
      return await this.httpUtils.getRequest(url, { id });
    } catch (e) {
      this.logger.error(`getEndExecutionCaseDetail err:`, e);
      return null;
    }
  }

  /*** 土地抵押详情* @param id*/
  @Cacheable({ ttlSeconds: 600 })
  public async getDetailsOfMortgage(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/LandMgmt/GetDetailsOfMortgage`;
      return await this.httpUtils.getRequest(url, { id });
    } catch (e) {
      this.logger.error(`getDetailsOfMortgage err:`, e);
      return null;
    }
  }

  /*** 土地公示详情* @param id*/
  @Cacheable({ ttlSeconds: 600 })
  public async getDetailsOfPublicity(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/LandMgmt/GetDetailsOfPublicity`;
      return await this.httpUtils.getRequest(url, { id });
    } catch (e) {
      this.logger.error(`getDetailsOfPublicity err:`, e);
      return null;
    }
  }

  /*** 对外担保详情* @param companyCode: 股票代码* @param no：详情id*/
  @Cacheable({ ttlSeconds: 600 })
  public async getDuiWaiDetail(companyCode: string, no: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/CompanyIPO/GetDuiWaiDetail`;
      return await this.httpUtils.getRequest(url, { companyCode, no });
    } catch (e) {
      this.logger.error(`getDuiWaiDetail err:`, e);
      return null;
    }
  }

  /*** 获取公司风险详情* @param riskId*/
  @Cacheable({ ttlSeconds: 600 })
  public async getECIRiskDetail(riskId: string) {
    try {
      const url = `${this.configService.proxyServer.riskService}/Risk/getECIRiskDetail`;
      return await this.httpUtils.getRequest(url, { riskId });
    } catch (e) {
      this.logger.error(`getECIRiskDetail err:`, e);
      return null;
    }
  }

  /*** 获取行政处罚新详情* @param riskId*/
  @Cacheable({ ttlSeconds: 600 })
  public async getAdminPenaltyDetail(riskId: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/AdminPenalty/DetailNew`;
      return await this.httpUtils.getRequest(url, { id: riskId });
    } catch (e) {
      this.logger.error(`getAdminPenaltyDetail err:`, e);
      return null;
    }
  }

  /*** 获取公司风险详情* @param companyCode: 股票代码*/
  @Cacheable({ ttlSeconds: 600 })
  public async getRiskDetailV2(riskId: string) {
    try {
      const url = `${this.configService.proxyServer.riskService}/Risk/GetRiskDetailV2`;
      return await this.httpUtils.getRequest(url, { riskId });
    } catch (e) {
      this.logger.error(`getRiskDetailV2 err:`, e);
      return null;
    }
  }

  /**
   * 获取社会组织详情
   * @param companyId
   */
  @Cacheable({ ttlSeconds: 600 })
  public async organismDetailsQcc(companyId: string) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/Organism/GetDetailV2', {
        keyNo: companyId,
      });
    } catch (e) {
      this.logger.error(`http Get Organism/GetDetailV2 err:`, e);
      return null;
    }
  }

  /**
   * 获取资质证书情况
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyCertificationSummary(params: Record<string, any>) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/IPR/Certification/Summary', params);
    } catch (e) {
      this.logger.error(`http getCompanyCertificationSummary err:`, e);
      return null;
    }
  }

  /**
   * 获取资质证书列表
   * @param param
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyCertificationList(param: Record<string, any>) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/IPR/Certification/List', param);
    } catch (e) {
      this.logger.error(`http getCompanyCertificationList err:`, e);
      return null;
    }
  }

  /**
   * 获取资质证书列表（包含证书状态）
   * @param param
   * @param certificationCode 证书代码
   * @param certificateStatus 1.有效，2.无效，3.暂停，4.撤销，5.注销，6.过期失效
   */
  public async getCompanyCertificationListWithStatus(
    param: Record<string, any>,
    certificationCode: string,
    certificateStatus = '1',
  ): Promise<CompanyCertificationItem[]> {
    if (certificationCode.length > 9) {
      // 三级 Code 长度
      param['certificateLevel'] = certificationCode;
    } else {
      // 二级 Code 长度
      param['certificateCode'] = certificationCode;
    }

    // 设置分页参数，初始查询第一页
    param['pageSize'] = 1;
    param['pageIndex'] = 1;

    // 查询第一页数据
    const initialResult = (await this.getCompanyCertificationList(param)) as CompanyCertificationResult;
    if (!initialResult) {
      return null;
    }
    const allResults: CompanyCertificationItem[] = [...initialResult.Result];
    // 按照指定的证书状态查找
    const certificateItems = allResults.filter((f) => f.CertificateStatus === certificateStatus);
    return certificateItems;
  }

  /**
   * 获取最新证书数量信息
   * @param companyId
   * @private
   */
  public async getCertSummary(companyId: string) {
    const result = await this.getCompanyCertificationSummary({
      keyNo: companyId,
      isNew: true,
      isNewAgg: true,
    });
    const resultItems: MonitorCertificationItem[] = result?.Result || [];

    if (resultItems.length === 0) {
      return resultItems;
    }

    // 并行获取所有证书的状态
    const certPromises = resultItems.map((item) => this.getCompanyCertificationListWithStatus({ keyNo: companyId }, item.CertificateCode));

    // 等待所有并行请求完成
    const certResults = await Bluebird.all(certPromises);

    // 创建证书状态查找映射，避免重复查找
    const certStatusMap = new Map<string, { status: string; desc: string }>();
    certResults.forEach((certs) => {
      if (certs?.[0]) {
        certStatusMap.set(certs[0].CertificateCode, {
          status: certs[0].CertificateStatus,
          desc: certs[0].CertificateStatusDesc,
        });
      }
    });

    // 一次遍历更新所有状态
    resultItems?.forEach((item) => {
      const statusInfo = certStatusMap.get(item.CertificateCode);
      if (statusInfo) {
        item.CertificateStatus = statusInfo.status;
        item.CertificateStatusDesc = statusInfo.desc;
      } else {
        item.CertificateStatus = '';
        item.CertificateStatusDesc = '';
      }
    });

    return resultItems;
  }

  /**
   * 获取证书详情
   * @param id
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyCertificationDetail(id: string) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/IPR/Certification/Detail', {
        id,
      });
    } catch (e) {
      this.logger.error(`http getCompanyCertificationDetail err:`, e);
      return null;
    }
  }

  /**
   * 信用评价汇总
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyCreditSummary(keyNo: string) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/ECILocal/Credit/Summary', {
        keyNo,
      });
    } catch (e) {
      this.logger.error(`http getCompanyCreditSummary err:`, e);
      return null;
    }
  }

  /**
   * 工商高级搜索（多选版）
   * @param data
   */
  @Cacheable({ ttlSeconds: 600 })
  public async searchMultiSelection(data: Record<string, any>) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/ECILocal/SearchMultiSelection`;
      return await this.httpUtils.postRequest(url, data);
    } catch (e) {
      this.logger.error(`searchMultiSelection err:`, e);
      return null;
    }
  }

  /**
   * 文本解析公司列表
   * @param text
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompaniesWithFreeText(text: string | string[]) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/ECILocal/GetCompaniesWithFreeText`;
      return await this.httpUtils.postRequest(url, { text, isMergedRequest: true });
    } catch (e) {
      this.logger.error(`getCompaniesWithFreeText err:`, e);
      return null;
    }
  }

  /**
   * 模糊匹配公司工商
   * @param text
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getLinkCompaniesWithFreeText(text: string | string[]) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/ECILocal/GetLinkCompaniesWithFreeText`;
      return await this.httpUtils.postRequest(url, { text });
    } catch (e) {
      this.logger.error(`getLinkCompaniesWithFreeText err:`, e);
      return null;
    }
  }

  /**
   * 供应商与客户信息
   * @param keyNo
   * @param pageIndex
   * @param pageSize
   * @param dataType
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getSupplierCustomer(keyNo: string, pageIndex: number, pageSize: number, dataType: number) {
    try {
      const reqUrl = this.configService.proxyServer.dataService + '/api/QccSearch/List/SupplierCustomer';
      return await this.httpUtils.getRequest(reqUrl, { keyNo, pageIndex, pageSize, dataType });
    } catch (e) {
      this.logger.error(`getSupplierCustomer err:`, e);
      return null;
    }
  }

  private static creditMapArr = [
    {
      key: 'tc',
      api: '/api/CompanyInfo/GetCompanyTaxCredit',
    },
    {
      key: 'bbc',
      api: '/api/Bond/BondList',
      params: {
        isAggs: true,
        nodeName: 'ComNewRating',
        sortField: 'RatingDate',
      },
    },
    {
      key: 'ec',
      api: '/api/QccSearch/List/EnvCreditEvaluation',
    },
    {
      key: 'lgc',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 2,
      },
    },
    {
      key: 'cc',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 3,
      },
    },
    {
      key: 'kfc',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 4,
      },
    },
    {
      key: 'spc',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 5,
      },
    },
    {
      key: 'customs',
      api: '/api/QccSearch/List/GetImportExportCredits',
      method: 'POST',
    },
    {
      key: 'fs',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 10,
      },
    },
    {
      key: 'road',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 9,
      },
    },
    {
      key: 'soft',
      api: '/api/QccSearch/List/Credit',
      params: {
        rateTypeCode: 11,
      },
    },
    {
      key: 'fund',
      api: '/api/QccSearch/List/GetFundManagerRates',
      method: 'POST',
    },
    {
      key: 'deceaseCapiNotice',
      api: '/api/QccSearch/List/DeceaseCapiNotice',
      method: 'POST',
    },
  ];

  /**
   * 获取企业信用列表
   * @param body
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyCreditList(body: SearchCreditRequest) {
    try {
      const type = body.type;
      const curr = CompanyDetailService.creditMapArr.find((item) => item.key == type);
      //减资公告数据接口入参发生了变化
      if (curr.key === 'deceaseCapiNotice') {
        const newBody = { ...body, ids: [body.id] };
        return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + curr.api, newBody);
      }
      if (curr.method == 'POST') {
        return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + curr.api, body);
      }
      if (curr.params) {
        Object.assign(body, curr.params);
      }
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + curr.api, body);
    } catch (e) {
      this.logger.error(`getCompanyCreditList err:`, e);
      return null;
    }
  }

  /**
   * 专利搜索
   * @param param
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyPatent(param: Record<string, any>) {
    try {
      const reqUrl = this.configService.proxyServer.dataService + '/api/QccSearch/IPRSearch/Patent';
      return await this.httpUtils.postRequest(reqUrl, param);
    } catch (e) {
      this.logger.error(`getCompanyPatent err:`, e);
      return null;
    }
  }

  /**
   * 专利关系
   * @param keyNos
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getPatentRelation(keyNos?: string[]) {
    try {
      const reqUrl = this.configService.proxyServer.dataService + '/api/IPR/PatentRelation';
      return await this.httpUtils.postRequest(reqUrl, { keyNos });
    } catch (e) {
      this.logger.error(`getPatentRelation err:`, e);
      return null;
    }
  }

  /**
   * 获取企查查行业信息
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getQccIndustries() {
    try {
      return await this.httpUtils.postRequest(`${this.configService.proxyServer.dataService}/api/Common/QccIndustries`, null);
    } catch (e) {
      this.logger.error(`getQccIndustries err:`, e);
      return null;
    }
  }

  /**
   * 国际专利关系
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getInternationalPatent(params: Record<string, any>) {
    try {
      return await this.httpUtils.getRequest(`${this.configService.proxyServer.dataService}/api/QccSearch/SingleApp/InternationalPatent`, params);
    } catch (e) {
      this.logger.error(`getInternationalPatent err:`, e);
      return null;
    }
  }

  /**
   * 查询著作权软件著作权
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCopyrights(params: Record<string, any>) {
    try {
      return await this.httpUtils.postRequest(`${this.configService.proxyServer.dataService}/api/QccSearch/SingleApp/Copyrights`, params);
    } catch (e) {
      this.logger.error(`getCopyrights err:`, e);
      return null;
    }
  }

  /**
   * 公司间软件著作权关系
   * @param keyNos
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getSoftwareCopyright(keyNos: string[]) {
    try {
      return await this.httpUtils.postRequest(`${this.configService.proxyServer.dataService}/api/IPR/Relation/SoftwareCopyright`, {
        keyNos,
      });
    } catch (e) {
      this.logger.error(`getSoftwareCopyright err:`, e);
      return null;
    }
  }

  /**
   * 企业担保信息
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getGuarantorList(params: Record<string, any>) {
    try {
      return await this.httpUtils.getRequest(`${this.configService.proxyServer.dataService}/api/QccSearch/List/Guarantor`, params);
    } catch (e) {
      this.logger.error(`getGuarantorList err:`, e);
      return null;
    }
  }

  /**
   * 获取区域列表信息
   * @param withEcoZone
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getAreas(withEcoZone?: boolean) {
    try {
      return await this.httpUtils.getRequest(`${this.configService.proxyServer.dataService}/api/Common/GetAreas`, { withEcoZone });
    } catch (e) {
      this.logger.error(`getAreas err:`, e);
      return null;
    }
  }

  /**
   * 获取控股公司信息
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getHoldingCompany(params: Record<string, any>) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/VIP/GetHoldingCompany', params);
    } catch (e) {
      this.logger.error(`getHoldingCompany err:`, e);
      return null;
    }
  }

  /**
   * 高管关联公司
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getBossDJGData(params: Record<string, any>) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/person/GetBossDJGData', params);
    } catch (e) {
      this.logger.error(`getBossDJGData err:`, e);
      return null;
    }
  }

  /**
   * 获取子公司列表keyNo list
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 600 })
  async getBranchKeyNos(keyNo: string): Promise<string[]> {
    try {
      const response = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/Branch', {
        keyNo,
      });
      if (response?.Result) {
        return union(flatten(map(response?.Result, 'KeyNo')));
      }
      return null;
    } catch (e) {
      this.logger.error(`getBranchKeyNos err:`, e);
      return null;
    }
  }

  /**
   * 获取子公司列表 list
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  async getBranchList(keyNo: string): Promise<string[]> {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/Branch', {
        keyNo,
      });
    } catch (e) {
      this.logger.error(`getBranchList err:`, e);
      return null;
    }
  }

  /**
   * 工商变更信息
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCoyHistoryInfo(keyNo: string) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/History/GetCoyHistoryInfo', { keyNo });
    } catch (e) {
      this.logger.error(`http GetCoyHistoryInfo data: ${JSON.stringify({ keyNo })} err: ${e.message}`);
      return null;
    }
  }

  /**
   * 获取企业疑似实际控制人
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 600 })
  async getSuspectedActualControllerV3(keyNo: string) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/Relation/GetSuspectedActualControllerV3', { keyNo });
    } catch (e) {
      this.logger.error(`getSuspectedActualControllerV3 err:`, e);
      return null;
    }
  }

  /**
   * 获取企业疑似实际控制人-新
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 600 })
  async getSuspectedActualControllerV5(keyNo: string) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/Relation/GetSuspectedActualControllerV5', { keyNo });
    } catch (e) {
      this.logger.error(`getSuspectedActualControllerV5 err:`, e);
      return null;
    }
  }

  /**
   * 变更记录列表
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getChangeInfoList(params: Record<string, any>) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/EciLocal/ChangeRecords', params);
    } catch (e) {
      this.logger.error(`getChangeInfoList err:`, e);
      return null;
    }
  }

  /**
   * 社会组织主要人员
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getOrganismPersonList(params: Record<string, any>) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/OrganismPerson', params);
    } catch (e) {
      this.logger.error(`getOrganismPersonList err:`, e);
      return null;
    }
  }

  /**
   * 历史高管去重
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCoyHistoryEmployee2Info(params: Record<string, any>) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/History/GetCoyHistoryEmployee2Info', params);
    } catch (e) {
      this.logger.error(`http GetCoyHistoryEmployee2Info data: ${JSON.stringify(params)} err: ${e.message}`);
      return null;
    }
  }

  /**
   * 历史股东信息
   * @param param
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCoyHistoryPartnerInfo(param: Record<string, any>) {
    try {
      const url = this.configService.proxyServer.dataService + '/api/History/GetCoyHistoryPartnerInfo';
      return await this.httpUtils.getRequest(url, param);
    } catch (e) {
      this.logger.error(`getCoyHistoryPartnerInfo err:`, e);
      return null;
    }
  }

  /**
   * 受益所有人
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getBenefitDetail(params: Record<string, any>) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccDetail/Benefit/Detail', params);
    } catch (e) {
      this.logger.error(`http GetBenefitDetail data: ${JSON.stringify(params)} err: ${e.message}`);
      return null;
    }
  }

  /**
   * 主要人员列表
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getEmployeeList(params: Record<string, any>) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/Employee', params);
    } catch (e) {
      this.logger.error(`getEmployeeList err:`, e);
      return null;
    }
  }

  /**
   * 最终受益人列表
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getBenefitList(params: Record<string, any>) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/Benefit', params);
    } catch (e) {
      this.logger.error(`getBenefitList err:`, e);
      return null;
    }
  }

  /**
   * 工商股东列表
   * @param param
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getPartnerWithGroup(param: Record<string, any>) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/ECILocal/GetPartnerWithGroup', param);
    } catch (e) {
      this.logger.error(`http GetPartnerWithGroup data: ${JSON.stringify(param)} err: ${e.message}`);
      return null;
    }
  }

  /**
   * 获取公司关联方
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getRelatedList(keyNo: string) {
    const url = `${this.configService.proxyServer.riskService}/Related/List`;
    try {
      return await this.httpUtils.getRequest(url, {
        userId: 'rover',
        keyNo,
        pageSize: 100,
        pageIndex: 1,
        nodeType: '2,4,5',
      });
    } catch (e) {
      this.logger.error(`http Post ${url} err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyAllChattelMortgage(keyNo: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccSearch/List/MPledge`;
      const params = {
        keyNo,
        pageIndex: 1,
        pageSize: 1000,
      };
      const response = await this.httpUtils.getRequest(url, params);
      return response?.Result || [];
    } catch (e) {
      this.logger.error(`getCompanyAllChattelMortgage err:`, e);
      return [];
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  async getTaxNoticeDetail(objectId: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/Risk/GetTaxReminderDetail`;
      const params = {
        id: objectId,
      };
      const response = await this.httpUtils.postRequest(url, params);
      return response?.Result || {};
    } catch (e) {
      this.logger.error(`getTaxNoticeDetail err:`, e);
      return {};
    }
  }

  /**
   * b端判断公司人员归属, 数据提供的接口，通过调用外部接口判断指定人员是否归属指定公司
   * @param params
   */
  @Cacheable({ ttlSeconds: 600 })
  async getCompanyVerifiedPerson(params: CompanyVerifiedPersonRequest) {
    try {
      const url = `http://10.0.0.4/api/CompanyInfo/GetCompanyVerifiedPerson`;
      const response = await this.httpUtils.postRequest(url, params);
      return response?.Result?.Data || {};
    } catch (e) {
      this.logger.error(`getCompanyVerifiedPerson err:`, e);
      return {};
    }
  }

  /**
   * 获取股权质押详情
   * @param id
   */
  @Cacheable({ ttlSeconds: 600 })
  async getStockPledgeDetail(id: string) {
    try {
      const url = `${this.configService.proxyServer.dataService}/api/QccDetail/Detail/PledgeV2`;
      const params = {
        id,
      };
      const response = await this.httpUtils.postRequest(url, params);
      return response?.Result || {};
    } catch (e) {
      this.logger.error(`getStockPledgeDetail err:`, e);
      return {};
    }
  }

  /**
   * 获取财务报表信息
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyFinance(keyNo: string) {
    try {
      const startDate = moment().add('-3', 'years').startOf('year').unix(); // 近3年
      const data = {
        keyNo,
        type: 'cm', // cm 大陆财务 hk 香港财务
        reportType: 1, // 类型 1 主要指标 2 资产负债 3 利润 4 现金流
        reportPeriodTypes: [4],
        currency: '',
        rate: 1, // 采用的汇率计算办法
        isAggs: true,
        sortField: 'reportdate',
        isSortAsc: 'false',
        decimal: 2,
        startDate,
      };
      const url = `${this.configService.proxyServer.dataService}/api/EciLocal/Finance/StatementsV2`;
      return await this.httpUtils.postRequest(url, data);
    } catch (e) {
      this.logger.error(`getCompanyFinance data: ${JSON.stringify({ keyNo })} err:`, e);
      return null;
    }
  }

  /**
   * 获取电信许可证书详情
   * @param licenseId
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyTelecomLicenseDetail(licenseId: string) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/TelecomLicense/GetDetails', {
        id: licenseId,
      });
    } catch (e) {
      this.logger.error(`http getCompanyTelecomLicenseDetail err:`, e);
      return null;
    }
  }

  /**
   * 获取电信许可证书详情
   * @param companyId
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getGeneralTaxPayerList(companyId: string) {
    try {
      const params = {
        keyNo: companyId,
        pageSize: 10,
        pageIndex: 1,
        sortField: 'startdate',
        isSortAsc: false,
      };
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/GeneralTaxPayer', params);
    } catch (e) {
      this.logger.error(`http getCompanyTelecomLicenseDetail err:`, e);
      return null;
    }
  }

  public async getCaseReasonDescription(caseReasonTypes: string[]): Promise<{ [key: string]: string }> {
    if (!caseReasonTypes.length) {
      return null;
    }
    try {
      const response = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/Risk/GetCaseReasonDescription', {
        caseReasonTypes: uniq(caseReasonTypes),
      });
      if (response?.Status === 200 && response?.Result?.length) {
        const keyedData = keyBy(response.Result, 'CaseReasonType');
        return Object.keys(keyedData).reduce((acc, key) => {
          acc[key] = keyedData[key].CaseReasonDescription;
          return acc;
        }, {} as { [key: string]: string });
      }
      return null;
    } catch (e) {
      this.logger.error(`http getCaseReasonDescription err:`, e);
      return null;
    }
  }

  public async getSingleAppJudicialCase(param: JudicialCaseRequest) {
    try {
      const response = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/QccSearch/SingleApp/JudicialCase', param);
      if (response?.Result?.length) {
        return pick(response.Result[0], 'CaseReasonDescription');
      }
      return null;
    } catch (e) {
      this.logger.error(`http SingleApp/JudicialCase err:`, e);
      return null;
    }
  }

  public async getCompanySocialSecurityTrend(keyNo: string) {
    try {
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/QccSearch/List/SocialSecurityTrend', { keyNo });
    } catch (e) {
      this.logger.error(`getCompanySocialSecurityTrend err:`, e);
      return null;
    }
  }

  async getBondList(request: GetBondListRequest) {
    try {
      const { id, ratingCompKeyNo } = request;
      const params = {
        pageSize: 500,
        pageIndex: 1,
        id,
        sortField: 'RatingDate',
        nodeName: 'HistoryRating',
        filterCondition: { fieldName: 'RatingCompKeyno', fieldValue: ratingCompKeyNo },
      };
      return await this.httpUtils.getRequest(this.configService.proxyServer.dataService + '/api/Bond/BondList', params);
    } catch (e) {
      this.logger.error(`getBondList err:`, e);
      return null;
    }
  }

  async getCoyHistoryEmployee2InfoByName(request: GetCoyHistoryEmployeeRequest) {
    try {
      const { name, keyNo } = request;
      const params = {
        keyNo,
        queryKey: name.trim(),
      };
      const result = [];
      const response = await this.getCoyHistoryEmployee2Info(params);
      if (response?.Result?.length) {
        const item = response.Result.find((item) => item.EmployeeName.replace('<em>', '').replace('</em>', '') == request.name);
        if (item) {
          result.push(item);
        }
        response.Result = result;
        response.Paging.TotalRecords = result.length;
      }
      return response;
    } catch (e) {
      this.logger.error(`getCoyHistoryEmployee2InfoByName err:`, e);
      return null;
    }
  }

  /**
   * 批量获取证书详情
   * @param idList
   */
  async getCompanyCertificationDetailList(idList: string[]) {
    try {
      return await this.httpUtils.postRequest(this.configService.proxyServer.dataService + '/api/IPR/Certification/Details', { ids: idList });
    } catch (e) {
      this.logger.error(`getCompanyCertificationDetailList err:`, e);
      return null;
    }
  }

  /**
   * 历史投资信息
   * @param request
   */
  async getCoyHisInvestInfo(request: GetCoyHisInvestInfoRequest) {
    try {
      const { keyNo, typeKeyNo, name } = request;
      let params;
      let byName = false;
      if (typeKeyNo) {
        params = {
          keyNo,
          partnerId: typeKeyNo,
        };
      } else {
        params = {
          keyNo,
          searchKey: name,
        };
        byName = true;
      }
      const result = [];
      const response = await this.getCoyHistoryPartnerInfo(params);
      if (byName && response?.Result?.length) {
        const item = response.Result.find((item) => item.PartnerName.replace('<em>', '').replace('</em>', '') == name);
        if (item) {
          result.push(item);
        }
        response.Result = result;
        response.Paging.TotalRecords = result.length;
      }
      return response;
    } catch (e) {
      this.logger.error(`getCoyHisInvestInfo err:`, e);
      return null;
    }
  }

  async getHisLegalInfoByName(request: GetCoyHistoryEmployeeRequest) {
    try {
      const { keyNo, typeKeyNo, name } = request;
      let result = [];
      const dataResult = await this.getCoyHistoryInfo(keyNo);
      // 工商变更记录分类型 对应不同的维度处理
      const OperList = dataResult?.Result?.OperList || [];
      if (OperList?.length) {
        result = OperList?.filter((e) => {
          e['Job'] = '历史法定代表人';
          if (name) {
            return e.OperName == name;
          }
          if (typeKeyNo) {
            return e.KeyNo == typeKeyNo;
          }
          return false;
        });
      }
      return { Result: result, Paging: { PageIndex: 1, PageSize: 10, TotalRecords: result.length } };
    } catch (e) {
      this.logger.error(`getHisLegalInfoByName err:`, e);
      return { Result: [], Paging: { PageIndex: 1, PageSize: 10, TotalRecords: 0 } };
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  async getHistoryCountInfo(keyNo: string) {
    try {
      const response = await this.httpUtils.getRequest(this.configService.dataServer.getHistoryCountInfo, { keyNo });
      return response?.Result || {};
    } catch (e) {
      this.logger.error(`getHistoryCountInfo err:`, e);
      return {};
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  async getCountInfo(keyNo: string) {
    try {
      return await this.httpUtils.postRequest(this.configService.dataServer.getCountInfo, { keyNo });
    } catch (e) {
      this.logger.error(`getCountInfo err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  async getCountForB(keyNo: string) {
    try {
      const response = await this.httpUtils.postRequest(this.configService.dataServer.getCountForB, { keyNo });
      return response?.Result || {};
    } catch (e) {
      this.logger.error(`getCountForB err:`, e);
      return {};
    }
  }
}
