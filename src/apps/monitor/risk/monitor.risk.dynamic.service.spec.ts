import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import * as Bluebird from 'bluebird';
import { MonitorRiskDynamicsV2Entity } from 'libs/entities/MonitorRiskDynamicsV2Entity';
import { RiskDynamicsSearchRequest } from 'libs/model/monitor/request/RiskDynamicsSearchRequest';
import { cloneDeep, flatMap, pick } from 'lodash';
import * as moment from 'moment';
import { EntityManager, getManager, getRepository, In, Repository } from 'typeorm';
import { MonitorRiskMessageEntity } from '../../../libs/entities/MonitorRiskMessageEntity';
import { SettingTypeEnums } from '../../../libs/model/settings/SettingTypeEnums';
import { AppTestModule } from '../../app/app.test.module';
import { SettingsService } from '../../settings/settings.service';
import { testMessagesIgnPhase2, testMessagesPhase2 } from '../risk.copy.from.c/phase-2-test.data';
import { testMessageValue } from '../risk.copy.from.c/test.data';
import { MonitorRiskDynamicService } from './monitor.risk.dynamic.service';
import { RiskChangeListESService } from './risk-changelist.es.service';
import { MonitorModule } from '../monitor.module';
import { DefaultMonitorDimension } from '../../../libs/constants/monitor.dimension.constants';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import { MonitorGroupCompanyEntity } from '../../../libs/entities/MonitorGroupCompanyEntity';
jest.setTimeout(600 * 1000);

describe('风险动态集成测试', () => {
  let monitorDynamicService: MonitorRiskDynamicService;
  let riskESService: RiskChangeListESService;
  let settingsService: SettingsService;

  const [testOrgId, testUserId] = generateUniqueTestIds('monitor.risk.dynamic.service.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  let entityManager: EntityManager;
  let testGroup: MonitorGroupEntity;
  let testMonitorCompany: MonitorGroupCompanyEntity;

  // jest.mock<KafkaQueue>()

  // 清理测试数据
  const cleanupTestData = async (orgId: number) => {
    await entityManager.delete(MonitorGroupEntity, { orgId });
    await entityManager.delete(MonitorGroupCompanyEntity, { orgId });
    await entityManager.delete(MonitorRiskDynamicsV2Entity, { orgId });
  };

  const setupTestData = async (orgId: number) => {
    // 创建测试数据
    //创建测试用的默认分组
    await entityManager.save(MonitorGroupEntity, {
      orgId: testOrgId,
      name: '风险动态测试分组',
      ownerId: testUserId,
      monitorStatus: 0,
    });

    testGroup = await entityManager.findOne(MonitorGroupEntity, { where: { orgId: testOrgId } });

    //测试文件久未调整，数据命中情况发生了变化，后续优化需要补充所有指标，以及下面的维度数据
    const allMessages = [
      ...flatMap(Object.values(testMessageValue), (v) => (Array.isArray(v) ? v : [v])),
      ...flatMap(Object.values(testMessagesPhase2), (v) => (Array.isArray(v) ? v : [v])),
      ...flatMap(Object.values(testMessagesIgnPhase2), (v) => (Array.isArray(v) ? v : [v])),
    ];
    const uniqueMessages = Array.from(new Map(Object.values(allMessages).map((msg) => [msg.keyno, msg])).values());

    await entityManager.save(
      MonitorGroupCompanyEntity,
      uniqueMessages.map((msg) => ({
        companyId: msg.keyno,
        companyName: msg.name,
        createBy: testUserId,
        orgId: testOrgId,
        groupId: testGroup.id,
      })),
    );

    // 获取监控模型设置
    const monitorSetting = await settingsService.getOrgLatestSettings(orgId, SettingTypeEnums.monitor);
    monitorSetting.content = cloneDeep(DefaultMonitorDimension);
    jest.spyOn(settingsService, 'getOrgLatestSettings').mockResolvedValue(monitorSetting);
  };

  beforeAll(async () => {
    // jest.mock('QueueService', () => {
    //   return {};
    // });
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, MonitorModule, TypeOrmModule.forFeature([MonitorRiskDynamicsV2Entity])],
    }).compile();
    monitorDynamicService = module.get<MonitorRiskDynamicService>(MonitorRiskDynamicService);
    riskESService = module.get<RiskChangeListESService>(RiskChangeListESService);
    settingsService = module.get(SettingsService);
    entityManager = getManager();

    await cleanupTestData(testOrgId);
    await setupTestData(testOrgId);
  });
  afterAll(async () => {
    await entityManager.connection.close();
    await cleanupTestData(testOrgId);
  });

  it('processRiskMessage test', async () => {
    const item = Object.assign(new MonitorRiskMessageEntity(), {
      id: 10,
      name: '恒大地产集团有限公司',
      keyNo: '917720a16d08bdde171681983391aed0',
    });
    await monitorDynamicService.processRiskMessage(item);
  });

  it('get risk dynamic', async () => {
    const dynamics = await entityManager.find(MonitorRiskDynamicsV2Entity);
    expect(dynamics.length).toBeGreaterThanOrEqual(0);
  });

  it('kafka收到监控动态', async () => {
    const processedResults = await Bluebird.map(
      Object.keys(testMessageValue),
      async (category) => {
        const messages = testMessageValue[category];

        // 统一处理函数
        const processItem = async (msg) => {
          const kafkaPayload: any = {
            message: { value: Buffer.from(JSON.stringify(msg)) },
          };
          const result = await monitorDynamicService.handleKafkaKysCompanyMonitorQueue(kafkaPayload);
          return { category, messageValue: msg, result };
        };

        // 处理数组或单对象
        if (Array.isArray(messages)) {
          return Bluebird.map(messages, processItem);
        }
        return [await processItem(messages)]; // 统一返回数组结构
      },
      { concurrency: 10 },
    )
      .then((results) => results.flat()) // 扁平化多维数组
      .filter((item) => item !== null && item !== undefined);

    // 2. 收集所有ID（包含需要验证和不需要验证的）
    const allIds = processedResults.map(({ messageValue }) => messageValue.id);

    await Bluebird.delay(3000);

    // 4. 批量查询所有记录
    const dynamics = await entityManager.createQueryBuilder(MonitorRiskDynamicsV2Entity, 'd').where('d.dynamicId IN (:...ids)', { ids: allIds }).getMany();

    // 5. 创建全量映射表
    const dynamicsMap = new Map(dynamics.map((d) => [d.dynamicId, d]));

    // 6. 统一验证结果
    for (const { category, messageValue, result } of processedResults) {
      // 公共断言：所有处理都应返回成功
      expect(result).toBe('success');

      // 特殊category断言 70114 67不命中  18类型记录动态，通过定时任务判断是否生成动态
      if (['70114', '67', '18'].includes(category)) {
        // 验证不应存在记录
        //console.log(category);
        expect(dynamicsMap.has(messageValue.id)).toBe(false);
      } else {
        // 验证必须存在记录
        //console.log(category);
        expect(dynamicsMap.get(messageValue.id)).toBeTruthy();
      }
    }
  });

  it('kafka收到监控动态 监控二期新增维度测试', async () => {
    // 裁判文书-公司 category = 4
    // 工商风险-大股东变更 category = 24
    // 合并需要验证的测试数据
    const testCases = [
      ...Object.values(testMessagesPhase2).map((msg) => ({ msg, shouldExist: true })),
      ...Object.values(testMessagesIgnPhase2).map((msg) => ({ msg, shouldExist: false })),
    ];

    // 批量处理所有测试消息
    const processedResults = await Bluebird.map(
      testCases,
      async ({ msg }) => {
        const kafkaPayload: any = {
          message: {
            value: Buffer.from(JSON.stringify(msg)),
          },
        };
        const result = await monitorDynamicService.handleKafkaKysCompanyMonitorQueue(kafkaPayload);
        return { msg, result };
      },
      { concurrency: 10 },
    );

    // 统一等待处理完成
    await Bluebird.delay(3000);

    // 批量查询所有记录
    const allIds = processedResults.map(({ msg }) => msg.id);
    const dynamics = await entityManager.createQueryBuilder(MonitorRiskDynamicsV2Entity, 'd').where('d.dynamicId IN (:...ids)', { ids: allIds }).getMany();

    // 创建快速查询映射表
    const dynamicsMap = new Map(dynamics.map((d) => [d.dynamicId, d]));

    // 统一验证结果
    processedResults.forEach(({ msg, result }, index) => {
      // 公共断言
      expect(result).toBe('success');

      // 特殊验证逻辑
      const shouldExist = testCases[index].shouldExist;
      if (shouldExist) {
        expect(dynamicsMap.get(msg.id)).toBeTruthy();
      } else {
        expect(dynamicsMap.get(msg.id)).toBeFalsy();
      }
    });
  });

  it('把es中的数据处理成动态消息的数据结构', async () => {
    const dynamics = await riskESService.getDetailByIds(['e23cf1e06f98720113f19ef7c6565b7b'], 1, 1);
    const raw = dynamics.data[0];
    if (!raw) return;
    raw['createDate'] = moment.unix(raw?.CreateDate).format('YYYY/MM/DD HH:mm:ss');
    raw['updateDate'] = moment.unix(raw?.UpdateDate).format('YYYY/MM/DD HH:mm:ss');
    raw['changeDate'] = moment.unix(raw?.ChangeDate).format('YYYY/MM/DD HH:mm:ss');
    const lowercaseRaw = JSON.stringify(raw).replace(/[A-Z]/g, function (match) {
      return String.fromCharCode(match.charCodeAt(0) + 32);
    });

    const messageValue = pick(JSON.parse(lowercaseRaw), [
      'key',
      'id',
      'keyno',
      'name',
      'risklevel',
      'category',
      'beforecontent',
      'aftercontent',
      'changeextend',
      'objectid',
      'changestatus',
      'changedate',
      'datatype',
      'extend1',
      'extend2',
      'isvalid',
      'createdate',
      'updatedate',
      'importanceflag',
      'isrisk',
      'extend3',
      'extend4',
      'extend5',
    ]);

    // console.log(JSON.stringify(messageValue));
  });

  it('应该更新数据状态变更为无效的风险动态', async () => {
    // Arrange
    //减资公告更新
    const testMessageValue132 = testMessageValue[132];
    const kafkaPayload132: any = {
      message: {
        value: Buffer.from(JSON.stringify(testMessageValue132)),
      },
    };
    await monitorDynamicService.handleKafkaKysCompanyMonitorQueue(kafkaPayload132);

    // Act
    testMessageValue132.isvalid = 0;
    const kafkaPayloadNoValid: any = {
      message: {
        value: Buffer.from(JSON.stringify(testMessageValue132)),
      },
    };
    await monitorDynamicService.handleKafkaKysCompanyMonitorQueue(kafkaPayloadNoValid);

    // Assert
    // 动态应该被更新了
    const savedDynamics = await entityManager.find(MonitorRiskDynamicsV2Entity, {
      where: {
        dynamicId: testMessageValue132.id,
      },
    });

    expect(savedDynamics[0].isvalid).toBe(0);
  });

  it.skip('风险动态列表', async () => {
    try {
      const { pageIndex, pageSize, total, data } = await monitorDynamicService.searchMonitorRiskDynamics(
        testUser,
        Object.assign(new RiskDynamicsSearchRequest(), {
          groupKeys: ['MonitorSupervisionRisk'],
          pageSize: 10,
          pageIndex: 1,
        }),
      );
      // expect(result).toBe('success');
      expect(total).toBeGreaterThan(0);
    } catch (error) {
      expect(error).toBeUndefined();
    }
  });

  it('风险动态列表，支持统一社会信用代码搜索', async () => {
    try {
      const { pageIndex, pageSize, total, data } = await monitorDynamicService.searchMonitorRiskDynamics(
        testUser,
        Object.assign(new RiskDynamicsSearchRequest(), {
          groupKeys: ['MonitorSupervisionRisk'],
          pageSize: 10,
          pageIndex: 1,
          searchKey: '11111111111111',
        }),
      );
      // expect(result).toBe('success');
      expect(total).toBe(0);
    } catch (error) {
      expect(error).toBeUndefined();
    }
  });

  it('kafka收到监控动态,黑名单排除失信被执行人', async () => {
    const testMessageValue = {
      77: {
        id: 'b1f7c3ef9afefc54482d22012f129d85',
        keyno: '078ecdacf53e7264675b03259b103b03',
        name: '江苏南通三建集团股份有限公司',
        risklevel: 1,
        category: 77,
        beforecontent: '',
        aftercontent: '失信被执行人',
        changeextend: '{"a":"失信被执行人","b":"最高人民法院","c":"2023-12-18","d":2,"e":2,"f":"有履行能力而拒不履行生效法律文书确定义务","g":1}',
        objectid: '1c7c0c8a203349a0bd912dd431540cb9',
        changestatus: 1,
        changedate: '2023/12/18 00:00:00',
        datatype: 1,
        extend1: '3',
        extend2: '',
        isvalid: 1,
        createdate: '2023/12/19 15:26:40',
        updatedate: '2023/12/19 15:31:46',
        importanceflag: 0,
        isrisk: 2,
        extend3: '',
        extend4: '',
        extend5: '',
      },
    };
    await Bluebird.map(Object.keys(testMessageValue), async (category) => {
      const messageValue = testMessageValue[category];
      const kafkaPayload: any = {
        message: {
          value: Buffer.from(JSON.stringify(messageValue)),
        },
      };
      const result = await monitorDynamicService.handleKafkaKysCompanyMonitorQueue(kafkaPayload);
      await Bluebird.delay(3000);
      const dynamics = await entityManager.find(MonitorRiskDynamicsV2Entity, {
        dynamicId: messageValue.id,
      });
      //77 黑名单，是失信被执行人类型，不会存数据
      expect(dynamics.length).toBe(0);
    });
  });

  it('kafka收到监控动态,破产重整，监控企业作为不是被申请人不会存数据', async () => {
    const testMessageValue = {
      58: {
        id: 'e2d64d309f4a285820b88bec4d7970c5',
        keyno: 'be130c2b36f438f5153cde0683bdb4ec',
        name: '广东江大和风香精香料有限公司',
        risklevel: 4,
        category: 58,
        beforecontent: '',
        aftercontent: '',
        changeextend:
          '{"d":1718899200,"a":"（2024）闽01破申334号","f":"福建省福州市中级人民法院","c":"[{\\"keyno\\":\\"35ab7f4b064cbc91b54911aab16f1a70\\",\\"name\\":\\"福州禾谷餐饮管理有限公司\\",\\"org\\":0,\\"showname\\":\\"福州禾谷餐饮管理有限公司\\"}]","b":"[{\\"keyno\\":\\"be130c2b36f438f5153cde0683bdb4ec\\",\\"name\\":\\"广东江大和风香精香料有限公司\\",\\"org\\":0,\\"showname\\":\\"广东江大和风香精香料有限公司\\"}]","e":"破产审查案件"}',
        objectid: 'ddcbc05afb4d40808e9cc90c6538b383',
        changestatus: 1,
        changedate: '2024/06/21 00:00:00',
        datatype: 1,
        extend1: '1',
        extend2: '',
        isvalid: 1,
        createdate: '2024/06/21 15:43:25',
        updatedate: '2024/06/21 15:45:31',
        importanceflag: 1,
        isrisk: 1,
        extend3: '',
        extend4: '',
        extend5: '',
      },
    };
    await Bluebird.map(Object.keys(testMessageValue), async (category) => {
      const messageValue = testMessageValue[category];
      const kafkaPayload: any = {
        message: {
          value: Buffer.from(JSON.stringify(messageValue)),
        },
      };
      const result = await monitorDynamicService.handleKafkaKysCompanyMonitorQueue(kafkaPayload);
      await Bluebird.delay(3000);
      const dynamics = await entityManager.find(MonitorRiskDynamicsV2Entity, {
        dynamicId: messageValue.id,
      });
      //58 破产重整，是监控企业作为被申请人，不会存数据
      expect(dynamics.length).toBe(0);
    });
  });

  it.skip('test companyViewStatistic', async () => {
    const response = await monitorDynamicService.companyViewStatistic(
      testUser,
      Object.assign(new RiskDynamicsSearchRequest(), {
        diligenceAt: [
          {
            currently: true,
            flag: 1,
            number: 1,
            unit: 'day',
          },
        ],
      }),
    );
    expect(response.groupCompanyCount).toBeDefined();
  });
});
