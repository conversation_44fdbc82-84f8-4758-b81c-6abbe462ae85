import { SettingsService } from '../settings/settings.service';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { MonitorGroupCompanyEntity } from '../../libs/entities/MonitorGroupCompanyEntity';
import { EntityManager, In, Repository } from 'typeorm';
import * as Bluebird from 'bluebird';
import { SettingTypeEnums } from '../../libs/model/settings/SettingTypeEnums';
import { BadRequestException, Injectable } from '@nestjs/common';
import { CompanyMonitorModelPO } from '../../libs/model/monitor/CompanyMonitorModelPO';
import { GetMonitorDynamicRemarkRequest, MonitorDynamicHandleRequest } from 'libs/model/monitor/request/MonitorDynamicHandleRequest';
import { RoverUser } from 'libs/model/common';
import { MonitorDynamicStatus, MonitorDynamicType, MonitorRiskEnums } from 'libs/enums/monitor/MonitorRiskEnums';
import { MonitorDynamicsRemarkEntity } from 'libs/entities/MonitorDynamicsRemarkEntity';
import { MonitorRiskDynamicsV2Entity } from 'libs/entities/MonitorRiskDynamicsV2Entity';
import { MonitorSentimentDynamicsV2Entity } from 'libs/entities/MonitorSentimentDynamicsV2Entity';
import { pick, forEach } from 'lodash';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import MyOssService from '../basic/my-oss.service';
import { MyRelatedCompanyInfo } from './monitor.types';
import { CompanyDetailService } from '../company/company-detail.service';
import { OrgSettingsLogEntity } from 'libs/entities/OrgSettingsLogEntity';

// import { Cacheable } from 'type-cacheable';

@Injectable()
export class MonitorCommonService {
  constructor(
    private readonly settingService: SettingsService,
    @InjectRepository(MonitorGroupCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorGroupCompanyEntity>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly myOssService: MyOssService,
    private readonly companyDetailService: CompanyDetailService,
  ) {}

  /**
   * 获取所有监控了指定公司的组织监控模型
   * @param companyId
   * @param monitorType
   * @param validOrgIds 未过期的 orgId 列表
   * @returns
   */
  async matchMonitorOrgSetting(
    companyId: string,
    monitorType: SettingTypeEnums = SettingTypeEnums.monitor,
    validOrgIds?: number[],
  ): Promise<CompanyMonitorModelPO[]> {
    const result: CompanyMonitorModelPO[] = [];
    let groupCompanies: MonitorGroupCompanyEntity[];
    if (validOrgIds?.length) {
      groupCompanies = await this.monitorCompanyRepo.find({ companyId, orgId: In(validOrgIds) });
    } else {
      groupCompanies = await this.monitorCompanyRepo.find({ companyId });
    }
    if (!groupCompanies?.length) {
      return result;
    }
    await Bluebird.map(
      groupCompanies,
      async (company: MonitorGroupCompanyEntity) => {
        const setting = await this.settingService.getOrgLatestSettings(company.orgId, monitorType);
        if (setting) {
          result.push({ groupId: company.groupId, setting, companyName: company.companyName });
        }
      },
      { concurrency: 10 },
    );
    return result;
  }

  /**
   * 根据维度key 获取 一组 org下 生效的设置
   * @param orgIds
   * @param dimensionKey
   * @param monitorType
   * @returns
   */
  async matchMonitorOrgSettingsByDimension(
    orgIds: number[],
    dimensionKey: MonitorRiskEnums,
    monitorType: SettingTypeEnums = SettingTypeEnums.monitor,
  ): Promise<OrgSettingsLogEntity[]> {
    // 如果没有组织ID，直接返回空数组
    if (!orgIds?.length) {
      return [];
    }

    const orgSettings: OrgSettingsLogEntity[] = await this.settingService.getActiveLatestSettingsByOrgIds(orgIds, monitorType);

    // 使用filter过滤出content中包含指定dimensionKey且status为1的orgSetting
    return orgSettings.filter((orgSetting) => {
      if (!Array.isArray(orgSetting?.content)) {
        return false;
      }

      return orgSetting.content.some(
        (dimensionLevel1) =>
          Array.isArray(dimensionLevel1?.items) &&
          dimensionLevel1.items.some((dimensionLevel2) => dimensionLevel2?.key === dimensionKey && dimensionLevel2.status === 1),
      );
    });
  }

  /**
   * 动态处理接口
   * @param currentUser
   * @param searchBody
   * @returns
   */
  public async dynamicHandle(currentUser: RoverUser, searchBody: MonitorDynamicHandleRequest) {
    const { currentOrg: orgId, userId } = currentUser;
    let dynamic: MonitorRiskDynamicsV2Entity | MonitorSentimentDynamicsV2Entity;

    if (searchBody.type == MonitorDynamicType.Risk) {
      // 风险动态
      dynamic = await this.entityManager.findOne(MonitorRiskDynamicsV2Entity, { id: +searchBody.dynamicId });
      // await manager.update(MonitorRiskDynamicsV2Entity, { id: +searchBody.dynamicId }, { status });
    } else if (searchBody.type == MonitorDynamicType.Sentiment) {
      dynamic = await this.entityManager.findOne(MonitorSentimentDynamicsV2Entity, { hashkey: searchBody.dynamicId });
    } else {
      throw new BadParamsException(RoverExceptions.Monitor.Dynamic.MonitorDynamicTypeNotFound);
    }

    if (dynamic.status === MonitorDynamicStatus.Processed) {
      throw new BadRequestException(RoverExceptions.Monitor.Dynamic.NotNeedHandle);
    }

    const remark = await this.entityManager.save(
      Object.assign(
        new MonitorDynamicsRemarkEntity(),
        {
          orgId,
          updateBy: userId,
        },
        pick(searchBody, ['dynamicId', 'type', 'grade', 'way', 'comment', 'attachments']),
      ),
    );

    // 1 待处理， 2 处理中， 3 已处理
    dynamic.status = searchBody.status;
    await this.entityManager.save(dynamic);
    return remark;
  }

  /**
   * 查询动态的处理记录
   * @param currentUser
   * @param searchBody
   * @returns
   */
  public async getDynamicRemark(currentUser: RoverUser, searchBody: GetMonitorDynamicRemarkRequest) {
    const { currentOrg: orgId } = currentUser;
    const { type, dynamicId, pageIndex, pageSize } = searchBody;

    const [data, total] = await this.entityManager
      .createQueryBuilder(MonitorDynamicsRemarkEntity, 'remark')
      .leftJoinAndSelect('remark.operator', 'operator')
      .select(['remark', 'operator.name', 'operator.userId'])
      .where('remark.orgId = :orgId', { orgId })
      .andWhere('remark.type = :type', { type })
      .andWhere('remark.dynamicId = :dynamicId', { dynamicId })
      .addOrderBy('remark.id', 'DESC')
      .skip(pageSize * (pageIndex - 1))
      .take(pageSize)
      .getManyAndCount();

    data.forEach((e) => {
      e?.attachments?.forEach((x) => {
        x.fileUrl = this.myOssService.signSingleUrl(x.fileUrl);
      });
    });
    return { pageIndex, pageSize, total, data };
  }

  // @Cacheable({ ttlSeconds: 600 })
  async getRelatedCompanies(keyNo: string, orgId: number): Promise<MyRelatedCompanyInfo[]> {
    const data = await this.companyDetailService.getRelatedList(keyNo);
    const items = (data.RelatedList as MyRelatedCompanyInfo[]).filter((it) => !it.KeyNo.startsWith('p'));
    const monitored = await this.monitorCompanyRepo.find({
      where: {
        companyId: In(items.map((it) => it.KeyNo)),
        orgId,
      },
    });
    for (const item of items) {
      item.isMonitored = !!monitored.find((it) => item.KeyNo === it.companyId);
    }
    return items;
  }
}
