import { KysCompanyResponseDetails, KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverBundleCounterType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { BadRequestException, Injectable, Scope } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { KysMoniterCompanyListEntity } from 'libs/riskentities/KysMoniterCompanyListEntity';
import { compact, difference, groupBy, gt, intersection, pick } from 'lodash';
import { Logger } from 'log4js';
import * as moment from 'moment';
import { Brackets, createConnection, getConnection, In, MoreThan, Not, Repository } from 'typeorm';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';
import { v4 as uuid } from 'uuid';
import { QueryBuilderHelper } from '../../libs/common/sql.helper';
import { ConfigService } from '../../libs/config/config.service';
import { HttpUtilsService } from '../../libs/config/httputils.service';
import { SecurityService } from '../../libs/config/security.service';
import { ForbiddenStandardCode } from '../../libs/constants/common';
import { DiligenceHistoryEntity } from '../../libs/entities/DiligenceHistoryEntity';
import { MonitorCertificationEntity, MonitorCertificationItem } from '../../libs/entities/MonitorCertificationEntity';
import { MonitorGroupCompanyEntity } from '../../libs/entities/MonitorGroupCompanyEntity';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { MonitorRiskDynamicsV2Entity } from '../../libs/entities/MonitorRiskDynamicsV2Entity';
import { MonitorRiskMessageEntity } from '../../libs/entities/MonitorRiskMessageEntity';
import { UserEntity } from '../../libs/entities/UserEntity';
import { BatchStatusEnums } from '../../libs/enums/batch/BatchStatusEnums';
import { MonitorRiskEnums } from '../../libs/enums/monitor/MonitorRiskEnums';
import { OperatorTypeEnums } from '../../libs/enums/oplog/OperatorTypeEnums';
import { PermissionByEnum } from '../../libs/enums/PermissionScopeEnum';
import { EntityDuplicatedException } from '../../libs/exceptions/EntityDuplicatedException';
import { RoverExceptions } from '../../libs/exceptions/exceptionConstants';
import { CompanyBasicInfo } from '../../libs/model/blacklist/RemoveCustomerResponse';
import { AffectedResponse, RoverUser } from '../../libs/model/common';
import { AggsResponse } from '../../libs/model/customer/SearchCustomerResponse';
import { QueryParamsEnums } from '../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { CompanyMonitorModelPO } from '../../libs/model/monitor/CompanyMonitorModelPO';
import { MonitorAgssRequest } from '../../libs/model/monitor/MonitorAgssRequest';
import { MonitorDimensionType } from '../../libs/model/monitor/MonitorDimensionPO';
import { AddCompanyRequest } from '../../libs/model/monitor/request/AddCompanyRequest';
import { ListSearchCompanyDictRequest } from '../../libs/model/monitor/request/ListSearchCompanyDictRequest';
import { SearchCompanyRequest } from '../../libs/model/monitor/request/SearchCompanyRequest';
import { UpdateCompanyRequest } from '../../libs/model/monitor/request/UpdateCompanyRequest';
import { MonitorGroupCompany, SearchCompanyResponse } from '../../libs/model/monitor/response/SearchCompanyResponse';
import { CompanyDetailService } from '../company/company-detail.service';
import { CompanySearchService } from '../company/company-search.service';
import { CompanyBusinessInfo } from '../company/model/CompanyBusinessInfo';
import { CompanyDetails } from '../company/model/CompanyDetails';
import { OpLogService } from '../oplog/oplog.service';
import OrganizationActivatedEvent from '../schedule/OrganizationActivatedEvent';
import OrganizationSuspendedEvent from '../schedule/OrganizationSuspendedEvent';
import { CreateCompanyResponse } from './model/CreateCompanyResonse';
import { MonitorRiskProcessModel } from './model/monitorRiskModel';
import { MonitorCommonService } from './monitor.common.service';
import { CompanyCertificationInfo } from './monitor.types';
import CommonHelper from './risk.copy.from.c/commonHelper';
import { MonitorRiskDynamicService } from './risk/monitor.risk.dynamic.service';
import { RateTrendInfo } from 'apps/company/model/CreditRateTrendResult';
import { CompanyEntity } from 'libs/entities/CompanyEntity';
import { OrgSettingsLogEntity } from 'libs/entities/OrgSettingsLogEntity';

@Injectable({ scope: Scope.DEFAULT })
export class MonitorCompanyService {
  private logger: Logger = QccLogger.getLogger(MonitorCompanyService.name);

  constructor(
    @InjectRepository(MonitorGroupCompanyEntity) private readonly monitorGroupCompanyRepo: Repository<MonitorGroupCompanyEntity>,
    @InjectRepository(MonitorGroupEntity) private readonly monitorGroupRepo: Repository<MonitorGroupEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(UserEntity) private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(MonitorCertificationEntity) private readonly certificationRepository: Repository<MonitorCertificationEntity>,
    @InjectRepository(MonitorRiskDynamicsV2Entity) private readonly riskDynamicsRepo: Repository<MonitorRiskDynamicsV2Entity>,
    @InjectRepository(CompanyEntity) private readonly companyRepo: Repository<CompanyEntity>,
    @InjectRepository(MonitorRiskMessageEntity) private readonly riskMessageRepo: Repository<MonitorRiskMessageEntity>,
    private readonly httpUtils: HttpUtilsService,
    private readonly configService: ConfigService,
    private readonly bundleService: RoverBundleService,
    private readonly companySearchService: CompanySearchService,
    private readonly oplogService: OpLogService,
    private readonly securityService: SecurityService,
    private readonly monitorCommonService: MonitorCommonService,
    private readonly companyDetailService: CompanyDetailService,
    private readonly monitorRiskDynamicService: MonitorRiskDynamicService,
  ) {}

  async checkCountLimit(currentUser: RoverUser, num: number) {
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.MonitorCompanyQuantity);
    await bundleCounter.check(num);
  }

  public async getRiskDbConn() {
    let connection;
    try {
      // 尝试获取已存在的连接
      connection = getConnection(this.configService.riskDB.name);
    } catch (error) {
      // 连接不存在，创建新的连接
      connection = await createConnection(this.configService.riskDB);
    }
    return connection;
  }

  public async create(currentUser: RoverUser, requests: AddCompanyRequest[], related = false): Promise<MonitorGroupCompanyEntity[]> {
    if (!requests || requests.length == 0) return [];
    const { userId, currentOrg: orgId } = currentUser;
    // 校验公司有无重复
    const companyIds: string[] = requests.map((x) => x.companyId);
    const companyMap: Map<string, string> = new Map<string, string>();
    requests.forEach((req) => {
      companyMap.set(req.companyId, req.companyName);
    });
    await this.checkCompanyIsSupport(companyIds);
    const dbCompanies = await this.monitorGroupCompanyRepo.find({ orgId, companyId: In(companyIds) });
    if (dbCompanies?.length) {
      // throw new BadRequestException(RoverExceptions.Monitor.Company.Duplicated);
      throw new EntityDuplicatedException(
        RoverExceptions.Monitor.Company.Duplicated,
        dbCompanies.map((d) => d.id),
      );
    }
    await this.checkMonitorGroup(requests, orgId, currentUser);

    // 校验总的公司数量是否超限
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.MonitorCompanyQuantity);
    // await bundleCounter.clear();
    await bundleCounter.increase(companyIds.length);

    // 校验关联方是否存在
    if (related) {
      const relatedCompanyIds = new Set();
      requests.forEach((r) => {
        if (r.relatedCompanyId) {
          relatedCompanyIds.add(r.relatedCompanyId);
        }
      });
      if (relatedCompanyIds.size) {
        const list = await this.monitorGroupCompanyRepo.find({
          where: {
            companyId: In(Array.from(relatedCompanyIds.values())),
            orgId: currentUser.currentOrg,
          },
        });
        if (list.length !== relatedCompanyIds.size) {
          throw new BadRequestException(RoverExceptions.Monitor.Company.InvalidRelatedCompanies);
        }
      }
    } else {
      requests.forEach((r) => {
        r.relatedCompanyId = undefined;
      });
    }

    // 查询每个公司最新的尽调记录
    const companyResultsMap = await this.getCompanyResultMap(orgId, companyIds);
    const groupCompany: MonitorGroupCompanyEntity[] = requests.map((r) =>
      Object.assign(new MonitorGroupCompanyEntity(), r, {
        createBy: userId,
        orgId: orgId,
        depId: currentUser.departments?.[0],
        result: companyResultsMap.get(r.companyId)?.result,
        diligenceId: companyResultsMap.get(r.companyId)?.diligenceId,
        diligenceDate: companyResultsMap.get(r.companyId)?.diligenceDate,
      }),
    );
    //创建company详情数据
    await this.companySearchService.createCompanyInfoBatch(companyMap);

    try {
      // 连接C端风险推送数据库，添加监控企业
      await this.sendToRiskDb(companyIds);
      // 添加到证书监控列表
      for (const companyId of companyIds) {
        await this.setupMonitorCertificationStep(companyId);
      }

      // 添加到企业监控列表
      const res = await this.monitorGroupCompanyRepo.save(groupCompany);
      // 添加操作日志
      await this.oplogService.add(
        OperatorTypeEnums.Create,
        MonitorGroupCompanyEntity,
        currentUser,
        res?.map((r) => r.id),
        res,
      );
      return res;
    } catch (error) {
      await bundleCounter.decrease(companyIds.length);
      throw error;
    }
  }

  /**
   * 通过 excel 导入监控列表
   * @param currentUser
   * @param requests
   * @param isUpdate
   */
  public async createFromExcel(currentUser: RoverUser, requests: AddCompanyRequest[], isUpdate: boolean): Promise<MonitorGroupCompanyEntity[]> {
    if (isUpdate) {
      //监控列表的原因，如果是更新则先删除原有数据
      const removeRequest = new SearchCompanyRequest();
      removeRequest.companyIds = requests.map((r) => r.companyId);
      await this.remove(currentUser, removeRequest);
    }
    return this.create(currentUser, requests);
  }

  /**
   * 移出监控
   * @param currentUser
   * @param postData
   * @returns
   */
  public async remove(currentUser: RoverUser, postData: SearchCompanyRequest) {
    const { currentOrg: orgId } = currentUser;
    //待删除的监控企业id
    const removeCompanies = await this.getRemoveCompanies(currentUser, postData);
    const removeIds = removeCompanies?.map((m) => m.id);
    const companyIds = removeCompanies?.map((m) => m.companyId);
    const preResult = await this.monitorGroupCompanyRepo.findByIds(removeIds);
    if (!preResult?.length) {
      return null;
    }

    // 检查关联方
    const relatedCompanies = await this.monitorGroupCompanyRepo.find({
      where: {
        relatedCompanyId: In(companyIds),
        orgId: currentUser.currentOrg,
      },
    });
    if (relatedCompanies.length) {
      relatedCompanies.forEach((r) => {
        preResult.push(r);
        removeCompanies.push(r);
        removeIds.push(r.id);
        companyIds.push(r.companyId);
      });
    }

    // 从企业监控列表删除
    const deleteResult = await this.monitorGroupCompanyRepo.delete({ orgId, id: In(removeIds) });
    // 恢复套餐额度
    const depIdsGroup = groupBy(preResult, (g) => g?.depId);
    await Bluebird.map(Object.keys(depIdsGroup), async (depId) => {
      const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.MonitorCompanyQuantity, depId);
      // await bundleCounter.clear();
      await bundleCounter.decrease(depIdsGroup[depId].length);
    });
    // 添加操作日志
    await Bluebird.all([this.oplogService.add(OperatorTypeEnums.Remove, MonitorGroupCompanyEntity, currentUser, removeIds, removeCompanies)]);
    try {
      await this.afterRemove(companyIds);
    } catch (error) {
      this.logger.error(error);
    }
    return deleteResult;
  }

  private async afterRemove(companyIds: string[]) {
    // 判断取消监控的企业是否被其他租户监控
    const existMonitorCompany = await this.monitorGroupCompanyRepo.find({ companyId: In(companyIds) });
    const removeKeyno = difference(
      companyIds,
      existMonitorCompany.map((e) => e.companyId),
    );

    if (removeKeyno.length) {
      // 从C端风险推送数据库中移出
      const conn = await createConnection(this.configService.riskDB);
      const env = this.configService.stage;
      // 添加监控的企业到C端风险推送数据库
      await conn.createEntityManager().delete(KysMoniterCompanyListEntity, { keyno: In(removeKeyno), env });

      // 删除证书监控列表
      await this.certificationRepository.delete({ companyId: In(removeKeyno) });
    }
  }

  private async getRemoveCompanies(currentUser: RoverUser, postData: SearchCompanyRequest): Promise<MonitorGroupCompanyEntity[]> {
    const { currentOrg: orgId } = currentUser;
    const { result } = postData;
    let data: MonitorGroupCompanyEntity[];
    const qb = await this.getBaseQueryBuilder(currentUser, postData);
    data = await qb.getMany();
    let companyResultMap;
    if (data?.length) {
      const companyIds = data.map((x) => x.companyId);
      companyResultMap = await this.getCompanyResultMap(orgId, companyIds);
      if (result?.length > 0) {
        data = data.filter((d) => result.includes(companyResultMap.get(d.companyId)?.result));
      }
    }
    const { clientRequest, useEsFilter } = this.createClientRequest(postData);

    const dataMap: Map<string, MonitorGroupCompanyEntity> = new Map();
    data.forEach((d) => dataMap.set(d.companyId, d));

    const esPageSize = 200;
    clientRequest.pageIndex = 1;
    clientRequest.pageSize = esPageSize; // 最多一次查200条

    if (useEsFilter) {
      const esData: KysCompanyResponseDetails[] = [];
      for (let i = 0; i < data.length; i += esPageSize) {
        clientRequest.filter.ids = data.slice(i, i + esPageSize).map((d) => d.companyId);
        const esResp = await this.companySearchService.companySearchForKys(clientRequest);
        if (esResp?.Result?.length > 0) {
          esData.push(...esResp.Result);
        }
      }
      data = esData.map((d) => dataMap.get(d.id));
    }
    return data;
  }

  /**
   * 创建 es client 查询条件
   * @param postData
   * @param includeFields
   * @private
   */
  private createClientRequest(postData: SearchCompanyRequest, includeFields: string[] = ['id']) {
    let useEsFilter = false; // 是否使用es来过滤
    const clientRequest: KysCompanySearchRequest = new KysCompanySearchRequest();
    clientRequest.includeFields = includeFields;
    clientRequest.filter = {};

    if (postData?.econKindCode?.length) {
      clientRequest.filter.ekc = postData.econKindCode;
      useEsFilter = true;
    }
    // 国民行业
    if (postData?.industry?.length) {
      clientRequest.filter.i = postData.industry;
      useEsFilter = true;
    }
    // 行政地区
    if (postData?.region?.length) {
      clientRequest.filter.r = postData.region;
      useEsFilter = true;
    }
    //成立日期
    if (postData.startDateCode) {
      clientRequest.filter.sd = postData.startDateCode[0];
      useEsFilter = true;
    }
    //注册状态
    if (postData.statusCode?.length) {
      clientRequest.filter.sc = postData.statusCode;
      useEsFilter = true;
    }
    //注册资本
    if (postData.registcapiAmount?.length) {
      clientRequest.filter.rca = postData.registcapiAmount;
      useEsFilter = true;
    }

    //公司 companyId
    if (postData.companyIds?.length) {
      clientRequest.filter.ids = postData.companyIds;
      useEsFilter = true;
    }
    return { useEsFilter, clientRequest };
  }

  public async update(currentUser: RoverUser, postData: UpdateCompanyRequest): Promise<AffectedResponse> {
    const { currentOrg: orgId } = currentUser;
    const resultGroup = await this.monitorGroupRepo.findOne({ orgId, id: postData.groupId });
    if (!resultGroup) {
      throw new BadRequestException(RoverExceptions.Monitor.Group.NotFound);
    }
    const companyEntities = await this.monitorGroupCompanyRepo.findByIds(postData.ids);
    if (companyEntities.length != postData.ids.length) {
      throw new BadRequestException(RoverExceptions.Monitor.Company.NotFound);
    }
    // //关联方公司也需要计算在内
    const relatedCompanyEntities =
      (await this.monitorGroupCompanyRepo.find({
        where: {
          id: Not(In(postData.ids)),
          orgId: currentUser.currentOrg,
          relatedCompanyId: In(companyEntities.map((x) => x.companyId)),
        },
      })) || [];

    // 取消监控分组下公司数量限制校验 RA-16508
    // const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    // const groupLimit = userBundle[RoverBundleLimitationType.MonitorGroupCompanyQuantity]?.value || 1000;
    // const countByGroupCompany = await this.monitorGroupCompanyRepo.count({ orgId, groupId: postData.groupId });
    // if (countByGroupCompany + companyEntities.length + (relatedCompanyEntities.length || 0) > groupLimit) {
    //   throw new BadRequestException(RoverExceptions.Monitor.Company.GroupCompanyLimit);
    // }
    // 由于分组监控的原因 需要删除再创建
    const removeIds = [...companyEntities.map((x) => x.id), ...relatedCompanyEntities.map((x) => x.id)];
    await this.remove(currentUser, Object.assign(new SearchCompanyRequest(), { ids: removeIds }));
    //有关联方的公司和没关联方的公司要分开处理，优先添加无关联方的公司
    const noRelatedCompanyEntities = companyEntities.filter((x) => !x.relatedCompanyId) || [];
    const reRelatedCompanyEntities = companyEntities.filter((x) => x.relatedCompanyId) || [];
    await this.createCompanyEntities(currentUser, postData, noRelatedCompanyEntities);
    //移动分组同时如果监控公司有关联方，一同移动到同一分组中
    await this.createCompanyEntities(currentUser, postData, compact([...reRelatedCompanyEntities, ...relatedCompanyEntities]), true);
    const affectedResponse: AffectedResponse = new AffectedResponse();
    affectedResponse.affected = companyEntities?.length;
    return affectedResponse;
  }

  async createCompanyEntities(currentUser: RoverUser, postData: UpdateCompanyRequest, entities: MonitorGroupCompanyEntity[], related = false) {
    if (entities?.length) {
      await this.create(
        currentUser,
        entities.map((x) =>
          Object.assign(new AddCompanyRequest(), {
            companyId: x.companyId,
            companyName: x.companyName,
            groupId: postData.groupId,
            relatedCompanyId: x.relatedCompanyId,
          }),
        ),
        related,
      );
    }
  }

  /**
   * 同步监控企业到 C端风险动态推送
   * @param orgId  -1时同步全部组织
   * @returns
   */
  public async sycnCompanyToRiskDB(orgId: number) {
    const qb = this.monitorGroupCompanyRepo.createQueryBuilder('company');

    if (orgId > 0) {
      qb.where('company.orgId = :orgId', { orgId });
    }
    const result = await qb.select('company.companyId', 'keyno').distinct().getRawMany();
    const companyIds = result.map((x) => x.keyno);

    // 连接C端风险推送数据库，添加监控企业
    const saveEntity = await this.sendToRiskDb(companyIds);
    return saveEntity.length;
  }

  public async listDict(currentUser: RoverUser, postData: ListSearchCompanyDictRequest) {
    const { by, userIds } = this.securityService.checkScope(currentUser, 2101);
    const resp = {};
    if (!postData?.dicts?.length) return resp;
    for (const dict of postData.dicts) {
      resp[dict] = [];
    }
    const { currentOrg: orgId } = currentUser;

    // 查询组织下属的全部companyId
    const qb = this.monitorGroupCompanyRepo.createQueryBuilder('company').select(['company_id as id']).where('company.orgId = :orgId', { orgId });
    if (postData?.groupId) {
      qb.andWhere('company.groupId = :groupId', { groupId: postData.groupId });
    }
    if (by == PermissionByEnum.USER) {
      qb.andWhere('company.createBy in (:...userIds)', { userIds });
    }
    const ids = (await qb.getRawMany()).map((x) => x.id);

    // 向resp中注入econkindcode和statuscode
    if (ids.length == 0) {
      return resp;
    }

    if (postData.dicts.includes('creators')) {
      // 查询操作人选项
      let queryIsValid = true;
      const sqlParams: any[] = [orgId];
      let existsSql = 'select 1 from monitor_group_company c where c.create_by = u.user_id';
      if (postData.groupId) {
        existsSql += ` and c.group_id = ?`;
        sqlParams.push(postData.groupId);
      }
      let sql = `select u.user_id, u.name from e_user u where u.org_id = ? and exists(${existsSql})`;
      if (by != PermissionByEnum.ORG) {
        if (userIds?.length) {
          sql += ` and u.user_id in (?)`;
          sqlParams.push(userIds);
        } else {
          queryIsValid = false;
        }
      }
      if (queryIsValid) {
        const users = await this.userRepository.query(sql, sqlParams);
        resp['creators'] = users.map((it: any) => ({
          userId: it.user_id,
          name: it.name,
        }));
      }
    }

    const clientRequest: KysCompanySearchRequest = new KysCompanySearchRequest();
    clientRequest.includeFields = ['id'];
    clientRequest.filter = { ids };
    clientRequest.pageSize = 1;
    clientRequest.pageIndex = 1;
    clientRequest['aggFields'] = postData.dicts;
    const esResp = await this.companySearchService.companySearchForKys(clientRequest);
    if (esResp?.GroupItems?.length) {
      for (const agg of esResp.GroupItems) {
        for (const dict of postData.dicts) {
          if (agg[dict]?.length) {
            resp[dict] = agg[dict].map((x) => x.key);
          }
        }
      }
    }
    return resp;
  }

  /**
   * 获取风险等级统计
   * @param orgId
   * @param companyIds
   * @private
   */
  private async getCompanyResultMap(
    orgId: number,
    companyIds: string[],
  ): Promise<Map<string, { companyId: string; result: number; diligenceId: number; diligenceDate: Date }>> {
    const companyResultMap: Map<string, { companyId: string; result: number; diligenceId: number; diligenceDate: Date }> = new Map(); // 风险等级统计
    const backgroundDiligenceEntities = await this.diligenceRepo.query(
      `select id as diligenceId, company_id, result, create_date from due_diligence
          join (select max(id) as max_id from due_diligence
                where org_id = ? and company_id in (?)
                group by company_id) as temp on due_diligence.id = temp.max_id`,
      [orgId, companyIds],
    );
    backgroundDiligenceEntities.forEach((entity) => {
      companyResultMap.set(entity.company_id, {
        companyId: entity.company_id,
        result: Number(entity.result),
        diligenceId: Number(entity.diligenceId),
        diligenceDate: entity.create_date,
      });
    });
    return companyResultMap;
  }

  public async search(currentUser: RoverUser, postData: SearchCompanyRequest): Promise<SearchCompanyResponse> {
    const { currentOrg: orgId } = currentUser;
    const { result, pageSize, pageIndex, sortField, isSortAsc } = postData;
    const needSkipSize = pageSize * (pageIndex - 1);
    const qb = await this.getBaseQueryBuilder(currentUser, postData);
    let data: MonitorGroupCompanyEntity[] = await qb.getMany();
    let companyIds = data.map((x) => x.companyId);
    if (data.length == 0) {
      return {
        pageSize,
        pageIndex,
        data: [],
        total: 0,
      };
    }

    const companyResultMap = await this.getCompanyResultMap(orgId, companyIds);
    if (result?.length > 0) {
      data = data.filter((d) => result.includes(companyResultMap.get(d.companyId)?.result));
      companyIds = data.map((x) => x.companyId);
      if (data.length == 0) {
        return {
          pageSize,
          pageIndex,
          data: [],
          total: 0,
        };
      }
    }

    // target ids
    const companyId2Ids = new Map<string, number>();
    // related company id to target id
    const relatedMap = new Map<string, number>();
    data.forEach((d) => {
      if (!d.relatedCompanyId) {
        companyId2Ids.set(d.companyId, d.id);
      }
    });
    data.forEach((d) => {
      if (d.relatedCompanyId) {
        const id = companyId2Ids.get(d.relatedCompanyId);
        if (id) {
          relatedMap.set(d.companyId, id);
        }
      }
    });

    let useEsSort = false; // 是否使用es来排序
    const { clientRequest, useEsFilter } = this.createClientRequest(postData);
    //成立日期排序
    if (sortField == 'startDateCode') {
      clientRequest.sortField = 'startdatecode';
      clientRequest.sortOrder = isSortAsc ? 'ASC' : 'DESC';
      useEsSort = true;
    }
    //注册资本排序
    if (sortField == 'registcapi' || sortField == 'registcapiAmount') {
      clientRequest.sortField = 'registcapiamount';
      clientRequest.sortOrder = isSortAsc ? 'ASC' : 'DESC';
      useEsSort = true;
    }

    const dataMap: Map<string, MonitorGroupCompanyEntity> = new Map();
    data.forEach((d) => dataMap.set(d.companyId, d));

    if (useEsSort) {
      // 一次查出所有记录 在内存里重新调整顺序
      clientRequest.pageSize = 200;
      clientRequest.filter.ids = companyIds;

      const esResults = [];
      let monitorGroupCompanies: MonitorGroupCompany[] = [];
      for (let i = 1; i <= 10; i++) {
        clientRequest.pageIndex = i;
        const esResp = await this.companySearchService.companySearchForKys(clientRequest);

        if (esResp?.Result?.length) {
          esResults.push(...esResp.Result);
        } else {
          break;
        }
      }

      if (esResults.length > 0) {
        // 对esResp.Result按照data顺序排序
        // target id to related ids
        const masterList = [];
        const relatedResultMap = new Map<string, object[]>();
        esResults.forEach((d) => {
          const targetId = relatedMap.get(d.id);
          if (targetId) {
            if (relatedResultMap.has(d.id)) {
              relatedResultMap.get(d.id).push(d);
            } else {
              relatedResultMap.set(d.id, [d]);
            }
          } else {
            masterList.push(d);
          }
        });
        const result = [];
        for (const masterListElement of masterList) {
          result.push(masterListElement);
          if (relatedResultMap.has(masterListElement.id)) {
            result.push(...relatedResultMap.get(masterListElement.id));
          }
        }
        monitorGroupCompanies = result
          .slice(pageSize * (pageIndex - 1), pageSize * pageIndex)
          .map((d) => this.buildMonitorGroupCompany(dataMap.get(d.id), d, companyResultMap.get(d.id)?.result));
      }
      return {
        pageSize,
        pageIndex,
        data: monitorGroupCompanies,
        total: companyIds.length,
      };
    } else {
      const esPageSize = 200;
      clientRequest.pageIndex = 1;
      clientRequest.pageSize = esPageSize; // 最多一次查200条
      // 按创建时间排序
      if (sortField) {
        data.sort((d1, d2) => {
          if (sortField == 'result') {
            return isSortAsc
              ? companyResultMap.get(d1.companyId)?.result - companyResultMap.get(d2.companyId)?.result
              : companyResultMap.get(d2.companyId)?.result - companyResultMap.get(d1.companyId)?.result;
          } else {
            if (gt(d1[sortField], d2[sortField])) {
              return isSortAsc ? 1 : -1;
            }
            return isSortAsc ? -1 : 1;
          }
        });
      } else {
        data.sort((d1, d2) => d2.id - d1.id);
      }
      // 二次排序
      const relatedResultMap = new Map<number, object[]>();
      const masterList = [];
      data.forEach((d) => {
        const targetId = relatedMap.get(d.companyId);
        if (targetId) {
          if (relatedResultMap.has(targetId)) {
            relatedResultMap.get(targetId).push(d);
          } else {
            relatedResultMap.set(targetId, [d]);
          }
        } else {
          masterList.push(d);
        }
      });
      const result = [];
      for (const masterListElement of masterList) {
        result.push(masterListElement);
        if (relatedResultMap.has(masterListElement.id)) {
          result.push(...relatedResultMap.get(masterListElement.id));
        }
      }
      data = result;
      if (useEsFilter) {
        const esData: KysCompanyResponseDetails[] = [];
        for (let i = 0; i < data.length; i += esPageSize) {
          clientRequest.filter.ids = data.slice(i, i + esPageSize).map((d) => d.companyId);
          const esResp = await this.companySearchService.companySearchForKys(clientRequest);
          if (esResp?.Result?.length > 0) {
            esData.push(...esResp.Result);
          }
        }
        // 将es数据按照data顺序排序
        const dataOrderMap: Map<string, number> = new Map();
        for (let i = 0; i < data.length; i++) {
          dataOrderMap.set(data[i].companyId, i);
        }
        esData.sort((d1, d2) => dataOrderMap.get(d1.id) - dataOrderMap.get(d2.id));
        let monitorGroupCompanies: MonitorGroupCompany[] = [];
        if (esData.length > needSkipSize) {
          monitorGroupCompanies = esData
            .slice(needSkipSize, needSkipSize + pageSize)
            .map((d) => this.buildMonitorGroupCompany(dataMap.get(d.id), d, companyResultMap.get(d.id)?.result));
        }
        return {
          pageSize,
          pageIndex,
          data: monitorGroupCompanies,
          total: esData.length,
        };
      } else {
        const resp: SearchCompanyResponse = {
          pageSize,
          pageIndex,
          data: [],
          total: data.length,
        };
        if (data.length > needSkipSize) {
          const pageData = data.slice(needSkipSize, needSkipSize + pageSize);
          clientRequest.filter.ids = pageData.map((d) => d.companyId);
          let esData: KysCompanyResponseDetails[] = [];
          const esResp = await this.companySearchService.companySearchForKys(clientRequest);
          if (esResp?.Result?.length > 0) {
            esData = esResp.Result;
          }
          const esDataMap: Map<string, KysCompanyResponseDetails> = new Map();
          esData.forEach((d) => esDataMap.set(d.id, d));
          resp.data = pageData.map((e) => this.buildMonitorGroupCompany(e, esDataMap.get(e.companyId), companyResultMap.get(e.companyId)?.result));
        }
        return resp;
      }
    }
  }

  public async search2(currentUser: RoverUser, postData: SearchCompanyRequest): Promise<SearchCompanyResponse> {
    const { pageSize, pageIndex, sortField, isSortAsc } = postData;
    const resp: SearchCompanyResponse = {
      pageSize,
      pageIndex,
      data: [],
      total: 0,
    };
    const needSkipSize = pageSize * (pageIndex - 1);
    const qb = await this.getBaseQueryBuilder(currentUser, postData);
    if (sortField) {
      if (sortField == 'createDate') {
        qb.orderBy('company.createDate', isSortAsc ? 'ASC' : 'DESC');
      } else if (sortField == 'result') {
        qb.orderBy('company.result', isSortAsc ? 'ASC' : 'DESC');
      } else if (sortField == 'registcapi') {
        qb.orderBy(`companyInfo.registcapiAmount`, isSortAsc ? 'ASC' : 'DESC');
      } else {
        qb.orderBy(`companyInfo.${sortField}`, isSortAsc ? 'ASC' : 'DESC');
      }
    } else {
      qb.orderBy('company.id', 'DESC');
    }
    //qb.skip(needSkipSize).take(pageSize);
    const [data, count] = await qb.getManyAndCount();
    if (data.length == 0) {
      return resp;
    }
    // target ids
    const companyId2Ids = new Map<string, number>();
    // related company id to target id
    const relatedMap = new Map<string, number>();
    const dataMap: Map<string, MonitorGroupCompanyEntity> = new Map();
    data.forEach((d) => {
      if (!d.relatedCompanyId) {
        companyId2Ids.set(d.companyId, d.id);
      }
    });
    data.forEach((d) => {
      if (d.relatedCompanyId) {
        const id = companyId2Ids.get(d.relatedCompanyId);
        if (id) {
          relatedMap.set(d.companyId, id);
        }
      }
      dataMap.set(d.companyId, d);
    });
    let responseData: MonitorGroupCompany[] = data.map((d) => {
      return Object.assign(
        d,
        pick(d.companyInfo, [
          'companyId',
          'econkind',
          'econkindDesc',
          'econType',
          'treasuryType',
          'enterpriseType',
          'province',
          'city',
          'district',
          'industry1',
          'industry2',
          'industry3',
          'industry4',
          'registcapi',
          'registcapiAmount',
          'startDateCode',
          'statusCode',
          'creditRate',
          'creditcode',
        ]),
        { companyInfo: undefined },
      );
    });
    // 二次排序
    const relatedResultMap = new Map<number, object[]>();
    const masterList = [];
    responseData.forEach((d) => {
      const targetId = relatedMap.get(d.companyId);
      if (targetId) {
        if (relatedResultMap.has(targetId)) {
          relatedResultMap.get(targetId).push(d);
        } else {
          relatedResultMap.set(targetId, [d]);
        }
      } else {
        masterList.push(d);
      }
    });
    const resultList = [];
    for (const masterListElement of masterList) {
      resultList.push(masterListElement);
      if (relatedResultMap.has(masterListElement.id)) {
        resultList.push(...relatedResultMap.get(masterListElement.id));
      }
    }
    responseData = resultList;
    resp.data = responseData.slice(needSkipSize, needSkipSize + pageSize);
    resp.total = count;
    return resp;
  }

  private async getBaseQueryBuilder(currentUser: RoverUser, postData: SearchCompanyRequest) {
    const { currentOrg: orgId } = currentUser;
    const { searchKey, groupIds, depIds, operators, companyIds, result, statusCode, startDateCode, registcapiAmount } = postData;
    const { by, userIds } = this.securityService.checkScope(currentUser, 2101);
    const qb = this.monitorGroupCompanyRepo
      .createQueryBuilder('company')
      .leftJoinAndSelect('company.creator', 'creator')
      .leftJoinAndSelect('company.companyInfo', 'companyInfo')
      .select(['company', 'creator.name', 'creator.userId', 'companyInfo'])
      .where('company.orgId = :orgId', { orgId });

    if (postData.creatorIds?.length) {
      qb.andWhere('company.createBy in (:...userIds)', { userIds: postData.creatorIds });
    }

    // 分组
    if (Array.isArray(groupIds) && groupIds.length > 0) {
      qb.andWhere('company.groupId in (:...groupIds)', { groupIds });
    }
    if (by == PermissionByEnum.USER) {
      qb.andWhere('company.createBy in (:...userIds)', { userIds });
    }

    if (searchKey) {
      qb.andWhere(
        new Brackets((qb1) => {
          qb1.orWhere('company.companyName like :name', { name: `%${searchKey}%` });
          qb1.orWhere('companyInfo.creditcode = :creditcode', { creditcode: searchKey });
        }),
      );
    }
    if (postData?.ids?.length) {
      qb.andWhere('company.id in (:...ids)', { ids: postData.ids });
    }

    if (depIds?.length) {
      qb.andWhere('company.depId in (:...depIds)', { depIds });
    }
    // 创建时间
    QueryBuilderHelper.applyDateRangeQuery(qb, postData?.createDate, 'createDate');
    if (operators?.length) {
      qb.andWhere('company.createBy in (:...operators)', { operators });
    }
    if (companyIds?.length) {
      qb.andWhere('company.companyId in (:...companyIds)', { companyIds });
    }
    if (result && result.length > 0) {
      qb.andWhere('company.result in (:...result)', { result });
    }

    // 企业类型
    if (postData?.econKindCode?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          const econKindMap = {};
          postData.econKindCode.forEach((econKind) => {
            econKindMap[econKind] = econKind;
          });
          postData.econKindCode.forEach((key) => {
            if (key) {
              qb1.orWhere(`find_in_set(:${key}, companyInfo.econkind)`, { [key]: econKindMap[key] });
            } else {
              qb1.orWhere('companyInfo.econkind is null');
            }
          });
        }),
      );
    }

    // 企业性质
    if (postData?.econType?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          postData.econType.forEach((key) => {
            qb1.orWhere(`find_in_set(:${key}, companyInfo.econType)`, { [key]: key });
          });
        }),
      );
    }

    // 司库类型
    if (postData?.treasuryType?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          postData.treasuryType.forEach((key) => {
            qb1.orWhere(`find_in_set(:${key}, companyInfo.treasuryType)`, { [key]: key });
          });
        }),
      );
    }

    // 机构类型
    if (postData?.enterpriseType?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          postData.enterpriseType.forEach((key) => {
            qb1.orWhere(`find_in_set(:${key}, companyInfo.enterpriseType)`, { [key]: key });
          });
        }),
      );
    }

    // 国民行业
    if (postData?.industry?.length) {
      const params = {};
      const industrySql = [];
      for (let i = 0; i < postData.industry?.length; i++) {
        const industry = postData.industry[i];
        if (industry.i4) {
          const key = 'i4' + i;
          params[key] = industry.i4;
          industrySql.push(`companyInfo.industry4 = :${key}`);
        } else if (industry.i3) {
          const key = 'i3' + i;
          params[key] = industry.i3;
          industrySql.push(`companyInfo.industry3 = :${key}`);
        } else if (industry.i2) {
          const key = 'i2' + i;
          params[key] = industry.i2;
          industrySql.push(`companyInfo.industry2 = :${key}`);
        } else if (industry.i1) {
          const key = 'i1' + i;
          params[key] = industry.i1;
          industrySql.push(`companyInfo.industry1 = :${key}`);
        }
      }
      qb.andWhere(`(${industrySql.join(' OR ')})`, params);
    }

    // 行政地区
    if (postData?.region?.length) {
      const params = {};
      const regionSql = [];
      for (let i = 0; i < postData.region?.length; i++) {
        const region = postData.region[i];
        if (region.dt) {
          const key = 'dt' + i;
          params[key] = region.dt;
          regionSql.push(`companyInfo.district = :${key}`);
        } else if (region.ct) {
          const key = 'ct' + i;
          params[key] = region.ct;
          regionSql.push(`companyInfo.city = :${key}`);
        } else if (region.pr) {
          const key = 'pr' + i;
          params[key] = region.pr;
          regionSql.push(`companyInfo.province = :${key}`);
        }
      }
      qb.andWhere(`(${regionSql.join(' OR ')})`, params);
    }

    //成立日期
    QueryBuilderHelper.applyDateRangeQuery(qb, startDateCode, 'companyInfo.startDateCode');

    //注册状态
    if (statusCode?.length) {
      qb.andWhere('companyInfo.statusCode in (:...statusCode)', { statusCode });
    }

    //注册资本
    if (registcapiAmount?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          registcapiAmount.forEach((r, index) => {
            const hasMin = r.min !== undefined && r.min !== null;
            const hasMax = r.max !== undefined && r.max !== null;
            if (hasMin && hasMax) {
              qb1.orWhere(
                new Brackets((qb2) => {
                  qb2
                    .where('companyInfo.registcapiAmount >= :min' + index, { ['min' + index]: r.min })
                    .andWhere('companyInfo.registcapiAmount < :max' + index, { ['max' + index]: r.max });
                }),
              );
            } else if (hasMin) {
              qb1.orWhere('companyInfo.registcapiAmount >= :min' + index, { ['min' + index]: r.min });
            } else if (hasMax) {
              qb1.orWhere('companyInfo.registcapiAmount < :max' + index, { ['max' + index]: r.max });
            }
          });
        }),
      );
    }
    return qb;
  }

  private buildMonitorGroupCompany(company: MonitorGroupCompanyEntity, esDetail?: KysCompanyResponseDetails, level?: number): MonitorGroupCompany {
    if (esDetail) {
      return Object.assign(
        new MonitorGroupCompany(),
        company,
        {
          econkind: esDetail.econkindcode?.join(','),
          province: esDetail.province,
          city: esDetail.areacode?.[0],
          district: esDetail.areacode?.[1],
          industry1: esDetail.industry,
          industry2: esDetail.subind?.[0],
          industry3: esDetail.subind?.[1],
          industry4: esDetail.subind?.[2],
          registcapi: esDetail.registcapi,
          registcapiAmount: esDetail.registcapiamount,
          startDateCode: this.parseEsDate(esDetail.startdatecode),
          statusCode: esDetail.statuscode,
        },
        { level },
      );
    }
    return Object.assign(new MonitorGroupCompany(), company);
  }

  private parseEsDate(esDate?: number): Date {
    if (esDate && String(esDate).length == 8) {
      const strEsDate = String(esDate);
      const strDate = `${strEsDate.substring(0, 4)}-${strEsDate.substring(4, 6)}-${strEsDate.substring(6, 8)}`;
      return new Date(strDate);
    }
    return null;
  }

  async getGroupCompany(currentUser: RoverUser, companyId: string) {
    const { currentOrg: orgId } = currentUser;
    const res = await this.monitorGroupCompanyRepo.findOne({ orgId, companyId });
    if (res) {
      return res;
    }
    return {};
  }

  private async getCertificationDetail(certificationId: string) {
    const result = await this.companyDetailService.getCompanyCertificationDetail(certificationId);
    if (result.Status == 200) {
      return result.Result;
    } else {
      return null;
    }
  }

  /**
   * 获取最新证书数量信息
   * @param companyId
   * @param certificateCode
   * @private
   */
  private async getInvalidCertList(companyId: string, certificateCode: string) {
    const result = await this.companyDetailService.getCompanyCertificationList({
      keyNo: companyId,
      certificateCode,
      pageIndex: 1,
      pageSize: 10,
    });
    const resultItems: CompanyCertificationInfo[] = result?.Result || [];
    return resultItems;
  }

  /**
   * 用户添加合作监控证书企业时调用
   * @param companyId
   */
  async setupMonitorCertificationStep(companyId: string) {
    if (await this.certificationRepository.count({ where: { companyId } })) return;
    let certification: MonitorCertificationEntity = {
      id: undefined,
      companyId,
      content: {
        result: [],
      },
      updateTime: new Date(),
    };
    certification = await this.certificationRepository.save(certification);
    setTimeout(
      async function () {
        try {
          const result = await this.getCertSummary(companyId);
          await this.certificationRepository.update(certification.id, {
            content: {
              result,
            },
            updateTime: new Date(),
          });
        } catch (e) {
          this.logger.error('setupMonitorCertificationStep', e);
        }
      }.bind(this),
      0,
    );
  }

  private getExpirationDate(key = 2) {
    switch (key) {
      case 1:
        // 近 7 天
        return moment().add('7', 'days').startOf('day').unix();
      case 3:
        // 近 3 个月
        return moment().add('3', 'months').startOf('day').unix();
      default:
        // 近 1 个月
        return moment().add('1', 'months').startOf('day').unix();
    }
  }

  /**
   * 为监控同一个公司的所有组织检查证书风险
   * @param monitorCert 上个版本的证书数据
   */
  async _scanCertRiskV2(monitorCert: MonitorCertificationEntity) {
    if (!monitorCert?.companyId) {
      return;
    }
    const companyId = monitorCert.companyId;
    const monitorGroupSettings = await this.monitorCommonService.matchMonitorOrgSetting(companyId);
    if (!monitorGroupSettings?.length) {
      this.logger.info(`scanCertRisk: no monitor group setting for company: ${companyId}`);
      await this.certificationRepository.delete(monitorCert.id);
      return;
    }
    const nowDate = moment().unix();
    const companyDetailsQcc = await this.companySearchService.companyDetailsQcc(companyId);
    // 营业执照处理
    if (companyDetailsQcc && companyDetailsQcc?.TeamEnd > 0) {
      await this.processBusinessLicenseRiskDynamic(companyDetailsQcc, monitorGroupSettings, nowDate, companyId);
    }
    // 资质证书处理
    const latestResult = await this.companyDetailService.getCertSummary(companyId);
    if (latestResult && latestResult.length > 0) {
      for (const certificate of latestResult) {
        // 获取各 org 下设置
        await Bluebird.map(monitorGroupSettings, async (monitorGroupSettingItem) => {
          const { checkFlag, group, dimension } = this.checkDimension(monitorGroupSettingItem);
          if (!checkFlag) {
            // 维度未开启
            return;
          }
          const codeArr = certificate.LatestDoc?.CommonInfo?.filter((x) => x.Key == '002')?.[0].Value?.split(',') || [];
          const certificateSetting = dimension.params
            .find((d) => d.field == QueryParamsEnums.certification)
            ?.fieldVal?.find((d) => d.status == 1 && (d.key == certificate.CertificateCode || codeArr.includes(d.key)));
          if (certificateSetting) {
            const certificateLevelCode = certificateSetting.key;
            await this.processCertificationRiskDynamic(monitorGroupSettingItem, group, certificate, dimension, companyId, nowDate, certificateLevelCode);
          }
        });
      }
    }
    // 将最新结果更新到数据库中
    monitorCert.content.result = latestResult;
    monitorCert.updateTime = new Date();
    await this.certificationRepository.save(monitorCert);
  }

  /**
   * 定期检查证书风险
   */
  async periodicalCheckCertRisks() {
    this.logger.info('periodicalCheckCertRisks begin');
    let lastId = 0;
    while (true) {
      const items = await this.certificationRepository.find({
        where: { id: MoreThan(lastId) },
        order: { id: 'ASC' },
        take: 100,
      });
      if (items.length === 0) {
        this.logger.info('periodicalCheckCertRisks: no more items');
        break;
      }
      this.logger.info(`periodicalCheckCertRisks: processing ${items.length} items`);

      await Bluebird.map(
        items,
        async (item: MonitorCertificationEntity) => {
          try {
            await this._scanCertRiskV2(item);
          } catch (error) {
            this.logger.error('periodicalCheckCertRisks', error);
          }
        },
        { concurrency: 10 },
      );
      lastId = items[items.length - 1].id;
    }
    this.logger.info('periodicalCheckCertRisks end');
  }

  async onOrganizationSuspended(event: OrganizationSuspendedEvent) {
    this.logger.info(`onOrganizationSuspended: orgId: ${event.orgId}`);
    const companies = await this.monitorGroupCompanyRepo.find({
      where: {
        orgId: event.orgId,
        status: BatchStatusEnums.Done,
      },
    });
    const companyIds = companies.map((it) => it.companyId);
    await this.afterRemove(companyIds);
    await this.monitorGroupCompanyRepo.update(
      {
        id: In(companies.map((it) => it.id)),
      },
      { status: BatchStatusEnums.Suspended },
    );
  }

  async onOrganizationActivated(event: OrganizationActivatedEvent) {
    this.logger.info(`onOrganizationActivated: orgId: ${event.orgId}`);
    const companies = await this.monitorGroupCompanyRepo.find({
      where: {
        orgId: event.orgId,
        status: BatchStatusEnums.Suspended,
      },
    });
    const companyIds = companies.map((it) => it.companyId);

    try {
      // 连接C端风险推送数据库，添加监控企业
      await this.sendToRiskDb(companyIds);
    } catch (error) {
      throw error;
    }

    await this.monitorGroupCompanyRepo.update(
      {
        id: In(companies.map((it) => it.id)),
      },
      { status: BatchStatusEnums.Done },
    );
  }

  /**
   * 同步监控公司中的数据到monitor_certification表
   */
  async syncCompanyToMonitorCertification() {
    const companyIds: string[] = (
      await this.monitorGroupCompanyRepo.createQueryBuilder('c').select('c.companyId', 'companyId').groupBy('c.companyId').getRawMany()
    ).map((it) => it.companyId);
    setTimeout(
      async function () {
        this.logger.info('syncCompanyToMonitorCertification begin');
        try {
          await Bluebird.map(companyIds, (companyId: string) => this.setupMonitorCertificationStep(companyId), { concurrency: 10 });
        } catch (e) {
          this.logger.error('syncCompanyToMonitorCertification', e);
        }
        this.logger.info('syncCompanyToMonitorCertification end');
      }.bind(this),
      0,
    );
  }

  async resumeFromSuspended(id: number, currentUser: RoverUser) {
    const company = await this.monitorGroupCompanyRepo.findOne(id);
    if (!company || company.orgId !== currentUser.currentOrg) {
      throw new BadRequestException(RoverExceptions.Monitor.Company.NotFound);
    }
    if (company.status !== 5) return;

    const companyIds = [company.companyId];
    await this.sendToRiskDb(companyIds);
    await this.monitorGroupCompanyRepo.update(id, {
      status: 2,
    });
  }

  /**
   * 更新客户的调查信息
   *
   * @param orgId
   * @param companyIds
   */
  async updateMonitorCompanyDiligenceInfo(orgId: number, companyIds?: string[]) {
    this.logger.log(`开始更新监控列表的调查信息，orgId=${orgId}, companyIds=${companyIds}`);
    let count = 0;
    let potentialLoopTimes = 0;
    const pageSize = 300;
    do {
      const qb = this.monitorGroupCompanyRepo
        .createQueryBuilder('company')
        .where('company.orgId = :orgId', { orgId })
        .andWhere('company.status = :status', { status: BatchStatusEnums.Done })
        .andWhere('( company.result is null or company.result = -1) ');
      if (companyIds?.length) {
        qb.andWhere('company.companyId in (:...companyIds)', { companyIds });
      }
      const items = await qb.take(pageSize).getMany();
      count += items.length;
      if (items.length) {
        const sql = `SELECT * FROM (SELECT company_id, org_id, id as diligenceId, result, create_date, update_date, ROW_NUMBER() OVER (PARTITION BY company_id ORDER BY id DESC) AS row_num FROM due_diligence WHERE org_id = ${orgId} and company_id IN (${items
          .map((i) => `'${i.companyId}'`)
          .join(',')}) ORDER BY id DESC) a WHERE a.row_num = 1;`;
        const rows = await this.monitorGroupCompanyRepo.query(sql);
        if (!rows.length) {
          potentialLoopTimes++;
        } else {
          await Bluebird.map(
            rows,
            async (row) => {
              const item = items.find((i) => i.companyId === row.company_id);
              if (item) {
                return this.monitorGroupCompanyRepo.update(item.id, {
                  result: row.result,
                  diligenceId: row.diligenceId,
                  diligenceDate: row.update_date,
                });
              }
            },
            { concurrency: 10 },
          );
        }
      }
      // 因为 latest_diligence_result 可能会被查询过程中修改，所以需要防止死循环
      // 如果连续4次的客户列表都没有找到任何尽调记录
      if (!items.length || items.length < pageSize || potentialLoopTimes > 3) {
        break;
      }
    } while (true);

    return count;
  }

  public async aggsForSearch(currentUser: RoverUser, postData: MonitorAgssRequest): Promise<AggsResponse[]> {
    const qb: SelectQueryBuilder<MonitorGroupCompanyEntity> = await this.getBaseQueryBuilder(currentUser, postData.query);
    if (!qb) {
      return [];
    }
    const aggsField = postData.aggsField;
    if (!aggsField) {
      return [];
    }
    switch (aggsField) {
      case 'operator':
        qb.select('creator.userId', 'fieldValue').addSelect('COUNT(distinct(company.id))', 'count').groupBy('creator.userId');
        break;
      case 'econType': {
        // @ts-ignore
        qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`companyInfo`.`econ_type`, ',', numbers.n), ',', -1)", 'fieldValue')
          .addSelect('COUNT(distinct(company.id))', 'count')
          .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
          .groupBy('fieldValue');
        break;
      }
      case 'treasuryType': {
        // @ts-ignore
        qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`companyInfo`.`treasury_type`, ',', numbers.n), ',', -1)", 'fieldValue')
          .addSelect('COUNT(distinct(company.id))', 'count')
          .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
          .groupBy('fieldValue');
        break;
      }
      case 'enterpriseType': {
        // @ts-ignore
        qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`companyInfo`.`enterprise_type`, ',', numbers.n), ',', -1)", 'fieldValue')
          .addSelect('COUNT(distinct(company.id))', 'count')
          .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
          .groupBy('fieldValue');
        break;
      }
      case 'statusCode': {
        // @ts-ignore
        qb.select('companyInfo.statusCode', 'fieldValue').addSelect('COUNT(distinct(company.id))', 'count').groupBy('companyInfo.statusCode');
        break;
      }
      case 'result': {
        qb.select('company.result', 'fieldValue').addSelect('COUNT(distinct(company.id))', 'count').groupBy('company.result');
        break;
      }
      case 'group': {
        qb.select('company.groupId', 'fieldValue').addSelect('COUNT(distinct(company.id))', 'count').groupBy('company.groupId');
        break;
      }
    }
    const rows = await qb.getRawMany();
    switch (aggsField) {
      case 'operator': {
        const operators = await this.userRepository.findByIds(rows.map((r) => r.fieldValue));
        return rows.map((r) => {
          if (!r.fieldValue) return;
          const operator = operators.find((d) => d.userId == r.fieldValue);
          r.fieldLabel = operator?.name || '';
          r.count = Number(r.count);
          return r;
        });
      }

      case 'group': {
        const groups = (
          await this.monitorGroupRepo.find({
            where: {
              orgId: currentUser.currentOrg,
            },
            order: { order: 'DESC' },
          })
        ).sort((r1, r2) => {
          // 按order从大到小排序，默认分组的order=-1，默认分组排到最前面
          if (r1.order >= 0 && r2.order >= 0) {
            return r2.order - r1.order;
          }
          return r1.order - r2.order;
        });
        const result = [];
        groups.forEach((g) => {
          const row = rows.find((r) => g.id == r.fieldValue);
          result.push({
            fieldLabel: g.name,
            fieldValue: g.id + '',
            count: Number(row?.count) || 0,
          });
        });
        return result;
      }
      default:
        return rows.map((r) => {
          r.count = Number(r.count);
          return r;
        });
    }
  }

  /**
   * 连接C端风险推送数据库，添加监控企业
   * @param companyIds
   */
  public async sendToRiskDb(companyIds: string[]) {
    if (companyIds?.length > 0) {
      const conn = await this.getRiskDbConn();
      const env = this.configService.stage;
      const existMonitorCompany = await conn.createEntityManager().find(KysMoniterCompanyListEntity, {
        keyno: In(companyIds),
        env,
      });
      const saveEntities = difference(
        companyIds,
        existMonitorCompany.map((e) => e.keyno),
      ).map((keyno) => Object.assign(new KysMoniterCompanyListEntity(), { keyno, env, type: 0 }));
      if (saveEntities.length) {
        // 添加监控的企业到C端风险推送数据库
        await conn.createEntityManager().insert(KysMoniterCompanyListEntity, saveEntities);
      }
      return saveEntities;
    }
    return [];
  }

  /**
   * 营业执照风险预警处理
   * @param companyDetailsQcc
   * @param monitorGroupSettings
   * @param nowDate
   * @param companyId
   * @private
   */
  private async processBusinessLicenseRiskDynamic(
    companyDetailsQcc: CompanyDetails,
    monitorGroupSettings: CompanyMonitorModelPO[],
    nowDate: number,
    companyId: string,
  ) {
    const objectId = QueryParamsEnums.businessLicense;
    const endDate = companyDetailsQcc.TeamEnd;
    const startDate = companyDetailsQcc.TermStart;
    await Bluebird.map(monitorGroupSettings, async (monitorGroupSettingItem) => {
      const { checkFlag, group, dimension } = this.checkDimension(monitorGroupSettingItem);
      if (!checkFlag) {
        // 维度未开启
        return;
      }
      const businessLicense = dimension.params.find((d) => d.field == QueryParamsEnums.businessLicense && d.status == 1);
      if (businessLicense) {
        const processMonitorRiskModel = Object.assign(new MonitorRiskProcessModel(), {
          monitorGroupSettingItem,
          dimension,
          startDate,
          endDate,
          companyId,
          objectIdPre: objectId,
          nowDate,
          group,
          name: '营业执照',
        });
        const monitorRiskDynamicsV2Entity = await this.processRiskDynamic(processMonitorRiskModel);
        if (monitorRiskDynamicsV2Entity) {
          await this.riskDynamicsRepo.insert(monitorRiskDynamicsV2Entity);
        }
      }
    });
  }

  /**
   * 风险动态
   * @private
   * @param monitorRiskProcessModel
   */
  private async processRiskDynamic(monitorRiskProcessModel: MonitorRiskProcessModel): Promise<MonitorRiskDynamicsV2Entity> {
    const { monitorGroupSettingItem, dimension, startDate, endDate, companyId, objectIdPre, nowDate, group, name, certificateStatus, certificateStatusDesc } =
      monitorRiskProcessModel;
    if (!endDate || endDate === 0) {
      //长期有效，不会过期
      return null;
    }
    const yesterday = moment().add(-1, 'days').startOf('day').unix();
    const nearExpirationType = dimension.params.find((d) => d.field == 'nearExpirationType')?.fieldVal || 2;
    const expirationDate = this.getExpirationDate(nearExpirationType);
    //  到期时间
    if (endDate > expirationDate || yesterday > endDate) {
      // 正常 或者 历史过期数据
      return null;
    }
    let insertFlag = false;
    let objectId = '';
    let tips = '';
    const orgId = monitorGroupSettingItem.setting.orgId;
    const qb = this.riskDynamicsRepo
      .createQueryBuilder('dynamic')
      .where('dynamic.orgId = :orgId', { orgId })
      .andWhere('dynamic.companyId = :companyId', { companyId });
    if (endDate <= nowDate || certificateStatus !== '1') {
      // 已过期或状态非有效
      objectId = objectIdPre;
      qb.andWhere('dynamic.objectId = :objectId', { objectId });
      const count = await qb.getCount();
      if (count > 0) {
        //已有动态
        return null;
      }
      insertFlag = true;
      // 优先使用证书状态描述,如果证书状态为有效则使用过期提示
      tips = certificateStatus !== '1' ? certificateStatusDesc : '已过期';
    } else {
      // 触发预警
      objectId = objectIdPre + '_early';
      qb.andWhere('dynamic.objectId = :objectId', { objectId });
      const count = await qb.getCount();
      if (count > 0) {
        //已有动态
        return null;
      }
      insertFlag = true;
      switch (nearExpirationType) {
        case 1:
          tips = '将于7日内到期';
          break;
        case 3:
          tips = '将于3个月内到期';
          break;
        case 2:
        default:
          tips = '将于1个月内到期';
      }
    }
    if (insertFlag) {
      const startDateStr = moment.unix(startDate).format('YYYY-MM-DD');
      const endDateStr = moment.unix(endDate).format('YYYY-MM-DD');
      //质量证书将于1个月后到期
      // 有效期：2023-03-03至2034-03-03
      const detail = {
        // ${certificate.CertificateCodeDesc}
        Content: `${name}${tips}\n有效期：${startDateStr}至${endDateStr}`,
      };
      const dynamicId = '_' + CommonHelper.replaceAll(uuid(), '-', '');
      return Object.assign(new MonitorRiskDynamicsV2Entity(), {
        orgId,
        companyId,
        companyName: monitorGroupSettingItem.companyName,
        dynamicId,
        objectId,
        riskCreateDate: new Date(),
        groupKey: group.key,
        dimensionKey: dimension.key,
        dimensionLevel: dimension.level,
        monitorSettingId: monitorGroupSettingItem.setting.id,
        detail,
      });
    }
  }

  /**
   * 资质证书风险预警处理
   * @param monitorGroupSettingItem
   * @param group
   * @param certificate
   * @param dimension
   * @param companyId
   * @param nowDate
   * @private
   */
  private async processCertificationRiskDynamic(
    monitorGroupSettingItem,
    group: MonitorDimensionType,
    certificate: MonitorCertificationItem,
    dimension: MonitorDimensionType,
    companyId: string,
    nowDate: number,
    certificateLevelCode: string,
  ) {
    let name;
    if (certificateLevelCode == certificate.CertificateCode) {
      // 二级 Code
      name = certificate.CertificateCodeDesc;
    } else {
      // 三级 Code
      name = certificate.LatestDoc?.CommonInfo?.find((x) => x.Key == '003')?.Value;
    }
    const objectId = certificate.CertificateCode;
    const startDate = certificate.LatestDoc?.StartDate;
    const endDate = certificate.LatestDoc?.EndDate;
    const certificateStatus = certificate?.CertificateStatus;
    const certificateStatusDesc = certificate?.CertificateStatusDesc;

    const processMonitorRiskModel = Object.assign(new MonitorRiskProcessModel(), {
      monitorGroupSettingItem,
      dimension,
      startDate,
      endDate,
      companyId,
      objectIdPre: objectId,
      nowDate,
      group,
      name,
      certificateStatus,
      certificateStatusDesc,
    });
    const monitorRiskDynamicsV2Entity = await this.processRiskDynamic(processMonitorRiskModel);
    if (monitorRiskDynamicsV2Entity) {
      const certificateId = certificate.LatestDoc.Id;
      const result = await this.getCertificationDetail(certificateId);
      if (result) {
        const certificateNo = result.CertificateNo;
        const split = monitorRiskDynamicsV2Entity.detail.Content.split('\n');
        monitorRiskDynamicsV2Entity.detail.Content = `${split[0]}\n证书编号：${certificateNo}\n${split[1]}`;
        monitorRiskDynamicsV2Entity.detail.CertificateId = certificateId;
        monitorRiskDynamicsV2Entity.detail.CertificateCodeDesc = result.CertificateCodeDesc;
      }
      await this.riskDynamicsRepo.insert(monitorRiskDynamicsV2Entity);
    }
  }

  /**
   * 维度状态校验
   * @param monitorGroupSettingItem
   * @private
   */
  private checkDimension(monitorGroupSettingItem) {
    const content = monitorGroupSettingItem.setting.content as MonitorDimensionType[];
    let checkFlag = true;
    const group = content.find((it) => it.key === MonitorRiskEnums.MonitorOperatingRisk && it.status == 1);
    if (!group) {
      checkFlag = false;
      return { checkFlag, group };
    }
    const dimension = group.items.find((it) => it.key === MonitorRiskEnums.MonitorCertificationExpired && it.status == 1);
    if (!dimension) {
      checkFlag = false;
    }
    return { checkFlag, group, dimension };
  }

  /**
   * 短期多起开庭公告处理
   */
  public async monitorRiskMessageSchedule(validOrgIds?: number[]) {
    const yesterday = moment().add(-1, 'days').startOf('day').unix();
    // 分页拉取数据
    const pageSize = 100;
    let pageIndex = 1;
    let finished = false;
    do {
      const qb = this.riskMessageRepo
        .createQueryBuilder('message')
        .where('message.status = :status', { status: 0 })
        .andWhere('message.createDate >= :createDate', { createDate: yesterday })
        .orderBy('message.id', 'ASC')
        .take(pageSize)
        .skip(pageSize * (pageIndex - 1));
      const [items, count] = await qb.getManyAndCount();
      if (items.length < pageSize || items.length == count) {
        finished = true;
      }
      // 单条处理
      await Bluebird.map(
        items,
        async (item) => {
          await this.monitorRiskDynamicService.processRiskMessage(item, validOrgIds);
        },
        { concurrency: 10 },
      );
      // 下一页
      pageIndex++;
    } while (!finished);
  }

  async createV2(currentUser: RoverUser, requests: AddCompanyRequest[]): Promise<CreateCompanyResponse> {
    const response = new CreateCompanyResponse();
    if (!requests || requests.length == 0) return response;
    const { userId, currentOrg: orgId } = currentUser;
    // 校验公司有无重复
    const companyIds: string[] = requests.map((x) => x.companyId);
    let newCompanyIds = companyIds;
    //校验公司是否为支持的类型
    await this.checkCompanyIsSupport(companyIds);
    const existCompanyList = await this.monitorGroupCompanyRepo.find({ where: { orgId, companyId: In(companyIds) } });
    response.failList = existCompanyList;
    if (existCompanyList.length > 0) {
      const existCompanyIds = existCompanyList.map((c) => c.companyId);
      newCompanyIds = companyIds.filter((x) => !existCompanyIds.includes(x));
      if (newCompanyIds.length == 0) {
        return response;
      }
    }
    const companyResultsMap = await this.getCompanyResultMap(orgId, newCompanyIds);
    const companyMap: Map<string, string> = new Map<string, string>();
    requests.forEach((req) => {
      if (newCompanyIds.includes(req.companyId)) {
        companyMap.set(req.companyId, req.companyName);
      }
    });
    await this.checkMonitorGroup(requests, orgId, currentUser);

    // 校验总的公司数量是否超限
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.MonitorCompanyQuantity);
    await bundleCounter.increase(newCompanyIds.length);

    // 设置关联方为空
    requests.forEach((r) => {
      r.relatedCompanyId = undefined;
    });

    const groupCompany: MonitorGroupCompanyEntity[] = [];
    requests.forEach((r) => {
      if (newCompanyIds.includes(r.companyId)) {
        const groupCompanyItem = Object.assign(new MonitorGroupCompanyEntity(), r, {
          createBy: userId,
          orgId: orgId,
          depId: currentUser.departments?.[0],
          diligenceId: companyResultsMap.get(r.companyId)?.diligenceId,
          diligenceDate: companyResultsMap.get(r.companyId)?.diligenceDate,
          result: companyResultsMap.get(r.companyId)?.result,
        });
        groupCompany.push(groupCompanyItem);
      }
    });
    //创建company详情数据
    await this.companySearchService.createCompanyInfoBatch(companyMap);
    try {
      // 连接C端风险推送数据库，添加监控企业
      await this.sendToRiskDb(newCompanyIds);
      // 添加到证书监控列表
      for (const companyId of newCompanyIds) {
        await this.setupMonitorCertificationStep(companyId);
      }
      // 添加到企业监控列表
      const res = await this.monitorGroupCompanyRepo.save(groupCompany);
      // 添加操作日志
      await this.oplogService.add(
        OperatorTypeEnums.Create,
        MonitorGroupCompanyEntity,
        currentUser,
        res?.map((r) => r.id),
        res,
      );
      response.successList = res;
    } catch (error) {
      await bundleCounter.decrease(newCompanyIds.length);
      throw error;
    }
    return response;
  }

  async getCompanies(companyIds: string[]) {
    const { clientRequest } = this.createClientRequest({ companyIds }, ['id', 'standard_code']);
    const esResp = await this.companySearchService.companySearchForKys(clientRequest);
    if (esResp?.Result?.length > 0) {
      return esResp.Result;
    }
    return [];
  }

  async checkCompanyIsSupport(companyIds: string[]) {
    const companyList = await this.getCompanies(companyIds);
    if (companyList.length > 0) {
      companyList.forEach((companyInfo) => {
        const companyStandardCode = companyInfo?.['standard_code'];
        if ((companyStandardCode?.length && intersection(companyStandardCode, ForbiddenStandardCode).length) || !companyStandardCode?.length) {
          throw new BadParamsException(RoverExceptions.Diligence.Common.Nonsupport);
        }
      });
    }
  }

  private async checkMonitorGroup(requests: AddCompanyRequest[], orgId: number, currentUser: RoverUser) {
    const groupMap: Map<number, AddCompanyRequest[]> = new Map();
    requests.forEach((r) => {
      if (groupMap.has(r.groupId)) {
        groupMap.get(r.groupId).push(r);
      } else {
        groupMap.set(r.groupId, [r]);
      }
    });
    // 校验分组是否存在
    const countByGroup = await this.monitorGroupRepo.count({ orgId, id: In([...groupMap.keys()]) });
    if (countByGroup != groupMap.size) {
      throw new BadRequestException(RoverExceptions.Monitor.Company.GroupNotExists);
    }
    // 取消监控分组下公司数量限制校验 RA-16508
    // 校验分组下的公司数量是否超限
    // const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    // const groupLimit = userBundle[RoverBundleLimitationType.MonitorGroupCompanyQuantity]?.value || 1000;
    // for (const [k, v] of groupMap) {
    //   const countByGroupCompany = await this.monitorGroupCompanyRepo.count({ orgId, groupId: k });
    //   if (countByGroupCompany + v.length > groupLimit) {
    //     throw new BadRequestException(RoverExceptions.Monitor.Company.GroupCompanyLimit);
    //   }
    // }
  }

  /**
   * 监控企业制裁动态
   */
  async monitorCompanySanctionsSchedule(validOrgIds?: number[]) {
    const yesterday = moment().add(-1, 'days').startOf('day').unix();
    // const yesterday = 0;
    // 分页拉取数据
    const pageSize = 100;
    let pageIndex = 1;
    let finished = false;
    do {
      const qb = this.certificationRepository
        .createQueryBuilder('certification')
        .orderBy('certification.id', 'ASC')
        .take(pageSize)
        .skip(pageSize * (pageIndex - 1));
      const [items, count] = await qb.getManyAndCount();
      if (items.length < pageSize || items.length == count) {
        finished = true;
      }
      const companyBusinessInfoMap: Map<string, CompanyBusinessInfo> = await this.companySearchService.getCompanyBusinessInfoMap(
        items.map((x) =>
          Object.assign(new CompanyBasicInfo(), {
            companyId: x.companyId,
          }),
        ),
      );
      // 单条处理
      await Bluebird.map(
        items,
        (item) => {
          const companyBusinessInfo = companyBusinessInfoMap.get(item.companyId);
          if (companyBusinessInfo && companyBusinessInfo.companyName) {
            item.name = companyBusinessInfo?.companyName;
            this.monitorRiskDynamicService.processCompanySanctionsByItem(item, yesterday, validOrgIds);
          }
        },
        { concurrency: 10 },
      );
      // 下一页
      pageIndex++;
    } while (!finished);
  }

  /**
   * 企查分变更处理
   * @param validOrgIds
   */
  async processQccCreditChange(validOrgIds?: number[]) {
    //获取监控设置，只有设置了企查分变更的监控设置，才进行处理
    const monitorGroupSettings: OrgSettingsLogEntity[] = await this.monitorCommonService.matchMonitorOrgSettingsByDimension(
      validOrgIds,
      MonitorRiskEnums.QccCreditChange,
    );
    if (!monitorGroupSettings?.length) {
      this.logger.info('No monitor settings found for QccCreditChange');
      return;
    }

    // 把 这部分监控设置中的 orgId 取出来，获取 这些 orgId 下的 企业
    const orgIds: number[] = [];
    const orgSettingMap = new Map<number, number>();

    // 处理二维数组，收集所有的 orgId 和对应的设置 ID
    monitorGroupSettings.forEach((setting) => {
      if (setting.orgId) {
        orgIds.push(setting.orgId);
        orgSettingMap.set(setting.orgId, setting.id);
      }
    });

    if (!orgIds.length) {
      return;
    }

    const companies = await this.monitorGroupCompanyRepo.find({
      where: {
        orgId: In(orgIds),
        status: BatchStatusEnums.Done,
      },
    });
    if (!companies?.length) {
      return;
    }

    await Bluebird.map(
      companies,
      async (company) => {
        // 获取企业的企查分变更记录, 日期参数为当前日期前一天
        const creditRateTrend: RateTrendInfo[] = await this.companySearchService.getCreditRateTrend(
          company.companyId,
          moment().subtract(1, 'days').format('YYYYMMDD'),
        );
        // 如果企查分变更记录中，有企查分变更，则进行处理
        if (creditRateTrend?.length) {
          //取第一条，第一条就是 trend before的企查分
          const beforeScore = creditRateTrend[0]?.Score;
          //传的是前一天的日期，返回会是两条结果，第一条是昨天的，第二条是今天的
          const currentScore = creditRateTrend?.[1]?.Score;
          // 如果 beforeScore 为空，不处理
          if (!beforeScore) {
            return;
          }
          // 如果beforeScore 不为空，但是 currentScore 为空，说明企查分没变化，则不处理
          if (beforeScore && !currentScore) {
            return;
          }
          // 如果企查分变更记录中，有企查分变更，则进行处理
          if (beforeScore !== currentScore) {
            // 进行处理
            const monitorRiskDynamicsV2Entity = Object.assign(new MonitorRiskDynamicsV2Entity(), {
              orgId: company.orgId,
              companyId: company.companyId,
              companyName: company.companyName,
              dynamicId: '_' + CommonHelper.replaceAll(uuid(), '-', ''),
              objectId: MonitorRiskEnums.QccCreditChange,
              riskCreateDate: new Date(),
              groupKey: MonitorRiskEnums.MonitorOperatingRisk,
              dimensionKey: MonitorRiskEnums.QccCreditChange,
              dimensionLevel: 1,
              monitorSettingId: orgSettingMap.get(company.orgId),
              detail: {
                Content: `变更前：${beforeScore}\n 变更后：${currentScore}`,
              },
            });
            this.logger.info(`企查分变更处理：${company.companyName} ${beforeScore} -> ${currentScore}`);
            await this.riskDynamicsRepo.insert(monitorRiskDynamicsV2Entity);
          }
        }
      },
      { concurrency: 10 },
    );
  }
}
