import { MailerService } from '@kezhaozhao/nest-mailer';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '../../../libs/config/config.service';
import { HttpUtilsService } from '../../../libs/config/httputils.service';
import { DiligenceHistoryEntity } from '../../../libs/entities/DiligenceHistoryEntity';
import { MonitorGroupCompanyEntity } from '../../../libs/entities/MonitorGroupCompanyEntity';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import { MonitorNotificationEntity } from '../../../libs/entities/MonitorPushUserLogEntity';
import { MonitorRiskDynamicsV2Entity } from '../../../libs/entities/MonitorRiskDynamicsV2Entity';
import { MonitorSentimentDynamicsV2Entity } from '../../../libs/entities/MonitorSentimentDynamicsV2Entity';
import { UserConfigurationEntity } from '../../../libs/entities/UserConfigurationEntity';
import { InterfacePushDataPO } from '../../../libs/model/monitor/InterfacePushDataPO';
import { InterfaceSetting } from '../../../libs/model/monitor/MonitorPushDimensionType';
import * as utils from '../../../libs/utils/utils';
import { CompanySearchService } from '../../company/company-search.service';
import { MessageService } from '../../message/message.service';
import { SettingsService } from '../../settings/settings.service';
import { RiskChangeListESService } from '../risk/risk-changelist.es.service';
import { ExportFileService } from './export.file.service';
import { MonitorNotificationService } from './monitor.notification.service';

jest.mock('../../../libs/utils/utils', () => ({
  getHash: jest.fn(),
  monitrPushEncrypt: jest.fn(),
}));

describe('MonitorNotificationService', () => {
  let service: MonitorNotificationService;
  let httpUtilsService: HttpUtilsService;

  const mockHttpUtilsService = {
    sendResquest: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MonitorNotificationService,
        {
          provide: HttpUtilsService,
          useValue: mockHttpUtilsService,
        },
        {
          provide: RiskChangeListESService,
          useValue: {},
        },
        {
          provide: CompanySearchService,
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {},
        },
        {
          provide: MessageService,
          useValue: {},
        },
        {
          provide: MailerService,
          useValue: {},
        },
        {
          provide: SettingsService,
          useValue: {},
        },
        {
          provide: ExportFileService,
          useValue: {},
        },
        {
          provide: getRepositoryToken(MonitorGroupEntity),
          useValue: {},
        },
        {
          provide: getRepositoryToken(MonitorGroupCompanyEntity),
          useValue: {},
        },
        {
          provide: getRepositoryToken(MonitorRiskDynamicsV2Entity),
          useValue: {},
        },
        {
          provide: getRepositoryToken(MonitorNotificationEntity),
          useValue: {},
        },
        {
          provide: getRepositoryToken(DiligenceHistoryEntity),
          useValue: {},
        },
        {
          provide: getRepositoryToken(MonitorSentimentDynamicsV2Entity),
          useValue: {},
        },
        {
          provide: getRepositoryToken(UserConfigurationEntity),
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<MonitorNotificationService>(MonitorNotificationService);
    httpUtilsService = module.get<HttpUtilsService>(HttpUtilsService);
  });

  describe('pushToUrl', () => {
    const mockInterfaceSetting: InterfaceSetting = {
      url: 'https://test.api.com',
      username: 'testuser',
      password: 'testpass',
      isEncrypted: false,
      privateKey: '',
    };

    const mockPushData: InterfacePushDataPO = {
      riskDynamic: [],
      sentimentDynamic: [],
    };

    it('应该成功发送非加密请求并返回成功状态', async () => {
      mockHttpUtilsService.sendResquest.mockResolvedValueOnce('success');

      const result = await service.pushToUrl(mockInterfaceSetting, mockPushData);

      //   expect(mockHttpUtilsService.sendResquest).toHaveBeenCalledWith({
      //     method: 'POST',
      //     url: mockInterfaceSetting.url,
      //     data: {
      //       username: mockInterfaceSetting.username,
      //       password: mockInterfaceSetting.password,
      //       data: mockPushData,
      //     },
      //     httpsAgent: expect.any(Object),
      //   });

      expect(result).toEqual({
        status: 'success',
        response: 'success',
      });
    });

    it('应该成功发送加密请求并返回成功状态', async () => {
      const mockEncryptedSetting: InterfaceSetting = {
        ...mockInterfaceSetting,
        isEncrypted: true,
        privateKey: 'test-private-key',
      };

      const mockHash = 'test-hash';
      const mockEncrypted = 'encrypted-data';

      (utils.getHash as jest.Mock).mockReturnValue(mockHash);
      (utils.monitrPushEncrypt as jest.Mock).mockReturnValue(mockEncrypted);
      mockHttpUtilsService.sendResquest.mockResolvedValueOnce('success');

      const result = await service.pushToUrl(mockEncryptedSetting, mockPushData);

      expect(utils.getHash).toHaveBeenCalledWith(JSON.stringify(mockPushData));
      expect(utils.monitrPushEncrypt).toHaveBeenCalledWith(
        JSON.stringify({
          username: mockEncryptedSetting.username,
          password: mockEncryptedSetting.password,
          hash: mockHash,
        }),
        mockEncryptedSetting.privateKey,
      );

      expect(mockHttpUtilsService.sendResquest).toHaveBeenCalledWith({
        method: 'POST',
        url: mockEncryptedSetting.url,
        data: {
          encrypted: mockEncrypted,
          data: mockPushData,
        },
        httpsAgent: expect.any(Object),
      });

      expect(result).toEqual({
        status: 'success',
        response: 'success',
      });
    });

    it('当请求返回非success时应该返回error状态', async () => {
      mockHttpUtilsService.sendResquest.mockResolvedValueOnce('failed');

      const result = await service.pushToUrl(mockInterfaceSetting, mockPushData);

      expect(result).toEqual({
        status: 'error',
        response: 'failed',
      });
    });

    it('当请求抛出异常时应该抛出错误', async () => {
      const mockError = new Error('Network error');
      mockHttpUtilsService.sendResquest.mockRejectedValueOnce(mockError);

      await expect(service.pushToUrl(mockInterfaceSetting, mockPushData)).rejects.toThrow(mockError);
    });
  });
});
