/* eslint-disable no-shadow */
// import _ from 'lodash';
import { chain, endsWith, find, forEach, head, includes, last, some, split, startsWith, uniq } from 'lodash';

import * as moment from 'moment';
// import callContext from '@qcc/call-context';
import riskHelper from './riskHelper';
import CommonHelper from './commonHelper';
import shared from './shared';
import { dynamicMap } from './dynamicAll';
import { ChangeInfoModel, ContentInfoModel, DescModel } from './risk.detail.help';

const titleMap = chain(dynamicMap)
  .map((item) => ({
    Desc: item.Desc,
    Category: head(item.Includes.filter((it) => startsWith(it.Desc, '全部')))?.Category,
  }))
  .map((it) => ({
    Desc: it.Desc,
    Category: split(it.Category, ',').map((c) => split(c, '(')[0]),
  }))
  .value();

// Category 98
const PRODUCT_TYPE_MAP = new Map([
  [0, '未披露'],
  [1, '食品'],
  [2, '化妆品'],
  [3, '工业品'],
]);

// 招标角色类型
// const TENDER_ROLE_TYPE_MAP = new Map([
//   [1, '招标单位'],
//   [2, '中标单位'],
//   [3, '建设单位'],
//   [4, '投标单位'],
//   [5, '提及单位']
// ])

const _getTitle = (cate) => {
  // 合并工商特殊类型
  if (cate === 9999) {
    return '工商';
  }
  const node = find(titleMap, (m) => includes(m.Category, `${cate}`));
  return node?.Desc ?? '';
};

// 是否能跳转keyno
// let _isValidHighlisghtKeyNo = keyno => {
//   return !!keyno && ((/^[a-f0-9]{32}$/.test(keyno)) || keyno.startsWith('p'));
// };

// 处理招投标公司显示逻辑

/**
 * 获取风险动态列表的描述
 * @method
 * @param  {[item.datatype]}  1:公司 2:人员
 * @description Highlight Array
 'Id': '',
 'Name': '',
 'Org': '',
 * @return {Object}
 *
 * <AUTHOR> Zhang <<EMAIL>>
 * @since  2019-07-29T16:12:12+0800
 */
const getRiskListDesc = (item, useH5Style = false) => {
  const { lineBreak, prefixLineBreak, redStyle, labelStyleStart, labelStyleStartOneLine, labelStyleEnd, linkStyleStart, linkStyleEnd } =
    riskHelper.getH5Style(useH5Style);
  /**
   * Title: 当前分类的父分类
   * @type {Object}
   */
  const desc: DescModel = {
    Title: '',
    Subtitle: '',
    Content: '',
    Highlight: [],
    HighLightContent: [],
    OtherHighlight: [],
    RelateChange: '',
  };

  let contentInfo: ContentInfoModel;
  // const context = callContext.get({});
  // let isEncryData = context.custom?.isEncryData || false;
  let isEncryData = false;
  // 判断是否是来自B端的请求,C端请求需对自然人脱密(裁判文书)
  // const useEncryName = !CommonHelper.isFromBSide();
  const useEncryName = false;
  // const isNormalUser = !(context.isVip || context.isSvip);
  const isNormalUser = false;
  let changeInfo: ChangeInfoModel;
  let extend = null;

  item.ChangeStatus = Number(item.ChangeStatus);
  item.Category = Number(item.Category);

  desc.Title = _getTitle(item.Category);
  try {
    changeInfo = item.ChangeExtend ? JSON.parse(item.ChangeExtend) : {};
    if (isEncryData && (CommonHelper.isEncryReason(changeInfo.RC || '') || CommonHelper.isEncryCategory(item.Category))) {
      isEncryData = true;
    } else {
      isEncryData = false;
    }
    if (item.Category === 130) {
      changeInfo.B = changeInfo.B ? moment.unix(changeInfo.B).format('YYYY-MM-DD') : '';
      changeInfo.C = changeInfo.C ? moment.unix(changeInfo.C).format('YYYY-MM-DD') : '';
    }
    if (item.Category === 131) {
      changeInfo.C = changeInfo.C ? moment.unix(changeInfo.C).format('YYYY-MM-DD') : '';
      changeInfo.D = changeInfo.D ? moment.unix(changeInfo.D).format('YYYY-MM-DD') : '';
    }
    if (item.Category === 28) {
      changeInfo.A = changeInfo.A ? JSON.parse(changeInfo.A) : [];
    }
    if (item.Category === 58) {
      changeInfo.B = changeInfo.B ? JSON.parse(changeInfo.B) : [];
      changeInfo.C = changeInfo.C ? JSON.parse(changeInfo.C) : [];
      changeInfo.D = changeInfo.D ? moment.unix(changeInfo.D).format('YYYY-MM-DD') : '';
    }
    if (item.Category === 59 || item.Category === 224) {
      changeInfo.B = changeInfo.B ? JSON.parse(changeInfo.B) : {};
      changeInfo.F = changeInfo.F ? JSON.parse(changeInfo.F) : {};
      changeInfo.G = changeInfo.G ? JSON.parse(changeInfo.G) : {};
      changeInfo.H = changeInfo.H ? JSON.parse(changeInfo.H) : {};
      changeInfo.I = changeInfo.I ? JSON.parse(changeInfo.I) : {};
    }
    if (item.Category === 76 || item.Category === 228) {
      changeInfo.C = changeInfo.C ? JSON.parse(changeInfo.C) : [];
    }
    if (item.Category === 78) {
      changeInfo.D = changeInfo.D ? JSON.parse(changeInfo.D) : [];
    }
    if (item.Category === 79) {
      changeInfo.F = changeInfo.F ? JSON.parse(changeInfo.F) : [];
      changeInfo.G = changeInfo.G ? JSON.parse(changeInfo.G) : [];
    }
  } catch (error) {
    // 如果是反序列化异常，这里在单独记录一下
    if (error.code === 'ERR_SERIALIZER') {
      throw error;
      // appLogger.error(JSON.stringify(item), error);
    }
    return desc;
  }

  // 处理changeInfo中的日期格式
  if (item.Category === 56 || item.Category === 216 || item.Category === 117) {
    changeInfo.C = changeInfo.C ? moment.unix(changeInfo.C).format('YYYY-MM-DD') : '';
    changeInfo.D = changeInfo.D ? moment.unix(changeInfo.D).format('YYYY-MM-DD') : '';
  }
  if (item.Category === 57) {
    changeInfo.D = changeInfo.D ? moment.unix(changeInfo.D).format('YYYY-MM-DD HH:mm:ss') : '';
  }
  if (item.Category === 61) {
    changeInfo.B = changeInfo.B ? moment.unix(changeInfo.B).format('YYYY-MM-DD') : '';
    changeInfo.C = changeInfo.C ? moment.unix(changeInfo.C).format('YYYY-MM-DD') : '';
  }
  if (item.Category === 63) {
    changeInfo.G = changeInfo.G ? moment.unix(changeInfo.G).format('YYYY-MM-DD') : '';
  }

  let dtChange = item.ChangeDate ? moment.unix(item.ChangeDate).format('YYYY-MM-DD') : '-';
  let dtCreate = item.CreateDate ? moment.unix(item.CreateDate).format('YYYY-MM-DD') : '-';
  if (dtChange === '1900-01-01') {
    dtChange = '-';
    dtCreate = '-';
  }
  let beforeContent = item.BeforeContent;
  let afterContent = item.AfterContent;
  const beforeNames = [];
  const afterNames = [];
  const tMsg = [];
  let tNames = [];
  const tNames2 = [];
  // const tNames3 = [];
  // const tNames4 = [];
  // const tNames5 = [];
  // const tNames6 = [];
  let tSufffix = '';
  let hasAmount;
  let hasPercent;
  let t = '';
  const tBigEventList = [];
  let extend1 = null;

  const needJsonParseCat = new Set([21, 24, 25]);
  if (needJsonParseCat.has(item.Category)) {
    beforeContent = JSON.parse(beforeContent);
    afterContent = JSON.parse(afterContent);
    if ((Number(changeInfo.T) === 1 && Number(item.Category) === 21) || Number(item.Category) === 24) {
      beforeContent.forEach((item) => {
        beforeNames.push(item.Name);
        desc.Highlight.push({
          Id: item.KeyNo || '',
          Name: item.Name || '',
          Org: item.Org || 0,
        });
      });
      afterContent.forEach((item) => {
        afterNames.push(item.Name);
        desc.Highlight.push({
          Id: item.KeyNo || '',
          Name: item.Name || '',
          Org: item.Org || 0,
        });
      });
    }
  }

  // 处理 content
  if (item.Category === 41) {
    beforeContent = beforeContent ? CommonHelper.replaceAll(beforeContent, '<em>', `${labelStyleEnd}${redStyle}`) : '';
    beforeContent = beforeContent ? CommonHelper.replaceAll(beforeContent, '</em>', `${labelStyleEnd}${labelStyleStartOneLine}`) : '';
    afterContent = afterContent ? CommonHelper.replaceAll(afterContent, '<em>', `${labelStyleEnd}${redStyle}`) : '';
    afterContent = afterContent ? CommonHelper.replaceAll(afterContent, '</em>', `${labelStyleEnd}${labelStyleStartOneLine}`) : '';
  }

  switch (item.Category) {
    case 123: {
      // 减资公告
      desc.Subtitle = `新增减资公告`;
      tMsg.push(`类型：新增减资公告`);
      tMsg.push(`公告标题：${changeInfo.F || '-'}`);
      tMsg.push(`公告内容：${changeInfo.D || '-'}`);
      tMsg.push(`公告期限：${changeInfo.C || '-'}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      break;
    }
    case 132: {
      // 减资公告（撤销或终止）
      const changeType = changeInfo.I === 1 ? '撤销' : '终止';
      desc.Subtitle = `新增减资公告`;
      tMsg.push(`类型：${changeType}减资公告`);
      tMsg.push(`公告标题：${changeInfo.F || '-'}`);
      tMsg.push(`公告内容：${changeInfo.D || '-'}`);
      tMsg.push(`公告期限：${changeInfo.C || '-'}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      break;
    }
    case 2:
    case 205:
      desc.Subtitle = '被列入失信被执行人';
      riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
      if (changeInfo.F) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '涉案金额（元）',
          v: CommonHelper.handleNum(Number(changeInfo.F)),
        });
      }
      // tMsg.push(`${labelStyleStart}履行情况：${labelStyleEnd}${changeInfo.B}`)
      // if (changeInfo.D) {
      //   tMsg.push(`${labelStyleStart}失信行为：${labelStyleEnd}${changeInfo.D}`)
      // }
      riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.E || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: dtChange || '-' });
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      if (item.Category === 205) {
        desc['HotDynamicDesc'] = '近期被列为<em>失信被执行人</em>';
      }
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，履行情况：${changeInfo.B}`;
      break;
    case 3:
      desc.Subtitle = '被列入被执行人';
      if (Number(changeInfo.T) === 1) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '执行标的（元）',
          v: changeInfo.B ? CommonHelper.handleNum(Number(changeInfo.B)) : '-',
          showEm: changeInfo.B > 2000000,
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.E || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '立案日期', v: dtChange || '-' });
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，执行标的（元）：${changeInfo.B}`;
      } else {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.C || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '立案日期', v: dtChange || '-' });
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，案号：${changeInfo.A}`;
      }
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      break;
    case 117:
      desc.Subtitle = '被列入非正常户';
      riskHelper.pushContentKV({ contents: tMsg, k: '纳税人识别号', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '列入机关', v: changeInfo.B || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '列入日期', v: changeInfo.C || '-' });
      // desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，案号：${changeInfo.A}`;
      // }
      // desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      break;
    case 4:
    case 221:
      // 裁判文书
      desc.Subtitle = '新增裁判文书';
      tMsg.push(`${labelStyleStart}案号：${labelStyleEnd}${changeInfo.A || '-'}`);
      tMsg.push(`${labelStyleStart}案由：${labelStyleEnd}${changeInfo.F || '-'}`);
      if (!isNormalUser) {
        if (changeInfo.I) {
          tMsg.push(`${labelStyleStart}案件金额（元）：${labelStyleEnd}${CommonHelper.handleNum(Number(changeInfo.I))}`);
        }
        if (changeInfo.K) {
          changeInfo.K.forEach((item) => {
            if (item.Name) {
              desc.Highlight.push({
                Id: item.KeyNo || '',
                Name: item.Name,
                Org: item.Org || 0,
              });
            }
          });
          tMsg.push(
            `${
              CommonHelper.processCaseRole({ field: changeInfo.K, lineBreak, useH5Style, item, useEncryName }) ||
              labelStyleStart + '当事人：' + labelStyleEnd + '-'
            }`,
          );
        }
        if (changeInfo.M) {
          tMsg.push(`${labelStyleStart}裁判日期：${labelStyleEnd}${moment.unix(changeInfo.M).format('YYYY-MM-DD')}`);
        }
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，案由：${changeInfo.F || '-'}`;
      break;
    case 7:
    case 218:
      // 法院公告
      desc.Subtitle = `新增法院公告${changeInfo.category === '其他法院公告' ? '' : '-' + changeInfo.category}`;
      contentInfo = riskHelper.getContent7And218(changeInfo, useH5Style, useEncryName, item);
      desc.Content = prefixLineBreak + contentInfo.tMsg.join(lineBreak);
      desc.Highlight = contentInfo.highlight;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，${contentInfo.bigEventDesc.join('，')}`;
      break;
    case 11:
      desc.Subtitle = '被列入经营异常';
      tMsg.push(`${labelStyleStart}列入原因：${labelStyleEnd}${changeInfo.B || '-'}`);
      tMsg.push(`${labelStyleStart}列入机关：${labelStyleEnd}${changeInfo.C || '-'}`);
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}列入日期：${labelStyleEnd}${moment.unix(changeInfo.A).format('YYYY-MM-DD')}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，列入原因：${changeInfo.B || '-'}`;
      break;
    case 12:
    case 213:
      desc.Subtitle = '新增股权出质';
      if (changeInfo.T2 === 1) {
        // Status 无效
        desc.Subtitle = `股权出质${changeInfo.Status || ''}`;
      }
      hasAmount = changeInfo.PledgedAmount && Number(changeInfo.PledgedAmount) !== 0;
      hasPercent = changeInfo.Percent && Number(changeInfo.Percent) !== 0;
      // 出质人（企业）
      if (Number(changeInfo.T) === 2) {
        if (changeInfo.PledgorInfo && changeInfo.PledgorInfo.Name) {
          tMsg.push(`${labelStyleStart}出质人：${labelStyleEnd}${changeInfo.PledgorInfo.Name || '-'}`);
        }
        if (changeInfo.PledgeeInfo && changeInfo.PledgeeInfo.Name) {
          tMsg.push(`${labelStyleStart}质权人：${labelStyleEnd}${changeInfo.PledgeeInfo.Name || '-'}`);
        }
        tMsg.push(`${labelStyleStart}出质股权标的企业：${labelStyleEnd}${changeInfo.CompanyName || '-'}`);
        if (hasAmount) {
          tMsg.push(`${labelStyleStart}出质股权数额：${labelStyleEnd}${changeInfo.PledgedAmount}`);
        }
        if (hasPercent) {
          tMsg.push(`${labelStyleStart}占所持股份比例：${labelStyleEnd}${changeInfo.Percent}`);
        }
        if (changeInfo.RegDate) {
          tMsg.push(`${labelStyleStart}登记日期：${labelStyleEnd}${moment.unix(changeInfo.RegDate).format('YYYY-MM-DD')}`);
        }
        desc.Content = prefixLineBreak + tMsg.join(lineBreak);
        if (changeInfo.PledgorInfo && changeInfo.PledgorInfo.Name) {
          desc.Highlight.push({
            Id: changeInfo.PledgorInfo.KeyNo || '',
            Name: changeInfo.PledgorInfo.Name || '',
            Org: changeInfo.PledgorInfo.Org || 0,
          });
        }
        if (changeInfo.PledgeeInfo && changeInfo.PledgeeInfo.Name) {
          desc.Highlight.push({
            Id: changeInfo.PledgeeInfo.KeyNo || '',
            Name: changeInfo.PledgeeInfo.Name || '',
            Org: changeInfo.PledgeeInfo.Org || 0,
          });
        }
        desc.Highlight.push({
          Id: changeInfo.CompanyId || '',
          Name: changeInfo.CompanyName || '',
          Org: changeInfo.Org || 0,
        });
      } else if (Number(changeInfo.T) === 1) {
        // 出质股权标的企业
        tMsg.push(`${labelStyleStart}出质人：${labelStyleEnd}${changeInfo.Name || '-'}`);
        if (changeInfo.PledgeeInfo && changeInfo.PledgeeInfo.Name) {
          tMsg.push(`${labelStyleStart}质权人：${labelStyleEnd}${changeInfo.PledgeeInfo.Name || '-'}`);
        }
        if (changeInfo.CompanyName) {
          tMsg.push(`${labelStyleStart}出质股权标的企业：${labelStyleEnd}${changeInfo.CompanyName || '-'}`);
        }
        if (hasAmount) {
          tMsg.push(`${labelStyleStart}出质股权数额：${labelStyleEnd}${changeInfo.PledgedAmount}`);
        }
        if (hasPercent) {
          tMsg.push(`${labelStyleStart}占该公司股权比例：${labelStyleEnd}${changeInfo.Percent}`);
        }
        if (changeInfo.RegDate) {
          tMsg.push(`${labelStyleStart}登记日期：${labelStyleEnd}${moment.unix(changeInfo.RegDate).format('YYYY-MM-DD')}`);
        }
        desc.Content = prefixLineBreak + tMsg.join(lineBreak);
        desc.Highlight.push({
          Id: changeInfo.KeyNo || '',
          Name: changeInfo.Name || '',
          Org: changeInfo.Org || 0,
        });
        if (changeInfo.PledgeeInfo && changeInfo.PledgeeInfo.Name) {
          desc.Highlight.push({
            Id: changeInfo.PledgeeInfo.KeyNo || '',
            Name: changeInfo.PledgeeInfo.Name || '',
            Org: changeInfo.PledgeeInfo.Org || 0,
          });
        }
        if (changeInfo.CompanyName) {
          desc.Highlight.push({
            Id: changeInfo.CompanyId || '',
            Name: changeInfo.CompanyName || '',
            Org: 0,
          });
        }
      }
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，出质股权数额：${hasAmount ? changeInfo.PledgedAmount : '-'}`;
      break;
    case 13:
    case 225:
      desc.Subtitle = '被行政处罚';
      if (isNormalUser) {
        tMsg.push(`${labelStyleStart}决定文书号：${labelStyleEnd}${linkStyleStart}${changeInfo.C || '-'}${linkStyleEnd}`);
        tMsg.push(`${labelStyleStart}决定机关：${labelStyleEnd}${changeInfo.D || '-'}`);
      } else {
        tMsg.push(`${labelStyleStart}决定文书号：${labelStyleEnd}${linkStyleStart}${changeInfo.C || '-'}${linkStyleEnd}`);
        if (changeInfo.A) {
          tMsg.push(`${labelStyleStart}违法行为类型：${labelStyleEnd}${changeInfo.A}`);
        }
        if (changeInfo.B) {
          tMsg.push(`${labelStyleStart}违法事实：${labelStyleEnd}${changeInfo.B}`);
        }
        tMsg.push(`${labelStyleStart}决定机关：${labelStyleEnd}${changeInfo.D || '-'}`);
        tMsg.push(`${labelStyleStart}决定日期：${labelStyleEnd}${dtChange}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，违法事实：${changeInfo.B || '-'}`;
      break;
    case 96:
    case 233:
    case 107:
    case 238:
      desc.Subtitle = '被行政处罚';
      if (isNormalUser) {
        riskHelper.pushContentKV({ contents: tMsg, k: '决定文书号', v: changeInfo.D || '-', isLink: true });
        riskHelper.pushContentKV({ contents: tMsg, k: '处罚单位', v: changeInfo.A || '-' });
      } else {
        riskHelper.pushContentKV({ contents: tMsg, k: '决定文书号', v: changeInfo.D || '-', isLink: true });
        // if (changeInfo.B) {
        //   riskHelper.pushContentKV({ contents: tMsg, k: '违法事实', v: changeInfo.B, line: 3 })
        // }
        if (changeInfo.B) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '违法事实',
            v: useEncryName ? changeInfo.B1 || changeInfo.B : changeInfo.B,
            line: 3,
          });
        }
        if (changeInfo.F) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '处罚金额（万元）',
            v: CommonHelper.div(changeInfo.F, 10000).toFixed(2),
            showEm:
              (includes(changeInfo.D, '反垄断') && Number(changeInfo.F) > 5000000) ||
              ((changeInfo.M === 'A01' || changeInfo.M === 'A02') && Number(changeInfo.F) > 3000000) ||
              (changeInfo.M !== 'A01' && changeInfo.M !== 'A02' && !includes(changeInfo.D, '反垄断') && Number(changeInfo.F) > 100000),
          });
        }
        riskHelper.pushContentKV({ contents: tMsg, k: '处罚单位', v: changeInfo.A || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '处罚日期', v: dtChange });
      }
      // 将文案拼接等规则放入公共方法调用
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，违法事实：${changeInfo.B || '-'}`;
      break;
    case 14:
      desc.Subtitle = '被抽查检查';
      if (!CommonHelper.isNotEmpty(changeInfo.G) || !changeInfo.G) {
        tMsg.push(`${labelStyleStart}结果：${labelStyleEnd}${changeInfo.A || '-'}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}检查实施机关：${labelStyleEnd}${changeInfo.B}`);
      }
      tMsg.push(`${labelStyleStart}日期：${labelStyleEnd}${dtChange}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，结果：${changeInfo.A || '-'}`;
      break;
    case 15:
      // 动产抵押 使用加密的id做为ObjectId
      desc.Subtitle = '新增动产抵押';
      if (changeInfo.Pledgor && typeof changeInfo.Pledgor === 'object') {
        afterNames.push(changeInfo.Pledgor.A);
        desc.Highlight.push({
          Id: changeInfo.Pledgor.K || '',
          Name: changeInfo.Pledgor.A || '',
          Org: changeInfo.Pledgor.O || 0,
        });
      }
      if (changeInfo.Pledgee && changeInfo.Pledgee.length && typeof changeInfo.Pledgee === 'object') {
        changeInfo.Pledgee.forEach((item) => {
          beforeNames.push(item.A);
          desc.Highlight.push({
            Id: item.K || '',
            Name: item.A || '',
            Org: item.O || 0,
          });
        });
      }
      if (afterNames.length) {
        tMsg.push(`${labelStyleStart}抵押人：${labelStyleEnd}${afterNames.join('，') || '-'}`);
      }
      if (beforeNames.length) {
        tMsg.push(`${labelStyleStart}抵押权人：${labelStyleEnd}${beforeNames.join('，') || '-'}`);
      }
      if (changeInfo.A) {
        changeInfo.A = changeInfo.A.trim();
        tMsg.push(`${labelStyleStart}被担保债权数额：${labelStyleEnd}${changeInfo.A}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}债务人履行债务期限：${labelStyleEnd}${changeInfo.B}`);
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}登记日期：${labelStyleEnd}${moment(changeInfo.C).format('YYYY-MM-DD')}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc.IsDecrypted = 0;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，被担保债权数额：${changeInfo.A || '-'}`;
      break;
    case 16:
      desc.Subtitle = '开始清算';
      desc.Content = `${labelStyleStart}清算组负责人：${labelStyleEnd}${changeInfo.A}，${labelStyleStart}清算组成员：${labelStyleEnd}${changeInfo.B}`;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 17:
    case 203:
      // 撤出
      if (Number(item.ChangeStatus) === 2) {
        desc.Subtitle = '退出对外投资';
        tMsg.push(`${labelStyleStart}退出投资企业：${labelStyleEnd}${changeInfo.A}`);
        if (beforeContent) {
          if (beforeContent === '0%') {
            beforeContent = '企业未公示';
            tMsg.push(`${labelStyleStart}退出前持股比例：${labelStyleEnd}${beforeContent}`);
          } else {
            tMsg.push(`${labelStyleStart}退出前持股比例：${labelStyleEnd}${beforeContent}`);
            const holdValue = Number(beforeContent.slice(0, -1));
            if (holdValue <= 50 && changeInfo?.IsBP === '2') {
              tMsg.push('不再是该企业的大股东');
              desc.RelateChange = '不再是该企业的大股东';
            }
          }
        }
        desc['BigEventDesc'] = `${dtCreate} 退出投资企业：${changeInfo.A} 退出前持股比例：${beforeContent}`;
      } else if (Number(item.ChangeStatus) === 1) {
        // 新增
        desc.Subtitle = '新增对外投资';
        tMsg.push(`${labelStyleStart}被投资企业：${labelStyleEnd}${changeInfo.A}`);
        if (!afterContent || afterContent === '0%') {
          afterContent = '企业未公示';
          tMsg.push(`${labelStyleStart}投资比例：${labelStyleEnd}${afterContent}`);
        } else {
          tMsg.push(`${labelStyleStart}投资比例：${labelStyleEnd}${afterContent}`);
          if (item.Extend2) {
            extend = JSON.parse(item.Extend2);
            if (extend?.C) {
              const parseNumber = (str: string) => {
                const num = Number(str.replace(/[^0-9.-]/g, ''));
                return isNaN(num) ? 0 : num;
              };

              let amount =
                typeof extend.C === 'string' && extend.C.includes(',')
                  ? extend.C.split(',')
                      .map(parseNumber)
                      .reduce((sum, num) => sum + num, 0)
                  : parseNumber(extend.C.toString());

              if (isNaN(amount)) {
                amount = 0;
              }

              tMsg.push(`${labelStyleStart}认缴出资额/持股数：${labelStyleEnd}${amount}${extend?.B ? extend.B.replace(/\d+\.?\d*/g, '') : '万元'}`);
            }
          }
          const holdValue = Number(afterContent.slice(0, -1));
          if (holdValue <= 50 && changeInfo?.IsBP === '1') {
            tMsg.push('成为该企业的大股东');
            desc.RelateChange = '成为该企业的大股东';
          }
        }
        desc['BigEventDesc'] = `${dtCreate} 被投资企业：${changeInfo.A} 投资比例：${afterContent}`;
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.A || '',
        Org: changeInfo.O || 0,
      });
      break;
    case 18:
    case 219:
      // 开庭公告
      desc.Subtitle = '新增开庭公告';
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}案号：${labelStyleEnd}${changeInfo.C}`);
      }
      if (changeInfo.D) {
        changeInfo.D.forEach((item) => {
          if (item.N) {
            desc.Highlight.push({
              Id: item.N || '',
              Name: item.P || '',
              Org: item.O || 0,
            });
          }
        });
        tMsg.push(
          `${
            CommonHelper.processCaseRole({ field: changeInfo.D, lineBreak, useEncryName, useH5Style, item }) ||
            labelStyleStart + '当事人：' + labelStyleEnd + '-'
          }`,
        );
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}案由：${labelStyleEnd}${changeInfo.B}`);
      }
      if (changeInfo.E) {
        tMsg.push(`${labelStyleStart}法院：${labelStyleEnd}${changeInfo.E}`);
      }
      if (changeInfo.A) {
        t = moment.unix(changeInfo.A).format('YYYY-MM-DD HH:mm');
        t = t.replace(' 00:00', '');
        tMsg.push(`${labelStyleStart}开庭时间：${labelStyleEnd}${t}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，案由：${changeInfo.B || '-'}`;
      break;
    case 20:
      desc.Subtitle = '被列入严重违法';
      tMsg.push(`${labelStyleStart}列入原因：${labelStyleEnd}${changeInfo.A || '-'}`);
      tMsg.push(`${labelStyleStart}决定机关：${labelStyleEnd}${changeInfo.C || '-'}`);
      tMsg.push(`${labelStyleStart}列入日期：${labelStyleEnd}${dtChange || '-'}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，列入原因：${changeInfo.A || '-'}`;
      break;
    case 21:
      if (Number(changeInfo.T) === 1) {
        desc.Subtitle = '受益所有人变更';
        desc.Content = `${labelStyleStartOneLine}从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”${labelStyleEnd}`;
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`;
      }
      if (Number(changeInfo.T) === 2) {
        if (changeInfo.Increase === 1) {
          // 上升
          desc.Subtitle = '受益所有人受益股份上升';
          desc.Content = `${labelStyleStartOneLine}${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”上升到“${afterContent.PercentTotal}”${labelStyleEnd}`;
          desc['BigEventDesc'] = `${dtCreate} ${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”上升到“${afterContent.PercentTotal}”`;
          desc.Highlight.push({
            Id: beforeContent.KeyNo || '',
            Name: beforeContent.Name || '',
            Org: beforeContent.Org || 0,
          });
        } else {
          desc.Subtitle = '受益所有人受益股份下降';
          desc.Content = `${labelStyleStartOneLine}${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”下降到“${afterContent.PercentTotal}”${labelStyleEnd}`;
          desc['BigEventDesc'] = `${dtCreate} ${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”下降到“${afterContent.PercentTotal}”`;
          desc.Highlight.push({
            Id: beforeContent.KeyNo || '',
            Name: beforeContent.Name || '',
            Org: beforeContent.Org || 0,
          });
        }
        // desc.Highlight.push({
        //   'Id': afterContent.KeyNo || '',
        //   'Name': afterContent.Name || '',
        //   'Org': afterContent.Org || 0,
        // });
      }
      break;
    case 114:
      tNames = chain(changeInfo)
        .map((c) => `${JSON.parse(c.ChangeExtend).T}_${JSON.parse(c.ChangeExtend).Increase || ''}`)
        .uniq()
        .value();
      if (tNames.length > 1 || tNames[0] === '1_') {
        desc.Subtitle = '受益所有人变更';
      } else if (tNames[0] === '2_1') {
        desc.Subtitle = '受益所有人受益股份上升';
      } else {
        desc.Subtitle = '受益所有人受益股份下降';
      }
      tNames = [];
      // 生成文案和高亮
      forEach(changeInfo, (item) => {
        beforeContent = JSON.parse(item.BeforeContent || '{}');
        afterContent = JSON.parse(item.AfterContent || '{}');
        extend1 = JSON.parse(item.ChangeExtend);
        if (extend1.T === 1) {
          for (const beforeName of beforeContent) {
            beforeNames.push(beforeName.Name);
            desc.Highlight.push({
              Id: beforeName.KeyNo || '',
              Name: beforeName.Name || '',
              Org: beforeName.Org || 0,
            });
          }
          for (const afterName of afterContent) {
            afterNames.push(afterName.Name);
            desc.Highlight.push({
              Id: afterName.KeyNo || '',
              Name: afterName.Name || '',
              Org: afterName.Org || 0,
            });
          }
          tNames.push(...beforeNames);
          tNames.push(...afterNames);
          tMsg.push(`从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`);
          if (changeInfo.length === 1) {
            desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`;
          }
        }
        if (extend1.T === 2) {
          if (extend1.Increase === 1) {
            // 上升
            tMsg.push(`${beforeContent.Name}的最终受益股份从“${beforeContent.PercentTotal}”上升到“${afterContent.PercentTotal}”`);
            desc.Highlight.push({
              Id: beforeContent.KeyNo || '',
              Name: beforeContent.Name || '',
              Org: beforeContent.Org || 0,
            });
            if (changeInfo.length === 1) {
              desc['BigEventDesc'] = `${dtCreate} ${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”上升到“${afterContent.PercentTotal}”`;
            }
            tNames.push(beforeContent.Name);
          } else {
            tMsg.push(`${beforeContent.Name}的最终受益股份从“${beforeContent.PercentTotal}”下降到“${afterContent.PercentTotal}”`);
            desc.Highlight.push({
              Id: beforeContent.KeyNo || '',
              Name: beforeContent.Name || '',
              Org: beforeContent.Org || 0,
            });
            if (changeInfo.length === 1) {
              desc['BigEventDesc'] = `${dtCreate} ${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”下降到“${afterContent.PercentTotal}”`;
            }
            tNames.push(beforeContent.Name);
          }
        }
      });
      tNames = uniq(tNames);
      if (changeInfo.length > 1) {
        desc['BigEventDesc'] = `${dtCreate} 受益所有人变更：${tNames.length > 3 ? tNames.slice(0, 3).join('、') + '等' : tNames.join('、')}`;
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      break;
    case 22:
      desc.Subtitle = '被环保处罚';
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '决定文书号', v: changeInfo.D, isLink: true });
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '违法事实',
          v: useEncryName ? changeInfo.A1 || changeInfo.A : changeInfo.A,
          line: 3,
        });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '处罚金额（万元）',
          v: Number(changeInfo.E).toFixed(2),
          showEm:
            (includes(changeInfo.D, '反垄断') && Number(changeInfo.E) > 500) ||
            ((changeInfo.M === 'A01' || changeInfo.M === 'A02') && Number(changeInfo.E) > 300) ||
            (changeInfo.M !== 'A01' && changeInfo.M !== 'A02' && !includes(changeInfo.D, '反垄断') && Number(changeInfo.E) > 10),
        });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '处罚结果', v: changeInfo.B, line: 3 });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '处罚单位', v: changeInfo.F });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '处罚日期',
          v: moment.unix(changeInfo.C).format('YYYY-MM-DD'),
        });
      }
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，处罚结果：${changeInfo.B || '-'}`;
      break;
    case 23:
      desc.Subtitle = '新增简易注销';
      tMsg.push(`${labelStyleStart}简易注销结果：${labelStyleEnd}${changeInfo.B}`);
      if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}核准日期：${labelStyleEnd}${changeInfo.D}`);
      } else {
        tMsg.push(`${labelStyleStart}公告期：${labelStyleEnd}${changeInfo.A}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} 简易注销结果：${changeInfo.B}`;
      break;
    case 24:
      desc.Subtitle = '大股东变更';
      desc.Content = `${labelStyleStartOneLine}从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”${labelStyleEnd}`;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`;
      break;
    case 25:
      desc.Subtitle = '实际控制人变更';
      desc.Content = `${labelStyleStartOneLine}从“${beforeContent.Name}”变更为“${afterContent.Name}”${labelStyleEnd}`;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从“${beforeContent.Name}”变更为“${afterContent.Name}”`;
      desc.Highlight.push({
        Id: beforeContent.KeyNo || '',
        Name: beforeContent.Name || '',
        Org: beforeContent.Org || 0,
      });
      desc.Highlight.push({
        Id: afterContent.KeyNo || '',
        Name: afterContent.Name || '',
        Org: afterContent.Org || 0,
      });
      break;
    case 26:
    case 212:
      // T2 Status: 失效 无效 解除
      if (changeInfo.T2 === 1) {
        desc.Subtitle = `股权冻结${changeInfo.Status}`;
        if (changeInfo.ExecutionNoticeNum) {
          tMsg.push(`${labelStyleStart}执行通知书文号：${labelStyleEnd}${changeInfo.ExecutionNoticeNum}`);
        }
        if (changeInfo.CompanyName) {
          tMsg.push(`${labelStyleStart}冻结股权标的企业：${labelStyleEnd}${changeInfo.CompanyName}`);
        }
        if (changeInfo.EquityAmount) {
          changeInfo.EquityAmount = changeInfo.EquityAmount.trim();
          tMsg.push(`${labelStyleStart}冻结权益数额：${labelStyleEnd}${changeInfo.EquityAmount}`);
        }
        if (changeInfo.UnFreezeDate) {
          tMsg.push(`${labelStyleStart}解冻日期：${labelStyleEnd}${changeInfo.UnFreezeDate || '-'}`);
        }
        desc.Highlight.push({
          Id: changeInfo.KeyNo || '',
          Name: changeInfo.Name || '',
          Org: changeInfo.Org || 0,
        });
        desc.Highlight.push({
          Id: changeInfo.CompanyId || '',
          Name: changeInfo.CompanyName || '',
          Org: changeInfo.CompanyOrg || 0,
        });
        // desc['BigEventDesc'] = `${changeInfo.UnFreezeDate || ''} 股权冻结解除`;
      } else {
        desc.Subtitle = '新增股权冻结';
        if (item.Category === 212) {
          desc.Subtitle = '股权被冻结';
        }
        if (changeInfo.ExecutionNoticeNum) {
          tMsg.push(`${labelStyleStart}执行通知书文号：${labelStyleEnd}${changeInfo.ExecutionNoticeNum}`);
        }
        if (changeInfo.Name) {
          tMsg.push(`${labelStyleStart}被执行人：${labelStyleEnd}${changeInfo.Name}`);
        }
        if (changeInfo.CompanyName) {
          tMsg.push(`${labelStyleStart}冻结股权标的企业：${labelStyleEnd}${changeInfo.CompanyName}`);
        }
        if (changeInfo.EquityAmount) {
          changeInfo.EquityAmount = changeInfo.EquityAmount.trim();
          tMsg.push(`${labelStyleStart}冻结权益数额：${labelStyleEnd}${changeInfo.EquityAmount}`);
        }
        if (changeInfo.FreezeStartDate || changeInfo.FreezeEndDate) {
          tMsg.push(`${labelStyleStart}冻结期限：${labelStyleEnd}${changeInfo.FreezeStartDate || '-'}至${changeInfo.FreezeEndDate || '-'}`);
        }
        desc.Highlight.push({
          Id: changeInfo.KeyNo || '',
          Name: changeInfo.Name || '',
          Org: changeInfo.Org || 0,
        });
        desc.Highlight.push({
          Id: changeInfo.CompanyId || '',
          Name: changeInfo.CompanyName || '',
          Org: changeInfo.CompanyOrg || 0,
        });
        // desc['BigEventDesc'] = `${changeInfo.FreezeStartDate || ''} 股权被冻结`;
      }
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，冻结权益数额：${changeInfo.EquityAmount || '-'}`;

      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      break;
    case 27:
    case 217:
      desc.Subtitle = '新增送达公告';
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}案号：${labelStyleEnd}${changeInfo.C}`);
      }
      if (changeInfo.D?.length) {
        changeInfo.D.forEach((b) => {
          desc.Highlight.push({
            Id: b.N || '',
            Name: b.P || '',
            Org: b.O || 0,
          });
        });
        tMsg.push(
          `${
            CommonHelper.processCaseRole({ field: changeInfo.D, lineBreak, useEncryName, useH5Style, item }) ||
            labelStyleStart + '当事人：' + labelStyleEnd + '-'
          }`,
        );
      }
      tMsg.push(`${labelStyleStart}法院名称：${labelStyleEnd}${changeInfo.B}`);
      tMsg.push(`${labelStyleStart}发布日期：${labelStyleEnd}${changeInfo.A}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，法院名称：${changeInfo.B || '-'}`;
      break;
    case 28:
      // 投资机构跳转没有org
      desc.Title = '经营状况';
      desc.Subtitle = '融资动态';
      if (changeInfo.G) {
        tMsg.push(`${labelStyleStart}产品名称：${labelStyleEnd}${changeInfo.G}`);
        tBigEventList.push(`产品名称：${changeInfo.G}`);
        if (changeInfo.I) {
          desc.OtherHighlight.push({
            Id: changeInfo.I,
            Name: changeInfo.G,
            Type: 'PRODUCT',
          });
        }
      }
      if (changeInfo.A && changeInfo.A.length) {
        changeInfo.A.forEach((item) => {
          if (item.Name && item.Name !== '投资方未知') {
            tNames.push(item.Name);
            if (item.Id) {
              desc.Highlight.push({
                Id: item.Id || '',
                Name: item.Name || '',
                Org: item.Org || 13,
              });
            }
            if (item.KeyNo) {
              desc.Highlight.push({
                Id: item.KeyNo || '',
                Name: item.Name || '',
                Org: item.Org || 0,
              });
            }
          }
        });
        if (tNames.length) {
          tMsg.push(`${labelStyleStart}投资方：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
          tBigEventList.push(`投资方：${tNames.join('、')}`);
        }
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}融资轮次：${labelStyleEnd}${changeInfo.C}`);
        tBigEventList.push(`融资轮次：${changeInfo.C}`);
      }
      if (changeInfo.B && changeInfo.B !== '金额未知') {
        tMsg.push(`${labelStyleStart}融资金额：${labelStyleEnd}${changeInfo.B}`);
        tBigEventList.push(`融资金额：${changeInfo.B}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，${tBigEventList.join(' ')}`;
      break;
    case 29:
      desc.Subtitle = '新增税收违法';
      tMsg.push(`${labelStyleStart}案件性质：${labelStyleEnd}${changeInfo.A}`);
      if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}罚款金额：${labelStyleEnd}${CommonHelper.handleNum(Number(changeInfo.D))}`);
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}税务机关：${labelStyleEnd}${changeInfo.C}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}发布日期：${labelStyleEnd}${moment.unix(changeInfo.B).format('YYYY-MM-DD')}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，案件性质：${changeInfo.A}`;
      break;
    case 30:
      desc.Subtitle = '新增土地抵押';
      if (changeInfo.E && typeof changeInfo.E === 'object') {
        afterNames.push(changeInfo.E.name);
        desc.Highlight.push({
          Id: changeInfo.E.KeyNo || '',
          Name: changeInfo.E.name || '',
          Org: changeInfo.E.Org || 0,
        });
      }
      if (changeInfo.F && typeof changeInfo.F === 'object') {
        beforeNames.push(changeInfo.F.name);
        desc.Highlight.push({
          Id: changeInfo.F.KeyNo || '',
          Name: changeInfo.F.name || '',
          Org: changeInfo.F.Org || 0,
        });
      }
      if (afterNames.length) {
        tMsg.push(`${labelStyleStart}抵押人：${labelStyleEnd}${afterNames.join('，') || '-'}`);
      }
      if (beforeNames.length) {
        tMsg.push(`${labelStyleStart}抵押权人：${labelStyleEnd}${beforeNames.join('，') || '-'}`);
      }
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}评估金额（万元）：${labelStyleEnd}${CommonHelper.handleNum(Number(changeInfo.A))}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}抵押金额（万元）：${labelStyleEnd}${CommonHelper.handleNum(Number(changeInfo.B))}`);
      }
      if (changeInfo.C || changeInfo.D) {
        tMsg.push(`${labelStyleStart}抵押期：${labelStyleEnd}${changeInfo.C || '-'}至${changeInfo.D || '-'}`);
      }
      // if (changeInfo.D) {
      //   tMsg.push(`${labelStyleStart}抵押结束时间：${labelStyleEnd}${changeInfo.D}`)
      // }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，抵押土地评估金额（万元）：${changeInfo.A || '-'}`;
      break;
    case 31:
      desc.Subtitle = '新增欠税公告';
      riskHelper.pushContentKV({ contents: tMsg, k: '欠税税种', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '欠税余额（元）',
        v: changeInfo.B ? CommonHelper.handleNum(Number(changeInfo.B)) : '-',
        showEm: includes(changeInfo.A, '企业所得税') && changeInfo.B > 2000000,
      });
      riskHelper.pushContentKV({ contents: tMsg, k: '发布单位', v: changeInfo.D || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: dtChange || '-' });

      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，欠税税种：${changeInfo.A || '-'}`;
      break;
    case 130:
      desc.Subtitle = '新增税务催报';
      riskHelper.pushContentKV({ contents: tMsg, k: '税种', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '所属期',
        v: `${changeInfo.B}至${changeInfo.C}`,
      });
      riskHelper.pushContentKV({ contents: tMsg, k: '主管税务机关', v: changeInfo.D || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: dtChange || '-' });
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，税种：${changeInfo.A || '-'}`;
      break;
    case 131:
      desc.Subtitle = '新增税务催缴';
      riskHelper.pushContentKV({ contents: tMsg, k: '税种', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '欠缴余额（元）', v: changeInfo.B || '-' });
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '所属期',
        v: `${changeInfo.C}至${changeInfo.D}`,
      });
      riskHelper.pushContentKV({ contents: tMsg, k: '主管税务机关', v: changeInfo.F || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: dtChange || '-' });
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，税种：${changeInfo.A || '-'}`;
      break;
    case 37:
      extend1 = item.Extend1 ? JSON.parse(item.Extend1) : {};
      if (extend1.T === 1) {
        desc.Subtitle = '注册资本币种变更';
        desc.Content = `从${redStyle}“${changeInfo.A}”${labelStyleEnd}变更为${redStyle}“${changeInfo.B}”${labelStyleEnd}`;
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从${changeInfo.A}变更为${changeInfo.B}`;
      } else if (Number(changeInfo.T) === 2) {
        desc.Subtitle = '注册资本增加';
        desc.Content = `从${redStyle}“${changeInfo.A}”${labelStyleEnd}增加到${redStyle}“${changeInfo.B}”${labelStyleEnd}`;
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从${changeInfo.A}增加到${changeInfo.B}`;
      } else {
        desc.Subtitle = '注册资本减少';
        desc.Content = `从${redStyle}“${changeInfo.A}”${labelStyleEnd}减少到${redStyle}“${changeInfo.B}”${labelStyleEnd}，减少了${redStyle}“${extend1.B}${extend1.C}”${labelStyleEnd}`;
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从${changeInfo.A}减少到${changeInfo.B}`;
        desc.HighLightContent.push(changeInfo.A, changeInfo.B, extend1.B + extend1.C);
      }
      break;
    case 38:
      desc.Subtitle = '经营状态变更';
      desc.Content = `${labelStyleStartOneLine}从“${changeInfo.A}”变更为“${changeInfo.B}”${labelStyleEnd}`;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从${changeInfo.A}变更为${changeInfo.B}`;
      break;
    case 39:
      desc.Subtitle = `${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}变更`;
      desc.Content = `${labelStyleStartOneLine}从“${changeInfo.A.N}”变更为“${changeInfo.B.N}”${labelStyleEnd}`;
      desc.Highlight.push({
        Id: changeInfo.A.K || '',
        Name: changeInfo.A.N || '',
        Org: changeInfo.A.O || 0,
      });
      desc.Highlight.push({
        Id: changeInfo.B.K || '',
        Name: changeInfo.B.N || '',
        Org: changeInfo.B.O || 0,
      });
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从“${changeInfo.A.N}”变更为“${changeInfo.B.N}”`;
      break;
    case 40:
      desc.Title = '工商变更';
      desc.Subtitle = '企业地址变更';
      desc.Content = `从“${
        useH5Style ? riskHelper.changeEmToRedStyle(changeInfo.C) || changeInfo.A || '-' : changeInfo.A || '-'
      }”${lineBreak}变更为${lineBreak}“${useH5Style ? riskHelper.changeEmToRedStyle(changeInfo.D) || changeInfo.B || '-' : changeInfo.B || '-'}”`;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从“${changeInfo.A || '-'}”变更为“${changeInfo.B || '-'}”`;
      break;
    case 41:
      desc.Title = '工商变更';
      desc.Subtitle = '经营范围变更';
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '',
        v: `${labelStyleStartOneLine}从“${beforeContent}”${lineBreak}变更为${lineBreak}“${afterContent}”${labelStyleEnd}`,
        line: 3,
      });
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从“${beforeContent}”变更为“${afterContent}”`;
      desc['BigEventDesc'] = CommonHelper.replaceAll(
        CommonHelper.replaceAll(CommonHelper.replaceAll(desc['BigEventDesc'], labelStyleStartOneLine, ''), labelStyleEnd, ''),
        redStyle,
        '',
      );
      break;
    case 42:
      desc.Subtitle = '企业类型变更';
      desc.Content = `从${redStyle}“${changeInfo.A}”${labelStyleEnd}变更为${redStyle}“${changeInfo.B}”${labelStyleEnd}`;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从“${changeInfo.A}”变更为“${changeInfo.B}”`;
      break;
    case 43:
      desc.Subtitle = '营业期限变更';
      desc.Content = `${labelStyleStartOneLine}从“${moment.unix(changeInfo.A).format('YYYY-MM-DD')}”变更为“${moment
        .unix(changeInfo.B)
        .format('YYYY-MM-DD')}”${labelStyleEnd}`;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从“${moment.unix(changeInfo.A).format('YYYY-MM-DD')}”变更为“${moment
        .unix(changeInfo.B)
        .format('YYYY-MM-DD')}”`;
      break;
    case 44:
      desc.Subtitle = '股东股份变更';
      if (Number(changeInfo.IsBP)) {
        desc.Subtitle += '、大股东变更';
      }
      extend1 = riskHelper.getContent44(changeInfo, useH5Style, item);
      desc['BigEventDesc'] = `${dtCreate} ${last(extend1.bigEventDesc)}`;
      desc.Highlight = extend1.highlight;
      desc.Content = prefixLineBreak + extend1.tMsg.join(lineBreak);
      break;
    case 46:
      desc.Subtitle = '主要成员变更';
      if (changeInfo.D && changeInfo.D.length) {
        tNames = [];
        tSufffix = '';
        if (changeInfo.D.length > 3) {
          changeInfo.D = changeInfo.D.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.D.forEach((item) => {
          tNames.push(item.A);
          desc.Highlight.push({
            Id: item.K || '',
            Name: item.A || '',
            Org: item.O || 0,
          });
        });
        tMsg.push(`${labelStyleStart}职务调整：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
      }
      if (changeInfo.E && changeInfo.E.length) {
        tNames = [];
        tSufffix = '';
        if (changeInfo.E.length > 3) {
          changeInfo.E = changeInfo.E.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.E.forEach((item) => {
          tNames.push(item.A);
          desc.Highlight.push({
            Id: item.K || '',
            Name: item.A || '',
            Org: item.O || 0,
          });
        });
        tMsg.push(`${labelStyleStart}退出：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
      }
      if (changeInfo.F && changeInfo.F.length) {
        tNames = [];
        tSufffix = '';
        if (changeInfo.F.length > 3) {
          changeInfo.F = changeInfo.F.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.F.forEach((item) => {
          tNames.push(item.A);
          desc.Highlight.push({
            Id: item.K || '',
            Name: item.A || '',
            Org: item.O || 0,
          });
        });
        tMsg.push(`${labelStyleStart}新增：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${tMsg.join('，')}`;
      break;
    case 48:
    case 223:
      desc.Subtitle = '受到违规处理';
      tMsg.push(`${labelStyleStart}处罚对象：${labelStyleEnd}${changeInfo.A}`);
      tMsg.push(`${labelStyleStart}违规类型：${labelStyleEnd}${changeInfo.B}`);
      // if (changeInfo.C) {
      //   tMsg.push(`${labelStyleStart}金额（万元）：${labelStyleEnd}${changeInfo.C}`)
      // }
      tMsg.push(`${labelStyleStart}公告日期：${labelStyleEnd}${dtChange}`);
      if (changeInfo?.E?.length) {
        changeInfo.E.forEach((item) => {
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
      } else {
        desc.Highlight.push({
          Id: changeInfo.K || '',
          Name: changeInfo.A || '',
          Org: changeInfo.O || 0,
        });
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，处罚对象：${changeInfo.A}`;
      break;
    case 49:
    case 220:
      // 立案信息
      desc.Subtitle = '新增立案';
      contentInfo = riskHelper.getContent49And220(changeInfo, useH5Style, useEncryName, item);
      desc.Content = prefixLineBreak + contentInfo.tMsg.join(lineBreak);
      desc.Highlight = contentInfo.highlight;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，${contentInfo.bigEventDesc.join(' ')}`;
      break;
    case 50:
    case 214:
      // 1: 上市、2: 新三板、3: 普通公司
      // 股权质押需要区分公司的类型, 默认为1, 现在只有上市的数据
      desc.CompanyType = 1;
      if (Number(item.Category) === 214) {
        desc.CompanyType = 3;
      }
      desc.Subtitle = '新增股权质押';
      hasAmount = changeInfo.B && Number(changeInfo.B) !== 0;
      hasPercent = changeInfo.C && Number(changeInfo.C) !== 0;
      if (changeInfo.Type === 1 || changeInfo.Type === 2) {
        // 风险只展示1:股东（企业），2:股权被质押的企业
        if (changeInfo.HolderArray?.length) {
          tNames = [];
          tSufffix = '';
          if (changeInfo.HolderArray.length > 3) {
            changeInfo.HolderArray = changeInfo.HolderArray.slice(0, 3);
            tSufffix = '等';
          }
          changeInfo.HolderArray.forEach((item) => {
            tNames.push(item.Name);
            desc.Highlight.push({
              Id: item.KeyNo || '',
              Name: item.Name || '',
              Org: item.Org || 0,
            });
          });
          tMsg.push(`${labelStyleStart}质押人：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
        } else if (changeInfo.Type === 1 && changeInfo.Holder && changeInfo.Holder.A) {
          // 兼容老数据
          tMsg.push(`${labelStyleStart}质押人：${labelStyleEnd}${changeInfo.Holder.A}`);
          desc.Highlight.push({
            Id: changeInfo.Holder.K || '',
            Name: changeInfo.Holder.A || '',
            Org: changeInfo.Holder.O || 0,
          });
        } else if (changeInfo.Type === 2 && changeInfo.A) {
          // 兼容老数据
          tMsg.push(`${labelStyleStart}质押人：${labelStyleEnd}${changeInfo.A}`);
        }
        if (changeInfo.Company && changeInfo.Company.A) {
          tMsg.push(`${labelStyleStart}质押人参股企业：${labelStyleEnd}${changeInfo.Company.A}`);
          desc.Highlight.push({
            Id: changeInfo.Company.K || '',
            Name: changeInfo.Company.A || '',
            Org: changeInfo.Company.O || 0,
          });
        } else if (changeInfo.A) {
          tMsg.push(`${labelStyleStart}质押人参股企业：${labelStyleEnd}${changeInfo.A}`);
        }
        if (hasAmount) {
          tMsg.push(`${labelStyleStart}质押股份数量：${labelStyleEnd}${changeInfo.B}`);
        }
        tBigEventList.push(`质押股份数量：${changeInfo.B || '-'}股`);
        if (hasPercent) {
          tMsg.push(`${labelStyleStart}${changeInfo.Type === 1 ? '占所持股份比例' : '占总股本的比例'}：${labelStyleEnd}${changeInfo.C}`);
        }
        tMsg.push(`${labelStyleStart}公告日期：${labelStyleEnd}${dtChange}`);
        if (changeInfo.JgArray?.length) {
          tNames = [];
          tSufffix = '';
          if (changeInfo.JgArray.length > 3) {
            changeInfo.JgArray = changeInfo.JgArray.slice(0, 3);
            tSufffix = '等';
          }
          changeInfo.JgArray.forEach((item) => {
            tNames.push(item.Name);
            desc.Highlight.push({
              Id: item.KeyNo || '',
              Name: item.Name || '',
              Org: item.Org || 0,
            });
          });
          tMsg.push(`${labelStyleStart}质押机构：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
        } else if (changeInfo.E) {
          // 兼容老数据
          tMsg.push(`${labelStyleStart}质押机构：${labelStyleEnd}${changeInfo.E}`);
          desc.Highlight.push({
            Id: changeInfo.JgK || '',
            Name: changeInfo.E || '',
            Org: changeInfo.JgO || 0,
          });
        }
        desc.Highlight.push({
          Id: changeInfo.K || '',
          Name: changeInfo.A || '',
          Org: changeInfo.O || 0,
        });
        desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      }
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，${tBigEventList.join(' ')}`;
      break;
    case 51:
      desc.Subtitle = '新增公示催告';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}票号：${labelStyleEnd}${linkStyleStart + changeInfo.A + linkStyleEnd}`);
      }
      if (changeInfo.B && changeInfo.B.Name) {
        tMsg.push(`${labelStyleStart}申请人：${labelStyleEnd}${changeInfo.B.Name}`);
        desc.Highlight.push({
          Id: changeInfo.B.KeyNo || '',
          Name: changeInfo.B.Name || '',
          Org: changeInfo.B.Org || 0,
        });
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}票面金额：${labelStyleEnd}${changeInfo.C}`);
      }
      if (dtChange) {
        tMsg.push(`${labelStyleStart}公告日期：${labelStyleEnd}${dtChange}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，票面金额：${changeInfo.C || '-'}`;
      break;
    case 52:
      desc.Subtitle = '投资机构减少';
      desc.Content = `${labelStyleStart}减少机构：${labelStyleEnd}${changeInfo.A}`;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，减少机构：${changeInfo.A}`;
      break;
    case 53:
      desc.Subtitle = '新增担保信息';
      if (changeInfo.Secured && typeof changeInfo.Secured === 'object') {
        afterNames.push(changeInfo.Secured.A);
        desc.Highlight.push({
          Id: changeInfo.Secured.K || '',
          Name: changeInfo.Secured.A || '',
          Org: changeInfo.Secured.O || 0,
        });
      }
      if (afterNames.length) {
        tMsg.push(`${labelStyleStart}担保方：${labelStyleEnd}${afterNames.join('，') || '-'}`);
      }
      tMsg.push(`${labelStyleStart}被担保方：${labelStyleEnd}${changeInfo.D || ''}`);
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}担保金额（万元）：${labelStyleEnd}${CommonHelper.handleNum(Number(changeInfo.A))}`);
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}币种：${labelStyleEnd}${changeInfo.C}`);
      }
      tMsg.push(`${labelStyleStart}公告日期：${labelStyleEnd}${dtChange}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.D || '',
        Org: changeInfo.O || 0,
      });
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，担保金额（万元）：${changeInfo.A}`;
      break;
    case 101:
      desc.Subtitle = '新增担保信息';
      if (changeInfo.D && typeof changeInfo.D === 'object') {
        changeInfo.D.forEach((c) => {
          afterNames.push(c.Name);
          desc.Highlight.push({
            Id: c.KeyNo || '',
            Name: c.Name || '',
            Org: c.Org || 0,
          });
        });
      }
      if (afterNames.length) {
        tMsg.push(`${labelStyleStart}担保方：${labelStyleEnd}${afterNames.join('，') || '-'}`);
      }
      if (changeInfo.E && typeof changeInfo.E === 'object') {
        changeInfo.E.forEach((c) => {
          beforeNames.push(c.Name);
          desc.Highlight.push({
            Id: c.KeyNo || '',
            Name: c.Name || '',
            Org: c.Org || 0,
          });
        });
      }
      if (beforeNames.length) {
        tMsg.push(`${labelStyleStart}被担保方：${labelStyleEnd}${beforeNames.join('，') || '-'}`);
      }
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}担保金额（万元）：${labelStyleEnd}${CommonHelper.handleNum(Number(changeInfo.A))}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}币种：${labelStyleEnd}${changeInfo.B}`);
      }
      tMsg.push(`${labelStyleStart}公告日期：${labelStyleEnd}${dtChange}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，担保金额（万元）：${Number(changeInfo.A) ? CommonHelper.handleNum(Number(changeInfo.A)) : '-'}`;
      break;
    case 56:
    case 216:
      desc.Title = '司法诉讼';
      desc.Subtitle = '新增终本案件';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A });
      }
      if (changeInfo.H) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '执行标的（元）',
          v: CommonHelper.handleNum(Number(changeInfo.H)),
          showEm: Number(changeInfo.H) > 2000000,
        });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.B });
      }
      // if (changeInfo.C) {
      //   tMsg.push(`${labelStyleStart}立案日期：${labelStyleEnd}${changeInfo.C}`)
      // }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '终本日期', v: changeInfo.D });
      }
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，执行法院：${changeInfo.B}`;
      break;
    case 57:
      desc.Title = '司法风险';
      desc.Subtitle = '新增司法拍卖';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}标题：${labelStyleEnd}${linkStyleStart + changeInfo.A + linkStyleEnd}`);
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}起拍价（元）：${labelStyleEnd}${CommonHelper.handleNum(Number(changeInfo.C))}`);
      }
      if (changeInfo.H) {
        tMsg.push(`${labelStyleStart}拍卖时间：${labelStyleEnd}${changeInfo.H}`);
      } else if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}拍卖时间：${labelStyleEnd}${changeInfo.D}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，${changeInfo.A}`;
      break;
    case 58:
      desc.Title = '经营风险';
      desc.Subtitle = '新增破产重整';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}案号：${labelStyleEnd}${changeInfo.A}`);
      }
      beforeNames.push(...(find(shared.BANKRUPTCY_MAPPING, (it) => includes(it.Key, changeInfo.E))?.Value ?? ['申请人', '被申请人']));
      if (changeInfo.C && changeInfo.C.length) {
        changeInfo.C.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        tMsg.push(`${labelStyleStart}${beforeNames[1]}：${labelStyleEnd}${tNames.join('、')}`);
      }
      if (changeInfo.B && changeInfo.B.length) {
        changeInfo.B.forEach((item) => {
          tNames2.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        tMsg.push(`${labelStyleStart}${beforeNames[0]}：${labelStyleEnd}${tNames2.join('、')}`);
      }
      if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}公开日期：${labelStyleEnd}${changeInfo.D}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，申请人：${tNames2.join('、')}`;
      break;
    case 59:
    case 224:
      desc.Title = '经营风险';
      desc.Subtitle = '有资产被询价评估';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}案号：${labelStyleEnd}${changeInfo.A}`);
      }
      if (changeInfo.F && changeInfo.F.length) {
        tNames = [];
        tSufffix = '';
        if (changeInfo.F.length > 3) {
          changeInfo.F = changeInfo.F.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.F.forEach((item) => {
          tNames.push(item.Target);
        });
        tMsg.push(`${labelStyleStart}标的物：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
        tBigEventList.push(`标的物：${tNames.join('、')}`);
      }
      if (changeInfo.G && changeInfo.G.length) {
        tNames = changeInfo.G.sort((a, b) => parseFloat(a.EvaluationPrice) - parseFloat(b.EvaluationPrice));
        if (tNames.length > 1) {
          tMsg.push(`${labelStyleStart}询价结果（元）：${labelStyleEnd}${`${tNames[0].EvaluationPrice} ~ ${tNames[tNames.length - 1].EvaluationPrice}`}`);
        } else {
          tMsg.push(`${labelStyleStart}询价结果（元）：${labelStyleEnd}${tNames.length ? tNames[0].EvaluationPrice : ''}`);
        }
      }
      if (changeInfo.H && changeInfo.H.length) {
        tNames = [];
        tSufffix = '';
        if (changeInfo.H.length > 3) {
          changeInfo.H = changeInfo.H.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.H.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        tMsg.push(`${labelStyleStart}标的物所有人：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
      }
      if (changeInfo.I && changeInfo.I.length) {
        tNames = [];
        tSufffix = '';
        if (changeInfo.I.length > 3) {
          changeInfo.I = changeInfo.I.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.I.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        tMsg.push(`${labelStyleStart}关联对象：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
      }
      if (changeInfo.J) {
        tMsg.push(`${labelStyleStart}发布日期：${labelStyleEnd}${moment.unix(changeInfo.J).format('YYYY-MM-DD')}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，${tBigEventList.join(' ')}`;
      break;
    case 76:
    case 228:
      desc.Title = '司法风险';
      desc.Subtitle = '有资产选定询价评估机构';
      tMsg.push(`${labelStyleStart}案号：${labelStyleEnd}${changeInfo.A}`);
      tMsg.push(`${labelStyleStart}标的物：${labelStyleEnd}${changeInfo.B}`);
      if (changeInfo.C && changeInfo.C.length > 0) {
        tSufffix = '';
        tNames = [];
        if (changeInfo.C.length > 3) {
          changeInfo.C = changeInfo.C.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.C.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        tMsg.push(`${labelStyleStart}标的物所有人：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
      }
      tMsg.push(`${labelStyleStart}摇号日期：${labelStyleEnd}${moment.unix(changeInfo.D).format('YYYY-MM-DD')}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 60:
      desc.Title = '工商变更';
      desc.Subtitle = '企业名称变更';
      desc.Content = `从“${
        useH5Style ? riskHelper.changeEmToRedStyle(changeInfo.C) || changeInfo.A || '-' : changeInfo.A || '-'
      }”${lineBreak}变更为${lineBreak}“${useH5Style ? riskHelper.changeEmToRedStyle(changeInfo.D) || changeInfo.B || '-' : changeInfo.B || '-'}”`;
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：从${changeInfo.A || '-'}变更为${changeInfo.B || '-'}`;
      break;
    case 61:
      desc.Title = '经营风险';
      desc.Subtitle = '新增注销备案';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}登记机关：${labelStyleEnd}${changeInfo.A}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}清算组备案日期：${labelStyleEnd}${changeInfo.B}`);
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}清算组成立日期：${labelStyleEnd}${changeInfo.C}`);
      }
      if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}注销原因：${labelStyleEnd}${changeInfo.D}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，登记机关：${changeInfo.A || '-'}`;
      break;
    case 63:
      desc.Title = '经营状况';
      desc.Subtitle = '新增双随机抽查';
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}任务编号：${labelStyleEnd}${linkStyleStart + changeInfo.C + linkStyleEnd}`);
      }
      if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}任务名称：${labelStyleEnd}${changeInfo.D}`);
      }
      if (changeInfo.F) {
        tMsg.push(`${labelStyleStart}抽查机关：${labelStyleEnd}${changeInfo.F}`);
      }
      if (changeInfo.G) {
        tMsg.push(`${labelStyleStart}完成日期：${labelStyleEnd}${changeInfo.G}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：${changeInfo.D || '-'}`;
      break;
    case 95:
      desc.Title = '经营状况';
      desc.Subtitle = '新增行政许可';
      riskHelper.pushContentKV({ contents: tMsg, k: '许可名称', v: changeInfo.B || '-' });
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '许可机关', v: changeInfo.C });
      }
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '许可内容',
        v: (startsWith(changeInfo.D, '许可项目') ? changeInfo.D.substring(5, changeInfo.D.length) : changeInfo.D) || '-',
        line: 3,
      });
      if (changeInfo.F || changeInfo.G) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '有效期',
          v: `${(changeInfo.F && moment(changeInfo.F, 'YYYYMMDD').format('YYYY-MM-DD')) || '-'}至${
            (changeInfo.G && moment(changeInfo.G, 'YYYYMMDD').format('YYYY-MM-DD')) || '-'
          }`,
        });
      }
      // 将文案拼接等规则放入公共方法调用
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：${
        (startsWith(changeInfo.D, '许可项目：') ? changeInfo.D.substring(5, changeInfo.D.length) : changeInfo.D) || '-'
      }`;
      break;
    case 65:
    case 113:
      desc.Title = '经营状况';
      desc.Subtitle = '新增企业公告';
      if (changeInfo.Title) {
        tMsg.push(`${labelStyleStart}公告名称：${labelStyleEnd}${linkStyleStart + changeInfo.Title + linkStyleEnd}`);
      }
      if (
        split(changeInfo.TS, ',')
          .map((t) => shared.ANNOUNCEMENT_REPORT_TYPE.get(Number(t)))
          .filter((t) => t).length
      ) {
        tMsg.push(
          `${labelStyleStart}公告类型：${labelStyleEnd}${split(changeInfo.TS, ',')
            .map((t) => shared.ANNOUNCEMENT_REPORT_TYPE.get(Number(t)))
            .filter((t) => t)
            .join('、')}`,
        );
      }
      if (changeInfo.Publishtime) {
        tMsg.push(`${labelStyleStart}公告日期：${labelStyleEnd}${moment.unix(changeInfo.Publishtime).format('YYYY-MM-DD')}`);
      }
      // 特殊处理，如果url是制定结尾，清除
      if (changeInfo.url && some(['.link', '.htm', '.html', '.rar', '.zip', '.txt'], (ext) => endsWith(changeInfo.url, ext))) {
        // 清除
        changeInfo.url = '';
        item.ChangeExtend = JSON.stringify(changeInfo);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${changeInfo.Title}`;
      break;
    case 62:
    case 66:
    case 67:
      desc.Subtitle = '新闻';
      desc.Content = `${labelStyleStartOneLine}${changeInfo.links ? changeInfo.links[0].title : ''}${labelStyleEnd}`;
      desc.IsHotNews = false;
      if (some(changeInfo.eventArray, 'isSelf')) {
        desc.IsHotNews = true;
      }
      desc['BigEventDesc'] = `${dtCreate} ${changeInfo.links ? changeInfo.links[0].title : ''}`;
      break;
    case 72:
      // 公司： 39-企业法人，44-非大股东变动，46-主要成员；72-成员变更  三合一
      desc.Subtitle = riskHelper.getSubtitle72(changeInfo, item.Category);
      contentInfo = riskHelper.getContent72(changeInfo, useH5Style, item);
      desc.Content = prefixLineBreak;
      forEach(contentInfo.tMsg, (v, i) => {
        if (v === 'br') {
          desc.Content += '<div style="height:10px"></div>';
        } else if (i !== contentInfo.tMsg.length - 1) {
          desc.Content += v + lineBreak;
        } else {
          desc.Content += v;
        }
      });
      // desc.Content = prefixLineBreak + contentInfo.tMsg.join(lineBreak)
      desc['BigEventDesc'] = `${dtCreate} ${contentInfo.bigEventDesc.join('，')}`;
      desc.Highlight = contentInfo.highlight;
      break;
    case 75:
      desc.Title = '经营风险';
      desc.Subtitle = '新增资产拍卖';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}标题：${labelStyleEnd}${linkStyleStart + changeInfo.A + linkStyleEnd}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}起拍价（元）：${labelStyleEnd}${changeInfo.B}`);
      }
      if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}竞拍时间：${labelStyleEnd}${changeInfo.D}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：${changeInfo.A || '-'}`;
      break;
    case 77:
      desc.Title = '监管风险';
      desc.Subtitle = '被列入黑名单';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}${item.Extend1 === '3' ? '类型' : '标题'}：${labelStyleEnd}${changeInfo.A}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}列入机关：${labelStyleEnd}${changeInfo.B}`);
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}列入日期：${labelStyleEnd}${changeInfo.C}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：${changeInfo.A || '-'}`;
      break;
    case 78:
      desc.Title = '监管风险';
      desc.Subtitle = '新增产品召回';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}召回产品：${labelStyleEnd}${linkStyleStart + changeInfo.A + linkStyleEnd}`);
      }
      if (changeInfo.D && changeInfo.D.length > 0) {
        tSufffix = '';
        tNames = [];
        if (changeInfo.D.length > 3) {
          changeInfo.D = changeInfo.D.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.D.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        tMsg.push(`${labelStyleStart}召回企业：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}发布日期：${labelStyleEnd}${moment.unix(changeInfo.C).format('YYYY-MM-DD')}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，召回产品：${changeInfo.A || '-'}`;
      break;
    case 79:
      desc.Title = '监管风险';
      desc.Subtitle = '食品抽检不合格';
      if (changeInfo.H === 1) {
        desc.Subtitle = '食品抽检合格';
      }
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}食品名称：${labelStyleEnd}${linkStyleStart + changeInfo.A + linkStyleEnd}`);
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}抽检结果：${labelStyleEnd}${changeInfo.H === 1 ? '合格' : '不合格'}`);
      }
      // if (changeInfo.F && changeInfo.F.length > 0) {
      //   tSufffix = ''
      //   tNames = []
      //   if (changeInfo.F.length > 3) {
      //     changeInfo.F = changeInfo.F.slice(0, 3)
      //     tSufffix = '等'
      //   }
      //   changeInfo.F.forEach(item => {
      //     tNames.push(item.Name)
      //     desc.Highlight.push({
      //       Id: item.KeyNo || '',
      //       Name: item.Name || '',
      //       Org: item.Org || 0
      //     })
      //   })
      //   tMsg.push(`${labelStyleStart}被抽检企业：${labelStyleEnd}${tNames.join('、')}${tSufffix}`)
      // }
      if (changeInfo.G && changeInfo.G.length > 0) {
        tSufffix = '';
        tNames = [];
        if (changeInfo.G.length > 3) {
          changeInfo.G = changeInfo.G.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.G.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        tMsg.push(`${labelStyleStart}标称生产企业：${labelStyleEnd}${tNames.join('、')}${tSufffix}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}生产日期：${labelStyleEnd}${changeInfo.B}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，食品名称：${changeInfo.A || '-'}`;
      break;
    case 98:
      desc.Title = '监管风险';
      desc.Subtitle = '相关产品被禁止入境';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}产品名称：${labelStyleEnd}${linkStyleStart + changeInfo.A + linkStyleEnd}`);
      }
      if (changeInfo.B) {
        tMsg.push(`${labelStyleStart}产品类型：${labelStyleEnd}${PRODUCT_TYPE_MAP.get(changeInfo.B)}`);
      }
      if (changeInfo.C && changeInfo.C !== '/') {
        tMsg.push(`${labelStyleStart}生产企业信息/品牌：${labelStyleEnd}${changeInfo.C}`);
      }
      if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}原因：${labelStyleEnd}${changeInfo.D}`);
      }
      if (changeInfo.E) {
        tMsg.push(`${labelStyleStart}报送时间：${labelStyleEnd}${moment(changeInfo.E, 'YYYYMM').format('YYYY年MM月')}`);
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，产品名称：${changeInfo.A || '-'}`;
      break;
    case 222:
      // 人员：201-企业法人，202-主要成员，203-对外投资；222-成员变更  三合一
      desc.Subtitle = riskHelper.getSubtitle72(changeInfo, item.Category);
      contentInfo = riskHelper.getContent222(changeInfo, item, useH5Style);
      desc.Content = prefixLineBreak;
      forEach(contentInfo.tMsg, (b) => {
        if (b === 'br') {
          desc.Content += '<div style="height:10px"></div>';
        } else {
          desc.Content += b + lineBreak;
        }
      });
      // desc.Content = prefixLineBreak + contentInfo.tMsg.join(lineBreak)
      desc.RelateChange = contentInfo.relateChange;
      desc['BigEventDesc'] = `${dtCreate} ${contentInfo.bigEventDesc.join('，')}`;
      desc.Highlight = contentInfo.highlight;
      break;

    // 人员
    case 201:
      desc.Subtitle = `${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}变更`;
      if (item.ChangeStatus === 1) {
        // 新增
        desc.Content = `${labelStyleStartOneLine}担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}${labelStyleEnd}`;
        desc.HotDynamicDesc = `近期成为一家${Number(changeInfo.C) === 4 ? '个体工商户' : '公司'}的<em>${
          shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'
        }</em>`;
        tBigEventList.push(`担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}`);
      }
      if (item.ChangeStatus === 2) {
        // 退出
        desc.Content = `${labelStyleStartOneLine}不再担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}${labelStyleEnd}`;
        desc.HotDynamicDesc = `近期不再是一家${Number(changeInfo.C) === 4 ? '个体工商户' : '公司'}的<em>${
          shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'
        }</em>`;
        tBigEventList.push(`不再担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}`);
      }
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.A || '',
        Org: changeInfo.O || 0,
      });
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：${tBigEventList.join(' ')}`;
      break;
    case 202:
      if (item.ChangeStatus === 1) {
        // 新增
        desc.Subtitle = '新增主要成员';
        if (changeInfo.A) {
          tMsg.push(`${labelStyleStart}新增企业：${labelStyleEnd}${changeInfo.A}`);
        }
        desc.HotDynamicDesc = '近期在一家公司任职';
        if (afterContent) {
          tMsg.push(`${labelStyleStart}担任职务：${labelStyleEnd}${afterContent}`);
          desc.HotDynamicDesc = `近期在一家公司担任<em>${afterContent}</em>`;
        }
      }
      if (item.ChangeStatus === 2) {
        // 退出
        desc.Subtitle = '退出主要成员';
        if (changeInfo.A) {
          tMsg.push(`${labelStyleStart}退出企业：${labelStyleEnd}${changeInfo.A}`);
        }
        desc.HotDynamicDesc = '近期在一家公司卸职';
        if (beforeContent) {
          tMsg.push(`${labelStyleStart}退出前职务：${labelStyleEnd}${beforeContent}`);
          desc.HotDynamicDesc = `近期在一家公司卸任<em>${beforeContent}</em>`;
        }
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${CommonHelper.replaceAll(CommonHelper.replaceAll(tMsg.join('，'), labelStyleStart, ''), labelStyleEnd, '')}`;
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.A || '',
        Org: changeInfo.O || 0,
      });
      break;
    case 68:
    case 204:
      if (item.ChangeStatus === 1) {
        desc.Subtitle = '持股比例上升';
        tMsg.push(`${labelStyleStartOneLine}在${changeInfo.A}的股份比例从${changeInfo.B}增加到${changeInfo.C}${labelStyleEnd}`);
        const holdValue = changeInfo.C ? Number(changeInfo.C.slice(0, -1)) : 0;
        if (holdValue <= 50 && changeInfo?.IsBP === '1') {
          tMsg.push(`${labelStyleStart}成为该企业的大股东${labelStyleEnd}`);
          desc.RelateChange = '成为该企业的大股东';
        }
        desc.HotDynamicDesc = `近期在一家公司的股份比例上升至<em>${changeInfo.C}</em>`;
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：在${changeInfo.A}的股份比例从${changeInfo.B}增加到${changeInfo.C}`;
      } else {
        desc.Subtitle = '持股比例下降';
        tMsg.push(`${labelStyleStartOneLine}在${changeInfo.A}的股份比例从${changeInfo.B}下降为${changeInfo.C}${labelStyleEnd}`);
        const beforeHoldValue = changeInfo.B ? Number(changeInfo.B.slice(0, -1)) : 0;
        const afterHoldValue = changeInfo.C ? Number(changeInfo.C.slice(0, -1)) : 0;
        if (!(beforeHoldValue > 50 && afterHoldValue < 50) && changeInfo?.IsBP === '2') {
          tMsg.push(`${labelStyleStart}不再是该企业的大股东${labelStyleEnd}`);
          desc.RelateChange = '不再是该企业的大股东';
        }
        desc.HotDynamicDesc = `近期在一家公司的股份比例下降至<em>${changeInfo.C}</em>`;
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：在${changeInfo.A}的股份比例从${changeInfo.B}下降为${changeInfo.C}`;
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.A || '',
        Org: changeInfo.O || 0,
      });
      break;
    case 89:
      desc.Title = '经营风险';
      if (item.ChangeStatus === 1) {
        desc.Subtitle = '成为大股东';
        desc.Content = `${labelStyleStartOneLine}成为${changeInfo.Name}的大股东${labelStyleEnd}`;
      } else if (item.ChangeStatus === 2) {
        desc.Subtitle = '不再是大股东';
        desc.Content = `${labelStyleStartOneLine}不再是${changeInfo.Name}的大股东${labelStyleEnd}`;
      }
      desc.Highlight.push({
        Id: changeInfo.KeyNo,
        Name: changeInfo.Name,
        Org: changeInfo.Org,
      });
      if (item.ChangeStatus === 1) {
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：成为${changeInfo.Name}的大股东`;
      } else {
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：不再是${changeInfo.Name}的大股东`;
      }
      break;
    case 206:
      desc.Subtitle = '被列入被执行人';
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '执行标的（元）',
          v: CommonHelper.handleNum(Number(changeInfo.B)),
          showEm: changeInfo.B > 2000000,
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '立案日期', v: dtChange || '-' });
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，执行标的（元）：${changeInfo.B || '-'}`;
      } else {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.D || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '立案日期', v: dtChange || '-' });
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，案号：${changeInfo.A || '-'}`;
      }
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.HotDynamicDesc = '近期被列为<em>被执行人</em>';
      break;
    case 55:
    case 208:
      desc.Subtitle = '被限制高消费';
      contentInfo = riskHelper.getContent55And208(changeInfo, useH5Style);
      desc.Content = prefixLineBreak + contentInfo.tMsg.join(lineBreak);
      desc.Highlight = contentInfo.highlight;
      desc.HotDynamicDesc = '近期被列为<em>限制高消费</em>';
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 91:
    case 231:
      desc.Subtitle = '被限制出境';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}案号：${labelStyleEnd}${changeInfo.A}`);
      }
      if (changeInfo.B && changeInfo.B.length) {
        tMsg.push(`${labelStyleStart}限制出境对象：${labelStyleEnd}${changeInfo.B.map((b) => b.Name).join('，')}`);
        changeInfo.B.forEach((b) => {
          desc.Highlight.push({
            Id: b.KeyNo || '',
            Name: b.Name || '',
            Org: b.Org || 0,
          });
        });
      }
      if (changeInfo.C && changeInfo.C.length) {
        tMsg.push(`${labelStyleStart}被执行人：${labelStyleEnd}${changeInfo.C.map((c) => c.Name).join('，')}`);
        changeInfo.C.forEach((c) => {
          desc.Highlight.push({
            Id: c.KeyNo || '',
            Name: c.Name || '',
            Org: c.Org || 0,
          });
        });
      }
      // if (changeInfo.D && changeInfo.D.length) {
      //   tMsg.push(`${labelStyleStart}申请执行人：${labelStyleEnd}${changeInfo.D.map(d => {
      //     return useEncryName ? (d.ShowName || d.Name) : d.Name
      //   }).join('，')}`)
      //   changeInfo.D.forEach(d => {
      //     desc.Highlight.push({
      //       Id: d.KeyNo || '',
      //       Name: d.Name || '',
      //       Org: d.Org || 0
      //     })
      //   })
      // }
      if (changeInfo.E) {
        tMsg.push(`${labelStyleStart}执行标的（元）：${labelStyleEnd}${CommonHelper.handleNum(Number(changeInfo.E))}`);
      }
      if (changeInfo.F) {
        tMsg.push(`${labelStyleStart}发布日期：${labelStyleEnd}${changeInfo.F}`);
      }
      // tMsg.push(`${labelStyleStart}做出决定法院：${labelStyleEnd}${changeInfo.B}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 90:
    case 232:
      desc.Subtitle = '新增诉前调解';
      if (changeInfo.E) {
        tMsg.push(`${labelStyleStart}案号：${labelStyleEnd}${linkStyleStart + changeInfo.E + linkStyleEnd}`);
      }
      if (changeInfo.I?.length) {
        changeInfo.I.forEach((b) => {
          desc.Highlight.push({
            Id: b.KeyNo || '',
            Name: b.Name || '',
            Org: b.Org || 0,
          });
        });
        tMsg.push(
          `${
            CommonHelper.processCaseRole({ field: changeInfo.I, lineBreak, useEncryName, useH5Style, item }) ||
            labelStyleStart + '当事人：' + labelStyleEnd + '-'
          }`,
        );
      }
      if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}案由：${labelStyleEnd}${changeInfo.D}`);
      }
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}立案日期：${labelStyleEnd}${moment.unix(changeInfo.A).format('YYYY-MM-DD')}`);
      }
      // tMsg.push(`${labelStyleStart}做出决定法院：${labelStyleEnd}${changeInfo.B}`);
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}，案由：${changeInfo.D || '-'}`;
      break;
    case 86:
      desc.Subtitle = '新增知识产权出质';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}名称：${labelStyleEnd}${changeInfo.A}`);
      }
      if (changeInfo.I) {
        tMsg.push(`${labelStyleStart}出质登记号：${labelStyleEnd}${changeInfo.I}`);
      }
      if (changeInfo.F) {
        tMsg.push(`${labelStyleStart}出质知产类型：${labelStyleEnd}${changeInfo.F === 1 ? '专利' : '商标'}`);
      }
      if (changeInfo.B && changeInfo.B.length) {
        changeInfo.B = JSON.parse(changeInfo.B);
        tMsg.push(`${labelStyleStart}出质人：${labelStyleEnd}${changeInfo.B.map((b) => b.Name).join('，') || '-'}`);
        changeInfo.B.forEach((b) => {
          desc.Highlight.push({
            Id: b.KeyNo || '',
            Name: b.Name || '',
            Org: b.Org || 0,
          });
        });
      }
      if (changeInfo.C && changeInfo.C.length) {
        changeInfo.C = JSON.parse(changeInfo.C);
        tMsg.push(`${labelStyleStart}质权人：${labelStyleEnd}${changeInfo.C.map((c) => c.Name).join('，') || '-'}`);
        changeInfo.C.forEach((c) => {
          desc.Highlight.push({
            Id: c.KeyNo || '',
            Name: c.Name || '',
            Org: c.Org || 0,
          });
        });
      }
      tMsg.push(`${labelStyleStart}公告日期：${labelStyleEnd}${dtChange}`);
      if (changeInfo.D) {
        tMsg.push(
          `${labelStyleStart}出质期限：${labelStyleEnd}${moment.unix(changeInfo.D).format('YYYY-MM-DD')} 至 ${
            (changeInfo.E && moment.unix(changeInfo.E).format('YYYY-MM-DD')) || '-'
          }`,
        );
      }
      // tMsg.push(`${labelStyleStart}做出决定法院：${labelStyleEnd}${changeInfo.B}`);
      if (changeInfo.G) {
        desc['IPRPledgeId'] = changeInfo.G;
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 209:
      desc.Subtitle = '实际控制人变更';
      if (item.ChangeStatus === 1) {
        desc.Content = `${labelStyleStartOneLine}成为${changeInfo.Name}的实际控制人${labelStyleEnd}`;
        desc.HotDynamicDesc = '近期成为一家公司的<em>实际控制人</em>';
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：成为${changeInfo.Name}的实际控制人`;
      }
      if (item.ChangeStatus === 2) {
        desc.Content = `${labelStyleStartOneLine}不再是${changeInfo.Name}的实际控制人${labelStyleEnd}`;
        desc.HotDynamicDesc = '近期不再是一家公司的<em>实际控制人</em>';
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：不再是${changeInfo.Name}的实际控制人`;
      }
      desc.Highlight.push({
        Id: changeInfo.KeyNo || '',
        Name: changeInfo.Name || '',
        Org: changeInfo.Org || 0,
      });
      break;
    case 240:
      desc.Subtitle = '实际控制人变更';
      forEach(changeInfo, (item) => {
        extend1 = JSON.parse(item.ChangeExtend);
        if (item.ChangeStatus === 1) {
          tMsg.push(`成为${extend1.Name}的实际控制人`);
          if (changeInfo.length === 1) {
            desc.HotDynamicDesc = '近期成为一家公司的<em>实际控制人</em>';
            desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：成为${extend1.Name}的实际控制人`;
          }
        }
        if (item.ChangeStatus === 2) {
          tMsg.push(`不再是${extend1.Name}的实际控制人`);
          if (changeInfo.length === 1) {
            desc.HotDynamicDesc = '近期不再是一家公司的<em>实际控制人</em>';
            desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：不再是${extend1.Name}的实际控制人`;
          }
        }
        tNames.push(extend1.Name);
        desc.Highlight.push({
          Id: extend1.KeyNo || '',
          Name: extend1.Name || '',
          Org: extend1.Org || 0,
        });
        t = extend1.Name;
      });
      if (changeInfo.length > 1) {
        desc.HotDynamicDesc = '近期发生<em>实际控制人</em>变更';
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：涉及${t}等实际控制人变更`;
      }
      if (tNames.length > 3) {
        desc.Content =
          prefixLineBreak + `${changeInfo[0].ChangeStatus === 1 ? '成为' : '不再是'}${tNames.slice(0, 3).join('、')}等${changeInfo.length}家企业的实际控制人`;
        desc.Highlight = desc.Highlight.slice(0, 3);
      } else {
        desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      }
      break;
    case 210:
      desc.Subtitle = '受益所有人变更';
      if (item.ChangeStatus === 1) {
        desc.Content = `${labelStyleStartOneLine}成为${changeInfo.Name}的受益所有人${labelStyleEnd}`;
        desc.HotDynamicDesc = '近期成为一家公司的<em>受益所有人</em>';
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：成为${changeInfo.Name}的受益所有人`;
      }
      if (item.ChangeStatus === 2) {
        tMsg.push(`不再是${changeInfo.Name}的受益所有人`);
        if (changeInfo.PercentTotal) {
          tMsg.push(`${labelStyleStart}变更前受益股份：${labelStyleEnd}${changeInfo.PercentTotal}`);
        }
        desc.Content = prefixLineBreak + tMsg.join(lineBreak);
        desc.HotDynamicDesc = '近期不再是一家公司的<em>受益所有人</em>';
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：不再是${changeInfo.Name}的受益所有人`;
      }
      desc.Highlight.push({
        Id: changeInfo.KeyNo || '',
        Name: changeInfo.Name || '',
        Org: changeInfo.Org || 0,
      });
      break;
    case 241:
      desc.Subtitle = '受益所有人变更';
      // 生成文案和高亮
      forEach(changeInfo, (item) => {
        extend1 = JSON.parse(item.ChangeExtend);
        if (item.ChangeStatus === 1) {
          tMsg.push(`成为${extend1.Name}的受益所有人`);
          if (changeInfo.length === 1) {
            desc.HotDynamicDesc = '近期成为一家公司的<em>受益所有人</em>';
            desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：成为${extend1.Name}的受益所有人`;
          }
        }
        if (item.ChangeStatus === 2) {
          tMsg.push(`不再是${extend1.Name}的受益所有人`);
          if (extend1.PercentTotal) {
            tMsg.push(`${labelStyleStart}变更前受益股份：${labelStyleEnd}${extend1.PercentTotal}`);
          }
          if (changeInfo.length === 1) {
            desc.HotDynamicDesc = '近期不再是一家公司的<em>受益所有人</em>';
            desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：不再是${extend1.Name}的受益所有人`;
          }
        }
        tNames.push(extend1.Name);
        desc.Highlight.push({
          Id: extend1.KeyNo || '',
          Name: extend1.Name || '',
          Org: extend1.Org || 0,
        });
        t = extend1.Name;
      });
      if (changeInfo.length > 1) {
        desc.HotDynamicDesc = '近期发生<em>受益所有人</em>变更';
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：涉及${t}等受益所有人变更`;
      }
      if (tNames.length > 3) {
        desc.Content =
          prefixLineBreak + `${changeInfo[0].ChangeStatus === 1 ? '成为' : '不再是'}${tNames.slice(0, 3).join('、')}等${changeInfo.length}家企业的受益所有人`;
        desc.Highlight = desc.Highlight.slice(0, 3);
      } else {
        desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      }
      break;
    case 215:
      if (item.ChangeStatus === 1) {
        desc.Subtitle = '成为大股东';
        desc.Content = `${labelStyleStartOneLine}成为${changeInfo.Name}的大股东${labelStyleEnd}`;
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：成为${changeInfo.Name}的大股东`;
      }
      if (item.ChangeStatus === 2) {
        desc.Subtitle = '不再是大股东';
        desc.Content = `${labelStyleStartOneLine}不再是${changeInfo.Name}的大股东${labelStyleEnd}`;
        desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}：不再是${changeInfo.Name}的大股东`;
      }
      desc.Highlight.push({
        Id: changeInfo.KeyNo || '',
        Name: changeInfo.Name || '',
        Org: changeInfo.O || 0,
      });
      break;
    case 108:
      desc.Subtitle = '新增票据违约';
      if (changeInfo.compName) {
        tMsg.push(`${labelStyleStart}承兑人：${labelStyleEnd}${changeInfo.compName}`);
      }
      if (changeInfo.overdueBalance) {
        tMsg.push(`${labelStyleStart}逾期余额（万元）：${labelStyleEnd}${CommonHelper.div(changeInfo.overdueBalance, 10000)}`);
      }
      if (changeInfo.endDate) {
        tMsg.push(`${labelStyleStart}截止日期：${labelStyleEnd}${moment.unix(changeInfo.endDate).format('YYYY-MM-DD')}`);
      }
      desc.Highlight.push({
        Id: changeInfo?.map?.KeyNo || '',
        Name: changeInfo?.map?.Name || '',
        Org: changeInfo?.map?.Org || 0,
      });
      if (changeInfo.overdueBalance) {
        desc['BigEventDesc'] = `${dtCreate} 该企业存在${CommonHelper.div(changeInfo.overdueBalance, 10000)}万元的票据违约`;
      } else {
        desc['BigEventDesc'] = `${dtCreate} 该企业存在票据违约`;
      }
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      break;
    case 109:
    case 239:
      desc.Subtitle = '新增公安通告';
      if (changeInfo.A) {
        tMsg.push(`${labelStyleStart}涉案企业：${labelStyleEnd}${changeInfo.A}`);
      }
      if (changeInfo.H) {
        tMsg.push(`${labelStyleStart}涉嫌案由：${labelStyleEnd}${changeInfo.H.replaceAll('|', '、')}`);
      }
      if (changeInfo.D) {
        tMsg.push(`${labelStyleStart}发布单位：${labelStyleEnd}${changeInfo.D}`);
      }
      if (changeInfo.C) {
        tMsg.push(`${labelStyleStart}发布日期：${labelStyleEnd}${moment(changeInfo.C, 'YYYYMMDD').format('YYYY-MM-DD')}`);
      }
      desc.Highlight.push({
        Id: changeInfo?.I?.KeyNo || '',
        Name: changeInfo?.I?.Name || '',
        Org: changeInfo?.I?.Org || 0,
      });
      desc['BigEventDesc'] = `${dtCreate} 该企业存在${changeInfo.H}的相关公告`;
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      break;
    case 110:
      desc.Subtitle = '新增债券违约';
      if (changeInfo.sname) {
        riskHelper.pushContentKV({ contents: tMsg, k: '债券简称', v: changeInfo.sname });
      }
      if (CommonHelper.isNotEmpty(changeInfo.newStatus) && shared.BondDefaultStatusMap.get(changeInfo.newStatus)) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '违约状态',
          v: shared.BondDefaultStatusMap.get(changeInfo.newStatus),
        });
      }
      if (changeInfo.overdueCapital && Number(changeInfo.overdueCapital)) {
        riskHelper.pushContentKV({ contents: tMsg, k: '累计违约本金（亿元）', v: changeInfo.overdueCapital });
      }
      if (changeInfo.overdueInterest && Number(changeInfo.overdueInterest)) {
        riskHelper.pushContentKV({ contents: tMsg, k: '累计违约利息（亿元）', v: changeInfo.overdueInterest });
      }
      if (changeInfo.maturityDate) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '到期日期',
          v: moment.unix(changeInfo.maturityDate).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} 新增债券违约${
        changeInfo.overdueCapital && Number(changeInfo.overdueCapital) ? '，累计违约本金（亿元）：' + changeInfo.overdueCapital : ''
      }`;
      break;
    case 116:
      desc.Subtitle = '登记机关变更';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '变更前登记机关', v: changeInfo.A });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '变更后登记机关', v: changeInfo.B });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '核准日期',
          v: moment.unix(changeInfo.C).format('YYYY-MM-DD'),
        });
      }
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} 变更登记机关为：${changeInfo.B || '-'}`;
      break;
    case 69:
      desc.Subtitle = '新增商标信息';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '申请/注册号', v: changeInfo.A });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '商标名称', v: changeInfo.C, isLink: true });
      }
      if (changeInfo.E) {
        let value;
        if (changeInfo.B) {
          value = `${changeInfo.B}类 ${changeInfo.E}`;
        } else {
          value = changeInfo.E;
        }
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '国际分类',
          v: value,
        });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '申请日期', v: dtChange });
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} 新增商标，${changeInfo.C || '-'}`;
      break;
    case 70:
      desc.Subtitle = '新增专利信息';
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '申请号', v: changeInfo.C });
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '发明名称', v: changeInfo.A, isLink: true });
      }
      // if (changeInfo.G) {
      //   riskHelper.pushContentKV({ contents: tMsg, k: '专利类型', v: changeInfo.G })
      // }
      if (changeInfo.F) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '申请日期',
          v: moment.unix(changeInfo.F).format('YYYY-MM-DD'),
        });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '公开日期',
          v: moment.unix(changeInfo.E).format('YYYY-MM-DD'),
        });
      }
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc['BigEventDesc'] = `${dtCreate} 新增专利，${changeInfo.A || '-'}`;
      break;
    case 121:
    case 242:
      desc.Subtitle = '新增监管处罚';
      riskHelper.pushContentKV({ contents: tMsg, k: '决定文书号', v: changeInfo.A || '-', isLink: true });
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '违规事实', v: changeInfo.B, line: 3 });
      } else if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '处理结果', v: changeInfo.D, line: 3 });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '处理单位', v: changeInfo.F });
      }
      if (changeInfo.G) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '处理日期',
          v: moment.unix(changeInfo.G).format('YYYY-MM-DD'),
        });
      }
      // 将文案拼接等规则放入公共方法调用
      desc['ContentArray'] = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      if (changeInfo.B) {
        // 主页滚动文案优先级展示
        extend = `，违规事实：${changeInfo.B}`;
      } else if (changeInfo.D) {
        extend = `，处理结果：${changeInfo.D}`;
      }
      desc['BigEventDesc'] = `${dtCreate} ${desc.Subtitle}${extend || ''}`;
      break;
    case 9999:
      desc.Title = '工商变更';
      desc.Subtitle = '发生工商信息变更';
      if (item.MergeList.length > 3) {
        tMsg.push(
          `${labelStyleStartOneLine}${item.MergeList.slice(0, 3).join('、')}${labelStyleEnd}等${labelStyleStartOneLine}${
            item.MergeList.length
          }${labelStyleEnd}类`,
        );
      } else {
        tMsg.push(`${labelStyleStartOneLine}${item.MergeList.join('、')}${labelStyleEnd}`);
      }
      delete item.MergeList;
      desc.Content = prefixLineBreak + tMsg.join(lineBreak);
      break;
    default:
  }

  return desc;
};

export default {
  getRiskListDesc,
};

export { getRiskListDesc };
