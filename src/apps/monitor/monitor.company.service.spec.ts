import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager, getRepository, Repository, In } from 'typeorm';
import { MonitorCompanyService } from './monitor.company.service';
import { MonitorCertificationEntity } from '../../libs/entities/MonitorCertificationEntity';
import { UserService } from '../user/user.service';
import { MonitorGroupCompanyEntity } from '../../libs/entities/MonitorGroupCompanyEntity';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { difference } from 'lodash';
import { RoverUser } from '../../libs/model/common';
import { generateUniqueTestIds, getTestUser, UserTestUtils } from '../test_utils_module/test.user';
import { BundleTestUtils } from '../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { MonitorGroupService } from './monitor.group.service';
import { AddGroupRequest } from '../../libs/model/monitor/request/AddGroupRequest';
import { UpdateCompanyRequest } from '../../libs/model/monitor/request/UpdateCompanyRequest';
import { SearchCompanyRequest } from '../../libs/model/monitor/request/SearchCompanyRequest';
import { AppTestModule } from '../app/app.test.module';
import { DiligenceHistoryEntity } from '../../libs/entities/DiligenceHistoryEntity';
import { MonitorModule } from './monitor.module';
import { CompanyEntity } from '../../libs/entities/CompanyEntity';
import { MonitorRiskDynamicsV2Entity } from '../../libs/entities/MonitorRiskDynamicsV2Entity';
import { MonitorCommonService } from './monitor.common.service';
import { CompanySearchService } from '../company/company-search.service';
import { OrgSettingsLogEntity } from '../../libs/entities/OrgSettingsLogEntity';
import { RateTrendInfo } from '../company/model/CreditRateTrendResult';
import { MonitorRiskEnums } from '../../libs/enums/monitor/MonitorRiskEnums';
import * as uuid from 'uuid';

// 为测试目的创建一个简单的CommonHelper模拟
const CommonHelper = {
  replaceAll: (str: string, s1: string, s2: string) => {
    return str.replace(new RegExp(s1, 'gm'), s2);
  },
};

jest.setTimeout(6000 * 1000);
describe('Test MonitorCompanyService', () => {
  let userService: UserService;
  let certRepository: Repository<MonitorCertificationEntity>;
  let companyRepository: Repository<MonitorGroupCompanyEntity>;
  let diligenceRepo: Repository<DiligenceHistoryEntity>;
  let monitorCompanyService: MonitorCompanyService;
  let monitorGroupService: MonitorGroupService;
  let entityManager: EntityManager;
  let companyRepo: Repository<CompanyEntity>;
  let riskDynamicsRepo: Repository<MonitorRiskDynamicsV2Entity>;
  let monitorCommonService: MonitorCommonService;
  let companySearchService: CompanySearchService;

  const [testOrgId, testUserId] = generateUniqueTestIds('monitor.company.service.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, MonitorModule],
    }).compile();
    BundleTestUtils.spy(module.get(BundleHelperService));
    UserTestUtils.spyUserService(testUserId, module.get(UserService));
    entityManager = getRepository(MonitorGroupEntity).manager;
    certRepository = getRepository(MonitorCertificationEntity);
    companyRepository = getRepository(MonitorGroupCompanyEntity);
    diligenceRepo = getRepository(DiligenceHistoryEntity);
    monitorCompanyService = module.get(MonitorCompanyService);
    userService = module.get(UserService);
    monitorGroupService = module.get(MonitorGroupService);
    companyRepo = getRepository(CompanyEntity);
    riskDynamicsRepo = getRepository(MonitorRiskDynamicsV2Entity);
    monitorCommonService = module.get(MonitorCommonService);
    companySearchService = module.get(CompanySearchService);
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  beforeEach(async () => {
    await entityManager.delete(MonitorGroupEntity, { orgId: testUser.currentOrg });
    await entityManager.delete(MonitorGroupCompanyEntity, { orgId: testUser.currentOrg });
    jest.clearAllMocks(); // 清除所有mock状态
  });

  it.skip('资质证书过期', async () => {
    try {
      await monitorCompanyService.periodicalCheckCertRisks();
    } catch (error) {
      expect(error).not.toBeUndefined();
    }
  });

  it('资质证书过期', async () => {
    try {
      const c = await certRepository.findOne({
        where: {
          companyId: '55695a53d57f54d17d8b7aa91ccf9454',
        },
      });
      await monitorCompanyService._scanCertRiskV2(c);
    } catch (error) {
      expect(error).not.toBeUndefined();
    }
  });

  it.skip('删除合作关联方监控', async () => {
    const user = await userService.getRoverUser(101144);
    await monitorCompanyService.remove(user, {
      pageIndex: 0,
      pageSize: 0,
      searchKey: '战鹰科技有限公司',
    });
    // const list = await companyRepository.find({
    //   where: {
    //     orgId: 208,
    //     companyId: '3569168e0afeebe9842287addde7d821',
    //   },
    // });
    // expect(list.length).toBe(0);
  });

  it.skip('添加合作关联方监控', async () => {
    const user = await userService.getRoverUser(101144);
    await monitorCompanyService.create(user, [
      {
        companyId: '0183e4d2ce09837bc55166f9abb7f794',
        companyName: '深圳水滴激光科技有限公司',
        groupId: 7,
      },
    ]);
    await monitorCompanyService.create(user, [
      {
        companyId: '3569168e0afeebe9842287addde7d821',
        companyName: '华夏老兵出行吉林集团有限公司',
        groupId: 7,
        relatedCompanyId: 'b650bffc694472a44fcba4c1fdf5fc7f',
      },
    ]);
  });

  it.skip('debug-已失效的企业恢复', async () => {
    const currentUser = await userService.getRoverUser(101234);
    currentUser.currentOrg = 1002259;
    await monitorCompanyService.resumeFromSuspended(10521, currentUser);
    await monitorCompanyService.resumeFromSuspended(10522, currentUser);
  });

  it.skip('debug- search2', async () => {
    const currentUser = await userService.getRoverUser(6893);
    const condition1 = {
      region: [
        {
          pr: 'GD',
        },
      ],
      pageIndex: 1,
      pageSize: 10,
    };
    const response = await monitorCompanyService.search2(currentUser, condition1);
    const res1 = await monitorCompanyService.search(currentUser, condition1);
    expect(response.total).toBe(res1.total);
    const condition2 = {
      statusCode: ['20'],
      pageIndex: 1,
      pageSize: 10,
    };
    const response2 = await monitorCompanyService.search2(currentUser, condition2);
    const res2 = await monitorCompanyService.search(currentUser, condition2);
    expect(response2.total).toBe(res2.total);
    const condition3 = {
      industry: [
        {
          i1: 'F',
        },
      ],
      pageIndex: 1,
      pageSize: 10,
    };
    const response3 = await monitorCompanyService.search2(currentUser, condition3);
    const res3 = await monitorCompanyService.search(currentUser, condition3);
    expect(response3.total).toBe(res3.total);
    const condition4 = {
      registcapiAmount: [
        {
          min: 1000,
          max: 5000,
        },
      ],
      pageIndex: 1,
      pageSize: 10,
    };
    const response4 = await monitorCompanyService.search2(currentUser, condition4);
    const res4 = await monitorCompanyService.search(currentUser, condition4);
    expect(response4.total).toBe(res4.total);
    const condition5 = {
      startDateCode: [
        Object.assign(new DateRangeRelative(), {
          currently: true,
          flag: 4,
          min: 10,
          max: 20,
          unit: 'year',
        }),
      ],
      pageIndex: 1,
      pageSize: 30,
    };
    const response5 = await monitorCompanyService.search2(currentUser, condition5);
    const res5 = await monitorCompanyService.search(currentUser, condition5);
    const diff = difference(
      response5.data.map((it) => it.companyId),
      res5.data.map((it) => it.companyId),
    );
    // // console.log(diff);
    expect(response5.total).toBeGreaterThanOrEqual(res5.total);
    const condition6 = {
      creatorIds: [5171],
      pageIndex: 1,
      pageSize: 10,
    };
    const response6 = await monitorCompanyService.search2(currentUser, condition6);
    const res6 = await monitorCompanyService.search(currentUser, condition6);
    expect(response6.total).toBe(res6.total);
    const condition7 = {
      createDate: [
        Object.assign(new DateRangeRelative(), {
          currently: true,
          flag: 1,
          number: 7,
          unit: 'day',
        }),
      ],
      pageIndex: 1,
      pageSize: 10,
    };
    const response7 = await monitorCompanyService.search2(currentUser, condition7);
    const res7 = await monitorCompanyService.search(currentUser, condition7);
    expect(response7.total).toBe(res7.total);
    const condition8 = {
      pageIndex: 1,
      pageSize: 10,
      sortField: 'startDateCode',
      isSortAsc: true,
    };
    const response8 = await monitorCompanyService.search2(currentUser, condition8);
    const res8 = await monitorCompanyService.search(currentUser, condition8);
    expect(response8.total).toBe(res8.total);
    const condition9 = {
      pageIndex: 1,
      pageSize: 10,
      sortField: 'registcapi',
      isSortAsc: true,
    };
    const response9 = await monitorCompanyService.search2(currentUser, condition9);
    const res9 = await monitorCompanyService.search(currentUser, condition9);
    expect(response9.total).toBe(res9.total);
  });

  it('test batch move group', async () => {
    const group = await monitorGroupService.addGroup(testUser, Object.assign(new AddGroupRequest(), { name: '单元测试分组' }));
    const group2 = await monitorGroupService.addGroup(testUser, Object.assign(new AddGroupRequest(), { name: '目标分组' }));
    const company1 = await monitorCompanyService.create(testUser, [
      {
        companyId: 'a9c17ffc0c9a4f20c5e4fe1fce2aa15b',
        companyName: '福建达利食品集团有限公司',
        groupId: group.id,
      },
    ]);
    //关联方企业
    const company2 = await monitorCompanyService.create(
      testUser,
      [
        {
          companyId: '6082661459e54974be2ef632068c8812',
          companyName: '福建达利食品有限公司湖北分公司',
          groupId: group.id,
          relatedCompanyId: 'a9c17ffc0c9a4f20c5e4fe1fce2aa15b',
        },
      ],
      true,
    );
    //批量移动分组，仅移动company1
    await monitorCompanyService.update(
      testUser,
      Object.assign(new UpdateCompanyRequest(), {
        groupId: group2.id,
        ids: [company1[0].id],
      }),
    );
    const companyResponse = await monitorCompanyService.search2(testUser, new SearchCompanyRequest());
    expect(companyResponse.total).toBe(2);
    const company1Response = await monitorCompanyService.search2(
      testUser,
      Object.assign(new SearchCompanyRequest(), { searchKey: '福建达利食品集团有限公司' }),
    );
    expect(company1Response.data[0].groupId).toBe(group2.id);
    const company2Response = await monitorCompanyService.search2(
      testUser,
      Object.assign(new SearchCompanyRequest(), { searchKey: '福建达利食品有限公司湖北分公司' }),
    );
    //移动 company1时，company2作为关联方，group也变成了group2
    expect(company2Response.data[0].groupId).toBe(group2.id);

    //company1,company2都变更分组为group
    await monitorCompanyService.update(
      testUser,
      Object.assign(new UpdateCompanyRequest(), {
        groupId: group.id,
        ids: [company1Response.data[0].id, company2Response.data[0].id],
      }),
    );
    const companyResponse12 = await monitorCompanyService.search2(testUser, Object.assign(new SearchCompanyRequest(), { groupIds: [group.id] }));
    expect(companyResponse12.total).toBe(2);

    //仅移动 company2,company2的 group 变更为 group2, company1不变
    const company2n = companyResponse12.data.find((it) => it.companyId === company2[0].companyId);
    await monitorCompanyService.update(
      testUser,
      Object.assign(new UpdateCompanyRequest(), {
        groupId: group2.id,
        ids: [company2n.id],
      }),
    );
    //仅移动 company2的 group, company2的 group 变更为 group2, company1不变
    const company2Group2Response = await monitorCompanyService.search2(testUser, Object.assign(new SearchCompanyRequest(), { groupIds: [group2.id] }));

    const company1Group1Response = await monitorCompanyService.search2(testUser, Object.assign(new SearchCompanyRequest(), { groupIds: [group.id] }));

    expect(company2Group2Response.total).toBe(1);
    expect(company2Group2Response.data[0].groupId).toBe(group2.id);

    expect(company1Group1Response.total).toBe(1);
    expect(company1Group1Response.data[0].groupId).toBe(group.id);
  });

  it('test save monitor company', async () => {
    const group = await monitorGroupService.addGroup(testUser, Object.assign(new AddGroupRequest(), { name: '单元测试分组' }));
    const testCompany = {
      companyId: 'a9c17ffc0c9a4f20c5e4fe1fce2aa15b',
      companyName: '福建达利食品集团有限公司',
    };
    const company = await monitorCompanyService.createV2(testUser, [
      {
        ...testCompany,
        groupId: group.id,
      },
    ]);
    const diligence = await diligenceRepo.findOne({
      where: { orgId: testUser.currentOrg, companyId: testCompany.companyId },
      order: { updateDate: 'DESC' },
    });
    if (diligence) {
      expect(diligence.id).toBe(company[0].diligenceId);
      expect(diligence.result).toBe(company[0].result);
    }
  });

  it('test getCompanies', async () => {
    const companyIds = [
      '715f4a0a608f1e6d4a772c31ade1b171',
      'he7910e63f79702ea812d50f082dfaec',
      'h1a3ef7ad8ec2e56ba43e250225d1d28',
      'g599bafb4da74aa069cc9b67a39992e0',
      'a0ed6d61d21271179021367cac5d4420',
      'b2fbc4155d4781978f94ef67bc61c1e7',
      'ga7b26dace8da0c3b2fbdc6acefb4739',
      'g38ed9b86b70ab7a169f5ba36ab4bfb9',
      'g4bdd6c24056bb434978acfc5f40d168',
    ];
    const companyList = await monitorCompanyService.getCompanies(companyIds);
    expect(companyList.length).toBeGreaterThan(0);
  });

  it.skip('test monitorCompanySanctionsSchedule', async () => {
    await monitorCompanyService.monitorCompanySanctionsSchedule();
  });

  it('测试企查分变更处理', async () => {
    // 准备测试数据
    const validOrgIds = [testOrgId];
    const testCompanyId = 'test-company-id';
    const testCompanyName = '测试公司';

    // 模拟监控设置
    const mockSettings = [
      {
        id: 1,
        orgId: testOrgId,
        content: [
          {
            items: [
              {
                key: MonitorRiskEnums.QccCreditChange,
                status: 1,
              },
            ],
          },
        ],
      },
    ] as OrgSettingsLogEntity[];

    // 模拟企业数据
    const mockCompany = {
      id: 1,
      orgId: testOrgId,
      companyId: testCompanyId,
      companyName: testCompanyName,
      groupId: 1,
    } as MonitorGroupCompanyEntity;

    // 模拟企查分变更记录
    const mockCreditTrend: RateTrendInfo[] = [
      {
        Date: '2023-01-01',
        Score: 80,
        ScoreLevel: 'A',
      },
    ];

    // 模拟企业实体
    const mockCompanyEntity = {
      companyId: testCompanyId,
      creditRate: 90, // 当前分数不同于变更记录
    } as CompanyEntity;

    // Mock uuid 和 CommonHelper.replaceAll 函数
    jest.spyOn(uuid, 'v4').mockReturnValue('test-uuid');
    jest.spyOn(CommonHelper, 'replaceAll').mockReturnValue('test-clean-uuid');

    // 设置各个依赖服务的mock
    jest.spyOn(monitorCommonService, 'matchMonitorOrgSettingsByDimension').mockResolvedValue(mockSettings);
    jest.spyOn(companyRepository, 'find').mockResolvedValue([mockCompany]);
    jest.spyOn(companySearchService, 'getCreditRateTrend').mockResolvedValue(mockCreditTrend);
    jest.spyOn(companyRepo, 'findOne').mockResolvedValue(mockCompanyEntity);
    jest.spyOn(riskDynamicsRepo, 'insert').mockResolvedValue(undefined);

    // 执行测试
    await monitorCompanyService.processQccCreditChange(validOrgIds);

    // 验证各个依赖是否以正确的参数被调用
    expect(monitorCommonService.matchMonitorOrgSettingsByDimension).toHaveBeenCalledWith(validOrgIds, MonitorRiskEnums.QccCreditChange);
    expect(companyRepository.find).toHaveBeenCalledWith({ where: { orgId: In([testOrgId]) } });
    expect(companySearchService.getCreditRateTrend).toHaveBeenCalledWith(testCompanyId);
    expect(companyRepo.findOne).toHaveBeenCalledWith({ where: { companyId: testCompanyId } });

    // 验证企查分变更数据是否被插入数据库
    expect(riskDynamicsRepo.insert).toHaveBeenCalledWith(
      expect.objectContaining({
        orgId: testOrgId,
        companyId: testCompanyId,
        companyName: testCompanyName,
        dynamicId: '_test-clean-uuid',
        objectId: MonitorRiskEnums.QccCreditChange,
        groupKey: MonitorRiskEnums.MonitorOperatingRisk,
        dimensionKey: MonitorRiskEnums.QccCreditChange,
        dimensionLevel: 1,
        detail: {
          beforeScore: 80,
          currentScore: 90,
        },
      }),
    );
  });

  it('测试企查分变更处理 - 没有监控设置时不处理', async () => {
    const riskDynamicsInsertSpy = jest.spyOn(riskDynamicsRepo, 'insert');
    const companyFindSpy = jest.spyOn(companyRepository, 'find');
    const getCreditTrendSpy = jest.spyOn(companySearchService, 'getCreditRateTrend');

    jest.spyOn(monitorCommonService, 'matchMonitorOrgSettingsByDimension').mockResolvedValue([]);

    await monitorCompanyService.processQccCreditChange([testOrgId]);

    expect(companyFindSpy).not.toHaveBeenCalled();
    expect(getCreditTrendSpy).not.toHaveBeenCalled();
    expect(riskDynamicsInsertSpy).not.toHaveBeenCalled();
  });

  it('测试企查分变更处理 - 分数未变更时不创建风险记录', async () => {
    // 准备测试数据
    const validOrgIds = [testOrgId];
    const testCompanyId = 'test-company-id';
    const testCompanyName = '测试公司';

    // 模拟监控设置
    const mockSettings = [
      {
        id: 1,
        orgId: testOrgId,
        content: [
          {
            items: [
              {
                key: MonitorRiskEnums.QccCreditChange,
                status: 1,
              },
            ],
          },
        ],
      },
    ] as OrgSettingsLogEntity[];

    // 模拟企业数据
    const mockCompany = {
      id: 1,
      orgId: testOrgId,
      companyId: testCompanyId,
      companyName: testCompanyName,
      groupId: 1,
    } as MonitorGroupCompanyEntity;

    // 模拟企查分变更记录 - 分数相同，不应创建风险
    const mockCreditTrend: RateTrendInfo[] = [
      {
        Date: '2023-01-01',
        Score: 90, // 与当前分数相同
        ScoreLevel: 'A',
      },
    ];

    // 模拟企业实体
    const mockCompanyEntity = {
      companyId: testCompanyId,
      creditRate: 90, // 分数与变更记录相同
    } as CompanyEntity;

    // 设置各个依赖服务的mock
    jest.spyOn(monitorCommonService, 'matchMonitorOrgSettingsByDimension').mockResolvedValue(mockSettings);
    jest.spyOn(companyRepository, 'find').mockResolvedValue([mockCompany]);
    jest.spyOn(companySearchService, 'getCreditRateTrend').mockResolvedValue(mockCreditTrend);
    jest.spyOn(companyRepo, 'findOne').mockResolvedValue(mockCompanyEntity);
    const riskDynamicsInsertSpy = jest.spyOn(riskDynamicsRepo, 'insert');

    // 执行测试
    await monitorCompanyService.processQccCreditChange(validOrgIds);

    // 验证没有插入风险数据
    expect(riskDynamicsInsertSpy).not.toHaveBeenCalled();
  });

  it('测试企查分变更处理 - 没有企查分变更记录时不创建风险记录', async () => {
    // 准备测试数据
    const validOrgIds = [testOrgId];
    const testCompanyId = 'test-company-id';
    const testCompanyName = '测试公司';

    // 模拟监控设置
    const mockSettings = [
      {
        id: 1,
        orgId: testOrgId,
        content: [
          {
            items: [
              {
                key: MonitorRiskEnums.QccCreditChange,
                status: 1,
              },
            ],
          },
        ],
      },
    ] as OrgSettingsLogEntity[];

    // 模拟企业数据
    const mockCompany = {
      id: 1,
      orgId: testOrgId,
      companyId: testCompanyId,
      companyName: testCompanyName,
      groupId: 1,
    } as MonitorGroupCompanyEntity;

    // 模拟没有企查分变更记录
    const mockCreditTrend = [] as RateTrendInfo[];

    // 设置各个依赖服务的mock
    jest.spyOn(monitorCommonService, 'matchMonitorOrgSettingsByDimension').mockResolvedValue(mockSettings);
    jest.spyOn(companyRepository, 'find').mockResolvedValue([mockCompany]);
    jest.spyOn(companySearchService, 'getCreditRateTrend').mockResolvedValue(mockCreditTrend);
    jest.spyOn(companyRepo, 'findOne').mockResolvedValue(null);

    const riskDynamicsInsertSpy = jest.spyOn(riskDynamicsRepo, 'insert');

    // 执行测试
    await monitorCompanyService.processQccCreditChange(validOrgIds);

    // 验证没有插入风险数据
    expect(riskDynamicsInsertSpy).not.toHaveBeenCalled();
  });

  it('测试企查分变更处理 - 没有企业时不处理', async () => {
    // 准备测试数据
    const validOrgIds = [testOrgId];

    // 模拟监控设置
    const mockSettings = [
      {
        id: 1,
        orgId: testOrgId,
        content: [
          {
            items: [
              {
                key: MonitorRiskEnums.QccCreditChange,
                status: 1,
              },
            ],
          },
        ],
      },
    ] as OrgSettingsLogEntity[];

    // 模拟没有企业数据
    jest.spyOn(monitorCommonService, 'matchMonitorOrgSettingsByDimension').mockResolvedValue(mockSettings);
    jest.spyOn(companyRepository, 'find').mockResolvedValue([]);

    const getCreditTrendSpy = jest.spyOn(companySearchService, 'getCreditRateTrend');
    const riskDynamicsInsertSpy = jest.spyOn(riskDynamicsRepo, 'insert');

    // 执行测试
    await monitorCompanyService.processQccCreditChange(validOrgIds);

    // 验证没有查询企查分变更记录和插入风险数据
    expect(getCreditTrendSpy).not.toHaveBeenCalled();
    expect(riskDynamicsInsertSpy).not.toHaveBeenCalled();
  });
});
