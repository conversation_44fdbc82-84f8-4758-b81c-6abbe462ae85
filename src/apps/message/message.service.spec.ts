import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { RoverUser } from '../../libs/model/common';
import { AppTestModule } from '../app/app.test.module';
import { getTestUser } from '../test_utils_module/test.user';
import { MessageModule } from './message.module';
import { MessageService } from './message.service';

jest.setTimeout(60 * 1000);
describe('message.service.spec.ts', () => {
  let messageService: MessageService;
  let entityManager: EntityManager;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, MessageModule],
    }).compile();
    messageService = module.get(MessageService);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('按类型部分全部已读', async () => {
    try {
      const readAll = await messageService.readAll(Object.assign(new RoverUser(), { userId: 101348 }), 0);
      // // console.log(JSON.stringify(readAll));
    } catch (error) {
      expect(error).toBeUndefined();
    }
  });

  it.skip('getMessages', async () => {
    const params = {
      msgType: 1,
      msgStatus: 0,
      pageSize: 10,
      pageIndex: 1,
    };
    const response = await messageService.getMessages(getTestUser(208, 5171), params);
    // console.log(JSON.stringify(response));
  });
});
