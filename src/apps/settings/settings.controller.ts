import { Body, Controller, ForbiddenException, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { SettingsService } from './settings.service';
import { RoverSessionGuard } from 'libs/guards/RoverSession.guard';
import { RoverUser } from 'libs/model/common';
import { RoverRolesGuard } from 'libs/guards/rover.roles.guard';
import { ConfigurationModel } from '../../libs/model/settings/ConfigurationModel';
import { ConfigurationTypeEnums } from '../../libs/model/settings/ConfigurationTypeEnums';
import { SettingTypeEnums } from '../../libs/model/settings/SettingTypeEnums';
import { OrgSettingsLogEntity } from '../../libs/entities/OrgSettingsLogEntity';
import { SearchOrgSettingParam } from '../../libs/model/settings/SearchOrgSettingParam';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from '../../libs/exceptions/exceptionConstants';

@Controller('settings')
@ApiTags('设置中心')
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class SettingsController {
  constructor(private readonly settingService: SettingsService) {}

  @Get('/')
  @ApiOperation({ summary: '获取组织的setting' })
  async getSettingsV2(@Req() req, @Query('id') id: number, @Query('type') type = SettingTypeEnums.diligence_risk) {
    const currentUser: RoverUser = req.user;
    let orgSetting: OrgSettingsLogEntity;
    if (id) {
      orgSetting = await this.settingService.getOrgSettings(currentUser.currentOrg, type, id);
    } else {
      orgSetting = await this.settingService.getOrgLatestSettings(currentUser.currentOrg, type);
    }
    return orgSetting;
  }

  // ------------------------------------------- 下面为不同模块设置的查询和更新接口，以方便不同权限点的设置和绑定 ------------------------------------------- //

  @Get('/diligence')
  @ApiOperation({ summary: '获取组织的准入排查setting' })
  async getDiligenceSettings(@Req() req, @Query('id') id: number) {
    const type = SettingTypeEnums.tender_risk;
    const currentUser: RoverUser = req.user;
    return this.settingService.getOrgSettingsByType(currentUser.currentOrg, type, id);
  }

  @Get('/tender')
  @ApiOperation({ summary: '获取组织的招标排查setting' })
  async getTenderSettings(@Req() req, @Query('id') id: number) {
    const type = SettingTypeEnums.tender_risk;
    const currentUser: RoverUser = req.user;
    return this.settingService.getOrgSettingsByType(currentUser.currentOrg, type, id);
  }

  @Get('/potential')
  @ApiOperation({ summary: '获取组织的潜在利益排查setting' })
  async getPotentialSettings(@Req() req, @Query('id') id: number) {
    const type = SettingTypeEnums.potential;
    const currentUser: RoverUser = req.user;
    return this.settingService.getOrgSettingsByType(currentUser.currentOrg, type, id);
  }
  @Get('/specific')
  @ApiOperation({ summary: '获取组织的特定利益排查setting' })
  async getSpecificSettings(@Req() req, @Query('id') id: number) {
    const type = SettingTypeEnums.specific;
    const currentUser: RoverUser = req.user;
    return this.settingService.getOrgSettingsByType(currentUser.currentOrg, type, id);
  }

  // ------------------------------------------- 上面为不同模块设置的查询和更新接口，以方便不同权限点的设置和绑定 ------------------------------------------- //

  @Post('/')
  @ApiOperation({ summary: '更新组织的setting' })
  async updateSettingsV2(@Req() req, @Body() data: OrgSettingsLogEntity) {
    const currentUser: RoverUser = req.user;
    return this.settingService.updateSetting(currentUser, data);
  }

  @Post('/compare')
  @ApiOperation({ summary: '比较系统setting和组织的setting的差异' })
  async compareSettingsV2(@Req() req, @Query('id') id: number) {
    const currentUser: RoverUser = req.user;
    return this.settingService.compareSettings(currentUser.currentOrg, id);
  }

  @Get('/system')
  @ApiOperation({ summary: '获取系统设置' })
  async getSystemSetting(@Req() req, @Query('type') type: SettingTypeEnums, @Query('groupVersion') groupVersion?: string) {
    const currentUser: RoverUser = req.user;
    return this.settingService.getSystemLatestSettings(type, groupVersion, currentUser.currentOrg);
  }

  @Post('/multi/list')
  @ApiOperation({ summary: '准入排查多模板-获取模板列表' })
  async listSetting(@Req() req, @Query('type') type = SettingTypeEnums.diligence_risk, @Body() param = new SearchOrgSettingParam()) {
    const currentUser: RoverUser = req.user;
    param.deleted = 0;
    const settings = await this.settingService.listOrgSettings(currentUser.currentOrg, type, param);
    return {
      total: settings.length,
      data: settings,
    };
  }

  @Post('/multi/add')
  @ApiOperation({ summary: '准入排查多模板-新增模板' })
  async addSetting(@Req() req, @Body() data: OrgSettingsLogEntity) {
    const currentUser: RoverUser = req.user;
    return this.settingService.addOrgSettings(currentUser, data);
  }

  @Post('/multi/update')
  @ApiOperation({ summary: '准入排查多模板-编辑模板' })
  async updateSetting(@Req() req, @Body() data: OrgSettingsLogEntity) {
    const currentUser: RoverUser = req.user;
    return this.settingService.updateSetting(currentUser, data);
  }

  @Post('/multi/delete')
  @ApiOperation({ summary: '准入排查多模板-删除模板' })
  async deleteSetting(@Req() req, @Query('id') id: number) {
    const currentUser: RoverUser = req.user;
    return this.settingService.deleteSetting(currentUser, id);
  }

  @Post('/multi/sort')
  @ApiOperation({ summary: '准入排查多模板-排序模板' })
  async sortSetting(@Req() req, @Body() ids: number[]) {
    const currentUser: RoverUser = req.user;
    return this.settingService.sortSetting(currentUser, ids);
  }

  @Get('/configuration')
  @ApiOperation({ summary: '获取org的批量巡检配置' })
  @ApiOkResponse({ type: ConfigurationModel })
  async getConfiguration(@Req() req) {
    const currentUser: RoverUser = req.user;
    const config = await this.settingService.getConfiguration(currentUser.currentOrg, ConfigurationTypeEnums.diligence_analyze);
    return { [ConfigurationTypeEnums.diligence_analyze]: config };
  }

  @Post('/configuration')
  @ApiOperation({ summary: '更新org的批量巡检配置' })
  async updateConfiguration(@Req() req, @Body() postData: ConfigurationModel) {
    const currentUser: RoverUser = req.user;
    return this.settingService.updateConfiguration(currentUser, ConfigurationTypeEnums.diligence_analyze, postData[ConfigurationTypeEnums.diligence_analyze]);
  }

  @Post('/risk/restore')
  @ApiOperation({ summary: '设置中心准入排查恢复出厂配置' })
  async riskRestore(@Req() req) {
    const currentUser: RoverUser = req.user;
    return this.settingService.restore(currentUser, SettingTypeEnums.diligence_risk);
  }

  @Post('/risk/restoreById')
  @ApiOperation({ summary: '设置中心准入排查恢复出厂配置' })
  async riskRestoreById(@Req() req, @Query('id') id: number) {
    const currentUser: RoverUser = req.user;
    if (!id) {
      throw new BadParamsException(RoverExceptions.BadParams.Common);
    }
    return this.settingService.riskRestoreById(currentUser, id);
  }

  @Post('/tender/restore')
  @ApiOperation({ summary: '设置中心招标排查恢复出厂配置' })
  async tenderRestore(@Req() req) {
    const currentUser: RoverUser = req.user;
    return this.settingService.restore(currentUser, SettingTypeEnums.tender_risk);
  }

  // @Get('/configuration/:type')
  // @ApiOperation({ summary: '获取org的configuration' })
  // async getOrgConfiguration(@Req() req, @Param('type') type: ConfigurationTypeEnums) {
  //   const currentUser: RoverUser = req.user;
  //   return this.settingService.getConfiguration(currentUser.currentOrg, type);
  // }

  // @Post('/configuration/:type')
  // @ApiOperation({ summary: '更新org的configuration' })
  // async updateOrgConfiguration(@Req() req, @Param('type') type: ConfigurationTypeEnums, @Body() postData) {
  //   const currentUser: RoverUser = req.user;
  //   return this.settingService.updateConfiguration(currentUser, type, postData);
  // }

  // @Get('/user/configuration/monitor_notify2/generatesecretKey')
  // @ApiOperation({ summary: '生成监控接口推送的秘钥' })
  // async generateRSAKeyPair() {
  //   return generateRandomString(35, 45);
  // }

  // @Get('/user/configuration/:type')
  // @ApiOperation({ summary: '获取user的configuration' })
  // async getUserConfiguration(@Req() req, @Param('type') type: UserConfigurationTypeEnums) {
  //   return this.settingService.getUserConfiguration(req.user, type);
  // }

  // @Post('/user/configuration/monitor_notify2')
  // @ApiOperation({ summary: '更新用户监控推送设置' })
  // async updateUserMonitorNotify(@Req() req, @Body() postData: MonitorPushDimensionType) {
  //   return this.settingService.updateUserConfiguration(req.user, UserConfigurationTypeEnums.monitor_notify2, postData);
  // }

  // @Post('/user/configuration/diligence_default_setting')
  // @ApiOperation({ summary: '更新用户模型准入排查模型' })
  // async updateUserConfiguration(@Req() req, @Body() postData: UserConfigurationDiligenceSetting) {
  //   return this.settingService.updateUserConfiguration(req.user, UserConfigurationTypeEnums.diligence_default_setting, postData);
  // }

  @Post('/incrementSystemSetting')
  async incrementSystemSetting(
    @Req() req,
    @Query('type') type: SettingTypeEnums,
    @Query('version') version: string,
    @Query('releaseNote') releaseNote: string,
    @Query('orgIds') orgIds?: string,
  ) {
    const currentUser: RoverUser = req.user;
    const { currentOrg: orgId } = currentUser;
    if (![295, 208].includes(orgId)) {
      throw new ForbiddenException();
    }
    return this.settingService.incrementSystemSetting(type, version, releaseNote, orgIds?.split(',').map(parseInt));
  }
}
