import { BadRequestException, Injectable } from '@nestjs/common';
import { Entity<PERSON>anager, MoreThan, Repository } from 'typeorm';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { DimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { cloneDeep, compact, find, flatten, omit, pick } from 'lodash';
import { DimensionGroupDefinitionType } from 'libs/model/diligence/pojo/dimension/group/DimensionGroupDefinitionType';
import { DimensionGroupItems } from 'libs/model/diligence/pojo/dimension/group/DimensionGroupItems';
import { GroupDimensionVersionEnums } from 'libs/model/diligence/pojo/dimension/group/GroupDimensionVersionEnums';
import { DimensionLevel1Enums } from 'libs/enums/diligence/DimensionLevel1Enums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { ConfigurationDiligenceAnalyze, ConfigurationModel } from '../../libs/model/settings/ConfigurationModel';
import { OrgConfigurationEntity } from '../../libs/entities/OrgConfigurationEntity';
import { DefaultConfigurationModel } from '../../libs/constants/settings.constants';
import { RoverUser } from '../../libs/model/common';
import { ConfigurationTypeEnums } from '../../libs/model/settings/ConfigurationTypeEnums';
import { TenderDimensions, TenderSettings } from '../../libs/constants/tender.dimension.constants';
import { SettingTypeEnums } from '../../libs/model/settings/SettingTypeEnums';
import { OrgSettingsLogEntity } from '../../libs/entities/OrgSettingsLogEntity';
import { SystemSettingsLogEntity } from '../../libs/entities/SystemSettingsLogEntity';
import { OperatorTypeEnums } from '../../libs/enums/oplog/OperatorTypeEnums';
import { OpLogEntity } from '../../libs/entities/OpLogEntity';
import { getLogModuleName, OpLogModuleEnums } from '../../libs/enums/oplog/OpLogModuleEnums';
import { SearchOrgSettingParam } from '../../libs/model/settings/SearchOrgSettingParam';
import { RoverExceptions } from '../../libs/exceptions/exceptionConstants';
import { Cacheable, CacheClear } from '@type-cacheable/core';
import { RoverBundleCounterType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { DefaultMonitorDimension } from 'libs/constants/monitor.dimension.constants';
import { NewsSetting } from '../../libs/constants/news.setting.constants';
import { MonitorDimensionType } from '../../libs/model/monitor/MonitorDimensionPO';
import { defaultExtraContent } from '../../libs/constants/default-settings';
import { ConfigurationTenderRiskDimension } from '../../libs/model/bidding/model/ConfigurationTenderRiskDimensionPo';
import { getDefaultDimensionGroupDefinition } from '../../libs/constants/dimension.constants';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import Redlock from 'redlock';
import {
  getAllDimension,
  getDimensionDefinition,
  getDimensionGroupDefinition,
  processDimensionLevel,
  processTenderConfig,
  updateItemWithConfig,
} from './settings.utils';
import * as LRUCache from 'lru-cache';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { isRedlockAcquireLock } from '../../libs/utils/redis.utils';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { SpecificDimensionSettings } from '../../libs/constants/specific.dimension.constant';
import { PotentialDimensionSettings } from '../../libs/constants/potential.dimension.constants';

interface DimensionLevel1Info {
  key: string;
  name: string;
  sort: number;
}

/**
 * 一级维度 key name
 */
interface DimensionLevel1Response {
  dimensions: DimensionLevel1Info[];
  dimensionsKV: Record<string, string>;
}

@Injectable()
export class SettingsService {
  private readonly redlock: Redlock;
  private readonly lruCache: LRUCache<any, any>;
  private readonly logger: Logger = QccLogger.getLogger(SettingsService.name);

  constructor(
    @InjectRepository(OrgSettingsLogEntity) private readonly orgSettingLogRepo: Repository<OrgSettingsLogEntity>,
    @InjectRepository(SystemSettingsLogEntity) private readonly systemSettingLogRepo: Repository<SystemSettingsLogEntity>,
    @InjectRepository(OrgConfigurationEntity) private readonly configurationRepo: Repository<OrgConfigurationEntity>,
    @InjectRepository(OpLogEntity) private readonly oplogRepo: Repository<OpLogEntity>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly bundleService: RoverBundleService,
    private readonly redisService: RedisService,
  ) {
    this.redlock = new Redlock([redisService.getClient()], {
      retryCount: 2,
    });
    this.lruCache = new LRUCache({
      max: 500,
      ttl: 1000 * 60,
      // return stale items before removing from cache?
      allowStale: false,
      updateAgeOnGet: false,
      updateAgeOnHas: false,
    });
  }

  static systemSettingCacheKey = (args: any[]) => `system_setting_cache:${args[0]}-${args[1]}-${args[2]}`;

  static orgSettingCacheKey = (args: any[]) => `org_setting_cache:${args[0]}-${args[1]}`;

  static orgConfigurationCacheKey = (args: any[]) => `org_configuration_cache:${args[0]}_${args[1]}`;

  /**
   * 获取不同模块的设置信息
   * @param currentOrg
   * @param type
   * @param id
   * @returns
   */
  async getOrgSettingsByType(currentOrg: number, type: SettingTypeEnums, id?: number) {
    let orgSetting: OrgSettingsLogEntity;
    if (id) {
      orgSetting = await this.getOrgSettings(currentOrg, type, id);
    } else {
      orgSetting = await this.getOrgLatestSettings(currentOrg, type);
    }
    return orgSetting;
  }

  /**
   * 根据组织ID获得diligence group version 返回最新的默认模型
   * @param orgId
   */
  async getSettings(orgId: number): Promise<DimensionGroupDefinitionType> {
    const cacheKey = `org_setting_cache:${orgId}`;
    const cached = this.lruCache.get(cacheKey);
    if (cached) {
      return cached;
    }
    // 首先通过orgSetting查询orgId对应的groupVersion，如果未找到则默认是v1
    const oldSetting = await this.orgSettingLogRepo.findOne({
      where: {
        orgId,
        type: SettingTypeEnums.diligence_risk,
      },
    });
    const groupVersion = oldSetting?.groupVersion || GroupDimensionVersionEnums.V1;
    let result;
    if (groupVersion === GroupDimensionVersionEnums.V2) {
      result = getDefaultDimensionGroupDefinition('v2');
    }
    result = getDefaultDimensionGroupDefinition('v1');
    this.lruCache.set(cacheKey, result);
    return result;
  }

  // async getMonitorRiskItems(orgSetting: OrgSettingsLogEntity): Promise<Map<string, MonitorDimensionType[]>> {
  //   const riskMap: Map<string, MonitorDimensionType[]> = new Map<string, MonitorDimensionType[]>();
  //   const highRisk: MonitorDimensionType[] = [];
  //   const mediumRisk: MonitorDimensionType[] = [];
  //   const alertRisk: MonitorDimensionType[] = [];
  //   const dimensions: MonitorDimensionType[] = orgSetting.content;
  //   dimensions?.forEach((d) => {
  //     d?.items?.forEach((i) => {
  //       if (i.level === DimensionRiskLevelEnum.High) {
  //         highRisk.push(i);
  //       } else if (i.level === DimensionRiskLevelEnum.Medium) {
  //         mediumRisk.push(i);
  //       } else if (i.level === DimensionRiskLevelEnum.Alert) {
  //         alertRisk.push(i);
  //       }
  //     });
  //   });
  //   riskMap.set(
  //     DimensionRiskLevelEnum.High.toString(),
  //     highRisk.sort((a, b) => {
  //       return a.sort - b.sort;
  //     }),
  //   );
  //   riskMap.set(
  //     DimensionRiskLevelEnum.Medium.toString(),
  //     mediumRisk.sort((a, b) => {
  //       return a.sort - b.sort;
  //     }),
  //   );
  //   riskMap.set(
  //     DimensionRiskLevelEnum.Alert.toString(),
  //     alertRisk.sort((a, b) => {
  //       return a.sort - b.sort;
  //     }),
  //   );
  //   return riskMap;
  // }

  // async getAllMonitorRisks(orgSetting: OrgSettingsLogEntity): Promise<MonitorDimensionType[]> {
  //   const riskItems: MonitorDimensionType[] = [];
  //   const dimensions: MonitorDimensionType[] = orgSetting.content;
  //   dimensions?.forEach((d) => {
  //     d?.items?.forEach((i) => {
  //       riskItems.push(i);
  //     });
  //   });
  //   return riskItems;
  // }

  /**
   * 获取排查模型 (按分组定义)
   * @param orgSetting
   * @param returnVersion 是否返回 version
   * @returns
   */
  async getDimensionGroupDefinition(orgSetting: OrgSettingsLogEntity, returnVersion = false): Promise<DimensionGroupDefinitionType> {
    return getDimensionGroupDefinition(orgSetting, returnVersion);
  }

  /**
   * 获取排查模型维度数组 (按维度打平)
   * 不包含一级维度
   * @param orgSetting
   * @param ignoreDisabled true -不返回关闭的维度
   * @param showAll  true - 维度有子维度把自身也同时返回
   * @returns
   */
  // @Cacheable({ ttlSeconds: 60 })
  async getAllDimension(orgSetting: OrgSettingsLogEntity, ignoreDisabled = false, showAll = false): Promise<DimensionDefinitionPO[]> {
    return getAllDimension(orgSetting, ignoreDisabled, showAll);
  }

  /**
   * 根据 维度 key 和 matchType 获取到维度的定义，
   * @param dimensionKey 维度 key
   * @param matchType
   * @param orgId
   * @returns
   */
  async getDimensionDefinition(dimensionKey: DimensionTypeEnums, orgSetting: OrgSettingsLogEntity): Promise<DimensionDefinitionPO> {
    return getDimensionDefinition(dimensionKey, orgSetting);
  }

  async getGroupKeys(orgId: number): Promise<DimensionLevel1Enums[]> {
    const groupSetting: DimensionGroupDefinitionType = (await this.getOrgLatestSettings(orgId, SettingTypeEnums.diligence_risk)).content;
    return Object.keys(groupSetting)
      .filter((t) => t !== 'version')
      .map((t) => t as DimensionLevel1Enums);
  }

  // @Cacheable({ ttlSeconds: 300, cacheKey: SettingsService.orgConfigurationCacheKey })
  async getConfiguration(orgId: number, type: ConfigurationTypeEnums): Promise<ConfigurationDiligenceAnalyze | undefined> {
    if (type == ConfigurationTypeEnums.tender_risk) {
      throw new BadRequestException({ error: 'invalid type' });
    }
    let orgConfig: ConfigurationDiligenceAnalyze;
    const defaultConfig: ConfigurationModel = cloneDeep(DefaultConfigurationModel);
    const configuration: OrgConfigurationEntity = await this.configurationRepo.findOne({ where: { orgId } });
    if (configuration?.content?.[type]) {
      orgConfig = configuration.content[type];
    } else {
      orgConfig = defaultConfig[type];
    }
    // Configuration 中的 tender_risk 废弃，不再返回
    return orgConfig;
  }

  // @CacheClear({ cacheKey: SettingsService.orgConfigurationCacheKey })
  async updateConfiguration(user: RoverUser, type: ConfigurationTypeEnums, postData: ConfigurationDiligenceAnalyze) {
    const { currentOrg: orgId, userId } = user;
    if (type == ConfigurationTypeEnums.tender_risk) {
      // Configuration 中的 tender_risk 已经废弃
      throw new BadRequestException({ error: 'invalid type' });
    }

    const preConfig = await this.getConfiguration(orgId, type);
    let configuration: OrgConfigurationEntity = await this.configurationRepo.findOne({ where: { orgId } });
    if (!configuration) {
      configuration = Object.assign(new OrgConfigurationEntity(), {
        orgId,
        editorId: userId,
        content: new ConfigurationModel(),
      });
    } else {
      if (!configuration?.content) {
        Object.assign(configuration, { content: new ConfigurationModel() });
      }
    }
    configuration.content[type] = postData;
    // 校验
    if (type == ConfigurationTypeEnums.diligence_analyze && postData.configs?.length > 1) {
      // 分组不能重叠
      if (postData.configs.some((x) => !x.groupIds?.length)) {
        throw new BadRequestException({ error: '分组不能重复选择' });
      }
      const usedGroupIds = [];
      postData.configs.forEach((x) => {
        x.groupIds.forEach((y) => {
          if (usedGroupIds.includes(y)) {
            throw new BadRequestException({ error: '分组不能重复选择' });
          }
          usedGroupIds.push(y);
        });
      });
    }
    // 删除已废弃的招标排查设置
    delete configuration.content[ConfigurationTypeEnums.tender_risk];
    await Bluebird.all([
      this.configurationRepo.save(configuration),
      this.oplogRepo.save(
        Object.assign(new OpLogEntity(), {
          orgId,
          moduleName: OpLogModuleEnums.Diligence_Analyze_Setting,
          operatorType: OperatorTypeEnums.Edit,
          preContent: preConfig,
          content: postData,
          createBy: userId,
        }),
      ),
    ]);
    return configuration.content[type];
  }

  async getTenderDimensionGroup(orgId: number, orgSettingsId?: number): Promise<DimensionGroupItems> {
    const tenderDimensionGroup = cloneDeep(TenderDimensions);
    const tenderConfigEntity: OrgSettingsLogEntity = await this.getTenderConfigEntity(orgId, orgSettingsId);
    const tenderConfig: ConfigurationTenderRiskDimension[] = processTenderConfig(tenderConfigEntity);

    tenderDimensionGroup.items.forEach((item) => {
      const config = tenderConfig.find((x) => x.key === item.key);
      if (!config) return;
      updateItemWithConfig(item, config);

      if (item.status === 1) {
        processDimensionLevel(item, config);
      }
    });
    tenderDimensionGroup.items.sort((a, b) => a?.sort - b?.sort);

    return tenderDimensionGroup;
  }

  private async getTenderConfigEntity(orgId: number, orgSettingsId?: number): Promise<OrgSettingsLogEntity> {
    return orgSettingsId
      ? await this.getOrgSettings(orgId, SettingTypeEnums.tender_risk, orgSettingsId)
      : await this.getOrgLatestSettings(orgId, SettingTypeEnums.tender_risk);
  }

  private async getAllTenderDimension(orgId: number): Promise<DimensionDefinitionPO[]> {
    const tenderDimensionGroup = await this.getTenderDimensionGroup(orgId);
    const dimensions: DimensionDefinitionPO[] = [];
    tenderDimensionGroup.items.forEach((dimension) => {
      if (!dimension.status) {
        return null;
      }
      dimension?.subDimensionList?.forEach((s) => {
        dimensions.push(Object.assign(new DimensionDefinitionPO(), {}, s));
      });
    });
    return compact(dimensions);
  }

  async getTenderDimensionDefinition(dimensionKey: DimensionTypeEnums, orgId = 0): Promise<DimensionDefinitionPO> {
    const allDimensionDefines = await this.getAllTenderDimension(orgId);
    return find(allDimensionDefines, { key: dimensionKey });
  }

  public async getDimensionKV(orgId: number) {
    const dimensionsKV = {};
    const groupSetting: DimensionGroupDefinitionType = (await this.getOrgLatestSettings(orgId, SettingTypeEnums.diligence_risk)).content;
    const orgSetting: OrgSettingsLogEntity = new OrgSettingsLogEntity();
    orgSetting.content = getDefaultDimensionGroupDefinition('v1');
    if (groupSetting.version == GroupDimensionVersionEnums.V2) {
      orgSetting.content = getDefaultDimensionGroupDefinition('v2');
    }
    Object.keys(groupSetting).forEach((gKey) => {
      if (gKey !== 'version') dimensionsKV[gKey] = groupSetting[gKey].name;
    });
    const allDefPOs = await this.getAllDimension(orgSetting, false, true);
    allDefPOs.forEach((d) => (dimensionsKV[d.key] = d.name));
    return dimensionsKV;
  }

  /**
   * 获取一级维度 key name
   * @param orgId
   */
  public async getDimensionLevel1KV(orgId: number): Promise<DimensionLevel1Response> {
    const groupSetting: DimensionGroupDefinitionType = (await this.getOrgLatestSettings(orgId, SettingTypeEnums.diligence_risk)).content;

    // Get sorted dimension items with complete information
    const sortedDimensions = Object.entries(groupSetting)
      .filter(([key]) => key !== 'version')
      .map(([key, value]) => ({
        key,
        name: value.name,
        sort: value.sort || 0,
      }))
      .sort((a, b) => a.sort - b.sort);

    // For backward compatibility, also return the KV object
    const dimensionsKV = sortedDimensions.reduce((acc, { key, name }) => {
      acc[key] = name;
      return acc;
    }, {});

    return {
      dimensions: sortedDimensions,
      dimensionsKV,
    };
  }

  /**
   * 获取监控维度 key name
   * @param orgId
   */
  public async getMonitorDimensionKV(orgId?: number) {
    const dimensionsKV = {};
    let groupSetting: MonitorDimensionType[] = DefaultMonitorDimension;
    if (orgId) {
      const rr = await this.getOrgLatestSettings(orgId, SettingTypeEnums.monitor);
      groupSetting = rr.content;
    }
    const items = flatten(
      groupSetting.map((s) => {
        dimensionsKV[s.key] = s.name;
        return s.items;
      }),
    );
    items.forEach((d) => (dimensionsKV[d.key] = d.name));
    return dimensionsKV;
  }

  public async getTenderDimensionKV() {
    const dimensionsKV = {};
    const setting = cloneDeep(TenderDimensions);
    setting.items.forEach((item) => {
      dimensionsKV[item.key] = item.name;
      item.subDimensionList?.forEach((sub) => {
        dimensionsKV[sub.key] = sub.name;
      });
    });
    dimensionsKV[DimensionLevel2Enums.BiddingCompanyRelationship] = '投资任职关联';
    dimensionsKV[DimensionLevel2Enums.BiddingCompanyRelationship2] = '潜在关联/利益关联方';
    dimensionsKV[DimensionLevel2Enums.DirectConnection] = '被列入内部黑名单';
    dimensionsKV[DimensionLevel2Enums.BlackListInvestigations] = '内部黑名单关联关系';

    return dimensionsKV;
  }

  public async restore(currentUser: RoverUser, type: SettingTypeEnums) {
    const { currentOrg: orgId } = currentUser;
    const orgSetting = await this.getOrgLatestSettings(orgId, type);
    const systemSetting = await this.systemSettingLogRepo.findOne({ id: orgSetting.systemSettingsId });
    orgSetting.content = systemSetting.content;
    orgSetting.extraContent = defaultExtraContent;
    return this.updateSetting(currentUser, orgSetting);
  }

  public async riskRestoreById(currentUser: RoverUser, id: number) {
    const orgSetting = await this.orgSettingLogRepo.findOne({ id, orgId: currentUser.currentOrg });
    if (!orgSetting) {
      throw new BadParamsException(RoverExceptions.BadParams.Common);
    }
    const systemSetting = await this.systemSettingLogRepo.findOne({ id: orgSetting.systemSettingsId });
    orgSetting.content = systemSetting.content;
    orgSetting.extraContent = defaultExtraContent;
    return this.updateSetting(currentUser, orgSetting);
  }

  /**
   * 拿到组织的默认设置，多模型时默认设置是在设置中心排序第一的模型
   * @param orgId
   * @param type
   */
  // @Cacheable({ ttlSeconds: 60, cacheKey: SettingsService.orgSettingCacheKey })
  async getOrgLatestSettings(orgId: number, type: SettingTypeEnums, version?: number): Promise<OrgSettingsLogEntity> {
    return (await this.listOrgSettings(orgId, type, { version, deleted: 0 }))[0];
  }

  /**
   * 获取系统默认设置（如果默认设置已被删除，使用最新设置模型）
   * @param orgId
   * @param version
   */
  async getDiligenceRiskDefaultOrgSetting(orgId: number, version?: number): Promise<OrgSettingsLogEntity> {
    let orgSetting = await this.getOrgLatestSettings(orgId, SettingTypeEnums.diligence_risk, version);
    //如果 orgSetting 为空表示，用户设置的默认模型已经被删除了，使用最新配置模型
    orgSetting = orgSetting || (await this.getOrgLatestSettings(orgId, SettingTypeEnums.diligence_risk));
    return orgSetting;
  }

  @Cacheable({ ttlSeconds: 5 })
  @TraceLog({ throwError: true, spanName: 'getOrgSettings' })
  async getOrgSettings(orgId: number, type: SettingTypeEnums, id?: number, version?: number) {
    // const cacheKey = `org_setting_cache:${orgId},${type},${id},${version}`;
    // const cached = this.lruCache.get(cacheKey);
    // if (cached) {
    //   return cached;
    // }
    let orgSetting: OrgSettingsLogEntity;
    if (id) {
      orgSetting = await this.orgSettingLogRepo.findOne({ id, orgId }, { relations: ['creator'] });
    } else if (version) {
      orgSetting = (await this.listOrgSettings(orgId, type, { version, deleted: 0 }))?.[0];
    }
    if (!orgSetting) {
      orgSetting = await this.getOrgLatestSettings(orgId, type);
    }
    const systemSetting = await this.getSystemLatestSettings(orgSetting.type, orgSetting.groupVersion, orgId);
    if (orgSetting.systemSettingsId < systemSetting.id) {
      orgSetting['canUpgrade'] = true;
    }
    this.mergeContent(orgSetting.type, orgSetting.content, orgSetting.groupVersion);
    // this.lruCache.set(cacheKey, orgSetting);
    return orgSetting;
  }

  /**
   * 比较组织设置与系统最新设置的差异
   * @param orgId 组织ID
   * @param id 设置ID
   * @returns 比较结果,包含:
   * - content: 合并后的设置内容
   * - systemSettingId: 系统设置ID
   * - version: 设置版本
   * - extraContent: 额外内容
   * - releaseNotes: 发布说明
   * @throws BadRequestException 当找不到指定设置时抛出异常
   * @description
   * 1. 获取组织设置和系统最新设置
   * 2. 将两个设置合并,保留组织的设置值
   * 3. 对于系统新增的维度,会标记highlight=true
   * 4. 支持多种设置类型的比较:
   *    - diligence_risk: 准入排查设置
   *    - tender_risk: 招标排查设置
   *    - monitor: 监控预警设置
   *    - monitor_news: 舆情监控设置
   *    - specific: 特定利益关系设置
   */
  async compareSettings(orgId: number, id: number) {
    const orgSettingEntity = await this.orgSettingLogRepo.findOne({ id, orgId });
    if (!orgSettingEntity) {
      throw new BadRequestException(RoverExceptions.BadParams.NotFound);
    }
    const orgSetting = orgSettingEntity.content;
    const type = orgSettingEntity.type;
    let groupVersion;
    if (type == SettingTypeEnums.diligence_risk) {
      groupVersion = orgSettingEntity?.groupVersion || 'v1';
    }
    const systemSettingEntity = await this.getSystemLatestSettings(type, groupVersion, orgId);
    const systemSetting = systemSettingEntity.content;
    let extraContent = undefined;
    // 将orgSetting和systemSetting合并,保留org 的设置值
    switch (type) {
      case SettingTypeEnums.diligence_risk: {
        Object.keys(systemSetting).forEach((key) => {
          if (key == 'version') {
            return;
          }
          const systemGroup = systemSetting[key];
          const orgGroup = orgSetting[key];
          if (orgGroup) {
            Object.assign(systemGroup, pick(orgGroup, ['status', 'sort']));
            systemGroup.items?.forEach((item) => {
              const orgItem = orgGroup.items.find((x) => x.key == item.key);
              if (orgItem) {
                this.compareDiligenceDimension(item, orgItem);
                item.subDimensionList?.forEach((sub) => {
                  const orgSub = orgGroup.subDimensionList?.find((x) => x.key == sub.key);
                  if (orgSub) {
                    this.compareDiligenceDimension(sub, orgSub);
                    sub.subDimensionList?.forEach((ssub) => {
                      const orgSsub = orgSub.subDimensionList?.find((x) => x.key == ssub.key);
                      if (orgSsub) {
                        this.compareDiligenceDimension(ssub, orgSsub);
                      } else {
                        // 新增的维度
                        ssub['highlight'] = true;
                      }
                    });
                  } else {
                    // 新增的维度
                    sub['highlight'] = true;
                  }
                });
              } else {
                item['highlight'] = true;
                // 新增的维度
              }
            });
          } else {
            systemGroup['highlight'] = true;
            // 新增的group
          }
        });
        // 合并extraContent
        if (systemSettingEntity.extraContent) {
          extraContent = cloneDeep(systemSettingEntity.extraContent);
          if (orgSettingEntity.extraContent?.creditRate) {
            extraContent.creditRate.status = orgSettingEntity.extraContent?.creditRate.status;
          }
        }
        break;
      }
      case SettingTypeEnums.tender_risk: {
        systemSetting.forEach((itemSys) => {
          const itemOrg = orgSetting.find((x) => x.key == itemSys.key);
          if (itemOrg) {
            Object.assign(itemSys, omit(itemOrg, ['subDimensionList', 'types']));
            itemSys.types?.forEach((type) => {
              const typeB = itemOrg.types?.find((x) => x.key == type.key);
              if (typeB) {
                Object.assign(type, omit(typeB, ['key', 'name']));
              }
            });
            itemSys.subDimensionList?.forEach((subItemSys) => {
              const subItemOrg = itemOrg.subDimensionList?.find((x) => x.key == subItemSys.key);
              if (subItemOrg) {
                Object.assign(subItemSys, omit(subItemOrg, ['types']));
                subItemSys.types?.forEach((type) => {
                  const typeB = subItemOrg.types?.find((x) => x.key == type.key);
                  if (typeB) {
                    Object.assign(type, omit(typeB, ['key', 'name']));
                  }
                });
              } else {
                subItemSys['highlight'] = true;
              }
            });
          } else {
            itemSys['highlight'] = true;
          }
        });
        break;
      }
      case SettingTypeEnums.monitor: {
        systemSetting.forEach((itemSys) => {
          const itemOrg = orgSetting.find((x) => x.key == itemSys.key);
          if (itemOrg) {
            Object.assign(itemSys, pick(itemOrg, ['status', 'sort']));
            itemSys.items?.forEach((subItemSys) => {
              const subItemOrg = itemOrg.items?.find((x) => x.key == subItemSys.key);
              if (subItemOrg) {
                if (subItemSys.params?.length > 0) {
                  subItemOrg.params = subItemSys.params.map((po) => {
                    const orgPo = subItemOrg.params?.find((orgPo) => po.field == orgPo.field);
                    if (!orgPo) {
                      return po;
                    } else {
                      //fieldVal
                      if (po.fieldVal?.length) {
                        orgPo.fieldVal = po.fieldVal.map((pFieldVal) => {
                          const orgFieldVal = orgPo.fieldVal?.find((x) => x.key == pFieldVal.key);
                          return orgFieldVal ?? pFieldVal;
                        });
                      }
                      orgPo.fieldName = po.fieldName;
                    }
                    return orgPo;
                  });
                }
                Object.assign(subItemSys, pick(subItemOrg, ['params', 'status', 'sort']));
              } else {
                subItemSys['highlight'] = true;
              }
            });
          } else {
            itemSys['highlight'] = true;
          }
        });
        break;
      }
      case SettingTypeEnums.monitor_news: {
        //打平当前设置的所有风险等级的舆情动态类型
        // const orgNewsTypes: string[] = [];
        // Object.keys(orgSetting).forEach((key) => {
        //   if (key !== NewsSettingEnum.Manually) {
        //     Array.prototype.push.apply(orgNewsTypes, orgSetting[key]);
        //   }
        // });
        // //打平系统设置的所有风险等级的舆情动态类型
        // const sysNewsTypes: string[] = [];
        // Object.keys(systemSetting).forEach((key) => {
        //   if (key !== NewsSettingEnum.Manually) {
        //     Array.prototype.push.apply(sysNewsTypes, systemSetting[key]);
        //   }
        // });
        // //orgNewsTypes和当前风险等级系统sysNewsTypes的差集表示删减类型
        // const sysDeleteTypes = difference(orgNewsTypes, sysNewsTypes);
        // //当前风险等级系统sysNewsTypes和orgNewsTypes的差集表示新增类型
        // const sysNewTypes = difference(sysNewsTypes, orgNewsTypes);
        // type TempType = { key?: string; types?: string[] };

        // const newTypes: TempType[] = [];
        // const deleteTypes: TempType[] = [];
        // Object.keys(systemSetting).forEach((key) => {
        //   if (key !== NewsSettingEnum.Manually) {
        //     const sysRiskLevelItems: string[] = systemSetting[key] as string[];
        //     //当前风险等级系统sysRiskLevelItems和sysNewTypes的交集表示新增类型
        //     const newType = intersection(sysRiskLevelItems, sysNewTypes);
        //     if (newType?.length) {
        //       newTypes.push({ key, types: newType });
        //     }
        //     //orgRiskLevelItems当前风险等级类型和sysDeleteTypes的交集表示删减类型
        //     const orgRiskLevelItems: string[] = orgSetting[key] as string[];
        //     const deleteType = intersection(sysDeleteTypes, orgRiskLevelItems);
        //     if (deleteType?.length) {
        //       deleteTypes.push({ key, types: deleteType });
        //     }
        //   }
        // });
        // const differenceItem: { newTypes: TempType[]; deleteTypes: TempType[] } = { newTypes, deleteTypes };
        // systemSetting['difference'] = differenceItem;
        break;
      }
      case SettingTypeEnums.specific: {
        systemSetting.forEach((itemSys) => {
          const itemOrg = orgSetting.find((x) => x.key == itemSys.key);
          if (itemOrg) {
            Object.assign(itemSys, omit(itemOrg, ['subDimensionList', 'types', 'strategyModel']));
            if (itemOrg.strategyModel?.detailsParams?.length) {
              itemSys.strategyModel?.detailsParams?.forEach((param) => {
                const orgField = itemOrg.strategyModel?.detailsParams.find((x) => x.field == param.field);
                if (orgField) {
                  param.fieldVal = orgField.fieldVal;
                }
              });
            }
            itemSys.types?.forEach((type) => {
              const typeB = itemOrg.types?.find((x) => x.key == type.key);
              if (typeB) {
                Object.assign(type, omit(typeB, ['key', 'name']));
              }
            });
          }
        });
        break;
      }
      case SettingTypeEnums.potential: {
        Object.keys(systemSetting).forEach((key) => {
          const systemGroup = systemSetting[key];
          const orgGroup = orgSetting[key];
          if (orgGroup) {
            Object.assign(systemGroup, omit(orgGroup, ['items']));
            systemGroup.items?.forEach((systemItem) => {
              const orgItem = orgGroup.items.find((x) => x.key == systemItem.key);
              if (orgItem) {
                Object.assign(systemItem, omit(orgItem, ['strategyModel']));
                if (orgItem.strategyModel?.detailsParams?.length) {
                  systemItem.strategyModel?.detailsParams?.forEach((param) => {
                    const orgField = orgItem.strategyModel?.detailsParams.find((x) => x.field == param.field);
                    if (orgField) {
                      param.fieldVal = orgField.fieldVal;
                      if (orgField.percentage) {
                        param.percentage = orgField.percentage;
                      }
                    }
                  });
                }
              }
            });
          }
        });
        break;
      }
    }
    const queryBuilder = this.systemSettingLogRepo.createQueryBuilder('log');
    const condition = { type, id: MoreThan(orgSettingEntity.systemSettingsId) };
    if (orgSettingEntity.groupVersion) {
      condition['groupVersion'] = orgSettingEntity.groupVersion;
    }
    queryBuilder.where(condition);
    if (orgId) {
      queryBuilder.andWhere(
        '(log.orgWhitelist IS NULL OR JSON_CONTAINS(log.orgWhitelist, :orgId, "$"))',
        { orgId: JSON.stringify(orgId) }, // 确保orgId是字符串，符合JSON查询
      );
    }
    const systems = await queryBuilder.getMany();
    systems.sort((a, b) => b.id - a.id);
    const releaseNotes = systems.map((x) => x.releaseNote).filter((t) => t);

    return {
      releaseNotes,
      content: systemSetting,
      systemSettingId: systemSettingEntity.id,
      version: groupVersion,
      extraContent,
    };
  }

  private compareDiligenceDimension(systemDim: DimensionDefinitionPO, orgDim: DimensionDefinitionPO) {
    Object.assign(systemDim, pick(orgDim, ['sort', 'type', 'status']));
    if (systemDim.isHidden) {
      systemDim.status = 0;
    }
    if (systemDim.strategyModel) {
      if (orgDim.strategyModel) {
        Object.assign(systemDim.strategyModel, omit(orgDim.strategyModel, ['detailsParams', 'sortField']));
        systemDim.strategyModel.detailsParams?.forEach((paramA) => {
          const paramB = orgDim.strategyModel.detailsParams?.find((x) => x.field == paramA.field);
          if (paramB) {
            if (paramA.fieldVal instanceof Array && paramA.fieldVal.some((x) => x.key)) {
              paramA.fieldVal
                .filter((x) => x.key)
                .forEach((subA) => {
                  const subB = paramB.fieldVal?.find((x) => x.key == subA.key);
                  if (subB) {
                    Object.assign(subA, omit(subB, ['child']));
                    if (subA.child?.length) {
                      subA.child.forEach((ssubA) => {
                        const ssubB = subB.child?.find((x) => x.key == ssubA.key);
                        if (ssubB) {
                          Object.assign(ssubA, ssubB);
                        }
                      });
                    }
                  }
                });
            } else {
              Object.assign(paramA, paramB);
            }
          } else {
            // 新增的参数
            systemDim['highlight'] = true;
          }
        });
      } else {
        systemDim['highlight'] = true;
      }
    }
  }

  @Cacheable({ ttlSeconds: 60, cacheKey: SettingsService.systemSettingCacheKey })
  async getSystemLatestSettings(type: SettingTypeEnums, groupVersion?: string, orgId?: number): Promise<SystemSettingsLogEntity> {
    const condition = { type };
    if (type == SettingTypeEnums.diligence_risk && groupVersion != 'v2') {
      groupVersion = 'v1';
    }
    if (groupVersion) {
      condition['groupVersion'] = groupVersion;
    }
    const count = await this.systemSettingLogRepo.count(condition);
    if (count == 0) {
      await this.incrementSystemSetting(type, groupVersion);
    }

    const qb = this.systemSettingLogRepo.createQueryBuilder('log').where('log.type = :type', { type }).addOrderBy('log.id', 'DESC').take(1);
    if (groupVersion) {
      qb.andWhere('log.groupVersion = :groupVersion', { groupVersion });
    }
    if (orgId) {
      qb.andWhere(`(log.orgWhitelist is null or json_contains(log.orgWhitelist, ':orgId', '$')) `, { orgId });
    }
    const systemSetting = (await qb.getMany())[0];
    this.mergeContent(type, systemSetting.content, systemSetting.groupVersion);
    return systemSetting;
  }

  @CacheClear({ cacheKey: SettingsService.systemSettingCacheKey })
  async incrementSystemSetting(type: SettingTypeEnums, groupVersion?: string, releaseNote?: string, orgIds?: number[]) {
    const entity: SystemSettingsLogEntity = Object.assign(new SystemSettingsLogEntity(), {
      type,
      releaseNote,
    });
    switch (type) {
      case SettingTypeEnums.diligence_risk: {
        entity.groupVersion = groupVersion;
        if (groupVersion == 'v2') {
          entity.content = getDefaultDimensionGroupDefinition('v2');
        } else {
          entity.content = getDefaultDimensionGroupDefinition('v1');
          entity.extraContent = defaultExtraContent;
        }
        break;
      }
      case SettingTypeEnums.tender_risk: {
        entity.content = cloneDeep(TenderSettings);
        break;
      }
      case SettingTypeEnums.monitor: {
        entity.content = cloneDeep(DefaultMonitorDimension);
        break;
      }
      case SettingTypeEnums.monitor_news: {
        entity.content = cloneDeep(NewsSetting);
        break;
      }
      case SettingTypeEnums.specific: {
        entity.content = cloneDeep(SpecificDimensionSettings);
        break;
      }
      case SettingTypeEnums.potential: {
        entity.content = cloneDeep(PotentialDimensionSettings);
        break;
      }
    }
    if (orgIds?.length) {
      entity.orgWhitelist = orgIds;
    }
    return this.systemSettingLogRepo.save(entity);
  }

  /**
   * 使用系统模型文案描述覆盖用户模型的文案
   * @param type
   * @param content 旧模型
   * @param groupVersion
   */
  private mergeContent(type: SettingTypeEnums, content: any, groupVersion?: string) {
    switch (type) {
      case SettingTypeEnums.diligence_risk: {
        let systemConfig;
        if (groupVersion == 'v2') {
          // 蔡司版
          systemConfig = getDefaultDimensionGroupDefinition('v2');
        } else {
          // SaaS版
          systemConfig = getDefaultDimensionGroupDefinition('v1');
        }
        // TODO 处理潜在风险 这种实现方式会导致新模型的一级维度直接被忽略
        // 遍历旧模型的key
        Object.keys(content).forEach((key) => {
          if (key == 'version') {
            return;
          }
          const element = systemConfig[key];
          if (!element) {
            return;
          }
          content[key].name = element.name;
          content[key].sort = element.sort;
          content[key].items?.forEach((itemSys) => {
            const itemOrg = element.items?.find((x) => x.key == itemSys.key);
            if (itemOrg) {
              this.mergeDimension(itemSys, itemOrg);
              itemSys.subDimensionList?.forEach((subA) => {
                const subB = itemOrg.subDimensionList?.find((x) => x.key == subA.key);
                if (subB) {
                  this.mergeDimension(subA, subB);
                  subA.subDimensionList?.forEach((ssubA) => {
                    const ssubB = subB.subDimensionList?.find((x) => x.key == ssubA.key);
                    if (ssubB) {
                      this.mergeDimension(ssubA, ssubB);
                    }
                  });
                }
              });
            }
          });
        });
        break;
      }

      case SettingTypeEnums.tender_risk: {
        content.sort((a, b) => a?.sort - b?.sort);
        break;
      }
      case SettingTypeEnums.monitor: {
        break;
      }
      case SettingTypeEnums.monitor_news: {
        break;
      }
      case SettingTypeEnums.specific: {
        break;
      }
      case SettingTypeEnums.potential: {
        break;
      }
    }
  }

  /**
   * 指定维度设置不跟用户自定义模型走，永远使用系统最新
   * @param orgDim  用户维度设置
   * @param systemDim  系统维度设置
   */
  private mergeDimension(orgDim: DimensionDefinitionPO, systemDim: DimensionDefinitionPO) {
    const pickArray = [
      'name',
      'source',
      'template',
      'template2',
      'sourcePath',
      'isVirtualDimension',
      'description',
      'detailSource',
      'detailSourcePath',
      'typeCode',
      'child',
      'isHidden',
    ];
    Object.assign(orgDim, pick(systemDim, pickArray));
    if (orgDim.strategyModel) {
      Object.assign(orgDim.strategyModel, { sortField: systemDim?.strategyModel?.sortField || undefined });
    }
    if (orgDim.strategyModel?.detailsParams?.length && systemDim.strategyModel?.detailsParams?.length) {
      orgDim.strategyModel?.detailsParams.forEach((paramA) => {
        const paramB = systemDim.strategyModel.detailsParams?.find((x) => x.field == paramA.field);
        if (paramB && paramA.sort >= 0 && paramB.sort >= 0) {
          Object.assign(paramA, pick(paramB, ['sort']));
        }
        if (paramB && paramA.fieldVal instanceof Array && paramA.fieldVal.some((x) => x.key)) {
          paramA.fieldVal
            .filter((x) => x.key)
            .forEach((subA) => {
              const subB = paramB.fieldVal?.find((x) => x.key == subA.key);
              if (subB) {
                Object.assign(subA, pick(subB, ['keyName']));
                if (subA.child?.length) {
                  subA.child.forEach((ssubA) => {
                    const ssubB = subB.child?.find((x) => x.key == ssubA.key);
                    if (ssubB) {
                      Object.assign(ssubA, pick(ssubB, ['keyName']));
                    }
                  });
                }
              }
            });
        }
      });
      orgDim.strategyModel.detailsParams.sort((a, b) => a?.sort - b?.sort);
    }
    if (!systemDim.strategyModel?.cycle && orgDim.strategyModel?.cycle) {
      delete orgDim.strategyModel.cycle;
    }
    // 处理排序异常 保证sort值唯一
    if (orgDim.subDimensionList) {
      let prev;
      orgDim.subDimensionList.sort((a, b) => a.sort - b.sort);
      orgDim.subDimensionList.forEach((item) => {
        if (item.sort === prev) {
          item.sort++;
        }
        prev = item.sort;
      });
    }
  }

  async getSettingById(settingId: number) {
    return this.orgSettingLogRepo.findOne(settingId);
  }

  /**
   * 获取组织的设置列表
   * @param orgId 组织ID
   * @param type 设置类型
   * @param param 查询参数
   * @returns 设置列表
   * @throws BadRequestException 当orgId未定义时抛出异常
   * @description
   * 1. 如果组织没有设置,会根据type类型初始化默认设置:
   *    - diligence_risk: 第三方风险排查标准模型
   *    - tender_risk: 招标风险排查标准模型
   *    - monitor: 监控预警标准模型
   *    - monitor_news: 舆情动态设置标准模型
   *    - specific: 特定利益关系排查标准模型
   * 2. 初始化的设置会设置active=1,createBy=-1
   * 3. 会同步系统设置的最新版本
   */

  async listOrgSettings(orgId: number, type: SettingTypeEnums, param?: SearchOrgSettingParam) {
    if (!orgId) {
      throw new BadRequestException({ error: 'undefined orgId' });
    }
    let systemSetting;
    let count = await this.orgSettingLogRepo.count({ orgId, type });
    if (count == 0) {
      let groupVersion;
      const entity: OrgSettingsLogEntity = Object.assign(new OrgSettingsLogEntity(), {
        orgId,
        type,
        createBy: -1,
        active: 1,
      });
      switch (type) {
        // 准入排查
        case SettingTypeEnums.diligence_risk: {
          entity.content = await this.getSettings(orgId);
          entity.extraContent = defaultExtraContent;
          groupVersion = entity.content.version || GroupDimensionVersionEnums.V1;
          entity.groupVersion = groupVersion;
          entity.name = '第三方风险排查标准模型';
          break;
        }
        // 招标排查
        case SettingTypeEnums.tender_risk: {
          entity.content = cloneDeep(TenderSettings);
          entity.name = '招标风险排查标准模型';
          break;
        }
        // 监控预警
        case SettingTypeEnums.monitor: {
          entity.content = cloneDeep(DefaultMonitorDimension);
          entity.name = '监控预警标准模型';
          break;
        }
        // 舆情监控
        case SettingTypeEnums.monitor_news: {
          entity.content = cloneDeep(NewsSetting);
          entity.name = '舆情动态设置标准模型';
          break;
        }
        // 特定利益关系排查
        case SettingTypeEnums.specific: {
          entity.content = cloneDeep(SpecificDimensionSettings);
          entity.name = '特定利益关系排查标准模型';
          break;
        }
        // 潜在利益排查
        case SettingTypeEnums.potential: {
          entity.content = cloneDeep(PotentialDimensionSettings);
          entity.name = '潜在利益关系排查标准模型';
          break;
        }
      }
      let lock = null;
      try {
        lock = await this.redlock.acquire([`lock_org_setting:${orgId}:${type}`], 3 * 1000);
        systemSetting = await this.getSystemLatestSettings(type, groupVersion, orgId);
        entity.systemSettingsId = systemSetting.id;
        if (lock) {
          count = await this.orgSettingLogRepo.count({ orgId, type });
          if (count == 0) {
            await this.orgSettingLogRepo.save(entity);
          }
        }
      } catch (e) {
        console.error(e);
      } finally {
        if (lock) {
          await lock.release();
        }
      }
    }

    const qb = this.orgSettingLogRepo
      .createQueryBuilder('log')
      .leftJoinAndSelect('log.creator', 'creator')
      .where('log.orgId = :orgId', { orgId })
      .andWhere('log.type = :type', { type })
      .andWhere('log.active = 1');
    if (param?.deleted != undefined) {
      qb.andWhere('log.deleted = :deleted', { deleted: param.deleted });
    }
    if (param?.version != undefined) {
      qb.andWhere('log.version = :version', { version: param.version });
    }
    const orgSettings = await qb.getMany();
    if (!orgSettings?.length) {
      return [];
    }
    if (!systemSetting) {
      systemSetting = await this.getSystemLatestSettings(orgSettings[0].type, orgSettings[0].groupVersion, orgId);
    }
    orgSettings.forEach((x) => {
      this.mergeContent(type, x.content, x.groupVersion);
      if (x.systemSettingsId < systemSetting.id) {
        x['canUpgrade'] = true;
      }
    });
    orgSettings.sort((a, b) => {
      if (a.sort == b.sort) {
        return a.version - b.version;
      }
      return b.sort - a.sort;
    });
    return orgSettings;
  }

  /**
   * 添加组织设置
   * @param currentUser 当前用户信息
   * @param data 组织设置实体数据
   * @returns 保存后的组织设置实体
   * @throws BadRequestException 当设置名称重复时抛出异常
   * @description
   * 1. 校验设置数据的合法性
   * 2. 检查设置名称是否重复
   * 3. 检查并增加套餐计数
   * 4. 使用分布式锁确保并发安全
   * 5. 生成新的版本号
   * 6. 保存设置和操作日志
   * 注意: 如果系统设置没有，会创建一个默认设置，所以如果在系统设置没有的情况下，添加设置，会创建一个默认设置，然后又添加了一个，所以会创建2个设置
   */
  async addOrgSettings(currentUser: RoverUser, data: OrgSettingsLogEntity) {
    const { currentOrg: orgId, userId } = currentUser;
    await this.validateSetting(data);
    const settings = await this.listOrgSettings(orgId, data.type);
    if (settings.some((x) => x.deleted == 0 && x.name == data.name)) {
      throw new BadRequestException(RoverExceptions.Setting.NameDuplicated);
    }
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceModelQuantity);
    // await bundleCounter.clear();
    await bundleCounter.increase(1);
    /*// 暂时10个，后续配置到套餐里
    if (settings.filter((x) => x.deleted == 0).length >= 5) {
      throw new BadRequestException(RoverExceptions.Setting.CreateReachLimit);
    }*/
    try {
      const lock = await this.redlock.acquire([`lock_org_setting:${orgId}:${data.type}`], 5 * 1000);
      // 版本号加1
      const version = Math.max(...settings.map((x) => x.version)) + 1;
      Object.assign(data, {
        orgId,
        createBy: userId,
        active: 1,
        deleted: 0,
        sort: 0,
        version,
        id: undefined,
        createDate: undefined,
        creator: undefined,
        systemSettingsLog: undefined,
      });

      const moduleName = getLogModuleName(data.type);
      const opLog = Object.assign(new OpLogEntity(), {
        orgId,
        moduleName,
        operatorEntity: data.name,
        operatorType: OperatorTypeEnums.Create,
        content: {
          logVersion: 2,
          content: data.content,
          extraContent: data.extraContent,
        },
        createBy: userId,
      });
      await Bluebird.all([this.orgSettingLogRepo.save(data), this.oplogRepo.save(opLog)]);
      await lock.release();
      return data;
    } catch (e) {
      await bundleCounter.decrease(1);
      if (isRedlockAcquireLock(e)) {
        this.logger.warn(`5s钟内有重复的请求`);
      } else {
        throw e;
      }
    }
  }

  async updateSetting(currentUser: RoverUser, data: OrgSettingsLogEntity) {
    const { currentOrg: orgId, userId } = currentUser;
    await this.validateSetting(data);
    try {
      const lock = await this.redlock.acquire([`lock_org_setting:${orgId}:${data.type}`], 5 * 1000);
      const settings = await this.listOrgSettings(orgId, data.type, { deleted: 0 });
      if (settings.some((x) => x.version !== data.version && x.name == data.name)) {
        throw new BadRequestException(RoverExceptions.Setting.NameDuplicated);
      }
      const setting = settings.find((x) => x.version == data.version);
      if (!setting) {
        throw new BadRequestException({ error: '模型版本不存在' });
      }
      setting.active = 0;
      setting.creator = undefined;
      Object.assign(data, {
        orgId,
        createBy: userId,
        active: 1,
        deleted: 0,
        sort: setting.sort,
        id: undefined,
        createDate: undefined,
        creator: undefined,
        systemSettingsLog: undefined,
      });
      const moduleName = getLogModuleName(data.type);
      const opLog = Object.assign(new OpLogEntity(), {
        orgId,
        moduleName,
        operatorEntity: data.name,
        operatorType: OperatorTypeEnums.Edit,
        preContent: {
          logVersion: 2,
          content: setting.content,
          extraContent: setting.extraContent,
        },
        content: {
          logVersion: 2,
          content: data.content,
          extraContent: data.extraContent,
        },
        createBy: userId,
      });
      await this.entityManager.transaction(async () => {
        await Bluebird.all([this.orgSettingLogRepo.save([setting, data]), this.oplogRepo.save(opLog)]);
        await lock.release();
      });
    } catch (e) {
      if (isRedlockAcquireLock(e)) {
        this.logger.warn('5s钟内有重复的请求');
      } else {
        throw e;
      }
    }

    return data;
  }

  async deleteSetting(currentUser: RoverUser, id: number) {
    const { currentOrg: orgId, userId } = currentUser;
    const setting = await this.orgSettingLogRepo.findOne(id);
    const settings = await this.listOrgSettings(orgId, setting.type, { deleted: 0 });
    if (settings.length <= 1) {
      throw new BadRequestException(RoverExceptions.Setting.AtLeastOne);
    }
    // 批量巡检的方案不能删除
    if (setting.type == SettingTypeEnums.diligence_risk) {
      const orgConfig = await this.getConfiguration(orgId, ConfigurationTypeEnums.diligence_analyze);
      if (orgConfig.configs?.some((x) => x.settingVersion == setting.version)) {
        throw new BadRequestException(RoverExceptions.Setting.CanNotDelete);
      }
    }
    const moduleName = getLogModuleName(setting.type);

    const opLog = Object.assign(new OpLogEntity(), {
      orgId,
      moduleName,
      operatorEntity: setting.name,
      operatorType: OperatorTypeEnums.Remove,
      createBy: userId,
    });
    await Bluebird.all([
      this.orgSettingLogRepo.update(
        {
          orgId,
          type: setting.type,
          version: setting.version,
        },
        { deleted: 1 },
      ),
      this.oplogRepo.save(opLog),
    ]);
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceModelQuantity);
    await bundleCounter.decrease(1);
    return { affected: 1 };
  }

  async sortSetting(currentUser: RoverUser, ids: number[]) {
    const { currentOrg: orgId } = currentUser;
    const orderMap: Map<number, number> = new Map<number, number>();
    for (let i = 0; i < ids.length; i++) {
      orderMap.set(ids[i], ids.length - i);
    }
    const settings = await this.orgSettingLogRepo.findByIds(ids);
    await Bluebird.map(
      ids,
      async (id) => {
        const setting = settings.find((x) => x.id == id);
        await this.orgSettingLogRepo.update(
          {
            orgId,
            type: setting.type,
            version: setting.version,
          },
          { sort: orderMap.get(id) },
        );
      },
      { concurrency: 10 },
    );
    return { affected: ids.length };
  }

  private async validateSetting(orgSetting: OrgSettingsLogEntity) {
    if (!orgSetting.systemSettingsId) {
      throw new BadRequestException({ error: '系统设置不存在' });
    }
    const systemSetting = await this.systemSettingLogRepo.findOne(orgSetting.systemSettingsId);
    const systemContent = systemSetting.content;
    const content = orgSetting.content;
    // 校验下content格式
    switch (orgSetting.type) {
      case SettingTypeEnums.diligence_risk: {
        systemSetting.groupVersion = systemContent?.version || 'v1';
        if (systemSetting.groupVersion != orgSetting.groupVersion) {
          throw new BadRequestException({ error: 'version不一致' });
        }
        Object.keys(systemContent).forEach((key) => {
          if (key == 'version') {
            return;
          }
          if (!content[key]) {
            throw new BadRequestException({ error: `维度不存在：${key}` });
          }
          delete content[key].highlight;
          systemContent[key].items?.forEach((itemSys) => {
            const itemOrg = content[key].items?.find((x) => x.key == itemSys.key);
            if (!itemOrg) {
              throw new BadRequestException({ error: `维度不存在：${itemSys.key}` });
            }
            delete itemOrg.highlight;
            itemSys.subDimensionList?.forEach((subA) => {
              const subB = itemOrg.subDimensionList?.find((x) => x.key == subA.key);
              if (!subB) {
                throw new BadRequestException({ error: `维度不存在：${subA.key}` });
              }
              delete subB.highlight;
              subA.subDimensionList?.forEach((ssubA) => {
                const ssubB = subB.subDimensionList?.find((x) => x.key == ssubA.key);
                if (!ssubB) {
                  throw new BadRequestException({ error: `维度不存在：${ssubA.key}` });
                }
                delete ssubB.highlight;
              });
            });
          });
        });
        break;
      }
      case SettingTypeEnums.tender_risk: {
        systemContent.forEach((itemSys) => {
          const itemOrg = content.find((x) => x.key == itemSys.key);
          if (!itemOrg) {
            throw new BadRequestException({ error: `维度不存在：${itemSys.key}` });
          }
          delete itemOrg.highlight;
          if (itemSys.types) {
            itemSys.types.forEach((type) => {
              if (!itemOrg.types?.find((x) => x.key == type.key)) {
                throw new BadRequestException({ error: `关联类型不存在：${itemSys.key}-${type.key}` });
              }
            });
          }
          itemSys.subDimensionList?.forEach((subA) => {
            const subB = itemOrg.subDimensionList?.find((x) => x.key == subA.key);
            if (!subB) {
              throw new BadRequestException({ error: `维度不存在：${subA.key}` });
            }
            if (subA.types) {
              subA.types.forEach((type) => {
                if (!subB.types?.find((x) => x.key == type.key)) {
                  throw new BadRequestException({ error: `关联类型不存在：${subA.key}-${type.key}` });
                }
              });
            }
            delete subB.highlight;
          });
        });
        break;
      }
      case SettingTypeEnums.monitor: {
        systemContent.forEach((itemSys) => {
          const itemOrg = content.find((x) => x.key == itemSys.key);
          if (!itemOrg) {
            throw new BadRequestException({ error: `维度不存在：${itemSys.key}` });
          }
          delete itemOrg.highlight;
          itemSys.items?.forEach((item) => {
            const subB = itemOrg.items?.find((x) => x.key == item.key);
            if (!subB) {
              throw new BadRequestException({ error: `维度不存在：${item.key}` });
            }
            delete subB.highlight;
          });
        });
        break;
      }
      case SettingTypeEnums.monitor_news: {
        Object.keys(systemContent).forEach((itemKey) => {
          const itemOrg = content[itemKey];
          if (!itemOrg) {
            throw new BadRequestException({ error: `维度不存在：${itemKey}` });
          }
          delete itemOrg.highlight;
        });
        break;
      }
    }
  }

  public async getOrgIds(): Promise<number[]> {
    const settings = await this.orgSettingLogRepo.createQueryBuilder('osl').select(['osl.orgId']).groupBy('osl.orgId').getMany();
    return settings?.map((s) => s.orgId);
  }

  // /**
  //  * 设置特殊处理
  //  * @param data
  //  * @private
  //  */
  // private settingConversion(data: OrgSettingsLogEntity) {
  //   if (data.type === SettingTypeEnums.diligence_risk) {
  //     // 外部黑名单风险等级取子维度的最高风险等级
  //     const hitOuterBlackListModel = data.content['risk_outer_blacklist']?.items?.find((x) => x.key == 'HitOuterBlackList');
  //     hitOuterBlackListModel.strategyModel.level = Math.max(...hitOuterBlackListModel.subDimensionList.map((x) => (x.status == 1 ? x.strategyModel.level : 0)));
  //   }
  // }
  /**
   * 获取当前组织下生效的特定利益排查设置项
   * @param orgId
   * @param settingId
   */
  async getActiveSpecificDimensionSettings(orgId: number, settingId?: number) {
    const activeSetting = [];
    let orgSetting;
    if (settingId) {
      orgSetting = await this.getOrgSettings(orgId, SettingTypeEnums.specific, settingId);
    } else {
      orgSetting = await this.getOrgLatestSettings(orgId, SettingTypeEnums.specific);
    }
    if (orgSetting.content?.length > 0) {
      orgSetting.content.forEach((x) => {
        if (x.status == 1) {
          x.types = x.types?.filter((t) => t.status == 1);
          activeSetting.push(x);
        }
      });
    }
    return activeSetting;
  }

  /**
   * 获取当前组织下生效的特定利益排查设置项
   * @param orgId
   * @param settingId
   */
  async getActivePotentialDimensionSettings(orgId: number, settingId?: number) {
    const activeSetting = [];
    let orgSetting;
    if (settingId) {
      orgSetting = await this.getOrgSettings(orgId, SettingTypeEnums.potential, settingId);
    } else {
      orgSetting = await this.getOrgLatestSettings(orgId, SettingTypeEnums.potential);
    }
    if (orgSetting.content?.length > 0) {
      orgSetting.content.forEach((x) => {
        if (x.status == 1) {
          x.types = x.types?.filter((t) => t.status == 1);
          activeSetting.push(x);
        }
      });
    }
    return activeSetting;
  }

  /**
   * 获取一组 org下 生效的设置
   * @param orgIds
   * @param settingType
   */
  async getActiveLatestSettingsByOrgIds(orgIds: number[], settingType: SettingTypeEnums): Promise<OrgSettingsLogEntity[]> {
    const orgSettings: OrgSettingsLogEntity[] = [];
    if (!orgIds?.length) {
      return orgSettings;
    }

    // 使用 Set 来去重 orgIds
    const uniqueOrgIds = [...new Set(orgIds)];

    await Bluebird.map(
      uniqueOrgIds,
      async (orgId) => {
        const orgSetting = await this.getOrgLatestSettings(orgId, settingType);

        // 检查 orgSetting 是否存在且有效
        if (orgSetting) {
          orgSettings.push(orgSetting);
        }
      },
      { concurrency: 10 },
    );

    return orgSettings;
  }
}
