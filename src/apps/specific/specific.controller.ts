import { Body, Controller, Get, Param, ParseIntPipe, Post, Query, Request, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { RoverSessionGuard } from '../../libs/guards/RoverSession.guard';
import { RoverRolesGuard } from '../../libs/guards/rover.roles.guard';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverUser } from '../../libs/model/common';
import { SpecificRiskRequest } from '../../libs/model/specific/model/SpecificRiskRequest';
import { SpecificFacadeService } from './specific.facade.service';
import { SecurityService } from '../../libs/config/security.service';
import { SpecificInterestRecordEntity } from '../../libs/entities/SpecificInterestRecordEntity';
import { SearchDiligenceBiddingRequest } from '../../libs/model/bidding/SearchDiligenceBiddingRequest';
import { RemarkDiligenceBiddingRequest } from '../../libs/model/bidding/RemarkDiligenceBiddingRequest';
import { DiligenceBiddingHistoryRequest } from '../../libs/model/bidding/DiligenceBiddingHistoryRequest';
import { SpecificPdfService } from './service/specific.pdf.service';

@Controller('specific')
@ApiTags('特定利益关系')
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class SpecificController {
  private logger: Logger = QccLogger.getLogger(SpecificController.name);

  constructor(
    private readonly specificFacadeService: SpecificFacadeService,
    private readonly securityService: SecurityService,
    private readonly specificPdfService: SpecificPdfService,
  ) {}

  @Post('scanSpecificRisk')
  @ApiOperation({ summary: '特定利益关系排查' })
  async scanSpecificRisk(@Request() req, @Body() postData: SpecificRiskRequest) {
    const currentUser: RoverUser = req.user;
    postData['keyNos'] = postData.keyNoAndNames?.map((e) => e.companyId);
    return this.specificFacadeService.scanSpecificRisk(currentUser, postData);
  }

  @Post('scanSpecificRiskAsync')
  @ApiOperation({ summary: '特定利益关系排查' })
  async scanSpecificRiskAsync(@Request() req, @Body() postData: SpecificRiskRequest) {
    const currentUser: RoverUser = req.user;
    postData.orgId = currentUser.currentOrg;
    postData['keyNos'] = postData.keyNoAndNames?.map((e) => e.companyId);
    if (postData.keyNos.length < 21) {
      return this.specificFacadeService.scanSpecificRisk(currentUser, postData);
    }
    return this.specificFacadeService.scanSpecificRiskAsync(currentUser, postData);
  }

  @Post('reScanSpecificRiskAsync')
  @ApiOperation({ summary: '特定利益关系排查-重新排查' })
  async reScanSpecificRiskAsync(@Request() req, @Query('id') id: number) {
    const currentUser: RoverUser = req.user;
    return this.specificFacadeService.reScanSpecificRiskAsync(currentUser, id);
  }

  @Post('reScanSpecificRisk')
  @ApiOperation({ summary: '特定利益关系排查-重新排查' })
  async reScanTenderRisk(@Request() req, @Query('id') id: number) {
    const currentUser: RoverUser = req.user;
    return this.specificFacadeService.reScanSpecificRisk(currentUser, id);
  }

  @Post('search/detail')
  @ApiOperation({ summary: '查看特定利益关系排查详情' })
  async searchDetail(@Request() req, @Query('id') id: number) {
    const currentUser: RoverUser = req.user;
    await this.securityService.allowWithinOrg(SpecificInterestRecordEntity, currentUser, [id], 'id', [10191, 10195], 'operator');
    return (await this.specificFacadeService.searchDetail(currentUser.currentOrg, [id]))[0];
  }

  @Post('search')
  @ApiOperation({ summary: '分页查询排查历史' })
  async search(@Request() req, @Body() postData: SearchDiligenceBiddingRequest) {
    const currentUser: RoverUser = req.user;
    return this.specificFacadeService.search(currentUser, postData);
  }

  @Post('operators')
  @ApiOperation({ summary: '操作人列表' })
  async operators(@Request() req) {
    const currentUser: RoverUser = req.user;
    return this.specificFacadeService.operatorList(currentUser);
  }

  @Post('remark')
  @ApiOperation({ summary: '特定利益关系排查添加审核意见' })
  async remark(@Request() req, @Query('id') id: number, @Body() postData: RemarkDiligenceBiddingRequest) {
    const currentUser: RoverUser = req.user;
    await this.securityService.allowWithinOrg(SpecificInterestRecordEntity, currentUser, [id], 'id', [10191, 10195], 'operator');
    return this.specificFacadeService.remark(id, currentUser, postData);
  }

  @Post('edit')
  @ApiOperation({ summary: '特定利益关系排查编辑项目名称项目编号' })
  async edit(@Request() req, @Query('id') id: number, @Body() postData: DiligenceBiddingHistoryRequest) {
    const currentUser: RoverUser = req.user;
    await this.securityService.allowWithinOrg(SpecificInterestRecordEntity, currentUser, [id], 'id', [10191, 10195], 'operator');
    return this.specificFacadeService.edit(id, currentUser, postData);
  }

  @Post('result/statistics')
  @ApiOperation({ summary: '排查结果统计' })
  async getResultStatistics(@Request() req) {
    const currentUser: RoverUser = req.user;
    return this.specificFacadeService.getResultStatistics(currentUser);
  }

  @Get('/:id/exportSpecificPDF')
  @ApiOperation({ description: '实时查看pdf报告（开发调试使用,暂不开放）' })
  async diligencePdfView(@Request() req, @Param('id', ParseIntPipe) id: number) {
    const currentUser: RoverUser = req.user;
    return this.specificPdfService.generatePdfView(currentUser.currentOrg, id);
  }
}
