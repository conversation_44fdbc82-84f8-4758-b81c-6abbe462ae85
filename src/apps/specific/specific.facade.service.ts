import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverUser } from '../../libs/model/common';
import { SpecificRiskRequest } from '../../libs/model/specific/model/SpecificRiskRequest';
import { SettingTypeEnums } from '../../libs/model/settings/SettingTypeEnums';
import { RoverBundleCounterType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { BiddingDimensionHitsDetails, DiligenceBiddingResponse } from '../../libs/model/bidding/DiligenceBiddingResponse';
import { TenderDiligenceHistoryStatus } from '../../libs/enums/tender/DiligenceHistoryEnum';
import { retry } from '@kezhaozhao/qcc-utils';
import { DiligenceBiddingCompanyDetailPo } from '../../libs/model/bidding/model/DiligenceBiddingCompanyDetailPo';
import { pick } from 'lodash';
import { SettingsService } from '../settings/settings.service';
import { SpecificInterestRecordEntity } from '../../libs/entities/SpecificInterestRecordEntity';
import { SpecificRecordService } from './service/specific.record.service';
import { SpecificInterestCompany } from '../../libs/entities/SpecificInterestCompany';
import { SpecificServiceHelper } from './helper/specific.service.helper';
import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import MyOssService from '../basic/my-oss.service';
import * as Bluebird from 'bluebird';
import { SearchDiligenceBiddingRequest } from '../../libs/model/bidding/SearchDiligenceBiddingRequest';
import { SecurityService } from '../../libs/config/security.service';
import { RemarkDiligenceBiddingRequest } from '../../libs/model/bidding/RemarkDiligenceBiddingRequest';
import { SpecificRemarkService } from './service/specific.remark.service';
import { SpecificRemarkEntity } from '../../libs/entities/SpecificRemarkEntity';
import { DiligenceBiddingHistoryRequest } from '../../libs/model/bidding/DiligenceBiddingHistoryRequest';
import { SpecificDimensionKeyEnums } from '../../libs/constants/specific.dimension.constant';
import { BatchSpecificDiligenceDetailEntity } from '../../libs/entities/BatchSpecificDiligenceDetailEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BatchEntity } from '../../libs/entities/BatchEntity';
import { BatchTypeEnums } from '../../libs/enums/batch/BatchTypeEnums';
import { BatchBusinessTypeEnums } from '../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchStatusEnums } from '../../libs/enums/batch/BatchStatusEnums';
import { BatchJobEntity } from '../../libs/entities/BatchJobEntity';
import { SpecificDiligenceRecord } from '../../libs/model/batch/po/parse/ParsedRecordBase';
import { JobMonitorMessagePO, JobMonitorMessageTypeEnums } from '../../libs/model/batch/po/message/JobMonitorMessagePO';
import { BatchSpecificDiligenceEntity } from '../../libs/entities/BatchSpecificDiligenceEntity';
import { checkSpecificPaidCount, getSpecificDiligencePaidCount } from './common/specific.utils';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { QueueService } from '../../libs/config/queue.service';
import { DiligenceBiddingRequest } from '../../libs/model/bidding/DiligenceBiddingRequest';
import { TimeInterval3Hour } from '../../libs/constants/common';

@Injectable()
export class SpecificFacadeService {
  private logger: Logger = QccLogger.getLogger(SpecificFacadeService.name);
  public batchJobMonitorQueue: RabbitMQ;

  constructor(
    private readonly settingService: SettingsService,
    private readonly specificRemarkService: SpecificRemarkService,
    private readonly specificRecordService: SpecificRecordService,
    private readonly bundleService: RoverBundleService,
    private readonly specificServiceHelper: SpecificServiceHelper,
    private readonly myOssService: MyOssService,
    private readonly securityService: SecurityService,
    private readonly queueService: QueueService,
    @InjectRepository(BatchEntity) protected readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchJobEntity) private readonly batchJobRepo: Repository<BatchJobEntity>,
    @InjectRepository(BatchSpecificDiligenceDetailEntity) private readonly batchSpecificDiligenceDetailRepo: Repository<BatchSpecificDiligenceDetailEntity>,
    @InjectRepository(BatchSpecificDiligenceEntity) private readonly batchSpecificDiligenceRepo: Repository<BatchSpecificDiligenceEntity>,
    @InjectRepository(SpecificInterestCompany) private readonly specificInterestCompanyRepo: Repository<SpecificInterestCompany>,
  ) {
    this.batchJobMonitorQueue = this.queueService.batchJobMonitorQueue;
  }

  /**
   * 特定利益关系排查
   * @param currentUser
   * @param params
   */
  async scanSpecificRisk(currentUser: RoverUser, params: SpecificRiskRequest) {
    const { userId, departments, currentOrg: orgId } = currentUser;
    const { keyNoAndNames, projectNo, projectName } = params;
    const specificConfigEntity = await this.settingService.getOrgLatestSettings(orgId, SettingTypeEnums.specific);
    const companyCount = params.keyNos.length;
    const paid = checkSpecificPaidCount(companyCount, specificConfigEntity);
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceSpecificQuantity);
    await bundleCounter.increase(paid);

    const recordRes: DiligenceBiddingResponse = {
      totalHits: 0,
      dimensionHitsDetails: [],
    };
    try {
      // 保存排查记录和排查公司记录
      const specificInterestRecordEntity = Object.assign(new SpecificInterestRecordEntity(), {
        orgId,
        depId: departments?.[0],
        status: TenderDiligenceHistoryStatus.Processing,
        projectNo: projectNo,
        projectName: projectName,
        operator: userId,
        orgSettingsId: specificConfigEntity.id,
        companyCount: companyCount,
      });
      const history = await retry(() => this.specificRecordService.saveSpecificInterestRecord(specificInterestRecordEntity), 2);
      const dimensionSettings = await this.settingService.getActiveSpecificDimensionSettings(orgId);
      // 执行排查
      const hitDetails = await this.specificServiceHelper.doSpecificRiskScan(params, dimensionSettings, orgId);

      const details = await this.specificServiceHelper.analyzeAndOverviewHitDetails(hitDetails, recordRes, dimensionSettings, keyNoAndNames);
      const detailFile = await this.specificServiceHelper.saveSpecificDiligenceDetail(details);

      specificInterestRecordEntity.companyList = recordRes.companyOverviews?.map((x) =>
        Object.assign(new SpecificInterestCompany(), {
          orgId,
          companyId: x.keyNo,
          companyName: x.name,
          status: x.status,
          level: x.level,
          operator: userId,
          recordId: history.id,
          details: Object.assign(new DiligenceBiddingCompanyDetailPo(), pick(x, ['keyNo', 'name', 'details'])),
        }),
      );
      specificInterestRecordEntity.status = TenderDiligenceHistoryStatus.Done;
      specificInterestRecordEntity.result = recordRes.result;
      specificInterestRecordEntity.remarkResult = specificInterestRecordEntity.result;
      specificInterestRecordEntity.description = recordRes.description;
      specificInterestRecordEntity.detailFile = detailFile;
      await this.specificRecordService.saveRecord(specificInterestRecordEntity);
      return (await this.searchDetail(currentUser.currentOrg, [specificInterestRecordEntity.id]))[0];
    } catch (e) {
      await bundleCounter.decrease(paid);
      this.logger.error('tender diligence error:' + e.message);
      throw e;
    }
  }

  /**
   * 重新排查特定利益关系
   * @param currentUser
   * @param recordId
   */
  async reScanSpecificRisk(currentUser: RoverUser, recordId: number) {
    const { currentOrg: orgId } = currentUser;
    //获取旧排查详情
    const preRecord = await this.specificRecordService.getSpecificInterestRecord(orgId, recordId);
    const params = this.generateScanParams(preRecord);
    return this.scanSpecificRisk(currentUser, params);
  }

  private generateScanParams(preRecord: SpecificInterestRecordEntity) {
    //筛选出入围公司列表
    const companyList = preRecord.companyList.filter((c) => c.status == 1);
    if (!companyList.length) {
      throw new BadRequestException({ error: `不存在的入围公司` });
    }
    return Object.assign(new SpecificRiskRequest(), {
      orgId: preRecord.orgId,
      keyNos: companyList.map((c) => c.companyId),
      projectNo: preRecord.projectNo,
      projectName: preRecord.projectName,
      keyNoAndNames: companyList.map((c) => pick(c, ['companyId', 'companyName'])),
    });
  }

  async searchDetail(currentOrg: number, recordIds: number[]) {
    const specificInterestRecords = await this.specificRecordService.getSpecificInterestRecords(currentOrg, recordIds);

    return await Bluebird.map(
      specificInterestRecords,
      async (entity) => {
        if (entity?.detailFile) {
          entity.details = await this.myOssService.getObject(entity?.detailFile);
          //维度排序处理
          await this.specificServiceHelper.detailsSort(entity.details);
        }
        if (entity?.remark?.length) {
          entity.remark.sort((a, b) => b.id - a.id);
          entity.remark.forEach((e) => {
            e.details?.attachments?.forEach((x) => {
              x.fileUrl = this.myOssService.signSingleUrl(x.fileUrl);
            });
          });
        }
        return entity;
      },
      { concurrency: 10 },
    );
  }

  async searchDetailById(currentOrg: number, recordId: number) {
    const specificInterestRecord = await this.specificRecordService.getSpecificInterestRecord(currentOrg, recordId);
    if (specificInterestRecord) {
      if (specificInterestRecord?.detailFile) {
        specificInterestRecord.details = await this.myOssService.getObject(specificInterestRecord?.detailFile);
        //维度排序处理
        await this.specificServiceHelper.detailsSort(specificInterestRecord.details);
      }
      if (specificInterestRecord?.remark?.length) {
        specificInterestRecord.remark.sort((a, b) => b.id - a.id);
        specificInterestRecord.remark.forEach((e) => {
          e.details?.attachments?.forEach((x) => {
            x.fileUrl = this.myOssService.signSingleUrl(x.fileUrl);
          });
        });
      }
      return specificInterestRecord;
    }
  }

  async search(currentUser: RoverUser, postData: SearchDiligenceBiddingRequest) {
    const { pageIndex, pageSize } = postData;
    try {
      const { by, userIds } = this.securityService.checkScopeByPermissionIds(currentUser, [10191, 10195]);
      const result = await this.specificRecordService.search(currentUser, postData, by, userIds);
      const { pageSize, pageIndex, data, total, dataOrderMap } = result;
      if (data.length > 0) {
        await Bluebird.map(data, async (s) => {
          const nowDate = new Date();
          if (s.status === 0 && s.updateDate.getTime() < nowDate.getTime() - TimeInterval3Hour) {
            await this.specificRecordService.updateRecord(s.id, { updateDate: nowDate, status: 2 });
            s.status = 2;
            s.updateDate = nowDate;
          }
          if (s.remark?.length) {
            // remark按时间倒序
            s.remark.sort((a, b) => b.id - a.id);
          }
          s.details = await this.myOssService.getObject(s?.detailFile);
        });
        data.sort((a, b) => dataOrderMap.get(a.id) - dataOrderMap.get(b.id));
      }
      return {
        pageSize,
        pageIndex,
        data,
        total,
      };
    } catch (e) {
      if (e instanceof ForbiddenException) {
        return {
          pageSize,
          pageIndex,
          data: [],
          total: 0,
        };
      }
    }
  }

  async remark(id: number, currentUser: RoverUser, postData: RemarkDiligenceBiddingRequest) {
    const { currentOrg: orgId } = currentUser;
    const recordEntity = await this.specificRecordService.getSpecificInterestRecord(orgId, id);
    if (!recordEntity) {
      throw new BadRequestException();
    }
    const [, remark] = await Bluebird.all([
      this.specificRecordService.updateRecord(id, { remarkResult: postData.status }),
      this.specificRemarkService.saveRemark(
        Object.assign(new SpecificRemarkEntity(), {
          orgId,
          recordId: id,
          status: postData.status,
          operator: currentUser.userId,
          details: postData,
        }),
      ),
    ]);
    return remark;
  }

  async edit(id: number, currentUser: RoverUser, postData: DiligenceBiddingHistoryRequest) {
    const { currentOrg: orgId } = currentUser;
    const recordEntity = await this.specificRecordService.getSpecificInterestRecord(orgId, id);
    if (!recordEntity) {
      throw new BadRequestException();
    }
    return this.specificRecordService.updateRecord(id, {
      projectNo: postData.projectNo,
      projectName: postData.projectName,
    });
  }

  async getResultStatistics(currentUser: RoverUser) {
    const { currentOrg: orgId } = currentUser;
    try {
      const { by, userIds } = this.securityService.checkScopeByPermissionIds(currentUser, [10191, 10195]);
      return await this.specificRecordService.getResultStatistics(orgId, by, userIds);
    } catch (e) {
      if (e instanceof ForbiddenException) {
        return {
          data: [],
          total: 0,
        };
      }
    }
  }

  async operatorList(currentUser: RoverUser) {
    const { currentOrg: orgId } = currentUser;
    try {
      const { by, userIds } = this.securityService.checkScopeByPermissionIds(currentUser, [10191, 10195]);
      return await this.specificRecordService.getOperatorList(orgId, by, userIds);
    } catch (e) {
      if (e instanceof ForbiddenException) {
        return [];
      }
    }
  }

  async checkCountLimit(currentUser: RoverUser, num: number) {
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceSpecificQuantity);
    await bundleCounter.check(num);
  }

  /**
   * 记录批量特定利益关系排查批次内各维度命中情况
   * @param batchId
   * @param diligence
   */
  async saveBatchDiligenceDetail(batchId: number, diligence: SpecificInterestRecordEntity) {
    const detailItems: BatchSpecificDiligenceDetailEntity[] = [];
    diligence?.details?.dimensionHitsDetails?.forEach((hd) => {
      if (hd?.subDimension?.length) {
        hd.subDimension.forEach((sd) => {
          const item = Object.assign(new BatchSpecificDiligenceDetailEntity(), {
            batchId,
            diligenceId: diligence.id,
            groupKey: hd.key,
            groupHits: hd.totalHits,
            dimKey: sd.key,
            dimHits: sd.totalHits,
          });
          detailItems.push(item);
        });
      } else {
        detailItems.push(
          Object.assign(new BatchSpecificDiligenceDetailEntity(), {
            batchId,
            diligenceId: diligence.id,
            groupKey: hd.key,
            groupHits: hd.totalHits,
          }),
        );
      }
    });
    if (detailItems.length) {
      try {
        await this.batchSpecificDiligenceDetailRepo.delete({ batchId, diligenceId: diligence.id });
        await this.batchSpecificDiligenceDetailRepo.save(detailItems);
      } catch (error) {
        if (error.code != 'ER_DUP_ENTRY') {
          this.logger.error(error);
        }
        return 0;
      }
    }
    return detailItems.length;
  }

  async getStock(currentUser: RoverUser) {
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceSpecificQuantity);
    return (await bundleCounter.getStock()).stock;
  }

  /**
   * 批量特定利益关系异步排查
   * @param currentUser
   * @param params
   */
  async scanSpecificRiskAsync(currentUser: RoverUser, params: SpecificRiskRequest) {
    const { userId, departments, currentOrg: orgId } = currentUser;
    const { keyNoAndNames, projectNo, projectName } = params;
    const specificConfigEntity = await this.settingService.getOrgLatestSettings(orgId, SettingTypeEnums.specific);
    const companyCount = params.keyNos.length;
    const paid = checkSpecificPaidCount(companyCount, specificConfigEntity);
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceSpecificQuantity);
    await bundleCounter.increase(paid);
    try {
      // 保存排查记录和排查公司记录
      const specificInterestRecordEntity = Object.assign(new SpecificInterestRecordEntity(), {
        orgId,
        depId: departments?.[0],
        status: TenderDiligenceHistoryStatus.Processing,
        projectNo: projectNo,
        projectName: projectName,
        operator: userId,
        orgSettingsId: specificConfigEntity.id,
        companyCount: companyCount,
      });
      const history = await retry(() => this.specificRecordService.saveSpecificInterestRecord(specificInterestRecordEntity), 2);
      history.companyList = keyNoAndNames.map((x) =>
        Object.assign(new SpecificInterestCompany(), {
          orgId,
          companyId: x.companyId,
          companyName: x.companyName,
          operator: userId,
          recordId: history.id,
        }),
      );
      await this.specificRecordService.saveRecord(history);
      await this.createBatchAndJob(params, history);
      return history;
    } catch (e) {
      await bundleCounter.decrease(paid);
      this.logger.error('tender diligence error:' + e.message);
      throw e;
    }
  }

  private async createBatchAndJob(params: SpecificRiskRequest, recordEntity: SpecificInterestRecordEntity) {
    const { operator: userId, orgId, depId } = recordEntity;
    try {
      const paidCount = getSpecificDiligencePaidCount(params.keyNos.length);
      const batchEntity = await this.batchRepo.save(
        Object.assign(new BatchEntity(), {
          createDate: new Date(),
          orgId,
          depId,
          creatorId: userId,
          batchType: BatchTypeEnums.Import,
          businessType: BatchBusinessTypeEnums.Specific_Async_Diligence,
          // recordCount: succeedItems.length + failedItems.length,
          status: BatchStatusEnums.Waiting,
          // comment: succeedItems.length === 0 ? '未识别到符合格式的数据' : '',
          // fileName,
          // originFile: originFileUrl,
          statisticsInfo: {
            recordCount: 0,
            duplicatedCount: 0,
            withholdingCount: paidCount,
            withholdingRecordCount: 0,
          },
          batchInfo: { params, diligenceId: recordEntity.id },
        }),
      );
      // 保存 batchDiligence 关系表
      await this.batchSpecificDiligenceRepo.save(
        Object.assign(new BatchSpecificDiligenceEntity(), {
          batchId: batchEntity.batchId,
          diligenceId: recordEntity.id,
          jobId: 0,
        }),
      );
      // 创建batchJob,
      // const { items: dimensionDefinitions } = await this.settingService.getTenderDimensionGroup(orgId);
      const dimensionSettings = await this.settingService.getActiveSpecificDimensionSettings(orgId);
      const jobs: BatchJobEntity[] = [];
      const baseRecord = Object.assign(new SpecificDiligenceRecord(), {
        diligenceId: recordEntity.id,
        dimensionSettings,
      });
      // 公司关系维度 JOB
      const companyDimensions = dimensionSettings.filter(
        (x) =>
          x.key === SpecificDimensionKeyEnums.ContactWay ||
          x.key === SpecificDimensionKeyEnums.UpAndDownRelation ||
          x.key === SpecificDimensionKeyEnums.GuaranteeRelation ||
          x.key === SpecificDimensionKeyEnums.OtherRelations,
      );
      if (companyDimensions.length) {
        const record = Object.assign(new SpecificDiligenceRecord(), baseRecord);
        record.dimensionSettings = companyDimensions;
        jobs.push(
          Object.assign(new BatchJobEntity(), {
            batchId: batchEntity.batchId,
            jobInfo: {
              items: [record],
              itemSize: 1,
            },
            status: BatchStatusEnums.Waiting,
          }),
        );
      }
      // 公司实际控制人、交叉持股、主要人员 JOB
      const actualControllerDimensions = dimensionSettings.filter(
        (x) =>
          x.key === SpecificDimensionKeyEnums.ActualController ||
          x.key === SpecificDimensionKeyEnums.CrossShareHolding ||
          x.key === SpecificDimensionKeyEnums.SameEmployee,
      );
      if (actualControllerDimensions.length) {
        const record = Object.assign(new SpecificDiligenceRecord(), baseRecord);
        record.dimensionSettings = actualControllerDimensions;
        jobs.push(
          Object.assign(new BatchJobEntity(), {
            batchId: batchEntity.batchId,
            jobInfo: {
              items: [record],
              itemSize: 1,
            },
            status: BatchStatusEnums.Waiting,
          }),
        );
      }
      if (jobs.length) {
        //批量保存
        await Bluebird.map(jobs, (job) => this.batchJobRepo.insert(job), { concurrency: 5 });
      }
      batchEntity.statisticsInfo.recordCount = jobs.length;
      await this.batchRepo.save(batchEntity);

      const msgBody: JobMonitorMessagePO = {
        batchId: batchEntity.batchId,
        batchType: batchEntity.batchType,
        businessType: batchEntity.businessType,
        startDate: Date.now(),
        batchTotalRecords: batchEntity.recordCount,
        operatorId: userId,
        orgId,
        index: 1,
        type: JobMonitorMessageTypeEnums.Scan,
      };
      const messageResponse = await this.batchJobMonitorQueue.sendMessageV2(msgBody, {
        ttl: 1,
        retries: 1,
        traceTags: [
          // @ts-ignore
          { key: 'batchId', val: batchEntity.batchId, overridable: true },
          {
            key: 'businessType',
            val: batchEntity.businessType.toString(),
            overridable: true,
          },
          // { key: 'businessId', val: _businessId, overridable: true },
        ],
      });
      this.logger.info(`batch(batchId=${batchEntity.batchId}) monitor message sent..., response=${messageResponse}`);
    } catch (error) {
      this.logger.info(`createBatchAndJob error: ${error.message}`);
      this.logger.error(error);
    }
  }

  /**
   * 特定利益关系排查成功后处理
   * @param params
   * @param hitDetails
   * @param diligenceId
   */
  async diligenceSuccess(params: DiligenceBiddingRequest, hitDetails: BiddingDimensionHitsDetails[], diligenceId: number) {
    const { orgId, keyNoAndNames } = params;
    const recordRes: DiligenceBiddingResponse = {
      totalHits: 0,
      dimensionHitsDetails: [],
    };
    const dimensionSettings = await this.settingService.getActiveSpecificDimensionSettings(orgId);

    const details = await this.specificServiceHelper.analyzeAndOverviewHitDetails(hitDetails, recordRes, dimensionSettings, keyNoAndNames);
    const detailFile = await this.specificServiceHelper.saveSpecificDiligenceDetail(details);

    const specificInterestRecordEntity = await this.specificRecordService.getSpecificInterestRecord(orgId, diligenceId);

    Object.assign(specificInterestRecordEntity, {
      status: TenderDiligenceHistoryStatus.Done,
      result: recordRes.result,
      description: recordRes.description,
      detailFile,
      remarkResult: recordRes.result,
      // 测试时看数据方便暂时开启，通过后关闭
      // details,
    });
    //更新 companyList
    const companyList: SpecificInterestCompany[] = await this.specificInterestCompanyRepo.find({
      recordId: specificInterestRecordEntity.id,
      orgId: specificInterestRecordEntity.orgId,
    });

    specificInterestRecordEntity.companyList = companyList.map((x) => {
      const companyOverview = recordRes.companyOverviews.find((y) => y.keyNo == x.companyId);
      return Object.assign(new SpecificInterestCompany(), x, {
        status: companyOverview.status,
        level: companyOverview.level,
        details: Object.assign(new DiligenceBiddingCompanyDetailPo(), pick(companyOverview, ['keyNo', 'name', 'details'])),
      });
    });
    await this.specificRecordService.saveRecord(specificInterestRecordEntity);
  }

  async diligenceError(currentUser: RoverUser, diligenceId: number) {
    const specificInterestRecordEntity = await this.specificRecordService.getSpecificInterestRecord(currentUser.currentOrg, diligenceId);
    // 更新记录状态为失败
    await this.specificRecordService.updateRecord(diligenceId, { status: TenderDiligenceHistoryStatus.Error });
    // 退回排查额度
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceSpecificQuantity);
    const paid = getSpecificDiligencePaidCount(specificInterestRecordEntity.companyCount);
    await bundleCounter.decrease(paid);
  }

  async reScanSpecificRiskAsync(currentUser: RoverUser, recordId: number) {
    const { currentOrg: orgId } = currentUser;
    //获取旧排查详情
    const preRecord = await this.specificRecordService.getSpecificInterestRecord(orgId, recordId);
    const params = this.generateScanParams(preRecord);
    if (params.keyNos.length < 21) {
      return this.scanSpecificRisk(currentUser, params);
    }
    return this.scanSpecificRiskAsync(currentUser, params);
  }

  async retryDiligence(currentUser: RoverUser, diligenceId: number) {
    const history = await this.retryDiligenceCheck(currentUser, diligenceId, TenderDiligenceHistoryStatus.Error);
    const params = this.generateScanParams(history);
    // 创建batch做异步排查
    await this.createBatchAndJob(params, history);
    return history;
  }

  async retryDiligenceCheck(currentUser: RoverUser, diligenceId: number, status: number): Promise<SpecificInterestRecordEntity> {
    const { currentOrg: orgId } = currentUser;

    //获取旧排查详情
    const history = await this.specificRecordService.getSpecificInterestRecord(orgId, diligenceId, status);

    //重新排查，按照原排查流程扣减额度
    const specificConfigEntity = await this.settingService.getOrgSettings(orgId, SettingTypeEnums.specific, history.orgSettingsId);
    const paid = checkSpecificPaidCount(history.companyCount, specificConfigEntity);
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceSpecificQuantity);
    await bundleCounter.increase(paid);
    //把旧排查记录状态改为0，重新排查
    await this.specificRecordService.updateRecord(diligenceId, {
      status: TenderDiligenceHistoryStatus.Processing,
    });
    history.status = TenderDiligenceHistoryStatus.Processing;
    return history;
  }
}
