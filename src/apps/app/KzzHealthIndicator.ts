import { HealthIndicator, HealthIndicatorResult } from '@nestjs/terminus';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Injectable } from '@nestjs/common';
import { HealthCheckError } from '@godaddy/terminus';

@Injectable()
export class KzzHealthIndicator extends HealthIndicator {
  constructor(private readonly redisService: RedisService) {
    super();
  }
  async isRedisHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      await this.redisService.getClient().set('healthcheck', 'healthcheck');
      return this.getStatus(key, true);
    } catch (e) {
      throw new HealthCheckError('Redis check failed', {});
    }
  }
}
