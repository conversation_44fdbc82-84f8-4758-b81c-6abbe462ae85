import { Module } from '@nestjs/common';
import { RedisModule } from '@kezhaozhao/nestjs-redis';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from '../user/user.module';
import { Product, QCCBundleModule } from '@kezhaozhao/saas-bundle-service';
import { QccAuthModule } from '@kezhaozhao/saas-auth';
import { AliyunModule, QichachaModule, SmsModule } from '@kezhaozhao/qcc-utils';
import { ConfigService } from 'libs/config/config.service';
import { ConfigModule } from 'libs/config/config.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HandlebarsAdapter, MailerModule } from '@kezhaozhao/nest-mailer';
import { join } from 'path';
import { ThrottlerModule } from '@nestjs/throttler';
import { ThrottlerStorageRedisService } from 'nestjs-throttler-storage-redis';

@Module({
  imports: [
    ThrottlerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({ ttl: 60, limit: 200, storage: new ThrottlerStorageRedisService(configService.redis) }),
    }),
    EventEmitterModule.forRoot(),
    ConfigModule,
    TypeOrmModule.forRootAsync({
      useFactory: async (configService: ConfigService) => configService.typeorm,
      inject: [ConfigService],
    }),
    AliyunModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return {
          oss: configService.server.oss,
        };
      },
      inject: [ConfigService],
    }),
    RedisModule.forRootAsync({
      useFactory: (configService: ConfigService) => configService.redis, // or use async method
      inject: [ConfigService],
    }),
    QccAuthModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          jwt: configService.jwt,
          server: {
            comDomainService: configService.server.comDomainService,
            ssoService: configService.server.ssoService,
            appService: configService.server.appService,
            wxAdminService: configService.server.wxAdminService,
            saasService: configService.server.saasService,
            entService: configService.kzzServer.enterpriseService,
            bundleService: configService.kzzServer.bundleService,
          },
          sessionName: 'QCCSESSID',
          mobileSessionName: 'MQCCSESSID',
          redis: configService.redis,
          product: Product.Rover,
          validBundle: true,
        };
      },
      inject: [ConfigService],
    }),
    QichachaModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          comDomainService: configService.server.comDomainService,
          extDomainService: configService.server.extDomainService,
          appService: configService.server.appService,
          saasService: configService.server.saasService,
          bossService: configService.server.bossService,
          wxQccDomainService: configService.server.wxQccDomainService,
        };
      },
      inject: [ConfigService],
    }),
    SmsModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return configService.server.smsService;
      },
      inject: [ConfigService],
    }),
    QCCBundleModule.registerMock(
      {
        useFactory: async (configService: ConfigService) => {
          return {
            serviceURL: configService.kzzServer.bundleService,
            product: Product.Rover,
          };
        },
        inject: [ConfigService],
      },
      {},
    ),
    MailerModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        transport: configService.server.mailerService,
        defaults: {
          from: '"企查查" <<EMAIL>>',
        },
        template: {
          dir: join(__dirname, '..', '..', '..', 'templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
      inject: [ConfigService],
    }),
    UserModule,
  ],
  controllers: [],
})
export class AppTestModule {}
