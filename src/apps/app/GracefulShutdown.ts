import { Injectable, OnApplicationShutdown } from '@nestjs/common';
import * as Bluebird from 'bluebird';
import { single } from 'rxjs/operators';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { RoverEventEmitter, SystemEvents } from '../../libs/common/RoverEventEmitter';

@Injectable()
export class GracefulShutdown implements OnApplicationShutdown {
  private readonly logger: Logger = QccLogger.getLogger(GracefulShutdown.name);

  onApplicationShutdown(signal?: string): any {
    this.logger.warn(`Receive single: ${signal}`);
    RoverEventEmitter.getInstance().emit(SystemEvents.ProcessExit);
    process.env.GRACEFUL_SHUTDOWN = single.name;
    const delay = process.env.NODE_ENV === 'prod' ? 3000 : 100;
    return Bluebird.delay(delay).finally(() => {
      this.logger.warn(`after the dedicated delay ,will exit now `);
      process.kill(process.pid, 'SIGKILL');
    });
  }
}
