import { BadRequestException, Body, Controller, Post } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { SAASBundleCallbackModel } from 'libs//model/app/model/CallbackModel';
import { SAASBundleUsageResponse } from 'libs//model/app/model/SAASBundleUsageResponse';
import { UsageRequest } from 'libs//model/app/model/UsageRequest';
import { captureException } from '@sentry/node';
import { SaasService } from './saas.service';
import { CallbackException } from 'libs/exceptions/CallbackException';
import { SyncMessageModel } from 'libs//model/app/sycn-model/SyncMessageModel';

@Controller('saas')
@ApiTags('SAAS API')
export class CallbackController {
  private readonly logger: Logger = QccLogger.getLogger(CallbackController.name);

  constructor(private readonly saasService: SaasService) {}

  // @Post('message.handler')
  // @ApiOperation({ summary: 'rover消息推送' })
  // @ApiOkResponse({ type: AffectedResponse })
  // @ApiResponse({ status: 400, description: '参数错误' })
  // @ApiResponse({ status: 500, description: 'Server错误' })
  // public async postMessage(@Body() postData: MessageBody): Promise<AffectedResponse> {
  //   await this.saasService.postMessage(postData);
  //   return { affected: 1 };
  // }

  @Post('testSyncMsg')
  public async testSyncMsg(@Body() postData: SyncMessageModel) {
    await this.saasService.handleSyncMsg(postData);
    return { affected: 1 };
  }

  @Post('callback')
  @ApiOperation({ summary: 'saas 回调' })
  @ApiOkResponse()
  @ApiResponse({ status: 400, description: '参数错误' })
  @ApiResponse({ status: 500, description: 'Server错误' })
  public async saasCallback(@Body() postData: SAASBundleCallbackModel) {
    this.logger.debug('receive callback after payOrder', postData);
    this.logger.info(`saas callback: ${JSON.stringify(postData)}`);
    try {
      switch (postData?.eventType) {
        case 'ADD_SERVICE':
          // await retry(() => this.saasService.addService(postData.eventData), 5);
          break;
        case 'UPDATE_SERVICE':
          // await retry(() => this.saasService.updateService(postData.eventData), 5);
          break;
        case 'SERVICE_EXPIRE':
          // await retry(() => this.saasService.expireService(postData.eventData), 5);
          break;
        case 'ADD_USER':
          // await retry(() => this.saasService.addUser(postData.eventData), 5);
          // await this.saasService.addUser(postData.eventData);
          break;
        case 'DELETE_USER':
          // await retry(() => this.saasService.deleteUser(postData.eventData), 5);
          break;
        case 'CHANGE_ADMIN':
          // await retry(() => this.saasService.changeAdmin(postData.eventData), 5);
          break;
        case 'CHANGE_USER':
          // 变更用户信息
          // await retry(() => this.saasService.changeUserInfo(postData.eventData), 5);
          break;
        case 'DELETE_TEAM':
          // 解散企业
          // await retry(() => this.saasService.deleteTeam(postData.eventData), 5);
          break;
        default:
          this.logger.error('eventType error');
          throw new BadRequestException('eventType error');
      }
    } catch (error) {
      this.logger.error(error);
      captureException(new CallbackException(error.messsage));
    }
  }

  @Post('bundle')
  @ApiOperation({ summary: '获取套餐使用量' })
  @ApiOkResponse({ description: '套餐余量', type: SAASBundleUsageResponse })
  @ApiResponse({ status: 400, description: '参数错误' })
  @ApiResponse({ status: 500, description: 'Server错误' })
  public getTeamBundleUsage(@Body() request: UsageRequest) {
    this.logger.debug('receive bundle usage request: ', request);
    const result: SAASBundleUsageResponse = new SAASBundleUsageResponse();
    result.exportQuantity = 0;
    result.subscriptionQuantity = 0;
    return result;
  }
}
