import { Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { RedisModule } from '@kezhaozhao/nestjs-redis';
import { KzzHealthIndicator } from './KzzHealthIndicator';
import { HealthController } from './health.controller';
import { GracefulShutdown } from './GracefulShutdown';
import { HealthModule } from '@kezhaozhao/nest-sentinel';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SaasService } from './saas.service';
import { CallbackController } from './callback.controller';
import { AccountModule } from '../account/account.module';
import { CustomerModule } from '../customer/customer.module';
import { ElementModule } from '../element/element.module';
import { UserModule } from '../user/user.module';
import { BlacklistModule } from '../blacklist/blacklist.module';
import { SchemaModule } from '../schema/schema.module';
import { DiligenceModule } from '../diligence/diligence.module';
import { SettingsModule } from '../settings/settings.module';
import { PersonModule } from '../person/person.module';
import { BatchModule } from '../batch/batch.module';
import { MessageModule } from '../message/message.module';
import { ChartsModule } from '../diligence/charts/charts.module';
import { CompanySearchModule } from '../company/company-search.module';
import { Product, QCCBundleModule } from '@kezhaozhao/saas-bundle-service';
import { QccAuthModule } from '@kezhaozhao/saas-auth';
import { AliyunModule, QichachaModule, SmsModule } from '@kezhaozhao/qcc-utils';
import { TerminusModule } from '@nestjs/terminus';
import { ConfigService } from 'libs/config/config.service';
import { ConfigModule } from 'libs/config/config.module';
import { UserEntity } from 'libs/entities/UserEntity';
import { RoverSessionGuard } from 'libs/guards/RoverSession.guard';
import { InternalModule } from '../internal/internal.module';
import { RoverScheduleModule } from 'apps/schedule/schedule.module';
import { DataModule } from '../data/data.module';
import { MonitorModule } from '../monitor/monitor.module';
import { UdeskModule } from 'apps/udesk/udesk.module';
import { BiddingModule } from '../bidding/bidding.module';
import { join } from 'path';
import { HandlebarsAdapter, MailerModule } from '@kezhaozhao/nest-mailer';
import { OpenApiModule } from 'apps/openapi/openapi.module';
import { OpenApiJwtGuard } from 'libs/guards/openapi.jwt.guard';
import { OpenApiResourceEntity } from '../../libs/entities/OpenApiResourceEntity';
import { OpLogModule } from '../oplog/oplog.module';
import { TenderAlertModule } from '../tenderAlert/tenderAlert.module';
import { BenchModule } from '../bench/bench.module';
import { DevapiModule } from '../devapi/devapi.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BasicModule } from '../basic/basic.module';
import { CanalStreamModule } from '../canal_stream/CanalStreamModule';
import { RoverSocketModule } from '../socket/rover.socket.module';
import { UserConfigurationModule } from '../user_configuration/user.configuration.module';
import { SpecificModule } from '../specific/specific.module';
import { AiAnalyzerModule } from '../ai_analyzer/ai.analyzer.module';
import { SystemInternalModule } from '../system/system.internal.module';
import { PotentialModule } from '../potential/potential.module';
import { PackageUsageModule } from '../usage/package.usage.module';
import { ThrottlerModule } from '@nestjs/throttler';
import { ThrottlerStorageRedisService } from 'nestjs-throttler-storage-redis';

@Module({
  imports: [
    ConfigModule,
    TerminusModule,
    ThrottlerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({ ttl: 60, limit: 200, storage: new ThrottlerStorageRedisService(configService.redis) }),
    }),
    HealthModule.registerAsync({
      useFactory: () => {
        return {
          terminationGracePeriodSeconds: 60,
          canary: false,
          preStopPauseSeconds: 5,
          gracefulShutdown: {
            pauseSecond: 3,
          },
        };
      },
    }),
    TypeOrmModule.forRootAsync({
      useFactory: async (configService: ConfigService) => configService.typeorm,
      inject: [ConfigService],
    }),
    AliyunModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return {
          oss: configService.server.oss,
        };
      },
      inject: [ConfigService],
    }),
    RedisModule.forRootAsync({
      useFactory: (configService: ConfigService) => configService.redis, // or use async method
      inject: [ConfigService],
    }),
    MailerModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        transport: configService.server.mailerService,
        defaults: {
          from: '"企查查" <<EMAIL>>',
        },
        template: {
          dir: join(__dirname, '..', '..', '..', 'templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
      inject: [ConfigService],
    }),
    QccAuthModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          jwt: configService.jwt,
          server: {
            comDomainService: configService.server.comDomainService,
            ssoService: configService.server.ssoService,
            appService: configService.server.appService,
            wxAdminService: configService.server.wxAdminService,
            saasService: configService.server.saasService,
            entService: configService.kzzServer.enterpriseService,
            bundleService: configService.kzzServer.bundleService,
            authService: configService.kzzServer.authService,
          },
          sessionName: 'QCCSESSID',
          mobileSessionName: 'MQCCSESSID',
          redis: configService.redis,
          product: Product.Rover,
          validBundle: true,
        };
      },
      inject: [ConfigService],
    }),
    QichachaModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          comDomainService: configService.server.comDomainService,
          extDomainService: configService.server.extDomainService,
          appService: configService.server.appService,
          saasService: configService.server.saasService,
          bossService: configService.server.bossService,
          wxQccDomainService: configService.server.wxQccDomainService,
        };
      },
      inject: [ConfigService],
    }),
    SmsModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return configService.server.smsService;
      },
      inject: [ConfigService],
    }),
    QCCBundleModule.register({
      useFactory: async (configService: ConfigService) => {
        return {
          serviceURL: configService.kzzServer.bundleService,
          product: Product.Rover,
        };
      },
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([UserEntity, OpenApiResourceEntity]),
    DataModule,
    CompanySearchModule,
    AccountModule,
    CustomerModule,
    ElementModule,
    UserModule,
    BlacklistModule,
    SchemaModule,
    DiligenceModule,
    SettingsModule,
    UserConfigurationModule,
    PersonModule,
    BatchModule,
    MessageModule,
    ChartsModule,
    InternalModule,
    RoverScheduleModule,
    MonitorModule,
    UdeskModule,
    BiddingModule,
    OpenApiModule,
    OpLogModule,
    TenderAlertModule,
    BenchModule,
    BasicModule,
    DevapiModule.register(),
    EventEmitterModule.forRoot(),
    CanalStreamModule,
    RoverSocketModule,
    SpecificModule,
    AiAnalyzerModule,
    SystemInternalModule,
    PotentialModule,
    PackageUsageModule,
  ],
  controllers: [AppController, HealthController, CallbackController],
  providers: [ConfigService, KzzHealthIndicator, GracefulShutdown, SaasService, RoverSessionGuard, OpenApiJwtGuard],
  exports: [KzzHealthIndicator],
})
export class AppModule {}
