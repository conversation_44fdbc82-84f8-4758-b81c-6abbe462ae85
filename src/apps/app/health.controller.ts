import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService } from '@nestjs/terminus';
import { KzzHealthIndicator } from './KzzHealthIndicator';
import { ApiTags } from '@nestjs/swagger';

@Controller('health')
@ApiTags('Home')
export class HealthController {
  constructor(private health: HealthCheckService, private readonly kzz: KzzHealthIndicator) {}

  @Get()
  @HealthCheck()
  healthCheck() {
    return this.health.check([async () => this.kzz.isRedisHealthy('redis')]);
  }
}
