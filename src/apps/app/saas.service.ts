import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { SyncMessageModel, SyncSubject } from 'libs//model/app/sycn-model/SyncMessageModel';
import { UserEntity } from 'libs/entities/UserEntity';
import { ConfigService } from 'libs/config/config.service';
import { UserSyncOperation } from 'libs//model/app/sycn-model/UserSyncOperation';
import { pick } from 'lodash';
import { OrgSyncOperation } from 'libs//model/app/sycn-model/OrgSyncOperation';
import { ServiceCode } from 'libs/constants/common';
import { GroupsEntity } from 'libs/entities/GroupsEntity';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { retry } from '@kezhaozhao/qcc-utils';
import { LabelEntity } from 'libs/entities/LabelEntity';
import { QueueService } from '../../libs/config/queue.service';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';

@Injectable()
export class SaasService {
  private readonly logger: Logger = QccLogger.getLogger(SaasService.name);
  private roverSyncQueue: RabbitMQ;

  constructor(
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly configService: ConfigService,
    private readonly queueService: QueueService,
    private readonly httpUtils: HttpUtilsService,
  ) {
    this.roverSyncQueue = this.queueService.roverSyncQueue;
    // 接受rover同步消息
    this.roverSyncQueue.consume(this.handleSyncMsg.bind(this)).catch((err) => this.logger.error(err));
  }

  public async handleSyncMsg(msg: SyncMessageModel) {
    this.logger.info(`rover hand sync msg for ${msg.subject}! operation: ${msg.operation}, message: ${JSON.stringify(msg)}`);
    try {
      switch (msg.subject) {
        case SyncSubject.Org:
          switch (msg.operation) {
            case OrgSyncOperation.Create:
              // TODO: 只对service_code 是 rover的org开通增加默认分组
              switch (msg?.data?.serviceCode) {
                case ServiceCode.SAAS_ROVER:
                case ServiceCode.KZZ_ENT:
                  await this.addDefaultGroup(msg?.data?.org);
                  await this.addDefaultLabel(msg?.data?.org);
                  await this.addMonitorDefaultGroup(msg?.data?.org);
                  await this.saveUser(msg.data.user);
                  await retry(() => this.initOrgToNebula(msg?.data?.org), 2);
                  break;
                default:
                  break;
              }
            default:
              break;
          }
          break;
        case SyncSubject.Department:
          break;
        case SyncSubject.LoginUser:
          break;
        case SyncSubject.User:
          // this.logger.info(`hand sync msg for User! operation: ${msg.operation}`);
          switch (msg.operation) {
            case UserSyncOperation.Update:
            case UserSyncOperation.Add:
              await this.saveUser(msg.data);
              break;
            case UserSyncOperation.ChangeDepartment:
              break;
            case UserSyncOperation.Delete:
              await this.deleteUser(msg.data, true);
              break;
            case UserSyncOperation.Disable:
              await this.deleteUser(msg.data, false);
              break;
            default:
              break;
          }
          break;
        default:
          break;
      }

      this.logger.info(`rover hand sync msg for ${msg.subject}! operation: ${msg.operation} success!`);
    } catch (error) {
      // throw error;
      this.logger.error(`rover handleSyncMsg error: ${error}`);
      this.logger.error(error);
    }
  }

  public async initOrgToNebula(data: any) {
    const { orgId, name } = data;
    await this.httpUtils.postRequest(this.configService.roverGraphServer.initOrg, { orgId, name });
  }

  public async addMonitorDefaultGroup(data: any) {
    if (!data?.orgId) {
      this.logger.error(`rover handleSyncMsg error:  orgId not found`);
    }
    const orgId: number = data.orgId;

    const groupCount = await this.entityManager.count(MonitorGroupEntity, { orgId });
    if (groupCount < 1) {
      await this.entityManager.save(MonitorGroupEntity, [{ orgId, ownerId: -1, name: '默认分组', order: -1 }]);
    }
    // this.entityManager.upsert(data, ['orgId', 'name', 'groupType']);
  }

  public async addDefaultGroup(data: any) {
    if (!data?.orgId) {
      this.logger.error(`rover handleSyncMsg error:  orgId not found`);
    }
    const orgId: number = data.orgId;

    const groupCount = await this.entityManager.count(GroupsEntity, { orgId });
    if (groupCount < 1) {
      await this.entityManager.save(GroupsEntity, [
        // 创建 合作伙伴 默认分组
        { orgId, name: '供应商', groupType: 1, order: 5 },
        { orgId, name: '客户', groupType: 1, order: 4 },
        { orgId, name: '经销商', groupType: 1, order: 3 },
        { orgId, name: '竞企', groupType: 1, order: 2 },
        { orgId, name: '分包商', groupType: 1, order: 1 },
        // 创建 人员 默认分组
        { orgId, name: '现任员工', groupType: 2, order: 4 },
        { orgId, name: '曾被处罚前员工', groupType: 2, order: 3 },
      ]);
    }
    // this.entityManager.upsert(data, ['orgId', 'name', 'groupType']);
  }

  public async addDefaultLabel(data: any) {
    if (!data?.orgId) {
      this.logger.error(`handleSyncMsg error:  orgId not found`);
    }
    const orgId: number = data.orgId;
    const labelCount = await this.entityManager.count(LabelEntity, { orgId });
    if (labelCount < 1) {
      await this.entityManager.save(LabelEntity, [
        { orgId, name: '潜在供应商' },
        { orgId, name: '合格供应商' },
        { orgId, name: '核心供应商' },
        { orgId, name: '战略供应商' },
        { orgId, name: '淘汰供应商' },
      ]);
    }
  }

  // 收到添加用户消息
  public async saveUser(data: UserEntity) {
    const { userId } = data;
    const userInfo: any = pick(data, [
      'orgId',
      'email',
      'phone',
      'createDate',
      'updateDate',
      'name',
      'active',
      'loginUserId',
      'guid',
      'lastLogin',
      'bUserId',
      'faceimg',
      'position',
      'staffId',
    ]);
    const user = await this.entityManager.findOne(UserEntity, userId);
    let dbUser;
    if (user) {
      dbUser = await this.entityManager.update(UserEntity, { userId: userId }, userInfo);
    } else {
      dbUser = await this.entityManager.save(Object.assign(new UserEntity(), { userId: userId }, userInfo));
    }
    // if (userRoles?.length) {
    //   await this.bindUserRole({ roleIds: userRoles.map((userRole) => userRole.roleId), userId: userRoles[0].userId });
    // }
    return dbUser;
  }

  // 收到删除用户消息
  public async deleteUser(userInfo: any, realDelete: boolean) {
    if (realDelete) {
      await this.entityManager.delete(UserEntity, { userId: userInfo.userId });
    } else {
      // 软删除用户
      await this.entityManager.update(
        UserEntity,
        { userId: userInfo.userId },
        {
          active: 0,
        },
      );
    }
  }

  // public async bindUserRole(data: any) {
  //   if (data?.userId && data?.roleIds) {
  //     await this.entityManager.delete(UserRoleEntity, {
  //       userId: data.userId,
  //     });
  //     await this.entityManager.save(
  //       data.roleIds.map((roleId) => {
  //         return Object.assign(new UserRoleEntity(), {
  //           userId: data.userId,
  //           roleId: roleId,
  //         });
  //       }),
  //     );
  //   }
  // }
}
