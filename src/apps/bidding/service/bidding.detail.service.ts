/* eslint-disable @typescript-eslint/naming-convention */
import { BadRequestException, Injectable } from '@nestjs/common';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { TenderService } from 'apps/data/source/tender.service';
import {
  BidCollusiveDetailListQueryParams,
  BidCollusiveDetailsQueryParams,
  HitDetailsBidBaseQueryParams,
  JointBiddingAnalysisDetailsQueryParams,
} from 'libs/model/diligence/pojo/req&res/details/request/HitDetailsBidBaseQueryParams';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { GetHitDetailsParamBase } from 'libs/model/diligence/pojo/req&res/details/GetHitDetailsParam';
import { compact, pick, set, union, uniq } from 'lodash';
import { CompanyJoinBiddingModel } from '../../../libs/model/diligence/pojo/dimension/CompanyJoinBiddingModel';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/pojo/req&res/details/response';
import { CaseService } from '../../data/source/case.service';
import { ConfigService } from '../../../libs/config/config.service';
import { SettingsService } from '../../settings/settings.service';
import { DimensionDefinitionPO, SubDimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import { IndicatorTypeEnums } from '../../../libs/model/settings/IndicatorTypeEnums';
import { DimensionScorePO } from '../../../libs/model/diligence/pojo/dimension/DimensionScorePO';
import * as Bluebird from 'bluebird';
import { RoverService } from '../../data/source/rover.service';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { JudgementService } from '../../data/source/judgement.service';
import { Cacheable } from '@type-cacheable/core';
import { EnterpriseLibService } from '../../data/source/enterprise.lib.service';
import { PledgeService } from 'apps/data/source/pledge.service';
import { OuterBlacklistService } from '../../data/source/outer.blacklist.service';
import { InjectRepository } from '@nestjs/typeorm';
import { DiligenceTenderCertificationEntity } from '../../../libs/entities/DiligenceTenderCertificationEntity';
import { Repository } from 'typeorm';
import { SupervisePunishService } from '../../data/source/supervise.punish.service';
import { RoverGraphService } from '../../data/source/rover.graph.service';
import { CompanyDetailService } from '../../company/company-detail.service';
import { BidCollusiveService } from '../../data/source/bid.collusive.service';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { DimensionLevel1Enums } from '../../../libs/enums/diligence/DimensionLevel1Enums';
import { DimensionGroupItems } from 'libs/model/diligence/pojo/dimension/group/DimensionGroupItems';

@Injectable()
export class BiddingDetailService {
  public snapshotQueue: RabbitMQ;

  constructor(
    private readonly configService: ConfigService,
    private readonly tenderService: TenderService,
    private readonly roverGraphService: RoverGraphService,
    private readonly supervisePunishService: SupervisePunishService,
    private readonly outerBlacklistService: OuterBlacklistService,
    private readonly judgementService: JudgementService,
    private readonly caseService: CaseService,
    private readonly pledgeService: PledgeService,
    private readonly settingService: SettingsService,
    private readonly roverService: RoverService,
    private readonly enterpriseLibService: EnterpriseLibService,
    private readonly companyDetailService: CompanyDetailService,
    private readonly bidCollusiveService: BidCollusiveService,
    @InjectRepository(DiligenceTenderCertificationEntity) private readonly tenderCertificationRepo: Repository<DiligenceTenderCertificationEntity>,
  ) {}

  /**
   * 涉围串标等投标处罚记录
   */
  async getBidAdministrativePenalties(orgId: number, body: HitDetailsBidBaseQueryParams) {
    const dimensionGroup = await this.settingService.getTenderDimensionGroup(orgId);
    const dimensionDefinitions = dimensionGroup.items;
    const dimension = dimensionDefinitions.find((e) => e.key == DimensionLevel2Enums.PurchaseIllegal);
    const subDimension = dimension.subDimensionList.find((e) => e.key == DimensionLevel2Enums.BidAdministrativePenalties);
    if (body?.sortField) {
      set(subDimension, ['strategyModel', 'sortField', 'field'], body?.sortField);
      set(subDimension, ['strategyModel', 'sortField', 'order'], body?.isSortAsc ? 'ASC' : 'DESC');
    }
    return this.supervisePunishService.getDimensionDetailForBid(subDimension, body);
  }

  /**
   * 涉采购黑名单
   */
  async getGovernmentPurchaseIllegal(orgId: number, body: HitDetailsBidBaseQueryParams) {
    const dimensionGroup = await this.settingService.getTenderDimensionGroup(orgId);
    const dimensionDefinitions = dimensionGroup.items;
    const dimension = dimensionDefinitions.find((e) => e.key == DimensionLevel2Enums.PurchaseIllegal);
    const subDimension = dimension.subDimensionList.find((e) => e.key == DimensionLevel3Enums.GovernmentPurchaseIllegal);
    if (body?.sortField) {
      set(subDimension, ['strategyModel', 'sortField', 'field'], body?.sortField);
      set(subDimension, ['strategyModel', 'sortField', 'order'], body?.isSortAsc ? 'ASC' : 'DESC');
    }
    return this.outerBlacklistService.getDimensionDetailForBid(subDimension, body);
  }

  async getGovProcurementIllegal(orgId: number, body: HitDetailsBidBaseQueryParams) {
    const dimensionGroup = await this.settingService.getTenderDimensionGroup(orgId);
    const dimensionDefinitions = dimensionGroup.items;
    const dimension = dimensionDefinitions.find((e) => e.key == DimensionLevel2Enums.PurchaseIllegal);
    const subDimension = dimension.subDimensionList.find((e) => e.key == DimensionLevel3Enums.GovProcurementIllegal);
    if (body?.sortField) {
      set(subDimension, ['strategyModel', 'sortField', 'field'], body?.sortField);
      set(subDimension, ['strategyModel', 'sortField', 'order'], body?.isSortAsc ? 'ASC' : 'DESC');
    }
    return this.outerBlacklistService.getDimensionDetailForBid(subDimension, body);
  }

  async getArmyProcurementIllegal(orgId: number, body: HitDetailsBidBaseQueryParams) {
    const dimensionGroup = await this.settingService.getTenderDimensionGroup(orgId);
    const dimensionDefinitions = dimensionGroup.items;
    const dimension = dimensionDefinitions.find((e) => e.key == DimensionLevel2Enums.PurchaseIllegal);
    const subDimension = dimension.subDimensionList.find((e) => e.key == DimensionLevel3Enums.ArmyProcurementIllegal);
    if (body?.sortField) {
      set(subDimension, ['strategyModel', 'sortField', 'field'], body?.sortField);
      set(subDimension, ['strategyModel', 'sortField', 'order'], body?.isSortAsc ? 'ASC' : 'DESC');
    }
    return this.outerBlacklistService.getDimensionDetailForBid(subDimension, body);
  }

  /**
   * 查询两个公司的相同司法案件
   * @param postData
   * @returns
   */
  async getBiddingCompanyCase(postData: HitDetailsBidBaseQueryParams) {
    const dimension: DimensionDefinitionPO = {
      key: DimensionLevel3Enums.BiddingCompanyRelationship2Case,
      name: '疑似关系-相同司法案件',
      strategyModel: {
        boost: 1.0,
        baseScore: 5,
        level: DimensionRiskLevelEnum.Medium,
        // cycle: 3,
        sortField: { field: 'LastestDate', order: 'DESC' },
        // detailsParams: [
        // ],
      },
      isVirtualDimension: 0,
      source: DimensionSourceEnums.Case,
      sourcePath: '/api/search/search-credit',
      status: 1,
      template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
      template2: '被列入#name# #count#条记录',
      sort: 6,
      type: IndicatorTypeEnums.generalItems,
    };
    return this.caseService.getDimensionDetailForBid(dimension, postData);
  }

  async getBiddingCompanyPatent(postData: HitDetailsBidBaseQueryParams) {
    const r = await this.companyDetailService.getCompanyPatent({
      keyNoRelation: postData.keyNos,
      pageIndex: postData.pageIndex,
      pageSize: postData.pageSize,
      sortField: ['publicationdate'],
    });
    if (!r?.Paging) {
      r['Paging'] = {
        TotalRecords: 0,
        PageIndex: postData.pageIndex,
        PageSize: postData.pageSize,
      };
      r['Result'] = [];
    }
    return r;
  }

  /**
   * 查询相同专利数量
   * @param postData
   * @returns
   */
  async countBiddingCompanyPatent(postData: HitDetailsBidBaseQueryParams) {
    const r = await this.companyDetailService.getPatentRelation(postData.keyNos);
    const TotalRecords = r?.Result?.[0]?.Count || 0;
    return {
      Paging: {
        PageIndex: postData.pageIndex,
        PageSize: postData.pageSize,
        TotalRecords,
      },
    };
  }

  /**
   * 查询两个公司的相同国际专利列表
   * @param postData
   * @returns
   */
  async getBiddingCompanyIntPatent(postData: HitDetailsBidBaseQueryParams) {
    const params = {
      searchType: 'relation',
      searchKey: postData.keyNos.join(','),
      pageIndex: postData.pageIndex,
      pageSize: postData.pageSize,
      sortField: 'publicationdate',
    };
    const r = await this.companyDetailService.getInternationalPatent(params);
    if (!r?.Paging) {
      r['Paging'] = {
        TotalRecords: 0,
        PageIndex: postData.pageIndex,
        PageSize: postData.pageSize,
      };
      r['Result'] = [];
    }
    return r;
  }

  /**
   * 查询相同国际专利数量
   * @param postData
   * @returns
   */
  async countBiddingCompanyIntPatent(postData: HitDetailsBidBaseQueryParams) {
    const r = await this.companyDetailService.getInternationalPatent({
      searchType: 'relation',
      searchKey: postData.keyNos.join(','),
    });

    const TotalRecords = r?.Paging?.TotalRecords || 0;
    return {
      Paging: {
        PageIndex: postData.pageIndex,
        PageSize: postData.pageSize,
        TotalRecords,
      },
    };
  }

  /**
   * 获取相同软著列表
   * @param postData
   * @returns
   */
  async getBiddingCompanySoftwareCopyright(postData: HitDetailsBidBaseQueryParams) {
    const params = {
      searchType: 'relation',
      searchKey: postData.keyNos.join(','),
      pageIndex: postData.pageIndex,
      pageSize: postData.pageSize,
    };
    const r = await this.companyDetailService.getCopyrights(params);
    if (!r?.Paging) {
      r['Paging'] = {
        TotalRecords: 0,
        PageIndex: postData.pageIndex,
        PageSize: postData.pageSize,
      };
      r['Result'] = [];
    }
    return r;
  }

  /**
   * 获取两个公司共同软著数量
   * @param postData
   * @returns
   */
  async countBiddingCompanySoftwareCopyright(postData: HitDetailsBidBaseQueryParams) {
    const r = await this.companyDetailService.getSoftwareCopyright(postData.keyNos);
    const TotalRecords = r?.Result?.[0]?.Count || 0;
    return {
      Paging: {
        PageIndex: postData.pageIndex,
        PageSize: postData.pageSize,
        TotalRecords,
      },
    };
  }

  /**
   * 查询两个公司直接的互相担保关联
   * @param postData
   * @returns
   */
  async getBiddingCompanyGuarantor(postData: HitDetailsBidBaseQueryParams) {
    if (postData.keyNos?.length != 2) {
      throw new BadRequestException({ error: '公司数量必须是2家' });
    }
    postData.keyNos.sort((a, b) => a.localeCompare(b));
    const interGuarantor = await this.getAllInterGuarantor(postData.keyNos[0], postData.keyNos[1]);
    const { pageIndex, pageSize, sortField, isSortAsc } = postData;
    if (sortField) {
      interGuarantor.sort((a, b) => {
        if (a[sortField] == undefined) {
          return 1;
        }
        if (b[sortField] == undefined) {
          return -1;
        }
        if (isSortAsc) {
          return a[sortField] - b[sortField];
        } else {
          return b[sortField] - a[sortField];
        }
      });
    }
    const start = pageSize * (pageIndex - 1);
    return {
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: interGuarantor.length,
      },
      Result: interGuarantor.slice(start, start + pageSize),
    };
  }

  /**
   * 查询两个公司直接的股权出质关联
   * @param postData
   */
  async getBiddingCompanyEquityPledge(postData: HitDetailsBidBaseQueryParams) {
    if (postData.keyNos?.length != 2) {
      throw new BadRequestException({ error: '公司数量必须是2家' });
    }
    const dimension: DimensionDefinitionPO = {
      key: DimensionLevel3Enums.BiddingCompanyRelationship2EquityPledge,
      name: '疑似关系-股权出质关联',
      strategyModel: {
        boost: 1.0,
        baseScore: 5,
        level: DimensionRiskLevelEnum.Medium,
        // cycle: 3,
        sortField: { field: 'sortdate', order: 'DESC' },
        // detailsParams: [
        // ],
      },
      isVirtualDimension: 0,
      source: DimensionSourceEnums.Pledge,
      status: 1,
      template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
      template2: '被列入#name# #count#条记录',
      sort: 6,
      type: IndicatorTypeEnums.generalItems,
    };
    return this.pledgeService.getDimensionDetailForBid(dimension, postData);
  }

  // /**
  //  * 获取两间公司的所有关联担保
  //  * @param keyNoA
  //  * @param keyNoB
  //  * @private
  //  */
  // @Cacheable({ ttlSeconds: 300 })
  // async getAllInterGuarantor(keyNoA: string, keyNoB: string) {
  //   const guarantorA = await this.getCompanyGuarantor(keyNoA);
  //   const guarantorB = await this.getCompanyGuarantor(keyNoB);
  //   if (!guarantorA?.length && !guarantorB?.length) {
  //     return [];
  //   }
  //   const [legalPersonsA, employeesA, partnersA, benefitsA] = await Bluebird.all([
  //     this.enterpriseLibService.getAllLegalPerson(keyNoA),
  //     this.enterpriseLibService.getAllEmployeeList(keyNoA),
  //     this.enterpriseLibService.getAllPartnerList(keyNoA),
  //     this.enterpriseLibService.getBenefitList(keyNoA),
  //   ]);
  //   const [legalPersonsB, employeesB, partnersB, benefitsB] = await Bluebird.all([
  //     this.enterpriseLibService.getAllLegalPerson(keyNoB),
  //     this.enterpriseLibService.getAllEmployeeList(keyNoB),
  //     this.enterpriseLibService.getAllPartnerList(keyNoB),
  //     this.enterpriseLibService.getBenefitList(keyNoB),
  //   ]);
  //   const mainPersonKeyNoA = compact(uniq(union(legalPersonsA, employeesA, partnersA, benefitsA).map((x) => x['keyNo'])));
  //   const mainPersonKeyNoB = compact(uniq(union(legalPersonsB, employeesB, partnersB, benefitsB).map((x) => x['keyNo'])));
  //
  //   const interGuarantor = [];
  //   const repeat: Set<string> = new Set<string>();
  //   guarantorA?.forEach((g) => {
  //     if (!repeat.has(g.Id)) {
  //       repeat.add(g.Id);
  //       g['companyId'] = keyNoA;
  //       if (
  //         g?.Vouchee?.some((x) => x.KeyNo == keyNoB || mainPersonKeyNoB?.includes(x.KeyNo)) ||
  //         g?.Guarantee?.some((x) => x.KeyNo == keyNoB || mainPersonKeyNoB?.includes(x.KeyNo))
  //       ) {
  //         interGuarantor.push(g);
  //         g.Vouchee.forEach((x) => {
  //           if (mainPersonKeyNoB?.includes(x.KeyNo)) {
  //             x['Role'] = this.unionPersonRole(legalPersonsB, employeesB, partnersB, benefitsB, x.KeyNo);
  //           }
  //         });
  //         g.Guarantee.forEach((x) => {
  //           if (mainPersonKeyNoB?.includes(x.KeyNo)) {
  //             x['Role'] = this.unionPersonRole(legalPersonsB, employeesB, partnersB, benefitsB, x.KeyNo);
  //           }
  //         });
  //       }
  //     }
  //   });
  //
  //   guarantorB?.forEach((g) => {
  //     if (!repeat.has(g.Id)) {
  //       repeat.add(g.Id);
  //       g['companyId'] = keyNoB;
  //       if (
  //         g?.Vouchee?.some((x) => x.KeyNo == keyNoA || mainPersonKeyNoA?.includes(x.KeyNo)) ||
  //         g?.Guarantee?.some((x) => x.KeyNo == keyNoA || mainPersonKeyNoA?.includes(x.KeyNo))
  //       ) {
  //         interGuarantor.push(g);
  //         g.Vouchee.forEach((x) => {
  //           if (mainPersonKeyNoA?.includes(x.KeyNo)) {
  //             x['Role'] = this.unionPersonRole(legalPersonsA, employeesA, partnersA, benefitsA, x.KeyNo);
  //           }
  //         });
  //         g.Guarantee.forEach((x) => {
  //           if (mainPersonKeyNoA?.includes(x.KeyNo)) {
  //             x['Role'] = this.unionPersonRole(legalPersonsA, employeesA, partnersA, benefitsA, x.KeyNo);
  //           }
  //         });
  //       }
  //     }
  //   });
  //   return interGuarantor;
  // }

  /**
   * 获取两间公司的所有关联担保
   * @param keyNoA
   * @param keyNoB
   * @private
   */
  @Cacheable({ ttlSeconds: 300 })
  async getAllInterGuarantor(keyNoA: string, keyNoB: string) {
    const guarantorA = await this.getCompanyGuarantor(keyNoA);
    const guarantorB = await this.getCompanyGuarantor(keyNoB);
    if (!guarantorA?.length && !guarantorB?.length) {
      return [];
    }
    const [legalPersonsA, employeesA, partnersA, benefitsA, actualControllerA] = await Bluebird.all([
      this.enterpriseLibService.getAllLegalPerson(keyNoA),
      this.enterpriseLibService.getAllEmployeeList(keyNoA),
      this.enterpriseLibService.getAllPartnerList(keyNoA),
      this.enterpriseLibService.getBenefitList(keyNoA),
      this.enterpriseLibService.getActualController(keyNoA)?.then((x) =>
        x?.Names.flatMap(
          (nameGroup) =>
            nameGroup?.Names.PersonList?.map((p) => ({
              keyNo: p.KeyNo,
              name: p.Name,
            })) || [],
        ),
      ),
    ]);
    const [legalPersonsB, employeesB, partnersB, benefitsB, actualControllerB] = await Bluebird.all([
      this.enterpriseLibService.getAllLegalPerson(keyNoB),
      this.enterpriseLibService.getAllEmployeeList(keyNoB),
      this.enterpriseLibService.getAllPartnerList(keyNoB),
      this.enterpriseLibService.getBenefitList(keyNoB),
      this.enterpriseLibService.getActualController(keyNoB)?.then((x) =>
        x?.Names.flatMap(
          (nameGroup) =>
            nameGroup?.Names.PersonList?.map((p) => ({
              keyNo: p.KeyNo,
              name: p.Name,
            })) || [],
        ),
      ),
    ]);
    const mainPersonKeyNoA = compact(uniq(union(legalPersonsA, employeesA, partnersA, benefitsA, actualControllerA).map((x) => x['keyNo'])));
    const mainPersonKeyNoB = compact(uniq(union(legalPersonsB, employeesB, partnersB, benefitsB, actualControllerB).map((x) => x['keyNo'])));

    const repeat: Set<string> = new Set<string>();
    const allGuarantor = guarantorA.concat(guarantorB);
    const allHitGuarantees = [];
    allGuarantor?.forEach((guarantee) => {
      if (!repeat.has(guarantee.Id)) {
        repeat.add(guarantee.Id);
        const putRoleA = (x) => {
          x['companyId'] = keyNoA;
          if (mainPersonKeyNoA?.includes(x.KeyNo)) {
            x['Role'] = this.unionPersonRole(legalPersonsA, employeesA, partnersA, benefitsA, x.KeyNo, actualControllerA);
          }
        };
        const putRoleB = (x) => {
          x['companyId'] = keyNoB;
          if (mainPersonKeyNoB?.includes(x.KeyNo)) {
            x['Role'] = this.unionPersonRole(legalPersonsB, employeesB, partnersB, benefitsB, x.KeyNo, actualControllerB);
          }
        };
        const guaranteeGuarantorA = guarantee.Guarantee.filter((x) => x.KeyNo == keyNoA || mainPersonKeyNoA?.includes(x.KeyNo));
        const guaranteeVoucheeB = guarantee.Vouchee.filter((x) => x.KeyNo == keyNoB || mainPersonKeyNoB?.includes(x.KeyNo));
        const guaranteeVoucheeA = guarantee.Vouchee.filter((x) => x.KeyNo == keyNoA || mainPersonKeyNoA?.includes(x.KeyNo));
        const guaranteeGuarantorB = guarantee.Guarantee.filter((x) => x.KeyNo == keyNoB || mainPersonKeyNoB?.includes(x.KeyNo));
        guaranteeGuarantorA?.forEach((x) => putRoleA(x));
        guaranteeVoucheeB?.forEach((x) => putRoleB(x));
        guaranteeVoucheeA?.forEach((x) => putRoleA(x));
        guaranteeGuarantorB?.forEach((x) => putRoleB(x));
        if (guaranteeGuarantorA?.length > 0 && guaranteeVoucheeB?.length > 0) {
          // A 方为担保人，B 方为被担保人
          guarantee['hitData'] = { start: guaranteeGuarantorA, end: guaranteeVoucheeB };
          allHitGuarantees.push(guarantee);
        } else if (guaranteeVoucheeA?.length > 0 && guaranteeGuarantorB?.length > 0) {
          // B 方为担保人，A 方为被担保人
          guarantee['hitData'] = { start: guaranteeVoucheeA, end: guaranteeGuarantorB };
          allHitGuarantees.push(guarantee);
        } else if (guaranteeGuarantorA?.length > 0 && guaranteeGuarantorB?.length > 0) {
          // 同为担保方
          guarantee['hitData'] = { start: guaranteeGuarantorA, end: guaranteeGuarantorB };
          allHitGuarantees.push(guarantee);
        } else if (guaranteeVoucheeA?.length > 0 && guaranteeVoucheeB?.length > 0) {
          // 同为被担保方
          guarantee['hitData'] = { start: guaranteeVoucheeA, end: guaranteeVoucheeB };
          allHitGuarantees.push(guarantee);
        }
      }
    });
    return allHitGuarantees;
  }

  /**
   * 共同投标分析
   */
  async getJointBiddingAnalysis(body: HitDetailsBidBaseQueryParams) {
    const { pageSize, pageIndex, sortField, isSortAsc } = body;
    const competitionCountList: CompanyJoinBiddingModel[] = await this.tenderService.getCompanyCompetitionCount(body.keyNoAndNames, sortField, isSortAsc);
    if (competitionCountList?.length) {
      //内存分页
      const start = pageSize * (pageIndex - 1);
      return Object.assign(new HitDetailsBaseResponse(), {
        Paging: {
          PageSize: pageSize,
          PageIndex: pageIndex,
          TotalRecords: competitionCountList.length,
        },
        Result: competitionCountList.slice(start, start + pageSize),
      });
    }
    return HitDetailsBaseResponse.failed('获取共同投标分析失败');
  }

  /**
   * 共同投标分析详情
   */
  async getJointBiddingAnalysisDetail(body: JointBiddingAnalysisDetailsQueryParams) {
    return this.tenderService.getCompanyCompetitionDetail(body);
  }

  /**
   * 内部黑名单详情
   * @param orgId
   * @param body
   * @param dimensionKey
   */
  async getInnerBlacklistDetail(orgId: number, body: HitDetailsBidBaseQueryParams, dimensionKey: DimensionTypeEnums) {
    let subDimension;
    if (dimensionKey == DimensionLevel2Enums.BlackListInvestigations || dimensionKey == DimensionLevel2Enums.DirectConnection) {
      subDimension = { key: dimensionKey };
    } else {
      subDimension = await this.settingService.getTenderDimensionDefinition(dimensionKey, orgId);
    }
    const hitDetailParam = Object.assign(new GetHitDetailsParamBase(), {
      key: subDimension.key,
      orgId,
    });
    return this.getHitsDetailsByKeyNos(orgId, hitDetailParam, body, subDimension);
  }

  /**
   * 根据keyNos批量查询内部黑名单列表
   * @param params
   * @param requestData
   * @param dimension
   */
  async getHitsDetailsByKeyNos(
    orgId: number,
    params: GetHitDetailsParamBase,
    requestData: HitDetailsBidBaseQueryParams,
    dimension: DimensionDefinitionPO,
  ): Promise<HitDetailsBaseResponse> {
    let response: HitDetailsBaseResponse = HitDetailsBaseResponse.failed('');
    if (
      [...this.roverGraphService.getBlacklistTypes()].some((t) => t === dimension.key) ||
      dimension.key === DimensionLevel2Enums.BlacklistSameSuspectedActualController
    ) {
      response = await this.roverGraphService.getDimensionDetailsByKeyNos(orgId, requestData.keyNos, dimension, requestData);
    } else if (dimension.key === DimensionLevel2Enums.DirectConnection) {
      const requestParams = Object.assign(new HitDetailsBidBaseQueryParams(), {
        keyNos: requestData.keyNos,
        pageSize: requestData.pageSize,
        pageIndex: requestData.pageIndex,
        orgId,
      });
      const result = await this.roverGraphService.getDirectConnection(requestParams);
      response.Paging = result.Paging;
      response.Result = result.Result;
    } else if (dimension.key === DimensionLevel2Enums.BlackListInvestigations) {
      const requestParams = Object.assign(new HitDetailsBidBaseQueryParams(), {
        keyNos: requestData.keyNos,
        pageSize: requestData.pageSize,
        pageIndex: requestData.pageIndex,
        orgId,
      });
      const result = await this.getBlackListInvestigations(orgId, requestParams);
      response.Paging = result.Paging;
      response.Result = result.Result;
    }
    response.realtime = true;
    return response;
  }

  /**
   * 潜在利益冲突
   * @param orgId
   * @param body
   * @param dimensionKey
   * @returns
   */
  async getInterestConflict(orgId: number, body: HitDetailsBidBaseQueryParams, dimensionKey: DimensionTypeEnums): Promise<HitDetailsBaseResponse> {
    const subDimension = await this.settingService.getTenderDimensionDefinition(dimensionKey, orgId);
    const results: DimensionScorePO[] = [];
    const { pageSize, pageIndex, keyNoAndNames } = body;
    const queryPO = subDimension.strategyModel.detailsParams?.find((d) => d.field === QueryParamsEnums.dataRange);
    const groupIds = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Groups)?.value || [];
    const biddingDimensionHitsDetails = await this.roverService.biddingAnalyze([subDimension], {
      orgId,
      keyNoAndNames,
      pageSize: 500,
      pageIndex: 1,
      groupIds,
    });
    biddingDimensionHitsDetails?.map((b) => {
      if (b?.data?.Result?.length > 0) {
        Array.prototype.push.apply(results, b.data.Result);
      }
    });
    const start = pageSize * (pageIndex - 1);
    return Object.assign(new HitDetailsBaseResponse(), {
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: results?.length,
      },
      Result: results?.slice(start, start + pageSize),
    });
  }

  private unionPersonRole(legalPersons, employees, partners, benefits, personNo: string, actualController?) {
    const roles: string[] = [];
    legalPersons?.forEach((x) => {
      if (x.keyNo == personNo) {
        roles.push(x.history ? '历史法定代表人' : '法定代表人');
      }
    });
    employees?.forEach((x) => {
      if (x.keyNo == personNo) {
        roles.push(x.history ? '历史董监高' : '董监高');
      }
    });
    partners?.forEach((x) => {
      if (x.keyNo == personNo) {
        roles.push(x.history ? '历史股东' : '股东');
      }
    });
    benefits?.forEach((x) => {
      if (x.keyNo == personNo) {
        roles.push('受益所有人');
      }
    });
    actualController?.forEach((x) => {
      if (x.keyNo == personNo) {
        roles.push('实际控制人');
      }
    });
    return uniq(roles).join(',');
  }

  @Cacheable({ ttlSeconds: 300 })
  private async getCompanyGuarantor(keyNo: string) {
    const params = {
      keyNo,
      pageIndex: 1,
      pageSize: 200,
      isValid: 1,
    };
    const resp = await this.companyDetailService.getGuarantorList(params);
    if (resp?.Result?.length) {
      return resp?.Result;
    }
    return [];
  }

  async getBiddingCompanyCertification(currentOrg: number, postData: HitDetailsBidBaseQueryParams) {
    const { keyNos, pageSize, pageIndex, status, diligenceId } = postData;
    const qb = this.tenderCertificationRepo.createQueryBuilder('certification');
    if (diligenceId) {
      qb.andWhere('certification.diligenceId = :diligenceId', { diligenceId });
    }
    if (keyNos?.length) {
      qb.andWhere('certification.keyNo in (:keyNos)', { keyNos });
    }
    if (status?.length) {
      qb.andWhere('certification.status in (:status)', { status });
    }
    qb.orderBy('certification.keyNo,certification.id', 'ASC');
    qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    const [data, total] = await qb.getManyAndCount();
    return Object.assign(new HitDetailsBaseResponse(), {
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: total,
      },
      Result: data,
    });
  }

  /**
   * 获取两间公司的围串标详情
   * @param postData
   */
  async getBidCollusive(postData: HitDetailsBidBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const hitDetailsBaseResponse = await this.bidCollusiveService.getBidCollusiveList(postData);
    if (hitDetailsBaseResponse.Result?.length > 0) {
      // 目前只有行政处罚数据类型，不需要处理，后续有新数据源需要区分处理 sourcetype
      const sourceIds = [];
      hitDetailsBaseResponse.Result.forEach((item) => {
        sourceIds.push(...item.sourceid);
      });
      // 获取处罚详情
      return await this.supervisePunishService.getDetailBySourceIds(sourceIds, postData);
    }
    return hitDetailsBaseResponse;
  }

  /**
   * 动产抵关联详情
   * @param postData
   */
  async getChattelMortgage(postData: HitDetailsBidBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const hitDetailsBaseResponse = HitDetailsBaseResponse.ok();
    const { keyNos, pageSize, pageIndex, sortField, isSortAsc } = postData;
    const chattelMortgageList = await this.getChattelMortgageList(keyNos[0], keyNos[1]);
    if (chattelMortgageList.length > 0) {
      const start = pageSize * (pageIndex - 1);
      const end = start + pageSize;
      hitDetailsBaseResponse.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: chattelMortgageList.length,
      };
      if (sortField == 'RegisterDate') {
        if (isSortAsc) {
          chattelMortgageList.sort((a, b) => a.RegisterDate - b.RegisterDate);
        } else {
          chattelMortgageList.sort((a, b) => b.RegisterDate - a.RegisterDate);
        }
      }
      hitDetailsBaseResponse.Result = chattelMortgageList.slice(start, end);
    }
    return hitDetailsBaseResponse;
  }

  /**
   * 获取两间公司的动产抵押列表
   * @param keyNoA
   * @param keyNoB
   */
  async getChattelMortgageList(keyNoA: string, keyNoB: string) {
    const [chattelMortgageA, chattelMortgageB] = await Bluebird.all([
      this.companyDetailService.getCompanyAllChattelMortgage(keyNoA),
      this.companyDetailService.getCompanyAllChattelMortgage(keyNoB),
    ]);
    if (chattelMortgageA?.length > 0 && chattelMortgageB?.length > 0) {
      const itemAIds = chattelMortgageA.map((item) => item.Id);
      return chattelMortgageB.filter((item) => itemAIds.includes(item.Id));
    }
    return [];
  }

  async getBidCollusiveDetails(postData: BidCollusiveDetailsQueryParams) {
    const { sourcetype, projectSourceId, keyNos } = postData;
    let response;
    if (sourcetype == 1) {
      // 行政处罚
      response = await this.supervisePunishService.getDetailBySourceId(projectSourceId, keyNos);
    }
    return response;
  }

  async getBidCollusiveList(postData: HitDetailsBidBaseQueryParams) {
    const bidResp = await this.bidCollusiveService.getBidCollusiveList(postData);
    if (bidResp?.Paging?.TotalRecords > 0) {
      return this.processRelationData(bidResp, postData);
    }
    return bidResp;
  }

  // 围串标关联数据处理好后使用
  processRelationData(bidResp: HitDetailsBaseResponse, postData: HitDetailsBidBaseQueryParams) {
    const { keyNos, pageSize, pageIndex } = postData;
    const result = [];
    for (const item of bidResp.Result) {
      if (item.sourcetype == 1) {
        // 行政处罚
        const companyArr = JSON.parse(item.collusivekeynoarray);
        let inKeyNos = false;
        for (const company of companyArr) {
          if (!keyNos.includes(company.KeyNo)) {
            // 去掉与排查的公司无关的文书号
            company.DocNo = '';
          }
          if (keyNos.includes(company.KeyNo) && company.DocNo?.length > 0) {
            //
            inKeyNos = true;
          }
        }
        item.collusivekeynoarray = JSON.stringify(companyArr);
        if (inKeyNos) {
          result.push(item);
        }
      } else {
        result.push(item);
      }
    }
    return {
      Paging: {
        PageIndex: pageIndex,
        PageSize: pageSize,
        TotalRecords: result.length,
      },
      Result: result.slice(pageSize * (pageIndex - 1), pageSize * pageIndex),
    };
  }

  async getControlRelation(postData: HitDetailsBidBaseQueryParams) {
    const params = {
      keyNo: postData.keyNos[0],
      filterKeyNo: postData.keyNos[1],
      pageSize: postData.pageSize,
      pageIndex: postData.pageIndex,
    };
    let res = await this.companyDetailService.getHoldingCompany(params);
    if (res?.Result == undefined) {
      params.keyNo = postData.keyNos[1];
      params.filterKeyNo = postData.keyNos[0];
      res = await this.companyDetailService.getHoldingCompany(params);
    }
    return pick(res, ['Paging', 'Result']);
  }

  async getBidCollusiveDetailList(postData: BidCollusiveDetailListQueryParams) {
    const { collusiveId, keyNos, pageSize, pageIndex, sortField, isSortAsc } = postData;
    let response = { Paging: { PageSize: pageSize, PageIndex: pageIndex, TotalRecords: 0 }, Result: [] };
    const collusiveRecordList = await this.bidCollusiveService.getCollusiveRecord(collusiveId);
    if (collusiveRecordList?.length > 0) {
      const collusiveRecord = collusiveRecordList[0];
      const { sourcetype, collusivekeynoarray } = collusiveRecord;
      const companyArr = JSON.parse(collusivekeynoarray);
      const sourceIds = [];
      if (companyArr?.length > 0) {
        companyArr.forEach((item) => {
          if (keyNos.includes(item.KeyNo)) {
            if (item.SourceId) {
              sourceIds.push(item.SourceId);
            }
          }
        });
      }
      if (sourceIds.length == 0) {
        return response;
      }
      if (sourcetype == 1) {
        // 行政处罚
        response = await this.supervisePunishService.getDetailBySourceIdsAndKeyNos(sourceIds, keyNos, pageSize, pageIndex, sortField, isSortAsc);
      }
      if (sourcetype == 5) {
        // 黑名单
        response = await this.outerBlacklistService.getBlacklistDetails(sourceIds, keyNos, pageSize, pageIndex, sortField, isSortAsc);
      }
      response.Paging['sourcetype'] = sourcetype;
    }
    return response;
  }

  async getDirectConnection(postData: HitDetailsBidBaseQueryParams) {
    return await this.roverGraphService.getDirectConnection(postData);
  }

  async getBlackListInvestigations(orgId: number, postData: HitDetailsBidBaseQueryParams) {
    const dimensionGroup = await this.settingService.getTenderDimensionGroup(orgId);
    const activeSubDimensions = await this.getOrgActiveSubDimensions(dimensionGroup);
    const dimensionDefinition = dimensionGroup.items.find((x) => x.key == DimensionLevel1Enums.Risk_InnerBlacklist);
    const strageDetailParams = dimensionDefinition.strategyModel?.detailsParams;
    return await this.roverGraphService.getBlackListInvestigations(postData, activeSubDimensions, strageDetailParams);
  }

  async getOrgActiveSubDimensions(dimensionGroup: DimensionGroupItems): Promise<SubDimensionDefinitionPO[]> {
    const dimensionDefinitions = dimensionGroup.items;
    const dimensionDefinition = dimensionDefinitions.find((x) => x.key == DimensionLevel1Enums.Risk_InnerBlacklist);
    if (dimensionDefinition.status == 0) {
      return [];
    }
    return dimensionDefinition.subDimensionList.filter((d) => d.status > 0);
  }
}
