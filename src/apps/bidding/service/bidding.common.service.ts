import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { DiligenceTenderHistoryEntity } from '../../../libs/entities/DiligenceTenderHistoryEntity';
import * as Bluebird from 'bluebird';
import { RoverUser } from '../../../libs/model/common';
import { DiligenceTenderCompanyEntity } from '../../../libs/entities/DiligenceTenderCompanyEntity';
import { DiligenceTenderRemarkEntity } from '../../../libs/entities/DiligenceTenderRemarkEntity';
import { SearchDiligenceBiddingRequest } from '../../../libs/model/bidding/SearchDiligenceBiddingRequest';
import { RemarkDiligenceBiddingRequest } from '../../../libs/model/bidding/RemarkDiligenceBiddingRequest';
import { RemarkDiligenceBiddingCompanyRequest } from '../../../libs/model/bidding/RemarkDiligenceBiddingCompanyRequest';
import { DiligenceBiddingHistoryRequest } from '../../../libs/model/bidding/DiligenceBiddingHistoryRequest';
import { SecurityService } from '../../../libs/config/security.service';
import { PermissionByEnum } from '../../../libs/enums/PermissionScopeEnum';
import { QueryBuilderHelper } from '../../../libs/common/sql.helper';
import MyOssService from '../../basic/my-oss.service';
import { getBundleStart } from '../../../libs/utils/diligence/diligence.utils';
import { add } from 'lodash';
import { BiddingDiligenceHelper } from '../helper/bidding.diligence.helper';
import { TimeInterval3Hour } from '../../../libs/constants/common';

@Injectable()
export class BiddingCommonService {
  public tenderDiligenceQueue: RabbitMQ;

  constructor(
    private readonly securityService: SecurityService,
    private readonly myOssService: MyOssService,
    private readonly biddingDiligenceHelper: BiddingDiligenceHelper,
    @InjectRepository(DiligenceTenderHistoryEntity) private readonly diligenceTenderHistoryRepo: Repository<DiligenceTenderHistoryEntity>,
    @InjectRepository(DiligenceTenderCompanyEntity) private readonly diligenceTenderCompanyRepo: Repository<DiligenceTenderCompanyEntity>,
    @InjectRepository(DiligenceTenderRemarkEntity) private readonly diligenceTenderRemarkRepo: Repository<DiligenceTenderRemarkEntity>,
  ) {}

  async remark(id: number, currentUser: RoverUser, postData: RemarkDiligenceBiddingRequest) {
    const { currentOrg: orgId } = currentUser;
    const history = await this.diligenceTenderHistoryRepo.findOne({ id, orgId });
    if (!history) {
      throw new BadRequestException();
    }

    const [, remark] = await Bluebird.all([
      this.diligenceTenderHistoryRepo.update(id, { remarkResult: postData.status }),
      this.diligenceTenderRemarkRepo.save(
        Object.assign(new DiligenceTenderRemarkEntity(), {
          orgId,
          diligenceId: id,
          status: postData.status,
          operator: currentUser.userId,
          details: postData,
        }),
      ),
    ]);
    return remark;
  }

  async remarkCompany(id: number, currentUser: RoverUser, postData: RemarkDiligenceBiddingCompanyRequest) {
    const { currentOrg: orgId } = currentUser;
    const history = await this.diligenceTenderHistoryRepo.findOne({ id, orgId });
    if (!history) {
      throw new BadRequestException();
    }
    //修改
    const [, remark] = await Bluebird.all([
      //修改了入围公司需要更新 招标排查，强制更新 updateDate，在生成报告时不会取到旧PDF文件
      this.diligenceTenderHistoryRepo.update(id, { updateDate: new Date() }),
      this.diligenceTenderCompanyRepo.update(
        {
          orgId,
          diligenceId: id,
          companyId: postData.companyId,
        },
        { status: postData.status, operator: currentUser.userId },
      ),
    ]);
    return remark;
  }

  async searchV2(currentUser: RoverUser, postData: SearchDiligenceBiddingRequest) {
    // 这边有searchKey时，需要根据公司名称过滤，但前端需要拿到所有的公司，所以先查询到id，再拿id做一次查询拿到数据
    const { currentOrg: orgId, bundle } = currentUser;
    const { searchKey, createDate, status, operators, pageIndex, pageSize, sortField, isSortAsc, depIds, executionState, diligenceIds } = postData;
    try {
      const { by, userIds } = this.securityService.checkScope(currentUser, 2110);
      const qb1 = this.diligenceTenderHistoryRepo
        .createQueryBuilder('history')
        .leftJoinAndSelect('history.editor', 'editor')
        .leftJoinAndSelect('history.companyList', 'company')
        .leftJoinAndSelect('history.remark', 'remark')
        .select([
          'history.id',
          'history.orgId',
          'history.tenderNo',
          'history.projectNo',
          'history.projectName',
          'history.result',
          'history.remarkResult',
          'history.description',
          'history.createDate',
          'history.updateDate',
          'history.status',
          'history.detailFile',
          'editor.userId',
          'editor.name',
          'editor.phone',
          'company.id',
          'company.orgId',
          'company.diligenceId',
          'company.companyId',
          'company.companyName',
          'company.status',
          'company.level',
          'company.createDate',
          'company.updateDate',
          'remark.id',
          'remark.diligenceId',
          'remark.status',
          'remark.createDate',
          'remark.updateDate',
        ])
        .addSelect('IFNULL(history.remarkResult, history.result)', 'showResult')
        .where('history.orgId = :orgId', { orgId });
      // .andWhere('history.status = 1');

      if ('bundle' == postData.mode) {
        const bundleStartDate = getBundleStart(bundle.startDate);
        qb1.andWhere('history.createDate > :bundleStartDate', { bundleStartDate });
      }

      if (by == PermissionByEnum.USER) {
        qb1.andWhere('history.operator in (:...userIds)', { userIds });
      }
      if (searchKey) {
        qb1.andWhere(
          '(history.tenderNo = :tenderNo or history.projectNo like :projectNo or history.projectName like :projectName or company.companyName like :companyName)',
          {
            tenderNo: searchKey,
            projectNo: `%${searchKey}%`,
            projectName: `%${searchKey}%`,
            companyName: `%${searchKey}%`,
          },
        );
      }
      QueryBuilderHelper.applyDateRangeQuery(qb1, createDate, 'createDate');
      if (status?.length) {
        qb1.andWhere('(history.remarkResult in (:...status) or (history.remarkResult is null and history.result in (:...status)))', { status });
      }
      if (executionState?.length) {
        qb1.andWhere('history.status in (:...executionState)', { executionState });
      }
      if (operators?.length) {
        qb1.andWhere('history.operator in (:...operators)', { operators });
      }
      if (depIds?.length) {
        qb1.andWhere('history.depId in (:...depIds)', { depIds });
      }
      if (diligenceIds?.length) {
        qb1.andWhere('history.id in (:...diligenceIds)', { diligenceIds });
      }
      if (sortField) {
        if (sortField == 'result') {
          qb1.orderBy('showResult', isSortAsc ? 'ASC' : 'DESC');
        } else {
          qb1.orderBy(`history.${sortField}`, isSortAsc ? 'ASC' : 'DESC');
        }
      } else {
        qb1.orderBy('history.id', 'DESC');
      }
      qb1.skip(pageSize * (pageIndex - 1)).take(pageSize);
      const [data1, total] = await qb1.getManyAndCount();
      if (data1?.length) {
        let records: DiligenceTenderHistoryEntity[];
        if (searchKey) {
          // 如果使用关键词搜索，可能会过滤掉一些公司名字，所以需要重新查询一遍
          const resultDiligenceIds = data1.map((s) => s.id);
          records = await this.diligenceTenderHistoryRepo
            .createQueryBuilder('history')
            .leftJoinAndSelect('history.companyList', 'company')
            .select([
              'history.id',
              'company.id',
              'company.orgId',
              'company.diligenceId',
              'company.companyId',
              'company.companyName',
              'company.status',
              'company.level',
              'company.createDate',
              'company.updateDate',
            ])
            .where({
              orgId,
              id: In(resultDiligenceIds),
            })
            .getMany();
        }
        const nowDate = new Date();
        await Bluebird.map(data1, async (s) => {
          if (records?.length) {
            const record = records.find((r) => r.id == s.id);
            if (record) {
              s.companyList = record.companyList;
            }
          }
          if (s.status === 0 && s.updateDate.getTime() < nowDate.getTime() - TimeInterval3Hour) {
            await this.diligenceTenderHistoryRepo.update(s.id, { updateDate: nowDate, status: 2 });
            s.status = 2;
            s.updateDate = nowDate;
          }
          if (s.remark?.length) {
            // remark按时间倒序
            s.remark.sort((a, b) => b.id - a.id);
          }
          s.details = await this.myOssService.getObject(s?.detailFile);
        });
        return {
          pageSize,
          pageIndex,
          data: data1,
          total,
        };
      } else {
        return {
          pageSize,
          pageIndex,
          data: [],
          total,
        };
      }
    } catch (e) {
      if (e instanceof ForbiddenException) {
        return {
          pageSize,
          pageIndex,
          data: [],
          total: 0,
        };
      }
    }
  }

  // async search(currentUser: RoverUser, postData: SearchDiligenceBiddingRequest) {
  //   // 这边有searchKey时，需要根据公司名称过滤，但前端需要拿到所有的公司，所以先查询到id，再拿id做一次查询拿到数据
  //   const { currentOrg: orgId, bundle } = currentUser;
  //   const { searchKey, createDate, status, operators, pageIndex, pageSize, sortField, isSortAsc, depIds, executionState, diligenceIds } = postData;
  //   try {
  //     const { by, userIds } = this.securityService.checkScope(currentUser, 2110);
  //     const qb1 = this.diligenceTenderHistoryRepo
  //       .createQueryBuilder('history')
  //       .leftJoinAndSelect('history.editor', 'editor')
  //       .leftJoinAndSelect('history.companyList', 'company')
  //       .leftJoinAndSelect('history.remark', 'remark')
  //       .select([
  //         'history.id',
  //         'history.orgId',
  //         'history.tenderNo',
  //         'history.projectNo',
  //         'history.projectName',
  //         'history.result',
  //         'history.status',
  //         'history.remarkResult',
  //         'history.description',
  //         'history.createDate',
  //         'history.updateDate',
  //       ])
  //       .addSelect('IFNULL(history.remarkResult, history.result)', 'showResult')
  //       .where('history.orgId = :orgId', { orgId });
  //     // .andWhere('history.status = 1');
  //
  //     if ('bundle' == postData.mode) {
  //       const bundleStartDate = getBundleStart(bundle.startDate);
  //       qb1.andWhere('history.createDate > :bundleStartDate', { bundleStartDate });
  //     }
  //
  //     if (by == PermissionByEnum.USER) {
  //       qb1.andWhere('history.operator in (:...userIds)', { userIds });
  //     }
  //     if (searchKey) {
  //       qb1.andWhere(
  //         '(history.tenderNo = :tenderNo or history.projectNo like :projectNo or history.projectName like :projectName or company.companyName like :companyName)',
  //         {
  //           tenderNo: searchKey,
  //           projectNo: `%${searchKey}%`,
  //           projectName: `%${searchKey}%`,
  //           companyName: `%${searchKey}%`,
  //         },
  //       );
  //     }
  //     QueryBuilderHelper.applyDateRangeQuery(qb1, createDate, 'createDate');
  //     if (status?.length) {
  //       qb1.andWhere('(history.remarkResult in (:...status) or (history.remarkResult is null and history.result in (:...status)))', { status });
  //     }
  //     if (executionState?.length) {
  //       qb1.andWhere('history.status in (:...executionState)', { executionState });
  //     }
  //     if (operators?.length) {
  //       qb1.andWhere('history.operator in (:...operators)', { operators });
  //     }
  //     if (depIds?.length) {
  //       qb1.andWhere('history.depId in (:...depIds)', { depIds });
  //     }
  //     if (diligenceIds?.length) {
  //       qb1.andWhere('history.id in (:...diligenceIds)', { diligenceIds });
  //     }
  //     if (sortField) {
  //       if (sortField == 'result') {
  //         qb1.orderBy('showResult', isSortAsc ? 'ASC' : 'DESC');
  //       } else {
  //         qb1.orderBy(`history.${sortField}`, isSortAsc ? 'ASC' : 'DESC');
  //       }
  //     } else {
  //       qb1.orderBy('history.id', 'DESC');
  //     }
  //     qb1.skip(pageSize * (pageIndex - 1)).take(pageSize);
  //     const [data1, total] = await qb1.getManyAndCount();
  //     if (data1?.length) {
  //       // 排序
  //       const dataOrderMap: Map<number, number> = new Map();
  //       for (let i = 0; i < data1.length; i++) {
  //         dataOrderMap.set(data1[i].id, i);
  //       }
  //       const ids = data1.map((x) => x.id);
  //       const qb2 = this.diligenceTenderHistoryRepo
  //         .createQueryBuilder('history')
  //         .leftJoinAndSelect('history.editor', 'editor')
  //         .leftJoinAndSelect('history.companyList', 'company')
  //         .leftJoinAndSelect('history.remark', 'remark')
  //         .select([
  //           'history.id',
  //           'history.orgId',
  //           'history.tenderNo',
  //           'history.projectNo',
  //           'history.projectName',
  //           'history.result',
  //           'history.remarkResult',
  //           'history.description',
  //           'history.createDate',
  //           'history.updateDate',
  //           'history.status',
  //           'history.detailFile',
  //           'editor.userId',
  //           'editor.name',
  //           'editor.phone',
  //           'company.id',
  //           'company.orgId',
  //           'company.diligenceId',
  //           'company.companyId',
  //           'company.companyName',
  //           'company.status',
  //           'company.level',
  //           'company.createDate',
  //           'company.updateDate',
  //           'remark.id',
  //           'remark.diligenceId',
  //           'remark.status',
  //           'remark.createDate',
  //           'remark.updateDate',
  //         ])
  //         .where('history.id in (:...ids)', { ids });
  //       const data2 = await qb2.getMany();
  //       await Bluebird.map(data2, async (s) => {
  //         if (s.remark?.length) {
  //           // remark按时间倒序
  //           s.remark.sort((a, b) => b.id - a.id);
  //         }
  //         s.details = await this.myOssService.getObject(s?.detailFile);
  //       });
  //       data2.sort((a, b) => dataOrderMap.get(a.id) - dataOrderMap.get(b.id));
  //       return {
  //         pageSize,
  //         pageIndex,
  //         data: data2,
  //         total,
  //       };
  //     } else {
  //       return {
  //         pageSize,
  //         pageIndex,
  //         data: [],
  //         total,
  //       };
  //     }
  //   } catch (e) {
  //     if (e instanceof ForbiddenException) {
  //       return {
  //         pageSize,
  //         pageIndex,
  //         data: [],
  //         total: 0,
  //       };
  //     }
  //   }
  // }

  async searchDetail(orgId: number, ids: number[]): Promise<DiligenceTenderHistoryEntity[]> {
    const qb = this.diligenceTenderHistoryRepo
      .createQueryBuilder('history')
      .leftJoinAndSelect('history.editor', 'editor')
      .leftJoinAndSelect('history.companyList', 'company')
      .leftJoinAndSelect('history.remark', 'remark')
      .leftJoinAndSelect('remark.editor', 'remarkEditor')
      .select([
        'history',
        'editor.userId',
        'editor.name',
        'editor.phone',
        'company',
        'remark',
        'remarkEditor.userId',
        'remarkEditor.name',
        'remarkEditor.phone',
      ])
      .where('history.orgId = :orgId', { orgId })
      .andWhere('history.id in (:...ids)', { ids });
    const historyEntities = await qb.getMany();

    return Bluebird.map(
      historyEntities,
      async (entity) => {
        if (entity?.detailFile) {
          entity.details = await this.myOssService.getObject(entity?.detailFile);
          //维度排序处理
          await this.biddingDiligenceHelper.tenderDetailsSort(orgId, entity.orgSettingsId, entity.details);
        }
        if (entity?.remark?.length) {
          entity.remark.sort((a, b) => b.id - a.id);
          entity.remark.forEach((e) => {
            e.details?.attachments?.forEach((x) => {
              x.fileUrl = this.myOssService.signSingleUrl(x.fileUrl);
            });
          });
        }
        return entity;
      },
      { concurrency: 10 },
    );
  }

  async edit(id: number, currentUser: RoverUser, postData: DiligenceBiddingHistoryRequest) {
    const { currentOrg: orgId } = currentUser;
    const history = await this.diligenceTenderHistoryRepo.findOne({ id, orgId });
    if (!history) {
      throw new BadRequestException();
    }
    return this.diligenceTenderHistoryRepo.update(id, {
      projectNo: postData.projectNo,
      projectName: postData.projectName,
    });
  }

  /**
   * 根据排查编号获取排查记录
   * @param currentUser
   * @param tenderNo
   * @returns
   */
  async getByTenderNo(currentUser: RoverUser, tenderNo: string) {
    const { currentOrg: orgId } = currentUser;
    const history = await this.diligenceTenderHistoryRepo.findOne({ orgId, tenderNo });
    if (!history) {
      throw new BadRequestException({ error: `不存在的排查记录，排查编号：${tenderNo}` });
    }
    return (await this.searchDetail(orgId, [history.id]))[0];
  }

  async getResultStatistics(currentUser: RoverUser) {
    const { currentOrg: orgId } = currentUser;
    try {
      const { by, userIds } = this.securityService.checkScope(currentUser, 2110);
      const countQb = this.diligenceTenderHistoryRepo.createQueryBuilder('history');
      countQb.select('history.remarkResult as remarkResult, COUNT(history.id) as count');
      countQb.where('history.orgId = :orgId', { orgId });
      if (by == PermissionByEnum.USER) {
        countQb.andWhere('history.operator in (:...userIds)', { userIds });
      }
      countQb.groupBy('history.remarkResult');
      const countData = await countQb.getRawMany();
      const resultMap = {};
      let total = 0;
      countData.forEach((item) => {
        const { remarkResult, count } = item;
        resultMap[remarkResult] = Number(count);
        total = add(total, Number(item.count));
      });
      return {
        total,
        data: resultMap,
      };
    } catch (e) {
      if (e instanceof ForbiddenException) {
        return {
          data: [],
          total: 0,
        };
      }
    }
  }
}
