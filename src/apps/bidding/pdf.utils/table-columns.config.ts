import { isEmpty } from 'lodash';
import {
  blacklistDurationFormatter,
  casePartiesColumnRender,
  companyBranchPath,
  controlRelationsPath,
  dateTrans,
  directRelationalTypes,
  interestConflictStaffColumnRender,
  pathParser,
  relatedCompanyNameWithHistoryRole,
  relationsPathParser2,
  relationsTypeParser,
  renderStockPercent,
} from 'apps/utils/pdf/pdf-table.util';
import { convertPathToGraph } from 'apps/diligence/pdf.utils/table-columns.config';

// 涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚
const AdministrativePenalties2Columns = [
  {
    title: '决定文书号',
    dataIndex: 'caseno',
  },
  {
    title: '企业名称',
    dataIndex: 'name',
  },
  {
    title: '违法事实',
    dataIndex: 'casereason',
  },
  {
    title: '处罚结果',
    dataIndex: 'title',
  },
  {
    title: '处罚单位',
    dataIndex: 'court',
  },
  {
    title: '处罚日期',
    dataIndex: 'punishdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '发布日期',
    dataIndex: 'publishdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

// 内部黑名单
const HitInnerBlackListColumns = [
  {
    title: '黑名单名称',
    dataIndex: 'companyNameDD',
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
  },
  {
    title: '列入日期',
    dataIndex: 'joinDate',
    width: 95,
    customRender: (date) => {
      if (!date) {
        return '不限';
      }
      return dateTrans(date, false);
    },
  },
  {
    title: '黑名单有效期',
    dataIndex: 'duration',
    customRender: {
      name: 'blacklist',
    },
  },
  {
    title: '截止日期',
    dataIndex: 'expiredDate',
    width: 110,
    customRender: (date) => {
      if (!date) {
        return '不限';
      }
      return dateTrans(date, false);
    },
  },
];

const EmploymentRelationshipColumns = [
  {
    title: '被排查企业',
    dataIndex: 'companyNameDD',
  },
  {
    title: '关联人员',
    dataIndex: 'personName',
  },
  {
    title: '关联黑名单企业名称',
    customRender: (_, item) => relatedCompanyNameWithHistoryRole(item, { key: 'ShareholdingRelationship' }),
  },
  {
    title: '关联路径详情',
    customRender: (item, index) => {
      const HistoryMap = {
        HISEMPLOY: '历史高管',
        HISLEGAL: '历史法人',
        HISINVEST: '历史股东',
      };
      item.leftInfo = HistoryMap[item.typeDD] || item.roleDD;
      item.rightInfo = HistoryMap[item.typeRelated] || item.roleRelated;
      return pathParser(item, index, {
        left: 'companyNameDD',
        leftInfo: 'leftInfo',
        middle: 'personName',
        rightInfo: 'rightInfo',
        right: 'companyNameRelated',
      } as any);
    },
  },
];

const ShareholderColumns = [
  {
    title: '被排查企业',
    dataIndex: 'companyNameDD',
  },
  {
    title: '关联黑名单企业名称',
    customRender: (_, item) => relatedCompanyNameWithHistoryRole(item, { key: 'Shareholder' }),
  },
  {
    title: '持股比例',
    dataIndex: 'stockpercent',
    customRender: renderStockPercent,
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
  },
  {
    title: '列入日期',
    dataIndex: 'joinDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '黑名单有效期',
    dataIndex: 'duration',
    customRender: blacklistDurationFormatter,
  },
];

const ForeignInvestmentColumns = [
  {
    title: '被排查企业',
    dataIndex: 'companyNameDD',
  },
  {
    title: '关联黑名单企业名称',
    customRender: (_, item) => relatedCompanyNameWithHistoryRole(item, { key: 'ForeignInvestment' }),
  },
  {
    title: '持股比例',
    dataIndex: 'stockpercent',
    customRender: renderStockPercent,
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
  },
  {
    title: '列入日期',
    dataIndex: 'joinDate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '黑名单有效期',
    dataIndex: 'duration',
    customRender: blacklistDurationFormatter,
  },
];

// ------------- 黑名单排查 -------------

// 潜在利益冲突

// 潜在利益冲突
const StaffWorkingOutsideForeignInvestmentColumns = [
  {
    title: '被排查企业',
    dataIndex: 'sourceCompanyName',
  },
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '“潜在利冲”人员',
    customRender: interestConflictStaffColumnRender,
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
];

// 疑似潜在利益冲突
const SuspectedInterestConflictColumns = [
  {
    title: '疑似关系',
    customRender: (_, item) => {
      if (item.isSameName) {
        const name = item.name || item.Name;
        return `${item.dimension}（${name}）`;
      }

      if (item.isSameContact) {
        const contacts: string[] = [];
        if (!isEmpty(item.phones)) {
          const phones = item.phones.map(({ n, t }) => `${n} ${t}`);
          contacts.push(...phones);
        }
        if (!isEmpty(item.emails)) {
          const emails = item.emails.map(({ e }) => e);
          contacts.push(...emails);
        }

        if (!isEmpty(contacts)) {
          return `${item.dimension}（${contacts.join('，')}）`;
        }
      }
      return '-';
    },
  },
  {
    title: '被排查企业',
    width: 88,
    dataIndex: 'sourceCompanyName',
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
    width: 88,
  },
  {
    title: '“疑似潜在利冲”人员',
    customRender: interestConflictStaffColumnRender,
    width: 150,
  },
  {
    title: '“疑似潜在利冲”人员分组',
    dataIndex: 'group',
    width: 150,
  },
];

// 买卖合同纠纷/重大纠纷
// 测试数据：广发银行股份有限公司
const Dispute = [
  {
    title: '文书标题',
    customRender: (item) => {
      if (!item.casename) {
        return '-';
      }
      return `
        <div><span style="margin-right: '4px'">${item.casename}</span></div>
        <div>
          ${
            Array.isArray(item.involveTags) && item.involveTags.length > 0
              ? (item.involveTags || []).map((tagText) => {
                  return `
                  <span style="background: #eee; color: #666; padding: 2px 4px; margin-right: 3px;">
                    ${tagText}
                  </span>
                `;
                })
              : ''
          }
        </div>
      `;
    },
  },
  {
    title: '案号',
    dataIndex: 'caseno',
  },
  {
    title: '案由',
    dataIndex: 'casereason',
  },
  {
    title: '当事人',
    customRender: casePartiesColumnRender,
  },
  {
    title: '案件金额(元) ',
    width: 100,
    dataIndex: 'amountinvolved',
    customRender: { name: 'money' },
  },
  {
    title: '裁判结果',
    dataIndex: 'judgeresult',
  },
  {
    title: '裁判日期',
    dataIndex: 'judgedate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
  {
    title: '发布日期',
    dataIndex: 'submitdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const SameControllRelation = [
  {
    title: '被排查企业',
    dataIndex: 'sourceCompanyName',
  },
  {
    title: '关联黑名单企业名称',
    dataIndex: 'companyName',
  },
  {
    title: '实际控制人',
    dataIndex: 'name',
  },
  {
    title: '控制链',
    customRender: controlRelationsPath,
  },
];

// 直接关联关系
const BiddingCompanyRelationshipColumns = [
  {
    title: '企业名称',
    dataIndex: 'startCompanyName',
    width: 140,
  },
  {
    title: '关联类型',
    customRender: directRelationalTypes,
    width: 120,
  },
  {
    title: '关联层级',
    dataIndex: 'steps',
    width: 80,
  },
  {
    title: '关联企业',
    dataIndex: 'endCompanyName',
    width: 140,
  },
  {
    title: '关联链',
    customRender: relationsPathParser2,
  },
];

// 疑似关联关系
const BiddingCompanyRelationColumns = [
  {
    title: '企业名称',
    dataIndex: 'startCompanyName',
  },
  {
    title: '关联企业',
    dataIndex: 'endCompanyName',
  },
  {
    title: '关联类型',
    customRender: relationsTypeParser,
    width: 180,
  },
  {
    title: '关联详情',
    customRender: relationsPathParser2,
    width: 300,
  },
];

// 涉诉围串标记录
const BidAdministrativeJudgementColumns = [
  // 企业名称
  // {
  //   title: '企业名称',
  //   customRender: (item, dataRow, originalData) => {
  //     const relatedCompanies = originalData.companyList.map(({ companyId }) => companyId);
  //
  //     let result = [];
  //     // 如果有被提及，就用被提及
  //     if (Array.isArray(item.involveRole) && item.involveRole.length > 0) {
  //       result = item.involveRole;
  //     } else {
  //       // 没有被提及，就用caserolegroupbyrolename&&排查公司的交集
  //       const defendant = item.caserolegroupbyrolename.reduce((arr, cur) => {
  //         const roleDetail = cur?.DetailList?.map((detail) => {
  //           return {
  //             ...detail,
  //             Job: cur.Role,
  //           };
  //         });
  //         return [...arr, ...roleDetail];
  //       }, []);
  //
  //       result = defendant.filter(({ KeyNo }) => relatedCompanies.includes(KeyNo));
  //     }
  //
  //     if (result.length === 0) {
  //       return '-';
  //     }
  //
  //     result = result.map(({ Name, Tag, Job }) => {
  //       const name = `<div><span style="margin-right: '4px'">${Name}</span></div>`;
  //       const tags = `<div>${[Tag, Job]
  //         .filter((tag) => tag && tag !== '企业主体')
  //         .map((t) => `<span style="background: #eee; color: #666; padding: 2px 4px; margin-right: 3px;">${t}</span>`)
  //         .join('')}</div>`;
  //       return [name, tags].join('');
  //     });
  //
  //     return result.join('') || '-';
  //   },
  // },

  // 案号
  {
    title: '案号',
    dataIndex: 'caseno',
    customRender: (text) => text.split('\n').join('<br />'),
  },
  {
    title: '案由',
    dataIndex: 'casereason',
  },

  // 当事人
  ...Dispute.slice(3, 4),
  {
    title: '案件金额(元) ',
    width: 100,
    dataIndex: 'amountinvolved',
    customRender: { name: 'money' },
  },
  {
    title: '发布日期',
    dataIndex: 'submitdate',
    width: 88,
    customRender: {
      name: 'date',
      options: {
        pattern: 'X',
      },
    },
  },
];

const GovernmentPurchaseIllegalColumns = [
  {
    title: '企业名称',
    dataIndex: 'Name',
  },
  {
    title: '黑名单类型',
    dataIndex: 'CaseReasonType',
  },
  {
    title: '列入原因',
    dataIndex: 'CaseReason',
  },
  {
    title: '列入机关',
    dataIndex: 'Court',
  },
  {
    title: '列入日期',
    dataIndex: 'decisiondate',
    width: 88,
    customRender: { name: 'date', options: { pattern: 'X' } },
  },
  {
    title: '移除日期',
    dataIndex: 'removedate',
    width: 88,
    customRender: { name: 'date', options: { pattern: 'X' } },
  },
];

const GovProcurementIllegalColumns = [
  {
    title: '企业名称',
    dataIndex: 'Name',
  },
  {
    title: '黑名单类型',
    dataIndex: 'CaseReasonType',
  },
  {
    title: '黑名单领域',
    dataIndex: 'field',
  },
  {
    title: '列入原因',
    dataIndex: 'CaseReason',
  },
  {
    title: '列入机关',
    dataIndex: 'Court',
  },
  {
    title: '列入日期',
    dataIndex: 'decisiondate',
    width: 88,
    customRender: { name: 'date', options: { pattern: 'X' } },
  },
  {
    title: '移除日期',
    dataIndex: 'removedate',
    width: 88,
    customRender: { name: 'date', options: { pattern: 'X' } },
  },
];

const JointBiddingAnalysisColumns = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
  },
  {
    title: '关联企业名称',
    dataIndex: 'competitionCompanyName',
  },
  {
    title: '共同投标项目数',
    dataIndex: 'jointTenderCount',
    customRender: (text) => text || '0',
  },
  {
    title: '企业中标项目数',
    dataIndex: 'tenderCount',
    customRender: (text) => text || '0',
  },
  {
    title: '关联企业中标数',
    dataIndex: 'competitionTenderCount',
    customRender: (text) => text || '0',
  },
  {
    title: '企业中标率',
    dataIndex: 'tenderPercent',
    customRender: (text, item) => {
      let content = text;
      if (item.tag) {
        content += `<span style="color: #f04040; background: #fcc; padding: 2px 3px;">${item.tag}</span>`;
      }
      return content;
    },
  },
  {
    title: '关联企业中标率',
    dataIndex: 'competitionTenderPercent',
    customRender: (text, item) => {
      let content = text;
      if (item.competitionTag) {
        content += `<span style="color: #f04040; background: #fcc; padding: 2px 3px;">${item.competitionTag}</span>`;
      }
      return content;
    },
  },
];

// 军队采购失信名单
const ArmyProcurementIllegalColumns = [
  {
    title: '企业名称',
    dataIndex: 'Name',
  },
  {
    title: '类型',
    dataIndex: 'dimensionName',
  },
  {
    title: '黑名单领域',
    dataIndex: 'field',
  },
  {
    title: '列入原因',
    dataIndex: 'CaseReason',
  },
  {
    title: '列入机关',
    dataIndex: 'Court',
  },
  {
    title: '列入日期',
    width: 88,
    dataIndex: 'decisiondate',
    customRender: { name: 'date', options: { pattern: 'X' } },
  },
  {
    title: '移出日期',
    width: 88,
    dataIndex: 'removedate',
    customRender: { name: 'date', options: { pattern: 'X' } },
  },
];

// 资质筛查
const BiddingCompanyCertificationColumns = [
  {
    title: '企业名称',
    dataIndex: 'companyName',
  },
  {
    title: '资质名称',
    dataIndex: 'certificationName',
  },
  {
    title: '证书编号',
    dataIndex: 'certificateNo',
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
  },
  {
    title: '有效期',
    width: 103,
    customRender: {
      name: 'range',
      options: {
        type: 'date',
        from: 'startDate',
        to: 'endDate',
        separator: ' - ',
      },
    },
  },
  {
    title: '状态',
    width: 150,
    dataIndex: 'status',
    customRender: (status) => {
      const codeMap = {
        0: '不存在',
        1: '有效',
        2: '无效',
        3: '暂停',
        4: '撤销',
        5: '注销',
        6: '过期失效',
      };

      const text = codeMap[status] || '-';
      return text;
    },
  },
];

const CompanyBranchColumns = [
  {
    title: '被排查企业',
    dataIndex: 'companyNameDD',
  },
  {
    title: '关联黑名单企业名称',
    dataIndex: 'companyNameRelated',
  },
  {
    title: '关联链',
    customRender: companyBranchPath,
  },
];

const DirectConnectionColumns = [
  {
    title: '黑名单企业名称',
    dataIndex: 'companyNameDD',
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
  },
  {
    title: '列入时间',
    dataIndex: 'joinDate',
    width: 120,
    customRender: { name: 'date', options: { pattern: 'X' } },
  },
  {
    title: '黑名单有效期',
    dataIndex: 'duration',
    width: 100,
    customRender: (data) => {
      return blacklistDurationFormatter(data);
    },
  },
  {
    title: '截止时间',
    dataIndex: 'expiredDate',
    width: 120,
    customRender: (date) => {
      if (!date) {
        return '不限';
      }
      return dateTrans(date, false);
    },
  },
];

const BlackListInvestigationsColumns = [
  {
    title: '企业名称',
    dataIndex: 'startCompanyName',
  },
  {
    title: '关联黑名单企业名称',
    dataIndex: 'companyNameRelated',
  },
  {
    title: '关联路径详情',
    dataIndex: 'relations',
    customRender: (path) => {
      return convertPathToGraph(path);
    },
  },
];

const SpecificRelationshipColumns = BiddingCompanyRelationshipColumns;
const SpecificRelationshipColumnsWithoutSteps = BiddingCompanyRelationshipColumns.filter((item) => item.dataIndex !== 'steps');

export const TABLE_COLUMNS = {
  BiddingCompanyRelationship: BiddingCompanyRelationshipColumns, // 直接关联关系
  BiddingCompanyRelationship2: BiddingCompanyRelationColumns, //疑似关联关系
  HitInnerBlackList: HitInnerBlackListColumns, // 被列入内部黑名单
  Shareholder: ShareholderColumns, // 参股股东被列入内部黑名单
  ForeignInvestment: ForeignInvestmentColumns, // 对外投资主体被列入内部黑名单
  EmploymentRelationship: EmploymentRelationshipColumns, // 与内部黑名单列表存在人员关联
  BlacklistSameSuspectedActualController: SameControllRelation, // 与内部黑名单列表存在相同实际控制人关联
  StaffWorkingOutsideForeignInvestment: StaffWorkingOutsideForeignInvestmentColumns, // 潜在利益冲突-投资任职
  SuspectedInterestConflict: SuspectedInterestConflictColumns, // 疑似潜在利益冲突
  BidAdministrativeJudgement: BidAdministrativeJudgementColumns, // 涉诉围串标记录
  BidAdministrativePenalties: AdministrativePenalties2Columns, // 涉围串标处罚
  governmentPurchaseIllegal: GovernmentPurchaseIllegalColumns, // 涉采购黑名单
  GovProcurementIllegal: GovProcurementIllegalColumns, // 国央企采购黑名单
  JointBiddingAnalysis: JointBiddingAnalysisColumns, //共同投标分析
  ArmyProcurementIllegal: ArmyProcurementIllegalColumns, //军队采购失信名单
  BiddingCompanyCertification: BiddingCompanyCertificationColumns, // 资质筛查
  CompanyBranch: CompanyBranchColumns, // 分支机构
  DirectConnection: DirectConnectionColumns, // 被列入内部黑名单
  BlackListInvestigations: BlackListInvestigationsColumns, // 内部黑名单关联关系
  /**
   * 特定利益关系排查（与直接关联关系相同）
   */
  CrossShareHolding: SpecificRelationshipColumns, // 上下游企业交叉持股
  ActualController: SpecificRelationshipColumnsWithoutSteps, // 上下游为母子公司或由相同的实际控制人控制
  SameEmployee: SpecificRelationshipColumnsWithoutSteps, // 上下游企业主要负责人、董事、监事、高级管理人员相同
  ContactWay: SpecificRelationshipColumnsWithoutSteps, // 上下游企业注册地址、实际办公地址、业务联系人或联系电话相同
  GuaranteeRelation: SpecificRelationshipColumnsWithoutSteps, // 上下游企业一方为另一方贸易合同履约提供担保
  UpAndDownRelation: SpecificRelationshipColumnsWithoutSteps, // 上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商
  OtherRelations: SpecificRelationshipColumnsWithoutSteps, // 其他根据实质重于形式原则认定存在特定利益关系的情形
};
