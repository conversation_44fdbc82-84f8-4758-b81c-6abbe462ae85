/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import {
  BiddingCompanyOverview,
  BiddingDimensionHitsDetails,
  BiddingDimensionOverview,
  BiddingOverviewDescription,
  DiligenceBiddingResponse,
} from 'libs/model/bidding/DiligenceBiddingResponse';
import { compact, intersection, pick, sum, uniq, uniqBy } from 'lodash';
import { DimensionLevel1Enums } from 'libs/enums/diligence/DimensionLevel1Enums';
import { ConfigService } from '../../../libs/config/config.service';
import { In, QueryFailedError, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { DiligenceTenderHistoryEntity } from '../../../libs/entities/DiligenceTenderHistoryEntity';
import { DimensionDefinitionPO, SubDimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { JointBiddingAnalysisModel } from '../../../libs/model/tender/JointBiddingAnalysisModel';
import { getDimensionDescription } from '../../../libs/utils/diligence/diligence.utils';
import * as Bluebird from 'bluebird';
import { DiligenceBiddingDetailPo } from '../../../libs/model/bidding/model/DiligenceBiddingDetailPo';
import * as moment from 'moment';
import MyOssService from '../../basic/my-oss.service';
import { writeFileSync } from 'fs';
import { tmpdir } from 'os';
import { DATE_FORMAT } from '@kezhaozhao/search-utils';
import { v1 as uuidv1 } from 'uuid';
import { DiligenceTenderCertificationEntity } from '../../../libs/entities/DiligenceTenderCertificationEntity';
import { generateNo } from '../common/bidding.utils';

/**
 * 招标排查结果处理
 */
@Injectable()
export class BiddingDiligenceResultHelper {
  private logger: Logger = QccLogger.getLogger(BiddingDiligenceResultHelper.name);
  public tenderDiligenceQueue: RabbitMQ;
  // 高风险维度，命中后排查结果为不通过
  private HighDimensionKeys = [
    DimensionLevel2Enums.BiddingCompanyCertification,
    DimensionLevel2Enums.BiddingCompanyRelationship,
    DimensionLevel1Enums.Risk_InnerBlacklist,
    // DimensionLevel2Enums.PurchaseIllegal,
    DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment,
  ];

  private AllDimensionKeys = [
    DimensionLevel2Enums.BiddingCompanyCertification,
    DimensionLevel2Enums.BiddingCompanyRelationship,
    DimensionLevel2Enums.BiddingCompanyRelationship2,
    DimensionLevel2Enums.JointBiddingAnalysis,
    DimensionLevel1Enums.Risk_InnerBlacklist,
    DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment,
    DimensionLevel3Enums.SuspectedInterestConflict,
    DimensionLevel2Enums.PurchaseIllegal,
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly myOssService: MyOssService,
    @InjectRepository(DiligenceTenderHistoryEntity) private readonly diligenceTenderHistoryRepo: Repository<DiligenceTenderHistoryEntity>,
    @InjectRepository(DiligenceTenderCertificationEntity) private readonly tenderCertificationRepo: Repository<DiligenceTenderCertificationEntity>,
  ) {}

  /**
   * 排查结果格式化处理
   * @param hitDetails
   * @param diligenceRes
   * @param dimensionDefinitions
   * @param keyNoAndNames
   * @param diligenceId
   * @returns
   */
  public async analyzeAndOverviewHitDetails(
    hitDetails: BiddingDimensionHitsDetails[],
    diligenceRes: DiligenceBiddingResponse,
    dimensionDefinitions: DimensionDefinitionPO[],
    keyNoAndNames: JointBiddingAnalysisModel[],
    diligenceId: number,
  ) {
    const flattenedDimensionDefinitions = this.flattenDimensionDefinitions(dimensionDefinitions);
    hitDetails.forEach((x) => {
      if (x.subDimension?.length) {
        x.subDimension = x.subDimension.filter((y) => y?.status == 1);
        x.totalHits = sum(x.subDimension.map((y) => y.totalHits));
        x.subDimension.sort((a, b) => a.sort - b.sort);
      }
    });
    hitDetails.sort((a, b) => a.sort - b.sort);
    diligenceRes.totalHits = sum(hitDetails.map((x) => x.totalHits));
    diligenceRes.dimensionHitsDetails.push(...hitDetails);

    // 聚合维度概览和公司概览
    await this.analyzeOverview(diligenceRes, keyNoAndNames, dimensionDefinitions, diligenceId);
    // 排查的风险等级，包括维度的风险等级、公司的风险等级和总的排查结果
    await this.analyzeLevel(diligenceRes, flattenedDimensionDefinitions);

    return Object.assign(
      new DiligenceBiddingDetailPo(),
      pick(diligenceRes, ['totalHits', 'result', 'description', 'dimensionHitsDetails', 'dimensionOverviews']),
    );
  }

  /**
   * 招标排查结果快照到oss
   * @param diligenceRes
   * @returns
   */
  public async saveTenderDiligenceDetail(diligenceRes: DiligenceBiddingDetailPo) {
    const filename = `${uuidv1()}.json`;
    const filepath = `${tmpdir()}/${filename}`;
    writeFileSync(filepath, JSON.stringify(diligenceRes), { encoding: 'utf-8' });
    const folder = `tender_diligence_snapshot/${moment().format(DATE_FORMAT)}`;
    const ossObject = this.configService.getOssObject(`${folder}`, filename);
    this.logger.debug(`save tender_diligence_snapshot data to oss file: ${ossObject}`);
    // const result = await this.ossService.putStream.call(this.ossService, ossObject, createReadStream(filepath));
    // await this.ossService.putACL.call(this.ossService, result['name'], 'private');
    // return result['name'];
    return await this.myOssService.putSteam(ossObject, filepath);
  }

  /**
   * 生成招标排查编号并保存招标排查记录
   * @param diligenceEntity
   */
  public async saveDiligenceHistory(diligenceEntity: DiligenceTenderHistoryEntity): Promise<DiligenceTenderHistoryEntity> {
    while (true) {
      try {
        diligenceEntity.tenderNo = generateNo('ZB');
        return this.diligenceTenderHistoryRepo.save(diligenceEntity);
      } catch (e) {
        if (e instanceof QueryFailedError && e.message.startsWith('ER_DUP_ENTRY')) {
          this.logger.warn(`ER_DUP_ENTRY tender no ${diligenceEntity.tenderNo}`); // tender no小概率冲突
        } else {
          throw e;
        }
      }
    }
  }

  /**
   * 构造招标排查维度概览和公司概览
   * @param orgId
   * @param diligenceRes
   * @param keyNoAndNames
   * @param dimensionDefinitions
   * @param diligenceId
   * @returns
   */
  private async analyzeOverview(
    diligenceRes: DiligenceBiddingResponse,
    keyNoAndNames: JointBiddingAnalysisModel[],
    dimensionDefinitions: DimensionDefinitionPO[],
    diligenceId: number,
  ) {
    diligenceRes.dimensionOverviews = [];
    diligenceRes.companyOverviews = keyNoAndNames.map((x) =>
      Object.assign(new BiddingCompanyOverview(), {
        keyNo: x.companyId,
        name: x.companyName,
        details: [],
        level: 0, // 风险等级
        status: 1, // 默认入围状态
      }),
    );
    const dimensionHitDetails = diligenceRes.dimensionHitsDetails?.filter((x) => x.totalHits > 0);
    if (!dimensionHitDetails?.length) return;

    await Bluebird.map(dimensionHitDetails, async (dimensionHit) => {
      const dimensionDefinition = dimensionDefinitions.find((x) => x.key == dimensionHit.key);

      // 聚合维度概览
      const dimensionOverview: BiddingDimensionOverview = Object.assign(new BiddingDimensionOverview(), pick(dimensionHit, ['key', 'name']));
      dimensionOverview.details = [];
      switch (dimensionHit.key) {
        case DimensionLevel2Enums.BiddingCompanyRelation:
        case DimensionLevel1Enums.Risk_InterestConflict: {
          const dimensionHits = dimensionHit?.subDimension?.filter((subDimensionHit) => subDimensionHit.totalHits > 0) || [];
          // 深度关系排查、利益冲突排查要按子维度分开统计
          for (const subDimensionHit of dimensionHits) {
            const subDimensionDefinition = dimensionDefinition.subDimensionList.find((x) => x.key == subDimensionHit.key);
            dimensionOverview.details.push(this.getDimensionOverviewDescription(subDimensionDefinition, subDimensionHit, keyNoAndNames));
          }
          break;
        }
        case DimensionLevel2Enums.BiddingCompanyCertification: {
          // 排查维度统计缺失、失效资质
          dimensionOverview.details.push(await this.getCertificationOverviewDescription(dimensionDefinition, dimensionHit, keyNoAndNames, diligenceId));
          break;
        }
        default: {
          // 默认按整个维度来统计命中公司
          dimensionOverview.details.push(this.getDimensionOverviewDescription(dimensionDefinition, dimensionHit, keyNoAndNames));
        }
      }
      diligenceRes.dimensionOverviews.push(dimensionOverview);

      // 聚合公司概览
      for (const companyOverview of diligenceRes.companyOverviews) {
        // 公司在此维度的命中情况
        const companyDimensionOverview: BiddingDimensionOverview = Object.assign(new BiddingDimensionOverview(), pick(dimensionHit, ['key', 'name']));
        companyDimensionOverview.details = [];
        switch (dimensionHit.key) {
          case DimensionLevel2Enums.BiddingCompanyRelation:
          case DimensionLevel1Enums.Risk_InterestConflict: {
            const dimensionHits = dimensionHit?.subDimension?.filter((subDimensionHit) => subDimensionHit.totalHits > 0) || [];
            for (const subDimensionHit of dimensionHits) {
              const subDimensionDefinition = dimensionDefinition.subDimensionList.find((x) => x.key == subDimensionHit.key);
              companyDimensionOverview.details.push(
                this.getCompanyDimensionOverviewDescription(companyOverview.keyNo, subDimensionDefinition, subDimensionHit, keyNoAndNames),
              );
            }
            break;
          }
          case DimensionLevel2Enums.BiddingCompanyCertification: {
            // 公司维度统计缺失、失效资质
            companyDimensionOverview.details.push(
              await this.getCertificationCompanyOverviewDescription(companyOverview.keyNo, dimensionDefinition, dimensionHit, keyNoAndNames, diligenceId),
            );
            break;
          }
          default: {
            companyDimensionOverview.details.push(
              this.getCompanyDimensionOverviewDescription(companyOverview.keyNo, dimensionDefinition, dimensionHit, keyNoAndNames),
            );
          }
        }
        companyOverview.details.push(companyDimensionOverview);
      }
    });

    // 过滤未命中的维度
    diligenceRes.dimensionOverviews?.forEach((x) => (x.details = compact(x.details)));
    diligenceRes.dimensionOverviews = diligenceRes.dimensionOverviews?.filter((x) => x?.details?.length);
    diligenceRes.companyOverviews.forEach((x) => {
      x.details.forEach((y) => (y.details = compact(y.details)));
      x.details = x.details?.filter((y) => y?.details?.length);
    });

    // 维度详情的全部数据不返回
    dimensionHitDetails.forEach((x) => {
      x.allData = undefined;
      if (x.subDimension?.length) {
        x.subDimension.forEach((y) => (y.allData = undefined));
      }
    });
  }

  /**
   * 排查的风险等级，包括维度的风险等级、公司的风险等级和总的排查结果
   * @param diligenceRes
   * @param flattenedDimensionDefinitions
   */
  private async analyzeLevel(diligenceRes: DiligenceBiddingResponse, flattenedDimensionDefinitions: DimensionDefinitionPO[]) {
    // 统计各个公司的风险等级
    const allKeys = [];
    diligenceRes.companyOverviews = await Bluebird.map(
      diligenceRes.companyOverviews,
      (companyOverview) => {
        const keys = [];
        // const keys = this.flattenCompanyKeys(companyOverview);
        companyOverview.details?.forEach((x) => {
          if (x.key != DimensionLevel2Enums.BiddingCompanyCertification || x.details[0]?.necessaryFlag == 1) {
            keys.push(x.key);
            x.details?.forEach((y) => keys.push(y.key));
          }
        });
        allKeys.push(...keys);
        if (intersection(keys, this.HighDimensionKeys).length > 0) {
          companyOverview.level = 2;
        } else if (keys.length > 0) {
          companyOverview.level = 1;
        } else {
          companyOverview.level = 0;
        }
        return companyOverview;
      },
      { concurrency: 10 },
    );
    // 统计各个维度的风险等级
    diligenceRes.dimensionHitsDetails = await Bluebird.map(
      diligenceRes.dimensionHitsDetails,
      (x) => {
        let keys = [];
        // 统计各维度的风险等级
        if (x.totalHits > 0) {
          if (x.key != DimensionLevel2Enums.BiddingCompanyCertification || x.data?.Result[0]?.necessaryFlag == 1) {
            keys.push(x.key);
          }
        }
        if (x.subDimension?.length) {
          keys.push(...x.subDimension.filter((y) => y.totalHits > 0).map((y) => y.key));
          if (x.key == DimensionLevel2Enums.BiddingCompanyCertification && x.subDimension.filter((y) => y.totalHits > 0 && y.necessaryFlag == 1)) {
            keys.push(x.key);
          }
        }
        keys = intersection(allKeys, keys); // 正常来讲allKeys包含keys，但共同投标分析需要判断异常中标率，此时可能allKeys未命中但keys命中，需要以allKeys为准
        if (intersection(keys, this.HighDimensionKeys).length > 0) {
          x.level = 2;
        } else if (keys.length > 0) {
          x.level = 1;
        } else {
          x.level = 0;
        }
        return x;
      },
      { concurrency: 10 },
    );

    // 统计总的排查结果
    diligenceRes.result = Math.max(...diligenceRes.dimensionHitsDetails.map((x) => x.level));

    if (intersection(this.AllDimensionKeys, allKeys).length > 0) {
      const suffix = diligenceRes.result == 2 ? '建议排除不合格的投标人或重新选择投标企业' : '建议对排查结果进一步核实，规避不合格的投标企业';
      const interKeys = intersection(this.AllDimensionKeys, allKeys);
      diligenceRes.description = `被排查企业存在【${interKeys
        .map((x) => flattenedDimensionDefinitions.find((t) => x == t.key).description)
        .join('】、【')}】等招标风险，${suffix}`;
    } else {
      diligenceRes.description = '本次招标排查未发现异常风险信息，请结合其他相关材料综合判定排查结果';
    }
  }

  /**
   * 获取指定公司各维度的命中情况
   * @param keyNo
   * @param dimensionDefinition
   * @param hitDetail
   * @param keyNoAndNames
   * @returns
   */
  private getCompanyDimensionOverviewDescription(
    keyNo: string,
    dimensionDefinition: DimensionDefinitionPO | SubDimensionDefinitionPO,
    hitDetail: BiddingDimensionHitsDetails,
    keyNoAndNames: JointBiddingAnalysisModel[],
  ): BiddingOverviewDescription {
    let hit = false; // 是否命中
    const desData = {
      name: hitDetail.name,
    };
    let necessaryFlag;
    const keyNos: string[] = [];
    let companyList: JointBiddingAnalysisModel[] = [];
    const hitType = [];
    switch (hitDetail.key) {
      case DimensionLevel2Enums.BiddingCompanyRelationship:
      case DimensionLevel2Enums.BiddingCompanyRelationship2: {
        hitDetail.data.forEach((x) => {
          if (keyNo == x.startCompanyKeyno) {
            hit = true;
            keyNos.push(x.endCompanyKeyno);
            this.getHitType(x, hitType);
          }
          if (keyNo == x.endCompanyKeyno) {
            hit = true;
            keyNos.push(x.startCompanyKeyno);
            this.getHitType(x, hitType);
          }
        });
        break;
      }
      case DimensionLevel2Enums.JointBiddingAnalysis: {
        hitDetail.allData?.Result?.forEach((x) => {
          if (x?.tag == '中标率异常' || x?.competitionTag == '中标率异常') {
            if (keyNo == x.companyId) {
              hit = true;
              keyNos.push(x.competitionCompanyId);
            }
            if (keyNo == x.competitionCompanyId) {
              hit = true;
              keyNos.push(x.companyId);
            }
          }
        });
        break;
      }
      case DimensionLevel2Enums.PurchaseIllegal: {
        let count = 0;
        for (const subHitDetail of hitDetail.subDimension.filter((x) => x.totalHits > 0)) {
          switch (subHitDetail.key) {
            case DimensionLevel3Enums.ArmyProcurementIllegal:
            case DimensionLevel2Enums.BidAdministrativePenalties:
            case DimensionLevel3Enums.GovProcurementIllegal:
            case DimensionLevel3Enums.GovernmentPurchaseIllegal: {
              subHitDetail.allData?.Result?.forEach((x) => {
                if (keyNo == x.keyno || keyNo == x.KeyNo) {
                  hit = true;
                  count++;
                }
              });
              break;
            }
            case DimensionLevel2Enums.BidAdministrativeJudgement: {
              subHitDetail.allData?.Result?.forEach((x) => {
                // if (x.defendant?.length && x.defendant.includes(keyNo)) {
                //   hit = true;
                //   count++;
                // } else if (x.othercns?.length && x.othercns.includes(keyNo)) {
                //   hit = true;
                //   count++;
                // } else if (x.outsider?.length && x.outsider.includes(keyNo)) {
                //   hit = true;
                //   count++;
                // }
                if (
                  x?.defendant?.includes(keyNo) ||
                  x?.othercns?.includes(keyNo) ||
                  x?.outsider?.includes(keyNo) ||
                  (x.dataType === 'case' && x.DefendantNames?.includes(keyNo))
                ) {
                  hit = true;
                  count++;
                }
              });
              break;
            }
          }
        }
        Object.assign(desData, { count });
        break;
      }
      case DimensionLevel1Enums.Risk_InnerBlacklist: {
        for (const subHitDetail of hitDetail.subDimension.filter((x) => x.totalHits > 0)) {
          subHitDetail.allData?.Result?.forEach((x) => {
            if (keyNo == x['companyKeynoDD'] || keyNo == x['companyKeynoRelated'] || keyNo == x['startCompanyKeyno']) {
              hit = true;
              // 被列入内部黑名单维度，没有companyKeynoRelated，此时关联公司就是自己
              companyList.push({
                companyId: x['companyKeynoRelated'] || x['companyId'] || x['companyKeynoDD'],
                companyName: x['companyNameRelated'] || x['companyName'] || x['companyNameDD'],
              });
            }
          });
        }

        break;
      }
      case DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment:
      case DimensionLevel3Enums.SuspectedInterestConflict: {
        const persons = [];
        const dimensionDes = [];
        hitDetail.allData?.Result?.forEach((x) => {
          if (keyNo == x.sourceCompanyId) {
            hit = true;
            if (x.relationship && x.personNo?.endsWith(x.name)) {
              let personNo = x.personNo;
              let splited = personNo.split('_');
              splited = splited.slice(0, splited.length - 1);
              personNo = splited.join('_');
              persons.push(`${personNo}-${x.name}`);
            } else {
              persons.push(`${x.personNo}-${x.name}`);
            }
            if (x.dimension) {
              dimensionDes.push(...x.dimension.split('、'));
            }
          }
        });
        Object.assign(desData, { personNames: uniq(persons).join('、') });
        if (dimensionDes.length) {
          Object.assign(desData, { dimension: uniq(dimensionDes).join('、') });
        }
        break;
      }
      case DimensionLevel2Enums.BiddingCompanyCertification: {
        let count = 0;
        hitDetail.subDimension?.forEach((x) => {
          if (x?.data.length > 0) {
            x.data.map((item) => {
              necessaryFlag = item.necessaryFlag;
              if (item.keyNo == keyNo && ['失效', '缺失'].includes(item.statusStr)) {
                hit = true;
                keyNos.push(x?.data[0]?.keyNo);
                count += 1;
              }
            });
          }
        });
        Object.assign(desData, { count });
        break;
      }
    }
    if (!hit) return null;

    if (keyNos.length) {
      companyList = keyNoAndNames.filter((x) => keyNos.includes(x.companyId));
    }
    if (companyList.length) {
      companyList = uniqBy(companyList, (x) => x.companyId);
      const companyNames = companyList.map((x) => x.companyName).join('、');
      Object.assign(desData, { companyNames });
    }

    let description = getDimensionDescription(dimensionDefinition.template2, desData);

    // 只命中被列入内部黑名单情况文案，RA-16755
    if (hitDetail.key == DimensionLevel1Enums.Risk_InnerBlacklist && companyList?.length == 1 && keyNo == companyList[0].companyId) {
      description = `${companyList[0].companyName} 被列入内部黑名单`;
    }

    return {
      key: hitDetail.key,
      name: hitDetail.name,
      keyNoAndNames: companyList,
      description,
      necessaryFlag,
      hitType,
    };
  }

  /**
   * 获取指定维度各公司的命中情况
   * @param dimensionDefinition
   * @param hitDetail
   * @param keyNoAndNames
   * @returns
   */
  private getDimensionOverviewDescription(
    dimensionDefinition: DimensionDefinitionPO | SubDimensionDefinitionPO,
    hitDetail: BiddingDimensionHitsDetails,
    keyNoAndNames: JointBiddingAnalysisModel[],
  ): BiddingOverviewDescription {
    let hit = false; // 是否命中
    const desData = {
      name: hitDetail.name,
      count: hitDetail.totalHits,
    };

    const keyNos: string[] = [];
    let companyList: JointBiddingAnalysisModel[] = [];
    const hitType = [];
    switch (hitDetail.key) {
      case DimensionLevel2Enums.BiddingCompanyRelationship:
      case DimensionLevel2Enums.BiddingCompanyRelationship2: {
        hitDetail.data.forEach((x) => {
          hit = true;
          keyNos.push(x.startCompanyKeyno);
          keyNos.push(x.endCompanyKeyno);
          this.getHitType(x, hitType);
        });
        break;
      }
      case DimensionLevel2Enums.JointBiddingAnalysis: {
        hitDetail.allData?.Result?.forEach((x) => {
          if (x?.tag == '中标率异常' || x?.competitionTag == '中标率异常') {
            hit = true;
            keyNos.push(x.companyId);
            keyNos.push(x.competitionCompanyId);
          }
        });
        break;
      }
      case DimensionLevel2Enums.PurchaseIllegal: {
        for (const subHitDetail of hitDetail.subDimension.filter((x) => x?.totalHits > 0)) {
          switch (subHitDetail.key) {
            case DimensionLevel2Enums.BidAdministrativePenalties:
            case DimensionLevel3Enums.ArmyProcurementIllegal:
            case DimensionLevel3Enums.GovProcurementIllegal:
            case DimensionLevel3Enums.GovernmentPurchaseIllegal: {
              subHitDetail.allData?.Result?.forEach((x) => {
                hit = true;
                keyNos.push(x.keyno || x.KeyNo);
              });
              break;
            }
            case DimensionLevel2Enums.BidAdministrativeJudgement: {
              const tempIds = keyNoAndNames.map((x) => x.companyId);
              subHitDetail.allData?.Result?.forEach((x) => {
                if (x.defendant?.length && intersection(x.defendant, tempIds).length) {
                  hit = true;
                  keyNos.push(...intersection(x.defendant, tempIds));
                }
                if (x.othercns?.length && intersection(x.othercns, tempIds).length) {
                  hit = true;
                  keyNos.push(...intersection(x.othercns, tempIds));
                }
                if (x.outsider?.length && intersection(x.outsider, tempIds).length) {
                  hit = true;
                  keyNos.push(...intersection(x.outsider, tempIds));
                }
                if (x.dataType === 'case') {
                  hit = true;
                  keyNos.push(...intersection(x.DefendantNames, tempIds));
                }
              });
              break;
            }
          }
        }
        break;
      }
      case DimensionLevel1Enums.Risk_InnerBlacklist: {
        for (const subHitDetail of hitDetail.subDimension.filter((x) => x?.totalHits > 0)) {
          subHitDetail.allData?.Result?.forEach((x) => {
            hit = true;
            keyNos.push(x['companyKeynoDD'] || x['startCompanyKeyno'] || x['companyKeynoRelated']);
          });
        }
        break;
      }
      case DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment:
      case DimensionLevel3Enums.SuspectedInterestConflict: {
        hitDetail.allData?.Result?.forEach((x) => {
          hit = true;
          keyNos.push(x.sourceCompanyId);
        });
        break;
      }
    }
    if (!hit) return null;

    if (keyNos.length) {
      companyList = keyNoAndNames.filter((x) => keyNos.includes(x.companyId));
    }
    if (companyList.length) {
      companyList = uniqBy(companyList, (x) => x.companyId);
      const companyNames = companyList.map((x) => x.companyName).join('、');
      Object.assign(desData, { companyNames });
    }

    const description = getDimensionDescription(dimensionDefinition.template, desData);
    return { key: hitDetail.key, name: hitDetail.name, keyNoAndNames: companyList, description, hitType };
  }

  /**
   * 招标排查-维度打平
   * @param dimensionDefinitions
   * @returns
   */
  private flattenDimensionDefinitions(dimensionDefinitions: DimensionDefinitionPO[]): DimensionDefinitionPO[] {
    const list = [];
    dimensionDefinitions.forEach((x) => {
      list.push(x);
      x.subDimensionList?.forEach((sub) => {
        list.push(Object.assign(new DimensionDefinitionPO(), sub));
      });
    });
    return list;
  }

  /**
   * 排查-资质筛查结果汇总(公司总览)
   */
  private async getCertificationOverviewDescription(
    dimensionDefinition: DimensionDefinitionPO | SubDimensionDefinitionPO,
    hitDetail: BiddingDimensionHitsDetails,
    keyNoAndNames: JointBiddingAnalysisModel[],
    diligenceId: number,
  ): Promise<BiddingOverviewDescription> {
    // 获取排查结果中失效、缺失的数据
    const hitStatus = [0, 2];
    const resultCount = await this.tenderCertificationRepo.query(
      `select key_no as keyNo,count(id) as count,necessary_flag as necessaryFlag from due_tender_certification_result where diligence_id = ? and status in (?) group by key_no`,
      [diligenceId, hitStatus],
    );
    if (!resultCount.length || !resultCount[0]?.keyNo) {
      return null;
    }
    let necessaryFlag = 0;
    const keyNos: string[] = [];
    resultCount.forEach((record) => {
      keyNos.push(record.keyNo);
      necessaryFlag = record.necessaryFlag;
    });
    if (necessaryFlag == 0) {
      //非必须情况，不需要生成
      return null;
    }
    const count = sum(resultCount.map((x) => Number(x.count)));
    const desData = {
      name: hitDetail.name,
      count,
    };
    let companyList: JointBiddingAnalysisModel[] = [];
    if (keyNos.length) {
      companyList = keyNoAndNames.filter((x) => keyNos.includes(x.companyId));
    }
    if (companyList.length) {
      companyList = uniqBy(companyList, (x) => x.companyId);
      const companyNames = companyList.map((x) => x.companyName).join('、');
      Object.assign(desData, { companyNames });
    }
    const description = getDimensionDescription(dimensionDefinition.template, desData);
    return {
      key: hitDetail.key,
      name: hitDetail.name,
      keyNoAndNames: companyList,
      description,
      necessaryFlag,
      hitType: ['certification'],
    };
  }

  /**
   * 排查-资质筛查结果汇总(公司资质明细)
   */
  private async getCertificationCompanyOverviewDescription(
    keyNo: string,
    dimensionDefinition: DimensionDefinitionPO | SubDimensionDefinitionPO,
    hitDetail: BiddingDimensionHitsDetails,
    keyNoAndNames: JointBiddingAnalysisModel[],
    diligenceId: number,
  ): Promise<BiddingOverviewDescription> {
    // 获取排查结果中失效、缺失的数据
    const hitStatus = [0, 2];
    const [result, count] = await this.tenderCertificationRepo.findAndCount({
      where: {
        keyNo,
        diligenceId,
        status: In(hitStatus),
      },
    });
    if (!result.length) {
      return null;
    }
    const necessaryFlag = result[0].necessaryFlag;
    const desData = {
      name: hitDetail.name,
      count,
    };
    const companyList: JointBiddingAnalysisModel[] = keyNoAndNames.filter((x) => x.companyId == keyNo);
    if (companyList.length) {
      const companyNames = companyList.map((x) => x.companyName).join('、');
      Object.assign(desData, { companyNames });
    }
    const description = getDimensionDescription(dimensionDefinition.template, desData);
    return {
      key: hitDetail.key,
      name: hitDetail.name,
      keyNoAndNames: companyList,
      description,
      necessaryFlag,
      hitType: ['certification'],
    };
  }

  private getHitType(x, hitType: string[]) {
    if (x?.type) {
      hitType.push(x.type);
    }
    if (x?.steps == 2) {
      x?.relations?.forEach((y) => {
        if (y?.roles?.length > 0) {
          y.roles.forEach((z) => {
            if (z?.type) {
              hitType.push(z.type);
            }
          });
        }
      });
    }
  }
}
