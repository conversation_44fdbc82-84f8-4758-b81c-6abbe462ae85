import { Injectable } from '@nestjs/common';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverGraphService } from 'apps/data/source/rover.graph.service';
import { TenderService } from 'apps/data/source/tender.service';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { BiddingDimensionHitsDetails } from 'libs/model/bidding/DiligenceBiddingResponse';
import { flatMap, intersection, pick, sum, union, uniq } from 'lodash';
import { ConfigService } from '../../../libs/config/config.service';

import { Client } from '@elastic/elasticsearch';
import { DiligenceBiddingRequest } from '../../../libs/model/bidding/DiligenceBiddingRequest';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { BiddingDetailService } from '../service/bidding.detail.service';
import { EnterpriseLibService } from '../../data/source/enterprise.lib.service';
import { CompanySearchService } from '../../company/company-search.service';
import { HttpUtilsService } from '../../../libs/config/httputils.service';
import { CompanyDetailService } from '../../company/company-detail.service';
import { BiddingDiligenceRelationshipHelperV2 } from './bidding.diligence.relationship.helperV2';
import { GroupCompanyRelationsRequest } from '../../../libs/model/company/GroupCompanyRelationsRequest';
import * as Bluebird from 'bluebird';
import { KysCompanyResponseDetails, KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { CompanyVerifiedPersonRequest } from '../../company/model/CompanyVerifiedPersonRequest';

/**
 * 招投标关系排查（直接关系、疑似关系）
 */
@Injectable()
export class BiddingDiligenceRelationshipHelper {
  public snapshotQueue: RabbitMQ;
  private kysEsClient: Client;
  private readonly kysIndexName: string;
  private logger: Logger = QccLogger.getLogger(BiddingDiligenceRelationshipHelper.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly graphService: RoverGraphService,
    private readonly tenderService: TenderService,
    private readonly biddingDetailService: BiddingDetailService,
    private readonly enterpriseLibService: EnterpriseLibService,
    private readonly companySearchService: CompanySearchService,
    private readonly companyDetailService: CompanyDetailService,
    private readonly httpUtils: HttpUtilsService,
    private readonly biddingDiligenceRelationshipHelperV2: BiddingDiligenceRelationshipHelperV2,
  ) {
    this.kysEsClient = new Client({
      nodes: configService.esConfig.kysCompany.nodes,
      ssl: { rejectUnauthorized: false },
    });
    this.kysIndexName = configService.esConfig.kysCompany.indexName;
  }

  /**
   * 关系排查
   * @param dimensionDefinitions
   * @param params
   * @returns
   */
  public async getBiddingCompanyRelations(
    dimensionDefinitions: DimensionDefinitionPO[],
    params: DiligenceBiddingRequest,
  ): Promise<BiddingDimensionHitsDetails> {
    const dimensionDefinition = dimensionDefinitions.find((x) => x.key == DimensionLevel2Enums.BiddingCompanyRelation);
    const result = Object.assign(new BiddingDimensionHitsDetails(), pick(dimensionDefinition, ['key', 'name', 'status', 'sort']), { totalHits: 0 });
    if (dimensionDefinition.status == 0) {
      return result;
    }

    result.subDimension = [];

    const companyLinkMap: Map<string, Set<string>> = new Map<string, Set<string>>();

    // 投标公司直接关系排查
    const relationShipDim = dimensionDefinition.subDimensionList.find((x) => x.status == 1 && x.key == DimensionLevel2Enums.BiddingCompanyRelationship);
    if (relationShipDim) {
      const hitDetail = Object.assign(new BiddingDimensionHitsDetails(), pick(relationShipDim, ['key', 'name', 'status', 'sort']), {
        totalHits: 0,
      });
      const start = Date.now();
      const groupCompanyRelationsRequest = new GroupCompanyRelationsRequest();
      const types = relationShipDim.strategyModel.detailsParams.find((x) => x.field == QueryParamsEnums.types).fieldVal;
      let graphTypes = types;
      if (types.includes('ControlRelation')) {
        graphTypes = types.filter((x) => x !== 'ControlRelation');
      }
      groupCompanyRelationsRequest.types = graphTypes;
      groupCompanyRelationsRequest.depth = relationShipDim.strategyModel.detailsParams.find((x) => x.field == QueryParamsEnums.depth).fieldVal;
      groupCompanyRelationsRequest.percentage = relationShipDim.strategyModel.detailsParams.find((x) => x.field == QueryParamsEnums.percentage).fieldVal;
      groupCompanyRelationsRequest.excludedTypes = relationShipDim.strategyModel.detailsParams.find((x) => x.field == QueryParamsEnums.excludedTypes).fieldVal;
      groupCompanyRelationsRequest.companyIds = params.keyNos;
      const relations = await this.graphService.getCompanyRelations(params.orgId, groupCompanyRelationsRequest);
      let keyNoPatch: string[][];
      if (relations?.length > 0) {
        // 记录两家企业之间已经存在直接关联关系，后续不再进行疑似关联排查
        this.processCompanyLinkMap(relations, companyLinkMap);
      }
      keyNoPatch = this.getNoPatchKeyNos(params.keyNos, companyLinkMap);
      if (keyNoPatch.length > 0) {
        // 存在无直接关系情况，补充人员排查情况
        const personTypes = intersection(types, ['Legal', 'Employ', 'Invest', 'ActualController']);
        if (personTypes.length > 0) {
          const personRelation = await this.getPersonRelations(keyNoPatch, personTypes);
          if (personRelation?.length > 0) {
            this.processCompanyLinkMap(personRelation, companyLinkMap);
            relations.push(...personRelation);
          }
        }
      }
      keyNoPatch = this.getNoPatchKeyNos(params.keyNos, companyLinkMap);
      if (keyNoPatch.length > 0 && types.includes('ControlRelation')) {
        // 存在无直接关系情况，补充控制关系排查情况
        const controlRelations = await this.processControlRelations(keyNoPatch);
        if (controlRelations?.length > 0) {
          this.processCompanyLinkMap(controlRelations, companyLinkMap);
          relations.push(...controlRelations);
        }
      }
      if (relations?.length > 0) {
        hitDetail.totalHits = relations.length;
        hitDetail.data = relations;
      }
      const end = Date.now();
      this.logger.info(`tender diligence key=${relationShipDim.key} cost=${end - start}`);
      result.subDimension.push(hitDetail);
    }

    // 投标公司疑似关系排查
    const relationShip2Dim = dimensionDefinition.subDimensionList.find((x) => x.status == 1 && x.key == DimensionLevel2Enums.BiddingCompanyRelationship2);
    if (relationShip2Dim) {
      const hitDetail = Object.assign(new BiddingDimensionHitsDetails(), pick(relationShip2Dim, ['key', 'name', 'status', 'sort']), {
        totalHits: 0,
      });
      const keyNoPatch = this.getNoPatchKeyNos(params.keyNos, companyLinkMap);
      const start = Date.now();
      const types = relationShip2Dim.strategyModel.detailsParams.find((x) => x.field == QueryParamsEnums.types).fieldVal;
      const excludedNodes = relationShip2Dim.strategyModel.detailsParams.find((x) => x.field == QueryParamsEnums.excludedTypes)?.fieldVal;
      const mailMatchingMode = relationShip2Dim.strategyModel.detailsParams.find((x) => x.field == QueryParamsEnums.mailMatchingMode)?.fieldVal;
      const addressMatchingMode = relationShip2Dim.strategyModel.detailsParams.find((x) => x.field == QueryParamsEnums.addressMatchingMode)?.fieldVal;
      const extendParams = { excludedNodes, types, mailMatchingMode, addressMatchingMode };
      if (keyNoPatch.length) {
        const diligenceId = params.diligenceId;
        let allKeyNos = await this.biddingDiligenceRelationshipHelperV2.getAllCompanyIdByDiligenceId(diligenceId);
        if (!allKeyNos || allKeyNos.length == 0) {
          allKeyNos = params.keyNos;
        }
        const relations2 = await this.biddingDiligenceRelationshipHelperV2.getBiddingCompanyRelationship3(keyNoPatch, allKeyNos, extendParams);
        if (relations2?.length > 0) {
          hitDetail.totalHits = relations2.length;
          hitDetail.data = relations2;
        }
      }

      const end = Date.now();
      this.logger.info(`tender diligence key=${relationShip2Dim.key} cost=${end - start}`);
      result.subDimension.push(hitDetail);
    }

    result.totalHits = sum(result.subDimension.map((x) => x.totalHits));
    return result;
  }

  private getNoPatchKeyNos(keyNos: string[], companyLinkMap: Map<string, Set<string>>) {
    const keyNoPatch: string[][] = [];
    for (let i = 0; i < keyNos.length; i++) {
      for (let j = i + 1; j < keyNos.length; j++) {
        if (companyLinkMap.get(keyNos[i])?.has(keyNos[j]) || companyLinkMap.get(keyNos[j])?.has(keyNos[i])) {
          // keyNos[i] 和 keyNos[j] 直接存在直接关联，不再进行疑似关联计算
        } else {
          keyNoPatch.push([keyNos[i], keyNos[j]]);
        }
      }
    }
    return keyNoPatch;
  }

  private processCompanyLinkMap(relations: any[], companyLinkMap: Map<string, Set<string>>) {
    relations.forEach((x) => {
      if (companyLinkMap.has(x.startCompanyKeyno)) {
        companyLinkMap.get(x.startCompanyKeyno).add(x.endCompanyKeyno);
      } else {
        companyLinkMap.set(x.startCompanyKeyno, new Set([x.endCompanyKeyno]));
      }
    });
  }

  private async getPersonRelations(keyNoPatch: string[][], personTypes: string[]) {
    const keyNos = uniq(flatMap(keyNoPatch));
    const allRelations: any[] = [];
    const { Result: companyDetails } = await this.companySearchService.companySearchForKys(
      Object.assign(new KysCompanySearchRequest(), {
        includeFields: ['id', 'name'],
        pageIndex: 1,
        pageSize: keyNos.length,
        filter: { ids: keyNos },
      }),
    );
    //查询主要人员信息 组装到companyDetails里
    await Bluebird.map(companyDetails, async (company) => {
      const getPerson = [];
      const defaultPerson = () => [];
      const actualControllerList = [];
      getPerson.push(personTypes.includes('Legal') ? this.enterpriseLibService.getLegalPerson(company.id) : defaultPerson());
      getPerson.push(personTypes.includes('Employ') ? this.enterpriseLibService.getEmployeeList(company.id) : defaultPerson());
      getPerson.push(personTypes.includes('Invest') ? this.enterpriseLibService.getPartnerList(company.id) : defaultPerson());
      getPerson.push(personTypes.includes('ActualController') ? this.enterpriseLibService.getActualController(company.id) : null);
      const [legalPerson, employeeList, partnerList, actualController] = await Bluebird.all(getPerson);
      if (null !== actualController && actualController !== undefined) {
        const names = actualController?.Names.flatMap(
          (nameGroup) =>
            nameGroup?.Names.PersonList?.map((person) => ({
              keyNo: person.KeyNo,
              name: person.Name,
              job: person.Job,
              history: false,
            })) || [],
        );

        actualControllerList.push(...names);
      }
      const allCurrentPersons = union(legalPerson, employeeList, partnerList, actualControllerList);
      Object.assign(company, { legalPerson, employeeList, partnerList, actualControllerList, allCurrentPersons });
    });
    await Bluebird.map(
      keyNoPatch,
      async (patch) => {
        const companyA = companyDetails.find((c) => c.id == patch[0]);
        const companyB = companyDetails.find((c) => c.id == patch[1]);
        const relations = await this.findCompanyPersonRelations(companyA, companyB);
        if (relations.length) {
          allRelations.push(...relations); // 有关联关系不需要找二层关系
        }
      },
      { concurrency: 5 },
    );
    return allRelations;
  }

  private async findCompanyPersonRelations(companyA: KysCompanyResponseDetails, companyB: KysCompanyResponseDetails) {
    if (!(companyA['allCurrentPersons'].length > 0) || !(companyB['allCurrentPersons'].length > 0)) {
      return [];
    }
    const relations = [];
    // 同名人员
    const nameMapA: Map<string, Set<string>> = new Map<string, Set<string>>();
    companyA['allCurrentPersons']?.forEach((x) => {
      if (nameMapA.has(x.name)) {
        nameMapA.get(x.name).add(x.keyNo);
      } else {
        nameMapA.set(x.name, new Set<string>([x.keyNo]));
      }
    });
    const repeatCheck: Set<string> = new Set<string>();
    for (const itemB of companyB['allCurrentPersons']) {
      if (!repeatCheck.has(itemB.keyNo)) {
        repeatCheck.add(itemB.keyNo);
        // 名字相同，keyNo不同
        if (nameMapA.has(itemB.name) && !nameMapA.get(itemB.name).has(itemB.keyNo) && !itemB.history) {
          const params = Object.assign(new CompanyVerifiedPersonRequest(), {
            Companys: [
              { Name: companyA.name, CreditCode: companyA.creditcode },
              {
                Name: companyB.name,
                CreditCode: companyB.creditcode,
              },
            ],
            PersonName: itemB.name,
          });
          const result = await this.companyDetailService.getCompanyVerifiedPerson(params);
          if (result?.[0]?.length == 2) {
            nameMapA.get(itemB.name).forEach((personKeyNoA) => {
              const relation = this.buildPersonRelationship(companyA, companyB, itemB, personKeyNoA);
              relations.push(relation);
            });
          }
        }
      }
    }
    return relations;
  }

  private buildPersonRelationship(companyA: KysCompanyResponseDetails, companyB: KysCompanyResponseDetails, itemB: any, personKeyNoA: string) {
    return {
      endCompanyKeyno: companyB.id,
      endCompanyName: companyB.name,
      history: false,
      relations: [
        {
          'Company.keyno': companyA.id,
          'Company.credit_code': companyA.creditcode,
          'Company.name': companyA.name,
        },
        {
          role: this.unionPersonRole(companyA, personKeyNoA).role,
          endid: companyA.id,
          startid: personKeyNoA,
          type: this.unionPersonRole(companyA, personKeyNoA).type,
          direction: -1,
        },
        {
          'Person.name': itemB.name,
          'Person.keyno': personKeyNoA,
        },
        {
          role: this.unionPersonRole(companyB, itemB.keyNo).role,
          endid: companyB.id,
          startid: personKeyNoA,
          type: this.unionPersonRole(companyB, itemB.keyNo).type,
          direction: 1,
        },
        {
          'Company.keyno': companyB.id,
          'Company.credit_code': companyB.creditcode,
          'Company.name': companyB.name,
        },
      ],
      startCompanyKeyno: companyA.id,
      startCompanyName: companyA.name,
      steps: 2,
    };
  }

  /**
   * 合并人员身份
   * @param company
   * @param personNo
   * @returns
   */
  private unionPersonRole(company: KysCompanyResponseDetails, personNo: string) {
    const roles: string[] = [];
    let type = '';
    company['legalPerson']?.forEach((x) => {
      if (x.keyNo == personNo) {
        roles.push('法定代表人');
        type = 'Legal';
      }
    });
    company['employeeList']?.forEach((x) => {
      if (x.keyNo == personNo) {
        roles.push('董监高');
        type = 'Employ';
      }
    });
    company['partnerList']?.forEach((x) => {
      if (x.keyNo == personNo) {
        roles.push('股东');
        type = 'Invest';
      }
    });
    company['actualControllerList']?.forEach((x) => {
      if (x.keyNo == personNo) {
        roles.push('实际控制人');
        type = 'ActualController';
      }
    });
    return { role: uniq(roles).join(','), type };
  }

  public async processControlRelations(keyNoPatch: string[][]) {
    const keyNos = uniq(flatMap(keyNoPatch));
    const allRelations: any[] = [];
    const { Result: companyDetails } = await this.companySearchService.companySearchForKys(
      Object.assign(new KysCompanySearchRequest(), {
        includeFields: ['id', 'name'],
        pageIndex: 1,
        pageSize: keyNos.length,
        filter: { ids: keyNos },
      }),
    );
    await Bluebird.map(
      keyNoPatch,
      async (patch) => {
        const companyA = companyDetails.find((c) => c.id == patch[0]);
        const companyB = companyDetails.find((c) => c.id == patch[1]);
        const relations = await this.findControlRelations(companyA, companyB);
        if (relations.length) {
          allRelations.push(...relations); // 有关联关系不需要找二层关系
        }
      },
      { concurrency: 5 },
    );
    return allRelations;
  }

  private async findControlRelations(companyA: KysCompanyResponseDetails, companyB: KysCompanyResponseDetails) {
    const relations = [];
    const companyIdA = companyA.id;
    const companyIdB = companyB.id;
    const params = {
      keyNo: companyIdA,
      filterKeyNo: companyIdB,
      pageSize: 5,
      pageIndex: 1,
    };
    let res = await this.companyDetailService.getHoldingCompany(params);
    if (res?.Result == undefined) {
      params.keyNo = companyIdB;
      params.filterKeyNo = companyIdA;
      res = await this.companyDetailService.getHoldingCompany(params);
    }
    if (res?.Result) {
      // 存在控制关系
      const controlCompany = res.Result.KeyNo == companyIdA ? companyA : companyB;
      const controlledCompany = controlCompany == companyA ? companyB : companyA;
      const relation = {
        endCompanyKeyno: controlledCompany.id,
        endCompanyName: controlledCompany.name,
        history: false,
        relations: [
          {
            'Company.keyno': controlCompany.id,
            'Company.credit_code': controlCompany.creditcode,
            'Company.name': controlCompany.name,
          },
          {
            role: '控制关系',
            endid: controlledCompany.id,
            startid: controlCompany.id,
            type: 'ControlRelation',
            direction: 1,
          },
          {
            'Company.keyno': controlledCompany.id,
            'Company.credit_code': controlledCompany.creditcode,
            'Company.name': controlledCompany.name,
          },
        ],
        startCompanyKeyno: controlCompany.id,
        startCompanyName: controlCompany.name,
        steps: 1,
      };
      relations.push(relation);
    }
    return relations;
  }
}
