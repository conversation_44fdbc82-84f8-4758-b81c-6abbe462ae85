import { Injectable } from '@nestjs/common';
import {
  AiAnalyzerClient,
  AiGetReportRequest,
  AiReportAnalyzeRequest,
  AiReportCommentRequest,
  AiSearchPromptsRequest,
  PlatformUser,
  ProductCodeEnums,
  UpdatePromptRequest,
} from '@kezhaozhao/dd-platform-service';
import { ConfigService } from '../../libs/config/config.service';
import { RoverUser } from '../../libs/model/common';
import { DiligencePDFService } from '../diligence/diligence.pdf.service';
import { ReportChatReq } from '../../libs/model/ai/req/ReportChatReq';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';

export const getPlatFormUser = (currentUser: RoverUser, currentProduct = ProductCodeEnums.Rover) => {
  const { currentOrg, userId, loginUserId, orgName, bundle } = currentUser;
  const bundleStartDate = bundle?.startDate as Date;
  const user: PlatformUser = {
    currentProduct,
    currentOrg,
    userId,
    loginUserId,
    orgName,
    bundleStartDate,
  };
  return user;
};

@Injectable()
export class AiAnalyzerService {
  private logger: Logger = QccLogger.getLogger(AiAnalyzerService.name);
  private readonly aiAnalyzerClient: AiAnalyzerClient;

  constructor(private readonly configService: ConfigService, private readonly diligencePDFService: DiligencePDFService) {
    this.aiAnalyzerClient = new AiAnalyzerClient({
      server: this.configService.kzzServer.ddPlatformService,
      // 'qcc-insights-service'
      requestFrom: process.env.SERVICE_NAME,
      jwtSecret: this.configService.jwt.secret,
      axiosConfig: this.configService.axiosConfig,
    });
  }

  async searchPrompts(currentUser: RoverUser, data: AiSearchPromptsRequest) {
    return this.aiAnalyzerClient.searchPrompts(getPlatFormUser(currentUser), data);
  }

  async updatePrompt(currentUser: RoverUser, postData: UpdatePromptRequest) {
    return this.aiAnalyzerClient.updatePrompt(getPlatFormUser(currentUser), postData);
  }

  async reportChat(currentUser: RoverUser, reportChatReq: ReportChatReq) {
    const postData = await this.processReportChat(currentUser, reportChatReq);
    return this.aiAnalyzerClient.reportChat(getPlatFormUser(currentUser), postData);
  }

  async reportChatStream(currentUser: RoverUser, reportChatReq: ReportChatReq, req: any, res: any) {
    const postData = await this.processReportChat(currentUser, reportChatReq);
    const stream: any = await this.aiAnalyzerClient.reportChatStream(getPlatFormUser(currentUser), postData, req);
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    //stream.getStream().pipe(res);
    stream.on('data', (data) => {
      res.write(data);
      res.flush();
    });
    stream.on('end', () => {
      res.end();
    });
    stream.on('error', (error) => {
      this.logger.error('流出现错误:', error);
      // const errorMessage = {
      //   error: error.message,
      //   timestamp: Date.now(),
      // };
      res.write(error);
      res.status(500).end();
    });
  }

  async findReport(currentUser: RoverUser, postData: AiGetReportRequest) {
    return this.aiAnalyzerClient.getReport(getPlatFormUser(currentUser), postData);
  }

  private async processReportChat(currentUser: RoverUser, reportChatReq: ReportChatReq) {
    const { businessId } = reportChatReq;
    // 补充内容信息 toAnalyzeContent、promptContent
    const toAnalyzeContent = await this.diligencePDFService.generateMarkdownView(currentUser.currentOrg, Number(businessId));
    const promptListRes = await this.aiAnalyzerClient.searchPrompts(getPlatFormUser(currentUser), {
      orgId: currentUser.currentOrg,
      product: ProductCodeEnums.Rover,
      businessType: 0,
    });
    return Object.assign(new AiReportAnalyzeRequest(), {
      businessType: 0,
      businessId,
      userId: currentUser.userId.toString(),
      product: ProductCodeEnums.Rover,
      orgId: currentUser.currentOrg.toString(),
      model: 'DeepSeek-R1',
      autoSaveReport: 1,
      toAnalyzeContent,
      promptContent: promptListRes.data?.[0].content,
    });
  }

  async addCommentToReport(postData: AiReportCommentRequest, currentUser: RoverUser) {
    return this.aiAnalyzerClient.addCommentToReport(getPlatFormUser(currentUser), postData);
  }

  async getReportChatStatus(currentUser: RoverUser) {
    return this.aiAnalyzerClient.getReportChatStatus(getPlatFormUser(currentUser));
  }
}
