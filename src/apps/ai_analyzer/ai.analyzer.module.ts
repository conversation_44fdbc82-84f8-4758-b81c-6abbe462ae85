import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AiAnalyzerController } from './ai.analyzer.controller';
import { AiAnalyzerService } from './ai.analyzer.service';
import { ConfigModule } from '../../libs/config/config.module';
import { DiligenceModule } from '../diligence/diligence.module';

@Module({
  providers: [AiAnalyzerService],
  controllers: [AiAnalyzerController],
  exports: [AiAnalyzerService],
  imports: [ConfigModule, DiligenceModule],
})
export class AiAnalyzerModule {}
