import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../app/app.test.module';
import { AiAnalyzerController } from './ai.analyzer.controller';
import { AiAnalyzerModule } from './ai.analyzer.module';

jest.setTimeout(30000000);
describe.skip('集成测试-AI-智能分析', () => {
  let aiAnalyzerService: AiAnalyzerController;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, AiAnalyzerModule],
    }).compile();
    aiAnalyzerService = module.get<AiAnalyzerController>(AiAnalyzerController);
  });

  it('should be defined', () => {
    expect(aiAnalyzerService).toBeDefined();
  });

  it.skip('debug-reportChat test ', async () => {
    // const currentUser: RoverUser = getTestUser(208, 5171);
    // const req = {
    //   user: currentUser,
    // };
    // const reportChatRequest = {
    //   businessType: 0,
    //   businessId: 691104,
    // };
    // const reportChatResponse = await aiAnalyzerService.reportChat(req, reportChatRequest);
  });
});
