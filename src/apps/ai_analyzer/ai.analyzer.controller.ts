import { Body, Controller, Post, Request, Res, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { RoverSessionGuard } from '../../libs/guards/RoverSession.guard';
import { RoverRolesGuard } from '../../libs/guards/rover.roles.guard';
import { AiAnalyzerService } from './ai.analyzer.service';
import { AiGetReportRequest, AiReportCommentRequest, AiSearchPromptsRequest, UpdatePromptRequest } from '@kezhaozhao/dd-platform-service';
import { RoverUser } from '../../libs/model/common';
import { GetReportChatReq, ReportChatReq, SearchPromptsRequest } from '../../libs/model/ai/req/ReportChatReq';

@Controller('ai-analyzer')
@ApiTags('AI-智能分析')
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class AiAnalyzerController {
  constructor(private readonly aiAnalyzerService: AiAnalyzerService) {}

  @ApiOperation({ description: '获取内置的提示词ID' })
  @Post('/prompt/search')
  async searchPrompts(@Request() req, @Body() searchPromptsRequest: SearchPromptsRequest) {
    const currentUser: RoverUser = req.user;
    const data = Object.assign(new AiSearchPromptsRequest(), searchPromptsRequest, {
      product: 'SAAS_ROVER',
      orgId: currentUser.currentOrg.toString(),
      businessType: 0,
    });
    return this.aiAnalyzerService.searchPrompts(currentUser, data);
  }

  @ApiOperation({ description: '修改提示词' })
  @Post('/prompt/update')
  async updatePrompt(@Request() req, @Body() postData: UpdatePromptRequest) {
    const currentUser: RoverUser = req.user;
    return this.aiAnalyzerService.updatePrompt(currentUser, postData);
  }

  @ApiOperation({ description: '帮忙阅读报告并生成新的报告' })
  @Post('/report/chat')
  async reportChat(@Request() req, @Body() postData: ReportChatReq) {
    const currentUser: RoverUser = req.user;
    return this.aiAnalyzerService.reportChat(currentUser, postData);
  }

  @ApiOperation({ description: '帮忙阅读报告并生成新的报告' })
  @Post('/report/chat/stream')
  async reportChatStream(@Body() postData: ReportChatReq, @Request() req: any, @Res() res: any) {
    const currentUser: RoverUser = req.user;
    this.aiAnalyzerService.reportChatStream(currentUser, postData, req, res);
  }

  @ApiOperation({ description: '获取报告' })
  @Post('/report/details')
  async getReport(@Request() req, @Body() getReportChatReq: GetReportChatReq) {
    const currentUser: RoverUser = req.user;
    const postData = Object.assign(new AiGetReportRequest(), getReportChatReq, {
      product: 'SAAS_ROVER',
      orgId: currentUser.currentOrg.toString(),
      businessType: 0,
    });
    return this.aiAnalyzerService.findReport(currentUser, postData);
  }

  @Post('/report/comment')
  @ApiOperation({ description: '给报告添加评论' })
  async addCommentToReport(@Body() postData: AiReportCommentRequest, @Request() req: any) {
    const currentUser: RoverUser = req.user;
    return this.aiAnalyzerService.addCommentToReport(postData, currentUser);
  }

  @ApiOperation({ description: '阅读报告限制状态返回:true-不限制，false-限制' })
  @Post('/report/chat/status')
  @ApiOkResponse({ type: Boolean })
  async getReportChatStatus(@Request() req: any) {
    const currentUser: RoverUser = req.user;
    return this.aiAnalyzerService.getReportChatStatus(currentUser);
  }
}
