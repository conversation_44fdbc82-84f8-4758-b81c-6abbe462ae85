import { FileParserAbstract } from './FileParserAbstract';
import { Injectable } from '@nestjs/common';
import { ExcelParserSettingPO } from 'libs/model/batch/po/parse/ExcelParserSettingPO';
import { RoverUser } from 'libs/model/common';
import { FileParseResult } from 'libs/model/batch/po/parse/FileParseResult';
import { ParseErrorItem, PersonImportExcelRecord } from 'libs/model/batch/po/parse/ParsedRecordBase';
import { FieldParseErrorTypeEnums } from 'libs/enums/batch/FieldParseErrorTypeEnums';
import { RoverBundleEntityConfig, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import * as moment from 'moment/moment';
import { RelationshipConst } from '../../../libs/model/batch/po/RelationshipConst';
import { PersonRelationshipEnums } from '../../../libs/enums/person/PersonRelationshipEnums';

@Injectable()
export class PersonImportFileParser extends FileParserAbstract {
  constructor(protected readonly bundleService: RoverBundleService) {
    super(bundleService);
  }

  async getFileLimit(currentUser: RoverUser): Promise<number> {
    const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    return userBundle[RoverBundleLimitationType.BatchPersonUploadQuantity]?.value || this.getParseSetting().limit;
  }

  async parseFile(currentUser: RoverUser, filePath: string): Promise<FileParseResult> {
    const success: PersonImportExcelRecord[] = [];
    const error: ParseErrorItem[] = [];
    // const nameSet = new Set<string>();
    const personNoSet = new Set<string>();
    const rows = await this.parse(currentUser, filePath);
    rows.forEach((rowData) => {
      const person = Object.assign(new PersonImportExcelRecord(), {
        personNo: this.parseRowString(rowData, 0),
        name: this.parseRowString(rowData, 1),
        relationship: this.parseRowString(rowData, 2),
        groupName: this.parseRowString(rowData, 3),
        companyName: this.parseRowString(rowData, 4, true),
        phone: this.parseRowString(rowData, 5),
        email: this.parseRowString(rowData, 6),
        birthDay: this.parseRowDate(rowData, 7),
        birthPlace: this.parseRowString(rowData, 8),
        cardId: this.parseRowString(rowData, 9),
      });

      if (!person.name) {
        error.push({
          data: person,
          errorType: FieldParseErrorTypeEnums.Required,
        });
        return;
      }
      // if (nameSet.has(person.name)) {
      //   // 有重复数据
      //   error.push({
      //     data: person,
      //     errorType: FieldParseErrorTypeEnums.Duplicated,
      //   });
      //   return;
      // } else {
      //   nameSet.add(person.name);
      // }
      if (person.birthDay && moment(person.birthDay).isAfter(moment())) {
        error.push({
          data: person,
          errorType: FieldParseErrorTypeEnums.Unmatched_Format,
        });
        return;
      }
      if (!person.personNo) {
        error.push({
          data: person,
          errorType: FieldParseErrorTypeEnums.Required,
        });
        return;
      }
      //人员关系必填
      if (!person.relationship) {
        error.push({
          data: person,
          errorType: FieldParseErrorTypeEnums.Required,
        });
        return;
      } else if (person.relationship.length > 10) {
        error.push({
          data: person,
          errorType: FieldParseErrorTypeEnums.Unmatched_Format,
        });
        return;
      }
      if (personNoSet.has(person.personNo + person.relationship + person.name)) {
        // 有重复数据
        error.push({
          data: person,
          errorType: FieldParseErrorTypeEnums.Duplicated,
        });
        return;
      } else {
        personNoSet.add(person.personNo + person.relationship + person.name);
      }
      success.push(person);
    });

    const sortedData: PersonImportExcelRecord[] = [];
    if (success?.length) {
      const selfData = success.filter((d) => d.relationship === RelationshipConst[PersonRelationshipEnums.self]);
      Array.prototype.push.apply(sortedData, selfData);
      const relatives = success.filter((d) => d.relationship !== RelationshipConst[PersonRelationshipEnums.self]);
      Array.prototype.push.apply(sortedData, relatives);
    }
    return { succeedItems: sortedData, failedItems: error };
  }

  getParseSetting(): ExcelParserSettingPO {
    return {
      explainRows: 3,
      limit: 1000,
      sheetName: '人员管理-批量添加',
      templateTitle: [
        '员工编号',
        '姓名（员工/近亲属）',
        '与员工关系',
        '人员分组（选填）',
        '关联企业（选填）',
        '手机号码（选填）',
        '电子邮箱（选填）',
        '出生日期（选填）',
        '出生地区（选填）',
        '证件号（选填）',
      ],
      exportExcelColumns: {
        resultDes: { header: '导入结果', width: 30 },
        personNo: { header: '员工编号', width: 30 },
        name: { header: '姓名（员工/近亲属）', width: 30 },
        relationship: { header: '与员工关系', width: 30 },
        groupName: { header: '人员分组（选填）', width: 30 },
        companyName: { header: '关联企业（选填）', width: 60 },
        phone: { header: '手机号码（选填）', width: 30 },
        email: { header: '电子邮箱（选填）', width: 30 },
        birthDay: { header: '出生日期（选填）', width: 30 },
        birthPlace: { header: '出生地区（选填）', width: 30 },
        cardId: { header: '证件号（选填）', width: 30 },
      },
    };
  }
}
