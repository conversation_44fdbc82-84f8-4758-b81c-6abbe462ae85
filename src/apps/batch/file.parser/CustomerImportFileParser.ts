import { FileParserAbstract } from './FileParserAbstract';
import { Injectable } from '@nestjs/common';
import { RoverUser } from 'libs/model/common';
import { ExcelParserSettingPO } from 'libs/model/batch/po/parse/ExcelParserSettingPO';
import { FileParseResult } from 'libs/model/batch/po/parse/FileParseResult';
import { CustomerImportExcelRecord, ParseErrorItem } from 'libs/model/batch/po/parse/ParsedRecordBase';
import { FieldParseErrorTypeEnums } from 'libs/enums/batch/FieldParseErrorTypeEnums';
import { RoverBundleEntityConfig, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';

@Injectable()
export class CustomerImportFileParser extends FileParserAbstract {
  constructor(protected readonly bundleService: RoverBundleService) {
    super(bundleService);
  }

  async getFileLimit(currentUser: RoverUser): Promise<number> {
    const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    return userBundle[RoverBundleLimitationType.BatchThirdPartyUploadQuantity]?.value || this.getParseSetting().limit;
  }

  async parseFile(currentUser: RoverUser, filePath): Promise<FileParseResult> {
    const success: CustomerImportExcelRecord[] = [];
    const error: ParseErrorItem[] = [];
    const nameSet = new Set<string>();
    const rows = await this.parse(currentUser, filePath);
    rows.forEach((rowData) => {
      const label = this.parseRowString(rowData, 2);
      const departmentNames = this.parseRowString(rowData, 8);
      const customer = Object.assign(new CustomerImportExcelRecord(), {
        companyName: this.parseRowString(rowData, 0, true),
        group: this.parseRowString(rowData, 1), //分组
        label: label ? label.split(',') : undefined, //标签，标签如果是填写多个的话用中文"，"逗号隔开，例如：「优质客户，普通客户」
        startDate: this.parseRowDate(rowData, 3, ''), // 开始时间（时间格式2023/01/06）
        endDate: this.parseRowDate(rowData, 4, ''), // 截止时间
        creditQuota: rowData[5], // 授信金额
        contactQuota: rowData[6], // 合同金额
        cost: rowData[7], // 资金占用
        departmentNames: departmentNames ? departmentNames.split(',') : undefined, //部门
        principal: this.parseRowString(rowData, 9), //负责人
      });
      if (!customer.companyName) {
        error.push({
          data: customer,
          errorType: FieldParseErrorTypeEnums.Required,
        });
        return;
      }
      if (nameSet.has(customer.companyName)) {
        // 有重复数据
        error.push({
          data: customer,
          errorType: FieldParseErrorTypeEnums.Duplicated,
        });
        return;
      } else {
        nameSet.add(customer.companyName);
      }
      success.push(customer);
    });
    return { succeedItems: success, failedItems: error };
  }

  getParseSetting(): ExcelParserSettingPO {
    return {
      explainRows: 3,
      limit: 5000,
      sheetName: '第三方管理-批量添加',
      templateTitle: [
        '企业名称/统一社会信用代码/注册号',
        '企业分组（选填）',
        '标签（选填）',
        '开始时间（选填）',
        '截止时间（选填）',
        '授信金额（选填）',
        '合同金额（选填）',
        '资金占用（选填）',
        '部门（选填）',
        '负责人（选填）',
      ],
      exportExcelColumns: {
        resultDes: { header: '导入结果', width: 30 },
        companyName: { header: '企业名称/统一社会信用代码/注册号', width: 50 },
        group: { header: '企业分组（选填）', width: 30 },
        label: { header: '标签（选填）', width: 30 },
        startDate: { header: '开始时间（选填）', width: 30 },
        endDate: { header: '截止时间（选填）', width: 30 },
        creditQuota: { header: '授信金额（选填）', width: 30 },
        contactQuota: { header: '合同金额（选填）', width: 30 },
        cost: { header: '资金占用（选填）', width: 30 },
        departmentNames: { header: '部门（选填）', width: 30 },
        principal: { header: '负责人（选填）', width: 30 },
      },
    };
  }
}
