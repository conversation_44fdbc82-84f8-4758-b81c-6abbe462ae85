import { FileParserAbstract } from './FileParserAbstract';
import { Injectable } from '@nestjs/common';
import { ExcelParserSettingPO } from 'libs/model/batch/po/parse/ExcelParserSettingPO';
import { RoverUser } from 'libs/model/common';
import { FileParseResult } from 'libs/model/batch/po/parse/FileParseResult';
import { InnerBlacklistImportExcelRecord, ParseErrorItem } from 'libs/model/batch/po/parse/ParsedRecordBase';
import { FieldParseErrorTypeEnums } from 'libs/enums/batch/FieldParseErrorTypeEnums';
import { RoverBundleEntityConfig, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { parseDurationChinese } from './file.export.template';

@Injectable()
export class InnerBlacklistImportFileParser extends FileParserAbstract {
  constructor(protected readonly bundleService: RoverBundleService) {
    super(bundleService);
  }

  async getFileLimit(currentUser: RoverUser): Promise<number> {
    const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    return userBundle[RoverBundleLimitationType.BatchInnerBlacklistUploadQuantity]?.value || this.getParseSetting().limit;
  }

  async parseFile(currentUser: RoverUser, filePath: string): Promise<FileParseResult> {
    const success: InnerBlacklistImportExcelRecord[] = [];
    const error: ParseErrorItem[] = [];
    const nameSet = new Set<string>();
    const rows = await this.parse(currentUser, filePath);
    rows.forEach((rowData) => {
      const label = this.parseRowString(rowData, 2);
      const departmentNames = this.parseRowString(rowData, 7);
      const blacklist = Object.assign(new InnerBlacklistImportExcelRecord(), {
        companyName: this.parseRowString(rowData, 0, true),
        group: this.parseRowString(rowData, 1), //分组
        label: label ? label.split(',') : undefined, //标签，标签如果是填写多个的话用中文"，"逗号隔开，例如：「优质客户，普通客户」
        reason: this.parseRowString(rowData, 3),
        joinDate: this.parseRowDate(rowData, 4, ''),
        duration: parseDurationChinese(rowData[5]),
        expiredDate: this.parseRowDate(rowData, 6, ''), //截止日期
        departmentNames: departmentNames ? departmentNames.split(',') : undefined, //部门
        comment: this.parseRowString(rowData, 8),
      });

      if (!blacklist.companyName) {
        error.push({
          data: blacklist,
          errorType: FieldParseErrorTypeEnums.Required,
        });
        return;
      }
      if (nameSet.has(blacklist.companyName)) {
        // 有重复数据
        error.push({
          data: blacklist,
          errorType: FieldParseErrorTypeEnums.Duplicated,
        });
        return;
      } else {
        nameSet.add(blacklist.companyName);
      }
      success.push(blacklist);
    });
    return { succeedItems: success, failedItems: error };
  }

  getParseSetting(): ExcelParserSettingPO {
    return {
      explainRows: 3,
      limit: 1000,
      sheetName: '内部黑名单-批量添加',
      templateTitle: [
        '企业名称/统一社会信用代码/注册号',
        '企业分组（选填）',
        '标签（选填）',
        '列入原因（选填）',
        '列入时间（选填）',
        '有效期（选填）',
        '截止日期（选填）',
        '来源部门（选填）',
        '备注（选填）',
      ],
      exportExcelColumns: {
        resultDes: { header: '导入结果', width: 30 },
        companyName: { header: '企业名称/统一社会信用代码/注册号', width: 50 },
        group: { header: '企业分组（选填）', width: 30 },
        label: { header: '标签（选填）', width: 30 },
        reason: { header: '列入原因（选填）', width: 30 },
        joinDate: { header: '列入时间（选填）', width: 30 },
        duration: { header: '有效期（选填）', width: 30 },
        expiredDate: { header: '截止日期（选填）', width: 30 },
        departmentNames: { header: '来源部门（选填）', width: 30 },
        comment: { header: '备注（选填）', width: 30 },
      },
    };
  }
}
