import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { ExcelGroupVersionParserSettingPO, ExcelParserSettingPO } from 'libs/model/batch/po/parse/ExcelParserSettingPO';
import { DurationEnums } from 'libs/enums/blacklist/DurationEnums';
import { GroupDimensionVersionEnums } from 'libs/model/diligence/pojo/dimension/group/GroupDimensionVersionEnums';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel1Enums } from '../../../libs/enums/diligence/DimensionLevel1Enums';
import { DATE_FORMAT } from '../../../libs/constants/common';
import * as moment from 'moment';
import { cloneDeep } from 'lodash';
import { TenderDimensionDetails, TenderGroupDimensionDetails } from '../../utils/excel/excel-template.util';

export class FileExportTemplateType {
  [BatchBusinessTypeEnums.Diligence_Batch_Detail]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Person_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.InnerBlacklist_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Diligence_Record]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Customer_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Dimension_Detail_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Analyze_Dimension_Detail]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Tender_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Risk_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Sentiment_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Analyze_Record_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Tender_Diligence_Record_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Tender_Diligence_History_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Specific_Record_List_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Tender_Dimension_Detail_Export]: ExcelGroupVersionParserSettingPO;
  [BatchBusinessTypeEnums.Specific_Batch_Export]: ExcelGroupVersionParserSettingPO;
}

export const DurationConstants = {
  [DurationEnums.Custom]: '自定义',
  [DurationEnums.ForEver]: '不限',
  [DurationEnums.ThreeMonth]: '3个月',
  [DurationEnums.SixMonth]: '6个月',
  [DurationEnums.AYear]: '1年',
  [DurationEnums.TwoYears]: '2年',
  [DurationEnums.ThreeYears]: '三年',
  [DurationEnums.FiveYears]: '五年',
};

// 创建一个映射表，用于将中文描述转换为 DurationEnums 枚举值
const ChineseToDurationEnumMap: { [key: string]: DurationEnums } = {
  自定义: DurationEnums.Custom,
  不限: DurationEnums.ForEver,
  '3个月': DurationEnums.ThreeMonth,
  '6个月': DurationEnums.SixMonth,
  '1年': DurationEnums.AYear,
  '2年': DurationEnums.TwoYears,
  三年: DurationEnums.ThreeYears,
  五年: DurationEnums.FiveYears,
};

// 将中文描述转换为 DurationEnums 枚举值
export function parseDurationChinese(durationChinese: string): DurationEnums {
  //默认返回不限
  if (ChineseToDurationEnumMap[durationChinese] !== undefined && ChineseToDurationEnumMap[durationChinese] !== null) {
    return ChineseToDurationEnumMap[durationChinese];
  }
  return DurationEnums.ForEver;
}

// /**
//  * 企业类型
//  */
// export const EconKindCode = {
//   10: '有限责任公司',
//   20: '股份有限公司',
//   30: '国企',
//   40: '外商投资企业',
//   50: '独资企业',
//   51: '法人独资',
//   52: '港澳台独资',
//   53: '个人独资企业',
//   54: '国有独资',
//   55: '外商独资',
//   56: '自然人独资',
//   70: '个体工商户',
//   80: '联营企业',
//   90: '集体所有制',
//   100: '有限合伙',
//   110: '普通合伙',
//   120: '股份合作制',
// };

export const QccExcelDeclaration =
  '声明：本数据仅供参考，不构成企查查对任何人之明示或暗示的观点或保证。您不得将本数据向任何第三方出售/向境外传输/在境外存储、使用；同时您不得利用本数据从事任何违法违规行为。否则，由此产生的一切后果由您自行承担';

/**
 * 经营状态
 */
export const StatusCode = {
  10: '在业', // 正常
  20: '存续',
  30: '筹建',
  40: '清算',
  50: '迁入',
  60: '迁出',
  70: '停业',
  80: '撤销',
  90: '吊销',
  99: '注销',
  93: '其他',
  92: '仍注册',
  94: '已告解散',
  95: '已终止营业地点',
  96: '不再是独立的实体',
  97: '休止活动',
  100: '废止',
  101: '废止清算完结',
  102: '废止许可',
  103: '废止许可完结',
  104: '废止认许',
  105: '废止认许完结',
  106: '接管',
  107: '撤回认许',
  108: '撤回认许完结',
  110: '撤销设立',
  111: '撤销完结',
  112: '撤销无需清算',
  113: '撤销许可',
  114: '撤销认许',
  115: '撤销认许完结',
  116: '核准报备',
  117: '核准设立',
  118: '设立但已解散',
  119: '核准许可报备',
  120: '核准许可登记',
  121: '核准认许',
  122: '清理',
  123: '清理完结',
  124: '破产',
  125: '破产清算完结',
  126: '破产程序终结',
  127: '解散',
  128: '解散清算完结',
  129: '重整',
  130: '合并解散',
  131: '终止破产',
  132: '涂销破产',
  133: '核准许可',
  134: '核准登记',
  135: '分割解散',
  136: '废止登记完结',
  137: '废止登记',
  138: '撤销登记完结',
  139: '撤销登记',
  140: '撤回登记完结',
  141: '撤回登记',
  9910: '注销',
};
export const billAcceptanceRiskStatusMap = {
  1: '承兑人逾期名单',
  2: '持续逾期名单',
  3: '信用信息未披露名单',
  4: '延迟披露名单',
};
//维度详情导出模板
//对[GroupDimensionVersionEnums.V1]中每个对象添加excelColumnsStartLine:2,headerDescription:QccExcelDeclaration,headerDescriptionLine:1,帮助生成代码
export const DimensionDetails: ExcelGroupVersionParserSettingPO = {
  [GroupDimensionVersionEnums.V1]: [
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.PersistentBillOverdue, //票据持续逾期 升级为票据承兑风险
      templateTitle: ['企业名称', '统一社会信用代码', '名单类型', '开始时间', '公告日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        listTag: { header: '名单类型', width: 30 },
        beginDate: { header: '开始时间', width: 30 },
        publishDate: { header: '公告日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.NoticeInTimePeriod, //近期多起开庭公告
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '案由', '当事人', '法院', '开庭时间'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        caseNo: { header: '案号', width: 60 },
        caseReason: { header: '案由', width: 60 },
        caseRoleGroup: { header: '当事人', width: 60 },
        court: { header: '法院', width: 60 },
        courtDate: { header: '开庭时间', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.BusinessAbnormal1,
      templateTitle: ['企业名称', '统一社会信用代码', '登记状态'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        value: { header: '登记状态', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.BusinessAbnormal2,
      templateTitle: ['企业名称', '统一社会信用代码', '公告名称', '公告开始日期-结束日期', '简易注销结果'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        annoName: { header: '公告名称', width: 40 },
        publishDate: { header: '公告开始日期-结束日期', width: 30 },
        resultContent: { header: '简易注销结果', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.BusinessAbnormal5,
      templateTitle: ['企业名称', '统一社会信用代码', '决定书文号', '违法事实', '处罚结果', '处罚单位', '处罚日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        docNo: { header: '决定书文号', width: 40 },
        punishReason: { header: '违法事实', width: 30 },
        punishResult: { header: '处罚结果', width: 30 },
        punishOffice: { header: '处罚单位', width: 30 },
        punishDate: { header: '处罚日期', width: 20 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.BusinessAbnormal6,
      templateTitle: ['企业名称', '统一社会信用代码', '营业期限'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        value: { header: '营业期限', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.BusinessAbnormal7,
      templateTitle: ['企业名称', '统一社会信用代码', '统一社会信用代码'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        description: { header: '统一社会信用代码', width: 40 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.BusinessAbnormal8,
      templateTitle: ['企业名称', '统一社会信用代码', '营业期限'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        value: { header: '营业期限', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.BusinessAbnormal9,
      templateTitle: ['企业名称', '统一社会信用代码', '营业期限', '状态'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        value: { header: '营业期限', width: 30 },
        termStatus: { header: '状态', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.CancellationOfFiling,
      templateTitle: ['企业名称', '统一社会信用代码', '注销原因', '公告内容', '公告期', '债权申报联系人', '债权申报联系电话', '债权申报地址', '登记机关'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CancelReason: { header: '注销原因', width: 30 },
        NoticeContent: { header: '公告内容', width: 30 },
        CreditorNoticeDate: { header: '公告期', width: 30 },
        ClaimsDeclarationMember: { header: '债权申报联系人', width: 30 },
        ClaimsDeclarationTelNo: { header: '债权申报联系电话', width: 30 },
        ClaimsDeclarationAddress: { header: '债权申报地址', width: 30 },
        BelongOrg: { header: '登记机关', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.NoCapital,
      templateTitle: ['企业名称', '统一社会信用代码', '注册资本', '实缴资本', '实缴比例', '母公司', '母公司持股比例', '母公司注册资本', '母公司实缴资本'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        RegistCapi: { header: '注册资本', width: 30 },
        RecCap: { header: '实缴资本', width: 30 },
        PaidInCapitalRatio: { header: '实缴比例', width: 30 },
        ParentName: { header: '母公司', width: 30 },
        ParentStockPercent: { header: '母公司持股比例', width: 30 },
        ParentRegistCapi: { header: '母公司注册资本', width: 30 },
        ParentRecCap: { header: '母公司实缴资本', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.EmployeeReduction,
      templateTitle: ['企业名称', '统一社会信用代码', '减少前', '减少前年份', '减少后', '减少后年份'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        prevCount: { header: '减少前', width: 30 },
        prevYear: { header: '减少前年份', width: 30 },
        currentCount: { header: '减少后', width: 30 },
        currentYear: { header: '减少后年份', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.DebtOverdue,
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '债务人',
        '债权人',
        '债务类型',
        '逾期金额（万元）',
        '逾期本金（万元）',
        '逾期利息（万元）',
        '披露方',
        '逾期起始日',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        debtorName: { header: '债务人', width: 30 },
        creditorName: { header: '债权人', width: 30 },
        debtType: { header: '债务类型', width: 30 },
        overdueSum: { header: '逾期金额（万元）', width: 30 },
        overdueCapital: { header: '逾期本金（万元）', width: 30 },
        overdueInterest: { header: '逾期利息（万元）', width: 30 },
        publicPartyName: { header: '披露方', width: 30 },
        startDate: { header: '逾期起始日', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.CompanyShell,
      templateTitle: ['企业名称', '统一社会信用代码'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.FakeSOES,
      templateTitle: ['企业名称', '统一社会信用代码', '被列入假冒国企企业名称', '关联路径'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        riskCompanyName: { header: '被列入假冒国企企业名称', width: 60 },
        pathDetail: { header: '关联路径', width: 120 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.EstablishedTime,
      templateTitle: ['企业名称', '统一社会信用代码', '成立日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        value: { header: '成立日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.Certification,
      templateTitle: ['企业名称', '统一社会信用代码', '资质证书', '状态', '有效期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        name: { header: '资质证书', width: 30 },
        expirationDesc: { header: '状态', width: 30 },
        validPeriod: { header: '有效期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.LowCapital,
      templateTitle: ['企业名称', '统一社会信用代码', '注册资本'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        value: { header: '注册资本', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.NoCertification, //无有效关键资质认证
      templateTitle: ['企业名称', '统一社会信用代码'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.FraudList,
      templateTitle: ['企业名称', '统一社会信用代码'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
      },
    },
    //法律风险
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.PersonCreditCurrent,
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '执行法院', '执行依据文号', '涉案金额（元）', '履行情况', '失信行为', '立案日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 60 },
        ExecuteGov: { header: '执行法院', width: 60 },
        ExecuteNo: { header: '执行依据文号', width: 60 },
        Amount: { header: '涉案金额（元）', width: 30 },
        ExecuteStatus: { header: '履行情况', width: 60 },
        ActionRemark: { header: '失信行为', width: 60 },
        LiAnDate: { header: '立案日期', width: 30 },
        PublicDate: { header: '发布日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.TaxationOffences,
      templateTitle: ['企业名称', '统一社会信用代码', '发布日期', '所属税务机关', '案件性质', '主要违法事实', '罚款金额(元)', '法律依据及处理处罚情况'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        PublishDate: { header: '发布日期', width: 30 },
        Court: { header: '所属税务机关', width: 60 },
        RemoveReason: { header: '案件性质', width: 60 },
        CaseReason: { header: '主要违法事实', width: 60 },
        amount: { header: '罚款金额(元)', width: 60 },
        Title: { header: '法律依据及处理处罚情况', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.RestrictedConsumptionCurrent,
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '限消令对象', '关联对象', '申请人', '立案日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 60 },
        Name: { header: '限消令对象', width: 60 },
        Pledgor: { header: '关联对象', width: 60 },
        ExecutionApplicant: { header: '申请人', width: 60 },
        LianDate: { header: '立案日期', width: 30 },
        PublishDate: { header: '发布日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.RestrictedConsumptionHistory,
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '限消令对象', '关联对象', '申请人', '立案日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 60 },
        Name: { header: '限消令对象', width: 60 },
        Pledgor: { header: '关联对象', width: 60 },
        ExecutionApplicant: { header: '申请人', width: 60 },
        LianDate: { header: '立案日期', width: 30 },
        PublishDate: { header: '发布日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.Bankruptcy, //破产重整
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '破产类型', '被申请人', '申请人', '经办法院', '公开日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 60 },
        CaseReasonType: { header: '破产类型', width: 60 },
        Name: { header: '被申请人', width: 60 },
        Applicant: { header: '申请人', width: 60 },
        Court: { header: '经办法院', width: 60 },
        PublishDate: { header: '公开日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.PersonCreditHistory, //历史失信被执行人
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '执行法院', '执行依据文号', '涉案金额（元）', '履行情况', '失信行为', '立案日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 60 },
        ExecuteGov: { header: '执行法院', width: 60 },
        ExecuteNo: { header: '执行依据文号', width: 60 },
        Amount: { header: '涉案金额（元）', width: 30 },
        ExecuteStatus: { header: '履行情况', width: 60 },
        ActionRemark: { header: '失信行为', width: 60 },
        LiAnDate: { header: '立案日期', width: 30 },
        PublicDate: { header: '发布日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.FreezeEquity, //有股权被冻结
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '执行通知文书号',
        '被执行人',
        '冻结股权标的企业',
        '股权数额',
        '执行法院',
        '类型|状态',
        '冻结起止日期',
        '公示日期',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '执行通知文书号', width: 60 },
        Name: { header: '被执行人', width: 60 },
        Pledgor: { header: '冻结股权标的企业', width: 60 },
        AmountDesc: { header: '股权数额', width: 30 },
        Court: { header: '执行法院', width: 60 },
        TypeDescExeCuteStatus: { header: '类型|状态', width: 30 },
        LianDateRemark: { header: '冻结起止日期', width: 60 },
        PublishDate: { header: '公示日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.PersonExecution, //被执行人信息
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '被执行人证件号/组织机构代码', '立案日期', '执行法院', '执行标的（元）'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Anno: { header: '案号', width: 60 },
        OrgNo: { header: '被执行人证件号/组织机构代码', width: 60 },
        LiAnDate: { header: '立案日期', width: 60 },
        ExecuteGov: { header: '执行法院', width: 30 },
        BiaoDi: { header: '执行标的（元）', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.ChattelSeizure, //动产查封
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '被执行人', '执行法院', '公示日期', '查封起始日期', '查封结束日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 30 },
        ExecuteCompanyNames: { header: '被执行人', width: 30 },
        ExecuteGov: { header: '执行法院', width: 30 },
        PublicDate: { header: '公示日期', width: 30 },
        StartDate: { header: '查封起始日期', width: 30 },
        EndDate: { header: '查封结束日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.ContractBreach, //合同违约
      templateTitle: ['企业名称', '统一社会信用代码', '违约等级', '违约指数', '违约次数', '涉案金额（万元）'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Revel: { header: '违约等级', width: 30 },
        ScoreLog: { header: '违约指数', width: 30 },
        TotalNum: { header: '违约次数', width: 30 },
        TotalAmt: { header: '涉案金额（万元）', width: 30 },
        //Relate: { header: '同行业对比', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.JudicialAuction, //司法拍卖信息
      templateTitle: ['企业名称', '统一社会信用代码', '标题', '案号', '起拍价（元）', '评估价（元）', '拍卖时间', '处置单位'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        name: { header: '标题', width: 30 },
        Caseno: { header: '案号', width: 30 },
        yiwu: { header: '起拍价（元）', width: 30 },
        EvaluationPrice: { header: '评估价（元）', width: 30 },
        actionremark: { header: '拍卖时间', width: 60 },
        executegov: { header: '处置单位', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainMembersPersonCreditCurrent, //主要人员被列入失信被执行人
      //sheetName: '主要人员被列入失信被执行人',
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '案号',
        '疑似申请执行人',
        '执行法院',
        '执行依据文号',
        '失信被执行人',
        '涉案金额（元）',
        '履行情况',
        '失信行为',
        '立案日期',
        '发布日期',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 60 },
        ApplicantNames: { header: '疑似申请执行人', width: 60 },
        Court: { header: '执行法院', width: 60 },
        OrgNo: { header: '执行依据文号', width: 60 },
        SubjectNames: { header: '失信被执行人', width: 60 },
        Amount: { header: '涉案金额（元）', width: 30 },
        ExecuteStatus: { header: '履行情况', width: 60 },
        ActionRemark: { header: '失信行为', width: 60 },
        LianDate: { header: '立案日期', width: 30 },
        PublishDate: { header: '发布日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      //sheetName: DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent, //主要人员被列入限制高消费
      sheetName: '主要人员及核心关联方被列入限制高消费', //主要人员被列入限制高消费
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '限消令对象', '关联对象', '申请人', '立案日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 60 },
        SubjectInfoName: { header: '限消令对象', width: 60 },
        Pledgor: { header: '关联对象', width: 60 },
        ExecutionApplicant: { header: '申请人', width: 60 },
        LianDate: { header: '立案日期', width: 30 },
        PublishDate: { header: '发布日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainMembersRestrictedOutbound, //主要人员被列入限制出境
      templateTitle: ['企业名称', '统一社会信用代码', '案号', '限制出境对象', '被执行人', '被执行人地址', '申请执行人', '执行标的金额（元）', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 60 },
        relatedObject: { header: '限制出境对象', width: 30 },
        Pledgor: { header: '被执行人', width: 30 },
        Address: { header: '被执行人地址', width: 60 },
        ExecutionApplicant: { header: '申请执行人', width: 30 },
        Amount: { header: '执行标的金额（元）', width: 30 },
        PublishDate: { header: '发布日期', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      //sheetName: DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence, //公司及主要人员/核心关联方涉刑事犯罪
      sheetName: '公司及主要人员、核心关联方涉刑事犯罪',
      templateTitle: ['企业名称', '统一社会信用代码', '案件名称', '当事人', '案号', '最新案件进程', '法院'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseName: { header: '案件名称', width: 60 },
        RoleAmt: { header: '当事人', width: 60 },
        AnNoList: { header: '案号', width: 60 },
        LatestTrialRound: { header: '最新案件进程', width: 60 },
        CourtList: { header: '法院', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      //sheetName: DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory, //公司及主要人员/核心关联方涉刑事犯罪（3年以上及其他）
      sheetName: '公司及主要人员、核心关联方涉刑事犯罪（3年以上及其他）',
      templateTitle: ['企业名称', '统一社会信用代码', '案件名称', '当事人', '案号', '最新案件进程', '法院'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseName: { header: '案件名称', width: 60 },
        RoleAmt: { header: '当事人', width: 60 },
        AnNoList: { header: '案号', width: 60 },
        LatestTrialRound: { header: '最新案件进程', width: 60 },
        CourtList: { header: '法院', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.SalesContractDispute, //买卖合同纠纷
      templateTitle: ['企业名称', '统一社会信用代码', '文书标题', '案号', '案由', '当事人', '案件金额(元)', '裁判结果', '裁判日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        casename: { header: '文书标题', width: 60 },
        caseno: { header: '案号', width: 60 },
        casereason: { header: '案由', width: 30 },
        caserolegroupbyrolename: { header: '当事人', width: 30 },
        amountinvolved: { header: '案件金额(元)', width: 30 },
        judgeresult: { header: '裁判结果', width: 60 },
        judgedate: { header: '裁判日期', width: 60 }, //需要时间转换
        courtdate: { header: '发布日期', width: 60 }, //需要时间转换
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.LaborContractDispute, //劳动纠纷
      templateTitle: ['企业名称', '统一社会信用代码', '案件名称', '案件身份', '案号', '案件金额（元）', '最新案件进程', '法院'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseName: { header: '案件名称', width: 60 },
        CaseRoleIdentity: { header: '案件身份', width: 60 },
        AnNoList: { header: '案号', width: 30 },
        Amt: { header: '案件金额（元）', width: 30 },
        LatestDateTrialRound: { header: '最新案件进程', width: 30 },
        CourtList: { header: '法院', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.UnfairCompetition, //不正当竞争
      templateTitle: ['企业名称', '统一社会信用代码', '案件名称', '案件身份', '案号', '案件金额（元）', '最新案件进程', '法院'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseName: { header: '案件名称', width: 60 },
        CaseRoleIdentity: { header: '案件身份', width: 60 },
        AnNoList: { header: '案号', width: 30 },
        Amt: { header: '案件金额（元）', width: 30 },
        LatestDateTrialRound: { header: '最新案件进程', width: 30 },
        CourtList: { header: '法院', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MajorDispute, //重大纠纷
      templateTitle: ['企业名称', '统一社会信用代码', '文书标题', '案号', '案由', '当事人', '案件金额(元)', '裁判结果', '裁判日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        casename: { header: '文书标题', width: 60 },
        caseno: { header: '案号', width: 60 },
        casereason: { header: '案由', width: 30 },
        caserolegroupbyrolename: { header: '当事人', width: 30 },
        amountinvolved: { header: '案件金额(元)', width: 30 },
        judgeresult: { header: '裁判结果', width: 60 },
        judgedate: { header: '裁判日期', width: 60 }, //需要时间转换
        courtdate: { header: '发布日期', width: 60 }, //需要时间转换
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      //sheetName: DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve, //近3年涉贪污受贿裁判相关提及方
      sheetName: '近3年涉贪污受贿裁判相关提及方',
      templateTitle: ['企业名称', '统一社会信用代码', '文书标题', '案号', '案由', '当事人', '案件金额（元）', '裁判结果', '裁判日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        casename: { header: '文书标题', width: 60 },
        caseno: { header: '案号', width: 60 },
        casereason: { header: '案由', width: 30 },
        CaseRoleGroup: { header: '当事人', width: 30 },
        amountinvolved: { header: '案件金额(元)', width: 30 },
        judgeresult: { header: '裁判结果', width: 60 },
        judgedate: { header: '裁判日期', width: 60 },
        courtdate: { header: '发布日期', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      //sheetName: DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory, //涉贪污受贿裁判相关提及方（3年以上及其他）
      sheetName: '涉贪污受贿裁判相关提及方（3年以上及其他）',
      templateTitle: ['企业名称', '统一社会信用代码', '文书标题', '案号', '案由', '当事人', '案件金额（元）', '裁判结果', '裁判日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        casename: { header: '文书标题', width: 60 },
        caseno: { header: '案号', width: 60 },
        casereason: { header: '案由', width: 30 },
        caserolegroupbyrolename: { header: '当事人', width: 30 },
        amountinvolved: { header: '案件金额(元)', width: 30 },
        judgeresult: { header: '裁判结果', width: 60 },
        judgedate: { header: '裁判日期', width: 60 },
        courtdate: { header: '发布日期', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.EndExecutionCase, //终本案件
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '案号',
        '被执行人',
        '疑似申请执行人',
        '未履行金额（元）',
        '执行标的（元）',
        '执行法院',
        '立案日期',
        '终本日期',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '案号', width: 60 },
        Name: { header: '被执行人', width: 30 },
        SqrInfo: { header: '疑似申请执行人', width: 30 },
        FailureAct: { header: '未履行金额（元）', width: 30 },
        ExecuteObject: { header: '执行标的（元）', width: 30 },
        Court: { header: '执行法院', width: 60 },
        LiAnDate: { header: '立案日期', width: 30 },
        EndDate: { header: '终本日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      // sheetName: DimensionLevel1Enums.Risk_InterestConflict,
      sheetName: '潜在利益冲突',
      templateTitle: ['企业名称', '统一社会信用代码', '姓名', '人员类型/职务', '”潜在利冲”人员', '”潜在利冲”人员分组'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        personName: { header: '姓名', width: 30 },
        job: { header: '人员类型/职务', width: 40 },
        relationPersonName: { header: '"潜在利冲"人员', width: 30 },
        group: { header: '"潜在利冲"人员分组', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.SuspectedInterestConflict,
      templateTitle: ['企业名称', '统一社会信用代码', '疑似关系', '人员类型/职务', '“疑似潜在利冲”人员', '“潜在利冲”人员分组'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 40 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        personName: { header: '疑似关系', width: 30 },
        job: { header: '人员类型/职务', width: 40 },
        relationPersonName: { header: '“疑似潜在利冲”人员', width: 30 },
        group: { header: '“潜在利冲”人员分组', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.CustomerPartnerInvestigation,
      templateTitle: ['企业名称', '统一社会信用代码', '关联企业名称', '关联类型', '关联路径详情'],
      exportExcelColumns: {
        companyNameDD: { header: '企业名称', width: 40 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        companyNameRelated: { header: '关联企业名称', width: 40 },
        relationType: { header: '关联类型', width: 30 },
        pathDetail: { header: '关联路径详情', width: 120 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.CustomerSuspectedRelation,
      templateTitle: ['企业名称', '统一社会信用代码', '关联企业名称', '关联类型', '关联路径详情'],
      exportExcelColumns: {
        companyNameDD: { header: '企业名称', width: 40 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        companyNameRelated: { header: '关联企业名称', width: 40 },
        relationType: { header: '关联类型', width: 30 },
        pathDetail: { header: '关联路径详情', width: 120 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.BlacklistSuspectedRelation,
      templateTitle: ['企业名称', '统一社会信用代码', '关联企业名称', '关联类型', '关联路径详情'],
      exportExcelColumns: {
        companyNameDD: { header: '企业名称', width: 40 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        companyNameRelated: { header: '关联企业名称', width: 40 },
        relationType: { header: '关联类型', width: 30 },
        pathDetail: { header: '关联路径详情', width: 120 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.HitInnerBlackList,
      templateTitle: ['黑名单名称', '统一社会信用代码', '列入原因', '列入时间', '黑名单有效期', '黑名单截止日期'],
      exportExcelColumns: {
        companyNameDD: { header: '黑名单名称', width: 40 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        reason: { header: '列入原因', width: 60 },
        joinDate: { header: '列入时间', width: 30 },
        duration: { header: '黑名单有效期', width: 30 },
        expiredDate: { header: '黑名单截止日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.BlacklistPartnerInvestigation,
      templateTitle: ['企业名称', '统一社会信用代码', '关联企业名称', '关联类型', '关联路径详情'],
      exportExcelColumns: {
        companyNameDD: { header: '企业名称', width: 40 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        companyNameRelated: { header: '关联企业名称', width: 40 },
        relationType: { header: '关联类型', width: 30 },
        pathDetail: { header: '关联路径详情', width: 120 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.NoQualityCertification,
      templateTitle: ['企业名称', '统一社会信用代码'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.HitOuterBlackList,
      templateTitle: ['企业名称', '统一社会信用代码', '命中黑名单类型', '风险等级', '列入原因', '列入机关', '列入日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseReasonType: { header: '命中黑名单类型', width: 60 },
        level: { header: '风险等级', width: 10 },
        CaseReason: { header: '列入原因', width: 60 },
        Court: { header: '列入机关', width: 30 },
        Publishdate: { header: '列入时间', width: 30 },
      },
    },
    // 行政监管风险维度
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.CompanyCredit, //被列入严重违法失信企业名录
      templateTitle: ['企业名称', '统一社会信用代码', '关联对象（被列入对象）', '列入日期', '作出决定机关(列入)', '列入严重违法失信企业名单原因'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        relatedObject: { header: '关联对象（被列入对象）', width: 30 },
        AddDate: { header: '列入日期', width: 30 },
        AddOffice: { header: '作出决定机关(列入)', width: 30 },
        AddReason: { header: '列入严重违法失信企业名单原因', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.CompanyCreditHistory, //被列入严重违法失信企业名录历史
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '关联对象（被列入对象）',
        '移出日期',
        '作出决定机关（移出）',
        '移出严重违法失信企业名单原因',
        '列入日期',
        '作出决定机关（列入）',
        '列入严重违法失信企业名单原因',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        relatedObject: { header: '关联对象（被列入对象）', width: 30 },
        RemoveDate: { header: '移出日期', width: 20 },
        RemoveOffice: { header: '作出决定机关（移出）', width: 30 },
        RemoveReason: { header: '移出严重违法失信企业名单原因', width: 60 },
        AddDate: { header: '列入时间', width: 20 },
        AddOffice: { header: '作出决定机关（列入）', width: 30 },
        AddReason: { header: '列入严重违法失信企业名单原因', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.BusinessAbnormal3, //被列入经营异常名录
      templateTitle: ['企业名称', '统一社会信用代码', '关联对象（被列入对象）', '列入日期', '作出决定机关（列入）', '列入经营异常名录原因'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        relatedObject: { header: '关联对象（被列入对象）', width: 30 },
        CurrenceDate: { header: '列入时间', width: 30 },
        Court: { header: '作出决定机关（列入）', width: 30 },
        CaseReason: { header: '列入经营异常名录原因', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.OperationAbnormal, //历史被列入经营异常名录
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '关联对象（被列入对象）',
        '移出日期',
        '作出决定机关（移出）',
        '移出经营异常名录原因',
        '列入日期',
        '作出决定机关（列入）',
        '列入经营异常名录原因',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        relatedObject: { header: '关联对象（被列入对象）', width: 30 },
        LianDate: { header: '移出日期', width: 20 },
        ActionRemark: { header: '作出决定机关（移出）', width: 30 },
        CaseReasonType: { header: '移出经营异常名录原因', width: 60 },
        CurrenceDate: { header: '列入时间', width: 30 },
        Court: { header: '作出决定机关（列入）', width: 30 },
        CaseReason: { header: '列入经营异常名录原因', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.TaxArrearsNotice, //欠税公告
      templateTitle: ['企业名称', '统一社会信用代码', '欠税税种', '欠税金额（元）', '当前新发生的欠税金额（元）', '发布单位', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Title: { header: '欠税税种', width: 30 },
        Amount: { header: '欠税金额（元）', width: 30 },
        NewAmount: { header: '当前新发生的欠税金额（元）', width: 30 },
        IssuedBy: { header: '发布单位', width: 60 },
        PublishDate: { header: '发布日期', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.BusinessAbnormal4, //被列入非正常户
      templateTitle: ['企业名称', '统一社会信用代码', '纳税人税号', '信用类型', '列入机关', '列入日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        TaxNo: { header: '纳税人识别号', width: 30 },
        IsValid: { header: '信用类型', width: 30 },
        Court: { header: '列入机关', width: 30 },
        AddDate: { header: '列入日期', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.ProductQualityProblem1, //产品召回
      templateTitle: ['企业名称', '统一社会信用代码', '召回产品', '召回企业', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Title: { header: '召回产品', width: 30 },
        Name: { header: '召回企业', width: 30 },
        PublishDate: { header: '发布日期', width: 60 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.ProductQualityProblem2, //产品抽查不合格
      templateTitle: ['企业名称', '统一社会信用代码', '产品名称', '产品类别', '规格型号', '生产单位', '抽查/公告时间', '主要不合格项目'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseReason: { header: '产品名称', width: 30 },
        CaseReasonType: { header: '产品类别', width: 30 },
        OrgNo: { header: '规格型号', width: 20 },
        Name: { header: '生产单位', width: 40 },
        PublishDate: { header: '抽查/公告时间', width: 30 },
        actionremark: { header: '主要不合格项目', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.ProductQualityProblem6, //未准入境
      templateTitle: ['企业名称', '统一社会信用代码', '产品名称', '产品类型', '数/重量', '原因', '报送时间', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Title: { header: '产品名称', width: 30 },
        CaseReasonType: { header: '产品类型', width: 30 },
        AmountDesc: { header: '数/重量', width: 20 },
        ActionRemark: { header: '原因', width: 40 },
        LianDate: { header: '报送时间', width: 30 },
        PublishDate: { header: '发布日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.ProductQualityProblem7, //药品抽查检验不合格
      templateTitle: ['企业名称', '统一社会信用代码', '药品品名', '检查实施机关', '类型', '检测结果'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Specs: { header: '药品品名', width: 30 },
        Court: { header: '检查实施机关', width: 30 },
        CaseReasonType: { header: '类型', width: 20 },
        ActionRemark: { header: '检测结果', width: 40 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.ProductQualityProblem9, //食品安全检查不合格
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '产品名称',
        '抽检次数',
        '被抽检企业',
        '标称生产企业',
        '标称生产企业地址',
        '商标',
        '规格型号',
        '生产日期/批号',
        '抽检结果',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Title: { header: '产品名称', width: 30 },
        Amount2: { header: '抽检次数', width: 30 },
        SamplingCompanyNames: { header: '被抽检企业', width: 20 },
        ProdCompanyNames: { header: '标称生产企业', width: 30 },
        Address: { header: '标称生产企业地址', width: 30 },
        ActionRemark: { header: '商标', width: 30 },
        Specs: { header: '规格型号', width: 30 },
        Batch: { header: '生产日期/批号', width: 30 },
        ExecuteStatus: { header: '抽检结果', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.AdministrativePenalties, //行政处罚
      templateTitle: ['企业名称', '统一社会信用代码', '决定书文号', '违法事实', '处罚结果', '处罚金额(元)', '处罚单位', '处罚日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '决定书文号', width: 30 },
        CaseReason: { header: '违法事实', width: 20 },
        Title: { header: '处罚结果', width: 30 },
        Amount: { header: '处罚金额(元)', width: 10 },
        Court: { header: '处罚单位', width: 30 },
        PunishDate: { header: '处罚日期', width: 20 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.TaxPenalties, //税务处罚
      templateTitle: ['企业名称', '统一社会信用代码', '决定书文号', '违法事实', '处罚结果', '处罚金额(元)', '处罚单位', '处罚日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '决定书文号', width: 30 },
        CaseReason: { header: '违法事实', width: 20 },
        Title: { header: '处罚结果', width: 30 },
        Amount: { header: '处罚金额(元)', width: 10 },
        Court: { header: '处罚单位', width: 30 },
        PunishDate: { header: '处罚日期', width: 20 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.EnvironmentalPenalties, //环保处罚
      templateTitle: ['企业名称', '统一社会信用代码', '决定书文号', '违法事实', '处罚结果', '处罚金额(元)', '处罚单位', '处罚日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CaseNo: { header: '决定书文号', width: 30 },
        CaseReason: { header: '违法事实', width: 20 },
        Title: { header: '处罚结果', width: 30 },
        Amount: { header: '处罚金额(元)', width: 10 },
        Court: { header: '处罚单位', width: 30 },
        PunishDate: { header: '处罚日期', width: 20 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.AdministrativePenalties2, //涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚
      templateTitle: ['企业名称', '统一社会信用代码', '处罚对象名称', '决定书文号', '违法事实', '处罚结果', '处罚金额(元)', '处罚单位', '处罚日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        relatedObject: { header: '处罚对象名称', width: 30 },
        CaseNo: { header: '决定书文号', width: 30 },
        CaseReason: { header: '违法事实', width: 20 },
        Title: { header: '处罚结果', width: 30 },
        Amount: { header: '处罚金额(元)', width: 10 },
        Court: { header: '处罚单位', width: 30 },
        PunishDate: { header: '处罚日期', width: 20 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.AdministrativePenalties3, //3年前涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚
      templateTitle: ['企业名称', '统一社会信用代码', '处罚对象名称', '决定书文号', '违法事实', '处罚结果', '处罚金额(元)', '处罚单位', '处罚日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        relatedObject: { header: '处罚对象名称', width: 30 },
        CaseNo: { header: '决定书文号', width: 30 },
        CaseReason: { header: '违法事实', width: 20 },
        Title: { header: '处罚结果', width: 30 },
        Amount: { header: '处罚金额(元)', width: 10 },
        Court: { header: '处罚单位', width: 30 },
        PunishDate: { header: '处罚日期', width: 20 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.SpotCheck, //抽查检查-不合格
      templateTitle: ['企业名称', '统一社会信用代码', '检查实施机关', '类型', '日期', '结果'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Court: { header: '检查实施机关', width: 30 },
        CaseReasonType: { header: '类型', width: 20 },
        PublishDate: { header: '日期', width: 30 },
        Title: { header: '结果', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.TaxCallNotice, //税务催缴公告
      templateTitle: ['企业名称', '统一社会信用代码', '标题', '发布机构', '发布日期', '公告类型'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        title: { header: '标题', width: 30 },
        courtname: { header: '发布机构', width: 20 },
        publicdate: { header: '发布日期', width: 30 },
        noticetype: { header: '公告类型', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.TaxCallNoticeV2, //税务催缴
      templateTitle: ['企业名称', '统一社会信用代码', '税种', '欠缴金额（元）', '所属期起', '所属期止', '缴款期限', '主管税务机关', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        TaxCategory: { header: '税种', width: 30 },
        AmountOwed: { header: '欠缴金额（元）', width: 20 },
        PeriodStartDate: { header: '所属期起', width: 30 },
        PeriodEndDate: { header: '所属期止', width: 30 },
        PaymentDate: { header: '缴款期限', width: 30 },
        courtname: { header: '主管税务机关', width: 30 },
        PublishDate: { header: '发布日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.TaxReminder, //税务催报
      templateTitle: ['企业名称', '统一社会信用代码', '税种', '所属期起', '所属期止', '申报期限', '主管税务机关', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        TaxCategory: { header: '税种', width: 30 },
        PeriodStartDate: { header: '所属期起', width: 30 },
        PeriodEndDate: { header: '所属期止', width: 30 },
        PaymentDate: { header: '申报期限', width: 30 },
        courtname: { header: '主管税务机关', width: 60 },
        PublishDate: { header: '发布日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.SecurityNotice, //公安通告
      templateTitle: ['企业名称', '统一社会信用代码', '涉案企业', '涉案案由', '发布单位', '发布日期', '更新日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        name: { header: '涉案企业', width: 60 },
        reason: { header: '涉案案由', width: 30 },
        publishUnit: { header: '发布单位', width: 30 },
        publishDate: { header: '发布日期', width: 20 },
        updateDate: { header: '更新日期', width: 20 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.RegulateFinance, //监管处罚
      templateTitle: ['企业名称', '统一社会信用代码', '处罚对象名称', '决定文书号', '违法事实', '处理结果', '处理单位', '处理日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        relatedObject: { header: '处罚对象名称', width: 30 },
        caseno: { header: '决定文书号', width: 30 },
        punishReason: { header: '违法事实', width: 30 },
        Title: { header: '处理结果', width: 30 },
        Court: { header: '处理单位', width: 30 },
        PunishDate: { header: '处理日期', width: 20 },
      },
    },
    //经营稳定性风险
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.ChattelMortgage, //动产抵押
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '登记编号',
        '抵押人',
        '抵押权人',
        '所有权或使用权归属',
        '债务人履行债务的期限',
        '被担保主债权数额',
        '登记日期',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        RegisterNo: { header: '登记编号', width: 30 },
        CompanyName: { header: '抵押人', width: 20 },
        PledgeName: { header: '抵押权人', width: 30 }, //uniq(MPledegeDetail.PledgeeList.map((m)=>m.Name)).join(',')
        GuaranteeName: { header: '所有权或使用权归属', width: 30 }, //uniq(MPledegeDetail.GuaranteeList.map((m)=>m.Name)).join(',')
        FulfillObligation: { header: '债务人履行债务的期限', width: 30 }, //MPledegeDetail.GuaranteedCredRight.FulfillObligation
        Amount: { header: '被担保主债权数额', width: 30 }, //MPledegeDetail.GuaranteedCredRight.Amount
        RegisterDate: { header: '登记日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.StockPledge, //股权质押
      templateTitle: ['企业名称', '统一社会信用代码', '质押人', '质押人参股企业', '质押权人', '质押股份总数（股）', '质押股份市值（元）', '状态', '公告日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Holders: { header: '质押人', width: 30 },
        Companys: { header: '质押人参股企业', width: 20 },
        Pledgees: { header: '质押权人', width: 30 },
        ShareFrozenNum: { header: '质押股份总数（股）', width: 30 },
        SZ: { header: '质押股份市值（元）', width: 30 },
        Type: { header: '状态', width: 30 },
        NoticeDate: { header: '公告日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.EquityPledge, //股权出质
      templateTitle: ['企业名称', '统一社会信用代码', '登记编号', '出质人', '出质股权标的企业', '质权人', '出质股权数额', '登记日期', '状态'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        RegistNo: { header: '登记编号', width: 30 },
        PledgorName: { header: '出质人', width: 40 }, //PledgorInfo?.map((p)=>p.Name).join(',');
        RelatedCompanyName: { header: '出质股权标的企业', width: 40 }, //RelatedCompanyInfo.Name;
        PledgeeName: { header: '质权人', width: 30 }, //PledgeeInfo?.map((p)=>p.Name).join(',');
        PledgedAmount: { header: '出质股权数额', width: 30 },
        RegDate: { header: '登记日期', width: 30 },
        Status: { header: '状态', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.IPRPledge, //知识产权出质
      templateTitle: ['企业名称', '统一社会信用代码', '出质知产类型', '名称', '出质登记号', '出质公告日', '出质人名称', '质权人名称', '出质期限'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        TypeDesc: { header: '出质知产类型', width: 10 },
        Name: { header: '名称', width: 30 },
        RegNo: { header: '出质登记号', width: 30 },
        PublishDate: { header: '出质公告日', width: 30 },
        PledgorName: { header: '出质人名称', width: 30 },
        PledgeeName: { header: '质权人名称', width: 30 },
        PledgeDuration: { header: '出质期限', width: 40 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.LandMortgage, //土地抵押
      templateTitle: ['企业名称', '统一社会信用代码', '土地坐落', '抵押人', '抵押权人', '抵押起止日期', '抵押面积(公顷)', '抵押金额(万元)'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Address: { header: '土地坐落', width: 30 },
        MortgagorNames: { header: '抵押人', width: 40 }, //MortgagorNames?.map((p)=>p.Name).join(',');
        MortgagePeoples: { header: '抵押权人', width: 40 }, //MortgagePeoples?.map((p)=>p.Name).join(',');
        StartEndDate: { header: '抵押起止日期', width: 30 }, //StartDate-EndDate 2014-04-02 至 2024-04-01
        MortgageAcreage: { header: '抵押面积(公顷)', width: 20 },
        MortgagePrice: { header: '抵押金额(万元)', width: 20 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.BondDefaults, //债券违约
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '债券简称',
        '债券类型',
        '违约状态',
        '首次违约日期',
        '累计违约本金(亿元)',
        '累计违约利息(亿元)',
        '到期日期',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        BondShortName: { header: '债券简称', width: 30 },
        BondTypeName: { header: '债券类型', width: 40 },
        DefaultStatusDesc: { header: '违约状态', width: 40 },
        FirstDefaultDate: { header: '首次违约日期', width: 30 }, //moment(FirstDefaultDate, "YYYYMMDD").format("YYYY-MM-DD");
        AccuOverdueCapital: { header: '累计违约本金(亿元)', width: 20 },
        AccuOverdueInterest: { header: '累计违约利息(亿元)', width: 20 },
        MaturityDate: { header: '到期日期', width: 20 }, //moment(MaturityDate, "YYYYMMDD").format("YYYY-MM-DD");
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.GuaranteeRisk, //担保风险
      templateTitle: ['企业名称', '统一社会信用代码', '保证类型', '被担保方', '担保方', '债权人', '被保证债权本金(万元)', '裁判日期', '发布日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        GuaranteeType: { header: '保证类型', width: 30 },
        VoucheeName: { header: '被担保方', width: 40 }, //VoucheeName.map((m)=>m.ShowName).join(',')
        GuaranteeName: { header: '担保方', width: 40 }, //Guarantee.map((m)=>m.ShowName).join(',')
        CreditorName: { header: '债权人', width: 30 }, //Creditor.map((m)=>m.ShowName).join(',')
        GuaranteeMoney: { header: '被保证债权本金(万元)', width: 20 }, //GuaranteeMoney/10000
        Judgedate: { header: '裁判日期', width: 20 }, //moment(Judgedate * 1000).format(DATE_FORMAT)
        PublicDate: { header: '发布日期', width: 20 }, //moment(PublicDate * 1000).format(DATE_FORMAT)
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.NoTender, //无招投标记录
      templateTitle: ['企业名称', '统一社会信用代码'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainInfoUpdateScope, //近期变更经营范围
      templateTitle: ['企业名称', '统一社会信用代码', '变更日期', '变更前', '变更后'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        ChangeDate: { header: '变更日期', width: 30 },
        Scope: { header: '变更前', width: 60 },
        afterScope: { header: '变更后', width: 60 }, //after.Scope
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainInfoUpdateAddress, //近期变更注册地址
      templateTitle: ['企业名称', '统一社会信用代码', '变更日期', '变更前', '变更后'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        ChangeDate: { header: '变更日期', width: 30 },
        Address: { header: '变更前', width: 60 },
        afterAddress: { header: '变更后', width: 60 }, //after.Address
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainInfoUpdateName, //近期变更企业名称
      templateTitle: ['企业名称', '统一社会信用代码', '变更日期', '变更前', '变更后'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        ChangeDate: { header: '变更日期', width: 30 },
        CompanyName: { header: '变更前', width: 60 },
        afterCompanyName: { header: '变更后', width: 60 }, //after.CompanyName
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainInfoUpdateLegalPerson, //近期变更法定代表人
      templateTitle: ['企业名称', '统一社会信用代码', '变更日期', '变更前', '变更后'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        ChangeDate: { header: '变更日期', width: 30 },
        OperName: { header: '变更前', width: 60 },
        afterOperName: { header: '变更后', width: 60 }, //after.OperName
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainInfoUpdateHolder, //近期变更大股东
      templateTitle: ['企业名称', '统一社会信用代码', '变更日期', '变更前', '变更后'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        ChangeDate: { header: '变更日期', width: 30 },
        BeforeName: { header: '变更前', width: 60 }, //JSON.parse(BeforeContent)[0].Name
        AfterName: { header: '变更后', width: 60 }, //JSON.parse(AfterContent)[0].Name
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainInfoUpdatePerson, //近期变更实际控制人
      templateTitle: ['企业名称', '统一社会信用代码', '变更日期', '变更前', '变更后'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        ChangeDate: { header: '变更日期', width: 30 },
        BeforeName: { header: '变更前', width: 60 }, //JSON.parse(BeforeContent).Name
        AfterName: { header: '变更后', width: 60 }, //JSON.parse(AfterContent).Name
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainInfoUpdateBeneficiary, //近期变更受益所有人
      templateTitle: ['企业名称', '统一社会信用代码', '变更日期', '变更前', '变更后'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        ChangeDate: { header: '变更日期', width: 30 },
        BeforeName: { header: '变更前', width: 30 }, //JSON.parse(JSON.parse(ChangeExtend)[0].BeforeContent).Name
        AfterName: { header: '变更后', width: 30 }, //JSON.parse(JSON.parse(ChangeExtend)[0].AfterContent).Name
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.BillDefaults, //票据违约
      templateTitle: ['企业名称', '统一社会信用代码', '承兑人', '承兑人开户行', '截止日期', '披露日期', '逾期金额(元)', '状态'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        CompanyName: { header: '承兑人', width: 30 },
        BankName: { header: '承兑人开户行', width: 30 },
        EndDate: { header: '截止日期', width: 30 },
        PublishDate: { header: '披露日期', width: 30 },
        OverdueBalance: { header: '逾期金额(元)', width: 30 },
        OverdueStatus: { header: '状态', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.Liquidation, //清算信息
      templateTitle: ['企业名称', '统一社会信用代码', '清算组负责人', '清算组成员'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Leader: { header: '清算组负责人', width: 20 },
        Member: { header: '清算组成员', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.NegativeNewsRecent, //近三年负面新闻
      templateTitle: ['企业名称', '统一社会信用代码', '新闻标题', '标签', '来源', '新闻链接', '发布时间'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        title: { header: '新闻标题', width: 30 },
        tagsnew: { header: '标签', width: 30 }, //codedesc.append(tagsnew).join('#')
        source: { header: '来源', width: 30 },
        url: { header: '新闻链接', width: 60 },
        publishtime: { header: '发布时间', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.NegativeNewsHistory, //三年前负面新闻
      templateTitle: ['企业名称', '统一社会信用代码', '新闻标题', '标签', '来源', '新闻链接', '发布时间'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        title: { header: '新闻标题', width: 30 },
        tagsnew: { header: '标签', width: 30 },
        source: { header: '来源', width: 30 },
        url: { header: '新闻链接', width: 60 },
        publishtime: { header: '发布时间', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.CapitalReduction, //减资公告
      templateTitle: ['企业名称', '统一社会信用代码', '公告内容', '公告期限', '公告日期'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        Content: { header: '公告内容', width: 30 },
        NoticePeriod: { header: '公告期限', width: 30 },
        NoticeDate: { header: '公告日期', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.FinancialHealth, //财务健康报告
      templateTitle: ['企业名称', '统一社会信用代码', '资产负债率'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        assetLiabilityRatio: { header: '资产负债率', width: 30 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel2Enums.FakeRegister, //涉嫌冒名登记
      templateTitle: [
        '企业名称',
        '统一社会信用代码',
        '冒名登记事项',
        '当事人姓名',
        '冒名登记时间',
        '公告期自',
        '公告期至',
        '处理结果',
        '作出决定时间',
        '作出决定机关',
      ],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        fakeRegisterItem: { header: '冒名登记事项', width: 60 },
        partyName: { header: '当事人姓名', width: 20 },
        fakeRegisterTime: { header: '冒名登记时间', width: 20 },
        startDate: { header: '公告期自', width: 20 },
        endDate: { header: '公告期至', width: 20 },
        processResult: { header: '处理结果', width: 20 },
        decisionDate: { header: '作出决定时间', width: 20 },
        executeGov: { header: '作出决定机关', width: 40 },
      },
    },
    {
      explainRows: 3,
      limit: 5000,
      sheetName: DimensionLevel3Enums.MainInfoUpdateRegisteredCapital, //注册资本变更
      templateTitle: ['企业名称', '统一社会信用代码', '变更前', '变更后'],
      exportExcelColumns: {
        companyName: { header: '企业名称', width: 60 },
        creditcode: { header: '统一社会信用代码', width: 30 },
        beforeContent: { header: '变更前', width: 30 },
        afterContent: { header: '变更后', width: 30 },
      },
    },
  ],
};

/**
 * 招标排查记录导出模板设置
 */
export const TenderRecordExcelItemSetting: ExcelParserSettingPO = {
  explainRows: 3,
  limit: 5000,
  sheetName: '招标排查历史记录',
  templateTitle: [
    '项目名称',
    '项目编号',
    '排查编号',
    '排查主体',
    '排查结果',
    '资质筛查',
    '深度关系排查',
    '涉采购不良行为',
    '共同投标分析',
    '内部黑名单',
    '潜在利益冲突',
    '操作人',
    '排查时间',
  ],
  exportExcelColumns: {
    projectName: { header: '项目名称', width: 30 },
    projectNo: { header: '项目编号', width: 30 },
    tenderNo: { header: '排查编号', width: 30 },
    companyList: { header: '排查主体', width: 40 },
    result: { header: '排查结果', width: 30 },
    BiddingCompanyCertification: { header: '资质筛查', width: 30 },
    BiddingCompanyRelation: { header: '深度关系排查', width: 30 },
    PurchaseIllegal: { header: '涉采购不良行为', width: 30 },
    JointBiddingAnalysis: { header: '共同投标分析', width: 30 },
    risk_inner_blacklist: { header: '内部黑名单', width: 30 },
    risk_interest_conflict: { header: '潜在利益冲突', width: 20 },
    editor: { header: '操作人', width: 30 },
    createDate: { header: '排查时间', width: 30 },
  },
  excelColumnsStartLine: 3,
  tableTitle: '招标排查记录列表信息',
  tableTitleLine: 2,
  headerDescriptionLine: 1,
  headerDescription: QccExcelDeclaration,
};

/**
 * 特定利益关系排查记录导出模板设置
 */
export const SpecificRecordExcelItemSetting: ExcelParserSettingPO = {
  explainRows: 3,
  limit: 5000,
  sheetName: '特定利益关系排查历史记录',
  templateTitle: ['项目名称', '项目编号', '排查编号', '排查主体', '排查结果', '命中维度', '操作人', '排查时间'],
  exportExcelColumns: {
    projectName: { header: '项目名称', width: 30 },
    projectNo: { header: '项目编号', width: 30 },
    recordNo: { header: '排查编号', width: 30 },
    companyNames: { header: '排查主体', width: 40 },
    result: { header: '排查结果', width: 30 },
    hitDimensions: { header: '命中维度', width: 40 },
    editorName: { header: '操作人', width: 30 },
    createDateStr: { header: '排查时间', width: 30 },
  },
  excelColumnsStartLine: 3,
  tableTitle: '特定利益关系排查记录列表信息',
  tableTitleLine: 2,
  headerDescriptionLine: 1,
  headerDescription: QccExcelDeclaration,
};

/**
 * 特定利益关系排查记录导出模板设置
 */
export const SpecificBatchStatisticExcelItemSetting: ExcelParserSettingPO = {
  explainRows: 3,
  limit: 5000,
  sheetName: '排查统计',
  templateTitle: ['风险类型', '匹配数量'],
  exportExcelColumns: {
    riskLevel: { header: '风险类型', width: 30 },
    riskCount: { header: '匹配数量', width: 30 },
  },
  aCell: {
    ActualController: '上下游为母子公司或由相同的实际控制人控制',
    CrossShareHolding: '上下游企业交叉持股',
    SameEmployee: '上下游企业主要负责人、董事、监事、高级管理人员相同',
    ContactWay: '上下游企业注册地址、实际办公地址、业务联系人或联系电话相同',
    GuaranteeRelation: '上下游企业一方为另一方贸易合同履约提供担保',
    UpAndDownRelation: '上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商',
    OtherRelations: '其他根据实质重于形式原则认定存在特定利益关系的情形',
  },
};

/**
 * 第三方列表导出模板设置
 */
export const CustomerExportTemplate: ExcelParserSettingPO = {
  explainRows: 3,
  limit: 5000,
  sheetName: '第三方列表信息',
  templateTitle: [
    '企业名称',
    '曾用名',
    '统一社会信用码',
    '风险等级',
    '企查分',
    '所属分组',
    '登记状态',
    '法定代表人',
    '成立时间',
    '注册资本',
    '实缴资本',
    '机构类型',
    '企业性质',
    '企业规模',
    '上市公司',
    '省份地区',
    '注册地址',
    '所属行业',
    '营业收入（万元）',
    '员工人数',
    '参保人数',
    '标签',
    '合作开始日期',
    '合作截止日期',
    '授信金额',
    '合同金额',
    '资金占用',
    '部门',
    '负责人',
    '联系人',
    '联系电话',
    '邮箱',
    '添加人',
    '更新时间',
  ],
  exportExcelColumns: {
    name: { header: '企业名称', width: 50 },
    originalName: { header: '曾用名', width: 50 },
    creditcode: { header: '统一社会信用码', width: 50 },
    result: { header: '风险等级', width: 10 },
    creditRate: { header: '企查分', width: 30 },
    group: { header: '所属分组', width: 10 },
    registrationStatus: { header: '登记状态', width: 20 },
    opername: { header: '法定代表人', width: 30 },
    startDateCode: { header: '成立时间', width: 30 },
    registcapi: { header: '注册资本', width: 30 },
    reccap: { header: '实缴资本', width: 30 },
    enterpriseType: { header: '机构类型', width: 30 },
    treasuryType: { header: '企业性质', width: 30 },
    scale: { header: '企业规模', width: 30 },
    listStatus: { header: '上市公司', width: 30 },
    area: { header: '省份地区', width: 30 },
    address: { header: '注册地址', width: 60 },
    industry: { header: '所属行业', width: 30 },
    companyRevenue: { header: '营业收入（万元）', width: 30 },
    employeecount: { header: '员工人数', width: 30 },
    insuredcount: { header: '参保人数', width: 30 },
    label: { header: '标签', width: 30 },
    startDate: { header: '合作开始时间', width: 30 },
    endDate: { header: '合作截止时间', width: 30 },
    creditQuota: { header: '授信金额', width: 30 },
    contactQuota: { header: '合同金额', width: 30 },
    cost: { header: '资金占用', width: 30 },
    customerDepartment: { header: '部门', width: 30 },
    principal: { header: '负责人', width: 10 },
    contactName: { header: '联系人', width: 30 },
    phone: { header: '联系电话', width: 30 },
    email: { header: '邮箱', width: 30 },
    creator: { header: '添加人', width: 10 },
    updateDate: { header: '更新时间', width: 10 },
  },
  excelColumnsStartLine: 3,
  tableTitleLine: 2,
  headerDescriptionLine: 1,
  tableTitle: '第三方列表信息',
  headerDescription: QccExcelDeclaration,
};

/**
 * 蔡司第三方列表导出模板
 * @constructor
 */
export const ZEEISSCustomerExportTemplate = (): ExcelParserSettingPO => {
  const exportTemplate = cloneDeep(CustomerExportTemplate);
  return Object.keys(exportTemplate).reduce((acc, key) => {
    if (key === 'templateTitle') {
      acc[key] = exportTemplate[key].filter((item) => item !== '企查分');
    } else if (key === 'exportExcelColumns') {
      delete exportTemplate[key].creditRate;
      acc[key] = exportTemplate[key];
    } else {
      acc[key] = exportTemplate[key];
    }
    return acc;
  }, {}) as ExcelParserSettingPO;
};

/**
 * 维度详情导出模板设置表头说明
 * @param detailSetting
 * @param excelColumnsStartLine:2
 * @param tableTitleLine
 * @param tableTitle
 * @constructor
 */
export const DimensionDetailsSetHeaderDescription = (
  detailSetting: ExcelGroupVersionParserSettingPO,
  excelColumnsStartLine: number,
  tableTitleLine?: number,
  tableTitle?: string,
): ExcelGroupVersionParserSettingPO => {
  const exportTemplate = cloneDeep(detailSetting);
  exportTemplate[GroupDimensionVersionEnums.V1].forEach((item) => {
    item.excelColumnsStartLine = excelColumnsStartLine;
    if (tableTitleLine && tableTitle) {
      item.tableTitleLine = tableTitleLine;
      item.tableTitle = tableTitle;
    }
    item.headerDescriptionLine = 1;
    item.headerDescription = QccExcelDeclaration;
  });
  return exportTemplate;
};

export const DimensionDetailsSetHeaderDescription1 = (
  detailSetting: ExcelParserSettingPO[],
  excelColumnsStartLine: number,
  tableTitleLine?: number,
  tableTitle?: string,
): ExcelParserSettingPO[] => {
  const exportTemplate = cloneDeep(detailSetting);
  exportTemplate.forEach((item) => {
    item.excelColumnsStartLine = excelColumnsStartLine;
    if (tableTitleLine && tableTitle) {
      item.tableTitleLine = tableTitleLine;
      item.tableTitle = tableTitle;
    }
    item.headerDescriptionLine = 1;
    item.headerDescription = QccExcelDeclaration;
  });
  return exportTemplate;
};
//导出模板
export const FileExportTemplate: FileExportTemplateType = {
  [BatchBusinessTypeEnums.Diligence_Batch_Detail]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '排查统计',
        templateTitle: ['风险类型', '匹配数量'],
        exportExcelColumns: {
          riskLevel: { header: '风险类型', width: 30 },
          riskCount: { header: '匹配数量', width: 30 },
        },
        aCell: {
          risk_base_info: '基础资质',
          risk_legal: '法律风险',
          risk_administrative_supervision: '行政监管风险',
          risk_operate_stability: '经营稳定性风险',
          risk_partner_investigation: '交叉重叠关系',
          risk_inner_blacklist: '内部黑名单',
          risk_outer_blacklist: '外部黑名单',
          risk_interest_conflict: '潜在利益冲突',
        },
      },
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '排查结果',
        templateTitle: [
          '企业名称',
          '统一社会信用代码',
          '排查结果',
          '企查分',
          '命中指标',
          '基础资质',
          '法律风险',
          '行政监管风险',
          '经营稳定性风险',
          '负面新闻',
          '交叉重叠关系',
          '内部黑名单',
          '外部黑名单',
          '潜在利益冲突',
          '操作人',
          '排查时间',
        ],
        exportExcelColumns: {
          companyName: { header: '企业名称', width: 30 },
          creditcode: { header: '统一社会信用代码', width: 30 },
          result: { header: '排查结果', width: 30 },
          creditRate: { header: '企查分', width: 30 },
          hit: { header: '命中指标', width: 40 },
          risk_base_info: { header: '基础资质', width: 30 },
          risk_legal: { header: '法律风险', width: 30 },
          risk_administrative_supervision: { header: '行政监管风险', width: 30 },
          risk_operate_stability: { header: '经营稳定性风险', width: 30 },
          risk_negative_news: { header: '负面新闻', width: 20 },
          risk_partner_investigation: { header: '交叉重叠关系', width: 30 },
          risk_inner_blacklist: { header: '内部黑名单', width: 30 },
          risk_outer_blacklist: { header: '外部黑名单', width: 30 },
          risk_interest_conflict: { header: '潜在利益冲突', width: 30 },
          editor: { header: '操作人', width: 30 },
          createDate: { header: '排查时间', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '排查记录列表信息',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
    [GroupDimensionVersionEnums.V2]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '排查统计',
        templateTitle: ['风险类型', '匹配数量'],
        exportExcelColumns: {
          riskLevel: { header: '风险类型', width: 30 },
          riskCount: { header: '匹配数量', width: 30 },
        },
        aCell: {
          risk_partner_investigation: '交叉重叠关系',
          risk_interest_conflict: '潜在利益冲突',
          risk_newly_established: '新成立第三方',
          risk_punished_employees: '被处罚员工',
          risk_negative_opinion: '负面信息',
          risk_blacklist: '第三方黑名单',
        },
      },
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '排查结果',
        templateTitle: [
          '企业名称',
          '统一社会信用代码',
          '风险等级',
          '命中指标',
          '交叉重叠关系',
          '潜在利益冲突',
          '新成立第三方',
          '被处罚员工',
          '负面信息',
          '第三方黑名单',
          '操作人',
          '排查时间',
        ],
        exportExcelColumns: {
          companyName: { header: '企业名称', width: 30 },
          creditcode: { header: '统一社会信用代码', width: 30 },
          level: { header: '风险等级', width: 30 },
          hit: { header: '命中指标', width: 40 },
          risk_partner_investigation: { header: '交叉重叠关系', width: 30 },
          risk_interest_conflict: { header: '潜在利益冲突', width: 30 },
          risk_newly_established: { header: '新成立第三方', width: 30 },
          risk_punished_employees: { header: '被处罚员工', width: 30 },
          risk_negative_opinion: { header: '负面信息', width: 30 },
          risk_blacklist: { header: '第三方黑名单', width: 30 },
          editor: { header: '操作人', width: 30 },
          createDate: { header: '排查时间', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '排查记录列表信息',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Person_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '人员信息',
        templateTitle: [
          '姓名（员工/近亲属）',
          '员工编号',
          '与员工关系',
          '人员分组',
          '手机号码',
          '电子邮箱',
          '关联企业',
          '证件号',
          '出生日期',
          '出生地区',
          '添加人',
          '更新时间',
        ],
        exportExcelColumns: {
          name: { header: '姓名（员工/近亲属）', width: 30 },
          personNo: { header: '员工编号', width: 30 },
          relationship: { header: '与员工关系', width: 30 },
          group: { header: '人员分组', width: 30 },
          phone: { header: '手机号码', width: 30 },
          email: { header: '电子邮箱', width: 30 },
          companyName: { header: '关联企业', width: 30 },
          cardId: { header: '证件号', width: 30 },
          birthDay: { header: '出生日期', width: 30 },
          birthPlace: { header: '出生地区', width: 30 },
          creator: { header: '添加人', width: 30 },
          updateDate: { header: '更新时间', width: 30 },
        },
        excelColumnsStartLine: 2, //column起始行
        tableTitle: '人员信息',
        tableTitleLine: 1,
      },
    ],
  },
  [BatchBusinessTypeEnums.InnerBlacklist_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '内部黑名单',
        templateTitle: [
          '企业名称',
          '黑名单状态',
          '风险等级',
          '企业分组',
          '登记状态',
          '成立时间',
          '注册资本',
          '机构类型',
          '企业性质',
          '所属地区',
          '所属行业',
          '标签',
          '列入原因',
          '列入日期',
          '有效期',
          '截止日期',
          '来源部门',
          '备注',
          '添加人',
          '更新时间',
        ],
        exportExcelColumns: {
          companyName: { header: '企业名称', width: 50 },
          expiredStatus: { header: '黑名单状态', width: 20 },
          result: { header: '风险等级', width: 20 },
          group: { header: '企业分组', width: 20 },
          registrationStatus: { header: '登记状态', width: 20 },
          startDateCode: { header: '成立时间', width: 30 },
          registcapi: { header: '注册资本', width: 30 },
          // econkind: { header: '企业类型', width: 30 },
          enterpriseType: { header: '机构类型', width: 30 },
          econType: { header: '企业性质', width: 30 },
          area: { header: '所属地区', width: 30 },
          industry: { header: '所属行业', width: 30 },
          label: { header: '标签', width: 30 },
          reason: { header: '列入原因', width: 30 },
          joinDate: { header: '列入日期', width: 30 },
          duration: { header: '有效期', width: 30 },
          expiredDate: { header: '截止日期', width: 30 },
          department: { header: '来源部门', width: 30 },
          comment: { header: '备注', width: 30 },
          creator: { header: '添加人', width: 30 },
          updateDate: { header: '更新时间', width: 30 },
        },
        excelColumnsStartLine: 2,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Diligence_Record]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '排查记录',
        templateTitle: [
          '企业名称',
          '统一社会信用代码',
          '风险等级',
          '企查分',
          '命中指标',
          '基础资质',
          '法律风险',
          '行政监管风险',
          '经营稳定性风险',
          '负面新闻',
          '交叉重叠关系',
          '内部黑名单',
          '外部黑名单',
          '潜在利益冲突',
          '操作人',
          '排查时间',
        ],
        exportExcelColumns: {
          name: { header: '企业名称', width: 30 },
          creditcode: { header: '统一社会信用代码', width: 30 },
          result: { header: '风险等级', width: 30 },
          creditRate: { header: '企查分', width: 30 },
          hit: { header: '命中指标', width: 40 },
          risk_base_info: { header: '基础资质', width: 30 },
          risk_legal: { header: '法律风险', width: 30 },
          risk_administrative_supervision: { header: '行政监管风险', width: 30 },
          risk_operate_stability: { header: '经营稳定性风险', width: 30 },
          risk_negative_news: { header: '负面新闻', width: 20 },
          risk_partner_investigation: { header: '交叉重叠关系', width: 30 },
          risk_inner_blacklist: { header: '内部黑名单', width: 30 },
          risk_outer_blacklist: { header: '外部黑名单', width: 30 },
          risk_interest_conflict: { header: '潜在利益冲突', width: 30 },
          editor: { header: '操作人', width: 30 },
          createDate: { header: '排查时间', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '排查记录列表信息',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
    [GroupDimensionVersionEnums.V2]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '排查记录',
        templateTitle: [
          '企业名称',
          '统一社会信用代码',
          '风险等级',
          '交叉重叠关系',
          '潜在利益冲突',
          '新成立第三方',
          '被处罚员工',
          '负面信息',
          '第三方黑名单',
          '操作人',
          '操作时间',
        ],
        exportExcelColumns: {
          name: { header: '企业名称', width: 30 },
          creditcode: { header: '统一社会信用代码', width: 30 },
          level: { header: '风险等级', width: 30 },
          risk_partner_investigation: { header: '交叉重叠关系', width: 30 },
          risk_interest_conflict: { header: '潜在利益冲突', width: 30 },
          risk_newly_established: { header: '新成立第三方', width: 30 },
          risk_punished_employees: { header: '被处罚员工', width: 30 },
          risk_negative_opinion: { header: '负面信息', width: 30 },
          risk_blacklist: { header: '第三方黑名单', width: 30 },
          editor: { header: '操作人', width: 30 },
          createDate: { header: '操作时间', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '排查记录列表信息',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '风险排查-消费详情',
        templateTitle: ['初次消费时间', '操作人', '企业名称'],
        exportExcelColumns: {
          createDate: { header: '初次消费时间', width: 30 },
          editor: { header: '操作人', width: 30 },
          companyName: { header: '企业名称', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '排查记录列表信息',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '风险巡检-消费详情',
        templateTitle: ['巡检时间', '操作人', '巡检识别'],
        exportExcelColumns: {
          createDate: { header: '巡检时间', width: 30 },
          editor: { header: '操作人', width: 30 },
          statisticsInfo: { header: '巡检识别', width: 60 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '排查记录列表信息',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '招标排查-消费详情',
        templateTitle: ['排查时间', '操作人', '排查编号', '排查企业数', '消耗额度'],
        exportExcelColumns: {
          createDate: { header: '巡检时间', width: 30 },
          editor: { header: '操作人', width: 30 },
          tenderNo: { header: '排查编号', width: 30 },
          companyListNumber: { header: '排查企业数', width: 30 },
          paidCount: { header: '消耗额度', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '排查记录列表信息',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '特定利益关系排查-消费详情',
        templateTitle: ['排查时间', '操作人', '排查编号', '排查企业数', '消耗额度'],
        exportExcelColumns: {
          createDate: { header: '巡检时间', width: 30 },
          editor: { header: '操作人', width: 30 },
          recordNo: { header: '排查编号', width: 30 },
          companyListNumber: { header: '排查企业数', width: 30 },
          paidCount: { header: '消耗额度', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '排查记录列表信息',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Customer_Export]: {
    [GroupDimensionVersionEnums.V1]: [CustomerExportTemplate],
    [GroupDimensionVersionEnums.V2]: [ZEEISSCustomerExportTemplate()],
  },
  [BatchBusinessTypeEnums.Tender_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 1000,
        sheetName: '标讯信息',
        templateTitle: [
          '预警方案名称',
          '项目名称',
          '信息类型',
          '省份',
          '城市',
          '区县',
          '行业分类',
          '招采单位',
          '招采类型',
          '项目编号',
          '代理单位',
          '中标单位',
          '中标金额',
          '中标候选单位',
          '联系电话',
          '发布时间',
          '是否含附件',
          '是否命中中标单位预警',
          '原文链接',
        ],
        exportExcelColumns: {
          settingName: { header: '预警方案名称', width: 20 },
          title: { header: '项目名称', width: 50 },
          ifbprogress: { header: '信息类型', width: 20 },
          province: { header: '省份', width: 10 },
          city: { header: '城市', width: 10 },
          district: { header: '区县', width: 10 },
          industryv2: { header: '行业分类', width: 10 },
          ifbunit: { header: '招采单位', width: 30 },
          tendertype: { header: '招采类型', width: 10 },
          projectno: { header: '项目编号', width: 30 },
          agent: { header: '代理单位', width: 30 },
          wtbunit: { header: '中标单位', width: 30 },
          wtbamt: { header: '中标金额', width: 10 },
          wtbcandidate: { header: '中标候选单位', width: 30 },
          teljson: { header: '联系电话', width: 50 },
          publishdate: { header: '发布时间', width: 20 },
          hasattachment: { header: '是否含附件', width: 15 },
          biddingAlert: { header: '是否命中中标单位预警', width: 25 },
          originalurl: { header: '原文链接', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        tableTitle: '标讯信息',
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Dimension_Detail_Export]: DimensionDetailsSetHeaderDescription(DimensionDetails, 2),
  [BatchBusinessTypeEnums.Analyze_Dimension_Detail]: DimensionDetailsSetHeaderDescription(DimensionDetails, 2),
  [BatchBusinessTypeEnums.Risk_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '风险动态',
        templateTitle: ['企业名称', '风险等级', '风险指标', '风险内容', '更新时间', '状态', '所在分组'],
        exportExcelColumns: {
          companyName: { header: '企业名称', width: 40 },
          dimensionLevel: { header: '风险等级', width: 20 },
          dimension: { header: '风险指标', width: 20 },
          content: { header: '风险内容', width: 40 },
          riskCreateDate: { header: '更新时间', width: 30 },
          status: { header: '状态', width: 30 },
          group: { header: '所在分组', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: `合作监控-风险动态${moment(new Date()).format(DATE_FORMAT)}`,
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Sentiment_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '舆情动态',
        templateTitle: ['企业名称', '风险等级', '风险指标', '风险内容', '更新时间', '状态', '所在分组', '新闻链接'],
        exportExcelColumns: {
          companyName: { header: '企业名称', width: 40 },
          dimensionLevel: { header: '风险等级', width: 20 },
          dimension: { header: '风险指标', width: 20 },
          content: { header: '风险内容', width: 40 },
          dynamicCreateDate: { header: '更新时间', width: 30 },
          status: { header: '状态', width: 30 },
          group: { header: '所在分组', width: 30 },
          originalUrl: { header: '新闻链接', width: 60 },
        },
        excelColumnsStartLine: 3,
        tableTitle: `合作监控-舆情动态${moment(new Date()).format(DATE_FORMAT)}`,
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Analyze_Record_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '风险巡检',
        templateTitle: [
          '企业名称',
          '统一社会信用代码',
          '登记状态',
          '成立时间',
          '注册资本',
          '企业类型',
          '企业规模',
          '所属地区',
          '注册地址',
          '所属行业',
          '营业收入',
          '员工人数',
          '风险等级',
          '标签',
          '企查分',
          '所属分组',
          '部门',
          '命中指标',
          '基础资质',
          '法律风险',
          '行政监管风险',
          '经营稳定性风险',
          '负面新闻',
          '交叉重叠关系',
          '内部黑名单',
          '外部黑名单',
          '潜在利益冲突',
          '操作人',
          '排查时间',
        ],
        exportExcelColumns: {
          companyName: { header: '企业名称', width: 30 },
          creditcode: { header: '统一社会信用代码', width: 30 },
          registrationStatus: { header: '登记状态', width: 20 },
          startDateCode: { header: '成立时间', width: 30 },
          registcapi: { header: '注册资本', width: 30 },
          econkind: { header: '企业类型', width: 30 },
          scale: { header: '企业规模', width: 30 },
          area: { header: '所属地区', width: 30 },
          address: { header: '注册地址', width: 60 },
          industry: { header: '所属行业', width: 30 },
          companyRevenue: { header: '营业收入', width: 20 },
          insuredcount: { header: '员工人数', width: 20 },
          level: { header: '风险等级', width: 20 },
          customerLabelNames: { header: '标签', width: 40 },
          creditRate: { header: '企查分', width: 20 },
          customerGroupNames: { header: '所属分组', width: 40 },
          customerDepNames: { header: '部门', width: 40 },
          hit: { header: '命中指标', width: 40 },
          risk_base_info: { header: '基础资质', width: 20 },
          risk_legal: { header: '法律风险', width: 20 },
          risk_administrative_supervision: { header: '行政监管风险', width: 20 },
          risk_operate_stability: { header: '经营稳定性风险', width: 20 },
          risk_negative_news: { header: '负面新闻', width: 20 },
          risk_partner_investigation: { header: '交叉重叠关系', width: 20 },
          risk_inner_blacklist: { header: '内部黑名单', width: 20 },
          risk_outer_blacklist: { header: '外部黑名单', width: 20 },
          risk_interest_conflict: { header: '潜在利益冲突', width: 20 },
          editor: { header: '操作人', width: 20 },
          createDate: { header: '排查时间', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '风险巡检结果',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
    [GroupDimensionVersionEnums.V2]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '风险巡检',
        templateTitle: [
          '企业名称',
          '统一社会信用代码',
          '登记状态',
          '成立时间',
          '注册资本',
          '企业类型',
          '企业规模',
          '所属地区',
          '注册地址',
          '所属行业',
          '营业收入',
          '员工人数',
          '风险等级',
          '命中指标',
          '交叉重叠关系',
          '潜在利益冲突',
          '新成立第三方',
          '被处罚员工',
          '负面信息',
          '第三方黑名单',
          '操作人',
          '排查时间',
        ],
        exportExcelColumns: {
          companyName: { header: '企业名称', width: 30 },
          creditcode: { header: '统一社会信用代码', width: 30 },
          registrationStatus: { header: '登记状态', width: 20 },
          startDateCode: { header: '成立时间', width: 30 },
          registcapi: { header: '注册资本', width: 30 },
          econkind: { header: '企业类型', width: 30 },
          scale: { header: '企业规模', width: 30 },
          area: { header: '所属地区', width: 30 },
          address: { header: '注册地址', width: 60 },
          industry: { header: '所属行业', width: 30 },
          companyRevenue: { header: '营业收入', width: 30 },
          insuredcount: { header: '员工人数', width: 30 },
          level: { header: '风险等级', width: 30 },
          hit: { header: '命中指标', width: 40 },
          risk_partner_investigation: { header: '交叉重叠关系', width: 20 },
          risk_interest_conflict: { header: '潜在利益冲突', width: 20 },
          risk_newly_established: { header: '新成立第三方', width: 20 },
          risk_punished_employees: { header: '被处罚员工', width: 20 },
          risk_negative_opinion: { header: '负面信息', width: 20 },
          risk_blacklist: { header: '第三方黑名单', width: 20 },
          editor: { header: '操作人', width: 20 },
          createDate: { header: '排查时间', width: 30 },
        },
        excelColumnsStartLine: 3,
        tableTitle: '风险巡检结果',
        tableTitleLine: 2,
        headerDescriptionLine: 1,
        headerDescription: QccExcelDeclaration,
      },
    ],
  },
  [BatchBusinessTypeEnums.Tender_Diligence_Record_Export]: {
    //批量招标排查记录列表导出模板
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '排查统计',
        templateTitle: ['风险类型', '匹配数量'],
        exportExcelColumns: {
          riskLevel: { header: '风险类型', width: 30 },
          riskCount: { header: '匹配数量', width: 30 },
        },
        aCell: {
          BiddingCompanyCertification: '资质筛查',
          BiddingCompanyRelation: '深度关系排查',
          JointBiddingAnalysis: '共同投标分析',
          risk_inner_blacklist: '内部黑名单',
          risk_interest_conflict: '潜在利益冲突',
          PurchaseIllegal: '涉采购不良行为',
        },
      },
      TenderRecordExcelItemSetting,
    ],
  },
  [BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export]: {
    [GroupDimensionVersionEnums.V1]: [
      {
        explainRows: 3,
        limit: 5000,
        sheetName: '排查统计',
        templateTitle: ['风险类型', '匹配数量'],
        exportExcelColumns: {
          riskLevel: { header: '风险类型', width: 30 },
          riskCount: { header: '匹配数量', width: 30 },
        },
        aCell: {
          BiddingCompanyCertification: '资质筛查',
          BiddingCompanyRelation: '深度关系排查',
          JointBiddingAnalysis: '共同投标分析',
          risk_inner_blacklist: '内部黑名单',
          risk_interest_conflict: '潜在利益冲突',
          PurchaseIllegal: '涉采购不良行为',
        },
      },
      TenderRecordExcelItemSetting,
      ...DimensionDetailsSetHeaderDescription1(TenderDimensionDetails, 2),
    ],
  },
  [BatchBusinessTypeEnums.Tender_Diligence_History_Export]: {
    //招标排查历史记录列表导出模板
    [GroupDimensionVersionEnums.V1]: [TenderRecordExcelItemSetting],
  },
  [BatchBusinessTypeEnums.Specific_Record_List_Export]: {
    //特定利益关系排查历史记录列表导出模板
    [GroupDimensionVersionEnums.V1]: [SpecificRecordExcelItemSetting],
  },
  [BatchBusinessTypeEnums.Tender_Dimension_Detail_Export]: DimensionDetailsSetHeaderDescription(TenderGroupDimensionDetails, 2),
  [BatchBusinessTypeEnums.Specific_Batch_Export]: {
    //特定利益关系批量排查记录列表导出模板
    [GroupDimensionVersionEnums.V1]: [SpecificBatchStatisticExcelItemSetting, SpecificRecordExcelItemSetting],
  },
};

export const SheetKeyName = {
  [DimensionLevel3Enums.NoticeInTimePeriod]: '近期多起开庭公告',
  [DimensionLevel1Enums.Risk_InterestConflict]: '潜在利益冲突',
  [DimensionLevel3Enums.SuspectedInterestConflict]: '疑似潜在利益冲突',
  [DimensionLevel2Enums.CustomerPartnerInvestigation]: '与第三方列表企业存在投资任职关联',
  [DimensionLevel2Enums.HitInnerBlackList]: '内部黑名单排查-命中内部黑名单列表',
  [DimensionLevel2Enums.BlacklistPartnerInvestigation]: '内部黑名单排查-与内部黑名单企业存在投资任职关联',
  [DimensionLevel3Enums.BusinessAbnormal1]: '经营状态非存续',
  [DimensionLevel3Enums.BusinessAbnormal2]: '简易注销',
  [DimensionLevel3Enums.BusinessAbnormal3]: '被列入经营异常名录',
  [DimensionLevel3Enums.BusinessAbnormal4]: '被列入非正常户',
  [DimensionLevel3Enums.BusinessAbnormal5]: '疑似停业歇业停产或被吊销证照',
  [DimensionLevel3Enums.BusinessAbnormal6]: '经营期限已过有效期',
  [DimensionLevel3Enums.BusinessAbnormal7]: '无统一社会信用代码',
  [DimensionLevel3Enums.BusinessAbnormal8]: '临近经营期限',
  [DimensionLevel2Enums.EstablishedTime]: '新成立企业',
  [DimensionLevel2Enums.LowCapital]: '注册资本过低',
  [DimensionLevel2Enums.CompanyShell]: '疑似空壳企业',
  [DimensionLevel2Enums.FakeSOES]: '假冒国企',
  [DimensionLevel2Enums.FraudList]: '涉诈高风险名单',
  [DimensionLevel3Enums.CancellationOfFiling]: '注销备案',
  [DimensionLevel2Enums.NoCapital]: '久无实缴',
};

export const ExcelFileName = {
  [DimensionLevel1Enums.Risk_InnerBlacklist]: '内部黑名单',
  [DimensionLevel1Enums.Risk_Legal]: '法律风险',
  [DimensionLevel1Enums.Risk_InterestConflict]: '潜在利益冲突',
  [DimensionLevel1Enums.Risk_BaseInfo]: '基础资质',
  [DimensionLevel1Enums.Risk_Blacklist]: '第三方黑名单',
  [DimensionLevel1Enums.Risk_OuterBlacklist]: '外部黑名单',
  [DimensionLevel1Enums.Risk_PartnerInvestigation]: '交叉重叠关系',
  [DimensionLevel1Enums.Risk_AdministrativeSupervision]: '行政监管风险',
  [DimensionLevel1Enums.Risk_OperateStability]: '经营稳定性风险',
  [DimensionLevel1Enums.Risk_NegativeNews]: '负面新闻',
};
