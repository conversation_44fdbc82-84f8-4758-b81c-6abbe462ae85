import { RoverUser } from 'libs/model/common';
import { FileParseResult } from 'libs/model/batch/po/parse/FileParseResult';
import xlsx from 'node-xlsx';
import { BadRequestException } from '@nestjs/common';
import { ExcelParserSettingPO } from 'libs/model/batch/po/parse/ExcelParserSettingPO';
import { isEqual, isNumber, isString } from 'lodash';
import { ParsedRecordBase } from 'libs/model/batch/po/parse/ParsedRecordBase';
import * as moment from 'moment';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { DATE_FORMAT } from 'libs/constants/common';
import { RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { getExceptionDescription } from '../../../libs/utils/diligence/diligence.utils';

export abstract class FileParserAbstract {
  protected constructor(protected readonly bundleService: RoverBundleService) {}

  abstract getParseSetting(): ExcelParserSettingPO;

  /**
   * 解析excel文件，取出空行并做最基本的校验(非空行)˝
   * @param currentUser
   * @param filePath
   */
  abstract parseFile(currentUser: RoverUser, filePath: string): Promise<FileParseResult>;

  abstract getFileLimit(currentUser: RoverUser): Promise<number>;

  /**
   * 数据校验
   * @param data
   */
  validateItems(items: ParsedRecordBase[]): Promise<FileParseResult> {
    return Promise.resolve({ succeedItems: items });
  }

  protected async parse(currentUser: RoverUser, filePath: string): Promise<any[][]> {
    let data: any[][] = [];
    const { explainRows, templateTitle, sheetName } = this.getParseSetting();
    const limit = await this.getFileLimit(currentUser);
    let rawData;
    try {
      rawData = xlsx.parse(filePath);
    } catch (error) {
      throw new BadRequestException(RoverExceptions.Import.FileError);
    }

    const sheet = rawData.find((sheet) => sheet.name == sheetName);
    if (!sheet) {
      throw new BadRequestException(RoverExceptions.Import.TemplateError);
    }

    if (!sheet?.data) {
      throw new BadRequestException(RoverExceptions.Import.TemplateError);
    }

    data = sheet.data;
    // 模板说明行数，需要被忽略
    /*
                导入企业
        注意事项：
        1.模版中的表头名称不可更改，表头行不能删除。
        2.其中“*”号标记的企业名称为必填项，必须保留。
        3.第一行内容为示例，可以修改或删除
        4.导入文件请勿超过 2MB。
                *企业名称
            */
    // 模板格式验证
    const title = data[explainRows - 1];
    //所有的半角转全角
    templateTitle.forEach((t) => t.replace(/[(]/g, '（').replace(/[)]/g, '）'));
    title.forEach((t) => t.replace(/[(]/g, '（').replace(/[)]/g, '）'));
    if (!isEqual(templateTitle, title)) {
      throw new BadRequestException(RoverExceptions.Import.TemplateError);
    }
    // 有效数据行数
    const validData = data.slice(explainRows).filter((row) => row.join('').trim());

    if (validData.length < 1) {
      throw new BadRequestException(RoverExceptions.Import.DataNotFount);
    }

    if (validData.length > limit) {
      const err = { ...RoverExceptions.Import.Limited };
      err.error = getExceptionDescription(err.error, limit);
      throw new BadRequestException(err);
    }
    return validData;
  }

  /**
   *
   * @param rowData
   * @param index
   * @param isCompanyName true-企业名称格式处理(去除名称中间空格，括号改成全角括号，中文逗号改为英文逗号)
   * @returns
   */
  protected parseRowString(rowData: any[], index: number, isCompanyName = false) {
    const data = rowData?.[index]
      ?.toString()
      .replace(/[\r\n\t]/g, '')
      .replace(/，/g, ',')
      .trim();
    if (data && isCompanyName) {
      const r = /^[A-Za-z\s.\-&'(),]+$/g;
      if (r.test(data)) {
        // 企业名称为英文字母、空格、点号、短横线、与号、单引号、圆括号、逗号，不做去除名称中间空格处理
        return data.replace(/[(]/g, '（').replace(/[)]/g, '）');
      }
      return data.replace(/[' ']/g, '').replace(/[(]/g, '（').replace(/[)]/g, '）');
    }
    return data;
  }

  protected parseRowDate(rowData: any[], index: number, format = DATE_FORMAT) {
    let data = rowData[index];
    if (!data || isNaN(Date.parse(data))) {
      return undefined;
    }
    if (isNumber(data)) {
      data = new Date(1900, 0, data - 1);
    } else if (isString(data)) {
      data = new Date(data);
    } else {
      return undefined;
    }

    return format ? moment(data).format(DATE_FORMAT) : moment(data);
  }
}
