import { FileParserAbstract } from './FileParserAbstract';
import { Injectable } from '@nestjs/common';
import { ExcelParserSettingPO } from 'libs/model/batch/po/parse/ExcelParserSettingPO';
import { RoverUser } from 'libs/model/common';
import { FileParseResult } from 'libs/model/batch/po/parse/FileParseResult';
import { BiddingDiligenceExcelRecord, ParseErrorItem } from 'libs/model/batch/po/parse/ParsedRecordBase';
import { FieldParseErrorTypeEnums } from 'libs/enums/batch/FieldParseErrorTypeEnums';
import { RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { compact, uniq } from 'lodash';
import { getBiddingCompanyCount, getTenderDiligencePaidCount } from '../../bidding/common/bidding.utils';
import { SettingsService } from '../../settings/settings.service';
import { SettingTypeEnums } from '../../../libs/model/settings/SettingTypeEnums';

@Injectable()
export class BiddingDiligenceFileParser extends FileParserAbstract {
  constructor(protected readonly bundleService: RoverBundleService, private readonly settingService: SettingsService) {
    super(bundleService);
  }

  async getFileLimit(currentUser: RoverUser): Promise<number> {
    return this.getParseSetting().limit;
  }

  async parseFile(currentUser: RoverUser, filePath: string): Promise<FileParseResult> {
    const success: BiddingDiligenceExcelRecord[] = [];
    const error: ParseErrorItem[] = [];
    const rows = await this.parse(currentUser, filePath);
    const repeatSet: Set<string> = new Set<string>();

    const tenderConfigEntity = await this.settingService.getOrgLatestSettings(currentUser.currentOrg, SettingTypeEnums.tender_risk);
    const maxCompanyCount = getBiddingCompanyCount(tenderConfigEntity);
    let paid = 0;
    rows.forEach((rowData) => {
      const projectName = this.parseRowString(rowData, 0);
      const projectNo = this.parseRowString(rowData, 1);
      const strCompanyNames = rowData?.[2]?.toString().replace(/，/g, ',').trim();
      const companyNames = uniq(compact(strCompanyNames?.split(/[\r\n\t,]/).map((x) => x?.replace(/[' ']/g, '').replace(/[(]/g, '（').replace(/[)]/g, '）'))));
      const data = Object.assign(new BiddingDiligenceExcelRecord(), {
        projectNo,
        projectName,
        companyNames,
      });

      const companyCount = data.companyNames?.length || 0;

      if (!data.projectNo || !data.projectName || !companyCount) {
        error.push({
          data: data,
          errorType: FieldParseErrorTypeEnums.Required,
          errorMsg: '项目名称、项目编号、排查企业不能为空',
        });
        return;
      }

      if (companyCount < 2) {
        error.push({
          data: data,
          errorType: FieldParseErrorTypeEnums.Failed_Valid,
          errorMsg: '排查企业数量不能少于2家',
        });
        return;
      } else if (companyCount > maxCompanyCount) {
        error.push({
          data: data,
          errorType: FieldParseErrorTypeEnums.Failed_Valid,
          errorMsg: `排查企业数量不能超过${maxCompanyCount}家`,
        });
        return;
      } else if (repeatSet.has(`${data.projectNo}-${data.projectName}`)) {
        // 有重复数据
        error.push({
          data,
          errorType: FieldParseErrorTypeEnums.Duplicated,
          errorMsg: '项目名称或项目编号重复',
        });
        return;
      } else {
        repeatSet.add(`${data.projectNo}-${data.projectName}`);
      }
      paid += getTenderDiligencePaidCount(companyCount);
      success.push(data);
    });
    return { succeedItems: success, failedItems: error, paid };
  }

  getParseSetting(): ExcelParserSettingPO {
    return {
      explainRows: 3,
      limit: 1000,
      sheetName: '批量招标排查-导入',
      templateTitle: ['项目名称', '项目编号', '排查企业'],
      exportExcelColumns: {
        resultDes: { header: '导入结果', width: 30 },
        projectName: { header: '项目名称', width: 50 },
        projectNo: { header: '项目编号', width: 30 },
        companyNames: { header: '排查企业', width: 50 },
      },
    };
  }
}
