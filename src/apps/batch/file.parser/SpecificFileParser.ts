import { FileParserAbstract } from './FileParserAbstract';
import { RoverUser } from '../../../libs/model/common';
import { ExcelParserSettingPO } from '../../../libs/model/batch/po/parse/ExcelParserSettingPO';
import { FileParseResult } from '../../../libs/model/batch/po/parse/FileParseResult';
import { Injectable } from '@nestjs/common';
import { BiddingDiligenceExcelRecord, ParseErrorItem } from '../../../libs/model/batch/po/parse/ParsedRecordBase';
import { getTenderDiligencePaidCount } from '../../bidding/common/bidding.utils';
import { compact, uniq } from 'lodash';
import { FieldParseErrorTypeEnums } from '../../../libs/enums/batch/FieldParseErrorTypeEnums';
import { SettingsService } from '../../settings/settings.service';
import { RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { getSpecificCompanyCount } from '../../specific/common/specific.utils';
import { SettingTypeEnums } from '../../../libs/model/settings/SettingTypeEnums';

@Injectable()
export class SpecificFileParser extends FileParserAbstract {
  constructor(private readonly settingService: SettingsService, protected readonly bundleService: RoverBundleService) {
    super(bundleService);
  }

  async getFileLimit(currentUser: RoverUser): Promise<number> {
    return this.getParseSetting().limit;
  }

  getParseSetting(): ExcelParserSettingPO {
    return {
      explainRows: 3,
      limit: 1000,
      sheetName: '批量特定利益关系排查-导入',
      templateTitle: ['项目名称', '项目编号', '排查企业'],
      exportExcelColumns: {
        resultDes: { header: '导入结果', width: 30 },
        projectName: { header: '项目名称', width: 50 },
        projectNo: { header: '项目编号', width: 30 },
        companyNames: { header: '排查企业', width: 50 },
      },
    };
  }

  async parseFile(currentUser: RoverUser, filePath: string): Promise<FileParseResult> {
    const success: BiddingDiligenceExcelRecord[] = [];
    const error: ParseErrorItem[] = [];
    const rows = await this.parse(currentUser, filePath);
    const repeatSet: Set<string> = new Set<string>();

    let paid = 0;
    for (const rowData of rows) {
      const projectName = this.parseRowString(rowData, 0);
      const projectNo = this.parseRowString(rowData, 1);
      const strCompanyNames = rowData?.[2]?.toString().replace(/，/g, ',').trim();
      const companyNames = uniq(compact(strCompanyNames?.split(/[\r\n\t,]/).map((x) => x?.replace(/[' ']/g, '').replace(/[(]/g, '（').replace(/[)]/g, '）'))));
      const data = Object.assign(new BiddingDiligenceExcelRecord(), {
        projectNo,
        projectName,
        companyNames,
      });

      const specificConfigEntity = await this.settingService.getOrgLatestSettings(currentUser.currentOrg, SettingTypeEnums.specific);
      const maxCompanyCount = getSpecificCompanyCount(specificConfigEntity);
      const companyCount = data.companyNames?.length || 0;

      if (!data.projectNo || !data.projectName || !companyCount) {
        error.push({
          data: data,
          errorType: FieldParseErrorTypeEnums.Required,
        });
        continue;
      }

      if (companyCount < 2) {
        error.push({
          data: data,
          errorType: FieldParseErrorTypeEnums.Failed_Valid,
          errorMsg: '排查企业数量不能少于2家',
        });
        continue;
      } else if (companyCount > maxCompanyCount) {
        error.push({
          data: data,
          errorType: FieldParseErrorTypeEnums.Failed_Valid,
          errorMsg: `排查企业数量不能超过${maxCompanyCount}家`,
        });
        continue;
      } else if (repeatSet.has(`${data.projectNo}-${data.projectName}`)) {
        // 有重复数据
        error.push({
          data,
          errorType: FieldParseErrorTypeEnums.Duplicated,
        });
        continue;
      } else {
        repeatSet.add(`${data.projectNo}-${data.projectName}`);
      }
      paid += getTenderDiligencePaidCount(companyCount);
      success.push(data);
    }
    return { succeedItems: success, failedItems: error, paid };
  }
}
