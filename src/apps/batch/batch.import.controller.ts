import {
  Body,
  Controller,
  Delete,
  ParseBoolPipe,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  UploadedFile,
  UseFilters,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiCookieAuth, ApiOkResponse, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { BatchService } from './service/batch.service';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { SearchBatchResponseItem } from 'libs/model/batch/response/SearchBatchResponseItem';
import { FileInterceptor } from '@nestjs/platform-express';
import { defaultFileUploadOptions } from 'libs/common/file/config';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { SearchBatchResultRequest } from 'libs/model/batch/request/SearchBatchResultRequest';
import { BatchDiligenceRequest } from 'libs/model/batch/po/parse/ParsedRecordBase';
import { FileSizeLimitExceptionFilter } from 'libs/exceptions/FileSizeLimitExceptionFilter';
import { SearchMatchCompanyRequest } from '../../libs/model/batch/request/SearchMatchCompanyRequest';
import { UpdateMatchCompanyRequest } from '../../libs/model/batch/request/UpdateMatchCompanyRequest';
import { BatchTypeEnums } from '../../libs/enums/batch/BatchTypeEnums';
import { BatchRemoveRequest } from '../../libs/model/batch/request/BatchRemoveRequest';
import { Cacheable, useIoRedisAdapter } from 'type-cacheable';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { createHash } from 'crypto';
import { keys, reduce } from 'lodash';
import { RoverSessionGuard } from '../../libs/guards/RoverSession.guard';
import { RoverRolesGuard } from '../../libs/guards/rover.roles.guard';

@Controller('batch/import')
@ApiTags('批量导入处理')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class BatchImportController {
  private readonly logger: Logger = QccLogger.getLogger(BatchImportController.name);

  constructor(private readonly batchService: BatchService, private readonly redisService: RedisService) {
    //@ts-ignore
    useIoRedisAdapter(this.redisService.getClient());
  }

  @Post('detail')
  @ApiOperation({ summary: '获取指定批量任务详情' })
  @ApiOkResponse({ type: SearchBatchResponseItem })
  @ApiTags('准入排查/批量-获取指定批量任务详情')
  @Cacheable({
    cacheKey: (args): string => {
      const body = args[1];
      const cacheKey = reduce(keys(body).sort(), (acc, key) => acc.concat([`${key}:${body[key]}`]), []);
      const hashKey = createHash('md5').update(cacheKey.join('-')).digest('hex');
      return `batch:import:detail:${hashKey}`;
    },
    ttlSeconds: 60 * 60 * 24 * 7, // 缓存7天
  })
  async getBatch(@Request() req, @Body() data: SearchBatchResultRequest) {
    return this.batchService.getBatch(req.user, data);
  }

  @Post('detail/company')
  @ApiOperation({ summary: '获取批量任务详情公司命中详情' })
  @ApiOkResponse({ type: SearchBatchResponseItem })
  @ApiTags('准入排查/批量-获取批量任务详情公司命中详情')
  async getCompanyDimensionDetail(@Request() req, @Body() data: SearchBatchResultRequest) {
    return this.batchService.getCompanyDimensionDetail(req.user.currentOrg, data);
  }

  @Post('diligence/excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '批量排查，上传excel文件，匹配公司' })
  async matchBatchDiligenceCompany(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.matchBatchDiligenceCompany(req.user, file.path, fileName || file.originalname);
  }

  @Post('diligence/excel/item/search')
  @ApiOperation({ summary: '分页查询上传excel文件匹配的公司' })
  async searchMatchCompany(@Request() req, @Body() body: SearchMatchCompanyRequest) {
    return this.batchService.searchMatchCompanyRequest(req.user, body);
  }

  @Delete('diligence/excel/item')
  @ApiOperation({ summary: '删除上传excel文件匹配的公司' })
  async deleteMatchCompany(@Request() req, @Body() data: BatchRemoveRequest) {
    return this.batchService.deleteMatchCompany(req.user, data);
  }

  @Post('diligence/excel/item')
  @ApiOperation({ summary: 'post 删除上传excel文件匹配的公司' })
  async deleteMatchCompany2(@Request() req, @Body() data: BatchRemoveRequest) {
    return this.batchService.deleteMatchCompany(req.user, data);
  }

  @Put('diligence/excel/item')
  @ApiOperation({ summary: '更新上传excel文件匹配的公司' })
  async updateMatchCompany(@Request() req, @Query('itemId', ParseIntPipe) itemId: number, @Body() body: UpdateMatchCompanyRequest) {
    return this.batchService.updateMatchCompany(req.user, itemId, body);
  }

  @Post('diligence/excel/execute')
  @ApiOperation({ summary: '批量排查，上传excel文件，执行排查' })
  async executeBatchDiligenceCompany(@Request() req, @Query('batchId', ParseIntPipe) batchId: number, @Query('settingId') settingId?: number) {
    return this.batchService.executeBatchDiligenceCompany(req.user, batchId, settingId);
  }

  @Post('diligence/data')
  @ApiOperation({ summary: '通过公司ID和名称创建 批量尽职调查任务' })
  @ApiTags('准入排查/批量')
  async createDiligenceCompanyIdJob(@Body() body: BatchDiligenceRequest, @Request() req) {
    const { settingId } = body;
    return this.batchService.createBatchDiligenceTask(req.user, body.data, BatchBusinessTypeEnums.Diligence_ID, BatchTypeEnums.Import, { settingId });
  }

  @Post('diligence/customer')
  @ApiOperation({ summary: '客商列表里选择公司ID创建 批量尽职调查任务' })
  @ApiTags('准入排查/批量')
  async createDiligenceCustomerIdJob(@Body() body: BatchDiligenceRequest, @Request() req) {
    const { settingId } = body;
    return this.batchService.createBatchDiligenceTask(req.user, body.data, BatchBusinessTypeEnums.Diligence_Customer, BatchTypeEnums.Import, { settingId });
  }

  @Post('customer/excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传excel文件并创建 客商导入任务' })
  @ApiTags('合作伙伴/批量导入')
  async createCustomerExcelJob(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.createBatchByFile(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.Customer_File);
  }

  @Post('customer/excel/parse')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传第三方excel文件，匹配公司' })
  async matchBatchCustomerCompany(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.matchBatchCompany(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.Customer_File);
  }

  @Post('customer/excel/item/search')
  @ApiOperation({ summary: '分页查询上传第三方excel文件匹配的公司' })
  async searchCustomerMatchCompany(@Request() req, @Body() body: SearchMatchCompanyRequest) {
    return this.batchService.searchMatchCompanyRequest(req.user, body);
  }

  @Post('customer/excel/item')
  @ApiOperation({ summary: 'post 删除上传第三方excel文件匹配的公司' })
  async deleteMatchCustomerCompany2(@Request() req, @Body() data: BatchRemoveRequest) {
    return this.batchService.deleteMatchCompany(req.user, data);
  }

  @Put('customer/excel/item')
  @ApiOperation({ summary: '更新上传第三方excel文件匹配的公司' })
  async updateMatchCustomerCompany(@Request() req, @Query('itemId', ParseIntPipe) itemId: number, @Body() body: UpdateMatchCompanyRequest) {
    return this.batchService.updateMatchCompany(req.user, itemId, body);
  }

  @Post('customer/excel/execute')
  @ApiOperation({ summary: '上传第三方excel文件，创建第三方导入任务' })
  async executeBatchCustomerCompany(@Request() req, @Query('batchId', ParseIntPipe) batchId: number, @Query('isUpdate', ParseBoolPipe) isUpdate: boolean) {
    return this.batchService.executeBatchImport(req.user, batchId, BatchBusinessTypeEnums.Customer_File, isUpdate);
  }

  @Post('person/excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传excel文件并创建 人员导入任务' })
  @ApiTags('人员管理/批量导入')
  async createPersonExcelJob(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.createBatchByFile(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.Person_File);
  }

  @Post('blacklist/inner/excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传excel文件并创建 黑名单（内部）导入任务' })
  @ApiTags('内部黑名单/批量导入')
  async createBlacklistExcelJob(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.createBatchByFile(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.InnerBlacklist_File);
  }

  @Post('blacklist/excel/parse')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传内部黑名单excel文件，匹配公司' })
  async matchBatchBlacklistCompany(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.matchBatchCompany(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.InnerBlacklist_File);
  }

  @Post('blacklist/excel/item/search')
  @ApiOperation({ summary: '分页查询内部黑名单excel文件匹配的公司' })
  async searchBlacklistMatchCompany(@Request() req, @Body() body: SearchMatchCompanyRequest) {
    return this.batchService.searchMatchCompanyRequest(req.user, body);
  }

  @Post('blacklist/excel/item')
  @ApiOperation({ summary: 'post 删除内部黑名单excel文件匹配的公司' })
  async deleteMatchBlacklistCompany2(@Request() req, @Body() data: BatchRemoveRequest) {
    return this.batchService.deleteMatchCompany(req.user, data);
  }

  @Put('blacklist/excel/item')
  @ApiOperation({ summary: '更新内部黑名单excel文件匹配的公司' })
  async updateMatchBlacklistCompany(@Request() req, @Query('itemId', ParseIntPipe) itemId: number, @Body() body: UpdateMatchCompanyRequest) {
    return this.batchService.updateMatchCompany(req.user, itemId, body);
  }

  @Post('blacklist/excel/execute')
  @ApiOperation({ summary: '创建内部黑名单导入任务' })
  async executeBatchBlacklistCompany(@Request() req, @Query('batchId', ParseIntPipe) batchId: number, @Query('isUpdate', ParseBoolPipe) isUpdate: boolean) {
    return this.batchService.executeBatchImport(req.user, batchId, BatchBusinessTypeEnums.InnerBlacklist_File, isUpdate);
  }

  @Post('monitor/excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传excel文件并创建 合作监控导入任务' })
  @ApiTags('合作监控导入任务')
  async createBatchMonitorJob(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.createBatchByFile(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.Monitor_File);
  }

  @Post('monitor/excel/parse')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传合作监控excel文件，匹配公司' })
  async matchBatchMonitorCompany(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.matchBatchCompany(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.Monitor_File);
  }

  @Post('monitor/excel/item/search')
  @ApiOperation({ summary: '分页查询上传合作监控excel文件匹配的公司' })
  async searchMonitorMatchCompany(@Request() req, @Body() body: SearchMatchCompanyRequest) {
    return this.batchService.searchMatchCompanyRequest(req.user, body);
  }

  @Post('monitor/excel/item')
  @ApiOperation({ summary: 'post 删除上传合作监控excel文件匹配的公司' })
  async deleteMatchMonitorCompany(@Request() req, @Body() data: BatchRemoveRequest) {
    return this.batchService.deleteMatchCompany(req.user, data);
  }

  @Put('monitor/excel/item')
  @ApiOperation({ summary: '更新上传合作监控excel文件匹配的公司' })
  async updateMatchMonitorCompany(@Request() req, @Query('itemId', ParseIntPipe) itemId: number, @Body() body: UpdateMatchCompanyRequest) {
    return this.batchService.updateMatchCompany(req.user, itemId, body);
  }

  @Post('monitor/excel/execute')
  @ApiOperation({ summary: '上传合作监控excel文件，创建合作监控导入任务' })
  async executeBatchMonitorCompany(@Request() req, @Query('batchId', ParseIntPipe) batchId: number, @Query('isUpdate', ParseBoolPipe) isUpdate: boolean) {
    return this.batchService.executeBatchImport(req.user, batchId, BatchBusinessTypeEnums.Monitor_File, isUpdate);
  }

  @Post('potential/data')
  @ApiOperation({ summary: '通过公司ID和名称创建 批量潜在利益排查任务' })
  @ApiTags('批量潜在利益排查')
  async createPotentialCompanyIdJob(@Body() body: BatchDiligenceRequest, @Request() req) {
    return this.batchService.createBatchDiligenceTask(req.user, body.data, BatchBusinessTypeEnums.Potential_Batch_Data, BatchTypeEnums.Import);
  }

  @Post('potential/excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '批量排查，上传excel文件，匹配公司' })
  async matchBatchPotentialCompany(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.matchBatchDiligenceCompany(req.user, file.path, fileName || file.originalname);
  }

  @Post('potential/excel/item/search')
  @ApiOperation({ summary: '分页查询上传excel文件匹配的公司' })
  async searchPotentialMatchCompany(@Request() req, @Body() body: SearchMatchCompanyRequest) {
    return this.batchService.searchMatchCompanyRequest(req.user, body);
  }

  @Post('potential/excel/item')
  @ApiOperation({ summary: 'post 删除上传excel文件匹配的公司' })
  async deleteMatchPotentialCompany2(@Request() req, @Body() data: BatchRemoveRequest) {
    return this.batchService.deleteMatchCompany(req.user, data);
  }

  @Put('potential/excel/item')
  @ApiOperation({ summary: '更新上传excel文件匹配的公司' })
  async updateMatchPotentialCompany(@Request() req, @Query('itemId', ParseIntPipe) itemId: number, @Body() body: UpdateMatchCompanyRequest) {
    return this.batchService.updateMatchCompany(req.user, itemId, body);
  }

  @Post('potential/excel/execute')
  @ApiOperation({ summary: '批量排查，上传excel文件，执行排查' })
  async executeBatchPotentialCompany(@Request() req, @Query('batchId', ParseIntPipe) batchMatchCompanyId: number, @Query('settingId') settingId?: number) {
    return this.batchService.executeBatchPotentialCompany(req.user, batchMatchCompanyId, settingId);
  }

  @Post('potential/customer')
  @ApiOperation({ summary: '客商列表里选择公司ID创建 批量潜在利益排查任务' })
  @ApiTags('潜在利益排查/批量')
  async createPotentialCustomerIdJob(@Body() body: BatchDiligenceRequest, @Request() req) {
    const { settingId } = body;
    return this.batchService.createBatchDiligenceTask(req.user, body.data, BatchBusinessTypeEnums.Potentail_Batch_Customer, BatchTypeEnums.Import, {
      settingId,
    });
  }
}
