import { Module } from '@nestjs/common';
import { Batch<PERSON>ontroller } from './batch.controller';
import { BatchService } from './service/batch.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { BatchMessageService } from './message.handler/batch.message.service';
import { BatchJobEntity } from 'libs/entities/BatchJobEntity';
import { BatchResultEntity } from 'libs/entities/BatchResultEntity';
import { BatchDiligenceEntity } from 'libs/entities/BatchDiligenceEntity';
import { DiligenceFileParser } from './file.parser/DiligenceFileParser';
import { CustomerImportFileParser } from './file.parser/CustomerImportFileParser';
import { DiligenceModule } from '../diligence/diligence.module';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { LabelEntity } from 'libs/entities/LabelEntity';
import { GroupsEntity } from 'libs/entities/GroupsEntity';
import { CustomerService } from '../customer/customer.service';
import { CustomerEntity } from 'libs/entities/CustomerEntity';
import { CustomerLabelEntity } from 'libs/entities/CustomerLabelEntity';
import { PersonService } from '../person/person.service';
import { BlacklistInnerService } from '../blacklist/blacklist.inner.service';
import { PersonEntity } from 'libs/entities/PersonEntity';
import { InnerBlacklistEntity } from 'libs/entities/InnerBlacklistEntity';
import { PersonImportFileParser } from './file.parser/PersonImportFileParser';
import { InnerBlacklistImportFileParser } from './file.parser/InnerBlacklistImportFileParser';
import { MessageService } from '../message/message.service';
import { MessageEntity } from 'libs/entities/MessageEntity';
import { BatchResultExportService } from './service/batch.result.export.service';
import { BatchMessageHandlerExport } from './message.handler/export/batch.message.handler.export';
import { SettingsModule } from '../settings/settings.module';
import { PersonOrgCompanyEntity } from 'libs/entities/PersonOrgCompanyEntity';
import { BatchImportController } from './batch.import.controller';
import { BatchExportController } from './batch.export.controller';
import { DataModule } from '../data/data.module';
import { BatchBundleService } from './service/batch.bundle.service';
import { CompanyEntity } from 'libs/entities/CompanyEntity';
import { InnerBlacklistLabelEntity } from 'libs/entities/InnerBlacklistLabelEntity';
import { OpLogModule } from '../oplog/oplog.module';
import { BatchMatchCompanyItemEntity } from 'libs/entities/BatchMatchCompanyItemEntity';
import { BatchMatchCompanyEntity } from 'libs/entities/BatchMatchCompanyEntity';
import { DiligenceSnapshotHelper } from '../diligence/snapshot/diligence.snapshot.helper';
import { TenderAlertModule } from '../tenderAlert/tenderAlert.module';
import { BiddingDiligenceFileParser } from './file.parser/BiddingDiligenceFileParser';
import { BiddingModule } from '../bidding/bidding.module';
import { DiligenceTenderHistoryEntity } from 'libs/entities/DiligenceTenderHistoryEntity';
import { MonitorRiskDynamicsV2Entity } from 'libs/entities/MonitorRiskDynamicsV2Entity';
import { MonitorGroupCompanyEntity } from 'libs/entities/MonitorGroupCompanyEntity';
import { MonitorGroupEntity } from 'libs/entities/MonitorGroupEntity';
import { MonitorImportFileParser } from './file.parser/MonitorImportFileParser';
import { UserEntity } from 'libs/entities/UserEntity';
import { BatchMessageHandlerDiligence } from './message.handler/diligence/batch.message.handler.diligence';
import { MonitorModule } from '../monitor/monitor.module';
import { BasicModule } from '../basic/basic.module';
import { CustomerExportProcessor } from './message.handler/export/processors/customer.export.processor';
import { DimensionDetailsExportProcessor } from './message.handler/export/processors/dimension.details.export.processor';
import { DiligenceExportProcessor } from './message.handler/export/processors/diligence.export.processor';
import { InnerBlacklistExportProcessor } from './message.handler/export/processors/inner.blacklist.export.processor';
import { PersonExportProcessor } from './message.handler/export/processors/person.export.processor';
import { RiskExportProcessor } from './message.handler/export/processors/risk.export.processor';
import { SentimentExportProcessor } from './message.handler/export/processors/sentiment.export.processor';
import { TenderExportProcessor } from './message.handler/export/processors/tender.export.processor';
import { ExportProcessorHelperService } from './message.handler/export/export.processor.helper.service';
import { BatchBiddingDiligenceProcessor } from './message.handler/tender/processor/batch.bidding.diligence.processor';
import { BatchDiligenceProcessor } from './message.handler/diligence/processors/batch.diligence.processor';
import { DiligenceCustomerAnalyzeProcessor } from './message.handler/diligence/processors/diligence.customer.analyze.processor';
import { BatchMonitorProcessor } from './message.handler/default/processors/batch.monitor.processor';
import { CustomerImportProcessor } from './message.handler/default/processors/customer.import.processor';
import { InnerBlacklistImportProcessor } from './message.handler/default/processors/inner.blacklist.import.processor';
import { PersonImportProcessor } from './message.handler/default/processors/person.import.processor';
import { BatchBaseHelper } from './service/helper/batch.base.helper';
import { BatchBiddingHelper } from './service/helper/batch.bidding.helper';
import { BatchCreatorHelperFile } from './service/helper/batch.creator.helper.file';
import { BatchCreatorHelperExport } from './service/helper/batch.creator.helper.export';
import { BatchCreatorHelperBase } from './service/helper/batch.creator.helper.base';
import { BatchDiligenceHelper } from './service/helper/batch.diligence.helper';
import { BatchMessageHandlerDefault } from './message.handler/default/batch.message.handler.default';
import { BatchMessageHandlerTender } from './message.handler/tender/batch.message.handler.tender';
import { BatchTenderDiligenceEntity } from 'libs/entities/BatchTenderDiligenceEntity';
import { CustomerDepartmentEntity } from 'libs/entities/CustomerDepartmentEntity';
import { InnerBlacklistDepartmentEntity } from 'libs/entities/InnerBlacklistDepartmentEntity';
import { DepartmentEntity } from 'libs/entities/DepartmentEntity';
import { BatchCreateHelperImport } from './service/helper/batch.create.helper.import';
import { BatchTenderDiligenceDetailEntity } from 'libs/entities/BatchTenderDiligenceDetailEntity';
import { BiddingDiligenceProcessor } from './message.handler/tender/processor/bidding.diligence.processor';
import { BatchBiddingController } from './batch.bidding.controller';
import { UserConfigurationModule } from '../user_configuration/user.configuration.module';
import { CompanySearchModule } from '../company/company-search.module';
import { ElementModule } from '../element/element.module';
import { SpecificExportProcessor } from './message.handler/export/processors/specific.export.processor';
import { SpecificModule } from '../specific/specific.module';
import { TenderDimensionExportProcessor } from './message.handler/export/processors/tender.dimension.export.processor';
import { BatchSpecificHelper } from './service/helper/batch.specific.helper';
import { SpecificFileParser } from './file.parser/SpecificFileParser';
import { BatchSpecificDiligenceDetailEntity } from '../../libs/entities/BatchSpecificDiligenceDetailEntity';
import { BatchSpecificDiligenceEntity } from '../../libs/entities/BatchSpecificDiligenceEntity';
import { BatchMessageHandlerSpecific } from './message.handler/specific/batch.message.handler.specific';
import { BatchSpecificDiligenceProcessor } from './message.handler/specific/processor/batch.specific.diligence.processor';
import { BatchSpecificController } from './batch.specific.controller';
import { SpecificDiligenceProcessor } from './message.handler/specific/processor/specific.diligence.processor';
import { NotificationService } from './common/notification/notification.service';
import { EmailNotificationProvider } from './common/notification/providers/email.notification';
import { BatchMessageHandlerPotential } from './message.handler/potential/batch.message.handler.potential';
import { PotentialDiligenceProcessor } from './message.handler/potential/processor/potential.diligence.processor';
import { PotentialModule } from '../potential/potential.module';
import { BundleConsumeDetailExportProcessor } from './message.handler/export/processors/bundle.consume.detail.export.processor';
import { BatchPotentialDiligenceDetailEntity } from '../../libs/entities/BatchPotentialDiligenceDetailEntity';
import { BatchPotentialDiligenceEntity } from '../../libs/entities/BatchPotentialDiligenceEntity';
import { BatchPotentialController } from './batch.potential.controller';
import { BatchPotentialHelper } from './service/helper/batch.potential.helper';
import { PotentialDiligenceEntity } from 'libs/entities/PotentialDiligenceEntity';

@Module({
  controllers: [BatchController, BatchImportController, BatchExportController, BatchBiddingController, BatchSpecificController, BatchPotentialController],
  exports: [BatchService, BatchMessageService, SpecificDiligenceProcessor, BatchBaseHelper],
  providers: [
    BatchService,
    BatchMessageService,
    BatchMessageHandlerDiligence,
    DiligenceFileParser,
    CustomerImportFileParser,
    PersonImportFileParser,
    InnerBlacklistImportFileParser,
    BiddingDiligenceFileParser,
    MonitorImportFileParser,
    CustomerService,
    PersonService,
    BlacklistInnerService,
    MessageService,
    BatchResultExportService,
    BatchMessageHandlerExport,
    BatchBundleService,
    DiligenceSnapshotHelper,
    CustomerExportProcessor,
    DimensionDetailsExportProcessor,
    DiligenceExportProcessor,
    InnerBlacklistExportProcessor,
    PersonExportProcessor,
    RiskExportProcessor,
    BundleConsumeDetailExportProcessor,
    SentimentExportProcessor,
    TenderExportProcessor,
    TenderDimensionExportProcessor,
    ExportProcessorHelperService,
    BatchBiddingDiligenceProcessor,
    BiddingDiligenceProcessor,
    BatchDiligenceProcessor,
    DiligenceCustomerAnalyzeProcessor,
    BatchMonitorProcessor,
    CustomerImportProcessor,
    InnerBlacklistImportProcessor,
    PersonImportProcessor,
    BatchBaseHelper,
    BatchBiddingHelper,
    BatchCreatorHelperFile,
    BatchCreatorHelperExport,
    BatchCreatorHelperBase,
    BatchDiligenceHelper,
    BatchMessageHandlerDefault,
    BatchMessageHandlerTender,
    BatchMessageHandlerDiligence,
    BatchMessageHandlerExport,
    BatchCreateHelperImport,
    SpecificExportProcessor,
    BatchSpecificHelper,
    SpecificFileParser,
    BatchMessageHandlerSpecific,
    BatchSpecificDiligenceProcessor,
    SpecificDiligenceProcessor,
    NotificationService,
    EmailNotificationProvider,
    BatchMessageHandlerPotential,
    PotentialDiligenceProcessor,
    BatchPotentialHelper,
  ],
  imports: [
    TypeOrmModule.forFeature([
      BatchEntity,
      BatchJobEntity,
      BatchResultEntity,
      BatchDiligenceEntity,
      BatchTenderDiligenceEntity,
      DiligenceHistoryEntity,
      LabelEntity,
      GroupsEntity,
      CustomerEntity,
      CustomerLabelEntity,
      PersonEntity,
      InnerBlacklistEntity,
      InnerBlacklistLabelEntity,
      MessageEntity,
      PersonOrgCompanyEntity,
      CompanyEntity,
      BatchMatchCompanyItemEntity,
      BatchMatchCompanyEntity,
      DiligenceTenderHistoryEntity,
      MonitorRiskDynamicsV2Entity,
      MonitorGroupCompanyEntity,
      MonitorGroupEntity,
      UserEntity,
      DepartmentEntity,
      CustomerDepartmentEntity,
      InnerBlacklistDepartmentEntity,
      BatchTenderDiligenceDetailEntity,
      BatchSpecificDiligenceDetailEntity,
      BatchSpecificDiligenceEntity,
      BatchPotentialDiligenceDetailEntity,
      BatchPotentialDiligenceEntity,
      PotentialDiligenceEntity,
    ]),
    DiligenceModule,
    SettingsModule,
    DataModule,
    OpLogModule,
    TenderAlertModule,
    BiddingModule,
    MonitorModule,
    BasicModule,
    UserConfigurationModule,
    CompanySearchModule,
    ElementModule,
    SpecificModule,
    PotentialModule,
  ],
})
export class BatchModule {}
