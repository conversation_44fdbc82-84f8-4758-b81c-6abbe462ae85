import { PaginationResponse } from '../../../libs/model/common';
import { UserEntity } from '../../../libs/entities/UserEntity';
import { ApiProperty } from '@nestjs/swagger';
import { DiligenceTenderHistoryEntity } from '../../../libs/entities/DiligenceTenderHistoryEntity';
import { SpecificInterestRecordEntity } from '../../../libs/entities/SpecificInterestRecordEntity';

/**
 * 批量招标排查列表返回模型
 */
export class BiddingDetailResponse extends PaginationResponse {
  @ApiProperty({ description: '列表' })
  data: DiligenceTenderHistoryEntity[];
  @ApiProperty({ description: '创建时间' })
  createDate: Date;
  @ApiProperty({ description: '创建人' })
  editor: UserEntity | null;
}

export class SpecificDetailResponse extends PaginationResponse {
  @ApiProperty({ description: '列表' })
  data: SpecificInterestRecordEntity[];
  @ApiProperty({ description: '创建时间' })
  createDate: Date;
  @ApiProperty({ description: '创建人' })
  editor: UserEntity | null;
}
