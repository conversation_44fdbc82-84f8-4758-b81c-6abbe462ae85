import { ApiPropertyOptional } from '@nestjs/swagger';
import { ConfigurationDiligenceAnalyze } from '../../../libs/model/settings/ConfigurationModel';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { DiligenceBiddingRequest } from '../../../libs/model/bidding/DiligenceBiddingRequest';

export class BatchInfoPO {
  @ApiPropertyOptional({ description: '手动执行 model settingId' })
  settingId?: number;

  @ApiPropertyOptional({ description: '是否是子批次' })
  isSub?: boolean;

  @ApiPropertyOptional({ description: '父批次ID' })
  parentBatchId?: number;

  @ApiPropertyOptional({ description: '子批次ID列表' })
  subBatchIds?: number[];

  type?: BatchBusinessTypeEnums;

  settingName?: string;

  fromSystem?: boolean;

  orgConfig?: ConfigurationDiligenceAnalyze;

  @ApiPropertyOptional({ description: '招标排查入参' })
  params?: DiligenceBiddingRequest;

  @ApiPropertyOptional({ description: '招标排查记录id' })
  diligenceId?: number;

  @ApiPropertyOptional({ description: '是否是更新(页面操作，选择更新，则覆盖数据库原有数据)' })
  isUpdate?: boolean = true;
}
