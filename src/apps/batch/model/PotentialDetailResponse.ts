import { PaginationResponse } from '../../../libs/model/common';
import { UserEntity } from '../../../libs/entities/UserEntity';
import { ApiProperty } from '@nestjs/swagger';
import { PotentialDiligenceEntity } from 'libs/entities/PotentialDiligenceEntity';

/**
 * 批量招标排查列表返回模型
 */
export class PotentialDetailResponse extends PaginationResponse {
  @ApiProperty({ description: '列表' })
  data: PotentialDiligenceEntity[];
  @ApiProperty({ description: '创建时间' })
  createDate: Date;
  @ApiProperty({ description: '创建人' })
  editor: UserEntity | null;
}
