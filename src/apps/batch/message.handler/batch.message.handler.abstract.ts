import { BatchBusinessMessageTitle, BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Not, Repository } from 'typeorm';
import { BatchJobEntity } from '../../../libs/entities/BatchJobEntity';
import { BatchResultEntity } from '../../../libs/entities/BatchResultEntity';
import { BatchStatusEnums } from '../../../libs/enums/batch/BatchStatusEnums';
import * as Bluebird from 'bluebird';
import { ContextManager, QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { BatchMessageProcessorAbstract } from './batch.message.processor.abstract';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { JobProcessCostPO } from '../../../libs/model/batch/po/JobProcessCostPO';
import { BatchJobMessagePO } from '../../../libs/model/batch/po/message/BatchJobMessagePO';
import Redlock, { Lock } from 'redlock';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { BatchMessageBase } from '../../../libs/model/batch/po/message/BatchMessageBase';
import { BatchResultExportService } from '../service/batch.result.export.service';
import { RoverBundleCounterType, RoverBundleEntityConfig, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { MessageBody } from '../../../libs/model/message/MessageBody';
import { MsgType } from '../../../libs/model/message/MessageRequest';
import { MessageService } from '../../message/message.service';
import { UserService } from '../../user/user.service';
import { BatchStatisticsBasePO } from '../../../libs/model/batch/po/BatchStatisticsBasePO';
import { BatchJobResultTypeEnums } from '../../../libs/enums/batch/BatchJobResultTypeEnums';
import { flatMap } from 'lodash';
import { BatchResultPO } from '../model/BatchResultPO';
import { BatchBaseHelper } from '../service/helper/batch.base.helper';
import { captureException } from '@sentry/node';
import { QueueService } from '../../../libs/config/queue.service';
import { PulsarError } from '@kezhaozhao/message-queue/dist/exceptions/PulsarError';
import { BatchProgressLock } from '../../../libs/common/distributed_lock/BatchProgressLock';
import { isRedlockAcquireLock } from '../../../libs/utils/redis.utils';
import { Time_Minutes_10 } from '../../../libs/constants/common';
import { NotificationService } from '../common/notification/notification.service';
import { retry } from '@kezhaozhao/qcc-utils';

@Injectable()
export abstract class BatchMessageHandlerAbstract {
  protected readonly logger: Logger = QccLogger.getLogger(BatchMessageHandlerAbstract.name);
  protected readonly allProcessor: BatchMessageProcessorAbstract[] = [];
  protected redlock: Redlock;
  @InjectRepository(BatchEntity) protected readonly batchRepo: Repository<BatchEntity>;
  @InjectRepository(BatchJobEntity) protected readonly batchJobRepo: Repository<BatchJobEntity>;
  @InjectRepository(BatchResultEntity) protected readonly batchResultRepo: Repository<BatchResultEntity>;

  @Inject()
  protected readonly resultExportService: BatchResultExportService;

  @Inject()
  public readonly messageService: MessageService;

  @Inject()
  protected readonly userService: UserService;

  @Inject()
  protected readonly bundleService: RoverBundleService;

  @Inject()
  protected readonly batchHelperService: BatchBaseHelper;

  @Inject()
  protected readonly notificationService: NotificationService;

  protected constructor(protected readonly queueService: QueueService, protected readonly redisService: RedisService) {
    this.redlock = new Redlock([this.redisService.getClient()], {
      retryCount: 2, // retry only 2 times then fails
    });
  }

  /**
   * 1. 先获取redlock ，防止消息重复消费
   * 2. 检查batch是否存在，是否已经失败
   * 3. 检查job是否存在或者已经开始处理
   * @param message
   * @private
   */
  protected async checkJobMessage(message: BatchJobMessagePO): Promise<Lock | null> {
    const { batchId, jobId } = message;
    try {
      const lock: Lock = await this.redlock.acquire([`batchId:${batchId}:jobId:${jobId}`], 5 * 1000);
      let exitFlag = false;
      let needRetry = false;
      const [job, batchEntity] = await Bluebird.all([this.batchJobRepo.findOne(jobId), this.batchRepo.findOne(batchId)]);
      if (!batchEntity || batchEntity.status == BatchStatusEnums.Error) {
        await this.batchJobRepo.update(jobId, {
          status: BatchStatusEnums.Error,
          comment: 'batch不存在或者已经失败',
          errorDate: new Date(),
        });
        exitFlag = true;
      } else if (batchEntity.status == BatchStatusEnums.Caneled) {
        await this.batchJobRepo.update(jobId, {
          status: BatchStatusEnums.Caneled,
          comment: 'batch已经中止',
          errorDate: new Date(),
        });
        return null;
      }
      if (!job) {
        this.logger.info(`processJobMessage warn: batchId=${batchId},jobId=${jobId} job 不存在...`);
        exitFlag = true;
      } else if (job.status == BatchStatusEnums.Processing) {
        this.logger.warn(`processJobMessage(duplicated): batchId=${batchId},jobId=${jobId},status=${job.status},startDate=${job.startDate}, will auto-retry`);
        captureException(new Error(`auto-retry-processJobMessage() batchId=${batchId},jobId=${jobId} , status=${job.status}`), {
          extra: { ...message },
        });
        exitFlag = true;
        needRetry = true; // true 这里只打印日志，然后会重新开始处理消息，一般消息可能在处理过程中节点重启了会造成消息是Processing状态中，需要自动重新执行
      } else if (job.status == BatchStatusEnums.Done || job.status == BatchStatusEnums.Error) {
        this.logger.warn(`processJobMessage warn: batchId=${batchId},jobId=${jobId} , status=${job.status} , job 已经是完成状态不会重新处理`);
        exitFlag = true;
        needRetry = false;
      }
      if (!exitFlag) {
        await this.batchJobRepo.update(jobId, { status: BatchStatusEnums.Processing, startDate: new Date() });
      } else if (needRetry) {
        const nowTime = new Date().getTime();
        const itemSize = job.jobInfo.itemSize;
        const resultEntities = await this.batchResultRepo.find({ jobId });
        if (
          (!resultEntities && nowTime - job.updateDate?.updateDate.getTime() > Time_Minutes_10) ||
          (itemSize > resultEntities?.length && nowTime - resultEntities?.[0]?.updateDate.getTime() > Time_Minutes_10)
        ) {
          // 更新了任务处理状态，但是 10 分钟内没有产生任务结果 or 产生了部分任务结果，但 10 分钟内没有全部处理完成
          // 更新状态为待处理，等待扫描重新进入队列处理
          await this.batchJobRepo.update(jobId, {
            status: BatchStatusEnums.Waiting,
            comment: `update status = 0, auto-retry at ${new Date()}`,
          });
          // 移除已生成的相关数据
          await this.batchResultRepo.delete({ jobId });
        }
      }
      await lock.release();
      return exitFlag ? null : lock;
    } catch (e) {
      if (isRedlockAcquireLock(e)) {
        this.logger.warn(`job has been process in 5 seconds, batchId=${batchId}, jobId=${jobId}`);
      } else {
        this.logger.error(`checkJobMessage error: batchId=${batchId},jobId=${jobId} error=${e}`);
        this.logger.error(e);
      }
      return null;
    }
  }

  async handleJobMessage(message1: BatchMessageBase): Promise<void | string> {
    const message = message1 as BatchJobMessagePO;
    const { batchId, businessType, jobId } = message;
    //1. 标记job 处理中
    try {
      this.logger.info(`processJobMessage begin: batchId=${batchId},jobId=${jobId},traceId=${ContextManager.current.traceId()},`);
      const lock = await this.checkJobMessage(message);
      if (!lock) {
        this.logger.warn(
          `processJobMessage fail: batchId=${batchId},jobId=${jobId}, 获取redis锁失败，可能是消息重复消费或者batch已经完成或者中止，job等检查失败`,
        );
        return;
      }
      const jobCost: JobProcessCostPO = new JobProcessCostPO();
      const jobStartTime = Date.now();
      // 1.判断当前关联的batch是否存在或者已经失败 2.判断当前job是否还在等待中, 防止重复处理
      const processor = this.getProperlyProcessor(businessType);
      const result = await processor.processJobMessage(message);
      const partial = { status: BatchStatusEnums.Done, endDate: new Date() };
      jobCost.jobTotalTime = Date.now() - jobStartTime;
      jobCost.extraCost = result;
      Object.assign(partial, { comment: JSON.stringify(jobCost) });
      await this.batchJobRepo.update(jobId, partial);
      this.logger.info(`processJobMessage finished: batchId=${batchId},jobId=${jobId}`);
    } catch (e) {
      await this.setJobError([jobId], null, batchId, businessType, e);
    }
  }

  getProperlyProcessor(businessType: BatchBusinessTypeEnums): BatchMessageProcessorAbstract {
    for (const processor of this.allProcessor) {
      if (processor.match(businessType)) {
        return processor;
      }
    }
    this.logger.error('no processor found for businessType: ' + businessType);
    throw new BadRequestException('no batch message message.handler processor found for businessType: ' + businessType);
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    const allTypes = [];
    for (const processor of this.allProcessor) {
      allTypes.push(...processor.getBusinessType());
    }
    return allTypes;
  }

  match(businessType: BatchBusinessTypeEnums): boolean {
    return this.getBusinessType()?.some((r) => r === businessType);
  }

  async onJobError(batchId: number, businessType: BatchBusinessTypeEnums, jobIds?: number[]) {
    return this.getProperlyProcessor(businessType)?.onJobError(jobIds, batchId, businessType);
  }

  async setJobError(jobIds: number[], passedComment: string, batchId: number, businessType: BatchBusinessTypeEnums, e?: any): Promise<void> {
    this.logger.error(`processJobMessage error: batchId=${batchId},jobId=${jobIds.join(',')}, passedComment= ${passedComment}, error=${e}`);
    //清除job 对应的result 以及 batchDiligence
    if (!jobIds || jobIds.length === 0) {
      return;
    }
    if (e) {
      this.logger.error(e);
    }
    try {
      const comment =
        passedComment ||
        (e?.response
          ? `${e.response.error}${e.response.internalMessage ? ',internalMsg:' + e.response.internalMessage : ''}${
              e.response?.errorExtraData?.httpRequest ? ',extraData:' + e.response?.errorExtraData?.httpRequest : ''
            }`.substr(0, 1000)
          : e?.message?.substr(0, 1000));

      const jobs = await this.batchJobRepo.find({ where: { jobId: In(jobIds) } });
      const batchResults: BatchResultPO[] = flatMap(jobs, (r) => {
        const items = r.jobInfo?.items || [];
        return items.map((item) => {
          return {
            jobId: r.jobId,
            batchId: r.batchId,
            resultType: BatchJobResultTypeEnums.FAILED_CODE,
            resultInfo: item,
            resultHashkey: item.recordHashkey,
            comment,
          };
        });
      });
      await Bluebird.all([
        // :TODO 客商、黑名单、人员 job失败时删除已导入部分
        // this.batchDiligenceRepo.delete({ jobId: In(jobIds) }),
        this.batchHelperService.handleJobResult(batchResults),
        this.batchJobRepo.update(
          { jobId: In(jobIds) },
          {
            status: BatchStatusEnums.Error,
            comment,
            errorDate: new Date(),
          },
        ),
        this.onJobError(batchId, businessType),
      ]);
    } catch (e) {
      this.logger.error(`setJobError error: batchId=${batchId},jobIds=${jobIds} error=${e}`);
      this.logger.error(e);
    }
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<void> {
    // do nothing
    if (!batchEntity) {
      return null;
    }
    const { businessType, batchId, orgId } = batchEntity;
    this.logger.info(`Batch Succesed! businessType: ${businessType} ,batchId: ${batchId} `);
    await this.getProperlyProcessor(businessType).onBatchSuccess(batchEntity);
    await this.refreshBatchStatus(batchEntity, true);
    await this.sendSysMessage(batchId, businessType);
    //只对风险年检发送邮件通知
    if (businessType === BatchBusinessTypeEnums.Diligence_Customer_Analyze) {
      // await this.sendEmailNotification(orgId, batchId);

      try {
        retry(async () => {
          await Bluebird.delay(5 * 60 * 1000);
          await this.sendEmailNotification(orgId, batchId);
        }, 3);
      } catch (error) {
        this.logger.error(`sendEmailNotification error: orgId=${orgId},batchId=${batchId} error=${error}`);
      }
    }
  }

  async onBatchError(batchEntity: BatchEntity, failedComment: string, generateResultFile?: boolean): Promise<void> {
    // const batchEntity = await this.batchRepo.findOne(batchId);
    if (!batchEntity) {
      return null;
    }
    const { batchId, businessType } = batchEntity;
    const notFinishedJobs = await this.batchJobRepo.find({
      where: {
        batchId,
        status: Not(In([BatchStatusEnums.Error, BatchStatusEnums.Done, BatchStatusEnums.Done_Timeout])),
      },
    });
    if (notFinishedJobs.length > 0) {
      this.logger.info(`Batch Failed!  batchId=${batchId},notFinishedJobs=${notFinishedJobs.length} , failedComment=${failedComment} `);
      await this.batchJobRepo.update(
        notFinishedJobs.map((r) => r.jobId),
        { status: BatchStatusEnums.Error, comment: failedComment, errorDate: new Date() },
      );
      const batchResults: BatchResultPO[] = flatMap(notFinishedJobs, (r) => {
        const items = r.jobInfo?.items || [];
        return items.map((item) => {
          return {
            jobId: r.jobId,
            batchId: r.batchId,
            resultType: BatchJobResultTypeEnums.FAILED_CODE,
            resultInfo: item,
            resultHashkey: item.recordHashkey,
            comment: failedComment,
          };
        });
      });
      await this.batchHelperService.handleJobResult(batchResults);
    }

    this.logger.info(`Batch Failed! businessType: ${businessType} ,batchId: ${batchId}, failedComment: ${failedComment} `);

    await this.getProperlyProcessor(businessType)?.onBatchError(batchEntity);
    await this.refreshBatchStatus(batchEntity, true, failedComment);
    await this.sendSysMessage(batchId, businessType);
  }

  /**
   * 这里需要每次使用batchId查询最新的batchEntity的状态
   * @param batchId
   * @param done
   * @param failedComment
   * @param isRetryBatch 是否是重试batch，如果是，需要根据最新的batch result记录重新计算statistics info
   */
  async refreshBatchStatusByEntity(batchId: number, done: boolean, failedComment?: string, isRetryBatch?: boolean) {
    const batchEntity = await this.batchRepo.findOne(batchId);
    if (!batchEntity) {
      return null;
    }
    const { businessType } = batchEntity;
    const withholdingCount = batchEntity.statisticsInfo.withholdingCount;
    // 标记batch 完成，统计最新的信息
    let statisticsInfo: BatchStatisticsBasePO = null;
    try {
      // 是否可以重试，指定的错误可以允许重试
      let canRetry = false;
      let partialUpdate: any = {};
      // 通过batch result 重新计算统计信息(单独维护独立字段的increase，在并发较大情况下容易造成数据库锁等待)
      // if (isRetryBatch) {
      statisticsInfo = Object.assign(new BatchStatisticsBasePO(), {
        recordCount: batchEntity.recordCount,
        withholdingCount,
      });
      // 有result记录的统计情况
      const rows = await this.batchResultRepo
        .createQueryBuilder('result')
        .select('result.resultType', 'resultType')
        .addSelect('COUNT(result.resultId)', 'count')
        .groupBy('result.resultType')
        .where('result.batchId = :batchId', { batchId })
        .getRawMany();

      statisticsInfo.paidCount = 0;
      rows.forEach((r) => {
        const c = parseInt(r.count) || 0;
        switch (r.resultType) {
          // 执行成功-忽略部分错误
          case BatchJobResultTypeEnums.SUCCEED_IGNORE:
          // 执行成功-付费
          case BatchJobResultTypeEnums.SUCCEED_PAID: {
            statisticsInfo.paidCount += c;
            statisticsInfo.successCount += c;
            break;
          }
          // 执行成功-未付费
          case BatchJobResultTypeEnums.SUCCEED_UNPAID: {
            statisticsInfo.successCount += c;
            break;
          }
          // 数据重复
          case BatchJobResultTypeEnums.SUCCEED_DUPLICATED: {
            statisticsInfo.duplicatedCount = c;
            break;
          }
          //更新数量
          case BatchJobResultTypeEnums.UPDATE_DUPLICATED: {
            statisticsInfo.updatedCount += c;
            break;
          }
          case BatchJobResultTypeEnums.SUCCEED_UPDATE_IGNORE: {
            //忽略更新不计入统计识别
            statisticsInfo.paidCount += 0;
            statisticsInfo.successCount += 0;
            break;
          }
          // 执行失败 超时
          case BatchJobResultTypeEnums.OVER_TIME:
          // 执行失败 超出套餐额度
          case BatchJobResultTypeEnums.FAILED_BUNDLE_LIMITED:
          // 执行失败 数据校验失败
          case BatchJobResultTypeEnums.FAILED_VERIFICATION:
          // 执行失败 （代码执行过程中失败）
          case BatchJobResultTypeEnums.FAILED_CODE:
            // 可以重试的失败
            canRetry = true;
            statisticsInfo.errorCount += c;
            break;
          // 不支持的企业类型
          case BatchJobResultTypeEnums.FAILED_UNSUPPORTED:
          // 解析时候发现的错误数据
          case BatchJobResultTypeEnums.PARSER_ERROR:
          // 执行失败 未识别或者格式错误的数据
          case BatchJobResultTypeEnums.FAILED_UNMATCHED: {
            statisticsInfo.errorCount += c;
            break;
          }
        }
      });

      if (batchEntity.businessType == BatchBusinessTypeEnums.Bidding_Diligence_File && statisticsInfo.paidCount > 0) {
        // 批量招标排查可否额度跟公司数量相关
        const batchResults = await this.batchResultRepo.find({
          batchId,
          resultType: In([BatchJobResultTypeEnums.SUCCEED_IGNORE, BatchJobResultTypeEnums.SUCCEED_PAID]),
        });
        if (batchResults.length) {
          const paidCount = batchResults
            .map((r) => r.resultInfo)
            .reduce((acc, cur) => {
              return acc + Math.ceil(cur['companyNames'].length / 20);
            }, 0);
          statisticsInfo.paidCount = paidCount;
        }
      }

      statisticsInfo.successCount = Math.min(statisticsInfo.recordCount, statisticsInfo.successCount);
      //如果是在重试batch，同时刷新 statisticsInfo json 字段以及单独的字段
      partialUpdate = { statisticsInfo, ...statisticsInfo };
      // } else {
      //   const canRetryCount = await this.batchResultRepo.count({
      //     where: {
      //       batchId,
      //       resultType: In([BatchJobResultTypeEnums.FAILED_BUNDLE_LIMITED, BatchJobResultTypeEnums.FAILED_VERIFICATION, BatchJobResultTypeEnums.FAILED_CODE]),
      //     },
      //   });
      //   statisticsInfo = Object.assign(
      //     new BatchStatisticsBasePO(),
      //     pick(batchEntity, [
      //       'recordCount',
      //       'withholdingRecordCount',
      //       'withholdingCount',
      //       'successCount',
      //       'errorCount',
      //       'paidCount',
      //       'duplicatedCount',
      //       'updatedCount',
      //     ]),
      //   );
      //   //如果是普通的状态刷新，只刷新 statisticsInfo json 字段
      //   canRetry = canRetryCount > 0;
      //   partialUpdate = { statisticsInfo };
      // }

      if (canRetry && !batchEntity?.canRetry) {
        partialUpdate.canRetry = 1;
      }
      if (done) {
        partialUpdate.endDate = new Date();
        if (batchEntity.status !== BatchStatusEnums.Caneled) {
          partialUpdate.status = BatchStatusEnums.Done;
          if (failedComment) {
            partialUpdate.comment = failedComment;
            partialUpdate.status = BatchStatusEnums.Error;
          }
        }
        //TODO 判断是否需要调用以下方法
        const url = await this.resultExportService.generateResultFile(batchId, businessType);
        partialUpdate.resultFile = url;
      }

      this.logger.info(`refreshBatchStatus batchId=${batchId} partialUpdate=${JSON.stringify(partialUpdate)}`);
      await this.batchRepo.update(batchId, partialUpdate);
      if (done) {
        // 归还预扣排查额度
        await this.returnDiligenceBundleCount(batchId);
        // 删除batch process redis锁
        // await this.redisService.getClient()?.del(getBatchProcessLockKey(batchId));
        await new BatchProgressLock(this.redisService.getClient(), batchId).release();
        await Bluebird.delay(Math.random() * 1000);
      }
    } catch (error) {
      this.logger.error(`refreshBatchStatus update batch err batchId=${batchId} , err: ${error}`);
      this.logger.error(error);
      captureException(Object.assign(error, { message: `refreshBatchStatus error: ${error.message}` }), {
        extra: {
          description: `refreshBatchStatus update batch err batchId=${batchId} , err: ${error}}`,
          batchId: batchId,
        },
      });
    }
  }

  /**
   * 刷新批量任务的状态以及统计信息
   */
  async refreshBatchStatus(batchEntity: BatchEntity, done: boolean, failedComment?: string) {
    // const batchEntity = await this.batchRepo.findOne(batchId);
    if (!batchEntity) {
      return null;
    }
    const { batchId } = batchEntity;
    this.logger.info(`refreshBatchStatus batchId=${batchId} , done=${done} `);
    return this.refreshBatchStatusByEntity(batchId, done, failedComment);
  }

  public async sendSysMessage(batchId: number, businessType: BatchBusinessTypeEnums) {
    const finishedBatch = await this.batchRepo.findOne(batchId);
    const statisticsInfo = finishedBatch.statisticsInfo;
    const msgType = MsgType.TaskMsg;
    const title = BatchBusinessMessageTitle[businessType];
    const url = `/tasklist/import-task-list?batchId=${batchId}`;

    let content = `您上传的 <b>${finishedBatch.fileName || 0}</b> 文件任务已经完成，导入成功 <em class="success">${
      statisticsInfo?.successCount || 0
    }</em> 条，导入失败 <em class="failed">${statisticsInfo?.errorCount || 0}</em> 条，下载文件查看导入结果`;

    // 全量风险排查巡检文案设置
    if (businessType === BatchBusinessTypeEnums.Diligence_Customer_Analyze) {
      content = `您启动的 <b>${finishedBatch.fileName || 0}</b> 任务已经完成，成功执行 <em class="success">${
        statisticsInfo?.successCount || 0
      }</em> 条，错误 <em class="failed">${statisticsInfo?.errorCount || 0}</em> 条，下载文件查看巡检结果`;
    }

    const messageBody: MessageBody = {
      msgType,
      title,
      content,
      url,
      userId: finishedBatch.creatorId,
      objectId: finishedBatch.batchId + '',
    };
    if (BatchBusinessMessageTitle[businessType] && finishedBatch) {
      switch (businessType) {
        case BatchBusinessTypeEnums.Potentail_Batch_Customer:
        case BatchBusinessTypeEnums.Potential_Batch_Excel: // 批量潜在利益风险排查-excel
        case BatchBusinessTypeEnums.Potential_Batch_Data: // 批量排查-上传文件
          messageBody.url = `/supplier/potential-investigation/batch-detail?batchId=${batchId}`;
          break;
        case BatchBusinessTypeEnums.Diligence_File: // 批量排查-从合作伙伴中选择
        case BatchBusinessTypeEnums.Diligence_Customer: // 批量排查-文本输入
        case BatchBusinessTypeEnums.Diligence_ID: //批量排查-输入框手动填写
          messageBody.url = `/supplier/batch-investigation/detail/${batchId}`;
          break;
        case BatchBusinessTypeEnums.Diligence_Customer_Analyze: //全量排查巡检
          //messageBody.url = `/supplier/batch-investigation/detail/${batchId}?searchKey=${finishedBatch.fileName}`;
          messageBody.url = `/supplier/third-party/annual-review/record/${batchId}`;
          break;
        case BatchBusinessTypeEnums.Bidding_Diligence_File:
          messageBody.url = `/supplier/bidding-investigation/batch-detail?batchId=${batchId}`;
          break;
        case BatchBusinessTypeEnums.Specific_Diligence_File:
          messageBody.url = `/supplier/interest-investigation/batch-detail?batchId=${batchId}`;
          break;
        default:
          break;
      }
      await this.messageService.addMessage(messageBody);
    }
  }

  customMessageBody(message: any): Promise<any> {
    return Promise.resolve(message);
  }

  public async returnDiligenceBundleCount(batchId: number) {
    const batchEntity = await this.batchRepo.findOne(batchId);
    if (!batchEntity) {
      return null;
    }
    switch (batchEntity.businessType) {
      case BatchBusinessTypeEnums.Diligence_File:
      case BatchBusinessTypeEnums.Diligence_ID:
      case BatchBusinessTypeEnums.Diligence_Customer:
      case BatchBusinessTypeEnums.Diligence_Customer_Analyze: {
        const user = await this.userService.getRoverUser(batchEntity.creatorId, batchEntity.orgId);
        const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(user);

        // 如果预扣的排查公司额度 > 实际消耗的公司额度 ,退回多扣的额度
        const returnCount = batchEntity.statisticsInfo.withholdingCount - batchEntity.statisticsInfo.paidCount;
        this.logger.info('returnCount', returnCount);
        this.logger.info(batchEntity.statisticsInfo);
        if (returnCount > 0) {
          this.logger.info(`---即将退回 ${returnCount} 次排查公司额度`);
          if (userBundle[RoverBundleCounterType.DiligenceCompanyQuantity].value !== -1) {
            const companyCounter = await this.bundleService.getBundleCounter(user, RoverBundleCounterType.DiligenceCompanyQuantity);
            // await companyCounter.clear();
            await companyCounter.decrease(returnCount);
          }
          // if (userBundle[RoverBundleCounterType.DiligenceHistoryQuantity].value !== -1) {
          //   // 说明开启了 按排查次数数量 计费模式,
          //   const historyCounter = await this.bundleService.getOrgBundleCounter(user, RoverBundleCounterType.DiligenceHistoryQuantity);
          //   await historyCounter.decrease(returnCount);
          // }
        }

        // 如果预扣的排查次数额度 > 实际执行的排查次数 ,退回多扣的额度
        const returnRecordCount = batchEntity.statisticsInfo.withholdingRecordCount - batchEntity.statisticsInfo.successCount;
        if (returnRecordCount > 0) {
          if (userBundle[RoverBundleCounterType.DiligenceHistoryQuantity].value !== -1) {
            // 说明开启了 按排查次数数量 计费模式,
            const historyCounter = await this.bundleService.getOrgBundleCounter(user, RoverBundleCounterType.DiligenceHistoryQuantity);
            await historyCounter.decrease(returnCount);
          }
          // 每日尽调上限校验
          const dailyCounter = await this.bundleService.getOrgLimitationCounter(user, RoverBundleLimitationType.DiligenceDailyQuantity);
          await dailyCounter.decrease(returnCount);
        }
      }
    }
  }

  abstract getTargetQueue(businessType?: BatchBusinessTypeEnums): RabbitMQ;

  async onPulsarQueueError(messageData: BatchMessageBase, error) {
    const { batchId, businessType } = messageData;
    const queue = this.getTargetQueue(businessType);
    this.logger.error(`onPulsarQueueError(): queue=${queue.queueName},batchId=${batchId} ,error=${error.message}, messageData=${JSON.stringify(messageData)}`);
    this.logger.error(error);
    captureException(new PulsarError(`onPulsarQueueError(): queue=${queue.queueName} ,batchId=${batchId} , error:${error?.message}`, error), {
      extra: {
        messageData,
      },
    });
  }

  //发送邮件通知
  async sendEmailNotification(orgId: number, batchId: number) {
    await this.notificationService.sendNotification(orgId, batchId);
  }
}
