import { DefaultProcessorBase } from '../default.processor.base';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { PersonImportExcelRecord } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { GroupType } from '../../../../../libs/model/element/CreateGroupModel';
import { RelationshipConst } from '../../../../../libs/model/batch/po/RelationshipConst';
import { PersonRelationshipEnums } from '../../../../../libs/enums/person/PersonRelationshipEnums';
import { InjectRepository } from '@nestjs/typeorm';
import { GroupsEntity } from '../../../../../libs/entities/GroupsEntity';
import { Not, Repository } from 'typeorm';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverUser } from '../../../../../libs/model/common';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { trim } from 'lodash';
import { CreatePersonModel } from '../../../../../libs/model/person/CreatePersonModel';
import { BatchStatusEnums } from '../../../../../libs/enums/batch/BatchStatusEnums';
import { validate } from 'class-validator';
import { processBatchJobFailed } from '../../../../../libs/utils/utils.bundle';
import { PersonService } from '../../../../person/person.service';
import { PersonEntity } from '../../../../../libs/entities/PersonEntity';
import { CompanySearchService } from '../../../../company/company-search.service';
import * as Bluebird from 'bluebird';
import { RoverBundleCounterType } from '@kezhaozhao/saas-bundle-service';
import { BatchBundleService } from '../../../service/batch.bundle.service';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchResultPO } from '../../../model/BatchResultPO';

export class PersonImportProcessor extends DefaultProcessorBase {
  protected readonly logger = QccLogger.getLogger(PersonImportProcessor.name);

  constructor(
    @InjectRepository(GroupsEntity) private readonly groupsRepo: Repository<GroupsEntity>,
    private readonly personService: PersonService,
    @InjectRepository(PersonEntity) private readonly personRepo: Repository<PersonEntity>,
    private readonly companyService: CompanySearchService,
    private readonly batchBundleService: BatchBundleService,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Person_File];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, orgId, operatorId, jobId } = message;
    this.logger.info(`processJobMessage batchCreatePerson: batchId=${batchId},jobId=${jobId},items=${JSON.stringify(items)}`);
    const currentUser = await this.userService.getRoverUser(operatorId, orgId);
    //获取所有分组
    const data: PersonImportExcelRecord[] = items;
    if (data?.length) {
      //分组匹配校验
      const groupKV = {};
      const groupsEntities = await this.groupsRepo.find({ groupType: GroupType.PersonGroup, orgId });
      groupsEntities.forEach((group) => (groupKV[group.name] = group.groupId));
      const selfData = data.filter((d) => d.relationship === RelationshipConst[PersonRelationshipEnums.self]);
      await this.savePersons(selfData, message, groupKV, currentUser);
      const relatives = data.filter((d) => d.relationship !== RelationshipConst[PersonRelationshipEnums.self]);
      await this.savePersons(relatives, message, groupKV, currentUser);
    }
    this.logger.info(`processJobMessage batchCreatePerson Finished: batchId=${batchId},jobId=${jobId} `);
  }

  private async savePersons(data: PersonImportExcelRecord[], message: BatchJobMessagePO, groupKV: any, currentUser: RoverUser) {
    const { batchId, orgId, operatorId, jobId } = message;
    //const personRelationMap = groupBy(data, (e) => e.personNo);
    const names: string[] = data.map((d) => d.companyName).filter((e) => e);
    const companyInfos = [];
    if (names?.length) {
      const { matchedCompanyInfos } = await this.companyService.matchCompanyInfo(names, [
        'id',
        'name',
        'creditcode',
        'regno',
        'province',
        'areacode',
        'address',
        'industry',
        'subind',
        'econkindcode',
        'registcapi',
        'startdatecode',
        'registcapiamount',
        'status',
      ]);
      Array.prototype.push.apply(companyInfos, matchedCompanyInfos);
    }
    await Bluebird.map(
      data,
      async (currentPerson) => {
        //const selfPerson = personRelationMap[currentPerson.personNo]?.find((p) => p.relationship === RelationshipConst[PersonRelationshipEnums.self]);
        const batchResult: BatchResultPO = {
          batchId,
          jobId,
          resultType: BatchJobResultTypeEnums.SUCCEED_PAID,
          resultInfo: currentPerson,
          resultHashkey: currentPerson.recordHashkey,
        };
        const dbPerson = await this.personRepo.findOne({ orgId, personNo: trim(currentPerson.personNo) });
        let relationPersonId = -1;
        if (dbPerson) {
          if (currentPerson.relationship !== RelationshipConst[PersonRelationshipEnums.self]) {
            //非本人，表示为亲属类型人员，
            if (dbPerson) {
              relationPersonId = dbPerson.id;
            }
          }
        } else if (currentPerson.relationship !== RelationshipConst[PersonRelationshipEnums.self]) {
          //该personNo查不到数据且非本人数据时
          batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
          batchResult.comment = '未找到关联人员';
          batchResult.resultInfo = currentPerson;
          await this.batchHelperService.handleJobResult([batchResult]);
          return;
        }
        let companyId: string;
        let keyNo: string;
        if (currentPerson.companyName) {
          //1.先根据人员和公司名称查询公司companyId
          const company = companyInfos.find((i) => this.batchHelperService.matchCompany(currentPerson.companyName, i));
          //2.匹配到公司列表了，通过人员姓名和公司keyNo获取人员keyNo
          if (company) {
            const companyKeyNo = company.id;
            const personList = await this.personService.matchCompanyPerson(currentPerson.name, companyKeyNo);
            if (personList?.length) {
              companyId = companyKeyNo;
              keyNo = personList[0].keyNo;
              currentPerson.companyName = company.name;
            }
          }
        }
        const personModel = Object.assign(new CreatePersonModel(), {
          orgId,
          createBy: operatorId,
          batchId: batchId,
          status: BatchStatusEnums.Processing, // 1: 处理中， 2: 处理完成
          name: currentPerson.name,
          personNo:
            currentPerson.relationship === RelationshipConst[PersonRelationshipEnums.self]
              ? currentPerson.personNo
              : `${currentPerson.personNo}_${currentPerson.relationship}_${currentPerson.name}`,
          birthDay: currentPerson.birthDay,
          cardId: currentPerson.cardId?.trim() ? currentPerson.cardId.trim() : null,
          phone: currentPerson.phone,
          email: currentPerson?.email,
          relationship: currentPerson?.relationship,
          relationPersonId,
          companyId,
          keyNo,
          companyName: companyId ? currentPerson.companyName : null,
        });
        batchResult.resultInfo = currentPerson;

        //获取分组id
        if (currentPerson.groupName) {
          personModel.groupId = groupKV[currentPerson.groupName];
        }
        //获取地区
        if (currentPerson.birthPlace) {
          const birthPlaceList = currentPerson.birthPlace.split('/');
          if (birthPlaceList?.length > 0) {
            personModel.province = birthPlaceList[0] || '';
            personModel.city = birthPlaceList[1] || '';
            personModel.district = birthPlaceList[2] || '';
          }
        }

        // 保存人员信息
        try {
          const err = await validate(personModel);
          if (err?.length) {
            if (err[0].constraints?.matches) {
              batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
              batchResult.comment = err[0].constraints?.matches;
            } else {
              batchResult.resultType = BatchJobResultTypeEnums.FAILED_UNMATCHED;
              batchResult.comment = JSON.stringify(err);
            }
          } else {
            await this.personService.create(currentUser, personModel, true);
          }
        } catch (error) {
          processBatchJobFailed(error, batchResult);
          if (batchResult.resultType === BatchJobResultTypeEnums.UPDATE_DUPLICATED) {
            //重复，更新数据
            try {
              const duplicatedPerson = await this.personService.updateFromExcel(currentUser, personModel);
              if (!duplicatedPerson) {
                batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
                batchResult.comment = '该员工编号已存在';
              }
            } catch (ue) {
              batchResult.resultType = BatchJobResultTypeEnums.FAILED_CODE;
              this.logger.error(`update inner blacklist error: ${ue} userId=${currentUser.userId}, personModel=${JSON.stringify(personModel)}`);
            }
          }
          this.logger.error(
            `processJobMessage batchCreatePerson createPerson error: ${error} userId=${currentUser.userId}, personModel=${JSON.stringify(
              personModel,
            )},message=${message}`,
          );
        }

        // 记录处理结果
        await this.batchHelperService.handleJobResult([batchResult]);
      },
      { concurrency: 1 },
    );
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    const deleteResult = await this.personRepo.delete({ batchId, status: Not(BatchStatusEnums.Done) });
    if (deleteResult) {
      await this.batchBundleService.handleBatchCounterEvent(batchId, RoverBundleCounterType.PersonQuantity, -deleteResult.affected);
    }
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return this.personRepo.update(
      {
        batchId: batchEntity.batchId,
        status: BatchStatusEnums.Processing,
      },
      { status: BatchStatusEnums.Done },
    );
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
