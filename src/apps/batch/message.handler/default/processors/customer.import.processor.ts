import { DefaultProcessorBase } from '../default.processor.base';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { CustomerImportExcelRecord } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { In, Not, Repository } from 'typeorm';
import { flatten } from 'lodash';
import { GroupType } from '../../../../../libs/model/element/CreateGroupModel';
import { CreateCustomerModel } from '../../../../../libs/model/customer/CreateCustomerModel';
import { BatchStatusEnums } from '../../../../../libs/enums/batch/BatchStatusEnums';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { validate } from 'class-validator';
import { processBatchJobFailed } from '../../../../../libs/utils/utils.bundle';
import { StreamTableEnums } from '../../../../../libs/enums/data/StreamTableEnums';
import { StreamOperationEnum } from '../../../../../libs/enums/data/StreamOperationEnum';
import { CompanySearchService } from '../../../../company/company-search.service';
import { InjectRepository } from '@nestjs/typeorm';
import { RoverGraphService } from '../../../../data/source/rover.graph.service';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { GroupsEntity } from '../../../../../libs/entities/GroupsEntity';
import { LabelEntity } from '../../../../../libs/entities/LabelEntity';
import { CustomerService } from '../../../../customer/customer.service';
import * as Bluebird from 'bluebird';
import { RoverBundleCounterType } from '@kezhaozhao/saas-bundle-service';
import { CustomerEntity } from '../../../../../libs/entities/CustomerEntity';
import { BatchBundleService } from '../../../service/batch.bundle.service';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchResultPO } from '../../../model/BatchResultPO';
import { allowType } from '../../../../../libs/constants/common';

export class CustomerImportProcessor extends DefaultProcessorBase {
  protected readonly logger: Logger = QccLogger.getLogger(CustomerImportProcessor.name);

  constructor(
    private readonly companyService: CompanySearchService,
    private readonly roverGraphService: RoverGraphService,
    @InjectRepository(GroupsEntity) private readonly groupsRepo: Repository<GroupsEntity>,
    @InjectRepository(LabelEntity) private readonly labelRepo: Repository<LabelEntity>,
    private readonly customerService: CustomerService,
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
    private readonly batchBundleService: BatchBundleService,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Customer_File];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, jobId, orgId, operatorId, isUpdate } = message;
    this.logger.info(`processJobMessage batchCreateCustomer batchId: ${batchId}, jobId: ${jobId} , items: ${JSON.stringify(items)}`);
    const currentUser = await this.userService.getRoverUser(operatorId, orgId);
    const data: CustomerImportExcelRecord[] = items;
    // 通过name字段匹配公司，匹配不上的，标记错误
    const names: string[] = data.map((d) => d.companyName);
    const { matchedCompanyInfos, unmatchedNames, matchedNames, unsupported } = await this.companyService.matchCompanyInfo(
      names,
      [
        'id',
        'name',
        'creditcode',
        'regno',
        'province',
        'areacode',
        'address',
        'industry',
        'subind',
        'econkindcode',
        'registcapi',
        'startdatecode',
        'registcapiamount',
        'status',
      ],
      allowType,
      true,
    );
    // 对匹配到的公司数据进行处理，保存客商列表
    if (matchedCompanyInfos?.length > 0) {
      //生成标签KV
      const labels: string[][] = data.map((d) => d.label);
      const labelKV = {};
      //TODO labelName 如果不存在，是否需要自动创建label
      if (labels && labels?.length > 0) {
        const labelEntities = await this.labelRepo.find({ name: In(flatten(labels)), orgId, labelType: 1 });
        labelEntities.forEach((label) => (labelKV[label.name] = label.labelId));
      }
      //生成分组KV
      const groupKV = {};
      const groups: string[] = data.map((d) => d.group);
      if (groups?.length > 0) {
        const groupsEntities = await this.groupsRepo.find({
          name: In(groups),
          groupType: GroupType.CustomerGroup,
          orgId,
        });
        groupsEntities.forEach((group) => (groupKV[group.name] = group.groupId));
      }

      const customerIds = (
        await Bluebird.map(
          data,
          async (excelRecord: CustomerImportExcelRecord) => {
            const company = matchedCompanyInfos.find((i) => this.batchHelperService.matchCompany(excelRecord.companyName, i));
            if (company) {
              const customerObj = Object.assign(new CreateCustomerModel(), {
                orgId,
                createBy: operatorId,
                batchId: batchId,
                status: BatchStatusEnums.Processing, // 1: 处理中， 2: 处理完成
                name: company.name,
                companyId: company.id,
                startDate: excelRecord.startDate || '',
                endDate: excelRecord.endDate || '',
                principal: excelRecord?.principal,
                customerDepartment: excelRecord?.customerDepartment || '',
                departmentNames: excelRecord?.departmentNames,
              });
              const unMatchedLabelName: string[] = [];
              let unMatchedGroup: string;

              //绑定labelIds
              const labelIds = [];
              excelRecord?.label?.forEach((label) => {
                labelKV[label] ? labelIds.push(labelKV[label]) : unMatchedLabelName.push(label);
              });
              customerObj.labelIds = labelIds?.length ? labelIds : undefined;

              //绑定groupId
              if (excelRecord?.group) {
                if (groupKV[excelRecord.group]) {
                  customerObj.groupId = groupKV[excelRecord.group];
                } else {
                  // error中标记该分组不存在
                  unMatchedGroup = excelRecord.group;
                }
              }
              this.logger.info(
                `processJobMessage batchCreateCustomer before create batchId: ${batchId}, jobId: ${jobId}, customerObj=${JSON.stringify(customerObj)}`,
              );
              const batchResult: BatchResultPO = {
                batchId,
                jobId,
                resultType: BatchJobResultTypeEnums.SUCCEED_PAID,
                resultInfo: excelRecord,
                resultHashkey: excelRecord.recordHashkey,
              };
              let customerId = 0;
              try {
                //验证客商参数
                const err = await validate(customerObj);
                if (err?.length) {
                  batchResult.resultType = BatchJobResultTypeEnums.FAILED_UNMATCHED;
                  batchResult.comment = JSON.stringify(err.map((e) => e.constraints));
                } else {
                  //保存客商列表
                  const dbCustomer = await this.customerService.create(currentUser, customerObj, true);
                  // 如果group和label不存在，记录错误记录
                  if (unMatchedGroup || unMatchedLabelName?.length > 0) {
                    batchResult.resultType = BatchJobResultTypeEnums.SUCCEED_IGNORE;
                  }
                  customerId = dbCustomer.customerId;
                }
              } catch (error) {
                processBatchJobFailed(error, batchResult);
                //isUpdate=true，覆盖更新，否则报错
                if (batchResult.resultType == BatchJobResultTypeEnums.UPDATE_DUPLICATED && isUpdate) {
                  //重复，判断有没有更新数据的权限
                  try {
                    const duplicateCustomer = await this.customerService.updateFromExcel(currentUser, customerObj);
                    if (duplicateCustomer) {
                      customerId = duplicateCustomer.customerId;
                    } else {
                      batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
                      batchResult.comment = '该企业已存在';
                    }
                  } catch (ue) {
                    batchResult.resultType = BatchJobResultTypeEnums.FAILED_CODE;
                    this.logger.error(`update customer error: ${ue} user=${currentUser.userId}, customerObj=${JSON.stringify(customerObj)}`);
                  }
                } else if (batchResult.resultType == BatchJobResultTypeEnums.UPDATE_DUPLICATED && !isUpdate) {
                  //选择不更新企业，则修改 batchResult.resultType为成功忽略
                  batchResult.resultType = BatchJobResultTypeEnums.SUCCEED_UPDATE_IGNORE;
                }
                this.logger.error(
                  `processJobMessage batchCreateCustomer createCustomer error: ${error} userId=${currentUser.userId}, customerObj=${JSON.stringify(
                    customerObj,
                  )}`,
                );
              }
              await this.batchHelperService.handleJobResult([batchResult]);
              return customerId;
            }
          },
          { concurrency: 5 },
        )
      ).filter((t) => t);
      if (customerIds.length > 0) {
        await this.roverGraphService.syncToNebula(orgId, customerIds, StreamTableEnums.Customer, StreamOperationEnum.INSERT);
      }
    }

    // 对未匹配到数据保
    if (unmatchedNames?.length) {
      // 保存未匹配上的公司
      await this.batchHelperService.saveUnMatchedCompanyBatch(unmatchedNames, batchId, jobId);
    }

    if (unsupported?.length) {
      // 通过name字段匹配公司，公司类型不支持的
      await this.batchHelperService.saveUnsupportedCompanyBatch(unsupported, batchId, jobId);
    }
    this.logger.info(
      `processJobMessage batchCreateCustomer Finished: batchId=${batchId},jobId=${jobId},matchedNames=${matchedNames},unmatchedNames=${unmatchedNames}`,
    );
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    const deleteResult = await this.customerRepo.delete({ batchId, status: Not(BatchStatusEnums.Done) });
    if (deleteResult) {
      await this.batchBundleService.handleBatchCounterEvent(batchId, RoverBundleCounterType.ThirdPartyQuantity, -deleteResult.affected);
    }
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return this.customerRepo.update(
      {
        batchId: batchEntity.batchId,
        status: BatchStatusEnums.Processing,
      },
      { status: BatchStatusEnums.Done },
    );
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
