import { DefaultProcessorBase } from '../default.processor.base';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { InnerBlacklistImportExcelRecord } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { processBatchJobFailed } from '../../../../../libs/utils/utils.bundle';
import { In, Not, Repository } from 'typeorm';
import { flatten } from 'lodash';
import { GroupType } from '../../../../../libs/model/element/CreateGroupModel';
import { CreateInnerBlacklistModel } from '../../../../../libs/model/blacklist/CreateInnerBlacklistModel';
import { BatchStatusEnums } from '../../../../../libs/enums/batch/BatchStatusEnums';
import { validate } from 'class-validator';
import { StreamTableEnums } from '../../../../../libs/enums/data/StreamTableEnums';
import { StreamOperationEnum } from '../../../../../libs/enums/data/StreamOperationEnum';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import * as Bluebird from 'bluebird';
import { InjectRepository } from '@nestjs/typeorm';
import { LabelEntity } from '../../../../../libs/entities/LabelEntity';
import { CompanySearchService } from '../../../../company/company-search.service';
import { GroupsEntity } from '../../../../../libs/entities/GroupsEntity';
import { BlacklistInnerService } from '../../../../blacklist/blacklist.inner.service';
import { RoverGraphService } from '../../../../data/source/rover.graph.service';
import { InnerBlacklistEntity } from '../../../../../libs/entities/InnerBlacklistEntity';
import { BatchBundleService } from '../../../service/batch.bundle.service';
import { RoverBundleCounterType } from '@kezhaozhao/saas-bundle-service';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchResultPO } from '../../../model/BatchResultPO';
import { allowType } from '../../../../../libs/constants/common';

export class InnerBlacklistImportProcessor extends DefaultProcessorBase {
  protected readonly logger = QccLogger.getLogger(DefaultProcessorBase.name);

  constructor(
    private readonly companyService: CompanySearchService,
    @InjectRepository(LabelEntity) private readonly labelRepo: Repository<LabelEntity>,
    @InjectRepository(GroupsEntity) private readonly groupsRepo: Repository<GroupsEntity>,
    @InjectRepository(InnerBlacklistEntity) private readonly innerBlacklistRepo: Repository<InnerBlacklistEntity>,
    private readonly batchBundleService: BatchBundleService,
    private readonly blacklistInnerService: BlacklistInnerService,
    private readonly roverGraphService: RoverGraphService,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.InnerBlacklist_File];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, orgId, operatorId, jobId, isUpdate } = message;
    this.logger.info(`processJobMessage batchCreateInnerBlacklist batchId: ${batchId}, jobId: ${jobId} , items: ${JSON.stringify(items)}`);
    const currentUser = await this.userService.getRoverUser(operatorId, orgId);
    const data: InnerBlacklistImportExcelRecord[] = items;
    // 通过name字段匹配公司，匹配不上的，标记错误
    const names: string[] = data.map((d) => d.companyName);
    const { matchedCompanyInfos, unmatchedNames, matchedNames, unsupported } = await this.companyService.matchCompanyInfo(
      names,
      ['id', 'name', 'creditcode', 'regno', 'province', 'areacode', 'address', 'industry', 'subind', 'econkindcode'],
      allowType,
      true,
    );
    // 对匹配到的公司数据进行处理，保存客商列表
    if (matchedCompanyInfos?.length > 0) {
      //生成标签KV
      const labels: string[][] = data.map((d) => d.label);
      const labelKV = {};
      //TODO labelName 如果不存在，是否需要自动创建label
      if (labels && labels?.length > 0) {
        const labelEntities = await this.labelRepo.find({ name: In(flatten(labels)), orgId, labelType: 3 });
        labelEntities.forEach((label) => (labelKV[label.name] = label.labelId));
      }
      //生成分组KV
      const groupKV = {};
      const groups: string[] = data.map((d) => d.group);
      if (groups?.length > 0) {
        const groupsEntities = await this.groupsRepo.find({
          name: In(groups),
          groupType: GroupType.InnerBlacklistGroup,
          orgId,
        });
        groupsEntities.forEach((group) => (groupKV[group.name] = group.groupId));
      }

      const blacklistIds = (
        await Bluebird.map(
          data,
          async (excelRecord: InnerBlacklistImportExcelRecord) => {
            const company = matchedCompanyInfos.find((i) => this.batchHelperService.matchCompany(excelRecord.companyName, i));
            if (company) {
              const innerBlacklistModel = Object.assign(new CreateInnerBlacklistModel(), {
                orgId,
                createBy: operatorId,
                batchId: batchId,
                status: BatchStatusEnums.Processing, // 1: 处理中， 2: 处理完成
                companyName: company.name,
                companyId: company.id,
                joinDate: excelRecord.joinDate || '',
                expiredDate: excelRecord?.expiredDate,
                reason: excelRecord.reason || '',
                duration: excelRecord.duration,
                department: excelRecord.department || '',
                departmentNames: excelRecord?.departmentNames,
                comment: excelRecord.comment || '',
              });
              const unMatchedLabelName: string[] = [];
              let unMatchedGroup: string;

              //绑定labelIds
              const labelIds = [];
              excelRecord?.label?.forEach((label) => {
                labelKV[label] ? labelIds.push(labelKV[label]) : unMatchedLabelName.push(label);
              });
              innerBlacklistModel.labelIds = labelIds?.length ? labelIds : undefined;

              //绑定groupId
              if (excelRecord?.group) {
                if (groupKV[excelRecord.group]) {
                  innerBlacklistModel.groupId = groupKV[excelRecord.group];
                } else {
                  // error中标记该分组不存在
                  unMatchedGroup = excelRecord.group;
                }
              }
              // 记录处理结果
              const batchResult: BatchResultPO = {
                batchId,
                jobId,
                resultType: BatchJobResultTypeEnums.SUCCEED_PAID,
                resultInfo: excelRecord,
                resultHashkey: excelRecord.recordHashkey,
              };
              let blacklistId = 0;
              //保存黑名单
              try {
                const err = await validate(innerBlacklistModel);
                if (err?.length) {
                  batchResult.resultType = BatchJobResultTypeEnums.FAILED_UNMATCHED;
                  batchResult.comment = JSON.stringify(err);
                } else {
                  const r = await this.blacklistInnerService.add(currentUser, innerBlacklistModel, true);
                  // 如果group和label不存在，记录错误记录
                  if (unMatchedGroup || unMatchedLabelName?.length > 0) {
                    batchResult.resultType = BatchJobResultTypeEnums.SUCCEED_IGNORE;
                  }
                  blacklistId = r.id;
                }
              } catch (error) {
                processBatchJobFailed(error, batchResult);
                if (batchResult.resultType == BatchJobResultTypeEnums.UPDATE_DUPLICATED && isUpdate) {
                  //重复，更新数据
                  try {
                    const duplicateBlacklist = await this.blacklistInnerService.updateFromExcel(currentUser, innerBlacklistModel);
                    if (duplicateBlacklist) {
                      blacklistId = duplicateBlacklist.id;
                    } else {
                      batchResult.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
                      batchResult.comment = '该企业已存在';
                    }
                  } catch (ue) {
                    batchResult.resultType = BatchJobResultTypeEnums.FAILED_CODE;
                    this.logger.error(`update inner blacklist error , err = ${ue.message}, innerBlacklistModel=${JSON.stringify(innerBlacklistModel)}`);
                    this.logger.error(ue);
                  }
                } else if (batchResult.resultType == BatchJobResultTypeEnums.UPDATE_DUPLICATED && !isUpdate) {
                  //选择不更新企业，则修改 batchResult.resultType为成功忽略
                  batchResult.resultType = BatchJobResultTypeEnums.SUCCEED_UPDATE_IGNORE;
                }
                this.logger.error(
                  `processJobMessage batchCreateInnerBlacklist create innerBlacklist error, err = ${error.message}, innerBlacklistModel=${JSON.stringify(
                    innerBlacklistModel,
                  )}`,
                );
                this.logger.error(error);
              }
              await this.batchHelperService.handleJobResult([batchResult]);
              return blacklistId;
            }
          },
          { concurrency: 5 },
        )
      ).filter((t) => t);
      if (blacklistIds.length > 0) {
        await this.roverGraphService.syncToNebula(orgId, blacklistIds, StreamTableEnums.InnerBlacklist, StreamOperationEnum.INSERT);
      }
    }

    // 对未匹配到数据保
    if (unmatchedNames?.length) {
      await this.batchHelperService.saveUnMatchedCompanyBatch(unmatchedNames, batchId, jobId);
    }
    if (unsupported?.length) {
      // 通过name字段匹配公司，公司类型不支持的
      await this.batchHelperService.saveUnsupportedCompanyBatch(unsupported, batchId, jobId);
    }
    this.logger.info(
      `processJobMessage batchCreateInnerBlacklist Finished: batchId=${batchId},jobId=${jobId},matchedNames=${matchedNames},unmatchedNames=${unmatchedNames}`,
    );
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    const deleteResult = await this.innerBlacklistRepo.delete({ batchId, status: Not(BatchStatusEnums.Done) });
    if (deleteResult) {
      await this.batchBundleService.handleBatchCounterEvent(batchId, RoverBundleCounterType.InnerBlacklistQuantity, -deleteResult.affected);
    }
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return this.innerBlacklistRepo.update(
      {
        batchId: batchEntity.batchId,
        status: BatchStatusEnums.Processing,
      },
      { status: BatchStatusEnums.Done },
    );
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
