import { BatchMessageProcessorAbstract } from '../batch.message.processor.abstract';
import { BatchBaseHelper } from '../../service/helper/batch.base.helper';
import { UserService } from '../../../user/user.service';
import { Inject } from '@nestjs/common';

export abstract class DefaultProcessorBase extends BatchMessageProcessorAbstract {
  @Inject(BatchBaseHelper)
  protected readonly batchHelperService: BatchBaseHelper;
  @Inject(UserService)
  protected readonly userService: UserService;
}
