import { BatchMessageHandlerAbstract } from '../batch.message.handler.abstract';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { Injectable } from '@nestjs/common';
import { BatchMonitorProcessor } from './processors/batch.monitor.processor';
import { CustomerImportProcessor } from './processors/customer.import.processor';
import { InnerBlacklistImportProcessor } from './processors/inner.blacklist.import.processor';
import { PersonImportProcessor } from './processors/person.import.processor';
import { BatchConstants } from '../../common/batch.constants';
import { QueueService } from '../../../../libs/config/queue.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';

@Injectable()
export class BatchMessageHandlerDefault extends BatchMessageHandlerAbstract {
  public batchJobQueue: RabbitMQ;

  constructor(
    private readonly batchMonitorProcessor: BatchMonitorProcessor,
    private readonly customerImportProcessor: CustomerImportProcessor,
    private readonly innerBlacklistImportProcessor: InnerBlacklistImportProcessor,
    private readonly personImportProcessor: PersonImportProcessor,
    protected readonly queueService: QueueService,
    protected readonly redisService: RedisService,
  ) {
    super(queueService, redisService);

    Array.prototype.push.apply(this.allProcessor, [
      this.batchMonitorProcessor,
      this.customerImportProcessor,
      this.innerBlacklistImportProcessor,
      this.personImportProcessor,
    ]);
    this.batchJobQueue = this.queueService.batchJobQueue;
    this.batchJobQueue.consume(this.handleJobMessage.bind(this), BatchConstants.Consumer.Others).catch((err) => this.logger.error(err));
    this.batchJobQueue.on('failed', this.onPulsarQueueError.bind(this));
  }

  getTargetQueue(businessType: BatchBusinessTypeEnums): RabbitMQ {
    return this.batchJobQueue;
  }
}
