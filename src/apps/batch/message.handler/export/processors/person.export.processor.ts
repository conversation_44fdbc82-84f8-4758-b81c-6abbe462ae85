import { ExportProcessorBase } from '../export.processor.base';
import { Injectable } from '@nestjs/common';
import { RoverUser } from '../../../../../libs/model/common';
import { FileResultPO } from '../model/FileResultPO';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { SearchPersonModel } from '../../../../../libs/model/person/SearchPersonModel';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import { PersonService } from '../../../../person/person.service';
import { ExportProcessorHelperService } from '../export.processor.helper.service';
import * as Bluebird from 'bluebird';
import { GroupType } from '../../../../../libs/model/element/CreateGroupModel';
import { AreaMapping } from '@kezhaozhao/qcc-model';
import { Workbook } from 'exceljs';
import { ExportConditionBase, ExportRecordPO } from '../model/ExportRecordPO';
import { SearchPersonResponse } from '../../../../../libs/model/person/SearchPersonResponse';
import * as moment from 'moment';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';

@Injectable()
export class PersonExportProcessor extends ExportProcessorBase {
  constructor(private readonly personService: PersonService, private readonly processorHelperService: ExportProcessorHelperService) {
    super();
  }

  async searchDataAndGeneratorFile(condition: ExportConditionBase, user: RoverUser, businessType: BatchBusinessTypeEnums): Promise<FileResultPO> {
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    switch (businessType) {
      case BatchBusinessTypeEnums.Person_Export: {
        const searchPersonResponse: SearchPersonResponse = await this.personService.searchPersonList(
          user,
          Object.assign(new SearchPersonModel(), condition),
          true,
        );
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.PersonList]: searchPersonResponse?.data,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = searchPersonResponse.total;
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Person_Export];
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordPO, workbook: Workbook, condition: ExportConditionBase): Promise<string> {
    let exportFileName = '';
    const worksheets = workbook.worksheets;
    switch (businessType) {
      case BatchBusinessTypeEnums.Person_Export: {
        // 人员导出
        exportFileName = '人员导出';
        const records = record[ExportEnums.PersonList];
        //获取分组信息并构建成map
        const personGroupMap = await this.processorHelperService.getGroupKV(condition.orgId, GroupType.PersonGroup);
        await Bluebird.map(worksheets, async (worksheet) => {
          for (const record of records) {
            const r: any = {};
            Object.keys(record).forEach((rk) => {
              r[rk] = record[rk] || '-';
            });
            r.creator = record?.creator?.name || '-';
            //r.owner = r?.owner?.name || '-';
            const area = [
              AreaMapping[record?.province] || record?.province,
              AreaMapping[record?.city] || record?.city,
              AreaMapping[record?.district] || record?.district,
            ]
              .filter((d) => {
                if (d) {
                  return d;
                }
              })
              .join('/');
            r.birthPlace = area || '-';
            //分组
            r.group = personGroupMap[r.groupId] || '-';
            r.personNo = record?.personNo?.split('_')[0];
            r.createDate = moment(record.createDate, moment.ISO_8601).format(DATE_TIME_FORMAT) || '-';
            r.updateDate = moment(record.updateDate, moment.ISO_8601).format(DATE_TIME_FORMAT) || '-';
            worksheet.addRow({ ...r }).commit();
          }
          //worksheet.commit();
        });
        break;
      }
      default: {
        this.logger.warn(`applyDataToFile unknown batch business type: ${businessType}`);
        return Promise.resolve('');
      }
    }
    return exportFileName;
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
