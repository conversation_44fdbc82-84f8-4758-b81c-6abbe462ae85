import { EntityManager, getRepository, Repository } from 'typeorm';
import MyOssService from '../../../../basic/my-oss.service';
import xlsx from 'node-xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { tmpdir } from 'os';
import { random } from 'lodash';
import { RiskExportProcessor } from './risk.export.processor';
import { Test, TestingModule } from '@nestjs/testing';
import { BundleTestUtils } from '../../../../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { ExportTestUtils, mockDynamicResponse } from '../../../../test_utils_module/export.test.utils';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { RiskDynamicsSearchRequest } from '../../../../../libs/model/monitor/request/RiskDynamicsSearchRequest';
import { MonitorRiskDynamicsV2Entity } from '../../../../../libs/entities/MonitorRiskDynamicsV2Entity';
import { PermissionByEnum } from '../../../../../libs/enums/PermissionScopeEnum';
import { SecurityService } from '../../../../../libs/config/security.service';
import { MonitorRiskDynamicResponse } from '../../../../../libs/model/monitor/response/MonitorRiskDynamicResponse';
import { dateTransform } from '../../../../../libs/utils/date.utils';
import { AppTestModule } from '../../../../app/app.test.module';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchModule } from '../../../batch.module';
import { MyOssMock } from 'apps/test_utils_module/my-oss.mock';
const [testOrgId, testUserId] = generateUniqueTestIds('risk.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
const ossMock = new MyOssMock();

/**
 * 单元测试
 */
jest.setTimeout(60 * 1000);
describe.skip('单元测试-风险动态列表导出processor', () => {
  let entityManager: EntityManager;
  let myOssService: MyOssService;
  const condition = Object.assign(new RiskDynamicsSearchRequest(), { pageSize: 10, pageIndex: 1 });
  const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));
  let dynamicRepo: Repository<MonitorRiskDynamicsV2Entity>;
  let processor: RiskExportProcessor;
  let securityService: SecurityService;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    BundleTestUtils.spy(moduleFixture.get(BundleHelperService));
    myOssService = moduleFixture.get(MyOssService);
    processor = moduleFixture.get(RiskExportProcessor);
    securityService = moduleFixture.get(SecurityService);
    dynamicRepo = getRepository(MonitorRiskDynamicsV2Entity);
    entityManager = dynamicRepo.manager;
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
  });

  afterAll(async () => {
    await ExportTestUtils.clearTestData(entityManager, testUser);
    await dynamicRepo.delete({ orgId: testUser.currentOrg });
    await entityManager.connection.close();
  });

  it.skip('查询风险动态列表数据并生成文件', async () => {
    // TODO: 准备mock数据
    jest.spyOn(securityService, 'checkScope').mockReturnValue({
      by: PermissionByEnum.ORG,
    });
    const spy = jest
      .spyOn(processor.riskDynamicService, 'searchMonitorRiskDynamics')
      .mockReturnValue(Promise.resolve(Object.assign(new MonitorRiskDynamicResponse(), mockDynamicResponse)));
    condition.orgId = testUser.currentOrg;
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Risk_Export);
    expect(result).not.toBeNull();
    expect(spy).toHaveBeenCalled(); // Ensure the method was called
    const ossFileUrl = ossMock.signSingleUrl(result.fileUrl);
    //创建本地临时文件夹，将ossFileUrl文件下载到本地临时文件夹
    fs.mkdirSync(tempDir);
    const dest = path.join(tempDir, result.fileName);
    await ExportTestUtils.downloadFile(ossFileUrl, dest, ossMock);
    //读取本地临时文件夹文件内容
    expect(ossFileUrl).not.toBeNull();
    //解析本地文件之后校验导出内容是否正确
    const rowData = xlsx.parse(dest);
    expect(rowData.length).toBeGreaterThan(0);
    const excelRowData = rowData[0].data.slice(3, result.recordCount + 3);
    expect(excelRowData.length).toBeGreaterThan(0);
    excelRowData.forEach((item, index) => {
      expect(item[0]).toEqual(mockDynamicResponse.data[index].companyName);
      expect(item[4]).toEqual(dateTransform(mockDynamicResponse.data[index].riskCreateDate, DATE_TIME_FORMAT)); //更新时间
      expect(item[6]).toEqual(mockDynamicResponse.data[index].group.name);
    });
  });
});
