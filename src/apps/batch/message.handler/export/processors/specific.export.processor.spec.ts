import { SpecificExportProcessor } from './specific.export.processor';
import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../../../../app/app.test.module';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchModule } from '../../../batch.module';
const [testOrgId, testUserId] = generateUniqueTestIds('specific.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

describe('单元测试-特定利益关系排查结果导出processor', () => {
  let processor: SpecificExportProcessor;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    processor = moduleFixture.get<SpecificExportProcessor>(SpecificExportProcessor);
  });

  it.skip('debug-查询特定利益关系排查结果列表数据并生成文件', async () => {
    const condition = {
      batchId: 48160,
      orgId: 208,
      pageIndex: 1,
      pageSize: 10,
      status: [0, 1, 2, -1],
    };
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Specific_Batch_Export);
    expect(result).not.toBeNull();
  }, 300000);
});
