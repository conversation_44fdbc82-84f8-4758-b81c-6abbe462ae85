import { ExportProcessorBase } from '../export.processor.base';
import { ExportConditionRequest, ExportRecordPO, ExportSpecificListItemPO } from '../model/ExportRecordPO';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { PaginationParams, RoverUser } from '../../../../../libs/model/common';
import { FileResultPO } from '../model/FileResultPO';
import { Injectable } from '@nestjs/common';
import { SearchDiligenceBiddingRequest } from '../../../../../libs/model/bidding/SearchDiligenceBiddingRequest';
import { SpecificFacadeService } from '../../../../specific/specific.facade.service';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import * as moment from 'moment/moment';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';
import { SpecificPdfService } from '../../../../specific/service/specific.pdf.service';
import { BiddingStaticPO } from '../../../model/BiddingStaticPO';
import { SearchBiddingBatchResultRequest } from '../../../../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { BatchSpecificHelper } from '../../../service/helper/batch.specific.helper';
import * as Bluebird from 'bluebird';
import { getSpecificDimensionKeyNames } from '../../../../../libs/constants/specific.dimension.constant';

@Injectable()
export class SpecificExportProcessor extends ExportProcessorBase {
  constructor(
    readonly specificFacadeService: SpecificFacadeService,
    readonly specificPDFService: SpecificPdfService,
    private readonly batchSpecificHelper: BatchSpecificHelper,
  ) {
    super();
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordPO, workbook, condition: PaginationParams): Promise<string> {
    let exportFileName = '';
    const worksheets = workbook.worksheets;
    switch (businessType) {
      case BatchBusinessTypeEnums.Specific_Record_List_Export:
        exportFileName = '特定利益关系排查历史记录';
        this.handleSpecificRecordListExport(record[ExportEnums.SpecificRecordList]).forEach((r) => {
          worksheets[0].addRow(r).commit();
        });
        break;
      case BatchBusinessTypeEnums.Specific_Batch_Export:
        exportFileName = '批量特定利益关系排查记录';
        await Bluebird.map(worksheets, async (worksheet) => {
          //取出data
          if (worksheet.name === '排查统计') {
            const staticInfo = record[ExportEnums.Specific_Batch_Statistics_Export];
            await Bluebird.map(staticInfo, (r) => {
              //处理statistics
              worksheet.addRow({ riskLevel: getSpecificDimensionKeyNames()[r.key], riskCount: r.value }).commit();
            });
          }
          if (worksheet.name === '特定利益关系排查历史记录') {
            const specificListItemPOS: ExportSpecificListItemPO[] = record[ExportEnums.Specific_Batch_Detail_Export];
            this.handleSpecificRecordListExport(specificListItemPOS).forEach((r) => {
              worksheet.addRow(r).commit();
            });
          }
        });
        break;
      default: {
        this.logger.warn(`applyDataToFile unknown batch business type: ${businessType}`);
      }
    }
    return exportFileName;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Specific_Record_List_Export, BatchBusinessTypeEnums.Specific_Report_Export, BatchBusinessTypeEnums.Specific_Batch_Export];
  }

  onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async searchDataAndGeneratorFile(condition: PaginationParams, user: RoverUser, businessType: BatchBusinessTypeEnums): Promise<FileResultPO> {
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    switch (businessType) {
      case BatchBusinessTypeEnums.Specific_Record_List_Export: {
        // 历史记录列表导出
        Object.assign(condition, { orgId: user.currentOrg });
        const searchRequest = condition as SearchDiligenceBiddingRequest;
        let recordList = [];
        if (searchRequest.diligenceIds?.length > 0) {
          // 选择导出
          const response = await this.specificFacadeService.search(user, searchRequest);
          recordList = response.data;
        } else {
          // 全部导出
          recordList = await this.getAllRecords(user, searchRequest);
        }
        if (recordList.length > 0) {
          const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
            [ExportEnums.SpecificRecordList]: recordList,
          });
          fileResult.fileUrl = fileUrl;
          fileResult.fileName = fileName;
          fileResult.recordCount = recordList.length;
        }
        break;
      }
      case BatchBusinessTypeEnums.Specific_Report_Export: {
        // pdf导出
        const { orgId, diligenceId } = condition as ExportConditionRequest;
        this.logger.info(`export tender record export id:${diligenceId}`);
        const { fileUrl, fileName, previewUrl } = await this.specificPDFService.generateSpecificPdf(orgId, diligenceId);
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = 1;
        fileResult.previewUrl = previewUrl;
        break;
      }
      case BatchBusinessTypeEnums.Specific_Batch_Export: {
        // 批量导出
        Object.assign(condition, { orgId: user.currentOrg });
        const searchRequest = condition as SearchBiddingBatchResultRequest;
        this.logger.info(`export tender record list export batchId:${searchRequest.batchId}`);
        const staticInfo: BiddingStaticPO[] = await this.batchSpecificHelper.getSpecificStatistics(searchRequest);
        const statistics = staticInfo
          .filter((s) => s.key !== 'all')
          .map(({ key, totalHits }) => ({
            key,
            value: totalHits,
          }));
        const recordList = await this.batchSpecificHelper.searchSpecificDetail(user, searchRequest);
        if (recordList.data.length) {
          const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
            [ExportEnums.Specific_Batch_Statistics_Export]: statistics,
            [ExportEnums.Specific_Batch_Detail_Export]: recordList.data,
          });
          fileResult.fileUrl = fileUrl;
          fileResult.fileName = fileName;
          fileResult.recordCount = recordList.data.length;
        }
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  async getAllRecords(user: RoverUser, searchRequest: SearchDiligenceBiddingRequest) {
    let pageIndex = 1;
    const pageSize = 100;
    const allRecords = [];
    while (true) {
      const response = await this.specificFacadeService.search(user, { ...searchRequest, pageIndex, pageSize });
      if (!response) {
        return allRecords;
      }
      allRecords.push(...response.data);
      if (response.total <= pageIndex * pageSize) {
        break;
      }
      pageIndex += 1;
    }
    return allRecords;
  }

  private handleSpecificRecordListExport(records: ExportSpecificListItemPO[]) {
    const resultMap = { '0': '通过', '1': '审慎核实', '2': '不通过', '-1': '稍后核定' };
    const resultList = [];
    records.forEach((record) => {
      const item = Object.assign(new ExportSpecificListItemPO(), record);
      const result = record.status == 2 ? '排查失败' : resultMap[record?.remarkResult ?? record?.result];
      const dimensionDetails = record.details?.dimensionOverviews?.map((detail) => detail.name);
      let hitDimensions = '-';
      if (dimensionDetails && dimensionDetails.length > 0) {
        hitDimensions = dimensionDetails.join('\n');
      }
      item.result = result;
      item.projectNo = record.projectNo;
      item.recordNo = record.recordNo;
      item['hitDimensions'] = hitDimensions;
      item['companyNames'] = record.companyList.map((company) => company.companyName).join('\n');
      item['editorName'] = record.editor?.name;
      item['createDateStr'] = moment(record.createDate).format(DATE_TIME_FORMAT);
      resultList.push(item);
    });
    return resultList;
  }
}
