import { ExportProcessorBase } from '../export.processor.base';
import { Injectable } from '@nestjs/common';
import { RoverUser } from '../../../../../libs/model/common';
import { FileResultPO } from '../model/FileResultPO';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { SearchCustomerNewsRequest } from '../../../../../libs/model/customer/SearchCustomerNewsRequest';
import { compact, flatten } from 'lodash';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import { MonitorSentimentDynamicService } from '../../../../monitor/sentiment/monitor.sentiment.dynamic.service';
import * as Bluebird from 'bluebird';
import { RiskLevelConst } from '../../../../../libs/model/diligence/pojo/dimension/DimensionStrategyPO';
import { TopicMap } from '../../../../../libs/constants/news.setting.constants';
import { MonitorDynamicMap } from '../../../../../libs/enums/monitor/MonitorRiskEnums';
import * as moment from 'moment/moment';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';
import { Workbook } from 'exceljs';
import { ExportConditionBase, ExportRecordPO } from '../model/ExportRecordPO';
import { NegativeNewsResponse } from '../../../../../libs/model/monitor/response/NegativeNewsResponse';

@Injectable()
export class SentimentExportProcessor extends ExportProcessorBase {
  constructor(public readonly sentimentService: MonitorSentimentDynamicService) {
    super();
  }

  async searchDataAndGeneratorFile(condition: ExportConditionBase, user: RoverUser, businessType: BatchBusinessTypeEnums): Promise<FileResultPO> {
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    switch (businessType) {
      case BatchBusinessTypeEnums.Sentiment_Export: {
        const sentimentResponse: NegativeNewsResponse = await this.sentimentService.searchNegativeNews(
          user,
          Object.assign(new SearchCustomerNewsRequest(), condition, {
            pageIndex: 1,
            pageSize: 500,
          }),
        );
        const exportData = [];
        if (sentimentResponse?.data) {
          exportData.push(...sentimentResponse.data);
        }
        const promises = [];
        if (sentimentResponse?.total >= 500) {
          for (let i = 2; i <= Math.floor(sentimentResponse?.total / 500) + 1; i++) {
            promises.push(
              this.sentimentService.searchNegativeNews(
                user,
                Object.assign(new SearchCustomerNewsRequest(), condition, {
                  pageIndex: i,
                  pageSize: 500,
                }),
              ),
            );
          }
        }
        //分批查询
        const promiseAll = await Bluebird.all(promises);
        exportData.push(...compact(flatten(promiseAll?.map((m) => m?.data))));
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.Sentiment_Export]: exportData,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = sentimentResponse?.total;
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Sentiment_Export];
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordPO, workbook: Workbook, condition: ExportConditionBase): Promise<string> {
    let exportFileName = '';
    const worksheets = workbook.worksheets;
    switch (businessType) {
      case BatchBusinessTypeEnums.Sentiment_Export: {
        exportFileName = '合作监控-舆情动态列表';
        const records = record[ExportEnums.Sentiment_Export];

        await Bluebird.map(worksheets, async (worksheet) => {
          for (const record of records) {
            const r: any = { ...record };
            r.dimensionLevel = RiskLevelConst[record.dimensionLevel] || '-';
            r.dimension = TopicMap[record?.dimensionKey];
            r.content = `${record?.title}\n来源：${record?.source}`;
            r.group = record?.group?.name || '-';
            r.status = MonitorDynamicMap[r?.status];
            r.dynamicPublishDate = r?.dynamicPublishDate ? moment(r.dynamicPublishDate, moment.ISO_8601).format(DATE_TIME_FORMAT) : '-';
            worksheet.addRow({ ...r }).commit();
          }
        });
        break;
      }
      default: {
        this.logger.warn(`applyDataToFile unknown batch business type: ${businessType}`);
      }
    }
    return exportFileName;
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
