import { EntityManager, getRepository, Repository } from 'typeorm';
import MyOssService from '../../../../basic/my-oss.service';
import { RoverUser } from '../../../../../libs/model/common';
import * as path from 'path';
import { tmpdir } from 'os';
import { random } from 'lodash';
import { Test, TestingModule } from '@nestjs/testing';
import { BundleTestUtils } from '../../../../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { ExportTestUtils } from '../../../../test_utils_module/export.test.utils';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import * as fs from 'fs';
import xlsx from 'node-xlsx';
import { InnerBlacklistEntity } from '../../../../../libs/entities/InnerBlacklistEntity';
import { InnerBlacklistExportProcessor } from './inner.blacklist.export.processor';
import { SearchInnerBlacklistModel } from '../../../../../libs/model/blacklist/SearchInnerBlacklistModel';
import { DurationConstants } from '../../../file.parser/file.export.template';
import { AppTestModule } from '../../../../app/app.test.module';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchModule } from '../../../batch.module';
import { MyOssMock } from '../../../../test_utils_module/my-oss.mock';
const [testOrgId, testUserId] = generateUniqueTestIds('inner.blacklist.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
const ossMock = new MyOssMock();

jest.setTimeout(600000);
describe('单元测试-内部黑名单导出processor', () => {
  let entityManager: EntityManager;
  let innerBlacklistRepo: Repository<InnerBlacklistEntity>;
  let myOssService: MyOssService;
  let innerBlacklistExportProcessor: InnerBlacklistExportProcessor;
  const condition = Object.assign(new SearchInnerBlacklistModel(), { pageSize: 10, pageIndex: 1 });
  const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    BundleTestUtils.spy(moduleFixture.get(BundleHelperService));
    myOssService = moduleFixture.get(MyOssService);
    innerBlacklistRepo = getRepository(InnerBlacklistEntity);
    entityManager = innerBlacklistRepo.manager;
    innerBlacklistExportProcessor = moduleFixture.get(InnerBlacklistExportProcessor);

    await innerBlacklistRepo.delete({ orgId: testUser.currentOrg });

    await ExportTestUtils.createInnerBlacklistTestData(innerBlacklistRepo, testUser);

    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
  });

  afterAll(async () => {
    await ExportTestUtils.clearTestData(entityManager, testUser);
    await innerBlacklistRepo.delete({ orgId: testUser.currentOrg });
    await entityManager.connection.close();
  });

  it(
    '查询内部黑名单列表数据并生成文件 InnerBlacklist_Export',
    async () => {
      Object.assign(condition, {
        orgId: testUser.currentOrg,
      });
      const result = await innerBlacklistExportProcessor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.InnerBlacklist_Export);
      expect(result).not.toBeNull();
      const ossFileUrl = ossMock.signSingleUrl(result.fileUrl);
      //创建本地临时文件夹，将ossFileUrl文件下载到本地临时文件夹
      fs.mkdirSync(tempDir);
      const dest = path.join(tempDir, result.fileName);
      await ExportTestUtils.downloadFile(ossFileUrl, dest, ossMock);
      //读取本地临时文件夹文件内容
      expect(ossFileUrl).not.toBeNull();
      //解析本地文件之后校验导出内容是否正确
      const rowData = xlsx.parse(dest);
      expect(rowData.length).toBeGreaterThan(0);
      const [blacklist, count] = await entityManager.findAndCount(InnerBlacklistEntity, { orgId: testUser.currentOrg });
      expect(result.recordCount).toEqual(count);
      const excelRowData = rowData[0].data.slice(2, result.recordCount + 2);
      expect(excelRowData.length).toBeGreaterThan(0);
      excelRowData.forEach((item, index) => {
        //导出数据校验
        expect(item[0]).toEqual(blacklist[index].companyName);
        expect(['有效', '注销'].includes(item[1].toString())).toBe(true);
        expect(item[12]).toEqual('内部黑名单导出测试' + testUser.currentOrg);
        expect(Object.values(DurationConstants).includes(item[14].toString())).toBe(true); //有效期
      });
    },
    60 * 1000,
  );

  it(
    '导出错误的batchBusinessType',
    async () => {
      const result = await innerBlacklistExportProcessor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Person_Export);
      expect(result.recordCount).toEqual(0);
      expect(result.fileUrl).toEqual('');
      expect(result.fileName).toEqual('');
      expect(result.previewUrl).toEqual('');
    },
    60 * 1000,
  );
});
