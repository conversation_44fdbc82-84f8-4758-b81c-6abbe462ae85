import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import * as Bluebird from 'bluebird';
import { Workbook, Worksheet } from 'exceljs';
import { Logger } from 'log4js';
import * as moment from 'moment/moment';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { OrgSettingsLogEntity } from '../../../../../libs/entities/OrgSettingsLogEntity';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import { DimensionLevel3Enums } from '../../../../../libs/enums/diligence/DimensionLevel3Enums';
import { DimensionRiskLevelEnum } from '../../../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { RoverUser } from '../../../../../libs/model/common';
import { DimensionDefinitionPO } from '../../../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { RiskLevelConst, RiskLevelDescription } from '../../../../../libs/model/diligence/pojo/dimension/DimensionStrategyPO';
import { GroupDimensionVersionEnums } from '../../../../../libs/model/diligence/pojo/dimension/group/GroupDimensionVersionEnums';
import { DiligenceHistoryRequest } from '../../../../../libs/model/diligence/pojo/history/DiligenceHistoryRequest';
import { DiligenceHistoryResponse } from '../../../../../libs/model/diligence/pojo/history/DiligenceHistoryResponse';
import { StatusMap } from '../../../../../libs/model/diligence/pojo/model/ModelScorePO';
import { fetchAll } from '../../../../../libs/utils/utils';
import { DiligenceAnalyzeService } from '../../../../diligence/analyze/diligence.analyze.service';
import { AnalyzedCompanySearchedRequest } from '../../../../diligence/analyze/po/AnalyzedCompanySearchedRequest';
import { DiligenceAnalyzeResponseItemPO } from '../../../../diligence/analyze/po/DiligenceAnalyzeResponseItemPO';
import { DiligenceHistoryService } from '../../../../diligence/details/diligence.history.service';
import { DiligencePDFService } from '../../../../diligence/diligence.pdf.service';
import { ExportProcessorBase } from '../export.processor.base';
import {
  DiligenceAnalysisExportRecordItemPO,
  ExportConditionBase,
  ExportConditionRequest,
  ExportDiligenceDetailsItemPO,
  ExportRecordPO,
  RiskInterestConflictExportRecordItemPO,
} from '../model/ExportRecordPO';
import { FileResultPO } from '../model/FileResultPO';

@Injectable()
export class DiligenceExportProcessor extends ExportProcessorBase {
  protected readonly logger: Logger = QccLogger.getLogger(DiligenceExportProcessor.name);

  constructor(
    public readonly diligenceHistoryService: DiligenceHistoryService,
    public readonly diligencePDFService: DiligencePDFService,
    public readonly diligenceAnalyzeService: DiligenceAnalyzeService,
  ) {
    super();
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordPO, workbook: Workbook, condition: ExportConditionBase): Promise<string> {
    let exportFileName = '';
    const worksheets = workbook.worksheets;
    //判断当前使用的orgSetting版本
    const orgSetting = await this.processorHelper.getOrgSetting(condition.orgId, condition?.targetId);
    const { dimensions, dimensionsKV } = await this.settingService.getDimensionLevel1KV(condition.orgId);
    switch (businessType) {
      case BatchBusinessTypeEnums.Diligence_Batch_Detail: {
        exportFileName = '批量排查报告';
        //通过状态map翻译
        await Bluebird.map(worksheets, async (worksheet) => {
          //取出data
          if (worksheet.name === '排查统计') {
            const records = record[ExportEnums.DiligenceStatistics];

            // Use the sort information from dimensions array
            const sortedRecords = records.sort((a, b) => {
              const aSort = dimensions.find((d) => d.key === a.key)?.sort || 0;
              const bSort = dimensions.find((d) => d.key === b.key)?.sort || 0;
              return aSort - bSort;
            });

            await Bluebird.map(sortedRecords, (r) => {
              //处理statistics
              worksheet.addRow({ riskLevel: dimensionsKV[r.key], riskCount: r.value }).commit();
            });
          }
          if (worksheet.name === '排查结果') {
            const records = record[ExportEnums.DiligenceResult];
            await this.handleDiligenceRecord(records, worksheet, orgSetting, businessType);
          }
        });
        break;
      }
      case BatchBusinessTypeEnums.Diligence_Record: {
        //排查记录导出
        exportFileName = '排查记录';
        const records = record[ExportEnums.DiligenceRecord];
        await Bluebird.map(worksheets, async (worksheet: Worksheet) => {
          await this.handleDiligenceRecord(records, worksheet, orgSetting);
        });
        break;
      }
      case BatchBusinessTypeEnums.Analyze_Record_Export: {
        //巡检排查结果导出
        exportFileName = '风险巡检排查结果';
        const records: DiligenceAnalyzeResponseItemPO[] = record[ExportEnums.DiligenceAnalyze] as DiligenceAnalyzeResponseItemPO[];
        await Bluebird.map(worksheets, async (worksheet: Worksheet) => {
          await this.handleDiligenceRecord(records, worksheet, orgSetting, businessType);
        });
        break;
      }
      default: {
        const msg = `generateResultFile unknown batch business type: ${businessType}`;
        this.logger.warn(msg);
      }
    }
    return exportFileName;
  }

  private async handleDiligenceRecord(
    records: ExportDiligenceDetailsItemPO[] | DiligenceAnalysisExportRecordItemPO[] | DiligenceAnalyzeResponseItemPO[],
    worksheet: Worksheet,
    orgSetting: OrgSettingsLogEntity,
    businessType?: BatchBusinessTypeEnums,
  ) {
    for (let record of records) {
      const r: any = { ...record };
      //处理命中信息
      const hitDetails: DimensionTypeEnums[] = r?.diligenceInfo?.details?.dimensionHits || r?.details.dimensionHits;
      //警示风险
      const hitsHigh: string[] = [];
      //关注风险
      const hitsMedium: string[] = [];
      if (hitDetails?.length) {
        for (const e of hitDetails) {
          const dimension: DimensionDefinitionPO = await this.settingService.getDimensionDefinition(e, orgSetting);
          if (dimension?.strategyModel?.level === DimensionRiskLevelEnum.High) {
            hitsHigh.push(`【${RiskLevelConst[dimension.strategyModel.level]}】${dimension.name}`);
          } else if (dimension?.strategyModel?.level === DimensionRiskLevelEnum.Medium) {
            hitsMedium.push(`【${RiskLevelConst[dimension.strategyModel.level]}】${dimension.name}`);
          }
        }
      }
      r['hit'] = hitsHigh.concat(hitsMedium).join('\n');
      if (!hitsHigh.length && !hitsMedium.length) {
        r['hit'] = '未命中风险维度';
      }
      if ([BatchBusinessTypeEnums.Analyze_Record_Export, BatchBusinessTypeEnums.Diligence_Batch_Detail].includes(businessType)) {
        const recordCopy = record as DiligenceAnalyzeResponseItemPO;
        r.editor = recordCopy?.diligenceInfo?.editor?.name;
        r.creditRate = recordCopy?.diligenceInfo?.creditRate || '-';
        r.createDate = moment(recordCopy?.diligenceInfo?.createDate).format(DATE_TIME_FORMAT);
        r.level = RiskLevelDescription[recordCopy?.diligenceInfo.result];
        r.result = StatusMap[recordCopy?.diligenceInfo.result];
        const changes = recordCopy.changes;
        const groupVersionKeys = ['risk_partner_investigation', 'risk_interest_conflict'];
        if (orgSetting.groupVersion === GroupDimensionVersionEnums.V1) {
          groupVersionKeys.push(
            'risk_base_info',
            'risk_administrative_supervision',
            'risk_operate_stability',
            'risk_inner_blacklist',
            'risk_legal',
            'risk_outer_blacklist',
            'risk_negative_news',
          );
        }
        if (orgSetting.groupVersion === GroupDimensionVersionEnums.V2) {
          groupVersionKeys.push('risk_newly_established', 'risk_punished_employees', 'risk_negative_opinion', 'risk_blacklist');
        }
        for (const key of groupVersionKeys) {
          const changesItemPOS = changes.filter((c) => c.dimensionLevel1 == key);
          if (changesItemPOS?.length > 0) {
            let currentHitValueSum = 0;
            // let decreasedValueSum = 0;
            let increasedValueSum = 0;
            changesItemPOS.forEach((changeItem) => {
              const {
                currentHitValue,
                // decreasedValue,
                increasedValue,
              } = changeItem;
              currentHitValueSum += currentHitValue;
              // decreasedValueSum += decreasedValue;
              increasedValueSum += increasedValue;
            });
            let value = '';
            if (increasedValueSum > 0) {
              value = `（+${increasedValueSum}）`;
            }
            r[key] = `${currentHitValueSum} ${value}`;
          } else {
            r[key] = recordCopy?.diligenceInfo?.details?.dimensionScoreDetails?.find((e) => e.groupKey === key)?.totalHits || 0;
          }
        }

        Object.assign(r, { ...r.companyDetail });
      } else {
        record = record as ExportDiligenceDetailsItemPO;
        r.editor = record?.editor?.name;
        r.createDate = moment(record.createDate).format(DATE_TIME_FORMAT);
        r.level = RiskLevelDescription[r?.result];
        r.result = StatusMap[r?.result];
      }
      worksheet.addRow({ ...r }).commit();
    }
  }

  async searchDataAndGeneratorFile(condition: ExportConditionBase, user: RoverUser, businessType: BatchBusinessTypeEnums): Promise<FileResultPO> {
    const { targetId, orgId } = condition;
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    switch (businessType) {
      case BatchBusinessTypeEnums.Diligence_Batch_Detail: {
        const searchCondition = condition as AnalyzedCompanySearchedRequest;
        //查询出要导出的列表信息并生成Excel
        searchCondition.pageSize = 100; //默认一次查询100条
        const analyzeData: DiligenceAnalyzeResponseItemPO[] = await fetchAll(
          this.diligenceAnalyzeService.analyze.bind(this.diligenceAnalyzeService),
          [],
          searchCondition,
          orgId,
          true,
        );
        let diligenceList = analyzeData?.map((e) => e.diligenceInfo);
        //潜在利益冲突快照ids
        const swoSnapShotIds: RiskInterestConflictExportRecordItemPO[] = [];
        //疑似潜在利益冲突快照ids
        const susSnapShotIds = [];
        if (diligenceList?.length) {
          diligenceList.forEach((d) => {
            d?.details?.dimensionScoreDetails?.map((e) => {
              d[e.groupKey] = e.totalHits;
            });
            if (d?.snapshotDetails?.successHits.includes(DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment)) {
              swoSnapShotIds.push({ snapshotId: d.snapshotId, companyName: d.name, creditcode: d.creditcode });
            }
            if (d?.snapshotDetails?.successHits.includes(DimensionLevel3Enums.SuspectedInterestConflict)) {
              susSnapShotIds.push({ snapshotId: d.snapshotId, companyName: d.name, creditcode: d.creditcode });
            }
          });
          if (searchCondition?.dimensionLevel1) {
            diligenceList = diligenceList.filter((d) => d?.[searchCondition.dimensionLevel1] > 0);
          }
        }
        const aggs = (await this.diligenceAnalyzeService.analyze(condition as AnalyzedCompanySearchedRequest, orgId)).aggs;
        const statistics = Object.keys(aggs)
          .filter((key) => key.includes('risk_'))
          .reduce((acc, cur) => {
            acc[cur] = aggs[cur];
            return acc;
          }, {});
        this.logger.info(`searchDataAndGeneratorFile businessType: ${businessType} ,targetId: ${targetId}, statistics: ${JSON.stringify(statistics)}`);
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.DiligenceStatistics]: Object.keys(statistics)?.map((key) => ({ key, value: statistics[key] })),
          [ExportEnums.DiligenceResult]: analyzeData as DiligenceAnalyzeResponseItemPO[],
          [ExportEnums.RiskInterestConflict]: swoSnapShotIds,
          [ExportEnums.SuspectedRiskInterestConflict]: susSnapShotIds,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = diligenceList?.length;
        break;
      }
      case BatchBusinessTypeEnums.Diligence_Record: {
        const diligenceHistoryResponse: DiligenceHistoryResponse = await this.diligenceHistoryService.search(
          user,
          Object.assign(new DiligenceHistoryRequest(), condition),
          true,
        );
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.DiligenceRecord]: diligenceHistoryResponse?.data,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = diligenceHistoryResponse?.total;
        break;
      }
      case BatchBusinessTypeEnums.Analyze_Record_Export: {
        condition.pageSize = 100; //默认一次查询100条
        const searchCondition = condition as AnalyzedCompanySearchedRequest;
        const analyzeData: DiligenceAnalyzeResponseItemPO[] = await fetchAll(
          this.diligenceAnalyzeService.analyze.bind(this.diligenceAnalyzeService),
          [],
          searchCondition,
          orgId,
          true,
        );
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.DiligenceAnalyze]: analyzeData as DiligenceAnalyzeResponseItemPO[],
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = analyzeData?.length;
        break;
      }
      case BatchBusinessTypeEnums.Diligence_Report_Batch_Export:
      case BatchBusinessTypeEnums.Diligence_Report_Export: {
        const { fileUrl, fileName, previewUrl } = await this.diligencePDFService.generatePdf(orgId, (condition as ExportConditionRequest).diligenceId);
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = 1;
        fileResult.previewUrl = previewUrl;
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [
      BatchBusinessTypeEnums.Diligence_Batch_Detail,
      BatchBusinessTypeEnums.Analyze_Record_Export,
      BatchBusinessTypeEnums.Diligence_Record,
      BatchBusinessTypeEnums.Diligence_Report_Batch_Export,
      BatchBusinessTypeEnums.Diligence_Report_Export,
    ];
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
