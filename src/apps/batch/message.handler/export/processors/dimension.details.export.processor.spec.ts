import { EntityManager, getRepository, Repository } from 'typeorm';
import MyOssService from '../../../../basic/my-oss.service';
import * as path from 'path';
import * as fs from 'fs';
import { tmpdir } from 'os';
import { random, sampleSize } from 'lodash';
import { Test, TestingModule } from '@nestjs/testing';
import { BundleTestUtils } from '../../../../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { ExportTestUtils } from '../../../../test_utils_module/export.test.utils';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { PermissionByEnum } from '../../../../../libs/enums/PermissionScopeEnum';
import { SecurityService } from '../../../../../libs/config/security.service';
import { AnalyzedCompanySearchedRequest } from '../../../../diligence/analyze/po/AnalyzedCompanySearchedRequest';
import { DiligenceAnalyzeService } from '../../../../diligence/analyze/diligence.analyze.service';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { DiligenceAnalyzeResponse } from '../../../../diligence/analyze/po/DiligenceAnalyzeResponse';
import { DimensionLevel1Enums } from '../../../../../libs/enums/diligence/DimensionLevel1Enums';
import { AppTestModule } from '../../../../app/app.test.module';
import { DimensionDetailsExportProcessor } from './dimension.details.export.processor';
import { FileResultPO } from '../model/FileResultPO';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchModule } from '../../../batch.module';
import { DiligenceSnapshotService } from '../../../../diligence/snapshot/diligence.snapshot.service';
import { SnapshotQueueTypeEnums } from '../../../../diligence/snapshot/po/SnapshotQueueTypeEnums';
import { sleep, BatchTestUtils } from '../../../../test_utils_module/batch.test.utils';
import { UserService } from '../../../../user/user.service';
import { BatchService } from '../../../service/batch.service';
import { BatchBaseHelper } from '../../../service/helper/batch.base.helper';
import { BatchMessageService } from '../../batch.message.service';
import { BatchMessageHandlerDiligence } from '../../diligence/batch.message.handler.diligence';
import { DiligenceExportProcessor } from './diligence.export.processor';
import { MyOssMock } from '../../../../test_utils_module/my-oss.mock';
const [testOrgId, testUserId] = generateUniqueTestIds('dimension.details.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
const ossMock = new MyOssMock();

jest.setTimeout(600 * 1000);
describe('单元测试-维度详情导出processor', () => {
  let entityManager: EntityManager;
  let myOssService: MyOssService;
  const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));
  let batchRepo: Repository<BatchEntity>;
  let processor: DiligenceExportProcessor;
  let securityService: SecurityService;
  let userService: UserService;
  let diligenceAnalyzeService: DiligenceAnalyzeService;
  let batchMessageService: BatchMessageService;
  let batchMessageHandlerDiligence: BatchMessageHandlerDiligence;
  let batchBaseHelper: BatchBaseHelper;
  let snapshotService: DiligenceSnapshotService;
  let batchService: BatchService;

  let batchEntity: BatchEntity;

  const testTool = {
    //创建真实的批量排查任务、执行尽调生产快照，
    mockBatchDiligence: async (companyData: { companyId: string; companyName: string }[]): Promise<BatchEntity> => {
      jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
        return await ossMock.putSteam(ossObject, filepath, options);
      });
      jest.spyOn(batchMessageHandlerDiligence.batchDiligenceQueue, 'sendMessageV2').mockImplementation(async (msg) => {
        await sleep(2);
        batchMessageHandlerDiligence.handleJobMessage(msg);
      });

      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation(async (msg) => {
        await sleep(0.2);
        batchMessageService.processJobMonitorMessage(msg);
      });

      jest.spyOn(userService, 'getRoverUser').mockResolvedValue(testUser);
      // mock 消费生成快照消息
      jest.spyOn(snapshotService.snapshotBatchQueue, 'sendMessageV2').mockImplementation((msg) => {
        return snapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.BatchDiligence);
      });
      const batchEntity = await batchService.createBatchDiligenceTask(testUser, companyData, BatchBusinessTypeEnums.Diligence_File);

      // 4. 等待批量任务完成并验证状态
      const currentBatch = await BatchTestUtils.waitForBatchCompletion(entityManager, batchEntity.batchId);
      return currentBatch;
    },
    mockAnalyzeResultWithDimension: async (orgId: number, condition: AnalyzedCompanySearchedRequest): Promise<DiligenceAnalyzeResponse> => {
      const analyzeResult = await diligenceAnalyzeService.analyze(condition, orgId, true);
      analyzeResult.total = analyzeResult.data.length;
      jest.spyOn(processor.diligenceAnalyzeService, 'analyze').mockReturnValue(Promise.resolve(analyzeResult));
      return analyzeResult;
    },
    getDimensionHits: async (analyzeResult: DiligenceAnalyzeResponse): Promise<string[]> => {
      const dimensionLevel1Hits = [];
      Object.keys(analyzeResult.aggs).forEach((key) => {
        if (
          Object.values(DimensionLevel1Enums)
            .map((item) => item.toString())
            .includes(key)
        ) {
          if (analyzeResult.aggs[key] > 0) {
            dimensionLevel1Hits.push(key);
          }
        }
      });
      return dimensionLevel1Hits;
    },
    checkExportRowData: async (fileResult: FileResultPO, analyzeResponse: DiligenceAnalyzeResponse) => {
      //解析本地文件之后校验导出内容是否正确
      const rowData = await ExportTestUtils.parseExcel(fileResult, ossMock, tempDir);
      //维度详情导出 rowData的长度表示有多少个 sheet页，每个 sheet页的名称为维度名称，也就是 allDimensions的数值
      expect(rowData.length).toBeGreaterThanOrEqual(1);
      //随机取rowData任意下标来校验导出内容是否正确
      const randomIndex = Math.floor(Math.random() * rowData.length);
      const diligenceRowData = rowData[randomIndex].data.slice(3, fileResult.recordCount + 3);
      if (diligenceRowData.length) {
        expect(diligenceRowData.length).toBeGreaterThanOrEqual(1);
        const companyNames = analyzeResponse.data.map((item) => item.companyName);
        const creditCodes = analyzeResponse.data.map((item) => item.companyDetail.creditcode);
        diligenceRowData.forEach((item) => {
          //存在导出维度中无数据的情况(交叉重叠关联中可能存在)
          if (item.length && item[0] && item[1]) {
            expect(companyNames.includes(item[0].toString())).toBe(true);
            expect(creditCodes.includes(item[1].toString())).toBe(true);
          }
        });
      }
      return true;
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  let moduleFixture: TestingModule;
  beforeAll(async () => {
    moduleFixture = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    BundleTestUtils.spy(moduleFixture.get(BundleHelperService));
    myOssService = moduleFixture.get(MyOssService);
    processor = moduleFixture.get(DimensionDetailsExportProcessor);
    securityService = moduleFixture.get(SecurityService);
    userService = moduleFixture.get(UserService);
    diligenceAnalyzeService = moduleFixture.get(DiligenceAnalyzeService);
    batchRepo = getRepository(BatchEntity);
    entityManager = batchRepo.manager;
    batchMessageService = moduleFixture.get(BatchMessageService);
    batchMessageHandlerDiligence = moduleFixture.get(BatchMessageHandlerDiligence);
    batchBaseHelper = moduleFixture.get(BatchBaseHelper);
    snapshotService = moduleFixture.get(DiligenceSnapshotService);
    batchService = moduleFixture.get(BatchService);

    //真实数据mock
    jest.spyOn(securityService, 'checkScope').mockReturnValue({
      by: PermissionByEnum.ORG,
    });

    batchEntity = await testTool.mockBatchDiligence([
      {
        companyId: '6c7108d628fc21216e5bc48bb035b61c',
        companyName: '上海米哈游网络科技股份有限公司',
      },
      {
        companyId: 'a52a3447c7a6df44c8a3f8698b572dc9',
        companyName: '成都钱乾信息技术有限公司',
      },
    ]);
  });

  afterAll(async () => {
    await ExportTestUtils.clearTestData(entityManager, testUser);
    await entityManager.connection.close();
    //删除临时文件夹
    await ExportTestUtils.rmDirSync(tempDir);
    jest.clearAllMocks();
    await moduleFixture.close();
  });

  it('test-myOssMockClient', async () => {
    const filepath = path.join(__dirname, 'test_file');
    const testContent = 'testContent123123123, time:' + new Date().toISOString();
    await fs.writeFileSync(filepath, testContent);
    const sourceFile = fs.readFileSync(filepath, 'utf8');
    expect(sourceFile).toBe(testContent);

    const fileName = await ossMock.putSteam('test-myOssMockClient', filepath);
    const url = ossMock.signSingleUrl(fileName);
    const dest = path.join(__dirname, 'test_file.download');
    await ExportTestUtils.downloadFile(url, dest, ossMock);
    const destFile = fs.readFileSync(dest, 'utf8');
    expect(destFile).toBe(testContent);

    // 测试完成后清理创建的临时文件
    try {
      fs.unlinkSync(filepath);
      fs.unlinkSync(dest);
      console.log('临时测试文件已成功删除');
    } catch (err) {
      console.error('删除临时文件失败:', err);
    }
  });

  it('批量排查维度详情导出 Dimension_Detail_ExportV1', async () => {
    // const ossMock = new MyOssMock();
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
    const currentBatchId = batchEntity.batchId;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testUser.currentOrg,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
    });
    condition['dimensionKey'] = 'Bankruptcy';
    condition.dimensionLevel1 = 'risk_legal';
    condition.dimensionLevel2 = 'Bankruptcy';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testUser.currentOrg, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it('批量排查维度详情导出 Dimension_Detail_Export', async () => {
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
    const currentBatchId = batchEntity.batchId;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testUser.currentOrg,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
    });
    //不带维度信息的分析结果
    const analyzeResult = await diligenceAnalyzeService.analyze(condition, testUser.currentOrg, true);
    //const dimensionKV = await settingService.getDimensionKV(208);
    const dimensionLevel1Hits = await testTool.getDimensionHits(analyzeResult);
    // 对命中的一级维度进行测试 dimensionLevel1 in DimensionLevel1Enums，任意取一个一级维度
    const dimensionLevel1 = sampleSize(dimensionLevel1Hits, 1)[0];
    // condition.dimensionLevel1 = 'risk_partner_investigation';
    condition.dimensionLevel1 = dimensionLevel1;
    // condition['dimensionKey'] = 'BusinessAbnormal3';
    // condition.dimensionLevel1 = 'risk_administrative_supervision';
    // condition.dimensionLevel2 = 'BusinessAbnormal3';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testUser.currentOrg, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it('风险巡检排查维度详情导出 Analyze_Dimension_Detail', async () => {
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
    const preBatch = await testTool.mockBatchDiligence([
      {
        companyId: '6c7108d628fc21216e5bc48bb035b61c',
        companyName: '上海米哈游网络科技股份有限公司',
      },
      {
        companyId: '4e3fb77a20a0f7b777e6cd70969101f3',
        companyName: '江西省保灵动物保健品有限公司',
      },
    ]);
    const currentBatchId = batchEntity.batchId;
    const preBatchId = preBatch.batchId;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testUser.currentOrg,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: preBatchId,
      batchIdPrevious: preBatchId,
      batchIdCurrent: currentBatchId,
    });
    //不带维度信息的分析结果
    const analyzeResult = await diligenceAnalyzeService.analyze(condition, testUser.currentOrg, true);
    //const dimensionKV = await settingService.getDimensionKV(208);
    const dimensionLevel1Hits = await testTool.getDimensionHits(analyzeResult);
    // 对命中的一级维度进行测试 dimensionLevel1 in DimensionLevel1Enums，任意取一个一级维度
    const dimensionLevel1 = sampleSize(dimensionLevel1Hits, 1)[0];
    condition.dimensionLevel1 = dimensionLevel1;
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testUser.currentOrg, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Analyze_Dimension_Detail);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it.skip('批量排查维度详情导出 Dimension_Detail_Export (真实数据导出)', async () => {
    const currentBatchId = batchEntity.batchId;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testUser.currentOrg,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
    });
    //不带维度信息的分析结果
    await diligenceAnalyzeService.analyze(condition, testUser.currentOrg, true);
    condition.dimensionLevel1 = 'risk_interest_conflict';
    condition.dimensionLevel2 = 'SuspectedInterestConflict';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testUser.currentOrg, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result.fileUrl).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it.skip('批量排查维度详情导出 Dimension_Detail_Export (指定 1001653组织 真实数据导出)', async () => {
    const currentBatchId = batchEntity.batchId;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testOrgId,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
      diligenceIds: [548154],
    });
    //不带维度信息的分析结果
    await diligenceAnalyzeService.analyze(condition, testOrgId, true);
    condition['dimensionKey'] = 'risk_inner_blacklist';
    condition.dimensionLevel1 = 'risk_blacklist';
    condition.dimensionLevel2 = 'risk_inner_blacklist';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testOrgId, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it.skip('涉贪污受贿裁判相关提及方 (指定 208组织 真实数据导出)', async () => {
    //涉贪污受贿裁判相关
    const testOrgId = 208;
    const currentBatchId = 14579;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testOrgId,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
    });
    //不带维度信息的分析结果
    await diligenceAnalyzeService.analyze(condition, testOrgId, true);
    condition['dimensionKey'] = 'CompanyOrMainMembersCriminalInvolveHistory';
    condition.dimensionLevel1 = 'risk_legal';
    condition.dimensionLevel2 = 'CompanyOrMainMembersCriminalInvolveHistory';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testOrgId, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it.skip('蔡司', async () => {
    //涉贪污受贿裁判相关
    const testOrgId = 1001652;
    const currentBatchId = 36247;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testOrgId,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
    });
    //不带维度信息的分析结果
    await diligenceAnalyzeService.analyze(condition, testOrgId, true);
    //condition['dimensionKey'] = 'MajorDispute';
    condition.dimensionLevel1 = 'risk_inner_blacklist';
    //condition.dimensionLevel2 = 'MajorDispute';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testOrgId, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it.skip('税务催报、催缴 (指定 208组织 真实数据导出)', async () => {
    //涉贪污受贿裁判相关
    const testOrgId = 208;
    const currentBatchId = 35049;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testOrgId,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
    });
    //不带维度信息的分析结果
    await diligenceAnalyzeService.analyze(condition, testOrgId, true);
    condition['dimensionKey'] = 'TaxReminder';
    condition.dimensionLevel1 = 'risk_administrative_supervision';
    condition.dimensionLevel2 = 'TaxReminder';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testOrgId, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it.skip('交叉重叠排查导出测试 (指定 208组织 真实数据导出)', async () => {
    //涉贪污受贿裁判相关
    const testOrgId = 208;
    const currentBatchId = 42937;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testOrgId,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
    });
    //不带维度信息的分析结果
    await diligenceAnalyzeService.analyze(condition, testOrgId, true);
    condition.dimensionLevel1 = 'risk_partner_investigation';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testOrgId, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it.skip('公司主要人员涉刑事犯罪', async () => {
    //涉贪污受贿裁判相关
    const testOrgId = 1001652;
    const currentBatchId = 50754;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 10,
      pageIndex: 1,
      orgId: testOrgId,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
    });
    //不带维度信息的分析结果
    await diligenceAnalyzeService.analyze(condition, testOrgId, true);
    condition.dimensionLevel1 = 'risk_legal';
    condition.dimensionLevel2 = 'CompanyOrMainMembersCriminalOffence';
    condition['dimensionKey'] = 'CompanyOrMainMembersCriminalOffence';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testOrgId, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it.skip('票据持续逾期', async () => {
    //涉贪污受贿裁判相关
    const testOrgId = 208;
    const currentBatchId = 53565;
    const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
      pageSize: 100,
      pageIndex: 1,
      orgId: testOrgId,
      targetId: currentBatchId,
      aggsInfo: 1,
      diligenceInfo: 1,
      batchId: currentBatchId,
      preBatchId: -1,
      batchIdPrevious: -1,
      batchIdCurrent: currentBatchId,
    });
    //不带维度信息的分析结果
    await diligenceAnalyzeService.analyze(condition, testOrgId, true);
    condition.dimensionLevel1 = 'risk_operate_stability';
    condition.dimensionLevel2 = 'PersistentBillOverdue';
    condition['dimensionKey'] = 'PersistentBillOverdue';
    // 带维度信息的分析结果
    const analyzeResponse = await testTool.mockAnalyzeResultWithDimension(testOrgId, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });
});
