import { EntityManager, getRepository, Repository } from 'typeorm';
import MyOssService from '../../../../basic/my-oss.service';
import * as path from 'path';
import { tmpdir } from 'os';
import { random, uniq } from 'lodash';
import { SecurityService } from '../../../../../libs/config/security.service';
import { UserService } from '../../../../user/user.service';
import { SettingsService } from '../../../../settings/settings.service';
import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../../../../app/app.test.module';
import { BundleTestUtils } from '../../../../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { PermissionByEnum } from '../../../../../libs/enums/PermissionScopeEnum';
import { ExportTestUtils } from '../../../../test_utils_module/export.test.utils';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { DiligenceAnalyzeResponse } from '../../../../diligence/analyze/po/DiligenceAnalyzeResponse';
import { DimensionLevel1Enums } from '../../../../../libs/enums/diligence/DimensionLevel1Enums';
import { FileResultPO } from '../model/FileResultPO';
import { SearchBiddingBatchResultRequest } from '../../../../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { BatchBiddingHelper } from '../../../service/helper/batch.bidding.helper';
import { TenderDimensionExportProcessor } from './tender.dimension.export.processor';
import { DiligenceTenderHistoryEntity } from '../../../../../libs/entities/DiligenceTenderHistoryEntity';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchModule } from '../../../batch.module';
const [testOrgId, testUserId] = generateUniqueTestIds('tender.dimension.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(600 * 1000);
describe.skip('单元测试-招标排查维度详情导出processor', () => {
  let entityManager: EntityManager;
  let myOssService: MyOssService;
  let batchRepository: Repository<BatchEntity>;
  const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));
  let processor: TenderDimensionExportProcessor;
  let securityService: SecurityService;
  let userService: UserService;
  let batchBiddingHelperService: BatchBiddingHelper;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    BundleTestUtils.spy(moduleFixture.get(BundleHelperService));
    myOssService = moduleFixture.get(MyOssService);
    processor = moduleFixture.get(TenderDimensionExportProcessor);
    securityService = moduleFixture.get(SecurityService);
    userService = moduleFixture.get(UserService);
    batchRepository = getRepository(BatchEntity);
    entityManager = batchRepository.manager;
    batchBiddingHelperService = moduleFixture.get(BatchBiddingHelper);
    //真实数据mock
    jest.spyOn(securityService, 'checkScope').mockReturnValue({
      by: PermissionByEnum.ORG,
    });
  });

  afterAll(async () => {
    await ExportTestUtils.clearTestData(entityManager, testUser);
    await entityManager.connection.close();
    //删除临时文件夹
    await ExportTestUtils.rmDirSync(tempDir);
    await batchRepository.manager.connection.close();
  });

  const testTool = {
    //使用真实数据mock
    mockBatchEntities: async (businessType: BatchBusinessTypeEnums): Promise<BatchEntity[]> => {
      const batchEntities = await entityManager.find(BatchEntity, {
        where: {
          businessType,
          orgId: 208,
          status: 2,
          batchType: 0,
        },
        order: {
          batchId: 'DESC',
        },
        take: 2, //限制查询结果数量为 2
      });
      return batchEntities;
    },
    mockAnalyzeResultWithDimension: async (orgId: number, condition: SearchBiddingBatchResultRequest): Promise<DiligenceTenderHistoryEntity[]> => {
      const analyzeResult = await batchBiddingHelperService.getAllTenderDetails(testUser, condition);
      //jest.spyOn(processor.batchBiddingHelper, 'getAllTenderDetails').mockReturnValue(Promise.resolve(analyzeResult));
      return analyzeResult;
    },
    getDimensionHits: async (analyzeResult: DiligenceAnalyzeResponse): Promise<string[]> => {
      const dimensionLevel1Hits = [];
      Object.keys(analyzeResult.aggs).forEach((key) => {
        if (
          Object.values(DimensionLevel1Enums)
            .map((item) => item.toString())
            .includes(key)
        ) {
          if (analyzeResult.aggs[key] > 0) {
            dimensionLevel1Hits.push(key);
          }
        }
      });
      return dimensionLevel1Hits;
    },
    checkExportRowData: async (fileResult: FileResultPO, analyzeResponse: DiligenceTenderHistoryEntity[]) => {
      //解析本地文件之后校验导出内容是否正确
      const rowData = await ExportTestUtils.parseExcel(fileResult, myOssService, tempDir);
      //维度详情导出 rowData的长度表示有多少个 sheet页，每个 sheet页的名称为维度名称，也就是 allDimensions的数值
      expect(rowData.length).toBeGreaterThanOrEqual(1);
      //随机取rowData任意下标来校验导出内容是否正确
      const randomIndex = Math.floor(Math.random() * rowData.length);
      const diligenceRowData = rowData[randomIndex].data.slice(2);
      if (diligenceRowData.length) {
        expect(diligenceRowData.length).toBeGreaterThanOrEqual(1);
        const projectNames = uniq(analyzeResponse.map((item) => item.projectName));
        const projectNos = uniq(analyzeResponse.map((item) => item.projectNo));
        diligenceRowData.forEach((item) => {
          //存在导出维度中无数据的情况(交叉重叠关联中可能存在)
          if (item.length && item[0] && item[1]) {
            expect(projectNames.includes(item[0].toString())).toBe(true);
            expect(projectNos.includes(item[1].toString())).toBe(true);
          }
        });
      }
      return true;
    },
  };

  it('全部维度导出测试 (指定 1001652组织 真实数据导出)', async () => {
    const realUser = getTestUser(1001652, 103027);
    const condition = Object.assign(new SearchBiddingBatchResultRequest(), {
      pageIndex: 1,
      pageSize: 10,
      batchId: 56497,
      dimensionLevelAll: ['risk_inner_blacklist', 'BiddingCompanyRelation'],
      dimensionLevel1: '',
      dimensionLevel2: '',
      status: [0, 1, 2, -1],
    });
    // 带维度信息的分析结果
    const analyzeResponse = await batchBiddingHelperService.getAllTenderDetails(realUser, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, realUser, BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it('共同投标分析导出测试 (指定 208组织 真实数据导出)', async () => {
    const realUser = await userService.getRoverUser(101347);
    const condition = Object.assign(new SearchBiddingBatchResultRequest(), {
      pageIndex: 1,
      pageSize: 10,
      batchId: '40916',
      dimensionLevel1: 'JointBiddingAnalysis',
      dimensionLevel2: '',
      status: [0, 1, 2, -1],
    });
    // 带维度信息的分析结果
    const analyzeResponse = await batchBiddingHelperService.getAllTenderDetails(realUser, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, realUser, BatchBusinessTypeEnums.Tender_Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it('涉采购不良行为 导出测试 (指定208组织真实数据导出)', async () => {
    const realUser = await userService.getRoverUser(101347);
    const testOrgId = 1001652;
    const condition = Object.assign(new SearchBiddingBatchResultRequest(), {
      pageIndex: 1,
      pageSize: 10,
      batchId: 40916,
      dimensionLevel1: 'PurchaseIllegal',
      dimensionLevel2: '',
      status: [0, 1, 2, -1],
    });
    // 带维度信息的分析结果
    const analyzeResponse = await batchBiddingHelperService.getAllTenderDetails(realUser, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, realUser, BatchBusinessTypeEnums.Tender_Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it('投资任职关联 导出测试 (指定1001652组织真实数据导出)', async () => {
    const realUser = await userService.getRoverUser(101347);
    const condition = Object.assign(new SearchBiddingBatchResultRequest(), {
      pageIndex: 1,
      pageSize: 10,
      batchId: 53485,
      dimensionLevel1: 'BiddingCompanyRelation',
      dimensionLevel2: 'BiddingCompanyRelationship2',
      status: [0, 1, 2, -1],
    });
    // 带维度信息的分析结果
    const analyzeResponse = await batchBiddingHelperService.getAllTenderDetails(realUser, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, realUser, BatchBusinessTypeEnums.Tender_Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it('潜在利益冲突 导出测试 (指定1001652组织真实数据导出)', async () => {
    const realUser = await userService.getRoverUser(101347);
    const condition = Object.assign(new SearchBiddingBatchResultRequest(), {
      pageIndex: 1,
      pageSize: 10,
      batchId: 53316,
      dimensionLevel1: 'risk_interest_conflict',
      dimensionLevel2: '',
      status: [0, 1, 2, -1],
    });
    // 带维度信息的分析结果
    const analyzeResponse = await batchBiddingHelperService.getAllTenderDetails(realUser, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, realUser, BatchBusinessTypeEnums.Tender_Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });

  it('内部黑名单 导出测试 (指定1001652组织真实数据导出)', async () => {
    const realUser = await userService.getRoverUser(101347);
    const condition = Object.assign(new SearchBiddingBatchResultRequest(), {
      pageIndex: 1,
      pageSize: 10,
      batchId: 53484,
      dimensionLevel1: 'risk_inner_blacklist',
      dimensionLevel2: 'BlackListInvestigations',
      status: [0, 1, 2, -1],
    });
    // 带维度信息的分析结果
    const analyzeResponse = await batchBiddingHelperService.getAllTenderDetails(realUser, condition);
    const result = await processor.searchDataAndGeneratorFile(condition, realUser, BatchBusinessTypeEnums.Tender_Dimension_Detail_Export);
    expect(result).not.toBeNull();
    const response = await testTool.checkExportRowData(result, analyzeResponse);
    expect(response).toBe(true);
  });
});
