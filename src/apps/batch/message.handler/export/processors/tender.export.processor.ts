import { ExportProcessorBase } from '../export.processor.base';
import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON>ationParams, RoverUser } from '../../../../../libs/model/common';
import { FileResultPO } from '../model/FileResultPO';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { SearchTenderAlertProjectRequest } from '../../../../../libs/model/tenderAlert/SearchTenderAlertProjectRequest';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import { TenderAlertService } from '../../../../tenderAlert/tenderAlert.service';
import { TenderAlertPdfService } from '../../../../tenderAlert/tenderAlert.pdf.service';
import * as Bluebird from 'bluebird';
import { Workbook } from 'exceljs';
import { TenderPdfService } from '../../../../bidding/service/tender.pdf.service';
import { ExportConditionRequest, ExportPDFDetailRequest, ExportRecordPO } from '../model/ExportRecordPO';
import { BatchBiddingHelper } from '../../../service/helper/batch.bidding.helper';
import { SearchBiddingBatchResultRequest } from '../../../../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { BiddingStaticPO } from '../../../model/BiddingStaticPO';
import { getTenderDimensionKeyName } from '../../../../../libs/constants/tender.dimension.constants';
import * as moment from 'moment';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';
import { DiligenceTenderHistoryEntity } from '../../../../../libs/entities/DiligenceTenderHistoryEntity';
import { SearchDiligenceBiddingRequest } from '../../../../../libs/model/bidding/SearchDiligenceBiddingRequest';

@Injectable()
export class TenderExportProcessor extends ExportProcessorBase {
  constructor(
    private readonly tenderAlertService: TenderAlertService,
    private readonly tenderAlertPdfService: TenderAlertPdfService,
    private readonly tenderPDFService: TenderPdfService,
    private readonly batchBiddingHelper: BatchBiddingHelper,
  ) {
    super();
  }

  async searchDataAndGeneratorFile(condition: PaginationParams, user: RoverUser, businessType: BatchBusinessTypeEnums): Promise<FileResultPO> {
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    switch (businessType) {
      case BatchBusinessTypeEnums.Tender_Export: {
        const searchCondition = condition as SearchTenderAlertProjectRequest;
        const searchTenderResponse = await this.tenderAlertService.searchProject(user, searchCondition, true, true);
        const tenderAlertSettings = await this.tenderAlertService.search(user);
        const tenderAlertSetting = tenderAlertSettings.data.find((x) => x.id == searchCondition.settingId);
        searchTenderResponse?.Result.forEach((x) => (x['settingName'] = tenderAlertSetting.name));
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.TenderList]: searchTenderResponse?.Result,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = searchTenderResponse?.Paging.TotalRecords;
        break;
      }
      case BatchBusinessTypeEnums.Tender_Detail_Export: {
        const { id } = condition as ExportPDFDetailRequest;
        const { fileUrl, fileName, previewUrl } = await this.tenderAlertPdfService.generatePdf(id);
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = 1;
        fileResult.previewUrl = previewUrl;
        break;
      }
      case BatchBusinessTypeEnums.Tender_Report_Export: {
        const { orgId, diligenceId } = condition as ExportConditionRequest;
        this.logger.info(`export tender record export id:${diligenceId}`);
        const { fileUrl, fileName, previewUrl } = await this.tenderPDFService.generateTenderPdf(orgId, diligenceId);
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = 1;
        fileResult.previewUrl = previewUrl;
        break;
      }
      case BatchBusinessTypeEnums.Tender_Diligence_Record_Export: {
        Object.assign(condition, { orgId: user.currentOrg });
        const searchRequest = condition as SearchBiddingBatchResultRequest;
        this.logger.info(`export tender record list export batchId:${searchRequest.batchId}`);
        const staticInfo: BiddingStaticPO[] = await this.batchBiddingHelper.getBiddingStatisticsV2(searchRequest);
        const statistics = staticInfo
          .filter((s) => s.key !== 'all')
          .map(({ key, totalHits }) => ({
            key,
            value: totalHits,
          }));
        const recordList = await this.batchBiddingHelper.searchBiddingDetailV2(user, searchRequest);
        if (recordList.data.length) {
          const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
            [ExportEnums.TenderStatic_Export]: statistics,
            [ExportEnums.TenderDetail_Export]: recordList.data,
          });
          fileResult.fileUrl = fileUrl;
          fileResult.fileName = fileName;
          fileResult.recordCount = recordList.data.length;
        }
        break;
      }
      case BatchBusinessTypeEnums.Tender_Diligence_History_Export: {
        Object.assign(condition, { orgId: user.currentOrg });
        const searchRequest = condition as SearchDiligenceBiddingRequest;
        const recordList = await this.batchBiddingHelper.getTenderDiligenceHistory(user, searchRequest);
        if (recordList.length) {
          const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
            [ExportEnums.TenderDetail_Export]: recordList,
          });
          fileResult.fileUrl = fileUrl;
          fileResult.fileName = fileName;
          fileResult.recordCount = recordList.length;
        }
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [
      BatchBusinessTypeEnums.Tender_Export,
      BatchBusinessTypeEnums.Tender_Detail_Export,
      BatchBusinessTypeEnums.Tender_Report_Export,
      BatchBusinessTypeEnums.Tender_Diligence_Record_Export,
      BatchBusinessTypeEnums.Tender_Diligence_History_Export,
    ];
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordPO, workbook: Workbook, condition: PaginationParams): Promise<string> {
    let exportFileName = '';
    const worksheets = workbook.worksheets;
    switch (businessType) {
      case BatchBusinessTypeEnums.Tender_Export: {
        exportFileName = '标讯列表';
        const records = record[ExportEnums.TenderList];
        const tenderTypeMap = new Map([
          ['1', '货物'],
          ['2', '工程'],
          ['3', '服务'],
        ]);
        const contactUnitMap = new Map([
          [1, '招采单位'],
          [2, '中标单位'],
          [3, '代理单位'],
        ]);
        await Bluebird.map(worksheets, async (worksheet) => {
          for (const r of records) {
            r.settingName = r.settingName || '-';
            r.title = r.title || '-';
            r.ifbprogress = r.ifbprogress?.join('|') || '-';
            r.province = r.province || '-';
            r.city = r.city || '-';
            r.district = r.district || '-';
            r.industryv2 = r.industryv2 || '-';
            r.ifbunit = r.ifbunit || '-';
            r.tendertype = tenderTypeMap.get(r.tendertype) || '-';
            r.projectno = r.projectno || '-';
            r.agent = r.agent || '-';
            r.wtbunit = (r?.wtbrecord || []).map(({ WtbComps }) => WtbComps.map((c) => c.Name || '').join('/')).join(',\n');
            r.wtbamt = (r?.wtbrecord || []).map(({ Amt }) => (Amt ? Amt : '未公示')).join(',\n');
            r.wtbinfo =
              r.wtbinfo
                ?.map((x) => x.Name)
                .filter((x) => x)
                .join('；') || '-';
            r.wtbamttotal = r.wtbamttotal || '-';
            const wtbcandidates = r?.wtbcandidate ? r.wtbcandidate.split(',') : [];
            r.wtbcandidate = wtbcandidates.sort((a, b) => a.localeCompare(b)).join(',\n');
            r.teljson =
              r.teljson
                ?.filter((x) => x?.TelNo)
                .map((x) => `${contactUnitMap.get(x.Type) || ''} ${x.Contact || ''} ${x.TelNo}`)
                .join('；') || '-';
            r.publishdate = r.publishdate || '-';
            r.hasattachment = r.hasattachment == 1 ? '是' : '否';
            r.biddingAlert = r.biddingAlert ? '是' : '否';
            r.originalurl = r.originalurl || '-';
            worksheet.addRow({ ...r }).commit();
          }
        });
        break;
      }
      case BatchBusinessTypeEnums.Tender_Diligence_Record_Export: {
        exportFileName = '批量招标排查报告';
        // const TenderResultMap = { '0': '通过', '1': '审慎核实', '2': '不通过', '-1': '稍后核定' };
        await Bluebird.map(worksheets, async (worksheet) => {
          //取出data
          if (worksheet.name === '排查统计') {
            const staticInfo = record[ExportEnums.TenderStatic_Export];
            await Bluebird.map(staticInfo, (r) => {
              //处理statistics
              worksheet.addRow({ riskLevel: getTenderDimensionKeyName()[r.key], riskCount: r.value }).commit();
            });
          }
          if (worksheet.name === '排查结果') {
            const tenderRecordList: DiligenceTenderHistoryEntity[] = record[ExportEnums.TenderDetail_Export];
            // for (const record of tenderRecordList) {
            //   const r: any = { ...record };
            //   r.result = TenderResultMap[record?.remarkResult ?? record?.result];
            //   //处理维度命中通过信息
            //   Object.keys(getTenderDimensionKeyName()).forEach((dimensionKey) => {
            //     const dimensionHit = record.details.dimensionHitsDetails.find((f) => f.key === dimensionKey);
            //     if (dimensionHit) {
            //       r[dimensionKey] = TenderResultMap[dimensionHit.level];
            //     } else {
            //       r[dimensionKey] = '-';
            //     }
            //   });
            //   r.companyList = record.companyList.map((c) => c.companyName).join('\n');
            //   r.editor = record?.editor?.name;
            //   r.createDate = moment(record.createDate).format(DATE_TIME_FORMAT);
            //   worksheet.addRow(r).commit();
            // }
            this.batchBiddingHelper.handleTenderHistory4Export(tenderRecordList).forEach((r) => {
              worksheet.addRow(r).commit();
            });
          }
        });
        break;
      }
      case BatchBusinessTypeEnums.Tender_Diligence_History_Export: {
        exportFileName = '招标排查历史记录';
        this.batchBiddingHelper.handleTenderHistory4Export(record[ExportEnums.TenderDetail_Export]).forEach((r) => {
          worksheets[0].addRow(r).commit();
        });
        break;
      }
      default: {
        this.logger.warn(`applyDataToFile unknown batch business type: ${businessType}`);
      }
    }
    return exportFileName;
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
