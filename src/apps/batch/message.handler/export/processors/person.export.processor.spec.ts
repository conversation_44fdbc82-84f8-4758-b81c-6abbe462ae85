/**
 * person.export.processor.ts unit test
 */
import { EntityManager, getRepository, Repository } from 'typeorm';
import { Test, TestingModule } from '@nestjs/testing';
import { BundleTestUtils } from '../../../../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { ExportTestUtils } from '../../../../test_utils_module/export.test.utils';
import MyOssService from '../../../../basic/my-oss.service';
import xlsx from 'node-xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { tmpdir } from 'os';
import { random } from 'lodash';
import { PersonEntity } from '../../../../../libs/entities/PersonEntity';
import { PersonExportProcessor } from './person.export.processor';
import { SearchPersonModel } from '../../../../../libs/model/person/SearchPersonModel';
import { AppTestModule } from '../../../../app/app.test.module';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchModule } from '../../../batch.module';
import { MyOssMock } from 'apps/test_utils_module/my-oss.mock';
const [testOrgId, testUserId] = generateUniqueTestIds('person.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
const ossMock = new MyOssMock();

jest.setTimeout(300 * 1000);
describe('单元测试-人员导出processor', () => {
  let entityManager: EntityManager;
  let personRepo: Repository<PersonEntity>;
  let myOssService: MyOssService;
  let personExportProcessor: PersonExportProcessor;
  const condition = Object.assign(new SearchPersonModel(), { pageSize: 10, pageIndex: 1 });
  const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    BundleTestUtils.spy(moduleFixture.get(BundleHelperService));
    myOssService = moduleFixture.get(MyOssService);
    personRepo = getRepository(PersonEntity);
    entityManager = personRepo.manager;
    personExportProcessor = moduleFixture.get(PersonExportProcessor);
    await ExportTestUtils.createPersonTestData(personRepo, testUser);
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
  });

  afterAll(async () => {
    await ExportTestUtils.clearTestData(entityManager, testUser);
    await personRepo.delete({ orgId: testUser.currentOrg });
    await entityManager.connection.close();
  });
  it('should be defined', () => {
    expect(personExportProcessor).toBeDefined();
  });

  it('查询人员列表数据并生成文件', async () => {
    Object.assign(condition, {
      orgId: testUser.currentOrg,
    });
    const result = await personExportProcessor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Person_Export);
    expect(result).not.toBeNull();
    const ossFileUrl = ossMock.signSingleUrl(result.fileUrl);
    //创建本地临时文件夹，将ossFileUrl文件下载到本地临时文件夹
    fs.mkdirSync(tempDir);
    const dest = path.join(tempDir, result.fileName);
    await ExportTestUtils.downloadFile(ossFileUrl, dest, ossMock);
    //读取本地临时文件夹文件内容
    expect(ossFileUrl).not.toBeNull();
    //解析本地文件之后校验导出内容是否正确
    const rowData = xlsx.parse(dest);
    expect(rowData.length).toBeGreaterThan(0);
    const [persons, count] = await entityManager.findAndCount(PersonEntity, { orgId: testUser.currentOrg });
    expect(result.recordCount).toEqual(count);
    const excelRowData = rowData[0].data.slice(2, result.recordCount + 2);
    expect(excelRowData.length).toBeGreaterThan(0);
    excelRowData.forEach((item, index) => {
      //导出数据校验
      expect(item[0]).toEqual(persons[index].name);
      expect(item[1]).toEqual(persons[index].personNo);
      expect(item[4]).toEqual(persons[index].phone);
      expect(item[5]).toEqual(persons[index].email);
      expect(item[6]).toEqual(persons[index].companyName);
    });
  });

  it('导出错误的batchBusinessType', async () => {
    const result = await personExportProcessor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Diligence_Report_Export);
    expect(result.recordCount).toEqual(0);
    expect(result.fileUrl).toEqual('');
    expect(result.fileName).toEqual('');
    expect(result.previewUrl).toEqual('');
  });
});
