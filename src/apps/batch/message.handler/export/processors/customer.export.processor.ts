import { ExportProcessorBase } from '../export.processor.base';
import { <PERSON><PERSON><PERSON><PERSON>, RoverUser } from '../../../../../libs/model/common';
import { FileResultPO } from '../model/FileResultPO';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { Injectable } from '@nestjs/common';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { SearchCustomerResponse } from '../../../../../libs/model/customer/SearchCustomerResponse';
import { SearchCustomerModel } from '../../../../../libs/model/customer/SearchCustomerModel';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import { CustomerService } from '../../../../customer/customer.service';
import { GroupType } from '../../../../../libs/model/element/CreateGroupModel';
import { StatusMap } from '../../../../../libs/model/diligence/pojo/model/ModelScorePO';
import { AreaMapping, IndustryMapping } from '@kezhaozhao/qcc-model';
import * as moment from 'moment/moment';
import { DATE_FORMAT, DATE_TIME_FORMAT, EconType, EnterpriseType, TreasuryType } from '../../../../../libs/constants/common';
import { uniq } from 'lodash';
import { StatusCode } from '../../../file.parser/file.export.template';
import * as Bluebird from 'bluebird';
import { Workbook } from 'exceljs';
import { CustomerExportRecordItemPO, ExportConditionRequest, ExportRecordPO } from '../model/ExportRecordPO';
import { formatMoney } from '../../../../../libs/utils/utils';

@Injectable()
export class CustomerExportProcessor extends ExportProcessorBase {
  constructor(private readonly customerService: CustomerService) {
    super();
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordPO, workbook: Workbook, condition: ExportConditionRequest): Promise<string> {
    let exportFileName = '';
    const worksheets = workbook.worksheets;
    switch (businessType) {
      case BatchBusinessTypeEnums.Customer_Export: {
        exportFileName = '第三方列表';
        const records: CustomerExportRecordItemPO[] = record[ExportEnums.CustomerList];
        //获取分组信息并构建成map
        const customerGroupMap = await this.processorHelper.getGroupKV(condition.orgId, GroupType.CustomerGroup);
        const labelKV = await this.processorHelper.getLabelKV(condition.orgId, 1);
        await Bluebird.map(worksheets, async (worksheet) => {
          for (const record of records) {
            const r: any = { ...record };
            r.creator = record?.creator?.name || '-';
            r.result = StatusMap[record?.result];
            r.label = labelKV[record.customerId] || '-';
            const industryKey = [record.industry1, record.industry2, record.industry3, record.industry4].filter((d) => d).join('_');
            const area = [AreaMapping[record?.province], AreaMapping[record?.city], AreaMapping[record?.district]].filter((d) => d).join('/');
            r.industry = IndustryMapping[industryKey] || '-';
            r.group = customerGroupMap[record.groupId] || '-';
            r.createDate = moment(record.createDate, moment.ISO_8601).format(DATE_TIME_FORMAT) || '-';
            r.updateDate = moment(record.updateDate, moment.ISO_8601).format(DATE_TIME_FORMAT) || '-';
            r.area = area || '-';
            r.creditQuota = formatMoney(record?.creditQuota);
            r.contactQuota = formatMoney(record?.contactQuota);
            r.registcapi = record?.registcapi?.replace('人民币', '');
            r.reccap = record?.reccap?.replace('人民币', '');
            const revenue = record?.companyRevenue ? Number(record.companyRevenue) : 0;
            r.companyRevenue = formatMoney(revenue / 10000);
            r.econType = uniq(
              record?.econType?.split(',').map((et) => {
                return EconType[et] || '其他';
              }),
            )
              ?.filter((d) => d)
              ?.join(',');
            r.treasuryType = uniq(
              record?.treasuryType?.split(',').map((et) => {
                const type = Object.values(TreasuryType).find((t) => t.code === et);
                return type?.name || '其他';
              }),
            )
              ?.filter((d) => d)
              ?.join(',');
            r.enterpriseType = uniq(
              record?.enterpriseType?.split(',').map((et) => {
                return EnterpriseType[et] || '其他';
              }),
            )
              ?.filter((d) => d)
              ?.join(',');
            r.startDateCode = record?.startDateCode ? moment(record.startDateCode, moment.ISO_8601).format(DATE_FORMAT) : '-';
            r.registrationStatus = record?.statusCode ? StatusCode[record.statusCode] : '-';
            r.listStatus = record?.listStatus == 1 ? '已上市' : '未上市';
            r.contactName = record?.contacts?.map((c) => c.name).join('\n') || '-';
            r.phone = record?.contacts?.map((c) => c.phone).join('\n') || '-';
            r.email = record?.contacts?.map((c) => c.email).join('\n') || '-';
            r.originalName = record?.originalName?.join('、') || '-';
            Object.keys(r).forEach((rk) => {
              r[rk] = r[rk] || '-';
            });
            worksheet.addRow({ ...r }).commit();
          }
        });
        break;
      }
      default: {
        this.logger.warn(`applyDataToFile unknown batch business type: ${businessType}`);
      }
    }
    return exportFileName;
  }

  async searchDataAndGeneratorFile(condition: PaginationParams, user: RoverUser, businessType: BatchBusinessTypeEnums): Promise<FileResultPO> {
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    switch (businessType) {
      case BatchBusinessTypeEnums.Customer_Export: {
        const searchCustomerResponse: SearchCustomerResponse = await this.customerService.search(
          user,
          Object.assign(new SearchCustomerModel(), condition),
          true,
        );
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.CustomerList]: searchCustomerResponse?.data,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = searchCustomerResponse?.total;
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Customer_Export];
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
