import { ExportProcessorBase } from '../export.processor.base';
import { Injectable } from '@nestjs/common';
import { PaginationResponse, RoverUser } from '../../../../../libs/model/common';
import { FileResultPO } from '../model/FileResultPO';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { RiskDynamicsSearchRequest } from '../../../../../libs/model/monitor/request/RiskDynamicsSearchRequest';
import { compact, flatten } from 'lodash';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import * as Bluebird from 'bluebird';
import { MonitorRiskDynamicService } from '../../../../monitor/risk/monitor.risk.dynamic.service';
import { RiskLevelConst } from '../../../../../libs/model/diligence/pojo/dimension/DimensionStrategyPO';
import { MonitorDynamicMap } from '../../../../../libs/enums/monitor/MonitorRiskEnums';
import { Workbook } from 'exceljs';
import { ExportConditionBase, ExportRecordPO, RiskExportRecordItemPO } from '../model/ExportRecordPO';
import { dateTransform } from '../../../../../libs/utils/date.utils';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';

@Injectable()
export class RiskExportProcessor extends ExportProcessorBase {
  constructor(public readonly riskDynamicService: MonitorRiskDynamicService) {
    super();
  }

  async searchDataAndGeneratorFile(condition: ExportConditionBase, user: RoverUser, businessType: BatchBusinessTypeEnums): Promise<FileResultPO> {
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    switch (businessType) {
      case BatchBusinessTypeEnums.Risk_Export: {
        //一次最多 1000条
        const searchRiskResponse = await this.riskDynamicService.searchMonitorRiskDynamics(
          user,
          Object.assign(new RiskDynamicsSearchRequest(), condition, { pageIndex: 1, pageSize: 500 }),
        );
        const exportData: RiskExportRecordItemPO[] = [];
        if (searchRiskResponse?.data) {
          exportData.push(...searchRiskResponse.data);
        }
        const promises = [];
        if (searchRiskResponse?.total >= 500) {
          for (let i = 2; i <= Math.floor(searchRiskResponse?.total / 500) + 1; i++) {
            promises.push(
              this.riskDynamicService.searchMonitorRiskDynamics(
                user,
                Object.assign(new RiskDynamicsSearchRequest(), condition, { pageIndex: i, pageSize: 500 }),
              ),
            );
          }
        }
        //分批查询
        const promiseAll: [PaginationResponse] = await Bluebird.all(promises);
        exportData.push(...compact(flatten(promiseAll?.map((m) => m?.data))));
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.Risk_Export]: exportData,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = searchRiskResponse?.total;
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Risk_Export];
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordPO, workbook: Workbook, condition: ExportConditionBase): Promise<string> {
    let exportFileName = '';
    const worksheets = workbook.worksheets;
    switch (businessType) {
      case BatchBusinessTypeEnums.Risk_Export: {
        exportFileName = '合作监控-风险动态列表';
        const records = record[ExportEnums.Risk_Export];
        //获取 风险 key name 映射
        const riskDimensionKV = await this.settingService.getMonitorDimensionKV();
        await Bluebird.map(worksheets, async (worksheet) => {
          for (const record of records) {
            const r: any = { ...record };
            r.dimensionLevel = RiskLevelConst[record.dimensionLevel] || '-';
            r.dimension = riskDimensionKV[record?.dimensionKey];
            r.content = record?.detail?.Content;
            r.group = r?.group?.name || '-';
            r.status = MonitorDynamicMap[r?.status];
            r.riskCreateDate = dateTransform(r?.riskCreateDate, DATE_TIME_FORMAT);
            worksheet.addRow({ ...r }).commit();
          }
        });
        break;
      }
      default: {
        this.logger.warn(`applyDataToFile unknown batch business type: ${businessType}`);
      }
    }
    return exportFileName;
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
