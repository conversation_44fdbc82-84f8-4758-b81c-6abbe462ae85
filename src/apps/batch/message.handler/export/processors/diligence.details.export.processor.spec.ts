import { En<PERSON>tyMana<PERSON>, getRepository, Repository } from 'typeorm';
import MyOssService from '../../../../basic/my-oss.service';
import * as path from 'path';
import { tmpdir } from 'os';
import { random, uniq } from 'lodash';
import { Test, TestingModule } from '@nestjs/testing';
import { BundleTestUtils } from '../../../../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { ExportTestUtils } from '../../../../test_utils_module/export.test.utils';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { PermissionByEnum } from '../../../../../libs/enums/PermissionScopeEnum';
import { SecurityService } from '../../../../../libs/config/security.service';
import { UserService } from '../../../../user/user.service';
import { DiligenceExportProcessor } from './diligence.export.processor';
import { AnalyzedCompanySearchedRequest } from '../../../../diligence/analyze/po/AnalyzedCompanySearchedRequest';
import { DiligenceAnalyzeService } from '../../../../diligence/analyze/diligence.analyze.service';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { DiligenceAnalyzeResponse } from '../../../../diligence/analyze/po/DiligenceAnalyzeResponse';
import { SettingsService } from '../../../../settings/settings.service';
import { DimensionLevel1Enums } from '../../../../../libs/enums/diligence/DimensionLevel1Enums';
import { RiskLevelDescription } from '../../../../../libs/model/diligence/pojo/dimension/DimensionStrategyPO';
import { DiligenceHistoryService } from '../../../../diligence/details/diligence.history.service';
import { DiligenceHistoryRequest } from '../../../../../libs/model/diligence/pojo/history/DiligenceHistoryRequest';
import { AppTestModule } from '../../../../app/app.test.module';
import { DiligenceAnalyzeResponseItemPO } from '../../../../diligence/analyze/po/DiligenceAnalyzeResponseItemPO';
import { fetchAll } from '../../../../../libs/utils/utils';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchModule } from '../../../batch.module';
import { BatchMessageService } from '../../batch.message.service';
import { DiligenceSnapshotService } from '../../../../diligence/snapshot/diligence.snapshot.service';
import { SnapshotQueueTypeEnums } from '../../../../diligence/snapshot/po/SnapshotQueueTypeEnums';
import { BatchService } from '../../../service/batch.service';
import { BatchBaseHelper } from '../../../service/helper/batch.base.helper';
import { BatchMessageHandlerDiligence } from '../../diligence/batch.message.handler.diligence';
import { BatchTestUtils, sleep } from '../../../../test_utils_module/batch.test.utils';
import { MyOssMock } from '../../../../test_utils_module/my-oss.mock';
const [testOrgId, testUserId] = generateUniqueTestIds('diligence.details.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
const ossMock = new MyOssMock();

/**
 * 单元测试
 */
jest.setTimeout(300 * 1000);

describe('集成测试-风险排查详情导出processor', () => {
  let entityManager: EntityManager;
  let myOssService: MyOssService;
  const condition = Object.assign(new AnalyzedCompanySearchedRequest(), {
    pageSize: 10,
    pageIndex: 1,
    aggsInfo: 1,
    diligenceInfo: 1,
  });
  const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));
  let batchRepo: Repository<BatchEntity>;
  let processor: DiligenceExportProcessor;
  let securityService: SecurityService;
  let userService: UserService;
  let diligenceAnalyzeService: DiligenceAnalyzeService;
  let settingService: SettingsService;
  let diligenceHistoryService: DiligenceHistoryService;
  let batchMessageService: BatchMessageService;
  let batchMessageHandlerDiligence: BatchMessageHandlerDiligence;
  let batchBaseHelper: BatchBaseHelper;
  let snapshotService: DiligenceSnapshotService;
  let batchService: BatchService;

  let batchEntity: BatchEntity;

  const testTool = {
    //创建真实的批量排查任务、执行尽调生产快照，
    mockBatchDiligence: async (companyData: { companyId: string; companyName: string }[]): Promise<BatchEntity> => {
      jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
        return ossMock.putSteam(ossObject, filepath, options);
      });
      jest.spyOn(batchMessageHandlerDiligence.batchDiligenceQueue, 'sendMessageV2').mockImplementation(async (msg) => {
        await sleep(2);
        batchMessageHandlerDiligence.handleJobMessage(msg);
      });

      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation(async (msg) => {
        await sleep(0.2);
        batchMessageService.processJobMonitorMessage(msg);
      });

      jest.spyOn(userService, 'getRoverUser').mockResolvedValue(testUser);
      // mock 消费生成快照消息
      jest.spyOn(snapshotService.snapshotBatchQueue, 'sendMessageV2').mockImplementation((msg) => {
        return snapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.BatchDiligence);
      });
      const batchEntity = await batchService.createBatchDiligenceTask(testUser, companyData, BatchBusinessTypeEnums.Diligence_File);

      // 4. 等待批量任务完成并验证状态
      const currentBatch = await BatchTestUtils.waitForBatchCompletion(entityManager, batchEntity.batchId);
      return currentBatch;
    },
    spy: async (orgId: number): Promise<DiligenceAnalyzeResponse> => {
      jest.spyOn(securityService, 'checkScope').mockReturnValue({
        by: PermissionByEnum.ORG,
      });
      const analyzeResult = await diligenceAnalyzeService.analyze(condition, orgId, true);
      analyzeResult.total = analyzeResult.data.length;
      jest.spyOn(processor.diligenceAnalyzeService, 'analyze').mockReturnValue(Promise.resolve(analyzeResult));
      return analyzeResult;
    },
    getDimensionHit: (analyzeData: DiligenceAnalyzeResponseItemPO, key: string) => {
      const changes = analyzeData.changes;
      const changesItemPOS = changes.filter((c) => c.dimensionLevel1 == key);
      if (changesItemPOS?.length > 0) {
        let currentHitValueSum = 0;
        let increasedValueSum = 0;
        changesItemPOS.forEach((changeItem) => {
          const { currentHitValue, increasedValue } = changeItem;
          currentHitValueSum += currentHitValue;
          increasedValueSum += increasedValue;
        });
        let value = '';
        if (increasedValueSum > 0) {
          value = `（+${increasedValueSum}）`;
        }
        return `${currentHitValueSum} ${value}`;
      } else {
        return analyzeData?.diligenceInfo?.details?.dimensionScoreDetails?.find((e) => e.groupKey === key)?.totalHits || 0;
      }
    },
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    BundleTestUtils.spy(moduleFixture.get(BundleHelperService));
    myOssService = moduleFixture.get(MyOssService);
    processor = moduleFixture.get(DiligenceExportProcessor);
    securityService = moduleFixture.get(SecurityService);
    userService = moduleFixture.get(UserService);
    diligenceAnalyzeService = moduleFixture.get(DiligenceAnalyzeService);
    settingService = moduleFixture.get(SettingsService);
    diligenceHistoryService = moduleFixture.get(DiligenceHistoryService);
    batchRepo = getRepository(BatchEntity);
    entityManager = batchRepo.manager;

    batchMessageService = moduleFixture.get(BatchMessageService);
    batchMessageHandlerDiligence = moduleFixture.get(BatchMessageHandlerDiligence);
    batchBaseHelper = moduleFixture.get(BatchBaseHelper);
    snapshotService = moduleFixture.get(DiligenceSnapshotService);
    batchService = moduleFixture.get(BatchService);

    batchEntity = await testTool.mockBatchDiligence([
      {
        companyId: '6c7108d628fc21216e5bc48bb035b61c',
        companyName: '上海米哈游网络科技股份有限公司',
      },
      {
        companyId: '4e3fb77a20a0f7b777e6cd70969101f3',
        companyName: '江西省保灵动物保健品有限公司',
      },
      {
        companyId: 'a52a3447c7a6df44c8a3f8698b572dc9',
        companyName: '成都钱乾信息技术有限公司',
      },
    ]);
  });

  afterAll(async () => {
    // await ExportTestUtils.clearTestData(entityManager, testUser);
    await BatchTestUtils.clearTestDiligenceData(entityManager, testOrgId);
    //删除临时文件夹
    await ExportTestUtils.rmDirSync(tempDir);
    jest.clearAllMocks();
    await entityManager.connection.close();
  });

  // beforeEach(() => {
  //   jest.clearAllMocks();
  // });

  it('批量排查-公司列表概览导出 Diligence_Batch_Detail', async () => {
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
    condition.batchIdCurrent = batchEntity.batchId;
    condition.batchIdPrevious = -1;
    //真实数据mock
    const analyzeResult = await testTool.spy(testOrgId);
    const dimensionKV = await settingService.getDimensionKV(testOrgId);
    const dimensionStaticInfo = {};
    Object.keys(analyzeResult.aggs).forEach((key) => {
      if (
        Object.values(DimensionLevel1Enums)
          .map((item) => item.toString())
          .includes(key)
      ) {
        dimensionStaticInfo[dimensionKV[key]] = {
          key,
          dimensionName: dimensionKV[key],
          countValue: analyzeResult.aggs[key],
        };
      }
    });
    Object.assign(condition, { orgId: testUser.currentOrg });
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Diligence_Batch_Detail);
    expect(result).not.toBeNull();
    //解析本地文件之后校验导出内容是否正确
    const rowData = await ExportTestUtils.parseExcel(result, ossMock, tempDir);
    expect(rowData.length).toBe(2);
    const staticRowData = rowData[0].data.slice(3, result.recordCount + 3);
    const diligenceRowData = rowData[1].data.slice(3, result.recordCount + 3);
    expect(staticRowData.length).toBeGreaterThan(0);
    expect(diligenceRowData.length).toBeGreaterThan(0);
    staticRowData.forEach((item) => {
      expect(item[0]).toEqual(dimensionStaticInfo[item[0].toString()].dimensionName);
      expect(item[1]).toEqual(dimensionStaticInfo[item[0].toString()].countValue);
    });
    diligenceRowData.forEach((item, index) => {
      const analyzeData = analyzeResult.data[index];
      expect(item[0]).toEqual(analyzeData.companyName);
      expect(item[1]).toEqual(analyzeData.companyDetail.creditcode);
      expect(item[2]).toEqual(RiskLevelDescription[analyzeData?.diligenceInfo.result]);
      expect(item[3]).toEqual(analyzeData?.diligenceInfo.creditRate || '-');
      expect(Number(item[5])).toEqual(analyzeData?.diligenceInfo['risk_base_info']);
      expect(Number(item[6])).toEqual(analyzeData?.diligenceInfo['risk_legal']);
      expect(Number(item[7])).toEqual(analyzeData?.diligenceInfo['risk_administrative_supervision']);
      expect(Number(item[8])).toEqual(analyzeData?.diligenceInfo['risk_operate_stability']);
      expect(Number(item[9])).toEqual(analyzeData?.diligenceInfo['risk_negative_news']);
      expect(Number(item[10])).toEqual(analyzeData?.diligenceInfo['risk_partner_investigation']);
      expect(Number(item[11])).toEqual(analyzeData?.diligenceInfo['risk_inner_blacklist']);
      expect(Number(item[12])).toEqual(analyzeData?.diligenceInfo['risk_outer_blacklist']);
      expect(Number(item[13])).toEqual(analyzeData?.diligenceInfo['risk_interest_conflict']);
      expect(item[14]).toEqual(analyzeData?.diligenceInfo?.editor?.name);
      // expect(item[14]).toEqual(moment(analyzeData.createDate).format(DATE_TIME_FORMAT));
    });
  });

  it('风险巡检-公司列表概览导出Analyze_Record_Export', async () => {
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });

    const preBatch = await testTool.mockBatchDiligence([
      {
        companyId: '6c7108d628fc21216e5bc48bb035b61c',
        companyName: '上海米哈游网络科技股份有限公司',
      },
      {
        companyId: '4e3fb77a20a0f7b777e6cd70969101f3',
        companyName: '江西省保灵动物保健品有限公司',
      },
    ]);

    condition.batchIdCurrent = batchEntity.batchId;
    condition.batchIdPrevious = preBatch.batchId;
    //真实数据mock
    const analyzeResult = await testTool.spy(testOrgId);
    Object.assign(condition, { orgId: testUser.currentOrg });
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Analyze_Record_Export);
    expect(result).not.toBeNull();
    //解析本地文件之后校验导出内容是否正确
    const rowData = await ExportTestUtils.parseExcel(result, ossMock, tempDir);
    expect(rowData.length).toBe(1);
    const diligenceRowData = rowData[0].data.slice(3, result.recordCount + 3);
    expect(diligenceRowData.length).toBeGreaterThan(0);
    diligenceRowData.forEach((item, index) => {
      const analyzeData = analyzeResult.data[index];
      expect(item[0]).toEqual(analyzeData.companyName);
      expect(item[1]).toEqual(analyzeData.companyDetail.creditcode);
      expect(item[2]).toEqual(analyzeData.companyDetail.registrationStatus);
      expect(item[3]).toEqual(analyzeData.companyDetail.startDateCode);
      expect(item[4]).toEqual(analyzeData.companyDetail.registcapi);
      expect(item[5]).toEqual(analyzeData.companyDetail.econkind);
      expect(item[6]).toEqual(analyzeData.companyDetail.scale);
      expect(item[7]).toEqual(analyzeData.companyDetail.area);
      expect(item[8]).toEqual(analyzeData.companyDetail.address);
      expect(item[9]).toEqual(analyzeData.companyDetail.industry);
      expect(item[10]).toEqual(analyzeData.companyDetail.companyRevenue);
      expect(item[11]).toEqual(analyzeData.companyDetail.insuredcount);
      expect(item[12]).toEqual(RiskLevelDescription[analyzeData?.diligenceInfo.result]);
      expect(item[13]).toEqual(analyzeData.customerLabelNames);
      expect(item[14]).toEqual(analyzeData?.diligenceInfo.creditRate || '-');
      expect(item[15]).toEqual(analyzeData.customerGroupNames);
      expect(item[16]).toEqual(analyzeData.customerDepNames);
      expect(item[18]).toEqual(testTool.getDimensionHit(analyzeData, 'risk_base_info'));
      expect(item[19]).toEqual(testTool.getDimensionHit(analyzeData, 'risk_legal'));
      expect(item[20]).toEqual(testTool.getDimensionHit(analyzeData, 'risk_administrative_supervision'));
      expect(item[21]).toEqual(testTool.getDimensionHit(analyzeData, 'risk_operate_stability'));
      expect(item[22]).toEqual(testTool.getDimensionHit(analyzeData, 'risk_negative_news'));
      expect(item[23]).toEqual(testTool.getDimensionHit(analyzeData, 'risk_partner_investigation'));
      expect(item[24]).toEqual(testTool.getDimensionHit(analyzeData, 'risk_inner_blacklist'));
      expect(item[25]).toEqual(testTool.getDimensionHit(analyzeData, 'risk_outer_blacklist'));
      expect(item[26]).toEqual(testTool.getDimensionHit(analyzeData, 'risk_interest_conflict'));
      expect(item[27]).toEqual(analyzeData?.diligenceInfo?.editor?.name);
    });
  });

  it('排查记录列表导出 Diligence_Record', async () => {
    jest.spyOn(securityService, 'checkScope').mockReturnValue({
      by: PermissionByEnum.ORG,
    });
    // const realUser = await userService.getRoverUser(6893);
    const diligenceHistories = await diligenceHistoryService.search(testUser, Object.assign(new DiligenceHistoryRequest(), condition));
    diligenceHistories.total = 10; //限制查询结果数量为 10
    jest.spyOn(processor.diligenceHistoryService, 'search').mockReturnValue(Promise.resolve(diligenceHistories));
    Object.assign(condition, { orgId: testUser.currentOrg });
    const result = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Diligence_Record);
    expect(result).not.toBeNull();
    //解析本地文件之后校验导出内容是否正确
    const rowData = await ExportTestUtils.parseExcel(result, ossMock, tempDir);
    expect(rowData.length).toBe(1);
    const diligenceRowData = rowData[0].data.slice(3, result.recordCount + 3);
    expect(diligenceRowData.length).toBeGreaterThan(0);
    diligenceRowData.forEach((item, index) => {
      const analyzeData = diligenceHistories.data[index];
      expect(item[0]).toEqual(analyzeData.name);
      expect(item[1]).toEqual(analyzeData.creditcode);
      expect(item[2]).toEqual(RiskLevelDescription[analyzeData?.result]);
      if (analyzeData?.creditRate) {
        expect(item[3]).toEqual(analyzeData?.creditRate);
      }
      // expect(Number(item[5])).toEqual(analyzeData['risk_base_info'] || undefined);
      // expect(Number(item[6])).toEqual(analyzeData['risk_legal'] || undefined);
      // expect(Number(item[7])).toEqual(analyzeData['risk_administrative_supervision'] || undefined);
      // expect(Number(item[8])).toEqual(analyzeData['risk_operate_stability'] || undefined);
      // expect(Number(item[9])).toEqual(analyzeData['risk_negative_news'] || undefined);
      // expect(Number(item[10])).toEqual(analyzeData['risk_partner_investigation'] || undefined);
      // expect(Number(item[11])).toEqual(analyzeData['risk_inner_blacklist'] || undefined);
      // expect(Number(item[12])).toEqual(analyzeData['risk_outer_blacklist'] || undefined);
      // expect(Number(item[13])).toEqual(analyzeData['risk_interest_conflict'] || undefined);
      expect(item[14]).toEqual(analyzeData?.editor?.name);
    });
  });

  it.skip('debug-fetchAll', async () => {
    const analyzeData: DiligenceAnalyzeResponseItemPO[] = await fetchAll(
      diligenceAnalyzeService.analyze.bind(diligenceAnalyzeService),
      [],
      {
        pageSize: 100,
        pageIndex: 1,
        batchIdCurrent: 15660,
        batchIdPrevious: -1,
        onlyChangedCompany: 0,
        diligenceInfo: 1,
        aggsInfo: 1,
      },
      1001652,
      true,
    );
    const companyNames = uniq(analyzeData.map((item) => item.companyName));
    expect(companyNames.length).toBe(analyzeData.length);
  });
});
