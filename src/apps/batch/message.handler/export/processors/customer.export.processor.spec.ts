/**
 * customer.export.processor.ts unit test
 */
import { CustomerExportProcessor } from './customer.export.processor';
import { EntityManager, getRepository, Repository } from 'typeorm';
import { Test, TestingModule } from '@nestjs/testing';
import { SearchCustomerModel } from '../../../../../libs/model/customer/SearchCustomerModel';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { CustomerEntity } from '../../../../../libs/entities/CustomerEntity';
import { ExportTestUtils } from '../../../../test_utils_module/export.test.utils';
import MyOssService from '../../../../basic/my-oss.service';
import xlsx from 'node-xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { tmpdir } from 'os';
import { random } from 'lodash';
import { AppTestModule } from '../../../../app/app.test.module';
import { BatchModule } from '../../../batch.module';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { MyOssMock } from '../../../../test_utils_module/my-oss.mock';
const [testOrgId, testUserId] = generateUniqueTestIds('customer.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
const ossMock = new MyOssMock();

jest.setTimeout(60 * 1000);
describe('单元测试-第三方列表导出processor', () => {
  let entityManager: EntityManager;
  let customerRepo: Repository<CustomerEntity>;
  let myOssService: MyOssService;
  let customerExportProcessor: CustomerExportProcessor;
  const condition = Object.assign(new SearchCustomerModel(), { pageSize: 10, pageIndex: 1 });
  const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    myOssService = moduleFixture.get(MyOssService);
    customerRepo = getRepository(CustomerEntity);
    entityManager = customerRepo.manager;
    customerExportProcessor = moduleFixture.get(CustomerExportProcessor);
    await ExportTestUtils.clearTestData(entityManager, testUser);
    await ExportTestUtils.createCompanyTestData(customerRepo.manager);
    await ExportTestUtils.createCustomerTestData(customerRepo, testUser);
  });

  afterAll(async () => {
    await ExportTestUtils.clearTestData(entityManager, testUser);
    await customerRepo.delete({ orgId: testUser.currentOrg });
    await entityManager.connection.close();
  });

  it('查询第三方列表数据并生成文件', async () => {
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
    Object.assign(condition, {
      orgId: testUser.currentOrg,
    });
    const result = await customerExportProcessor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Customer_Export);
    expect(result).not.toBeNull();
    const ossFileUrl = ossMock.signSingleUrl(result.fileUrl);
    //创建本地临时文件夹，将ossFileUrl文件下载到本地临时文件夹
    fs.mkdirSync(tempDir);
    const dest = path.join(tempDir, result.fileName);
    await ExportTestUtils.downloadFile(ossFileUrl, dest, ossMock);
    //读取本地临时文件夹文件内容
    expect(ossFileUrl).not.toBeNull();
    //解析本地文件之后校验导出内容是否正确
    const rowData = xlsx.parse(dest);
    expect(rowData.length).toBeGreaterThan(0);
    const [customers, count] = await entityManager.findAndCount(CustomerEntity, { orgId: testUser.currentOrg });
    expect(result.recordCount).toEqual(count);
    const excelRowData = rowData[0].data.slice(3, result.recordCount + 3);
    expect(excelRowData.length).toBeGreaterThan(0);
    excelRowData.forEach((item, index) => {
      expect(item[0]).toEqual(customers[index].name);
    });
  });

  it('导出错误的batchBusinessType', async () => {
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
    const result = await customerExportProcessor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Diligence_Report_Export);
    expect(result.recordCount).toEqual(0);
    expect(result.fileUrl).toEqual('');
    expect(result.fileName).toEqual('');
    expect(result.previewUrl).toEqual('');
  });
});
