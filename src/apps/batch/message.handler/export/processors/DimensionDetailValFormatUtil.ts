import { flatten, minBy, uniq } from 'lodash';
import { RelationTypeConst } from '../../../../../libs/constants/model.constants';
import { DetailsParamEnums } from '../../../../../libs/enums/diligence/DetailsParamEnums';
import { OptionsObject } from '../model/OptionsObject';
import {
  DimensionHitDetailsBlacklistPartnerInvestigation,
  DimensionHitDetailsCustomerPartnerInvestigation,
} from '../../../../../libs/model/diligence/pojo/req&res/details/response';
import { mergeRelations, mergeRelationsV2, processRelationRoles } from '../model/path-node/data-node-utils';
import { CompanyAnalyzedV2PO } from '../../../../../libs/model/diligence/pojo/graph/CompanyAnalyzedV2PO';
import { RelationTypeMapV2 } from '../model/path-node/data-node';
import { convertSuspectPathToGraph } from 'apps/diligence/pdf.utils/table-columns.config';
import { InvestigationConnectionPO } from 'libs/model/diligence/pojo/graph/InvestigationConnectionPO';
import { processPaths } from '../../../../data/utils/path.util';

export const pathParser = (left: string, right: string, middle: string, leftInfo: string, rightInfo: string, options: OptionsObject) => {
  const leftNode = left ? `${left}` : '';
  const rightNode = right ? `${right}` : '';
  const middleNode = middle ? `${middle}` : '';

  const createLine = (content: string, direction: 'left' | 'right') => {
    let output = content;
    // 特殊字段处理
    if (['stockpercent'].indexOf(options[`${direction}Info`]) > -1) {
      output = `${content}%`;
    }
    output = content ? `（${output}）` : '';
    return `${direction === 'left' ? '←' : ''}${output}${direction === 'right' ? '→' : ''}`;
  };

  return `${leftNode}${leftNode ? createLine(leftInfo, 'left') : ''}${middleNode}${rightNode ? createLine(rightInfo, 'right') : ''}${rightNode}`;
};

//处理交叉重叠关系数据
export const getMergedResult = (sourceData: InvestigationConnectionPO[] | CompanyAnalyzedV2PO[]) => {
  sourceData.forEach((c: CompanyAnalyzedV2PO) => {
    //把 relation 统一处理为 node，edge 结构，并根据路径优先级需求过滤高优先级路径
    if (!c?.relationPaths?.length) return;
    c.relations = processPaths(c.relationPaths.map((m) => processRelationRoles(m)));
  });
  return mergeRelations(sourceData) as CompanyAnalyzedV2PO[];
};
export const getMergedResultV2 = (sourceData: InvestigationConnectionPO[] | CompanyAnalyzedV2PO[]) => {
  sourceData.forEach((c: CompanyAnalyzedV2PO) => {
    //把 relation 统一处理为 node，edge 结构，并根据路径优先级需求过滤高优先级路径
    if (!c?.relationPaths?.length) return;
    c.relations = processPaths(c.relationPaths.map((m) => processRelationRoles(m)));
  });
  return mergeRelationsV2(sourceData) as CompanyAnalyzedV2PO[];
};

const getRoleName = (str) => {
  return str?.split(',')?.map((role) => RelationTypeMapV2[role.toLowerCase()]) || [];
};
//生成路径V2版本
export const generateControlPaths = (relations: any[][]): string[] => {
  const paths: string[] = [];

  for (const relation of relations) {
    let path = '';

    for (let i = 0; i < relation.length; i++) {
      const element = relation[i];
      if (element.type === 'node') {
        path += (i > 0 ? '-' : '') + element.name;
      } else if (element.type === 'edge') {
        const directionArrow = element.direction === 'left' ? '<-' : '->';
        // 当前edge上的角色，优先取roles，如果没有就roleType匹配
        const edgeRole = element.roles?.length ? element.roles : getRoleName(element.roleType);
        const groupRoles = (element?.groups ?? []).flatMap((group) => {
          if (group.roles.length > 0) {
            return group.roles;
          } else {
            return getRoleName(group.role || group.roleType);
          }
        });
        const roles = uniq([...edgeRole, ...groupRoles]).join(',');
        path += `${directionArrow}(${roles})${directionArrow}`;
      }
    }

    paths.push(path);
  }

  return paths;
};

export const processRelationTypes = (
  r: DimensionHitDetailsBlacklistPartnerInvestigation | DimensionHitDetailsCustomerPartnerInvestigation | CompanyAnalyzedV2PO,
): any[] => {
  const responseList: any[] = [];
  if (r['version'] === 'V2') {
    //V2版本数据需要使用新的方法处理字段
    return processRelationTypesV2(r as CompanyAnalyzedV2PO);
  }

  const historyMap = {
    HISEMPLOY: '历史高管',
    HISLEGAL: '历史法人',
    HISINVEST: '历史股东',
  };
  const processRelationType = (rp: any) => {
    const { relationType, typeDD, roleDD, typeRelated, roleRelated, stockpercent, personName, companyNameDD, companyNameRelated } = rp;
    if (rp?.details) {
      return {
        ...rp,
        relationType: '实际控制人',
        pathDetail: `${rp.sourceCompanyName}←${relationType}-${rp.name}-${relationType}→${rp.companyName}`,
        companyNameDD: rp.sourceCompanyName,
        companyNameRelated: rp.companyName,
      };
    }
    if (relationType === RelationTypeConst[DetailsParamEnums.MainInfoUpdateBeneficiary].replace('相同', '')) {
      return {
        ...rp,
        relationType: RelationTypeConst[DetailsParamEnums.MainInfoUpdateBeneficiary],
        pathDetail: `${companyNameDD}←(受益所有人)-${personName}-(受益所有人)→${companyNameRelated}`,
      };
    }
    if (relationType === RelationTypeConst[DetailsParamEnums.Branch]) {
      return {
        ...rp,
        pathDetail: `${companyNameDD}-${RelationTypeConst[DetailsParamEnums.Branch]}→${companyNameRelated}`,
      };
    }
    const leftInfo = historyMap[typeDD] || roleDD;
    const rightInfo = historyMap[typeRelated] || roleRelated;
    return {
      ...rp,
      relationType,
      pathDetail:
        leftInfo || rightInfo
          ? pathParser(companyNameDD, companyNameRelated, personName, leftInfo, rightInfo, {
              left: 'companyNameDD',
              leftInfo: 'leftInfo',
              middle: 'personName',
              rightInfo: 'rightInfo',
              right: 'companyNameRelated',
            })
          : `${companyNameDD}-${stockpercent}%→${companyNameRelated}`,
    };
  };

  r.relationTypes?.forEach((rp) => responseList.push(processRelationType(rp)));

  return responseList;
};

export const processRelationTypesV2 = (sourceDataList: CompanyAnalyzedV2PO): any[] => {
  const responseList: any[] = [];
  const mergedResult = getMergedResult([sourceDataList]);
  //mergedResult 只会有一条记录
  const currentRecord = mergedResult[0];
  // 优先取relations2，有权重差别
  let minPath = [];
  if (currentRecord.relations2) {
    minPath = currentRecord.relations2;
  } else {
    minPath = currentRecord.shortestPath
      ? currentRecord.shortestPath
      : currentRecord.relations.filter((f) => f.length == minBy(currentRecord.relations, 'length').length);
  }
  const pathDetail = generateControlPaths(minPath).join('\n');

  const relationType = translateRelationType(
    uniq(
      flatten(minPath)
        .filter((node) => node.type === 'edge')
        .reduce((arr, item) => {
          const groupType = (item.groups || []).flatMap((group) => group.roleType);
          return [...arr, item.roleType, ...groupType];
        }, []),
    ),
  );
  const companyNameDD = currentRecord.startCompanyName;
  responseList.push({
    relationType,
    pathDetail,
    companyNameDD,
    companyNameRelated: currentRecord.endCompanyName,
  });
  return responseList;
};

export const processSuspectRelationTypes = (sourceDataList: CompanyAnalyzedV2PO): any[] => {
  const responseList: any[] = [];
  const mergedResult = sourceDataList.relations;
  // 关联类型
  const relationType = uniq(sourceDataList.relationTypes).join('、');
  // 关联路径，取最短路径
  const pathDetail = convertSuspectPathToGraph(sourceDataList.relations, '\n'); // 按类型渲染
  const companyNameDD = sourceDataList.startCompanyName;
  responseList.push({
    relationType,
    pathDetail,
    companyNameDD,
    companyNameRelated: sourceDataList.endCompanyName,
  });
  return responseList;
};

const translateRelationType = (edgeTypes: string[]) => {
  return edgeTypes
    .map((type) => {
      return RelationTypeMapV2[type.toLowerCase()];
    })
    .join(',');
};
