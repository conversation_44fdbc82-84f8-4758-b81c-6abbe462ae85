import { Injectable } from '@nestjs/common';
import { ExportProcessorBase } from '../export.processor.base';
import { BatchBiddingHelper } from '../../../service/helper/batch.bidding.helper';
import { ExportConditionBase, ExportRecordPO } from '../model/ExportRecordPO';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { FileResultPO } from '../model/FileResultPO';
import { PaginationParams, RoverUser } from '../../../../../libs/model/common';
import { SearchBiddingBatchResultRequest } from '../../../../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { Worksheet } from 'exceljs';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { flatten, xor } from 'lodash';
import { TenderDimensionDetailCommonFields } from '../../../../utils/excel/excel-template.util';
import { TenderDimensionDetailPO } from '../../../model/TenderDimensionDetailPO';
import * as Bluebird from 'bluebird';
import { BiddingStaticPO } from '../../../model/BiddingStaticPO';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import { getTenderDimensionKeyName } from '../../../../../libs/constants/tender.dimension.constants';
import { DiligenceTenderHistoryEntity } from '../../../../../libs/entities/DiligenceTenderHistoryEntity';

@Injectable()
export class TenderDimensionExportProcessor extends ExportProcessorBase {
  constructor(private readonly batchBiddingHelper: BatchBiddingHelper) {
    super();
  }

  async searchDataAndGeneratorFile(condition: PaginationParams, user: RoverUser, businessType: BatchBusinessTypeEnums): Promise<FileResultPO> {
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    switch (businessType) {
      case BatchBusinessTypeEnums.Tender_Dimension_Detail_Export: {
        Object.assign(condition, { orgId: user.currentOrg });
        const searchRequest = condition as SearchBiddingBatchResultRequest;
        const response = await this.batchBiddingHelper.getAllTenderDetails(user, searchRequest);
        if (response?.length) {
          const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
            [BatchBusinessTypeEnums.Tender_Dimension_Detail_Export]: response,
          });
          fileResult.fileUrl = fileUrl;
          fileResult.fileName = fileName;
          fileResult.recordCount = response.length;
        }
        break;
      }
      case BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export: {
        Object.assign(condition, { orgId: user.currentOrg });
        const searchRequest = condition as SearchBiddingBatchResultRequest;
        //排查统计&排查结果
        const staticInfo: BiddingStaticPO[] = await this.batchBiddingHelper.getBiddingStatisticsV2(searchRequest);
        const statistics = staticInfo
          .filter((s) => s.key !== 'all')
          .map(({ key, totalHits }) => ({
            key,
            value: totalHits,
          }));
        const recordList = await this.batchBiddingHelper.searchBiddingDetailV2(user, searchRequest);

        //排查详情
        const response = await this.batchBiddingHelper.getAllTenderDetails(user, searchRequest);
        if (recordList.data?.length || response?.length) {
          const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
            [ExportEnums.TenderStatic_Export]: statistics,
            [ExportEnums.TenderDetail_Export]: recordList.data,
            [BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export]: response,
          });
          fileResult.fileUrl = fileUrl;
          fileResult.fileName = fileName;
          fileResult.recordCount = response.length;
        }
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  async applyDataToFile<T extends ExportConditionBase>(
    businessType: BatchBusinessTypeEnums,
    record: ExportRecordPO,
    workbook,
    condition: ExportConditionBase,
  ): Promise<string> {
    let exportFileName = '批量招标排查维度详情报告';
    //const orgSetting = await this.processorHelper.getOrgSetting(condition.orgId, condition?.targetId);
    const dimensionKV = await this.settingService.getTenderDimensionKV();
    const worksheets = workbook.worksheets;

    switch (businessType) {
      case BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export: {
        exportFileName = '批量招标排查报告';
        const searchRequest = condition as SearchBiddingBatchResultRequest;
        const tenderHistoryList = record[businessType];
        const usedSheetNames = [];
        const workSheetNames = worksheets.map((w) => w.name);

        await Bluebird.map(worksheets, async (worksheet) => {
          //取出data
          if (worksheet.name === '排查统计') {
            const staticInfo = record[ExportEnums.TenderStatic_Export];
            usedSheetNames.push(worksheet.name);
            await Bluebird.map(staticInfo, (r) => {
              //处理statistics
              worksheet.addRow({ riskLevel: getTenderDimensionKeyName()[r.key], riskCount: r.value }).commit();
            });
          }
          if (worksheet.name === '招标排查历史记录') {
            const tenderRecordList: DiligenceTenderHistoryEntity[] = record[ExportEnums.TenderDetail_Export];
            usedSheetNames.push(worksheet.name);
            this.batchBiddingHelper.handleTenderHistory4Export(tenderRecordList).forEach((r) => {
              worksheet.addRow(r).commit();
            });
          }
        });

        await Bluebird.map(searchRequest.dimensionLevelAll, async (d) => {
          const dimensionHitsDetails: Map<string, TenderDimensionDetailPO[]> = await this.batchBiddingHelper.getDimensionHitsDetails(
            { ...searchRequest, dimensionLevel1: d },
            tenderHistoryList,
          );
          dimensionHitsDetails.forEach((value, key) => {
            const dimension = key;
            const useSheet = worksheets.find((w) => w.name === key || w.name === dimensionKV[dimension]);
            usedSheetNames.push(useSheet.name);
            //sheet名字替换为一级维度+二级维度
            const tempName = dimensionKV[useSheet.name] || useSheet.name;
            if (dimensionKV[d] === tempName) {
              useSheet.name = tempName;
            } else {
              useSheet.name = `${dimensionKV[d]}-${tempName}`;
            }
            const dimensionData: TenderDimensionDetailPO[] = dimensionHitsDetails.get(key);
            this.handleDimensionDetailRecord(dimensionData, useSheet, dimension);
          });
        });
        const unusedSheet = xor(usedSheetNames, workSheetNames);
        unusedSheet.forEach((u) => workbook.removeWorksheet(u));
        break;
      }
      case BatchBusinessTypeEnums.Tender_Dimension_Detail_Export: {
        const searchRequest = condition as SearchBiddingBatchResultRequest;
        const { dimensionLevel1 } = searchRequest;
        const tenderHistoryList = record[businessType];
        const usedSheetNames = [];
        const workSheetNames = worksheets.map((w) => w.name);

        exportFileName = `${exportFileName}-${dimensionKV[dimensionLevel1]}`;
        const dimensionHitsDetails: Map<string, TenderDimensionDetailPO[]> = await this.batchBiddingHelper.getDimensionHitsDetails(
          searchRequest,
          tenderHistoryList,
        );
        dimensionHitsDetails.forEach((value, key) => {
          const dimension = key;
          const useSheet = worksheets.find((w) => w.name === key || w.name === dimensionKV[dimension]);
          usedSheetNames.push(useSheet.name);
          //sheet名字替换为一级维度+二级维度
          const tempName = dimensionKV[useSheet.name] || useSheet.name;
          if (dimensionKV[dimensionLevel1] === tempName) {
            useSheet.name = tempName;
          } else {
            useSheet.name = `${dimensionKV[dimensionLevel1]}-${tempName}`;
          }
          const dimensionData: TenderDimensionDetailPO[] = dimensionHitsDetails.get(key);
          this.handleDimensionDetailRecord(dimensionData, useSheet, dimension);
        });
        const unusedSheet = xor(usedSheetNames, workSheetNames);
        unusedSheet.forEach((u) => workbook.removeWorksheet(u));
        break;
      }
    }
    return exportFileName;
  }

  private handleDimensionDetailRecord(tenderDimensionDetailPO: TenderDimensionDetailPO[], worksheet: Worksheet, dimension: DimensionTypeEnums | string) {
    const addRow = (dataDetail = {}, fields = []) => {
      const row = {
        ...dataDetail,
      };
      fields.forEach((field) => {
        row[field.key] = field.format ? field.format(dataDetail) : dataDetail[field.key] || '-';
      });
      worksheet.addRow(row).commit();
    };

    const matchedCommonField = TenderDimensionDetailCommonFields.find((f) => f.dimensions.map((d) => d.toString()).includes(dimension));
    if (matchedCommonField) {
      tenderDimensionDetailPO.forEach((record) => {
        const dimensionDataDetails = record.dimensionDetails;
        dimensionDataDetails.forEach((dataDetail) => {
          dataDetail['projectName'] = record.projectName;
          dataDetail['projectNo'] = record.projectNo;
        });
      });
      const exportItems = flatten(tenderDimensionDetailPO.map((record) => record.dimensionDetails));
      exportItems.forEach((item) => {
        addRow(item, matchedCommonField.fields);
      });
    }
    const msg = `handleDimensionDetailRecord unknown dimension type: ${dimension}`;
    this.logger.warn(msg);
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Tender_Dimension_Detail_Export, BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export];
  }

  onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }
}
