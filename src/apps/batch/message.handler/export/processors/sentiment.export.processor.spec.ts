import { EntityManager, getRepository, Repository } from 'typeorm';
import MyOssService from '../../../../basic/my-oss.service';
import xlsx from 'node-xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { tmpdir } from 'os';
import { random } from 'lodash';
import { Test, TestingModule } from '@nestjs/testing';
import { BundleTestUtils } from '../../../../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { ExportTestUtils } from '../../../../test_utils_module/export.test.utils';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { MonitorSentimentDynamicsV2Entity } from '../../../../../libs/entities/MonitorSentimentDynamicsV2Entity';
import { PermissionByEnum } from '../../../../../libs/enums/PermissionScopeEnum';
import { SecurityService } from '../../../../../libs/config/security.service';
import { UserService } from '../../../../user/user.service';
import { SentimentExportProcessor } from './sentiment.export.processor';
import { SearchCustomerNewsRequest } from '../../../../../libs/model/customer/SearchCustomerNewsRequest';
import { MonitorSentimentDynamicService } from '../../../../monitor/sentiment/monitor.sentiment.dynamic.service';
import { ExportConditionBase } from '../model/ExportRecordPO';
import { AppTestModule } from '../../../../app/app.test.module';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchModule } from '../../../batch.module';
import { MyOssMock } from '../../../../test_utils_module/my-oss.mock';
const [testOrgId, testUserId] = generateUniqueTestIds('sentiment.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
const ossMock = new MyOssMock();

/**
 * 单元测试
 */
jest.setTimeout(60 * 1000);
describe.skip('单元测试-负面舆情列表导出processor', () => {
  let entityManager: EntityManager;
  let myOssService: MyOssService;
  const condition = Object.assign(new SearchCustomerNewsRequest(), { pageSize: 10, pageIndex: 1 });
  const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));
  let dynamicRepo: Repository<MonitorSentimentDynamicsV2Entity>;
  let processor: SentimentExportProcessor;
  let securityService: SecurityService;
  let userService: UserService;
  let sentimentService: MonitorSentimentDynamicService;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    BundleTestUtils.spy(moduleFixture.get(BundleHelperService));
    myOssService = moduleFixture.get(MyOssService);
    processor = moduleFixture.get(SentimentExportProcessor);
    securityService = moduleFixture.get(SecurityService);
    userService = moduleFixture.get(UserService);
    sentimentService = moduleFixture.get(MonitorSentimentDynamicService);
    dynamicRepo = getRepository(MonitorSentimentDynamicsV2Entity);
    entityManager = dynamicRepo.manager;
  });

  afterAll(async () => {
    await ExportTestUtils.clearTestData(entityManager, testUser);
    await dynamicRepo.delete({ orgId: testUser.currentOrg });
    await entityManager.connection.close();
  });

  it.skip('查询负面舆情列表数据并生成文件', async () => {
    // TODO: 缺少初始mock数据
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
    jest.spyOn(securityService, 'checkScope').mockReturnValue({
      by: PermissionByEnum.ORG,
    });
    // const userServiceSpy = jest.spyOn(userService, 'getRoverUser').mockResolvedValue(testUser);
    const negativeNews = await sentimentService.searchNegativeNews(testUser, condition);
    negativeNews.total = 10;
    const spy = jest.spyOn(processor.sentimentService, 'searchNegativeNews').mockReturnValue(Promise.resolve(negativeNews));
    const exportCondition = Object.assign(new ExportConditionBase(), {
      userId: testUser.userId,
      orgId: testUser.currentOrg,
    });
    const result = await processor.searchDataAndGeneratorFile(exportCondition, testUser, BatchBusinessTypeEnums.Sentiment_Export);
    expect(result).not.toBeNull();
    expect(spy).toHaveBeenCalled(); // Ensure the method was called
    const ossFileUrl = ossMock.signSingleUrl(result.fileUrl);
    //创建本地临时文件夹，将ossFileUrl文件下载到本地临时文件夹
    fs.mkdirSync(tempDir);
    const dest = path.join(tempDir, result.fileName);
    await ExportTestUtils.downloadFile(ossFileUrl, dest, ossMock);
    //读取本地临时文件夹文件内容
    expect(ossFileUrl).not.toBeNull();
    //解析本地文件之后校验导出内容是否正确
    const rowData = xlsx.parse(dest);
    expect(rowData.length).toBeGreaterThan(0);
    const excelRowData = rowData[0].data.slice(3, result.recordCount + 3);
    expect(excelRowData.length).toBeGreaterThanOrEqual(negativeNews.data.length);
    excelRowData.forEach((item, index) => {
      expect(item[0]).toEqual(negativeNews.data[index].companyName);
      expect(item[6]).toEqual(negativeNews.data[index].group.name);
      expect(item[7]).toEqual(negativeNews.data[index].originalUrl);
    });
  });
});
