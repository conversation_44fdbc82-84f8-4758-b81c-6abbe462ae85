import { ExportProcessorBase } from '../export.processor.base';
import { RoverUser } from '../../../../../libs/model/common';
import { FileResultPO } from '../model/FileResultPO';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { Injectable } from '@nestjs/common';
import { SearchInnerBlacklistModel } from '../../../../../libs/model/blacklist/SearchInnerBlacklistModel';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BlacklistInnerService } from '../../../../blacklist/blacklist.inner.service';
import { GroupType } from '../../../../../libs/model/element/CreateGroupModel';
import { AreaMapping, IndustryMapping } from '@kezhaozhao/qcc-model';
import { uniq } from 'lodash';
import { DurationConstants, StatusCode } from '../../../file.parser/file.export.template';
import * as moment from 'moment/moment';
import { DATE_FORMAT, DATE_TIME_FORMAT, EconType, EnterpriseType } from '../../../../../libs/constants/common';
import * as Bluebird from 'bluebird';
import { Workbook } from 'exceljs';
import { ExportConditionBase, ExportRecordPO } from '../model/ExportRecordPO';
import { SearchInnerBlacklistResponse } from '../../../../../libs/model/blacklist/SearchInnerBlacklistResponse';
import { StatusMap } from '../../../../../libs/model/diligence/pojo/model/ModelScorePO';

@Injectable()
export class InnerBlacklistExportProcessor extends ExportProcessorBase {
  constructor(private readonly blacklistInnerService: BlacklistInnerService) {
    super();
  }

  async searchDataAndGeneratorFile(condition: ExportConditionBase, user: RoverUser, businessType: BatchBusinessTypeEnums): Promise<FileResultPO> {
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    switch (businessType) {
      case BatchBusinessTypeEnums.InnerBlacklist_Export: {
        const innerBlacklistResponse: SearchInnerBlacklistResponse = await this.blacklistInnerService.search(
          user,
          Object.assign(new SearchInnerBlacklistModel(), condition),
          true,
        );
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.InnerBlacklist]: innerBlacklistResponse?.data,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = innerBlacklistResponse?.total;
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.InnerBlacklist_Export];
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordPO, workbook: Workbook, condition: ExportConditionBase): Promise<string> {
    let exportFileName = '';
    const worksheets = workbook.worksheets;
    switch (businessType) {
      case BatchBusinessTypeEnums.InnerBlacklist_Export: {
        //内部黑名单导出
        exportFileName = '内部黑名单';
        const records = record[ExportEnums.InnerBlacklist];
        const groupMap = await this.processorHelper.getGroupKV(condition.orgId, GroupType.InnerBlacklistGroup);
        const labelKV = await this.processorHelper.getLabelKV(condition.orgId, 3);
        await Bluebird.map(worksheets, async (worksheet) => {
          for (const record of records) {
            const r: any = { ...record };
            let expiredDate;
            if (record.expiredDate) {
              expiredDate = moment(new Date(record.expiredDate)).toDate();
            }
            if (expiredDate && moment(new Date()).isAfter(expiredDate)) {
              r.expiredStatus = '已过期';
            } else {
              r.expiredStatus = '有效';
            }
            r.expiredDate = expiredDate ? moment(expiredDate).format(DATE_FORMAT) : '-';
            r.createDate = moment(record.createDate, moment.ISO_8601).format(DATE_TIME_FORMAT) || '-';
            r.updateDate = moment(record.updateDate, moment.ISO_8601).format(DATE_TIME_FORMAT) || '-';
            r.result = StatusMap[r?.result];
            r.group = groupMap[record.groupId] || '-';
            r.label = labelKV[record.id] || '-';
            r.creator = record?.creator?.name || '-';
            const industryKey = [record.industry1, record.industry2, record.industry3, record.industry4].filter((d) => d).join('_');
            const area = [AreaMapping[record?.province], AreaMapping[record?.city], AreaMapping[record?.district]].filter((d) => d).join('/');
            r.industry = IndustryMapping[industryKey] || '-';
            r.area = area || '-';
            r.econType = uniq(
              r?.econType?.split(',').map((et) => {
                return EconType[et] || '其他';
              }),
            )
              ?.filter((d) => d)
              ?.join(',');
            r.enterpriseType = uniq(
              r?.enterpriseType?.split(',').map((et) => {
                return EnterpriseType[et] || '其他';
              }),
            )
              ?.filter((d) => d)
              ?.join(',');
            r.registcapi = record?.registcapi?.replace('人民币', '');
            r.duration = DurationConstants[record?.duration] || '-';
            r.joinDate = moment(new Date(record.joinDate)).format(DATE_FORMAT) || '-';
            r.startDateCode = record.startDateCode ? moment(record?.startDateCode, moment.ISO_8601).format(DATE_FORMAT) : '-';
            r.registrationStatus = r.statusCode ? StatusCode[r.statusCode] : '-';
            Object.keys(r).forEach((rk) => {
              r[rk] = r[rk] || '-';
            });
            worksheet.addRow({ ...r }).commit();
          }
        });
        break;
      }
      default: {
        this.logger.warn(`applyDataToFile unknown batch business type: ${businessType}`);
      }
    }
    return exportFileName;
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
