import { EntityManager, getRepository, Repository } from 'typeorm';
import { PaginationParams } from '../../../../../libs/model/common';
import { Test, TestingModule } from '@nestjs/testing';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { ExportTestUtils } from '../../../../test_utils_module/export.test.utils';
import MyOssService from '../../../../basic/my-oss.service';
import xlsx from 'node-xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { tmpdir } from 'os';
import { random } from 'lodash';
import { AppTestModule } from '../../../../app/app.test.module';
import { TenderExportProcessor } from './tender.export.processor';
import { UserService } from '../../../../user/user.service';
import { DiligenceTenderHistoryEntity } from '../../../../../libs/entities/DiligenceTenderHistoryEntity';
import { BiddingStaticPO } from '../../../model/BiddingStaticPO';
import { BatchBiddingHelper } from '../../../service/helper/batch.bidding.helper';
import { SearchBiddingBatchResultRequest } from '../../../../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { getTenderDimensionKeyName } from '../../../../../libs/constants/tender.dimension.constants';
import { SearchDiligenceBiddingRequest } from '../../../../../libs/model/bidding/SearchDiligenceBiddingRequest';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchModule } from '../../../batch.module';
import { MockBundleService, RoverBundleCounterType, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { MyOssMock } from '../../../../test_utils_module/my-oss.mock';
const [testOrgId, testUserId] = generateUniqueTestIds('tender.export.processor.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
const ossMock = new MyOssMock();

jest.setTimeout(60 * 1000);
describe.skip('单元测试-招标排查结果导出processor', () => {
  let entityManager: EntityManager;
  let tenderHisRepo: Repository<DiligenceTenderHistoryEntity>;
  let myOssService: MyOssService;
  let tenderExportProcessor: TenderExportProcessor;
  let userService: UserService;
  let batchBiddingHelper: BatchBiddingHelper;
  let batchBiddingHelperService: BatchBiddingHelper;
  const condition = Object.assign(new PaginationParams(), { pageSize: 10, pageIndex: 1 });
  const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));
  let mockedBundleService: MockBundleService<RoverBundleCounterType, RoverBundleLimitationType>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    // BundleTestUtils.spy(moduleFixture.get(BundleHelperService));
    myOssService = moduleFixture.get(MyOssService);
    tenderHisRepo = getRepository(DiligenceTenderHistoryEntity);
    entityManager = tenderHisRepo.manager;
    tenderExportProcessor = moduleFixture.get(TenderExportProcessor);
    userService = moduleFixture.get(UserService);
    batchBiddingHelper = moduleFixture.get(BatchBiddingHelper);
    batchBiddingHelperService = moduleFixture.get(BatchBiddingHelper);
    mockedBundleService = moduleFixture.get(RoverBundleService);
  });

  afterAll(async () => {
    await ExportTestUtils.clearTestData(entityManager, testUser);
    await entityManager.connection.close();
  });
  afterEach(async () => {
    //删除临时文件夹
    await ExportTestUtils.rmDirSync(tempDir);
  });

  it('查询批量招标排查记录列表数据并生成文件', async () => {
    const realUser = await userService.getRoverUser(101172);
    Object.assign(condition, {
      pageIndex: 1,
      pageSize: 10,
      batchId: 30070,
      dimensionLevel1: '',
      dimensionLevel2: '',
      status: [0, 1, 2, -1],
      orgId: realUser.currentOrg,
    });
    const result = await tenderExportProcessor.searchDataAndGeneratorFile(condition, realUser, BatchBusinessTypeEnums.Tender_Diligence_Record_Export);
    expect(result).not.toBeNull();
    const ossFileUrl = ossMock.signSingleUrl(result.fileUrl);
    //创建本地临时文件夹，将ossFileUrl文件下载到本地临时文件夹
    fs.mkdirSync(tempDir);
    const dest = path.join(tempDir, result.fileName);
    await ExportTestUtils.downloadFile(ossFileUrl, dest, ossMock);
    //读取本地临时文件夹文件内容
    expect(ossFileUrl).not.toBeNull();
    //解析本地文件之后校验导出内容是否正确
    const rowData = xlsx.parse(dest);
    expect(rowData.length).toBeGreaterThan(0);
    const searchRequest = condition as SearchBiddingBatchResultRequest;
    const staticInfo: BiddingStaticPO[] = await batchBiddingHelper.getBiddingStatisticsV2(searchRequest);
    const statistics = staticInfo
      .filter((s) => s.key !== 'all')
      .map(({ key, totalHits }) => ({
        key,
        value: totalHits,
      }));
    const recordList = await batchBiddingHelperService.searchBiddingDetailV2(realUser, searchRequest);
    expect(result.recordCount).toEqual(recordList.data.length);
    //static
    const staticRowData = rowData[0].data.slice(3, result.recordCount + 3);
    expect(staticRowData.length).toBeGreaterThan(0);
    staticRowData.forEach((item, index) => {
      statistics.forEach((s) => {
        if (item[0] === getTenderDimensionKeyName()[s.key]) {
          expect(item[0]).toEqual(getTenderDimensionKeyName()[s.key]);
          expect(item[1]).toEqual(s.value);
        }
      });
    });
    //diligence list
    const diligenceRowData = rowData[1].data.slice(3, result.recordCount + 3);
    expect(diligenceRowData.length).toBeGreaterThan(0);
    diligenceRowData.forEach((item, index) => {
      expect(item[0]).toEqual(recordList.data[index].projectName);
    });
  });

  it('查询招标排查报告列表数据并生成文件', async () => {
    const realUser = await userService.getRoverUser(101348);
    Object.assign(condition, {
      pageIndex: 1,
      pageSize: 10,
      // diligenceIds: [221576],
      diligenceId: 221576,
      orgId: 1001652,
    });
    const result = await tenderExportProcessor.searchDataAndGeneratorFile(condition, realUser, BatchBusinessTypeEnums.Tender_Report_Export);
    expect(result).not.toBeNull();
    const ossFileUrl = ossMock.signSingleUrl(result.fileUrl);
    //创建本地临时文件夹，将ossFileUrl文件下载到本地临时文件夹
    fs.mkdirSync(tempDir);
    const dest = path.join(tempDir, result.fileName);
    await ExportTestUtils.downloadFile(ossFileUrl, dest, ossMock);
    //读取本地临时文件夹文件内容
    expect(ossFileUrl).not.toBeNull();
    //解析本地文件之后校验导出内容是否正确
    const rowData = xlsx.parse(dest);
    expect(rowData.length).toBeGreaterThan(0);
    const searchRequest = condition as SearchDiligenceBiddingRequest;

    const recordList = await batchBiddingHelperService.getTenderDiligenceHistory(realUser, searchRequest);
    expect(result.recordCount).toEqual(recordList.length);
    //diligence list
    const diligenceRowData = rowData[0].data.slice(3, result.recordCount + 3);
    expect(diligenceRowData.length).toBe(recordList.length);
    diligenceRowData.forEach((item, index) => {
      expect(item[0]).toEqual(recordList[index].projectName);
    });
  });

  it('导出错误的batchBusinessType', async () => {
    Object.assign(condition, { batchId: -1, orgId: testUser.currentOrg });
    const result = await tenderExportProcessor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Tender_Diligence_Record_Export);
    expect(result.recordCount).toEqual(0);
    expect(result.fileUrl).toEqual('');
    expect(result.fileName).toEqual('');
    expect(result.previewUrl).toEqual('');
  });
});
