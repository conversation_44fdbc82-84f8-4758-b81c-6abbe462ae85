import { ExportProcessorBase } from '../export.processor.base';
import { Injectable } from '@nestjs/common';
import { RoverUser } from '../../../../../libs/model/common';
import { FileResultPO } from '../model/FileResultPO';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { compact, flatten } from 'lodash';
import { ExportEnums } from '../../../../../libs/enums/batch/ExportEnums';
import * as Bluebird from 'bluebird';
import { Workbook } from 'exceljs';
import {
  BundleConsumeDetailItemPO,
  ExportBatchBundleConsumeDetailRequest,
  ExportBatchReportRequest,
  ExportConditionBase,
  ExportRecordPO,
} from '../model/ExportRecordPO';
import { dateTransform } from '../../../../../libs/utils/date.utils';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';
import { DiligenceHistoryRequest } from '../../../../../libs/model/diligence/pojo/history/DiligenceHistoryRequest';
import { DiligenceHistoryService } from '../../../../diligence/details/diligence.history.service';
import { BatchBaseHelper } from '../../../service/helper/batch.base.helper';
import { SearchBatchRequest } from '../../../../../libs/model/batch/request/SearchBatchRequest';
import { SearchDiligenceBiddingRequest } from '../../../../../libs/model/bidding/SearchDiligenceBiddingRequest';
import { BiddingCommonService } from '../../../../bidding/service/bidding.common.service';
import { SpecificFacadeService } from '../../../../specific/specific.facade.service';

@Injectable()
export class BundleConsumeDetailExportProcessor extends ExportProcessorBase {
  constructor(
    public readonly diligenceHistoryService: DiligenceHistoryService,
    public readonly batchBaseHelperService: BatchBaseHelper,
    public readonly biddingCommonService: BiddingCommonService,
    public readonly specificFacadeService: SpecificFacadeService,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [
      BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export,
      BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export,
      BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export,
      BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export,
    ];
  }

  // 分页查询和合并数据的逻辑
  private async fetchAllData(
    searchFunction: (request: any) => Promise<any>,
    initialRequest: any,
  ): Promise<{ exportData: BundleConsumeDetailItemPO[]; total: number }> {
    const response = await searchFunction(initialRequest);
    const exportData: BundleConsumeDetailItemPO[] = [];
    if (response?.data) {
      exportData.push(...response.data);
    }
    const promises = [];
    if (response?.total >= 500) {
      for (let i = 2; i <= Math.ceil(response?.total / 500); i++) {
        const newRequest = { ...initialRequest, pageIndex: i };
        promises.push(searchFunction(newRequest));
      }
    }
    const promiseAll = await Bluebird.all(promises);
    exportData.push(...compact(flatten(promiseAll?.map((m) => m?.data))));
    return { exportData, total: response?.total };
  }

  async searchDataAndGeneratorFile(
    condition: ExportBatchBundleConsumeDetailRequest,
    user: RoverUser,
    businessType: BatchBusinessTypeEnums,
  ): Promise<FileResultPO> {
    const fileResult = { recordCount: 0, fileUrl: '', fileName: '', previewUrl: '' };
    const { ids, ...restCondition } = condition;

    switch (businessType) {
      case BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export: {
        const initialRequest = Object.assign(new DiligenceHistoryRequest(), condition, { pageIndex: 1, pageSize: 500 });
        const { exportData, total } = await this.fetchAllData((request) => this.diligenceHistoryService.search(user, request), initialRequest);
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.Bundle_Diligence_Consume_Detail_Export]: exportData,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = total;
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export: {
        const transformedCondition = { ...restCondition, batchIds: ids };
        const initialRequest = Object.assign(new SearchBatchRequest(), transformedCondition, { pageIndex: 1, pageSize: 500 });
        const { exportData, total } = await this.fetchAllData((request) => this.batchBaseHelperService.searchAnalyzeRecordBatch(user, request), initialRequest);
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.Bundle_Analyze_Record_Consume_Detail_Export]: exportData,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = total;
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export: {
        const transformedCondition = { ...restCondition, diligenceIds: ids };
        const initialRequest = Object.assign(new SearchDiligenceBiddingRequest(), transformedCondition, { pageIndex: 1, pageSize: 500 });
        const { exportData, total } = await this.fetchAllData((request) => this.biddingCommonService.searchV2(user, request), initialRequest);
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.Bundle_Bidding_Consume_Detail_Export]: exportData,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = total;
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export: {
        const transformedCondition = { ...restCondition, diligenceIds: ids };
        const initialRequest = Object.assign(new SearchDiligenceBiddingRequest(), transformedCondition, { pageIndex: 1, pageSize: 500 });
        const { exportData, total } = await this.fetchAllData((request) => this.specificFacadeService.search(user, request), initialRequest);
        const { fileUrl, fileName } = await this.generateResultFile(condition, businessType, {
          [ExportEnums.Bundle_Special_Consume_Detail_Export]: exportData,
        });
        fileResult.fileUrl = fileUrl;
        fileResult.fileName = fileName;
        fileResult.recordCount = total;
        break;
      }
      default: {
        this.logger.warn(`searchDataAndGeneratorFile unknown batch business type: ${businessType}`);
      }
    }
    return fileResult;
  }

  async applyDataToFile(businessType: BatchBusinessTypeEnums, record: ExportRecordPO, workbook: Workbook, condition: ExportConditionBase): Promise<string> {
    let exportFileName = '';
    const worksheets = workbook.worksheets;
    switch (businessType) {
      case BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export: {
        exportFileName = '风险排查-消费详情';
        const records = record[ExportEnums.Bundle_Diligence_Consume_Detail_Export];
        //获取 风险 key name 映射
        await Bluebird.map(worksheets, async (worksheet) => {
          for (const record of records) {
            const r: any = { ...record };
            r.createDate = dateTransform(r?.createDate, DATE_TIME_FORMAT) || '-';
            r.editor = r?.editor?.name + '(' + r?.editor?.phone + ')';
            r.companyName = r?.name;
            worksheet.addRow({ ...r }).commit();
          }
        });
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export: {
        exportFileName = '风险巡检-消费详情';
        const records = record[ExportEnums.Bundle_Analyze_Record_Consume_Detail_Export];
        //获取 风险 key name 映射
        await Bluebird.map(worksheets, async (worksheet) => {
          for (const record of records) {
            const r: any = { ...record };
            r.createDate = dateTransform(r?.createDate, DATE_TIME_FORMAT) || '-';
            r.editor = r?.creator?.name;
            r.statisticsInfo =
              '识别' +
              r.statisticsInfo?.recordCount +
              '/更新' +
              r.statisticsInfo?.updatedCount +
              '/重复' +
              r.statisticsInfo?.duplicatedCount +
              '/错误' +
              r.statisticsInfo?.errorCount +
              '/执行' +
              r.statisticsInfo?.successCount +
              '/消耗额度' +
              r.statisticsInfo?.paidCount;
            worksheet.addRow({ ...r }).commit();
          }
        });
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export: {
        exportFileName = '招标排查-消费详情';
        const records = record[ExportEnums.Bundle_Bidding_Consume_Detail_Export];
        //获取 风险 key name 映射
        await Bluebird.map(worksheets, async (worksheet) => {
          for (const record of records) {
            const r: any = { ...record };
            r.createDate = dateTransform(r?.createDate, DATE_TIME_FORMAT) || '-';
            r.editor = r?.editor?.name;
            r.tenderNo = r?.tenderNo || '-';
            r.companyListNumber = r?.companyList?.length || 0;
            r.paidCount = Math.ceil(r?.companyList?.length / 20) || 0;
            worksheet.addRow({ ...r }).commit();
          }
        });
        break;
      }
      case BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export: {
        exportFileName = '特定利益关系排查-消费详情';
        const records = record[ExportEnums.Bundle_Special_Consume_Detail_Export];
        //获取 风险 key name 映射
        await Bluebird.map(worksheets, async (worksheet) => {
          for (const record of records) {
            const r: any = { ...record };
            r.createDate = dateTransform(r?.createDate, DATE_TIME_FORMAT) || '-';
            r.editor = r?.editor?.name;
            r.recordNo = r?.recordNo || '-';
            r.companyListNumber = r?.companyList?.length || 0;
            r.paidCount = Math.ceil(r?.companyList?.length / 20) || 0;
            worksheet.addRow({ ...r }).commit();
          }
        });
        break;
      }
      default: {
        this.logger.warn(`applyDataToFile unknown batch business type: ${businessType}`);
      }
    }
    return exportFileName;
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
