import { In, Repository } from 'typeorm';
import MyOssService from '../../../../basic/my-oss.service';
import { PaginationResponse } from '../../../../../libs/model/common';
import { generateUniqueTestIds, getTestUser } from '../../../../test_utils_module/test.user';
import xlsx from 'node-xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { tmpdir } from 'os';
import { random } from 'lodash';
import { Test, TestingModule } from '@nestjs/testing';
import {
  ExportTestUtils,
  mockAnalyzeRecordResponse,
  mockBiddingResponse,
  mockDiligenceResponse,
  mockSpecialResponse,
} from '../../../../test_utils_module/export.test.utils';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { PermissionByEnum } from '../../../../../libs/enums/PermissionScopeEnum';
import { SecurityService } from '../../../../../libs/config/security.service';
import { dateTransform } from '../../../../../libs/utils/date.utils';
import { AppTestModule } from '../../../../app/app.test.module';
import { DATE_TIME_FORMAT } from '../../../../../libs/constants/common';
import { BundleConsumeDetailExportProcessor } from './bundle.consume.detail.export.processor';
import { DiligenceHistoryEntity } from '../../../../../libs/entities/DiligenceHistoryEntity';
import { DiligenceHistoryResponse } from '../../../../../libs/model/diligence/pojo/history/DiligenceHistoryResponse';
import { SearchBatchResponse } from '../../../../../libs/model/batch/response/SearchBatchResponse';
import { BatchModule } from '../../../batch.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchJobEntity } from '../../../../../libs/entities/BatchJobEntity';
import { BatchResultEntity } from '../../../../../libs/entities/BatchResultEntity';
import { MyOssMock } from '../../../../test_utils_module/my-oss.mock';

const ossMock = new MyOssMock();

/**
 * 单元测试
 */
jest.setTimeout(60 * 1000);
describe('套餐权益消费明细导出处理', () => {
  const [testOrgId, testUserId] = generateUniqueTestIds('bundle.consume.detail.export.processor.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  let securityService: SecurityService;
  let myOssService: MyOssService;
  const condition = Object.assign(new DiligenceHistoryEntity(), { pageSize: 10, pageIndex: 1 });
  let batchRepo: Repository<BatchEntity>;
  let batchJobRepo: Repository<BatchJobEntity>;
  let batchResultRepo: Repository<BatchResultEntity>;
  let processor: BundleConsumeDetailExportProcessor;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    myOssService = module.get(MyOssService);
    processor = module.get(BundleConsumeDetailExportProcessor);
    securityService = module.get(SecurityService);
    batchRepo = module.get<Repository<BatchEntity>>(getRepositoryToken(BatchEntity));
    batchJobRepo = module.get<Repository<BatchJobEntity>>(getRepositoryToken(BatchJobEntity));
    batchResultRepo = module.get<Repository<BatchResultEntity>>(getRepositoryToken(BatchResultEntity));
  });
  afterAll(async () => {
    await batchRepo.manager.connection.close();
  });

  afterEach(async () => {
    // 清理测试数据
    const batches = await batchRepo.find({ where: { orgId: testOrgId } });
    const batchIds = batches.map((b) => b.batchId);

    if (batchIds.length > 0) {
      await batchResultRepo.delete({ batchId: In(batchIds) });
      await batchJobRepo.delete({ batchId: In(batchIds) });
    }
    await batchRepo.delete({ orgId: testOrgId });
  });

  it('should be defined', () => {
    expect(batchRepo).toBeDefined();
  });

  const testExportResult = async (result, mockResponse, dataVerifyCallback) => {
    const ossFileUrl = ossMock.signSingleUrl(result.fileUrl);
    const tempDir = path.join(tmpdir(), testUser.userId.toString() + '-' + random(1000, 9999));
    const dest = path.join(tempDir, result.fileName);
    fs.mkdirSync(tempDir, { recursive: true });
    await ExportTestUtils.downloadFile(ossFileUrl, dest, ossMock);
    expect(ossFileUrl).not.toBeNull();
    const rowData = xlsx.parse(dest);
    expect(rowData.length).toBeGreaterThan(0);
    const excelRowData = rowData[0].data.slice(3, result.recordCount + 3);
    expect(excelRowData.length).toBeGreaterThan(0);
    excelRowData.forEach((item, index) => {
      dataVerifyCallback(item, index, mockResponse);
    });
  };

  it('套餐权益消费明细导出单元测试', async () => {
    jest.spyOn(myOssService, 'putSteam').mockImplementation(async (ossObject: string, filepath: string, options?: any) => {
      return await ossMock.putSteam(ossObject, filepath, options);
    });
    jest.spyOn(myOssService, 'signSingleUrl').mockImplementation((p: string, preview = false) => {
      return ossMock.signSingleUrl(p, preview);
    });
    jest.spyOn(myOssService, 'getObject').mockImplementation((ossObject: string) => {
      return ossMock.getObject(ossObject);
    });
    jest.spyOn(securityService, 'checkScope').mockReturnValue({
      by: PermissionByEnum.ORG,
    });
    const spy1 = jest
      .spyOn(processor.diligenceHistoryService, 'search')
      .mockResolvedValue(Object.assign(new DiligenceHistoryResponse(), mockDiligenceResponse));
    const spy2 = jest
      .spyOn(processor.batchBaseHelperService, 'searchAnalyzeRecordBatch')
      .mockResolvedValue(Object.assign(new SearchBatchResponse(), mockAnalyzeRecordResponse));
    condition.orgId = testUser.currentOrg;
    const spy3 = jest.spyOn(processor.biddingCommonService, 'searchV2').mockResolvedValue(Object.assign(new PaginationResponse(), mockBiddingResponse));
    const spy4 = jest.spyOn(processor.specificFacadeService, 'search').mockResolvedValue(Object.assign(new PaginationResponse(), mockSpecialResponse));
    condition.orgId = testUser.currentOrg;

    const result1 = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export);
    expect(spy1).toHaveBeenCalled();
    expect(result1).not.toBeNull();

    const result2 = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export);
    expect(spy2).toHaveBeenCalled();
    expect(result2).not.toBeNull();

    const result3 = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export);
    expect(spy3).toHaveBeenCalled();
    expect(result3).not.toBeNull();

    const result4 = await processor.searchDataAndGeneratorFile(condition, testUser, BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export);
    expect(spy4).toHaveBeenCalled();
    expect(result4).not.toBeNull();

    // 定义数据验证回调函数
    const verifyDiligence = (item, index, mockResponse) => {
      expect(item[0]).toEqual(dateTransform(mockResponse.data[index].createDate, DATE_TIME_FORMAT));
      expect(item[1]).toEqual(mockResponse.data[index]?.editor?.name + '(' + mockResponse.data[index]?.editor?.phone + ')');
      expect(item[2]).toEqual(mockResponse.data[index].name);
    };

    const verifyAnalyze = (item, index, mockResponse) => {
      expect(item[0]).toEqual(dateTransform(mockResponse.data[index].createDate, DATE_TIME_FORMAT));
      expect(item[1]).toEqual(mockResponse.data[index]?.creator?.name);
    };

    const verifyBidding = (item, index, mockResponse) => {
      expect(item[0]).toEqual(dateTransform(mockResponse.data[index].createDate, DATE_TIME_FORMAT));
      expect(item[1]).toEqual(mockResponse.data[index]?.editor?.name);
      expect(item[2]).toEqual(mockResponse.data[index]?.tenderNo);
      expect(item[3]).toEqual(mockResponse.data[index]?.companyList?.length);
      expect(item[4]).toEqual(Math.ceil(mockResponse.data[index]?.companyList?.length / 20));
    };

    const verifySpecial = (item, index, mockResponse) => {
      expect(item[0]).toEqual(dateTransform(mockResponse.data[index].createDate, DATE_TIME_FORMAT));
      expect(item[1]).toEqual(mockResponse.data[index]?.editor?.name);
      expect(item[2]).toEqual(mockResponse.data[index]?.recordNo);
      expect(item[3]).toEqual(mockResponse.data[index]?.companyList?.length);
      expect(item[4]).toEqual(Math.ceil(mockResponse.data[index]?.companyList?.length / 20));
    };

    // 调用公共函数进行测试
    await testExportResult(result1, mockDiligenceResponse, verifyDiligence);
    await testExportResult(result2, mockAnalyzeRecordResponse, verifyAnalyze);
    await testExportResult(result3, mockBiddingResponse, verifyBidding);
    await testExportResult(result4, mockSpecialResponse, verifySpecial);
  });
});
