import { findIndex, uniq } from 'lodash';

import { createCompanyNode, createEdge, createGraphEdge, createGraphNode, createPersonNode, isCompany, isEdge, isPerson } from './data-node';
import { processPaths } from '../../../../../data/utils/path.util';

/**
 * 高效处理关系角色，优化后的版本
 * 1. 一次性处理人员方向问题
 * 2. 使用类型判断函数简化代码
 * 3. 减少不必要的函数调用
 * @param relations 关系数组
 */
export const processRelationRoles = (relations: any[]) => {
  // 处理空数组情况
  if (!relations?.length) {
    return [];
  }

  // 先处理人员箭头方向问题
  processPersonDirection(relations);

  // 使用更高效的循环替代map
  const result = new Array(relations.length);

  // 一次性处理所有节点类型
  for (let i = 0; i < relations.length; i++) {
    const relation = relations[i];

    // 使用类型判断简化分支逻辑
    if (isEdge(relation)) {
      result[i] = createEdge(relation);
    } else if (isPerson(relation)) {
      result[i] = createPersonNode(relation);
    } else if (isCompany(relation)) {
      result[i] = createCompanyNode(relation);
    } else {
      result[i] = relation;
    }
  }

  return result;
};

/**
 * 优化后的人员方向处理函数
 * 1. 使用单次遍历解决问题
 * 2. 减少重复判断和条件嵌套
 * 3. 添加安全检查，避免异常
 * @param relations 关系数组
 */
export const processPersonDirection = (relations: any[]) => {
  // 处理空数组或过短数组的情况
  if (!relations?.length || relations.length < 3) {
    return;
  }

  // 使用单次遍历，仅处理边关系（奇数位置）
  for (let i = 1; i < relations.length - 1; i += 2) {
    // 边节点必须有type等属性，这里添加安全检查
    const currentEdge = relations[i];
    if (!currentEdge) continue;

    const prevNode = relations[i - 1];
    const nextNode = relations[i + 1];
    if (!prevNode || !nextNode) continue;

    // 提前解构，避免重复访问，添加默认值避免undefined错误
    const startid = currentEdge.startid || '';
    const endid = currentEdge.endid || '';

    // 使用短路逻辑避免undefined对象取属性错误
    const isPrevPerson = prevNode && 'Person.name' in prevNode;
    const isNextPerson = nextNode && 'Person.name' in nextNode;

    // 互斥条件判断，避免同时设置两个方向
    if (isPrevPerson && !isNextPerson) {
      currentEdge.direction = 1;

      // 只在需要时更新id
      if (!startid.startsWith('p')) {
        currentEdge.endid = nextNode['Company.keyno'];
        currentEdge.startid = prevNode['Person.keyno'];
      }
    } else if (isNextPerson && !isPrevPerson) {
      currentEdge.direction = -1;

      // 只在需要时更新id
      if (!endid.startsWith('p')) {
        currentEdge.endid = prevNode['Company.keyno'];
        currentEdge.startid = nextNode['Person.keyno'];
      }
    } else if (isPrevPerson && isNextPerson) {
      // 处理两端都是人的特殊情况
      currentEdge.direction = 0; // 中立方向

      // 确保ID设置正确
      currentEdge.startid = prevNode['Person.keyno'];
      currentEdge.endid = nextNode['Person.keyno'];
    }
  }
};

/**
 * 合并相分组: 根据 `startCompanyKeyno` 和 `endCompanyKeyno`
 * @param dataSource
 */
const mergeGroups = (dataSource: any[]) => {
  const result: Record<string, any>[] = [];

  dataSource.forEach((item) => {
    // 根据 `startCompanyKeyno` 和 `endCompanyKeyno` 合并数据
    let index: number;

    // startToEnd
    index = findIndex(result, {
      startCompanyKeyno: item.startCompanyKeyno,
      endCompanyKeyno: item.endCompanyKeyno,
    });

    // endToStart
    if (index === -1) {
      index = findIndex(result, {
        startCompanyKeyno: item.endCompanyKeyno,
        endCompanyKeyno: item.startCompanyKeyno,
      });
    }

    if (item.data?.length) {
      item.relations?.forEach((relation) => {
        if (isEdge(relation)) {
          // FIXME: 在修改源数据，会影响渲染
          relation.data = [
            {
              data: item.data,
              role: relation.role,
              type: relation.type,
            },
          ];
        }
      });
    }
    if (index > -1) {
      const target = result[index];
      result.splice(index, 1, {
        ...target,
        relations: [...target.relations, processRelationRoles(item.relations)],
      });
    } else {
      result.push({
        ...item,
        relations: [processRelationRoles(item.relations)],
      });
    }
  });
  return result;
};

/**
 * 生成基于路径边的哈希键（替代原长字符串方案）
 * 使用 djb2 哈希算法（速度快、碰撞率低）
 */
export const generateEdgePathKey = (nodes: any[]): string => {
  // 先拼接原始路径字符串
  const rawKey = nodes
    .filter(isEdge)
    .map((edge) => `${edge.startid}-${edge.endid}`)
    .join('|');

  // const rawKey2 = nodes
  //   .filter((node) => node.type === 'node')
  //   .map((node) => `${node.id}`)
  //   .join('|');

  // 计算哈希值（返回32位十六进制字符串）
  return djb2Hash(rawKey);
};

/**
 * djb2 哈希算法实现
 * 特点：速度快、碰撞率低（2.5e-9）
 */
const djb2Hash = (str: string): string => {
  let hash = 5381; // 初始种子
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) + hash + char; // hash * 33 + char
  }
  // 转换为32位十六进制（高位补0）
  return (hash >>> 0).toString(16).padStart(8, '0');
};

type Edge = {
  startid: string;
  endid: string;
  roles?: string[];
  data?: Array<{ type: string; data: any[] }>;
  groups?: any[];
};

type RelationNode = Edge | any; // 根据实际业务定义完整类型

/**
 * 合并实体关系路径
 * 任意两条路径，路径长度一致，下标从低到高，node节点都相同，边关系不同，合并边关系
 * @param dataSource
 */
export const mergeRelations = (dataSource: any[]) => {
  const result: any[] = [];
  dataSource.forEach((item) => {
    const dedupeByEdgeMap: Record<string, any> = {};
    // 合并已有边属性
    item?.relations?.map((nodes) => {
      // 基于 edges 生成唯一ID
      const dedupeEdgeId = generateEdgePathKey(nodes);
      // 命中
      if (Array.isArray(dedupeByEdgeMap[dedupeEdgeId])) {
        dedupeByEdgeMap[dedupeEdgeId].map((t, i) => {
          if (isEdge(t)) {
            t.roles = uniq([...(t?.roles || []), ...(nodes[i]?.roles || [])]);
            const dataTypeArr = t?.data?.map((_item) => _item.type);
            nodes[i]?.data?.forEach((_item) => {
              const typeIndex = dataTypeArr?.indexOf(_item.type);
              if (typeIndex > -1) {
                t.data[typeIndex].data = [...t.data[typeIndex].data, ..._item.data];
              } else {
                t.data = uniq([...(t?.data || []), _item]);
              }
            });
            //groups 需要去重
            t.groups = [...new Set([...(t?.groups || []), nodes[i]])];
            return t;
          }
          return t;
        });
      } else {
        dedupeByEdgeMap[dedupeEdgeId] = nodes;
        // mergedRelations.push(nodes);
      }
    });
    //获取 dedupeByEdgeMap 所有值，且只取前99个
    const mergedRelations = Object.values(dedupeByEdgeMap)?.slice(0, 99);
    result.push({
      ...item,
      relations: mergedRelations,
      relations2: mergedRelations,
    });
  });

  return result;
};

/**
 * 修复图数据中, 边的 `startid` 与 `endid` 与上下游节点顺序不一致
 */
const correctEdgeDirection = (relations: { startid: string; endid: string; id: string; direction: string; roles?: any[] }[], index: number) => {
  const edge = relations[index];
  const prevNode = relations[index - 1];
  const nextNode = relations[index + 1];
  if (prevNode.id === edge.endid && nextNode.id === edge.startid) {
    let direction = edge.direction;
    if (direction === 'left') {
      direction = 'right';
    } else if (direction === 'right') {
      direction = 'left';
    }
    return {
      ...edge,
      direction,
    };
  }
  return edge;
};

/**
 * 原始数据转换为关系链路
 */
export function parseRelationsData<T = Record<string, any>>(dataSource: T[], filterBy?: (item: T) => boolean) {
  const filterData = typeof filterBy === 'function' ? dataSource.filter(filterBy) : dataSource;
  const groups = mergeGroups(filterData);
  // const relationRoles = processRelationRoles(filterData);
  const result = mergeRelations(groups);
  return result;
}

/**
 * 关系链接转换为关系图数据
 */
export function parseGraphData<T = Record<string, any>>(dataSource: T[]) {
  const nodes: any[] = [];
  const edges: any[] = [];

  const uniqNodeMap = new Map<string, boolean>();
  const uniqEdgeMap = new Map<string, boolean>();

  const dedupMap = new Map<string, boolean>();

  dataSource.forEach((item: any) => {
    item.relations.forEach((relations, rIndex) => {
      relations.forEach((node, index) => {
        if (node.type === 'node') {
          const isEndpoint = [item.startCompanyName, item.endCompanyName].includes(node.name);
          // nodes 节点去重
          const newNode = createGraphNode(node, isEndpoint);
          const newNodeId = newNode.data.id;
          if (!uniqNodeMap.has(newNodeId)) {
            nodes.push(newNode);
            uniqNodeMap.set(newNodeId, true);
          }
        } else if (node.type === 'edge') {
          const edgeNode = correctEdgeDirection(relations, index);
          // edges 边去重
          const newEdge = createGraphEdge(edgeNode);
          // 通过 `正向端点ID` 和 `反向端点ID` 同时进行匹配
          const newEdgeId = newEdge.data.id;
          const reversedNewEdgeId = newEdge.data.id.split('-').reverse().join('-');

          if (!uniqEdgeMap.has(newEdgeId) && !uniqEdgeMap.has(reversedNewEdgeId)) {
            edges.push(newEdge);
            uniqEdgeMap.set(newEdgeId, true);
          }
        }
      });
    });
  });

  return {
    nodes,
    edges,
  };
}

export const getTransV2Detail = (data) => {
  const { Result = [], ...rest } = data;
  Result.forEach((item) => {
    const { companyNameRelated, companyKeynoRelated, relationPaths } = item;
    item.endCompanyKeyno = companyKeynoRelated;
    item.endCompanyName = companyNameRelated;
    relationPaths.forEach((paths) => {
      for (let i = 1; i < paths.length - 1; i += 2) {
        if (i % 2 === 1) {
          const { endid, startid } = paths[i];
          // 边关系，判断上一个节点是否为人 node
          if (paths[i - 1]['Person.name']) {
            paths[i].direction = 1;
            if (!startid?.startsWith('p')) {
              paths[i].endid = startid;
              paths[i].startid = endid;
            }
          }
          if (paths[i + 1]['Person.name']) {
            paths[i].direction = -1;
            if (!endid?.startsWith('p')) {
              paths[i].endid = startid;
              paths[i].startid = endid;
            }
          }
        }
      }
    });
    item.relations = relationPaths.map(processRelationRoles);
  });
  const relations = mergeRelations(Result);
  return {
    Result: relations,
    ...rest,
  };
};

/**
 * 定义关系数据的类型
 */
interface RelationData {
  type: string;
  data: any[];
}

/**
 * 定义边的类型
 */
interface EdgeNode {
  type: 'edge';
  startid: string;
  endid: string;
  roles?: string[];
  data?: RelationData[];
  groups?: any[];
}

/**
 * 定义节点的类型
 */
interface NodeData {
  type: 'node';
  id: string;
  [key: string]: any;
}

/**
 * 定义关系路径的类型
 */
type RelationPath = (EdgeNode | NodeData)[];

/**
 * 定义数据源的类型
 */
interface DataSource {
  relations?: RelationPath[];
  [key: string]: any;
}

/**
 * 类型守卫：检查是否为边节点
 */
const isEdgeNode = (node: EdgeNode | NodeData): node is EdgeNode => {
  return node.type === 'edge';
};

/**
 * 合并边的属性
 * @param target 目标边
 * @param source 源边
 */
const mergeEdgeProperties = (target: EdgeNode, source: EdgeNode): void => {
  // 合并角色
  target.roles = uniq([...(target?.roles || []), ...(source?.roles || [])]);

  // 合并数据
  if (source?.data?.length) {
    if (!target.data) {
      target.data = [];
    }

    const dataTypeMap = new Map(target.data.map((item) => [item.type, item]));

    source.data.forEach((sourceData) => {
      const existingData = dataTypeMap.get(sourceData.type);
      if (existingData) {
        existingData.data = [...existingData.data, ...sourceData.data];
      } else {
        target.data.push({ ...sourceData });
      }
    });
  }

  // 合并分组
  target.groups = [...new Set([...(target?.groups || []), ...(source?.groups || [])])];
};

/**
 * 合并实体关系路径 V2
 * 优化版本：
 * 1. 更清晰的类型定义
 * 2. 更好的代码组织
 * 3. 更高的性能
 * 4. 更好的错误处理
 * @param dataSource 数据源
 * @returns 合并后的结果
 */
export const mergeRelationsV2 = (dataSource: DataSource[]): DataSource[] => {
  return dataSource.map((item) => {
    if (!item.relations?.length) {
      return item;
    }

    const edgePathMap = new Map<string, RelationPath>();

    // 处理每个关系路径
    item.relations.forEach((nodes) => {
      const edgePathKey = generateEdgePathKey(nodes);

      if (edgePathMap.has(edgePathKey)) {
        // 合并现有路径
        const existingPath = edgePathMap.get(edgePathKey)!;
        nodes.forEach((node, index) => {
          const existingNode = existingPath[index];
          // 确保两个节点都是边节点类型
          if (isEdgeNode(node) && isEdgeNode(existingNode)) {
            mergeEdgeProperties(existingNode, node);
          }
        });
      } else {
        // 添加新路径
        edgePathMap.set(edgePathKey, [...nodes]); // 创建新的数组副本
      }
    });

    // 获取合并后的关系（限制为前99个）
    const mergedRelations = Array.from(edgePathMap.values()).slice(0, 99);

    return {
      ...item,
      relations: mergedRelations,
      relations2: mergedRelations,
    };
  });
};
