export interface RawBiddingCompanyRelationDetail {
  role?: string;
  type?: string;
  endid?: string;
  startid?: string;
  direction?: number;
  // eslint-disable-next-line camelcase
  create_time?: number;
  iscal?: string;
  shouldcapi?: number;
  stockpercent?: number;
  'Company.org'?: number;
  'Company.name'?: string;
  'Company.isipo'?: string;
  'Company.keyno'?: string;
  'Company.labels'?: string;
  'Company.econkind'?: string;
  'Company.hasimage'?: boolean;
  'Company.supernode'?: boolean;
  'Company.isinvestor'?: string;
  'Company.registcapi'?: string;
  'Company.companytype'?: number;
  'Company.create_time'?: number;
  'Company.credit_code'?: string;
  'Company.shortstatus'?: string;
  'Company.partner_source'?: string;
  'Person.name'?: string;
  'Person.keyno'?: string;
  'Person.hasimage'?: boolean;
  'Person.supernode'?: boolean;
  'Person.create_time'?: number;
}

export interface RawBiddingCompanyRelationData {
  steps: number;
  history: boolean;
  endCompanyName: string;
  endCompanyKeyno: string;
  startCompanyName: string;
  startCompanyKeyno: string;
  relations: RawBiddingCompanyRelationDetail[];
}

interface BaseNode {
  type: string;
  entityType: string;
  id: string | number;
  name: string;
  label: string;
}

export interface PersonNode extends BaseNode {
  isTrueId: string; // 是否是真实id，如果不是真实id就不能跳转
}

export type CompanyNode = BaseNode;

export interface Edge {
  type: 'edge';
  roleType: string;
  roles: string[];
  startid: string | number;
  endid: string | number;
  direction: 'right' | 'left';
}
