import { cloneDeep, isObject, keyBy, groupBy, trim, uniq, compact } from 'lodash';
import type { CompanyNode, PersonNode, Edge, RawBiddingCompanyRelationDetail } from './data-types';

// 针对人员没有keyno的时候手动赋值
const genNodeId = () => new Date().getTime();

export const RelationTypeOptions = [
  { label: '法定代表人', value: 'Legal', type: 0 },
  { label: '历史法定代表人', value: 'HisLegal', type: 0 },
  { label: '持股/投资关联', value: 'Invest', type: 0 },
  { label: '董监高', value: 'Employ', type: 0 },
  { label: '历史董监高', value: 'HisEmploy', type: 0 },
  { label: '历史持股/投资关联', value: 'HisInvest', type: 0 },
  { label: '分支机构', value: 'Branch', type: 0 },
  { label: '实际控制人', value: 'ActualController', type: 0 },
  { label: '控制关系', value: 'Hold', type: 0 },
  { label: '受益所有人', value: 'FinalBenefit', type: 0 },

  { label: '相同电话号码', value: 'ContactNumber', type: 1 },
  { label: '相同域名信息', value: 'Website', type: 1 },
  { label: '相同经营地址', value: 'Address', type: 1 },
  { label: '相同邮箱', value: 'Mail', type: 1 },
  { label: '相同专利信息', value: 'Patent', type: 1 },
  { label: '相同国际专利信息', value: 'IntPatent', type: 1 },
  { label: '相同软件著作权', value: 'SoftwareCopyright', type: 1 },
  { label: '相同司法案件', value: 'Case', type: 1 },
  { label: '疑似同名主要人员', value: 'SameNameEmployee', type: 1 },
];

export const RelationTypeMapV2 = {
  legal: '法定代表人',
  hislegal: '历史法定代表人',
  invest: '持股/投资关联',
  employ: '董监高',
  hisemploy: '历史董监高',
  hisinvest: '历史持股/投资关联',
  branch: '分支机构',
  actualcontroller: '相同实际控制人',
  hold: '控制关系',
  contactnumber: '相同电话号码',
  website: '相同域名信息',
  address: '相同经营地址',
  mail: '相同邮箱',
  patent: '相同专利信息',
  intpatent: '相同国际专利信息',
  softwarecopyright: '相同软件著作权',
  case: '相同司法案件',
  samenameemployee: '疑似同名主要人员',
  shareholdingrelationship: '持股关联',
  investorsrelationship: '投资关联',
  employmentrelationship: '董监高/法人关联',
  hisshareholdingrelationship: '持股关联（历史）',
  hisinvestorsrelationship: '投资关联（历史）',
  hislegalandemploy: '董监高/法人（历史）',
  maininfoupdatebeneficiary: '相同受益所有人',
  finalbenefit: '最终受益所有人',
  shareholdingratio: '持股/投资股权比例（含历史）',
};

export const createChartLine = (direction: 'left' | 'right' | 'none') => {
  switch (direction) {
    case 'left':
      return {
        sourceArrow: 'triangle',
        targetArrow: 'none',
      };
    case 'right':
      return {
        sourceArrow: 'none',
        targetArrow: 'triangle',
      };
    default:
      return {
        sourceArrow: 'none',
        targetArrow: 'none',
      };
  }
};

export const getRoles = (array) => {
  return Object.entries(groupBy(array, 't'))?.map(([key]) => {
    return key;
  });
};

export const getGraphEdgeData = (val) => {
  const { data = [], roles = [] } = val;

  if (!data.length) {
    return null;
  }

  const newData = data.flatMap((item2) => {
    if (item2.type === 'ContactNumber' && item2.data?.some(isObject)) {
      // 保留原始数据，方便数据读取
      const phones = [];
      let originalData = [];
      Object.entries(groupBy(item2.data, 't'))?.forEach(([key, value]) => {
        phones.push(key);
        originalData = [...originalData, ...value];
      });
      return {
        ...item2,
        data: phones,
        originalData,
      };
    }
    return item2;
  });

  if (roles?.length) {
    return roles.map((role) => {
      const find = newData.find((v) => trim(v.role) === trim(role));
      return {
        ...find,
        role: find?.role || role,
        data: find?.data || [],
      };
    });
  }
  return newData;
};

export const createGraphNode = (item, isEndpoint: boolean) => {
  return {
    data: {
      id: item.id,
      label: item.name,
      name: item.name,
      type: item.entityType, // company | person
      endpoint: isEndpoint, // 是否是起点或终点
      isTrueId: item.isTrueId, // 是否是真实ID，非真实不支持点击跳转，因为id是时间戳，是假的
    },
  };
};

export const createGraphEdge = (node) => {
  const { startid, endid, roles = [] } = node;
  // 箭头类型
  return {
    data: {
      id: `${startid}-${endid}`,
      label: roles?.filter(Boolean).join(','),
      source: startid,
      target: endid,
      data: getGraphEdgeData(node),
      ...createChartLine(node.direction),
    },
  };
};

export const createEdge = (edge): Edge => {
  const node = cloneDeep(edge);

  if (node.roles?.some(isObject) && !node.data) {
    node.data = node.roles.flatMap((item2) => item2);
  }
  if (!node.role && node.roles?.length) {
    node.role = node.roles.map((item) => item.role).join(', ');
    node.direction = 'null';
  } else {
    const directionsMap = {
      '0': 'null',
      '1': 'right',
      '-1': 'left',
    };
    node.direction = directionsMap[node.direction];
  }
  const typeUpperCase = String(node.type).toUpperCase();
  let roles = compact(node.role?.split(',')); // 当前边的角色 (可能存在多个, 以`,`分隔)
  const RelationTypeMap = keyBy(
    RelationTypeOptions.map((item) => ({ ...item, value: String(item.value).toUpperCase() })),
    'value',
  );
  // 角色特殊逻辑处理 (历史董监高、历史法定代表人)
  if (typeUpperCase === 'HISEMPLOY' || typeUpperCase === 'HISLEGAL') {
    if (roles?.length) {
      roles = roles.map((roleName: string) => `历史${roleName}`); // 历史任职
    } else {
      roles = [RelationTypeMap[typeUpperCase]?.label || RelationTypeMap[typeUpperCase]]; // 历史任职
    }
  } else if (typeUpperCase === 'HISINVEST' && !!node.stockpercent) {
    roles = [`历史投资${node.stockpercent}%`]; // 历史投资关系
  } else if (typeUpperCase === 'INVEST' && !!node.stockpercent) {
    roles = [`投资${node.stockpercent}%`]; // 投资关系
  } else if (typeUpperCase === 'BRANCH') {
    roles = ['分支机构'];
  } else if (RelationTypeMap[typeUpperCase]) {
    roles = [RelationTypeMap[typeUpperCase]?.label || RelationTypeMap[typeUpperCase]];
  } else if (typeUpperCase === '0') {
    roles = [RelationTypeMap['actualcontroller'.toUpperCase()]?.label];
  }
  return {
    ...node,
    type: 'edge',
    roles: roles?.filter(Boolean),
    startid: node.startid || genNodeId(),
    endid: node.endid || genNodeId(),
    direction: node.direction,
    roleType: node.type,
  };
};

export const createCompanyNode = (entity: Partial<RawBiddingCompanyRelationDetail>): CompanyNode => {
  return {
    type: 'node',
    entityType: 'company',
    id: entity['Company.keyno'],
    name: entity['Company.name'],
    label: entity['Company.name'],
  };
};

export const createPersonNode = (entity: Partial<RawBiddingCompanyRelationDetail>): PersonNode => {
  return {
    type: 'node',
    entityType: 'person',
    id: entity['Person.keyno'] || genNodeId(),
    name: entity['Person.name'],
    label: entity['Person.name'],
    isTrueId: entity['Person.keyno'], // 是否是真实id，如果不是真实id就不能跳转
  };
};

export const isCompany = (entity: Partial<RawBiddingCompanyRelationDetail>) => {
  return entity['Company.name'] !== undefined;
};

export const isPerson = (entity: Partial<RawBiddingCompanyRelationDetail>) => {
  return entity['Person.name'] !== undefined;
};

export const isEdge = (entity: Partial<RawBiddingCompanyRelationDetail>) => {
  return [entity.startid, entity.endid, entity.direction].every((t) => t !== undefined);
};
