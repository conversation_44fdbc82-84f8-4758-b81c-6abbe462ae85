import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { RoverUser } from '../../../../libs/model/common';
import { FileResultPO } from './model/FileResultPO';
import { tmpdir } from 'os';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { FileExportTemplate } from '../../file.parser/file.export.template';
import { GroupDimensionVersionEnums } from '../../../../libs/model/diligence/pojo/dimension/group/GroupDimensionVersionEnums';
import { plainToClass } from 'class-transformer';
import { DimensionGroupDefinitionType } from '../../../../libs/model/diligence/pojo/dimension/group/DimensionGroupDefinitionType';
import { v1 } from 'uuid';
import { readFileSync } from 'fs';
import { join } from 'path';
import * as Bluebird from 'bluebird';
import { SettingsService } from '../../../settings/settings.service';
import { BatchResultExportService } from '../../service/batch.result.export.service';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import { Repository } from 'typeorm';
import { Workbook } from 'exceljs';
import { ExportProcessorHelperService } from './export.processor.helper.service';
import { BatchMessageProcessorAbstract } from '../batch.message.processor.abstract';
import { Inject } from '@nestjs/common';
import { ExportConditionBase, ExportConditionRequest, ExportRecordPO } from './model/ExportRecordPO';

export abstract class ExportProcessorBase extends BatchMessageProcessorAbstract {
  protected readonly logger: Logger = QccLogger.getLogger('ExportProcessorBase');
  @Inject()
  protected readonly settingService: SettingsService;
  @Inject()
  protected readonly resultExportService: BatchResultExportService;
  @InjectRepository(BatchEntity) protected readonly batchRepo: Repository<BatchEntity>;
  @Inject()
  protected readonly processorHelper: ExportProcessorHelperService;

  //TODO 需要在
  async processJobMessage() {}

  abstract searchDataAndGeneratorFile<T extends ExportConditionBase>(
    condition: T,
    user: RoverUser,
    businessType: BatchBusinessTypeEnums,
  ): Promise<FileResultPO>;

  abstract applyDataToFile<T extends ExportConditionBase>(
    businessType: BatchBusinessTypeEnums,
    record: ExportRecordPO,
    workbook: Workbook,
    condition: T,
  ): Promise<string>;

  /**
   * 生成导出的结果文件
   * @param condition
   * @param businessType
   * @param record
   */
  public async generateResultFile<T extends ExportConditionBase>(
    condition: T,
    businessType: BatchBusinessTypeEnums,
    record: ExportRecordPO,
  ): Promise<FileResultPO> {
    try {
      const { targetId } = condition as ExportConditionRequest; //导出目标的batchId,在批量排查报告中有效
      let fileName: string;
      if (targetId) {
        fileName = `diligence_detail_${targetId}_${new Date().getTime()}.xlsx`;
      } else {
        fileName = `export_${new Date().getTime()}.xlsx`;
      }
      const filepath = `${tmpdir()}/${fileName}`;
      //判断当前使用的orgSetting版本
      const orgSetting = await this.processorHelper.getOrgSetting(condition.orgId, condition?.targetId);
      const setting = orgSetting.content;
      if (!setting) {
        this.logger.error(`get setting error! condition: ${JSON.stringify(condition)} `);
      }
      const version = FileExportTemplate[businessType][orgSetting.groupVersion] ? orgSetting.groupVersion : GroupDimensionVersionEnums.V1;
      let excelParserSetting = FileExportTemplate[businessType][version];
      let foundError = false;
      if (!excelParserSetting) {
        const set2 = plainToClass(DimensionGroupDefinitionType, setting);
        const version2 = FileExportTemplate[businessType][set2.version] ? set2.version : GroupDimensionVersionEnums.V1;
        excelParserSetting = FileExportTemplate[businessType][version2];
        foundError = true;
      }

      if (foundError || !excelParserSetting) {
        const uuid = v1();
        this.logger.error(
          `failed to parse excelParserSetting(uuid=${uuid}) parsedError=${foundError}, businessType=${businessType}, version=${setting.version}`,
        );
        if (!excelParserSetting) {
          this.logger.error(`uuid=${uuid}, 最终也没有找到`);
        }
        const set2 = plainToClass(DimensionGroupDefinitionType, setting);
        const version2 = FileExportTemplate[businessType][set2.version] ? set2.version : GroupDimensionVersionEnums.V1;
        this.logger.error(`uuid=${uuid},类型转换前, ${null == FileExportTemplate[businessType][version]}`);
        this.logger.error(`uuid=${uuid},转换前 setting=${JSON.stringify(setting)}`);
        this.logger.error(`uuid=${uuid},类型转换后, ${null == FileExportTemplate[businessType][version2]}`);
        this.logger.error(`uuid=${uuid},转换后 setting=${JSON.stringify(setting)}`);
        this.logger.error(`uuid=${uuid}, template=${JSON.stringify(FileExportTemplate[businessType])}`);
        this.logger.error(`uuid=${uuid}, templateCode=${readFileSync(join(__dirname, '../file.parser/file.export.template.js'))}`);
      }
      const { workbook } = await this.resultExportService.getExcelByTemplate(filepath, excelParserSetting);
      const exportFileName = await this.applyDataToFile(businessType, record, workbook, condition);
      if (!exportFileName) {
        const msg = `generateResultFile unknown batch business type: ${businessType}`;
        this.logger.warn(msg);
        return {};
      }
      //await workbook.commit();
      await workbook.xlsx.writeFile(filepath);
      await Bluebird.delay(200);
      const displayName = `${exportFileName}.xlsx`;
      const exportUrl = await this.resultExportService.putFileToOss(filepath, fileName, displayName);
      return { fileUrl: exportUrl, fileName: displayName };
    } catch (e) {
      this.logger.error(`generateResultFile error: ${e}`);
      this.logger.error(e);
      throw new Error(`generateResultFile error: ${e}`);
    }
  }
}
