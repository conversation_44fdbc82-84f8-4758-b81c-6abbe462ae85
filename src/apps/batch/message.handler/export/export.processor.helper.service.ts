import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { GroupsEntity } from '../../../../libs/entities/GroupsEntity';
import { CustomerLabelEntity } from '../../../../libs/entities/CustomerLabelEntity';
import { InnerBlacklistLabelEntity } from '../../../../libs/entities/InnerBlacklistLabelEntity';
import { GroupType } from '../../../../libs/model/element/CreateGroupModel';
import { LabelEntity } from '../../../../libs/entities/LabelEntity';
import { CustomerEntity } from '../../../../libs/entities/CustomerEntity';
import { InnerBlacklistEntity } from '../../../../libs/entities/InnerBlacklistEntity';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import { SettingsService } from '../../../settings/settings.service';
import { SettingTypeEnums } from '../../../../libs/model/settings/SettingTypeEnums';
import { OrgSettingsLogEntity } from '../../../../libs/entities/OrgSettingsLogEntity';
import { Cacheable } from 'type-cacheable';

@Injectable()
export class ExportProcessorHelperService {
  constructor(
    @InjectRepository(GroupsEntity) private readonly groupRepo: Repository<GroupsEntity>,
    @InjectRepository(CustomerLabelEntity) private readonly customerLabelRepo: Repository<CustomerLabelEntity>,
    @InjectRepository(InnerBlacklistLabelEntity) private readonly innerBlacklistLabelRepo: Repository<InnerBlacklistLabelEntity>,
    @InjectRepository(BatchEntity) protected readonly batchRepo: Repository<BatchEntity>,
    private readonly settingService: SettingsService,
  ) {}

  @Cacheable({ ttlSeconds: 3 })
  async getOrgSetting(orgId: number, batchId: number): Promise<OrgSettingsLogEntity> {
    let settingId;
    if (batchId) {
      const batch = await this.batchRepo.findOne(batchId);
      settingId = batch?.batchInfo?.settingId;
    }
    //判断当前使用的orgSetting版本
    const orgSetting = await this.settingService.getOrgSettings(orgId, SettingTypeEnums.diligence_risk, settingId);
    return orgSetting;
  }

  /**
   * 获取分组信息并构建成map
   * @param orgId
   * @param groupType
   * @private
   */
  @Cacheable({ ttlSeconds: 3 })
  async getGroupKV(orgId: number, groupType: GroupType) {
    const groupList = await this.groupRepo.find({ orgId, groupType });
    const groupMap = {};
    groupList?.map((group) => {
      Object.assign(groupMap, { [group.groupId]: group.name });
    });
    return groupMap;
  }

  @Cacheable({ ttlSeconds: 3 })
  async getLabelKV(orgId: number, labelType: number) {
    if (labelType == 1) {
      const queryBuilder = await this.customerLabelRepo
        .createQueryBuilder('cl')
        .select('cl.customer_id', 'customer_id')
        .addSelect("GROUP_CONCAT(l.name SEPARATOR ',')", 'label_name')
        .leftJoin(CustomerEntity, 'c', 'cl.customer_id = c.id')
        .leftJoin(LabelEntity, 'l', 'cl.label_id = l.id')
        .where('c.orgId = :orgId', { orgId })
        .groupBy('customer_id');

      const labelList = await queryBuilder.getRawMany();
      const labelMap = {};
      labelList?.map((label) => {
        Object.assign(labelMap, { [Number(label?.customer_id)]: label?.label_name });
      });
      return labelMap;
    } else if (labelType == 3) {
      const queryBuilder = await this.innerBlacklistLabelRepo
        .createQueryBuilder('cl')
        .select('cl.inner_blacklist_id', 'inner_blacklist_id')
        .addSelect("GROUP_CONCAT(l.name SEPARATOR ',')", 'label_name')
        .leftJoin(InnerBlacklistEntity, 'c', 'cl.inner_blacklist_id = c.id')
        .leftJoin(LabelEntity, 'l', 'cl.label_id = l.id')
        .where('c.orgId = :orgId', { orgId })
        .groupBy('inner_blacklist_id');

      const labelList = await queryBuilder.getRawMany();
      const labelMap = {};
      labelList?.map((label) => {
        Object.assign(labelMap, { [Number(label?.inner_blacklist_id)]: label?.label_name });
      });
      return labelMap;
    }
  }
}
