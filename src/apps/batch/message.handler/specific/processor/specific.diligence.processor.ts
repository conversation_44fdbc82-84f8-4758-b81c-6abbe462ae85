import { BatchMessageProcessorAbstract } from '../../batch.message.processor.abstract';
import { Injectable } from '@nestjs/common';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { SpecificDiligenceRecord } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { BatchResultPO } from '../../../model/BatchResultPO';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { DiligenceBiddingRequest } from '../../../../../libs/model/bidding/DiligenceBiddingRequest';
import { processBatchJobFailed } from '../../../../../libs/utils/utils.bundle';
import * as Bluebird from 'bluebird';
import { BatchBaseHelper } from '../../../service/helper/batch.base.helper';
import { UserService } from '../../../../user/user.service';
import { SpecificRelationshipHelper } from '../../../../specific/helper/specific.relationship.helper';
import { SpecificServiceHelper } from '../../../../specific/helper/specific.service.helper';
import { SpecificFacadeService } from '../../../../specific/specific.facade.service';

@Injectable()
export class SpecificDiligenceProcessor extends BatchMessageProcessorAbstract {
  constructor(
    private readonly batchHelperService: BatchBaseHelper,
    private readonly userService: UserService,
    private readonly specificRelationshipHelper: SpecificRelationshipHelper,
    private readonly specificServiceHelper: SpecificServiceHelper,
    private readonly specificFacadeService: SpecificFacadeService,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Specific_Async_Diligence];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, orgId, jobId } = message;
    this.logger.info(`processJobMessage SpecificDiligence batchId:${batchId}, jobId:${jobId}, length:${items?.length}, items:${JSON.stringify(items)}`);
    const data: SpecificDiligenceRecord[] = items as SpecificDiligenceRecord[];
    const batchEntity = await this.batchHelperService.getOneBatch(batchId);
    const params: DiligenceBiddingRequest = batchEntity.batchInfo?.params;
    await Bluebird.map(
      data,
      async (record: SpecificDiligenceRecord) => {
        const { dimensionSettings } = record;
        const resultList = [];
        const resultBase: BatchResultPO = {
          batchId,
          jobId,
          resultInfo: record,
          resultHashkey: record.recordHashkey,
          resultType: BatchJobResultTypeEnums.SUCCEED_UNPAID,
          result: null,
        };
        try {
          const dimensionHitsDetails = await this.specificServiceHelper.doSpecificRiskScan(params, dimensionSettings, orgId);
          dimensionHitsDetails.forEach((hitDetail) => {
            const result: BatchResultPO = Object.assign(new BatchResultPO(), resultBase);
            result.result = hitDetail;
            result.resultType = BatchJobResultTypeEnums.SUCCEED_PAID;
            resultList.push(result);
          });
          await this.batchHelperService.handleJobResult(resultList);
        } catch (e) {
          //出错后保存到数据库中
          this.logger.error(`Specific_Diligence error: batchId=${batchId},jobId=${jobId},param=${JSON.stringify(record)},error=${e}`);
          this.logger.error(e);
          processBatchJobFailed(e, resultBase);
          await this.batchHelperService.handleJobResult([resultBase]);
        }
      },
      { concurrency: 10 },
    );
    this.logger.info(`processJobMessage Specific_Diligence Finished: batchId=${batchId},jobId=${jobId}`);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    const { diligenceId } = batchEntity.batchInfo;
    const currentUser = await this.userService.getRoverUser(batchEntity.creatorId, batchEntity.orgId);
    await this.specificFacadeService.diligenceError(currentUser, diligenceId);
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    const batchResults = await this.batchHelperService.getBatchResult(batchEntity.batchId);
    const hitDetails = batchResults.map((result) => result.result);
    const { params, diligenceId } = batchEntity.batchInfo;
    params.orgId = batchEntity.orgId;
    await this.specificFacadeService.diligenceSuccess(params, hitDetails, diligenceId);
    return Promise.resolve(undefined);
  }

  onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }
}
