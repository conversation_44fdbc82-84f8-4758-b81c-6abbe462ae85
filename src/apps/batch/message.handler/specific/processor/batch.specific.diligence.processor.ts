import { BatchMessageProcessorAbstract } from '../../batch.message.processor.abstract';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { UserService } from '../../../../user/user.service';
import * as Bluebird from 'bluebird';
import { BiddingDiligenceExcelRecord } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { BatchResultPO } from '../../../model/BatchResultPO';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { CompanySearchService } from '../../../../company/company-search.service';
import { BatchBaseHelper } from '../../../service/helper/batch.base.helper';
import { SpecificFacadeService } from '../../../../specific/specific.facade.service';
import { SpecificRiskRequest } from '../../../../../libs/model/specific/model/SpecificRiskRequest';
import { JointBiddingAnalysisModel } from '../../../../../libs/model/tender/JointBiddingAnalysisModel';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BatchSpecificDiligenceEntity } from '../../../../../libs/entities/BatchSpecificDiligenceEntity';
import { processBatchJobFailed } from '../../../../../libs/utils/utils.bundle';

@Injectable()
export class BatchSpecificDiligenceProcessor extends BatchMessageProcessorAbstract {
  protected readonly logger = QccLogger.getLogger(BatchSpecificDiligenceProcessor.name);

  constructor(
    private readonly batchHelperService: BatchBaseHelper,
    private readonly companyService: CompanySearchService,
    private readonly userService: UserService,
    private readonly specificFacadeService: SpecificFacadeService,
    @InjectRepository(BatchSpecificDiligenceEntity) private readonly batchSpecificDiligenceRepo: Repository<BatchSpecificDiligenceEntity>,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Specific_Diligence_File];
  }

  onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  /**
   * 处理job消息
   * @param message
   */
  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, orgId, operatorId, jobId } = message;
    this.logger.info(
      `processJobMessage batchCreateSpecificDiligence batchId:${batchId}, jobId:${jobId}, length:${items?.length}, items:${JSON.stringify(items)}`,
    );
    const currentUser = await this.userService.getRoverUser(operatorId, orgId);
    const data: BiddingDiligenceExcelRecord[] = items;
    await Bluebird.map(
      data,
      async (excelRecord) => {
        const result: BatchResultPO = {
          batchId,
          jobId,
          resultInfo: excelRecord,
          resultHashkey: excelRecord.recordHashkey,
          resultType: BatchJobResultTypeEnums.SUCCEED_UNPAID,
        };
        try {
          const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companyService.matchCompanyInfo(excelRecord.companyNames);
          if (unmatchedNames?.length || unsupported?.length) {
            result.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
            result.comment = '';
            if (unmatchedNames?.length) {
              result.comment += `未识别的企业：${unmatchedNames.join(',')}`;
            }
            if (unsupported?.length) {
              if (result.comment?.length) {
                result.comment += '；';
              }
              result.comment += `不支持的企业：${unsupported.join(',')}`;
            }
            await this.batchHelperService.handleJobResult([result]);
            return;
          }
          const res = await this.specificFacadeService.scanSpecificRisk(
            currentUser,
            Object.assign(new SpecificRiskRequest(), {
              projectNo: excelRecord.projectNo,
              projectName: excelRecord.projectName,
              keyNoAndNames: matchedCompanyInfos.map((x) =>
                Object.assign(new JointBiddingAnalysisModel(), {
                  companyId: x.id,
                  companyName: x.name,
                }),
              ),
              keyNos: matchedCompanyInfos.map((x) => x.id),
              orgId,
            }),
            // true,
          );

          result.resultType = BatchJobResultTypeEnums.SUCCEED_PAID;
          await Bluebird.all([
            this.batchHelperService.handleJobResult([result]),
            this.batchSpecificDiligenceRepo.save(
              Object.assign(new BatchSpecificDiligenceEntity(), {
                batchId,
                jobId,
                diligenceId: res.id,
              }),
            ),
            this.specificFacadeService.saveBatchDiligenceDetail(batchId, res),
          ]);
        } catch (e) {
          //出错后保存到数据库中
          this.logger.error(`batchCreateSpecificDiligence error: batchId=${batchId},jobId=${jobId},param=${JSON.stringify(excelRecord)},error=${e}`);
          this.logger.error(e);
          processBatchJobFailed(e, result);
          await this.batchHelperService.handleJobResult([result]);
        }
      },
      { concurrency: 2 },
    );
    this.logger.info(`processJobMessage batchCreateSpecificDiligence Finished: batchId=${batchId},jobId=${jobId}`);
  }
}
