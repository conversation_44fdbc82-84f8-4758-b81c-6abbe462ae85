import { BatchMessageHandlerAbstract } from '../batch.message.handler.abstract';
import { Injectable } from '@nestjs/common';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { QueueService } from '../../../../libs/config/queue.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { BatchConstants } from '../../common/batch.constants';
import { BatchSpecificDiligenceProcessor } from './processor/batch.specific.diligence.processor';
import { SpecificDiligenceProcessor } from './processor/specific.diligence.processor';

@Injectable()
export class BatchMessageHandlerSpecific extends BatchMessageHandlerAbstract {
  public batchSpecificQueue: RabbitMQ;

  constructor(
    protected readonly queueService: QueueService,
    protected readonly redisService: RedisService,
    private readonly batchSpecificDiligenceProcessor: BatchSpecificDiligenceProcessor,
    private readonly specificDiligenceProcessor: SpecificDiligenceProcessor,
  ) {
    super(queueService, redisService);
    this.allProcessor.push(this.batchSpecificDiligenceProcessor);
    this.allProcessor.push(this.specificDiligenceProcessor);
    this.batchSpecificQueue = this.queueService.batchSpecificQueue;
    this.batchSpecificQueue.consume(this.handleJobMessage.bind(this), BatchConstants.Consumer.Diligence).catch((err) => this.logger.error(err));
    this.batchSpecificQueue.on('failed', this.onPulsarQueueError.bind(this));
  }

  getTargetQueue(businessType?: BatchBusinessTypeEnums): RabbitMQ {
    return this.batchSpecificQueue;
  }
}
