import { TenderProcessorBase } from '../tender.processor.base';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { CompanySearchService } from '../../../../company/company-search.service';
import { UserService } from '../../../../user/user.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { BiddingDiligenceExcelRecord } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { DiligenceBiddingRequest } from '../../../../../libs/model/bidding/DiligenceBiddingRequest';
import { JointBiddingAnalysisModel } from '../../../../../libs/model/tender/JointBiddingAnalysisModel';
import { processBatchJobFailed } from '../../../../../libs/utils/utils.bundle';
import * as Bluebird from 'bluebird';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchResultPO } from '../../../model/BatchResultPO';
import { BatchBaseHelper } from '../../../service/helper/batch.base.helper';
import { BatchTenderDiligenceEntity } from '../../../../../libs/entities/BatchTenderDiligenceEntity';
import { BiddingDiligenceService } from '../../../../bidding/service/bidding.diligence.service';

export class BatchBiddingDiligenceProcessor extends TenderProcessorBase {
  protected readonly logger = QccLogger.getLogger(TenderProcessorBase.name);

  constructor(
    private readonly companyService: CompanySearchService,
    private readonly userService: UserService,
    @InjectRepository(BatchTenderDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchTenderDiligenceEntity>,
    private readonly biddingDiligenceService: BiddingDiligenceService,
    private readonly batchHelperService: BatchBaseHelper,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Bidding_Diligence_File];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, orgId, operatorId, jobId } = message;
    this.logger.info(
      `processJobMessage batchCreateBiddingDiligence batchId:${batchId}, jobId:${jobId}, length:${items?.length}, items:${JSON.stringify(items)}`,
    );
    const currentUser = await this.userService.getRoverUser(operatorId, orgId);
    const data: BiddingDiligenceExcelRecord[] = items;
    await Bluebird.map(
      data,
      async (excelRecord) => {
        const result: BatchResultPO = {
          batchId,
          jobId,
          resultInfo: excelRecord,
          resultHashkey: excelRecord.recordHashkey,
          resultType: BatchJobResultTypeEnums.SUCCEED_UNPAID,
        };
        try {
          const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companyService.matchCompanyInfo(excelRecord.companyNames);
          if (unmatchedNames?.length || unsupported?.length) {
            result.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
            result.comment = '';
            if (unmatchedNames?.length) {
              result.comment += `未识别的企业：${unmatchedNames.join(',')}`;
            }
            if (unsupported?.length) {
              if (result.comment?.length) {
                result.comment += '；';
              }
              result.comment += `不支持的企业：${unsupported.join(',')}`;
            }
            await this.batchHelperService.handleJobResult([result]);
            return;
          }
          const res = await this.biddingDiligenceService.scanRisk(
            currentUser,
            Object.assign(new DiligenceBiddingRequest(), {
              projectNo: excelRecord.projectNo,
              projectName: excelRecord.projectName,
              keyNoAndNames: matchedCompanyInfos.map((x) =>
                Object.assign(new JointBiddingAnalysisModel(), {
                  companyId: x.id,
                  companyName: x.name,
                }),
              ),
              keyNos: matchedCompanyInfos.map((x) => x.id),
              orgId,
            }),
            // true,
          );

          result.resultType = BatchJobResultTypeEnums.SUCCEED_PAID;
          await Bluebird.all([
            this.batchHelperService.handleJobResult([result]),
            this.batchDiligenceRepo.save(
              Object.assign(new BatchTenderDiligenceEntity(), {
                batchId,
                jobId,
                diligenceId: res.id,
              }),
            ),
            this.biddingDiligenceService.saveBatchDiligenceDetail(batchId, res),
          ]);
        } catch (e) {
          //出错后保存到数据库中
          this.logger.error(`batchCreateBiddingDiligence error: batchId=${batchId},jobId=${jobId},param=${JSON.stringify(excelRecord)},error=${e}`);
          this.logger.error(e);
          processBatchJobFailed(e, result);
          await this.batchHelperService.handleJobResult([result]);
        }
      },
      { concurrency: 2 },
    );
    this.logger.info(`processJobMessage batchCreateBiddingDiligence Finished: batchId=${batchId},jobId=${jobId}`);
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
