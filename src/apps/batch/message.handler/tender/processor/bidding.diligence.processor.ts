import { TenderProcessorBase } from '../tender.processor.base';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { BiddingDiligenceRecord } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { DiligenceBiddingRequest } from '../../../../../libs/model/bidding/DiligenceBiddingRequest';
import { processBatchJobFailed } from '../../../../../libs/utils/utils.bundle';
import * as Bluebird from 'bluebird';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchResultPO } from '../../../model/BatchResultPO';
import { BatchBaseHelper } from '../../../service/helper/batch.base.helper';
import { DimensionLevel2Enums } from '../../../../../libs/enums/diligence/DimensionLevel2Enums';
import { HitDetailsBidBaseQueryParams } from '../../../../../libs/model/diligence/pojo/req&res/details/request/HitDetailsBidBaseQueryParams';
import { DimensionLevel1Enums } from '../../../../../libs/enums/diligence/DimensionLevel1Enums';
import { BiddingDiligenceRelationshipHelper } from '../../../../bidding/helper/bidding.diligence.relationship.helper';
import { BiddingDiligenceService } from '../../../../bidding/service/bidding.diligence.service';
import { Injectable } from '@nestjs/common';
import { BiddingDimensionHitsDetails } from '../../../../../libs/model/bidding/DiligenceBiddingResponse';
import { DimensionDefinitionPO } from '../../../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { pick, sum } from 'lodash';
import { UserService } from '../../../../user/user.service';
import { BiddingDiligenceHelper } from '../../../../bidding/helper/bidding.diligence.helper';

@Injectable()
export class BiddingDiligenceProcessor extends TenderProcessorBase {
  protected readonly logger = QccLogger.getLogger(TenderProcessorBase.name);

  constructor(
    private readonly biddingDiligenceService: BiddingDiligenceService,
    private readonly biddingRelationshipHelper: BiddingDiligenceRelationshipHelper,
    private readonly biddingDiligenceHelper: BiddingDiligenceHelper,
    private readonly batchHelperService: BatchBaseHelper,
    private readonly userService: UserService,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Bidding_Diligence];
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<any> {
    const { batchId, items, orgId, jobId } = message;
    this.logger.info(`processJobMessage BiddingDiligence batchId:${batchId}, jobId:${jobId}, length:${items?.length}, items:${JSON.stringify(items)}`);
    const data: BiddingDiligenceRecord[] = items as BiddingDiligenceRecord[];
    await Bluebird.map(
      data,
      async (record: BiddingDiligenceRecord) => {
        const { diligenceId, dimensionKey, dimensionDefinition, keyNos } = record;
        const result: BatchResultPO = {
          batchId,
          jobId,
          resultInfo: record,
          resultHashkey: record.recordHashkey,
          resultType: BatchJobResultTypeEnums.SUCCEED_UNPAID,
          result: null,
        };
        try {
          if (dimensionKey == DimensionLevel2Enums.BiddingCompanyRelation) {
            // 关系排查
            result.result = await this.biddingRelationshipHelper.getBiddingCompanyRelations(
              [dimensionDefinition],
              Object.assign(new DiligenceBiddingRequest(), { keyNos, orgId, diligenceId }),
            );
          } else {
            const batchEntity = await this.batchHelperService.getOneBatch(batchId);
            const params: DiligenceBiddingRequest = batchEntity.batchInfo?.params;
            const detailParams: HitDetailsBidBaseQueryParams = {
              orgId,
              pageIndex: 1,
              pageSize: 500,
              keyNoAndNames: params.keyNoAndNames,
              keyNos: params.keyNoAndNames?.map((e) => e.companyId),
            };
            switch (dimensionKey) {
              // 共同投标分析
              case DimensionLevel2Enums.JointBiddingAnalysis:
                result.result = await this.biddingDiligenceHelper.getJointBiddingAnalysis([dimensionDefinition], detailParams);
                break;
              // 内部黑名单排查
              case DimensionLevel1Enums.Risk_InnerBlacklist:
                result.result = await this.biddingDiligenceHelper.getInnerBlackList([dimensionDefinition], detailParams);
                break;
              // 潜在利益冲突
              case DimensionLevel1Enums.Risk_InterestConflict:
                result.result = await this.biddingDiligenceHelper.getInterestConflict([dimensionDefinition], detailParams);
                break;
              // 涉采购不良行为
              case DimensionLevel2Enums.PurchaseIllegal:
                result.result = await this.biddingDiligenceHelper.getPurchaseIllegal([dimensionDefinition], detailParams);
                break;
              // 资质筛查
              case DimensionLevel2Enums.BiddingCompanyCertification:
                result.result = await this.biddingDiligenceHelper.getCertificationV2(
                  [dimensionDefinition],
                  detailParams,
                  params.certification,
                  diligenceId,
                  orgId,
                );
                break;
              default:
                break;
            }
          }
          result.resultType = BatchJobResultTypeEnums.SUCCEED_PAID;
          await this.batchHelperService.handleJobResult([result]);
        } catch (e) {
          //出错后保存到数据库中
          this.logger.error(`Bidding_Diligence error: batchId=${batchId},jobId=${jobId},param=${JSON.stringify(record)},error=${e}`);
          this.logger.error(e);
          processBatchJobFailed(e, result);
          await this.batchHelperService.handleJobResult([result]);
        }
      },
      { concurrency: 10 },
    );
    this.logger.info(`processJobMessage Bidding_Diligence Finished: batchId=${batchId},jobId=${jobId}`);
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    const hitDetails: BiddingDimensionHitsDetails[] = [];
    const relations1 = [];
    const relations2 = [];
    const batchResults = await this.batchHelperService.getBatchResult(batchEntity.batchId);

    batchResults.forEach((r) => {
      const result = r?.result;
      if (result) {
        if (result?.key !== DimensionLevel2Enums.BiddingCompanyRelation) {
          hitDetails.push(r.result);
        } else if (result.totalHits > 0) {
          const subDimRelation = result.subDimension.find((x) => x.key == DimensionLevel2Enums.BiddingCompanyRelationship);
          const subDimRelation2 = result.subDimension.find((x) => x.key == DimensionLevel2Enums.BiddingCompanyRelationship2);
          if (subDimRelation?.data?.length > 0) {
            relations1.push(...subDimRelation.data);
          }
          if (subDimRelation2?.data?.length > 0) {
            relations2.push(...subDimRelation2.data);
          }
        }
      }
    });
    const dimensionDefinition = batchResults.find((r) => r.result.key == DimensionLevel2Enums.BiddingCompanyRelation).resultInfo['dimensionDefinition'];
    const relationHitsDetails = this.getBiddingCompanyRelations(dimensionDefinition, relations1, relations2);
    hitDetails.push(relationHitsDetails);

    const { params, diligenceId } = batchEntity.batchInfo;
    await this.biddingDiligenceService.diligenceSuccess(diligenceId, params, hitDetails);
    return Promise.resolve(undefined);
  }

  /**
   * 关系排查结果处理
   * @param dimensionDefinition
   * @param relations1
   * @param relations2
   * @returns
   */
  private getBiddingCompanyRelations(dimensionDefinition: DimensionDefinitionPO, relations1: any[], relations2: any[]) {
    const result = Object.assign(new BiddingDimensionHitsDetails(), pick(dimensionDefinition, ['key', 'name', 'status', 'sort']), { totalHits: 0 });
    if (dimensionDefinition.status == 0) {
      return result;
    }
    result.subDimension = [];

    const companyLinkMap: Map<string, Set<string>> = new Map<string, Set<string>>();

    // 投标公司直接关系排查
    const relationShipDim = dimensionDefinition.subDimensionList.find((x) => x.status == 1 && x.key == DimensionLevel2Enums.BiddingCompanyRelationship);
    if (relationShipDim) {
      const hitDetail = Object.assign(new BiddingDimensionHitsDetails(), pick(relationShipDim, ['key', 'name', 'status', 'sort']), {
        totalHits: 0,
        data: [],
      });

      if (relations1?.length > 0) {
        hitDetail.totalHits = relations1.length;
        hitDetail.data = relations1;

        // 记录两家企业之间已经存在直接关联关系，后续不再进行疑似关联排查
        relations1.forEach((x) => {
          if (companyLinkMap.has(x.startCompanyKeyno)) {
            companyLinkMap.get(x.startCompanyKeyno).add(x.endCompanyKeyno);
          } else {
            companyLinkMap.set(x.startCompanyKeyno, new Set([x.endCompanyKeyno]));
          }
        });
      }

      result.subDimension.push(hitDetail);
    }

    // 投标公司疑似关系排查
    const relationShip2Dim = dimensionDefinition.subDimensionList.find((x) => x.status == 1 && x.key == DimensionLevel2Enums.BiddingCompanyRelationship2);
    if (relationShip2Dim) {
      const hitDetail = Object.assign(new BiddingDimensionHitsDetails(), pick(relationShip2Dim, ['key', 'name', 'status', 'sort']), {
        totalHits: 0,
        data: [],
      });
      relations2 = relations2.filter((x) => {
        if (companyLinkMap.has(x.startCompanyKeyno) && companyLinkMap.get(x.startCompanyKeyno).has(x.endCompanyKeyno)) {
          return false;
        }
        if (companyLinkMap.has(x.endCompanyKeyno) && companyLinkMap.get(x.endCompanyKeyno).has(x.startCompanyKeyno)) {
          return false;
        }
        return true;
      });

      if (relations2?.length > 0) {
        hitDetail.totalHits = relations2.length;
        hitDetail.data = relations2;
      }
      result.subDimension.push(hitDetail);
    }

    result.totalHits = sum(result.subDimension.map((x) => x.totalHits));
    return result;
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    const { diligenceId } = batchEntity.batchInfo;
    const currentUser = await this.userService.getRoverUser(batchEntity.creatorId, batchEntity.orgId);
    await this.biddingDiligenceService.diligenceError(currentUser, diligenceId);
    return Promise.resolve(undefined);
  }
}
