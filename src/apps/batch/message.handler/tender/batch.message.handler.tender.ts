import { Injectable } from '@nestjs/common';
import { BatchConstants } from '../../common/batch.constants';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchMessageHandlerAbstract } from '../batch.message.handler.abstract';
import { BatchBiddingDiligenceProcessor } from './processor/batch.bidding.diligence.processor';
import { QueueService } from '../../../../libs/config/queue.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { BiddingDiligenceProcessor } from './processor/bidding.diligence.processor';

@Injectable()
export class BatchMessageHandlerTender extends BatchMessageHandlerAbstract {
  public batchTenderQueue: RabbitMQ;

  constructor(
    private readonly batchBiddingDiligenceProcessor: BatchBiddingDiligenceProcessor,
    private readonly biddingDiligenceProcessor: BiddingDiligenceProcessor,
    protected readonly queueService: QueueService,
    protected readonly redisService: RedisService,
  ) {
    super(queueService, redisService);
    this.allProcessor.push(this.batchBiddingDiligenceProcessor);
    this.allProcessor.push(this.biddingDiligenceProcessor);
    this.batchTenderQueue = this.queueService.batchTenderQueue;
    this.batchTenderQueue.consume(this.handleJobMessage.bind(this), BatchConstants.Consumer.Diligence).catch((err) => this.logger.error(err));
    this.batchTenderQueue.on('failed', this.onPulsarQueueError.bind(this));
  }

  getTargetQueue(businessType: BatchBusinessTypeEnums): RabbitMQ {
    return this.batchTenderQueue;
  }
}
