import { BatchJobMessagePO } from '../../../libs/model/batch/po/message/BatchJobMessagePO';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { In, Repository } from 'typeorm';
import { BatchJobEntity } from '../../../libs/entities/BatchJobEntity';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchStatusEnums } from '../../../libs/enums/batch/BatchStatusEnums';
import { InjectRepository } from '@nestjs/typeorm';

export abstract class BatchMessageProcessorAbstract {
  @InjectRepository(BatchJobEntity)
  protected readonly batchJobRepo: Repository<BatchJobEntity>;

  protected logger: Logger = QccLogger.getLogger('BatchMessageProcessor');

  abstract processJobMessage(message: BatchJobMessagePO): Promise<any>;

  abstract onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any>;

  abstract onBatchError(batchEntity: BatchEntity): Promise<any>;

  abstract onBatchSuccess(batchEntity: BatchEntity): Promise<any>;

  abstract getBusinessType(): BatchBusinessTypeEnums[];

  match(businessType: BatchBusinessTypeEnums): boolean {
    return this.getBusinessType()?.some((r) => r === businessType);
  }

  /**
   * 默认情况下，当所有任务都完成时，批次才算成功
   * 如果需要自定义，请重写该方法
   * @param batchId
   */
  async shouldBatchFinished(batchEntity: BatchEntity): Promise<boolean> {
    // if (batchEntity.status == BatchStatusEnums.Caneled) {
    //   return true;
    // }
    const notFinishedJobs = await this.batchJobRepo.count({
      where: {
        batchId: batchEntity.batchId,
        status: In([BatchStatusEnums.Waiting, BatchStatusEnums.Processing, BatchStatusEnums.Queuing]),
      },
    });
    return notFinishedJobs === 0;
  }
}
