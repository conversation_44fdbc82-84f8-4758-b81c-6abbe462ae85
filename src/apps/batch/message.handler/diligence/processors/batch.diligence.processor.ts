import { Inject, Injectable } from '@nestjs/common';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { BatchRunDDResultPO } from '../../../../../libs/model/batch/po/BatchRunDDResultPO';
import { BatchDiligenceExcelRecord } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { CompanySearchService } from '../../../../company/company-search.service';
import { GetDiligenceResultParams } from '../../../../../libs/model/diligence/pojo/req&res/GetDiligenceResultParams';
import { DDCostItemPO, DDCostPO } from '../../../../../libs/model/batch/po/DDCostItemPO';
import { BatchConstants } from '../../../common/batch.constants';
import { DiligenceResponseV2 } from '../../../../../libs/model/diligence/pojo/req&res/DiligenceResponseV2';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { DiligenceHistoryEntity } from '../../../../../libs/entities/DiligenceHistoryEntity';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchDiligenceEntity } from '../../../../../libs/entities/BatchDiligenceEntity';
import { processBatchJobFailed } from '../../../../../libs/utils/utils.bundle';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BatchBaseHelper } from '../../../service/helper/batch.base.helper';
import * as Bluebird from 'bluebird';
import { UserService } from '../../../../user/user.service';
import { EvaluationService } from '../../../../diligence/evaluation/evaluation.service';
import { DiligenceProcessorBase } from '../diligence.processor.base';
import { BatchResultPO } from '../../../model/BatchResultPO';
import { CompanyEntity } from '../../../../../libs/entities/CompanyEntity';
import { DiligenceSnapshotHelper } from '../../../../diligence/snapshot/diligence.snapshot.helper';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';
import { DiligenceSnapshotEsService } from '../../../../diligence/snapshot/diligence.snapshot.es.service';
import { SnapshotDimensionMessagePO } from '../../../../../libs/model/diligence/pojo/snapshot/SnapshotDimensionMessagePO';
import { OperationEnums } from '../../../../../libs/enums/diligence/OperationEnums';
import { DiligenceAnalyzeHelperService } from '../../../../diligence/analyze/diligence.analyze.helper.service';

/**
 * 批量排查-excel上传
 * 批量排查-从客商中选择
 * 批量排查-文本输入
 */
@Injectable()
export class BatchDiligenceProcessor extends DiligenceProcessorBase {
  @Inject()
  protected readonly companyService: CompanySearchService;
  @InjectRepository(CompanyEntity) private readonly companyEntity: Repository<CompanyEntity>;
  @Inject()
  protected readonly diligenceAnalyzeHelper: DiligenceAnalyzeHelperService;
  @Inject()
  protected readonly batchHelperService: BatchBaseHelper;

  @Inject()
  protected readonly userService: UserService;

  @Inject()
  protected readonly evaluationService: EvaluationService;

  @Inject()
  protected readonly snapshotHelperService: DiligenceSnapshotHelper;

  protected readonly logger: Logger = QccLogger.getLogger(BatchDiligenceProcessor.name);

  constructor(protected readonly diligenceSnapshotEsService: DiligenceSnapshotEsService) {
    super();
  }

  @InjectRepository(DiligenceHistoryEntity) protected readonly diligenceRepo: Repository<DiligenceHistoryEntity>;

  /**
   * 单个 batchJob 执行
   * @param message 任务信息
   * @param needMatchCompanyInfo 是否需要根据名称匹配公司信息 即未提供companyId
   */
  @TraceLog({ throwError: true })
  public async batchRunDiligence(message: BatchJobMessagePO, needMatchCompanyInfo = false): Promise<BatchRunDDResultPO> {
    const { batchId, items, jobId, orgId } = message;
    const data: BatchDiligenceExcelRecord[] = items;
    const response: BatchRunDDResultPO = new BatchRunDDResultPO();
    response.passedCount = data.length;
    const jobStart = Date.now();
    this.logger.info(`processJobMessage batchRunDiligence: batchId=${batchId},jobId=${jobId},needMatchCompanyInfo=${needMatchCompanyInfo}`);
    if (needMatchCompanyInfo) {
      // 通过name字段匹配公司，匹配不上的，标记错误
      const names: string[] = data.map((d) => d.companyName);
      const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companyService.matchCompanyInfo(names);
      this.logger.info(
        `processJobMessage batchRunDiligence: batchId=${batchId},jobId=${jobId},matchedCount=${matchedCompanyInfos?.length},unmatchedNames=${unmatchedNames}`,
      );
      if (matchedCompanyInfos?.length > 0) {
        //检查对应batchId下面该公司是否已经排查过，如果排查过，则不再排查
        let toDiligenceItems: GetDiligenceResultParams[] = matchedCompanyInfos.map((t) => {
          const jobDataItem = data.find((d) => d.companyId === t.id || d.companyName === t.name);
          return { companyName: t.name, companyId: t.id, settingId: jobDataItem?.recordParams?.settingId };
        });
        const existingDiligenceList = await this.diligenceRepo
          .createQueryBuilder('diligence')
          .where('diligence.orgId = :orgId', { orgId })
          .leftJoin('diligence.batchEntities', 'batchEntities')
          .andWhere('batchEntities.batchId = :batchId', { batchId })
          .andWhere('diligence.companyId IN (:...ids)', { ids: matchedCompanyInfos.map((t) => t.id) })
          .select(['diligence.id', 'diligence.companyId'])
          .getMany();
        if (existingDiligenceList.length > 0) {
          toDiligenceItems = toDiligenceItems.filter((t) => {
            return !existingDiligenceList.some((e) => e.companyId === t.companyId);
          });
        }
        //匹配到的进行批量排查
        const result = await this.runDiligence(toDiligenceItems, message);
        response.ddDetails = result;
      }
      if (unmatchedNames?.length) {
        // 通过name字段匹配公司，匹配不上的，标记错误
        await this.batchHelperService.saveUnMatchedCompanyBatch(unmatchedNames, batchId, jobId);
      }

      if (unsupported?.length) {
        // 通过name字段匹配公司，公司类型不支持的
        await this.batchHelperService.saveUnsupportedCompanyBatch(unsupported, batchId, jobId);
      }

      response.matchedCompanyCount = matchedCompanyInfos?.length || 0;
    } else {
      const result = await this.runDiligence(
        data.map((t) => {
          return {
            companyName: t.companyName,
            companyId: t.companyId,
            settingId: t.recordParams?.settingId,
          };
        }),
        message,
      );
      response.ddDetails = result;
      response.matchedCompanyCount = -1;
    }
    //设置一个平均时间，只有大于这个时间的，才记录 ddCostDetails
    const ddDetails = response.ddDetails;
    const limitSeconds = 5000;
    if (ddDetails && ddDetails.details) {
      if (!response.ddDetails.details) {
        this.logger.warn('error here');
      }
      response.ddDetails.details = ddDetails.details.filter((t) => t.cost > limitSeconds);
    }
    response.totalCost = Date.now() - jobStart;
    return response;
  }

  @TraceLog({ throwError: true })
  public async runDiligence(items: GetDiligenceResultParams[], jobParams: BatchJobMessagePO): Promise<DDCostPO> {
    const { batchId, operatorId, orgId, jobId } = jobParams;
    const results: DDCostItemPO[] = [];
    const concurrency = BatchConstants.JobConcurrency.Diligence;
    const response: DDCostPO = { concurrency, recordCount: items.length, totalCost: 0, details: [] };
    const user = await this.userService.getRoverUser(operatorId, orgId);
    const startTime = Date.now();
    //TODO 这里有个潜在风险点，一批次里面的公司，可能存在某些公司的尽调一直卡住，导致整个批次卡住，直到batch 超时
    await Bluebird.map(
      items,
      async (param: GetDiligenceResultParams) => {
        const dd: DDCostItemPO = {
          companyId: param.companyId,
          cost: 0,
        };
        const start = Date.now();
        try {
          this.logger.info(`runDiligence() start for batchId=${batchId}, jobId=${jobId}, companyId=${param.companyId}`);
          const res: DiligenceResponseV2 = await this.evaluationService.getRiskListForBatchJob(user, param, batchId, param.settingId);
          const { id: diligenceId, paid, details: modelScore } = res;
          dd.diligenceId = diligenceId;
          dd.paid = paid;
          const batchDiligence = await this.batchDiligenceRepo.findOne({
            where: {
              batchId,
              diligenceId,
            },
          });
          const toSave: BatchResultPO = {
            batchId,
            jobId,
            resultInfo: { ...param, diligenceId },
            resultHashkey: jobParams?.items?.find((t) => t.companyId === param.companyId)?.recordHashkey,
            resultType: BatchJobResultTypeEnums.SUCCEED_UNPAID,
          };
          if (batchDiligence) {
            await this.batchHelperService.handleJobResult([Object.assign(toSave, { resultType: BatchJobResultTypeEnums.SUCCEED_DUPLICATED })]);
            this.logger.info(`runDiligence() finished(with existed batchDiligence) for batchId=${batchId}, jobId=${jobId}, companyId=${param.companyId}`);
          } else {
            // await this.evaluationService.getRiskList(user, param); TODO 这里是否需要再次调用？
            // let changingVersion: number | undefined = undefined;
            // let changingDetail: BatchChangingDetailPO | undefined = undefined;
            if (jobParams.businessType === BatchBusinessTypeEnums.Diligence_Customer_Analyze) {
              // 更新查查信用分到customer， 排查是是通过C端接口获取查查信用分，我们的kys_company索引的查查信用分T+1延迟
              if (modelScore.creditRateResult?.Score) {
                await this.companyEntity.update(
                  {
                    companyId: res.companyId,
                  },
                  {
                    creditRate: modelScore.creditRateResult.Score,
                  },
                );
              }

              // // 巡检比较统一个公司两次巡检排查结果的变更情况
              // const prevResult = await this.batchDiligenceRepo
              //   .createQueryBuilder('b')
              //   .select('b')
              //   .innerJoinAndMapOne('b.diligence', DiligenceHistoryEntity, 'd', 'd.id = b.diligenceId')
              //   .innerJoin(BatchEntity, 'bt', 'bt.batchId = b.batchId')
              //   .where('d.orgId = :orgId and d.companyId = :companyId and bt.businessType = :busType', {
              //     orgId,
              //     companyId: res.companyId,
              //     busType: BatchBusinessTypeEnums.Diligence_Customer_Analyze,
              //   })
              //   .orderBy('b.id', 'DESC')
              //   .take(1)
              //   .getOne();

              // const __ret = this.compareDiligence(prevResult, res, changingVersion);
              // changingVersion = __ret.changingVersion || 1;
              // changingDetail = {
              //   diligenceId: res.id,
              //   prevDiligenceId: (prevResult?.['diligence'] as DiligenceHistoryEntity)?.id,
              //   dimensionVersions: __ret.changingDimensionVersions,
              // };
            }

            await Bluebird.all([
              this.batchHelperService.handleJobResult([
                Object.assign(toSave, { resultType: paid ? BatchJobResultTypeEnums.SUCCEED_PAID : BatchJobResultTypeEnums.SUCCEED_UNPAID }),
              ]),
              this.batchDiligenceRepo.save(
                Object.assign(new BatchDiligenceEntity(), {
                  batchId,
                  jobId,
                  diligenceId,
                  // changingVersion,
                  // changingDetail,
                }),
              ),
            ]);
            this.logger.info(`runDiligence() finished for batchId=${batchId}, jobId=${jobId}, companyId=${param.companyId}`);
          }
        } catch (e) {
          //出错后保存到数据库中
          this.logger.error(`runDiligence error: batchId=${batchId},jobId=${jobId},param=${JSON.stringify(param)},error=${e.message}`);
          this.logger.error(e);
          // const isBundleError = e instanceof BundleReachedLimitationException;
          // let message.handler = e.message.handler;
          // if (isBundleError) {
          //   const errorRes: any = e.getResponse();
          //   message.handler = `${e.name}:${errorRes?.field}:${errorRes?.error}`;
          // }
          const batchResult: BatchResultPO = {
            batchId,
            jobId,
            resultInfo: param,
            resultHashkey: jobParams?.items?.find((t) => t.companyId === param.companyId)?.recordHashkey,
            resultType: BatchJobResultTypeEnums.FAILED_CODE,
          };
          processBatchJobFailed(e, batchResult);
          await this.batchHelperService.handleJobResult([batchResult]);
        }
        dd.cost = Date.now() - start;
        results.push(dd);
      },
      { concurrency },
    );
    response.details = results;
    response.totalCost = Date.now() - startTime;
    return response;
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Diligence_File, BatchBusinessTypeEnums.Diligence_Customer, BatchBusinessTypeEnums.Diligence_ID];
  }

  processJobMessage(message: BatchJobMessagePO): Promise<BatchRunDDResultPO> {
    return this.batchRunDiligence(message, false);
  }

  async onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    await Bluebird.delay(3000);
    await this.snapshotHelperService.refreshBatchSnapshotRelation(batchEntity.batchId, batchEntity.orgId);
    await this.diligenceAnalyzeHelper.refreshBatchAnalyze(batchEntity.orgId, batchEntity.batchId, -1);
    return Promise.resolve(undefined);
  }

  async shouldBatchFinished(batchEntity: BatchEntity): Promise<boolean> {
    //先用默认办法判断batch 的job是否都已经完成
    let batchFinished = await super.shouldBatchFinished(batchEntity);
    if (batchFinished) {
      //检查batch 对应的 snapshot的完成情况，如果snapshot 都完成了，则说明全量风险排查巡检已经完成(分析结果需要基于快照，所以需要等待快照完成)
      const qb = this.searchNoSnapshotDiligence(batchEntity.orgId, batchEntity.batchId);
      //找到 快照状态是 0 的diligence
      const c = await qb.getCount();
      if (c > 0) {
        batchFinished = false;
        this.logger.info(`shouldBatchFinished batchId: ${batchEntity.batchId} not finished, snapshot not finished, still have ${c} diligence left`);

        //
        const reachedLimited = Date.now() - batchEntity.createDate.getTime() > BatchConstants.Threshold.Batch * 0.6;
        //如果最后一次更新已经超过30分钟，则认为快照消息没有正常处理，重新发送生成快照消息
        if (reachedLimited) {
          const pageSize = 100;
          let pageNum = 1;
          do {
            const items = await qb
              .skip((pageNum - 1) * pageSize)
              .take(pageSize)
              .getMany();
            if (items.length === 0) {
              break;
            }
            await Bluebird.map(
              items,
              async (diligenceHistory: DiligenceHistoryEntity) => {
                const snapshotMsg: SnapshotDimensionMessagePO = {
                  orgId: diligenceHistory.orgId,
                  operation: OperationEnums.Create,
                  riskData: diligenceHistory.details,
                  companyId: diligenceHistory.companyId,
                  companyName: diligenceHistory.name,
                  snapshotId: diligenceHistory.snapshotId,
                  diligenceId: diligenceHistory.id,
                  diligenceAt: diligenceHistory.createDate,
                  batchIds: diligenceHistory.batchEntities?.map((t) => t.batchId),
                };
                return this.snapshotHelperService.createSnapshot(snapshotMsg);
              },
              { concurrency: 10 },
            );

            pageNum++;
          } while (true);
        }
      }
      //如果快照都完成了，则认为全量风险排查巡检已经完成，等待5秒钟是为了确保快照数据已经写入es
      await Bluebird.delay(3000);
      batchFinished = true;
    }
    return batchFinished;
  }

  private searchNoSnapshotDiligence(orgId: number, batchId: number): SelectQueryBuilder<DiligenceHistoryEntity> {
    const qb = this.diligenceRepo
      .createQueryBuilder('diligence')
      .where('diligence.orgId = :orgId', { orgId })
      .leftJoin('diligence.batchEntities', 'batchEntities')
      .andWhere('batchEntities.batchId = :batchId ', { batchId });
    qb.andWhere('(diligence.snapshot_details is null or diligence.snapshot_details->>"$.status" = 0)');
    return qb;
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
