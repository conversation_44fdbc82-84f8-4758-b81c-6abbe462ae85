import { BatchDiligenceProcessor } from './batch.diligence.processor';
import { Injectable } from '@nestjs/common';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { BatchRunDDResultPO } from '../../../../../libs/model/batch/po/BatchRunDDResultPO';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DiligenceSnapshotEsService } from '../../../../diligence/snapshot/diligence.snapshot.es.service';

/**
 * 批量排查-全量风险排查巡检
 * 这个有两个版本，
 * 风险巡检 对应普通用户
 * 分析看板 对应蔡司
 */
@Injectable()
export class DiligenceCustomerAnalyzeProcessor extends BatchDiligenceProcessor {
  protected readonly logger = QccLogger.getLogger(DiligenceCustomerAnalyzeProcessor.name);

  constructor(protected readonly diligenceSnapshotEsService: DiligenceSnapshotEsService) {
    super(diligenceSnapshotEsService);
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Diligence_Customer_Analyze];
  }

  processJobMessage(message: BatchJobMessagePO): Promise<BatchRunDDResultPO> {
    // TODO:  batchRunDiligence 的第二个参数是为了过滤第三方中不支持排查的海外企业等， 后续可以简化
    return this.batchRunDiligence(message, true);
  }

  async onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    const { batchId, orgId } = batchEntity;
    await super.onBatchSuccess(batchEntity);
    // 发送消息到分析队列
    // const sendResult = await this.diligenceAnalyzeQueue.sendMessage({  orgId });
    const sendResult = await this.diligenceAnalyzeHelper.refreshBatchAnalyze(orgId, batchId);
    this.logger.info(`onBatchFinished diligenceAnalyzeQueue sendMessage result: ${sendResult}`);
  }

  async onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }
}
