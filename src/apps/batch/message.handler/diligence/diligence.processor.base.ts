import { BatchMessageProcessorAbstract } from '../batch.message.processor.abstract';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchDiligenceEntity } from '../../../../libs/entities/BatchDiligenceEntity';
import { Repository } from 'typeorm';

export abstract class DiligenceProcessorBase extends BatchMessageProcessorAbstract {
  @InjectRepository(BatchDiligenceEntity)
  protected readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>;
}
