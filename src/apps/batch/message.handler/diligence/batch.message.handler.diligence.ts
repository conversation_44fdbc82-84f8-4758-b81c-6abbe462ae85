/**
 * @file diligence相关任务消息处理
 */
import { Injectable } from '@nestjs/common';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchConstants } from '../../common/batch.constants';
import { BatchMessageHandlerAbstract } from '../batch.message.handler.abstract';
import { BatchDiligenceProcessor } from './processors/batch.diligence.processor';
import { DiligenceCustomerAnalyzeProcessor } from './processors/diligence.customer.analyze.processor';
import { DiligenceProcessorBase } from './diligence.processor.base';
import { QueueService } from '../../../../libs/config/queue.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';

@Injectable()
export class BatchMessageHandlerDiligence extends BatchMessageHandlerAbstract {
  public batchDiligenceQueue: RabbitMQ;
  protected readonly logger: Logger = QccLogger.getLogger(BatchMessageHandlerDiligence.name);

  constructor(
    private readonly batchDiligenceProcessor: BatchDiligenceProcessor,
    private readonly diligenceCustomerAnalyzeProcessor: DiligenceCustomerAnalyzeProcessor,
    protected readonly queueService: QueueService,
    protected readonly redisService: RedisService,
  ) {
    super(queueService, redisService);
    Array.prototype.push.apply(this.allProcessor, [this.batchDiligenceProcessor, this.diligenceCustomerAnalyzeProcessor]);
    // this.batchJobQueue = this.queueService.batchJobQueue;
    // this.batchJobQueue.consume(this.handleJobMessage.bind(this), BatchConstants.Consumer.Others).catch((err) => this.logger.error(err));
    // this.batchJobQueue.on('failed', this.handleBatchJobQueueError.bind(this));

    this.batchDiligenceQueue = this.queueService.batchDiligenceQueue;
    this.batchDiligenceQueue.consume(this.handleJobMessage.bind(this), BatchConstants.Consumer.Diligence).catch((err) => this.logger.error(err));
    this.batchDiligenceQueue.on('failed', this.onPulsarQueueError.bind(this));
  }

  // async onBatchDiligenceQueueError(messageData: BatchExportMessagePO, error) {
  //   const { batchId, targetId, jobId } = messageData;
  //   this.logger.error(`onBatchDiligenceQueueError(): batchId=${batchId} ,targetId=${targetId},jobId=${jobId} ,error=${error.message}`);
  //   this.logger.error(error);
  //   captureException(new PulsarError(`onBatchDiligenceQueueError() error:${error?.message}`, error), {
  //     extra: {
  //       messageData,
  //     },
  //   });
  // }

  getTargetQueue(businessType: BatchBusinessTypeEnums): RabbitMQ {
    return this.batchDiligenceQueue;
  }

  getProperlyProcessor(businessType: BatchBusinessTypeEnums): DiligenceProcessorBase {
    return super.getProperlyProcessor(businessType) as DiligenceProcessorBase;
  }
}
