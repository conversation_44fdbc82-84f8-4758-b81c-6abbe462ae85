import { BatchMessageHandlerAbstract } from '../batch.message.handler.abstract';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { QueueService } from '../../../../libs/config/queue.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { BatchConstants } from '../../common/batch.constants';
import { Injectable } from '@nestjs/common';
import { PotentialDiligenceProcessor } from './processor/potential.diligence.processor';

@Injectable()
export class BatchMessageHandlerPotential extends BatchMessageHandlerAbstract {
  public batchPotentialQueue: RabbitMQ;
  protected readonly logger: Logger = QccLogger.getLogger(BatchMessageHandlerPotential.name);

  constructor(
    private readonly potentialDiligenceProcessor: PotentialDiligenceProcessor,
    protected readonly queueService: QueueService,
    protected readonly redisService: RedisService,
  ) {
    super(queueService, redisService);
    Array.prototype.push.apply(this.allProcessor, [this.potentialDiligenceProcessor]);

    this.batchPotentialQueue = this.queueService.batchPotentialQueue;
    this.batchPotentialQueue.consume(this.handleJobMessage.bind(this), BatchConstants.Consumer.Diligence).catch((err) => this.logger.error(err));
    this.batchPotentialQueue.on('failed', this.onPulsarQueueError.bind(this));
  }

  getTargetQueue(businessType?: BatchBusinessTypeEnums): RabbitMQ {
    return this.batchPotentialQueue;
  }
}
