import { Test, TestingModule } from '@nestjs/testing';
import { PotentialDiligenceProcessor } from './potential.diligence.processor';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { AppTestModule } from 'apps/app/app.test.module';
import { BatchModule } from 'apps/batch/batch.module';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';

jest.setTimeout(300000);
describe.skip('PotentialDiligenceProcessor', () => {
  let processor: PotentialDiligenceProcessor;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();

    processor = module.get<PotentialDiligenceProcessor>(PotentialDiligenceProcessor);
  });

  describe('processJobMessage', () => {
    it('应该成功处理潜在利益排查任务并保存结果', async () => {
      // Arrange
      const mockMessage: BatchJobMessagePO = {
        batchId: 56279,
        jobId: 299478,
        orgId: 208,
        operatorId: 5171,
        items: [
          {
            companyId: '84c17a005a759a5e0d875c1ebb6c9846',
            companyName: '乐视网信息技术（北京）股份有限公司',
            recordParams: {},
            recordHashkey: '0703dc71-9550-46ea-839f-811b2f9fff5d',
          },
          {
            companyId: 'f625a5b661058ba5082ca508f99ffe1b',
            companyName: '企查查科技股份有限公司',
            recordParams: {},
            recordHashkey: 'd3614b00-d96d-40c7-9600-90adb4be80a0',
          },
        ],
        startDate: 1234567890,
        batchType: 1,
        businessType: BatchBusinessTypeEnums.Potential_Batch_Data,
      };

      // Act
      await processor.processJobMessage(mockMessage);
    });
  });
});
