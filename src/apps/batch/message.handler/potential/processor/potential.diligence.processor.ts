import { Injectable } from '@nestjs/common';
import { BatchMessageProcessorAbstract } from '../../batch.message.processor.abstract';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../../libs/entities/BatchEntity';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { BatchBaseHelper } from '../../../service/helper/batch.base.helper';
import { UserService } from '../../../../user/user.service';
import { BatchDiligenceRequestItemPO, ParsedRecordBase } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import * as Bluebird from 'bluebird';
import { PotentialFacadeService } from '../../../../potential/potential.facade.service';
import { BatchPotentialDiligenceEntity } from '../../../../../libs/entities/BatchPotentialDiligenceEntity';
import { BatchResultPO } from '../../../model/BatchResultPO';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BatchPotentialDiligenceDetailEntity } from '../../../../../libs/entities/BatchPotentialDiligenceDetailEntity';

/**
 * 潜在利益排查任务处理类
 */
@Injectable()
export class PotentialDiligenceProcessor extends BatchMessageProcessorAbstract {
  constructor(
    private readonly batchHelperService: BatchBaseHelper,
    private readonly userService: UserService,
    private readonly potentialFacadeService: PotentialFacadeService,
    @InjectRepository(BatchPotentialDiligenceEntity) protected readonly batchPotentialRepo: Repository<BatchPotentialDiligenceEntity>,
    @InjectRepository(BatchPotentialDiligenceDetailEntity) protected readonly batchPotentialDetailRepo: Repository<BatchPotentialDiligenceDetailEntity>,
  ) {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Potential_Batch_Data, BatchBusinessTypeEnums.Potential_Batch_Excel, BatchBusinessTypeEnums.Potentail_Batch_Customer];
  }

  onBatchError(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  onBatchSuccess(batchEntity: BatchEntity): Promise<any> {
    return Promise.resolve(undefined);
  }

  onJobError(jobIds: number[], batchId: number, businessType: BatchBusinessTypeEnums): Promise<any> {
    return Promise.resolve(undefined);
  }

  async processJobMessage(message: BatchJobMessagePO) {
    const { batchId, items, jobId, orgId, operatorId } = message;
    const data: ParsedRecordBase[] = items;
    this.logger.info(`processJobMessage batchRunPotentialDiligence: batchId=${batchId},jobId=${jobId}`);
    await Bluebird.map(data, async (item: BatchDiligenceRequestItemPO) => {
      const params = {
        companyName: item.companyName,
        companyId: item.companyId,
      };
      const user = await this.userService.getRoverUser(operatorId, orgId);
      const potentialDiligence = await this.potentialFacadeService.scanRisk(user, params, batchId);
      const diligenceId = potentialDiligence.id;
      const groupResults = potentialDiligence.details['groupResults'];
      // 组级别结果写入数据库
      const batchPotentialDetailEntities = [];
      const entity = Object.assign(new BatchPotentialDiligenceDetailEntity(), {
        batchId,
        diligenceId,
        groupKey: 'StaffWorkingOutsideForeignInvestment',
        groupHits: groupResults?.['StaffWorkingOutsideForeignInvestment']?.length || 0,
      });
      const entity2 = Object.assign(new BatchPotentialDiligenceDetailEntity(), {
        batchId,
        diligenceId,
        groupKey: 'SuspectedInterestConflict',
        groupHits: groupResults?.['SuspectedInterestConflict']?.length || 0,
      });
      batchPotentialDetailEntities.push(entity, entity2);
      await this.batchPotentialDetailRepo.save(batchPotentialDetailEntities);
      // batch 与排查结果关联
      const batchPotentialEntity = Object.assign(new BatchPotentialDiligenceEntity(), {
        jobId,
        batchId,
        diligenceId: potentialDiligence.id,
      });
      await this.batchPotentialRepo.save(batchPotentialEntity);
      const batchResult = Object.assign(new BatchResultPO(), {
        jobId,
        batchId,
        resultInfo: { ...params, diligenceId },
        resultHashkey: message?.items?.find((t) => t.companyId === params.companyId)?.recordHashkey,
        resultType: BatchJobResultTypeEnums.SUCCEED_PAID,
      });
      await this.batchHelperService.handleJobResult([batchResult]);
    });
  }
}
