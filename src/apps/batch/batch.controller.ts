import { Body, Controller, Get, Param, Post, Query, Request, UseGuards } from '@nestjs/common';
import { ApiCookieAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { BatchService } from './service/batch.service';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { SearchBatchRequest } from 'libs/model/batch/request/SearchBatchRequest';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { RoverSessionGuard } from 'libs/guards/RoverSession.guard';
import { RoverUser } from 'libs/model/common';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { BatchResultExportService } from './service/batch.result.export.service';
import { RoverRolesGuard } from 'libs/guards/rover.roles.guard';
import { SecurityService } from '../../libs/config/security.service';
import { BatchRetryRequest, RetryTenderDiligenceRequest } from '../../libs/model/batch/request/BatchRetryRequest';
import { BatchCreatorHelperBase } from './service/helper/batch.creator.helper.base';
import { BatchMessageService } from './message.handler/batch.message.service';
import { DiligenceTenderHistoryEntity } from '../../libs/entities/DiligenceTenderHistoryEntity';

@Controller('batch')
@ApiTags('批量操作处理')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class BatchController {
  private readonly logger: Logger = QccLogger.getLogger(BatchController.name);

  constructor(
    private readonly batchService: BatchService,
    private readonly resultExportService: BatchResultExportService,
    private readonly securityService: SecurityService,
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
    private readonly batchMessageService: BatchMessageService,
  ) {}

  @Post('retry/specific_diligence')
  @ApiOperation({ summary: '特定利益排查任务重试' })
  @ApiOkResponse({ type: DiligenceTenderHistoryEntity })
  async retrySpecificDiligence(@Request() req, @Body() postBody: RetryTenderDiligenceRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchMessageService.specificDiligenceRetry(currentUser, postBody.diligenceId);
  }

  @Post('search')
  @ApiOperation({ summary: '查询批量任务列表' })
  @ApiOkResponse({ type: BatchEntity })
  async searchBatch(@Request() req, @Body() postBody: SearchBatchRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.searchBatch(currentUser, postBody);
  }

  @Get('detail')
  @ApiOperation({ summary: '获取批量任务详情' })
  async getBatchById(@Request() req, @Query('id') id: number) {
    const currentUser: RoverUser = req.user;
    return this.batchService.getBatchEntity(currentUser, id);
  }

  @Post('retry')
  @ApiOperation({ summary: '批量任务重试' })
  async retryBatchById(@Request() req, @Body() postBody: BatchRetryRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchMessageService.retryBatch(postBody.batchId, currentUser);
  }

  @Post('discontinue')
  @ApiOperation({ summary: '批量任务中止' })
  async discontinueBatchById(@Request() req, @Body() postBody: BatchRetryRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchMessageService.discontinue(postBody.batchId, currentUser);
  }

  @Post('retry/tender_diligence')
  @ApiOperation({ summary: '招标排查任务重试' })
  @ApiOkResponse({ type: DiligenceTenderHistoryEntity })
  async retryTenderDiligence(@Request() req, @Body() postBody: RetryTenderDiligenceRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchMessageService.tenderDiligenceRetry(currentUser, postBody.diligenceId);
  }

  @Post('user')
  @ApiOperation({ summary: '批量任务-人员列表' })
  @ApiOkResponse({ type: BatchEntity })
  async getUsers(@Request() req, @Body() postBody: SearchBatchRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.searchUser(currentUser, postBody);
  }

  @Post('customer/diligence_analyze/check')
  @ApiOperation({ summary: '对全部的客商列表进行批量排查并进行 全量风险排查巡检，统计额度使用' })
  @ApiTags('准入调查/全量风险排查巡检额度统计')
  async checkCustomerDiligenceAnalyzeJob(@Request() req) {
    return this.batchCreatorHelperService.checkDiligenceBundleStock(req.user, { succeedItems: [] }, BatchBusinessTypeEnums.Diligence_Customer_Analyze);
  }

  @Post('customer/diligence_analyze')
  @ApiOperation({ summary: '对全部的客商列表进行批量排查并进行 全量风险排查巡检' })
  @ApiTags('准入调查/全量风险排查巡检')
  async createCustomerDiligenceAnalyzeJob(@Request() req) {
    return this.batchService.createBatchDiligenceTask(req.user, [], BatchBusinessTypeEnums.Diligence_Customer_Analyze);
  }

  /**  以下接口为为测试接口可删除        */

  // @Post('update2')
  // @ApiOperation({ summary: '更新company', deprecated: true })
  // async updateBackground2(@Request() req, @Param('orgId') orgId: number) {
  //   const currentUser: RoverUser = req.user;
  //   return this.batchUpdateDbService.updateBackground2();
  // }

  // @Post('updateBackground/:orgId')
  // @ApiOperation({ summary: '后台更新DB，适应新增字段', deprecated: true })
  // async updateBackground(@Request() req, @Param('orgId') orgId: number) {
  //   const currentUser: RoverUser = req.user;
  //   return this.batchUpdateDbService.updateBackground(currentUser, orgId);
  // }

  @Post('generateResultFile/:batchId/:businessType')
  @ApiOperation({ summary: '获取导出数据并生成excel', deprecated: true })
  async generateResultFile(@Request() req, @Param('batchId') batchId: number, @Param('businessType') businessType: number) {
    await this.securityService.allowWithinOrg(BatchEntity, req.user, [batchId], 'batchId');
    return this.resultExportService.generateResultFile(batchId, businessType);
  }
}
