import { BadRequestException, Body, Controller, Param, ParseIntPipe, Post, Query, Request, UseGuards } from '@nestjs/common';
import { ApiCookieAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { BatchService } from './service/batch.service';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { RoverSessionGuard } from 'libs/guards/RoverSession.guard';
import { DiligenceHistoryRequest } from 'libs/model/diligence/pojo/history/DiligenceHistoryRequest';
import { SearchPersonModel } from 'libs/model/person/SearchPersonModel';
import { SearchInnerBlacklistModel } from 'libs/model/blacklist/SearchInnerBlacklistModel';
import { RoverUser } from 'libs/model/common';
import { SearchBatchResultRequest } from '../../libs/model/batch/request/SearchBatchResultRequest';
import { SearchCustomerModel } from '../../libs/model/customer/SearchCustomerModel';
import { RoverRolesGuard } from 'libs/guards/rover.roles.guard';
import { SearchTenderAlertProjectRequest } from '../../libs/model/tenderAlert/SearchTenderAlertProjectRequest';
import { RiskDynamicsSearchRequest } from '../../libs/model/monitor/request/RiskDynamicsSearchRequest';
import { SearchCustomerNewsRequest } from '../../libs/model/customer/SearchCustomerNewsRequest';
import { DiligenceBatchExportRequest } from '../../libs/model/monitor/request/DiligenceBatchExportRequest';
import { SearchCustomerDiligenceRequest } from '../../libs/model/customer/analyze/SearchCustomerDiligenceRequest';
import { ExportBatchReportRequest, ExportConditionRequest, ExportPDFDetailRequest } from './message.handler/export/model/ExportRecordPO';
import { SearchDiligenceBiddingRequest } from '../../libs/model/bidding/SearchDiligenceBiddingRequest';
import { SearchBundleConsumeDetailRequest } from '../../libs/model/batch/request/SearchBundleConsumeDetailRequest';
import { BundleConsumeDetailExportEnum } from '../../libs/enums/batch/BundleConsumeDetailExportEnum';

@Controller('batch/export')
@ApiTags('批量导出处理')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class BatchExportController {
  constructor(private readonly batchService: BatchService) {}

  @Post('diligence_batch_detail')
  @ApiOperation({ summary: '导出批量任务详情' })
  async getDetailExport(@Request() req, @Body() data: SearchBatchResultRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Diligence_Batch_Detail, data);
  }

  @Post('dimension_detail')
  @ApiOperation({ summary: '导出批量任务维度详情' })
  async getDimensionDetailExport(@Request() req, @Body() data: SearchBatchResultRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Dimension_Detail_Export, data);
  }

  @Post('analyze_record')
  @ApiOperation({ summary: '导出风险巡检结果' })
  async getAnalyzeRecordExport(@Request() req, @Body() data: SearchCustomerDiligenceRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Analyze_Record_Export, data);
  }

  @Post('analyze_dimension_detail')
  @ApiOperation({ summary: '导出巡检批量任务维度详情' })
  async getAnalyzeDimensionDetailExport(@Request() req, @Body() data: SearchBatchResultRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Analyze_Dimension_Detail, data);
  }

  @Post('diligence_record')
  @ApiOperation({ summary: '排查记录导出' })
  async getDiligenceExport(@Request() req, @Body() data: DiligenceHistoryRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Diligence_Record, data);
  }

  @Post('person')
  @ApiOperation({ summary: '人员导出' })
  async getPersonExport(@Request() req, @Body() data: SearchPersonModel) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Person_Export, data);
  }

  @Post('inner_blacklist')
  @ApiOperation({ summary: '内部黑名单导出' })
  async getInnerBlackListExport(@Request() req, @Body() data: SearchInnerBlacklistModel) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.InnerBlacklist_Export, data);
  }

  @Post('customer')
  @ApiOperation({ summary: '第三方列表导出' })
  async getCustomerExport(@Request() req, @Body() data: SearchCustomerModel) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Customer_Export, data);
  }

  @Post('tender')
  @ApiOperation({ summary: '投标预警标讯导出' })
  async getTenderExport(@Request() req, @Body() data: SearchTenderAlertProjectRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Export, data);
  }

  @Post('/diligence_pdf/:diligenceId')
  @ApiOperation({ description: '排查报告导出pdf任务' })
  async diligenceExport(@Param('diligenceId', ParseIntPipe) diligenceId: number, @Request() req) {
    const currentUser: RoverUser = req.user;
    const condition: ExportConditionRequest = { diligenceId };
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Diligence_Report_Export, condition);
  }

  @Post('/batch_diligence_pdfs')
  @ApiOperation({ description: '排查报告批量导出pdf任务,并生成zip压缩包' })
  async diligenceBatchExport(@Body() data: DiligenceBatchExportRequest, @Request() req) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(
      currentUser,
      BatchBusinessTypeEnums.Diligence_Report_Batch_Export,
      Object.assign(new ExportBatchReportRequest(), data),
    );
  }

  @Post('/tender_detail_pdf')
  @ApiOperation({ description: '导出标讯项目详情pdf' })
  async tenderDetailExport(@Query('id') id: string, @Request() req) {
    const currentUser: RoverUser = req.user;
    const condition: ExportPDFDetailRequest = { id };
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Detail_Export, condition);
  }

  @Post('risk')
  @ApiOperation({ summary: '风险动态列表导出' })
  async getRiskExport(@Request() req, @Body() data: RiskDynamicsSearchRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Risk_Export, data);
  }

  @Post('sentiment')
  @ApiOperation({ summary: '舆情动态列表导出' })
  async getSentimentExport(@Request() req, @Body() data: SearchCustomerNewsRequest) {
    const currentUser: RoverUser = req.user;
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Sentiment_Export, data);
  }

  @Post('/tender_report_pdf')
  @ApiOperation({ description: '导出招标排查PDF' })
  async tenderExport(@Query('id') id: number, @Request() req) {
    const currentUser: RoverUser = req.user;
    const condition: ExportConditionRequest = { diligenceId: id };
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Report_Export, condition);
  }

  @Post('/tender_diligence_record')
  @ApiOperation({ description: '导出批量批量招标排查统计以及列表' })
  async batchTenderExportDiligenceRecord(@Request() req, @Body() data: ExportBatchReportRequest) {
    const currentUser: RoverUser = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Diligence_Record_Export, data);
  }

  @Post('/tender_dimension_detail')
  @ApiOperation({ description: '导出批量招标排查维度详情' })
  async tenderExportDimensionDetail(@Request() req, @Body() data: SearchDiligenceBiddingRequest) {
    const currentUser: RoverUser = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Dimension_Detail_Export, data);
  }

  @Post('/tender_all_dimension_detail')
  @ApiOperation({ description: '导出批量招标排查维度详情' })
  async tenderExportAllDimensionDetail(@Request() req, @Body() data: SearchDiligenceBiddingRequest) {
    const currentUser: RoverUser = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export, data);
  }

  @Post('/tender_diligence_history')
  @ApiOperation({ description: '导出招标排查历史记录列表' })
  async tenderExportDiligenceHistory(@Request() req, @Body() data: SearchDiligenceBiddingRequest) {
    const currentUser: RoverUser = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Tender_Diligence_History_Export, data);
  }

  @Post('/specific_record_list')
  @ApiOperation({ description: '导出特定利益关系排查历史记录列表' })
  async exportSpecificRecordList(@Request() req, @Body() data: SearchDiligenceBiddingRequest) {
    const currentUser: RoverUser = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Specific_Record_List_Export, data);
  }

  @Post('/specific_report_pdf')
  @ApiOperation({ description: '导出特定利益关系排查历史记录列表' })
  async exportSpecificPdf(@Query('id') id: number, @Request() req) {
    const currentUser: RoverUser = req.user;
    const condition: ExportConditionRequest = { diligenceId: id };
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Specific_Report_Export, condition);
  }

  @Post('/specific_batch_record')
  @ApiOperation({ description: '导出批量特定利益关系排查统计以及列表' })
  async batchSpecificExportDiligenceRecord(@Request() req, @Body() data: ExportBatchReportRequest) {
    const currentUser: RoverUser = req.user;
    Object.assign(data, { orgId: currentUser.currentOrg });
    return this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Specific_Batch_Export, data);
  }

  @Post('/bundle_consume_detail')
  @ApiOperation({ summary: '套餐权益消费明细导出' })
  async exportBundleDiligenceConsumeDetail(@Request() req, @Body() data: SearchBundleConsumeDetailRequest) {
    const currentUser: RoverUser = req.user;
    const type = Number(data.type);

    const createExportBatch = (businessType: BatchBusinessTypeEnums) => {
      return this.batchService.createExportBatchEntity(currentUser, businessType, data);
    };

    switch (type) {
      case BundleConsumeDetailExportEnum.Diligence:
        return createExportBatch(BatchBusinessTypeEnums.Bundle_Diligence_Consume_detail_Export);
      case BundleConsumeDetailExportEnum.Analyze_Record:
        return createExportBatch(BatchBusinessTypeEnums.Bundle_Analyze_Record_Consume_detail_Export);
      case BundleConsumeDetailExportEnum.Bidding:
        return createExportBatch(BatchBusinessTypeEnums.Bundle_Bidding_Consume_detail_Export);
      case BundleConsumeDetailExportEnum.Special:
        return createExportBatch(BatchBusinessTypeEnums.Bundle_Special_Consume_detail_Export);
      default:
        throw new BadRequestException('无效的导出类型!');
    }
  }
}
