import { Body, Controller, Post, Query, Request, UploadedFile, UseFilters, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiCookieAuth, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { BatchService } from './service/batch.service';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { FileInterceptor } from '@nestjs/platform-express';
import { defaultFileUploadOptions } from 'libs/common/file/config';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { FileSizeLimitExceptionFilter } from 'libs/exceptions/FileSizeLimitExceptionFilter';
import { SearchBiddingBatchResultRequest } from '../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { RoverSessionGuard } from '../../libs/guards/RoverSession.guard';
import { RoverRolesGuard } from '../../libs/guards/rover.roles.guard';

@Controller('batch/import/bidding')
@ApiTags('批量招标排查')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class BatchBiddingController {
  private readonly logger: Logger = QccLogger.getLogger(BatchBiddingController.name);
  constructor(private readonly batchService: BatchService) {}

  @Post('excel')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '上传excel文件并创建 批量招标排查任务' })
  async createBatchBiddingJob(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    return this.batchService.createBatchByFile(req.user, file.path, fileName || file.originalname, BatchBusinessTypeEnums.Bidding_Diligence_File);
  }

  @Post('diligence_check')
  @UseFilters(FileSizeLimitExceptionFilter)
  @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  @ApiOperation({ summary: '批量招标排查任务的额度检查' })
  async checkBatchBiddingJob(@UploadedFile() file, @Request() req) {
    return this.batchService.checkBatchBiddingDiligenceLimint(req.user, file.path);
  }

  @Post('statistics')
  @ApiOperation({ summary: '查询批量招标排查统计信息' })
  async searchBiddingStatistics(@Request() req, @Body() data: SearchBiddingBatchResultRequest) {
    return this.batchService.getBiddingStatistics(req.user, data);
  }

  @Post('detail')
  @ApiOperation({ summary: '查询批量招标排查详情' })
  async searchBiddingDetail(@Request() req, @Body() data: SearchBiddingBatchResultRequest) {
    return this.batchService.searchBiddingDetail(req.user, data);
  }
}
