import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../../app/app.test.module';
import { EntityManager, getRepository, In, Repository } from 'typeorm';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { BatchService } from '../service/batch.service';
import { BatchJobEntity } from '../../../libs/entities/BatchJobEntity';
import { BatchResultEntity } from '../../../libs/entities/BatchResultEntity';
import { Batch<PERSON>aseHelper } from '../service/helper/batch.base.helper';
import { BatchMessageService } from '../message.handler/batch.message.service';
import { BatchMessageHandlerDiligence } from '../message.handler/diligence/batch.message.handler.diligence';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { BatchMatchCompanyItemEntity } from '../../../libs/entities/BatchMatchCompanyItemEntity';
import * as path from 'path';
import * as fs from 'fs';
import { BatchStatusEnums } from '../../../libs/enums/batch/BatchStatusEnums';
import { EvaluationService } from '../../diligence/evaluation/evaluation.service';
import { BatchModule } from '../batch.module';
import { UserService } from '../../user/user.service';
import { BatchJobResultTypeEnums } from '../../../libs/enums/batch/BatchJobResultTypeEnums';
import { RoverBundleCounterType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { BatchTestUtils, sleep } from '../../test_utils_module/batch.test.utils';

// 生成测试用户ID
const [testOrgId, testUserId] = generateUniqueTestIds('batch.diligence.integration.2.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

// 设置较长的超时时间，因为批量任务可能需要较长时间
jest.setTimeout(300 * 1000);

/**
 * 批量排查集成测试
 * 测试从Excel模板导入到完成整个流程
 */
describe('集成测试 - 批量排查job中部分item失败, 重试后全部成功', () => {
  let module: TestingModule;
  let batchService: BatchService;
  let batchMessageService: BatchMessageService;
  let batchMessageHandlerDiligence: BatchMessageHandlerDiligence;
  let batchBaseHelper: BatchBaseHelper;
  let evaluationService: EvaluationService;
  let userService: UserService;
  let entityManager: EntityManager;
  let bundleService: RoverBundleService;
  // 数据库仓库
  let batchRepo: Repository<BatchEntity>;
  let batchJobRepo: Repository<BatchJobEntity>;
  let batchResultRepo: Repository<BatchResultEntity>;
  let batchMatchCompanyItemRepo: Repository<BatchMatchCompanyItemEntity>;

  // 测试数据
  let templateFilePath: string;
  let batchId: number;

  beforeAll(async () => {
    // 创建测试模块
    module = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();

    // 获取服务和仓库
    batchService = module.get<BatchService>(BatchService);
    batchMessageService = module.get<BatchMessageService>(BatchMessageService);
    batchMessageHandlerDiligence = module.get<BatchMessageHandlerDiligence>(BatchMessageHandlerDiligence);
    batchBaseHelper = module.get<BatchBaseHelper>(BatchBaseHelper);
    evaluationService = module.get<EvaluationService>(EvaluationService);
    userService = module.get<UserService>(UserService);
    entityManager = module.get<EntityManager>(EntityManager);
    bundleService = module.get(RoverBundleService);

    batchRepo = getRepository(BatchEntity);
    batchJobRepo = getRepository(BatchJobEntity);
    batchResultRepo = getRepository(BatchResultEntity);
    batchMatchCompanyItemRepo = getRepository(BatchMatchCompanyItemEntity);

    // 设置模板文件路径
    templateFilePath = path.join(__dirname, './批量排查模版.xlsx');

    // 确保测试文件存在
    if (!fs.existsSync(templateFilePath)) {
      throw new Error(`测试文件不存在: ${templateFilePath}`);
    }
  });

  beforeEach(async () => {
    // 清理之前的测试数据
    await BatchTestUtils.clearTestDiligenceData(entityManager, testOrgId);
  });

  afterAll(async () => {
    // 清理所有测试数据
    await BatchTestUtils.clearTestDiligenceData(entityManager, testOrgId);
    await batchRepo.manager.connection.close();
  });

  /**
   * 创建基础的消息队列和服务模拟
   */
  function setupBasicMocks() {
    const diligenceQueueSpy = jest.spyOn(batchMessageHandlerDiligence.batchDiligenceQueue, 'sendMessageV2').mockImplementation(async (msg) => {
      await sleep(2);
      batchMessageHandlerDiligence.handleJobMessage(msg);
    });

    const jobMonitorQueueSpy = jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation(async (msg) => {
      await sleep(0.2);
      batchMessageService.processJobMonitorMessage(msg);
    });

    const userServiceSpy = jest.spyOn(userService, 'getRoverUser').mockResolvedValue(testUser);

    return { diligenceQueueSpy, jobMonitorQueueSpy, userServiceSpy };
  }

  it('job中部分item失败, 重试后全部成功', async () => {
    const { diligenceQueueSpy, jobMonitorQueueSpy, userServiceSpy } = setupBasicMocks();

    const bundleCounter = await bundleService.getBundleCounter(testUser, RoverBundleCounterType.DiligenceCompanyQuantity);
    const originalStock = await bundleCounter.getStock();

    let callCount = 0;
    let diligenceId = 20000;
    let succeedCount = 0;
    const errorCount = 10;

    const getRiskListForBatchJobSpy1 = jest
      .spyOn(evaluationService as any, 'getRiskListForBatchJob')
      .mockImplementation(async (user, params: { companyId: string; companyName: string }) => {
        callCount++;

        // 让偶数次调用失败
        if (callCount <= errorCount) {
          throw new Error('模拟排查失败');
        }

        // 奇数次调用成功
        succeedCount++;
        diligenceId++;
        return {
          id: diligenceId,
          companyId: params.companyId,
          name: params.companyName || `测试公司${params.companyId}`,
          snapshotId: `snapshot_${diligenceId}`,
          details: { hits: [], score: 85 },
          createDate: new Date(),
          paid: true,
          operatorId: testUserId,
          orgId: testOrgId,
        };
      });

    try {
      // 1. 导入Excel模板并匹配公司
      const matchResult = await batchService.matchBatchDiligenceCompany(testUser, templateFilePath, '批量排查模版.xlsx');
      expect(matchResult).toEqual(expect.objectContaining({ id: expect.any(Number) }));

      const matchItems = await batchMatchCompanyItemRepo.find({ where: { batchId: matchResult['id'] } });

      // 2. 使用匹配结果创建批量排查任务
      const batchEntity = await batchService.executeBatchDiligenceCompany(testUser, matchResult['id']);

      expect(batchEntity).toBeDefined();
      batchId = batchEntity.batchId;

      // 4. 等待批量任务完成并验证状态
      const currentBatch = await BatchTestUtils.waitForBatchCompletion(batchRepo.manager, batchId);
      expect(currentBatch.status).toBe(BatchStatusEnums.Done);

      // 5. 验证作业状态
      const jobs = await batchJobRepo.find({ where: { batchId } });
      expect(jobs.map((job) => job.jobInfo.itemSize).reduce((a, b) => a + b, 0)).toBe(matchItems.length);
      expect(jobs.every((job) => job.status === BatchStatusEnums.Done)).toBe(true);

      // 6. 验证排查结果
      const resultSuccessCount = await batchResultRepo.count({
        where: {
          batchId,
          resultType: In([
            BatchJobResultTypeEnums.SUCCEED_DUPLICATED,
            BatchJobResultTypeEnums.SUCCEED_PAID,
            BatchJobResultTypeEnums.SUCCEED_UNPAID,
            BatchJobResultTypeEnums.SUCCEED_IGNORE,
            BatchJobResultTypeEnums.SUCCEED_UPDATE_IGNORE,
          ]),
        },
      });
      expect(resultSuccessCount).toBe(succeedCount);

      const failedResults = await batchResultRepo.find({
        where: {
          batchId,
          resultType: In([BatchJobResultTypeEnums.FAILED_CODE, BatchJobResultTypeEnums.FAILED_VERIFICATION, BatchJobResultTypeEnums.FAILED_BUNDLE_LIMITED]),
        },
        select: ['jobId', 'resultInfo', 'resultId'],
      });

      expect(failedResults.length).toBe(errorCount);
      expect(resultSuccessCount + failedResults.length).toBe(matchItems.length);

      // 7. 验证尽调历史记录, 尽调方法被spy 无尽调历史记录，直接验证getRiskListForBatchJob 被调用次数
      expect(getRiskListForBatchJobSpy1).toHaveBeenCalledTimes(matchItems.length);

      // 8. 验证统计信息
      const updatedBatch = await batchRepo.findOne(batchId);

      // 验证可以重试, 存在执行错误数据
      expect(updatedBatch.canRetry).toBe(1);
      expect(updatedBatch.statisticsInfo.errorCount).toBeGreaterThan(0);

      BatchTestUtils.validateStatisticsInfo(updatedBatch.statisticsInfo, {
        paidCount: succeedCount,
        errorCount: errorCount,
        recordCount: matchItems.length,
        successCount: succeedCount,
        updatedCount: 0,
        duplicatedCount: 0,
        withholdingCount: matchItems.length,
        withholdingRecordCount: 0,
      });

      // 9 验证套餐额度扣费
      const originalStock1 = await bundleCounter.getStock();
      // 批量排查每天的次数上限卡控 1 次  1 个额度
      // 排查额度扣费 DiligenceCompanyQuantity 1 次 matchItems.length 个 额度
      // 按排查次数数量扣费 DiligenceHistoryQuantity 1 次 matchItems.length 个额度
      // 每日尽调上限校验 DiligenceDailyQuantity  1次 matchItems.length 个额度
      expect(bundleCounter.increase).toBeCalledTimes(4);
      // 失败退回排查额度 DiligenceCompanyQuantity 1 次 matchItems.length - paidCount 个 额度
      expect(bundleCounter.decrease).toBeCalledTimes(1);
      // 当前余额 = 初始余额 - 排查次数 1 个额度 - matchItems.length * 3 个额度 + 失败退回排查额度 1 次 matchItems.length - paidCount 个 额度
      expect(originalStock1.stock).toEqual(originalStock.stock - 1 - matchItems.length * 3 + (matchItems.length - succeedCount));

      // 10 重试，验证重试成功
      getRiskListForBatchJobSpy1.mockRestore();
      const getRiskListForBatchJobSpy2 = jest
        .spyOn(evaluationService as any, 'getRiskListForBatchJob')
        .mockImplementation(async (user, params: { companyId: string; companyName: string }) => {
          diligenceId++;
          return {
            id: diligenceId,
            companyId: params.companyId,
            name: params.companyName || `测试公司${params.companyId}`,
            snapshotId: `snapshot_${diligenceId}`,
            details: { hits: [], score: 85 },
            createDate: new Date(),
            paid: true,
            operatorId: testUserId,
            orgId: testOrgId,
          };
        });

      const retryJobs = await batchJobRepo.find({ where: { jobId: In(failedResults.map((r) => r.jobId)) } });
      const retryItemsCount = retryJobs.map((job) => job.jobInfo.itemSize).reduce((a, b) => a + b, 0);

      await batchMessageService.retryBatch(batchId, testUser);
      const retryBatch = await BatchTestUtils.waitForBatchCompletion(batchRepo.manager, batchId);
      expect(retryBatch.status).toBe(BatchStatusEnums.Done);
      expect(retryBatch.canRetry).toBe(0);
      BatchTestUtils.validateStatisticsInfo(retryBatch.statisticsInfo, {
        paidCount: matchItems.length,
        errorCount: 0,
        recordCount: matchItems.length,
        successCount: matchItems.length,
        updatedCount: 0,
        duplicatedCount: 0,
        withholdingCount: matchItems.length,
        withholdingRecordCount: 0,
      });

      // 验证重试的执行范围
      expect(getRiskListForBatchJobSpy2).toHaveBeenCalledTimes(retryItemsCount);

      // 11 验证重试后套餐额度扣费
      const originalStock2 = await bundleCounter.getStock();
      // 在重试前上次扣费 4 次 的基础上 再扣以下3次
      // 排查额度扣费 DiligenceCompanyQuantity 1 次 上次未成功的数量 (matchItems.length - succeedCount) 个 额度
      // 按排查次数数量扣费 DiligenceHistoryQuantity 1 次 上次未成功的数量 (matchItems.length - succeedCount) 个额度
      // 每日尽调上限校验 DiligenceDailyQuantity  1次 上次未成功的数量 (matchItems.length - succeedCount) 个额度
      expect(bundleCounter.increase).toBeCalledTimes(4 + 3);
      // 重试前退费 1 次
      expect(bundleCounter.decrease).toBeCalledTimes(1);
      // 重试后余额 = 重试前余额  - 上次未成功的数量 (matchItems.length - succeedCount) 个 额度 * 3 个额度
      expect(originalStock2.stock).toEqual(originalStock1.stock - (matchItems.length - succeedCount) * 3);
      // 总余额 = 初始余额 - 排查次数 1 个额度 - matchItems.length * 3 个额度 + 失败退回排查额度 1 次 (matchItems.length - succeedCount) 个额度 - 上次未成功的数量 (matchItems.length - succeedCount) 个 额度 * 3 个额度
      expect(originalStock2.stock).toEqual(
        originalStock.stock - 1 - matchItems.length * 3 + (matchItems.length - succeedCount) - (matchItems.length - succeedCount) * 3,
      );

      getRiskListForBatchJobSpy2.mockRestore();
    } finally {
      // 恢复spy
      diligenceQueueSpy.mockRestore();
      jobMonitorQueueSpy.mockRestore();
      getRiskListForBatchJobSpy1.mockRestore();
      userServiceSpy.mockRestore();
    }
  });
});
