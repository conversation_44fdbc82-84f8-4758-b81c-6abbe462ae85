import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../../app/app.test.module';
import {
  clearCustomerImportData,
  clearImportData,
  fakeBatchMatchItemList,
  fakeBatchPersonItemList,
  fakeMonitorItemList,
  prepareImportData,
} from '../../test_utils_module/import.test.utils';
import { EntityManager, getRepository, Repository } from 'typeorm';
import { BatchMatchCompanyEntity } from '../../../libs/entities/BatchMatchCompanyEntity';
import { BatchService } from '../service/batch.service';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { BatchResultEntity } from '../../../libs/entities/BatchResultEntity';
import { CustomerEntity } from '../../../libs/entities/CustomerEntity';
import { InnerBlacklistEntity } from '../../../libs/entities/InnerBlacklistEntity';
import { PersonEntity } from '../../../libs/entities/PersonEntity';
import { MonitorGroupCompanyEntity } from '../../../libs/entities/MonitorGroupCompanyEntity';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchBaseHelper } from '../service/helper/batch.base.helper';
import { BatchMessageService } from '../message.handler/batch.message.service';
import { BatchMessageHandlerDefault } from '../message.handler/default/batch.message.handler.default';
import { MockBundleService, RoverBundleCounterType, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { CustomerService } from '../../customer/customer.service';
import { RoverGraphService } from '../../data/source/rover.graph.service';
import { FileParseResult } from '../../../libs/model/batch/po/parse/FileParseResult';
import { BatchResultExportService } from '../service/batch.result.export.service';
import { PersonImportFileParser } from '../file.parser/PersonImportFileParser';
import { BatchTestUtils } from '../../test_utils_module/batch.test.utils';
import { UserService } from '../../user/user.service';
import { TestHelper } from '../../test_utils_module/test.helper';
import { MonitorImportFileParser } from '../file.parser/MonitorImportFileParser';
import { MonitorCompanyService } from '../../monitor/monitor.company.service';
import { SearchCompanyRequest } from '../../../libs/model/monitor/request/SearchCompanyRequest';
import { BadRequestException } from '@nestjs/common';
import * as Bluebird from 'bluebird';
import { BatchModule } from '../batch.module';
import { generateUniqueTestIds, getTestUser, UserTestUtils } from 'apps/test_utils_module/test.user';
const [testOrgId, testUserId] = generateUniqueTestIds('batch.import.unittest.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(60 * 1000);

async function sleep(sec) {
  return new Promise((resolve) => {
    const timer = setTimeout(() => {
      clearTimeout(timer);
      resolve(true);
    }, sec * 1000).unref();
  });
}

describe('集成测试-批量导入', () => {
  let entityManager: EntityManager;
  let batchService: BatchService;
  let customerService: CustomerService;
  let batchBaseHelper: BatchBaseHelper;
  let batchMessageService: BatchMessageService;
  let batchMessageHandlerDefault: BatchMessageHandlerDefault;
  let batchEntityRepo: Repository<BatchEntity>;
  let batchResultEntityRepo: Repository<BatchResultEntity>;
  let customerEntityRepo: Repository<CustomerEntity>;
  let innerBlacklistEntityRepo: Repository<InnerBlacklistEntity>;
  let personEntityRepo: Repository<PersonEntity>;
  let monitorGroupEntityRepo: Repository<MonitorGroupCompanyEntity>;
  let roverGraphService: RoverGraphService;
  let resultExportService: BatchResultExportService;
  let personFileParser: PersonImportFileParser;
  let monitorImportFileParser: MonitorImportFileParser;
  let monitorCompanyService: MonitorCompanyService;
  let userService: UserService;
  let mockedBundleService: MockBundleService<RoverBundleCounterType, RoverBundleLimitationType>;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    BatchTestUtils.spyOnBatchProcess(module, BatchBusinessTypeEnums.Customer_File);
    BatchTestUtils.spyOnBatchProcess(module, BatchBusinessTypeEnums.InnerBlacklist_File);
    BatchTestUtils.spyOnBatchProcess(module, BatchBusinessTypeEnums.Person_File);
    BatchTestUtils.spyOnBatchProcess(module, BatchBusinessTypeEnums.Monitor_File);
    UserTestUtils.spyUserService(testUserId, module.get(UserService));

    entityManager = getRepository(BatchMatchCompanyEntity).manager;
    batchService = module.get(BatchService);
    batchBaseHelper = module.get(BatchBaseHelper);
    batchMessageService = module.get(BatchMessageService);
    batchMessageHandlerDefault = module.get(BatchMessageHandlerDefault);
    customerService = module.get(CustomerService);
    roverGraphService = module.get(RoverGraphService);
    resultExportService = module.get(BatchResultExportService);
    monitorImportFileParser = module.get(MonitorImportFileParser);
    personFileParser = module.get(PersonImportFileParser);
    batchEntityRepo = getRepository(BatchEntity);
    batchResultEntityRepo = getRepository(BatchResultEntity);
    customerEntityRepo = getRepository(CustomerEntity);
    innerBlacklistEntityRepo = getRepository(InnerBlacklistEntity);
    personEntityRepo = getRepository(PersonEntity);
    monitorGroupEntityRepo = getRepository(MonitorGroupCompanyEntity);
    monitorCompanyService = module.get(MonitorCompanyService);
    userService = module.get(UserService);
    // mock bundle service
    mockedBundleService = module.get(RoverBundleService);
  });
  beforeEach(async () => {
    await clearImportData(entityManager, testUser, roverGraphService);
    await TestHelper.clearAllTestData(entityManager, testUser.currentOrg, testUser.userId);
    // 数据清理
    await monitorCompanyService.remove(testUser, Object.assign(new SearchCompanyRequest(), { companyIds: fakeMonitorItemList.map((a) => a.companyId) }));
    await entityManager.delete(MonitorGroupEntity, { orgId: testUser.currentOrg });
  });

  afterEach(async () => {
    await clearImportData(entityManager, testUser, roverGraphService);
    await TestHelper.clearAllTestData(entityManager, testUser.currentOrg, testUser.userId);
    // 数据清理
    await monitorCompanyService.remove(testUser, Object.assign(new SearchCompanyRequest(), { companyIds: fakeMonitorItemList.map((a) => a.companyId) }));
  });

  afterAll(async () => {
    await clearImportData(entityManager, testUser, roverGraphService);
    await TestHelper.clearAllTestData(entityManager, testUser.currentOrg, testUser.userId);
    await entityManager.connection.close();
  });

  describe.skip('debug专用', () => {
    it.skip('debug-解析上传的 Excel', async () => {
      const filePath = '/Users/<USER>/Downloads/chromeDownload/第三方列表导入模版_ (1).xlsx';
      await batchService.matchBatchCompany(testUser, filePath, '第三方列表导入模版_.xlsx', BatchBusinessTypeEnums.Customer_File);
    });
  });

  describe('第三方列表导入', () => {
    it('第三方列表导入失败处理', async () => {
      // 清理数据
      // 数据准备 batch_match_company 、batch_match_company_item
      const batchMatchId = await prepareImportData(entityManager, testUser, BatchBusinessTypeEnums.Customer_File);
      const customerCounter = await mockedBundleService.getBundleCounter(testUser, RoverBundleCounterType.ThirdPartyQuantity);
      const stock1 = await customerCounter.getStock();
      const originalStock = stock1.stock;
      jest.spyOn(batchMessageHandlerDefault.batchJobQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageHandlerDefault.handleJobMessage(msg);
      });
      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageService.processJobMonitorMessage(msg);
      });
      const spyInstance = jest.spyOn(customerService.companySearchService, 'createCompanyInfo').mockReturnValue(Promise.resolve(undefined));
      await batchService.executeBatchImport(testUser, batchMatchId, BatchBusinessTypeEnums.Customer_File);
      spyInstance.mockRestore();
      // 导入额度消耗校验
      const stock3 = await customerCounter.getStock();
      expect(originalStock).toBe(stock3.stock);
    });

    it('第三方列表导入主体流程', async () => {
      // 清理数据
      await clearCustomerImportData(entityManager, testUser);
      // 数据准备 batch_match_company 、batch_match_company_item
      let batchMatchId = await prepareImportData(entityManager, testUser, BatchBusinessTypeEnums.Customer_File);
      let batchId;
      const customerCounter = await mockedBundleService.getBundleCounter(testUser, RoverBundleCounterType.ThirdPartyQuantity);
      const stock1 = await customerCounter.getStock();

      const originalStock = stock1.stock;
      jest.spyOn(userService, 'getRoverUser').mockReturnValue(Promise.resolve(testUser));
      jest.spyOn(batchMessageHandlerDefault.batchJobQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageHandlerDefault.handleJobMessage(msg);
      });
      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
        batchId = msg.batchId;
        return batchMessageService.processJobMonitorMessage(msg);
      });
      await batchService.executeBatchImport(testUser, batchMatchId, BatchBusinessTypeEnums.Customer_File);
      // 数据校验
      await sleep(5);
      const batchEntity = await batchEntityRepo.findOne({ batchId });
      const statisticsInfo = batchEntity.statisticsInfo;
      const batchResultEntities = await batchResultEntityRepo.find({ batchId });
      expect(statisticsInfo.recordCount).toBe(batchResultEntities?.length);
      const customerEntities = await customerEntityRepo.find({ batchId });
      expect(statisticsInfo.recordCount).toBe(customerEntities?.length);

      expect(mockedBundleService.getBundleCounter).toBeCalled();
      // 导入额度消耗校验
      const stock2 = await customerCounter.getStock();
      expect(originalStock - stock2.stock).toBe(statisticsInfo.paidCount);
      expect(customerCounter.increase).toBeCalledTimes(6);

      // 导入额度退回
      await customerCounter.decrease(fakeBatchMatchItemList.length);
      expect((await customerCounter.getStock()).stock).toBe(originalStock);

      // 重复导入
      batchMatchId = await prepareImportData(entityManager, testUser, BatchBusinessTypeEnums.Customer_File);
      await batchService.executeBatchImport(testUser, batchMatchId, BatchBusinessTypeEnums.Customer_File);
      // 重复导入额度消耗校验
      const stock3 = await customerCounter.getStock();
      expect(originalStock).toBe(stock3.stock);
    });
  });

  describe('内部黑名单导入', () => {
    it('内部黑名单导入主体流程', async () => {
      // 清理数据
      // 数据准备 batch_match_company 、batch_match_company_item
      let batchMatchId = await prepareImportData(entityManager, testUser, BatchBusinessTypeEnums.InnerBlacklist_File);
      let batchId;
      const blackListCounter = await mockedBundleService.getBundleCounter(testUser, RoverBundleCounterType.InnerBlacklistQuantity);
      const stock1 = await blackListCounter.getStock();
      jest.spyOn(userService, 'getRoverUser').mockReturnValue(Promise.resolve(testUser));
      jest.spyOn(batchMessageHandlerDefault.batchJobQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageHandlerDefault.handleJobMessage(msg);
      });
      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
        batchId = msg.batchId;
        return batchMessageService.processJobMonitorMessage(msg);
      });
      await batchService.executeBatchImport(testUser, batchMatchId, BatchBusinessTypeEnums.InnerBlacklist_File);
      // 数据校验
      const batchEntity = await batchEntityRepo.findOne({ batchId });
      const statisticsInfo = batchEntity.statisticsInfo;
      const batchResultEntities = await batchResultEntityRepo.find({ batchId });
      expect(statisticsInfo.recordCount).toBe(batchResultEntities?.length);
      const innerBlacklistEntities = await innerBlacklistEntityRepo.find({ batchId });
      expect(statisticsInfo.recordCount).toBe(innerBlacklistEntities?.length);

      // 导入额度消耗校验
      const stock2 = await blackListCounter.getStock();
      const originalStock = stock1.stock;
      expect(originalStock - stock2.stock).toBe(statisticsInfo.paidCount);

      // 导入额度退回
      await blackListCounter.decrease(fakeBatchMatchItemList.length);
      expect((await blackListCounter.getStock()).stock).toBe(originalStock);

      // 重复导入
      batchMatchId = await prepareImportData(entityManager, testUser, BatchBusinessTypeEnums.InnerBlacklist_File);
      await batchService.executeBatchImport(testUser, batchMatchId, BatchBusinessTypeEnums.InnerBlacklist_File);
      // 导入额度消耗校验
      const stock3 = await blackListCounter.getStock();
      expect(originalStock).toBe(stock3.stock);
    });

    it('内部黑名单导入失败处理', async () => {
      const batchMatchId = await prepareImportData(entityManager, testUser, BatchBusinessTypeEnums.InnerBlacklist_File);
      const blackListCounter = await mockedBundleService.getBundleCounter(testUser, RoverBundleCounterType.InnerBlacklistQuantity);
      const stock1 = await blackListCounter.getStock();
      jest.spyOn(batchMessageHandlerDefault.batchJobQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageHandlerDefault.handleJobMessage(msg);
      });
      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageService.processJobMonitorMessage(msg);
      });
      jest.spyOn(customerService.companySearchService, 'createCompanyInfo').mockReturnValue(Promise.resolve(undefined));
      await batchService.executeBatchImport(testUser, batchMatchId, BatchBusinessTypeEnums.InnerBlacklist_File);

      // 导入额度消耗校验
      const originalStock = stock1.stock;
      const stock3 = await blackListCounter.getStock();
      expect(originalStock).toBe(stock3.stock);
    });
  });

  describe('人员导入', () => {
    it('人员导入主体流程', async () => {
      let batchId;
      const personCounter = await mockedBundleService.getBundleCounter(testUser, RoverBundleCounterType.PersonQuantity);
      const stock1 = await personCounter.getStock();

      jest.spyOn(userService, 'getRoverUser').mockReturnValue(Promise.resolve(testUser));
      jest
        .spyOn(personFileParser, 'parseFile')
        .mockReturnValue(Promise.resolve(Object.assign(new FileParseResult(), { succeedItems: fakeBatchPersonItemList })));
      jest.spyOn(resultExportService, 'putFileToOss').mockReturnValue(Promise.resolve('test-mock-url'));

      jest.spyOn(batchMessageHandlerDefault.batchJobQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageHandlerDefault.handleJobMessage(msg);
      });
      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
        batchId = msg.batchId;
        return batchMessageService.processJobMonitorMessage(msg);
      });
      await batchService.createBatchByFile(testUser, '', '人员管理导入模版_单测.xlsx', BatchBusinessTypeEnums.Person_File);

      // 数据校验
      const batchEntity = await batchEntityRepo.findOne({ batchId });
      const statisticsInfo = batchEntity.statisticsInfo;
      const batchResultEntities = await batchResultEntityRepo.find({ batchId });
      expect(statisticsInfo.recordCount).toBe(batchResultEntities?.length);
      const personEntities = await personEntityRepo.find({ batchId });
      expect(statisticsInfo.successCount).toBe(personEntities?.length);

      // 导入额度消耗校验
      const stock2 = await personCounter.getStock();
      const originalStock = stock1.stock;
      expect(originalStock - stock2.stock).toBe(statisticsInfo.paidCount);

      // 导入额度退回
      await personCounter.decrease(fakeBatchPersonItemList.length);
      expect((await personCounter.getStock()).stock).toBe(originalStock);

      // 重复导入
      await batchService.createBatchByFile(testUser, '', '人员管理导入模版_单测.xlsx', BatchBusinessTypeEnums.Person_File);
      // 重复导入额度消耗校验
      const stock3 = await personCounter.getStock();
      expect(originalStock).toBe(stock3.stock);
    });

    it('人员管理导入失败处理', async () => {
      const personCounter = await mockedBundleService.getBundleCounter(testUser, RoverBundleCounterType.PersonQuantity);
      const stock1 = await personCounter.getStock();
      const fakeBatchPersonItemList2 = fakeBatchPersonItemList.map((f) => {
        f.email = f.email.replace('.com', '');
        return f;
      });
      const fileParseResult = Object.assign(new FileParseResult(), { succeedItems: fakeBatchPersonItemList2 });
      jest.spyOn(personFileParser, 'parseFile').mockReturnValue(Promise.resolve(fileParseResult));
      jest.spyOn(resultExportService, 'putFileToOss').mockReturnValue(Promise.resolve('test-mock-url'));

      jest.spyOn(batchMessageHandlerDefault.batchJobQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageHandlerDefault.handleJobMessage(msg);
      });
      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageService.processJobMonitorMessage(msg);
      });
      await batchService.createBatchByFile(testUser, '', '人员管理导入模版_单测.xlsx', BatchBusinessTypeEnums.Person_File);
      // 导入额度消耗校验
      const originalStock = stock1.stock;
      const stock3 = await personCounter.getStock();
      expect(originalStock).toBe(stock3.stock);
    });
  });

  describe('监控列表导入', () => {
    it('监控列表导入主体流程', async () => {
      let batchId;
      const monitorCounter = await mockedBundleService.getBundleCounter(testUser, RoverBundleCounterType.MonitorCompanyQuantity);
      const stock1 = await monitorCounter.getStock();

      jest
        .spyOn(monitorImportFileParser, 'parseFile')
        .mockReturnValue(Promise.resolve(Object.assign(new FileParseResult(), { succeedItems: fakeMonitorItemList })));
      jest.spyOn(resultExportService, 'putFileToOss').mockReturnValue(Promise.resolve('test-mock-url'));
      jest.spyOn(userService, 'getRoverUser').mockReturnValue(Promise.resolve(testUser));
      // 不向 C端发送请求
      jest.spyOn(monitorCompanyService, 'sendToRiskDb').mockReturnValue(Promise.resolve([]));

      jest.spyOn(batchMessageHandlerDefault.batchJobQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageHandlerDefault.handleJobMessage(msg);
      });
      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
        batchId = msg.batchId;
        return batchMessageService.processJobMonitorMessage(msg);
      });
      await batchService.createBatchByFile(testUser, '', '监控列表导入模版_单测.xlsx', BatchBusinessTypeEnums.Monitor_File);

      // 数据校验
      const batchEntity = await batchEntityRepo.findOne({ batchId });
      const statisticsInfo = batchEntity.statisticsInfo;
      const batchResultEntities = await batchResultEntityRepo.find({ batchId });
      expect(statisticsInfo.recordCount).toBe(batchResultEntities?.length);
      const monitorGroupCompanyEntities = await monitorGroupEntityRepo.find({ batchId });
      expect(statisticsInfo.successCount).toBe(monitorGroupCompanyEntities?.length);

      // 导入额度消耗校验
      const stock2 = await monitorCounter.getStock();
      const originalStock = stock1.stock;
      expect(originalStock - stock2.stock).toBe(statisticsInfo.paidCount);

      // 导入额度退回
      await monitorCounter.decrease(statisticsInfo.paidCount);
      expect((await monitorCounter.getStock()).stock).toBe(originalStock);

      // 重复导入
      await batchService.createBatchByFile(testUser, '', '监控列表导入模版_单测.xlsx', BatchBusinessTypeEnums.Monitor_File);
      // 重复导入额度消耗校验
      const stock3 = await monitorCounter.getStock();
      expect(originalStock).toBe(stock3.stock);
    });

    it('监控列表导入失败处理', async () => {
      const monitorCounter = await mockedBundleService.getBundleCounter(testUser, RoverBundleCounterType.MonitorCompanyQuantity);
      const stock1 = await monitorCounter.getStock();

      jest
        .spyOn(monitorImportFileParser, 'parseFile')
        .mockReturnValue(Promise.resolve(Object.assign(new FileParseResult(), { succeedItems: fakeMonitorItemList })));
      jest.spyOn(resultExportService, 'putFileToOss').mockReturnValue(Promise.resolve('test-mock-url'));
      jest.spyOn(userService, 'getRoverUser').mockReturnValue(Promise.resolve(testUser));
      // 模拟发送请求异常
      jest.spyOn(monitorCompanyService, 'sendToRiskDb').mockImplementation(() => {
        throw new BadRequestException('模拟发送请求异常');
      });

      jest.spyOn(batchMessageHandlerDefault.batchJobQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageHandlerDefault.handleJobMessage(msg);
      });
      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageService.processJobMonitorMessage(msg);
      });
      await batchService.createBatchByFile(testUser, '', '监控列表导入模版_单测.xlsx', BatchBusinessTypeEnums.Monitor_File);

      // 导入额度消耗校验
      const stock2 = await monitorCounter.getStock();
      expect(stock2.stock).toBe(stock1.stock);
    });

    it('监控列表导入二次确认流程测试', async () => {
      // 准备数据，创建分组数据
      await entityManager.save(MonitorGroupEntity, {
        name: '默认分组',
        orgId: testUser.currentOrg,
        ownerId: testUser.userId,
      });
      await entityManager.save(MonitorGroupEntity, {
        name: 'group2',
        orgId: testUser.currentOrg,
        ownerId: testUser.userId,
      });
      jest
        .spyOn(monitorImportFileParser, 'parseFile')
        .mockReturnValue(Promise.resolve(Object.assign(new FileParseResult(), { succeedItems: fakeMonitorItemList })));
      jest.spyOn(resultExportService, 'putFileToOss').mockReturnValue(Promise.resolve('test-mock-url'));
      jest.spyOn(userService, 'getRoverUser').mockReturnValue(Promise.resolve(testUser));
      // 不向 C端发送请求
      jest.spyOn(monitorCompanyService, 'sendToRiskDb').mockReturnValue(Promise.resolve([]));
      jest.spyOn(batchMessageHandlerDefault.batchJobQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageHandlerDefault.handleJobMessage(msg);
      });
      jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
        return batchMessageService.processJobMonitorMessage(msg);
      });
      const batchEntity = await batchService.matchBatchCompany(
        testUser,
        '/test/监控列表导入模版_单测.xlsx',
        '监控列表导入模版_单测.xlsx',
        BatchBusinessTypeEnums.Monitor_File,
      );
      //第一次批量导入不覆盖更新，isUpdate为false
      await batchService.executeBatchImport(testUser, batchEntity.id, BatchBusinessTypeEnums.Monitor_File, false);
      await Bluebird.delay(3000);
      //判断第一次导入是否成功
      const successCompanies = await entityManager.find(MonitorGroupCompanyEntity, { orgId: testUser.currentOrg });
      //全部导入成功
      expect(successCompanies.length).toBe(fakeMonitorItemList.length);
      //再次导入，修改导入公司的监控分组
      fakeMonitorItemList.forEach((f) => (f.group = 'group2'));
      const batchEntity2 = await batchService.matchBatchCompany(
        testUser,
        '/test/监控列表导入模版_单测.xlsx',
        '监控列表导入模版_单测.xlsx',
        BatchBusinessTypeEnums.Monitor_File,
      );
      //第二次批量导入覆盖更新，isUpdate为true
      await batchService.executeBatchImport(testUser, batchEntity2.id, BatchBusinessTypeEnums.Monitor_File, true);
      await Bluebird.delay(3000);
      //判断第二次导入是否成功
      const successCompanies2 = await entityManager.find(MonitorGroupCompanyEntity, { orgId: testUser.currentOrg });
      //全部导入成功
      expect(successCompanies2.length).toBe(fakeMonitorItemList.length);
      //因为选择了覆盖更新，所以分组 Id 发生了变化
      expect(successCompanies2[0].groupId).not.toBe(successCompanies[0].groupId);
      //第三次导入，批量导入不覆盖更新，isUpdate为false
      const batchEntity3 = await batchService.matchBatchCompany(
        testUser,
        '/test/监控列表导入模版_单测.xlsx',
        '监控列表导入模版_单测.xlsx',
        BatchBusinessTypeEnums.Monitor_File,
      );
      await batchService.executeBatchImport(testUser, batchEntity3.id, BatchBusinessTypeEnums.Monitor_File, false);
      await Bluebird.delay(3000);
      //判断第三次导入是否成功
      const successCompanies3 = await entityManager.find(MonitorGroupCompanyEntity, { orgId: testUser.currentOrg });
      //全部导入成功
      expect(successCompanies3.length).toBe(fakeMonitorItemList.length);
      //因为选择了不覆盖更新，所以分组 Id 保持不变
      expect(successCompanies3[0].groupId).toBe(successCompanies2[0].groupId);
    });
  });
});
