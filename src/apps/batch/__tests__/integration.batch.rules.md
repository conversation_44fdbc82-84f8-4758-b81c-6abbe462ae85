### 批量任务 集成测试编写指南

#### 集成测试准备流程

1、详细阅读 src/apps/batch/service/batch.service.ts 中的 createBatchDiligenceTask 创建批量排查任务的详细内容以及后续批量排查任务的执行全过程；
2、所有的批量排查任务相关数据表结构定义都在 src/libs/entities/路径下 以 batch 开头的 entity 中，不要自己创建 status 和 type 相关的枚举，全部使用 entity 中定义的规则；
3、批量排查的公司数据从 src/appsbatch/**test**/批量排查模版.xlsx 中读取

#### 测试重点与实现策略

1. 模拟消息队列处理；
2. 模拟 evaluationService.getRiskListForBatchJob 方法，避免实际调用外部 API；
3. 业务流程:
   a 导入 Excel 模板并匹配公司
   b 使用匹配的公司数据创建批量排查任务
   c 执行批量排查任务
   d 验证 batch 状态、 job 状态、batch.statisticsInfo 中数据的一致性
4. 重点测试，批量排查全部成功、部分成功、全部失败、job 超时、自动重试
5. matchBatchDiligenceCompany 的返回值不是批量排查任务，需要将取出 matchBatchDiligenceCompany 后的 matchCompany 数据调用 createBatchDiligenceTask 方法重新创建批量排查任务，

#### 高级测试场景

1. **状态一致性测试**

   - 验证客户端状态与模拟服务器状态的一致性
   - 测试不同同步触发条件下的数据更新

2. **性能和资源使用测试**

   - 测试大数据量下的同步性能
   - 验证内存使用情况和潜在泄漏

3. **多实现类验证**

   - 使用参数化测试为不同产品的实现类运行相同的测试套件
   - 验证所有实现类都符合抽象类定义的契约

4. **并发测试**

   - 模拟多用户同时操作的场景
   - 验证在高并发条件下数据一致性和锁机制是否正常工作
   - 测试竞态条件下的服务行为

5. **基本断言原则**

   - 使用 Jest 提供的断言方法，避免自定义断言逻辑
   - 每个测试用例应包含至少一个明确的断言
   - 断言失败时提供清晰的错误信息，便于问题定位

6. **常用断言方法推荐**

   - 对象比较：使用 `expect().toEqual()` 进行深度比较
   - 对象部分比较：使用 `expect().toMatchObject()` 或 `expect().toHaveProperty()`
   - 数组断言：使用 `expect().toContain()` 或 `expect().toHaveLength()`
   - 异常断言：使用 `expect(() => {}).toThrow()` 验证异常情况
   - 异步断言：使用 `expect().resolves` 或 `expect().rejects` 处理 Promise

7. **复杂场景断言**

   - 使用 `expect.any(Constructor)` 处理不确定的具体值但确定类型的情况
   - 使用 `expect.arrayContaining([])` 验证数组包含特定元素
   - 使用 `expect.objectContaining({})` 验证对象包含特定属性
   - 自定义匹配器处理特殊业务逻辑断言

8. **断言示例**

   ```typescript
   // 验证服务方法返回值
   expect(result).toEqual(
     expect.objectContaining({
       success: true,
       data: expect.arrayContaining([expect.objectContaining({ id: expect.any(String) })]),
     }),
   );

   // 验证异常抛出
   await expect(service.someMethodWithError()).rejects.toThrow(SpecificError);

   // 验证服务状态变化
   expect(service.getState()).toEqual('expected_state');
   ```

#### 测试覆盖率标准

1. **整体覆盖率目标**

   - 行覆盖率（Line Coverage）：≥ 85%
   - 分支覆盖率（Branch Coverage）：≥ 80%
   - 函数覆盖率（Function Coverage）：≥ 90%
   - 语句覆盖率（Statement Coverage）：≥ 85%

2. **关键模块覆盖率要求**

   - 核心业务逻辑模块：≥ 90% 行覆盖率
   - 错误处理和边界条件：≥ 85% 分支覆盖率
   - 公共 API 和接口方法：100% 函数覆盖率

3. **覆盖率测量与报告**

   - 使用 Jest 内置的覆盖率工具 (`--coverage` 参数)
   - 配置 `.coveragerc` 文件排除不需要测试的文件或代码块
   - 在 CI 流程中强制执行覆盖率检查
   - 定期生成覆盖率报告并进行团队审查

4. **低覆盖率处理策略**
   - 对于测试覆盖率低于标准的模块，需要提交改进计划
   - 允许通过文档说明合理忽略特定代码块的覆盖率
   - 新增代码必须满足覆盖率标准才能合并

#### 测试环境配置

1. 相关环境变量我已经在 jest.setup.js 中动态设置

#### 测试数据清理

1. **清理策略**

   - 每个测试用例执行后必须清理其创建的所有数据
   - 使用事务包装测试，在 `afterEach` 中回滚事务
   - 对于非事务性存储（如 Redis、ES），使用专门的清理函数
   - 关注下 testHelper.cleanTestBundles 这个方法来清理测试数据，如果这个方法有遗漏，可以及时补充并更新

#### 测试超时和重试机制

1. **超时设置**

   - 单个测试用例超时时间：10 秒
   - 异步操作等待超时：3 秒
   - 配置 Jest 超时参数：`jest.setTimeout(10000)`

2. **重试策略**
   - 对于网络相关的不稳定测试，配置最多 3 次重试
   - 使用 Jest 的 `retry` 功能或自定义重试逻辑
   - 重试之间添加递增延迟，避免立即重试

#### 测试最佳实践

1. 使用事务包装每个测试，确保测试数据隔离
2. 在 `beforeEach` 中准备测试数据，在 `afterEach` 中彻底清理
3. 使用中文描述测试用例，提高可读性
4. 按照 "Given-When-Then" 模式组织测试步骤
5. 对异步操作使用适当的等待机制，避免时序问题
6. 定期审查和重构测试代码，保持测试质量
7. 测试失败时提供明确的错误信息，便于问题定位
8. 避免测试间的依赖，每个测试应该独立运行
9. 测试代码应遵循与产品代码相同的质量标准
10. 在 CI/CD 流程中集成测试执行和覆盖率检查
