import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../../app/app.test.module';
import { EntityManager, getRepository, In, Repository } from 'typeorm';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { BatchService } from '../service/batch.service';
import { BatchJobEntity } from '../../../libs/entities/BatchJobEntity';
import { BatchResultEntity } from '../../../libs/entities/BatchResultEntity';
import { BatchBaseHelper } from '../service/helper/batch.base.helper';
import { BatchMessageService } from '../message.handler/batch.message.service';
import { BatchMessageHandlerDiligence } from '../message.handler/diligence/batch.message.handler.diligence';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { BatchMatchCompanyEntity } from '../../../libs/entities/BatchMatchCompanyEntity';
import { BatchMatchCompanyItemEntity } from '../../../libs/entities/BatchMatchCompanyItemEntity';
import * as path from 'path';
import * as fs from 'fs';
import { BatchStatusEnums } from '../../../libs/enums/batch/BatchStatusEnums';
import { BatchDiligenceEntity } from '../../../libs/entities/BatchDiligenceEntity';
import { EvaluationService } from '../../diligence/evaluation/evaluation.service';
import { BatchModule } from '../batch.module';
import { UserService } from '../../user/user.service';
import { BatchJobResultTypeEnums } from '../../../libs/enums/batch/BatchJobResultTypeEnums';
import { RoverBundleCounterType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { DiligenceHistoryEntity } from '../../../libs/entities/DiligenceHistoryEntity';
import { DiligenceSnapshotService } from '../../diligence/snapshot/diligence.snapshot.service';
import { SnapshotQueueTypeEnums } from '../../diligence/snapshot/po/SnapshotQueueTypeEnums';
import { BatchTestUtils, sleep } from '../../test_utils_module/batch.test.utils';

// 生成测试用户ID
const [testOrgId, testUserId] = generateUniqueTestIds('batch.diligence.integration.1.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

// 设置较长的超时时间，因为批量任务可能需要较长时间
jest.setTimeout(300 * 1000);

/**
 * 批量排查集成测试
 * 测试从Excel模板导入到完成整个流程
 */
describe('集成测试 - 批量排查job全部成功, 随机 paid', () => {
  let module: TestingModule;
  let batchService: BatchService;
  let batchMessageService: BatchMessageService;
  let batchMessageHandlerDiligence: BatchMessageHandlerDiligence;
  let batchBaseHelper: BatchBaseHelper;
  let evaluationService: EvaluationService;
  let userService: UserService;
  let entityManager: EntityManager;
  let bundleService: RoverBundleService;
  let snapshotService: DiligenceSnapshotService;

  // 数据库仓库
  let batchRepo: Repository<BatchEntity>;
  let batchJobRepo: Repository<BatchJobEntity>;
  let batchResultRepo: Repository<BatchResultEntity>;
  let batchMatchCompanyRepo: Repository<BatchMatchCompanyEntity>;
  let batchMatchCompanyItemRepo: Repository<BatchMatchCompanyItemEntity>;
  let batchDiligenceRepo: Repository<BatchDiligenceEntity>;

  // 测试数据
  let templateFilePath: string;
  let batchId: number;
  let matchCompanyId: number;

  beforeAll(async () => {
    // 创建测试模块
    module = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();

    // 获取服务和仓库
    batchService = module.get<BatchService>(BatchService);
    batchMessageService = module.get<BatchMessageService>(BatchMessageService);
    batchMessageHandlerDiligence = module.get<BatchMessageHandlerDiligence>(BatchMessageHandlerDiligence);
    batchBaseHelper = module.get<BatchBaseHelper>(BatchBaseHelper);
    evaluationService = module.get<EvaluationService>(EvaluationService);
    userService = module.get<UserService>(UserService);
    entityManager = module.get<EntityManager>(EntityManager);
    bundleService = module.get(RoverBundleService);

    snapshotService = module.get(DiligenceSnapshotService);

    batchRepo = getRepository(BatchEntity);
    batchJobRepo = getRepository(BatchJobEntity);
    batchResultRepo = getRepository(BatchResultEntity);
    batchMatchCompanyRepo = getRepository(BatchMatchCompanyEntity);
    batchMatchCompanyItemRepo = getRepository(BatchMatchCompanyItemEntity);
    batchDiligenceRepo = getRepository(BatchDiligenceEntity);

    // 设置模板文件路径
    templateFilePath = path.join(__dirname, './批量排查模版.xlsx');

    // 确保测试文件存在
    if (!fs.existsSync(templateFilePath)) {
      throw new Error(`测试文件不存在: ${templateFilePath}`);
    }
  });

  beforeEach(async () => {
    // 清理之前的测试数据
    await BatchTestUtils.clearTestDiligenceData(entityManager, testOrgId);
  });

  afterAll(async () => {
    // 清理所有测试数据
    await BatchTestUtils.clearTestDiligenceData(entityManager, testOrgId);
    jest.clearAllMocks();
    await batchRepo.manager.connection.close();
  });

  /**
   * 创建基础的消息队列和服务模拟
   */
  function setupBasicMocks() {
    const diligenceQueueSpy = jest.spyOn(batchMessageHandlerDiligence.batchDiligenceQueue, 'sendMessageV2').mockImplementation(async (msg) => {
      await sleep(2);
      batchMessageHandlerDiligence.handleJobMessage(msg);
    });

    const jobMonitorQueueSpy = jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation(async (msg) => {
      await sleep(0.2);
      batchMessageService.processJobMonitorMessage(msg);
    });

    const userServiceSpy = jest.spyOn(userService, 'getRoverUser').mockResolvedValue(testUser);

    return { diligenceQueueSpy, jobMonitorQueueSpy, userServiceSpy };
  }

  it('job全部成功, 随机 paid', async () => {
    // Given: 设置测试环境和模拟
    const { diligenceQueueSpy, jobMonitorQueueSpy, userServiceSpy } = setupBasicMocks();

    const bundleCounter = await bundleService.getBundleCounter(testUser, RoverBundleCounterType.DiligenceCompanyQuantity);
    const originalStock = await bundleCounter.getStock();
    let diligenceId = 10000;
    let paidCount = 0;
    const getRiskListForBatchJobSpy = jest
      .spyOn(evaluationService as any, 'getRiskListForBatchJob')
      .mockImplementation(async (user, params: { companyId: string; companyName: string }) => {
        const paid = Math.random() < 0.5;
        if (paid) {
          paidCount++;
        }
        return {
          id: diligenceId++,
          companyId: params.companyId,
          name: params.companyName,
          snapshotId: `snapshot_${params.companyId}`,
          details: {
            totalScore: 100,
            result: 0,
            dimensionScoreDetails: [],
            originalHits: [],
          },
          createDate: new Date(),
          operatorId: testUserId,
          orgId: testOrgId,
          updateDate: new Date(),
          result: 0, // 通过
          score: 100,
          shouldUpdate: 0,
          orgSettingsId: 1,
          realtime: true,
          paid,
          editor: null,
          orgSettingsLog: null,
        };
      });

    try {
      // 1. 导入Excel模板并匹配公司
      const matchResult = await batchService.matchBatchDiligenceCompany(testUser, templateFilePath, '批量排查模版.xlsx');
      expect(matchResult).toEqual(
        expect.objectContaining({
          id: expect.any(Number),
        }),
      );
      matchCompanyId = matchResult['id'];

      // 验证匹配结果
      const matchCompany = await batchMatchCompanyRepo.findOne({ where: { id: matchCompanyId } });
      expect(matchCompany).toBeDefined();
      expect(matchCompany).toMatchObject({
        id: matchCompanyId,
        orgId: testUser.currentOrg,
      });

      const matchItems = await batchMatchCompanyItemRepo.find({ where: { batchId: matchCompanyId } });
      expect(matchItems.length).toBeGreaterThan(0);

      // 2. 使用匹配结果创建批量排查任务
      const batchEntity = await batchService.executeBatchDiligenceCompany(testUser, matchCompany.id);

      expect(batchEntity).toBeDefined();
      batchId = batchEntity.batchId;

      // 4. 等待批量任务完成并验证状态
      const currentBatch = await BatchTestUtils.waitForBatchCompletion(batchRepo.manager, batchId);
      expect(currentBatch.status).toBe(BatchStatusEnums.Done);

      // 5. 验证作业状态
      const jobs = await batchJobRepo.find({
        where: {
          batchId,
        },
      });
      expect(jobs.map((job) => job.jobInfo.itemSize).reduce((a, b) => a + b, 0)).toBe(matchItems.length);
      expect(jobs.every((job) => job.status === BatchStatusEnums.Done)).toBe(true);

      // 6. 验证排查结果
      const resultCount = await batchResultRepo.count({
        where: {
          batchId,
          resultType: In([
            BatchJobResultTypeEnums.SUCCEED_DUPLICATED,
            BatchJobResultTypeEnums.SUCCEED_PAID,
            BatchJobResultTypeEnums.SUCCEED_UNPAID,
            BatchJobResultTypeEnums.SUCCEED_IGNORE,
          ]),
        },
      });
      expect(resultCount).toBe(matchItems.length);

      // 7. 验证尽调历史记录, 尽调方法被spy 无尽调历史记录，直接验证getRiskListForBatchJob 被调用次数
      expect(getRiskListForBatchJobSpy).toHaveBeenCalledTimes(matchItems.length);

      // 8. 验证批量尽调关联
      const batchDiligences = await batchDiligenceRepo.find({ where: { batchId } });
      expect(batchDiligences.length).toBe(matchItems.length);

      // 9. 验证统计信息
      const updatedBatch = await batchRepo.findOne(batchId);
      expect(updatedBatch).toBeDefined();

      BatchTestUtils.validateStatisticsInfo(updatedBatch.statisticsInfo, {
        paidCount: paidCount,
        errorCount: 0,
        recordCount: matchItems.length,
        successCount: matchItems.length,
        updatedCount: 0,
        duplicatedCount: 0,
        withholdingCount: matchItems.length,
        withholdingRecordCount: 0,
      });

      // 10 验证套餐额度扣费
      const originalStock1 = await bundleCounter.getStock();
      // 批量排查每天的次数上限卡控 1 次  1 个额度
      // 排查额度扣费 DiligenceCompanyQuantity 1 次 matchItems.length 个 额度
      // 按排查次数数量扣费 DiligenceHistoryQuantity 1 次 matchItems.length 个额度
      // 每日尽调上限校验 DiligenceDailyQuantity  1次 matchItems.length 个额度
      expect(bundleCounter.increase).toBeCalledTimes(4);
      // 失败退回排查额度 DiligenceCompanyQuantity 1 次 matchItems.length - paidCount 个 额度
      expect(bundleCounter.decrease).toBeCalledTimes(1);
      expect(originalStock1.stock).toEqual(originalStock.stock - 1 - matchItems.length * 3 + (matchItems.length - paidCount));

      // 验证spy调用
      expect(diligenceQueueSpy).toHaveBeenCalled();
      expect(jobMonitorQueueSpy).toHaveBeenCalled();
    } finally {
      // 恢复spy
      diligenceQueueSpy.mockRestore();
      jobMonitorQueueSpy.mockRestore();
      getRiskListForBatchJobSpy.mockRestore();
      userServiceSpy.mockRestore();
    }
  });

  it.skip('创建一个批量排查任务，正常执行完成，并且生成es快照kys_snapshot，和analysis_snapshot', async () => {
    setupBasicMocks();
    // mock 消费生成快照消息
    jest.spyOn(snapshotService.snapshotBatchQueue, 'sendMessageV2').mockImplementation((msg) => {
      return snapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.BatchDiligence);
    });
    const companyData = [
      {
        companyId: '6c7108d628fc21216e5bc48bb035b61c',
        companyName: '上海米哈游网络科技股份有限公司',
      },
      {
        companyId: '4e3fb77a20a0f7b777e6cd70969101f3',
        companyName: '江西省保灵动物保健品有限公司',
      },
    ];
    const batchEntity = await batchService.createBatchDiligenceTask(testUser, companyData, BatchBusinessTypeEnums.Diligence_File);

    // 4. 等待批量任务完成并验证状态
    const currentBatch = await BatchTestUtils.waitForBatchCompletion(batchRepo.manager, batchEntity.batchId);
    expect(currentBatch.status).toBe(BatchStatusEnums.Done);

    const batchDiligences = await batchDiligenceRepo.find({ where: { batchId: batchEntity.batchId } });
    expect(batchDiligences.length).toBe(companyData.length);

    const diligence = batchDiligences[0];
    const diligenceHistory = await batchDiligenceRepo.manager.findOne<DiligenceHistoryEntity>(DiligenceHistoryEntity, {
      where: { id: diligence.diligenceId },
    });
    expect(diligenceHistory).toBeDefined();
    expect(diligenceHistory.snapshotId).toBeDefined();
    expect(diligenceHistory.snapshotDetails).toBeDefined();
  });
});
