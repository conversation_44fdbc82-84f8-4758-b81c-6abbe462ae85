import { v4 } from 'uuid';
import { md5 } from '@kezhaozhao/qcc-utils/dist/qichacha/qichacha.util';

export const getUniqueJobResultKey = (batchId: number, jobId?: number, prefix?: string): string => {
  let key = `${batchId}-${jobId}_${v4()}`;
  if (prefix) {
    key = `${batchId}-${jobId}_prefix_${prefix}`;
  }
  return md5(key);
};

/**
 * batch的状态会通过分布式锁进行控制，这里获取锁的key
 * @param batchId
 */
export const getBatchProcessLockKey = (batchId: number): string => {
  return `batch-process-lock-${batchId}`;
};
