import { Injectable } from '@nestjs/common';
import { INotification, NotificationOptions } from './notification.interface';
import { EmailNotificationProvider } from './providers/email.notification';
import { SettingsService } from '../../../settings/settings.service';
import { ConfigurationTypeEnums } from '../../../../libs/model/settings/ConfigurationTypeEnums';
import { FileExportTemplate } from '../../file.parser/file.export.template';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { GroupDimensionVersionEnums } from '../../../../libs/model/diligence/pojo/dimension/group/GroupDimensionVersionEnums';
import { AnalyzeDiligenceContext, EmailContext } from '../../../../libs/model/export/ExcelItemData';
import { DiligenceAnalyzeService } from '../../../diligence/analyze/diligence.analyze.service';
import * as moment from 'moment';
import { DATE_TIME_FORMAT } from '../../../../libs/constants/common';
import { QccLogger } from '@kezhaozhao/qcc-logger';

@Injectable()
export class NotificationService {
  private readonly logger = QccLogger.getLogger(NotificationOptions.name);
  private providers: Map<string, INotification<EmailContext>>;

  constructor(
    public settingService: SettingsService,
    private emailProvider: EmailNotificationProvider,
    private diligenceAnalyzeService: DiligenceAnalyzeService,
  ) {
    this.providers = new Map([['email', emailProvider]]);
  }

  async sendNotification(orgId: number, batchId: number): Promise<void> {
    const provider = this.providers.get('email');
    if (!provider) {
      throw new Error('Notification provider not found');
    }
    //构建邮件内容
    // 1. 获取风控巡检配置
    const config = await this.settingService.getConfiguration(orgId, ConfigurationTypeEnums.diligence_analyze);

    // 2. 如果邮件通知设置打开，则发送邮件通知
    if (config.on && config?.pushSetting?.mailEnable && config?.pushSetting?.mailAddress) {
      this.logger.info(`Start to send notification for batch ${batchId}, mail address: ${config.pushSetting.mailAddress}`);
      const { data: analyzeRecordData, currentBatchInfo, previousBatchInfo } = await this.diligenceAnalyzeService.getAllAnalyzeData(batchId);
      if (!analyzeRecordData?.length) return;
      const context = new AnalyzeDiligenceContext();
      context.subject = `风险巡检-巡检结果推送${moment(new Date()).format(DATE_TIME_FORMAT)}`;
      context.companyCount = analyzeRecordData.length;
      context.diligenceList = analyzeRecordData;
      context.riskLevelIncreasedCount = analyzeRecordData.filter((f) => f.customerRiskLevel > f.previousCustomerRiskLevel)?.length;
      context.riskLevelDecreasedCount = analyzeRecordData.filter((f) => f.customerRiskLevel < f.previousCustomerRiskLevel)?.length;
      context.riskDimensionHitIncreasedCount = analyzeRecordData.filter((f) => f.changes.filter((c) => c.increasedValue)?.length)?.length;
      context.latestTime = moment(currentBatchInfo.createDate).format(DATE_TIME_FORMAT);
      context.compareTime = previousBatchInfo?.createDate ? moment(previousBatchInfo.createDate).format(DATE_TIME_FORMAT) : '-';
      const option = Object.assign(new NotificationOptions(), {
        to: config?.pushSetting.mailAddress,
        subject: '风险巡检-巡检结果推送',
        template: 'customer_analyze',
        context,
        excelTemplate: FileExportTemplate[BatchBusinessTypeEnums.Analyze_Record_Export][GroupDimensionVersionEnums.V1][0],
      });
      await provider.send(option);
      this.logger.info(`Notification for batch ${batchId} sent successfully`);
    }
  }
}
