import { EmailContext } from '../../../../libs/model/export/ExcelItemData';
import { ExcelParserSettingPO } from '../../../../libs/model/batch/po/parse/ExcelParserSettingPO';

export class NotificationOptions<T extends EmailContext> {
  to: string;
  subject: string;
  template: string;
  context?: T;
  excelTemplate?: ExcelParserSettingPO;
}

export interface INotification<T extends EmailContext> {
  send(option: NotificationOptions<T>): Promise<void>;
}
