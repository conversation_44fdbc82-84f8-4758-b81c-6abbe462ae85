import { Injectable } from '@nestjs/common';
import { INotification, NotificationOptions } from '../notification.interface';
import { MailerService } from '@kezhaozhao/nest-mailer';
import { ExcelHelper } from '../../../../utils/excel/excel.helper';
import { AnalyzeDiligenceContext, EmailContext } from '../../../../../libs/model/export/ExcelItemData';

@Injectable()
export class EmailNotificationProvider implements INotification<EmailContext> {
  constructor(private readonly mailerService: MailerService) {}

  async send(options: NotificationOptions<AnalyzeDiligenceContext>): Promise<void> {
    try {
      const riskFileBuffer = await ExcelHelper.generateFileBuffer(options.subject, options.excelTemplate, options.context.diligenceList);
      const riskAttachmentData = riskFileBuffer.toString('base64');
      await this.mailerService.sendMail({
        ...options,
        attachments: [
          {
            filename: `${options.subject}.xlsx`,
            content: riskAttachmentData,
            encoding: 'base64',
            contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // 设置xlsx文件的内容类型
          },
        ],
      });
    } catch (error) {
      // 记录错误但不抛出，避免影响主流程
      console.error('Failed to send email notification:', error);
    }
  }
}
