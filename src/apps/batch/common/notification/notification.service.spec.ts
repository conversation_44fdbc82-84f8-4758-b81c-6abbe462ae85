import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../../../app/app.test.module';
import { BatchModule } from '../../batch.module';
import { BundleTestUtils } from '../../../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { NotificationService } from './notification.service';
import { ConfigurationDiligenceAnalyze } from '../../../../libs/model/settings/ConfigurationModel';

jest.setTimeout(60 * 1000);
describe('debug-NotificationService', () => {
  let notificationService: NotificationService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();
    BundleTestUtils.spy(module.get(BundleHelperService));

    notificationService = module.get<NotificationService>(NotificationService);
  });

  it.skip('debug- batchId should send notification', async () => {
    jest.spyOn(notificationService.settingService, 'getConfiguration').mockReturnValue(
      Promise.resolve(
        Object.assign(new ConfigurationDiligenceAnalyze(), {
          on: true,
          pushSetting: {
            mailEnable: true,
            mailAddress: '<EMAIL>',
          },
        }),
      ),
    );
    const batchId = 56792;
    const orgId = 1001652;
    await notificationService.sendNotification(orgId, batchId);
  });
});
