/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { tmpdir } from 'os';
import * as Excel from 'exceljs';
// import * as fs from 'fs';
// import { createReadStream } from 'fs';
import * as Bluebird from 'bluebird';
// import { OssService } from '@kezhaozhao/qcc-utils';
import { ConfigService } from 'libs/config/config.service';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchResultEntity } from 'libs/entities/BatchResultEntity';
import { Repository } from 'typeorm';
import { BatchJobResultTypeEnums, GetBatchJobResultTypeEnumsName } from 'libs/enums/batch/BatchJobResultTypeEnums';
import { DiligenceFileParser } from '../file.parser/DiligenceFileParser';
import { PersonImportFileParser } from '../file.parser/PersonImportFileParser';
import { InnerBlacklistImportFileParser } from '../file.parser/InnerBlacklistImportFileParser';
import { CustomerImportFileParser } from '../file.parser/CustomerImportFileParser';
import { ExcelParserSettingPO } from 'libs/model/batch/po/parse/ExcelParserSettingPO';
import Worksheet from 'exceljs/index';
import * as moment from 'moment';
import { DurationConstants } from '../file.parser/file.export.template';
import { v1 as uuidv1 } from 'uuid';
import { FieldParseErrorTypeEnums } from '../../../libs/enums/batch/FieldParseErrorTypeEnums';
import { BiddingDiligenceFileParser } from '../file.parser/BiddingDiligenceFileParser';
import { MonitorImportFileParser } from '../file.parser/MonitorImportFileParser';
import { CustomerImportExcelRecord } from '../../../libs/model/batch/po/parse/ParsedRecordBase';
import { SpecificFileParser } from '../file.parser/SpecificFileParser';
import MyOssService from '../../basic/my-oss.service';

@Injectable()
export class BatchResultExportService {
  private readonly logger: Logger = QccLogger.getLogger(BatchResultExportService.name);
  private readonly dataFormat = 'YYYY/MM/DD';

  constructor(
    private configService: ConfigService,
    private readonly myOssService: MyOssService,
    private readonly diligenceFileParser: DiligenceFileParser,
    private readonly customerFileParser: CustomerImportFileParser,
    private readonly personFileParser: PersonImportFileParser,
    private readonly innerBlacklistImportFileParser: InnerBlacklistImportFileParser,
    private readonly biddingDiligenceFileParser: BiddingDiligenceFileParser,
    private readonly monitorImportFileParser: MonitorImportFileParser,
    private readonly specificFileParser: SpecificFileParser,
    @InjectRepository(BatchResultEntity) private readonly batchResultRepo: Repository<BatchResultEntity>,
  ) {}

  // 获取excel对象
  public async getExcel(filepath: string, sheetName: string, excelColumns: object) {
    const options = {
      filename: filepath,
      useStyles: true,
      useSharedStrings: true,
    };
    const workbook = new Excel.stream.xlsx.WorkbookWriter(options);
    const worksheet = workbook.addWorksheet(sheetName, {
      views: [
        {
          state: 'frozen',
          xSplit: 1,
          ySplit: 1,
        },
      ],
    });
    const displayColumns: any = [];
    Object.keys(excelColumns).forEach((field) => {
      const column = excelColumns[field];
      if (column) {
        displayColumns.push({
          key: field,
          header: column.header,
          width: column.width,
        });
      }
    });

    worksheet.columns = displayColumns;
    for (let i = 1; i <= displayColumns.length; i++) {
      worksheet.getColumn(i).alignment = {
        wrapText: true,
        vertical: 'middle',
        horizontal: 'center',
      };
    }
    const header = worksheet.getRow(1);
    header.font = {
      color: { argb: 'FFFFFFFF' },
      bold: true,
      // size: 14,
    };
    header.height = 22;
    header.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF228B22' },
      bgColor: { argb: 'FF228B22' },
    };
    return {
      workbook,
      worksheet,
    };
  }

  //获取多sheet excel对象
  public async getExcelByTemplate(filepath: string, excelSettings: ExcelParserSettingPO[]) {
    const workbook = new Excel.Workbook();
    const worksheets: Worksheet[] = [];
    excelSettings?.forEach((excelSetting) => {
      const sheetName = excelSetting.sheetName;
      const excelColumns = excelSetting.exportExcelColumns;
      const excelColumnsStartLine = excelSetting.excelColumnsStartLine || 1;
      const worksheet = workbook.addWorksheet(sheetName, {
        views: [
          {
            state: 'frozen',
            xSplit: 1,
            ySplit: 1,
          },
        ],
      });
      //worksheet.addRow(excelSetting.templateTitle);
      worksheet.insertRow(excelColumnsStartLine, excelSetting.templateTitle);
      Object.keys(excelColumns).forEach((field, index) => {
        const column = excelColumns[field];
        const columnIndexStr = this.getColumnIndexStr(index);
        const columnTemp = worksheet.getColumn(columnIndexStr);
        if (column) {
          columnTemp.width = column.width;
          columnTemp.key = field;
          // columnTemp.header = column.header;
          columnTemp.alignment = {
            wrapText: true,
            vertical: 'middle',
            horizontal: 'left',
          };
        }
      });
      if (excelSetting?.headerDescription) {
        //按开始行，开始列，结束行，结束列合并
        // worksheet.mergeCells(`${String.fromCharCode(65)}1:${String.fromCharCode(64 + excelSetting.templateTitle?.length)}1`);
        worksheet.mergeCells(`${String.fromCharCode(65)}1:${this.getColumnIndexStr(excelSetting.templateTitle?.length - 1)}1`);
        const titleRow = worksheet.getRow(1);
        titleRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFC0C0C0' },
          bgColor: { argb: 'FFC0C0C0' },
        };
        // 设置声明行的数据和样式
        titleRow.getCell(1).value = excelSetting?.headerDescription;
        // 合并单元格并居中展示声明行，默认声明行为第一行
        titleRow.getCell(1).alignment = { horizontal: 'left', vertical: 'middle' };
      }
      if (excelSetting?.tableTitle) {
        worksheet.mergeCells(
          `${String.fromCharCode(65)}${excelSetting.tableTitleLine}:${this.getColumnIndexStr(excelSetting.templateTitle?.length - 1)}${
            excelSetting.tableTitleLine
          }`,
        );
        const tableTitle = worksheet.getRow(excelSetting.tableTitleLine);
        // 设置表格标题行的数据和样式
        tableTitle.getCell(1).value = excelSetting?.tableTitle;
        // 合并单元格并居中展示标题行，默认表格标题行为第二行
        tableTitle.getCell(1).alignment = { horizontal: 'center', vertical: 'middle' };
        tableTitle.getCell(1).font = { bold: true, size: 14 };
      }
      const header = worksheet.getRow(excelColumnsStartLine);
      header.font = {
        color: { argb: 'FFFFFFFF' },
        bold: true,
        // size: 14,
      };
      header.height = 22;
      header.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF228B22' },
        bgColor: { argb: 'FF228B22' },
      };
      worksheets.push(worksheet);
    });
    return {
      workbook,
      worksheets,
    };
  }

  /**
   * 并上传到oss 返回oss下载地址
   * @param filepath
   * @param fileName
   * @param displayName
   * @returns
   */
  public async putFileToOss(filepath: string, fileName: string, displayName?: string) {
    try {
      const ossObject = this.configService.getOssObject(`batch_import_error_result`, fileName);
      const options: any = {};
      if (displayName) {
        const downloadName = encodeURIComponent(displayName);
        options.headers = {
          'Content-Disposition': `attachment;filename=${downloadName};filename*=UTF-8''${downloadName}`,
        };
      }
      const result = await this.myOssService.putSteam(ossObject, filepath, options);
      return result;
    } catch (e) {
      this.logger.error('oss error', e);
    }
  }

  // 获取导出数据并生成excel
  public async generateResultFile(batchId: number, businessType: BatchBusinessTypeEnums) {
    const results: BatchResultEntity[] = await this.batchResultRepo.find({ batchId });
    if (!results?.length) {
      this.logger.info('generateResultFile no result info for batchId: ', batchId);
      return '';
    }
    const fileName = `batch_import_result_info_${batchId}_${uuidv1()}.xlsx`;
    const filepath = `${tmpdir()}/${fileName}`;

    this.logger.info(`generateResultFile batchId: ${batchId} fileName ${fileName}`);
    try {
      let excelParserSetting: ExcelParserSettingPO;
      switch (businessType) {
        case BatchBusinessTypeEnums.Customer_ID: {
          excelParserSetting = this.diligenceFileParser.getParseSetting();
          excelParserSetting.sheetName = '合作伙伴-文本输入';
          break;
        }
        case BatchBusinessTypeEnums.Diligence_Customer: {
          excelParserSetting = this.diligenceFileParser.getParseSetting();
          excelParserSetting.sheetName = '批量排查-从第三方列表中选择';
          break;
        }
        case BatchBusinessTypeEnums.Diligence_ID: {
          excelParserSetting = this.diligenceFileParser.getParseSetting();
          excelParserSetting.sheetName = '批量排查-输入框手动填写';
          break;
        }
        case BatchBusinessTypeEnums.Diligence_Customer_Analyze: {
          excelParserSetting = this.diligenceFileParser.getParseSetting();
          excelParserSetting.sheetName = '批量排查-全部第三方-全量风险排查巡检';
          break;
        }
        case BatchBusinessTypeEnums.Diligence_File: {
          excelParserSetting = this.diligenceFileParser.getParseSetting();
          break;
        }
        case BatchBusinessTypeEnums.Customer_File: {
          excelParserSetting = this.customerFileParser.getParseSetting();
          await Bluebird.map(results, (result: BatchResultEntity) => {
            const recordItem = result?.resultInfo as CustomerImportExcelRecord;
            if (result?.resultInfo) {
              result.resultInfo['startDate'] = recordItem?.startDate ? moment(recordItem.startDate).format(this.dataFormat) : '';
              result.resultInfo['endDate'] = recordItem?.endDate ? moment(recordItem.endDate).format(this.dataFormat) : '';
              result.resultInfo['label'] = recordItem?.label?.join(',');
              result.resultInfo['departmentNames'] = recordItem?.departmentNames?.join(',');
            }
          });
          break;
        }
        case BatchBusinessTypeEnums.Person_File: {
          //人员导入
          excelParserSetting = this.personFileParser.getParseSetting();
          await Bluebird.map(results, (result) => {
            if (result?.resultInfo) {
              result.resultInfo['personNo'] = result.resultInfo['personNo']?.split('_')?.[0];
            }
          });
          break;
        }
        case BatchBusinessTypeEnums.InnerBlacklist_File: {
          //内部黑名单导入
          excelParserSetting = this.innerBlacklistImportFileParser.getParseSetting();
          await Bluebird.map(results, (result) => {
            if (result?.resultInfo) {
              result.resultInfo['duration'] = result.resultInfo['duration'] !== null ? DurationConstants[result.resultInfo['duration']] : '';
              result.resultInfo['joinDate'] = result.resultInfo['joinDate'] !== null ? moment(result.resultInfo['joinDate']).format(this.dataFormat) : '';
              result.resultInfo['departmentNames'] = result?.resultInfo?.departmentNames?.join(',');
            }
          });
          break;
        }
        case BatchBusinessTypeEnums.Bidding_Diligence_File: {
          //批量招标排查导入
          excelParserSetting = this.biddingDiligenceFileParser.getParseSetting();
          await Bluebird.map(results, (result) => {
            if (result?.resultInfo) {
              result.resultInfo['companyNames'] = result.resultInfo?.['companyNames']?.join(',');
            }
          });
          break;
        }
        case BatchBusinessTypeEnums.Monitor_File: {
          //合作监控监控列表导入
          excelParserSetting = this.monitorImportFileParser.getParseSetting();
          await Bluebird.map(results, (result) => {
            if (result?.resultInfo) {
              result.resultInfo['companyNames'] = result.resultInfo?.['companyNames']?.join(',');
            }
          });
          break;
        }
        case BatchBusinessTypeEnums.Specific_Diligence_File: {
          excelParserSetting = this.specificFileParser.getParseSetting();
          await Bluebird.map(results, (result) => {
            if (result?.resultInfo) {
              result.resultInfo['companyNames'] = result.resultInfo?.['companyNames']?.join(',');
            }
          });
          break;
        }
        default: {
          const msg = `generateResultFile unknown batch business type: ${businessType}`;
          this.logger.warn(msg);
          return '';
        }
      }

      const { workbook, worksheet } = await this.getExcel(filepath, excelParserSetting.sheetName, excelParserSetting.exportExcelColumns);
      await Bluebird.map(results, (result) => {
        let resultDes = '';
        if (result.resultType == BatchJobResultTypeEnums.FAILED_VERIFICATION) {
          resultDes = result.comment;
        } else if (result.comment === FieldParseErrorTypeEnums.Required) {
          resultDes = GetBatchJobResultTypeEnumsName(BatchJobResultTypeEnums.COLUMN_REQUIRED);
        } else if (result.comment === FieldParseErrorTypeEnums.Unmatched_Format) {
          resultDes = GetBatchJobResultTypeEnumsName(BatchJobResultTypeEnums.FAILED_VERIFICATION);
        } else {
          resultDes = GetBatchJobResultTypeEnumsName(result.resultType);
        }

        if (result?.resultInfo) {
          worksheet.addRow({ ...result.resultInfo, resultDes }).commit();
        }
      });
      worksheet.commit();
      await workbook.commit();
      await Bluebird.delay(200);

      const displayName = `${excelParserSetting.sheetName}_结果导出.xlsx`;
      // await unlinkSync(filepath);
      return await this.putFileToOss(filepath, fileName, displayName);
    } catch (error) {
      this.logger.error(`generateResultFile error batchId: ${batchId} error ${error}`);
      this.logger.error(error);
      throw error;
    }
  }

  /**
   *
   * @param worksheet
   * @param startRowIndex:合并开始行
   * @param startColIndex:合并开始列
   * @param endColIndex:合并结束列
   * @private
   */
  public mergeCell(worksheet: Worksheet, startRowIndex: number, startColIndex: number, endColIndex: number) {
    // 遍历工作表的列，从startColIndex 开始合并列开始
    //实际行数
    const actualRowCount = worksheet.actualRowCount;
    for (let colIndex = startColIndex; colIndex <= endColIndex; colIndex++) {
      const currentCol = worksheet.getColumn(colIndex);
      let currentVal = worksheet.getCell(startRowIndex, colIndex)?.value?.toString();
      let preRowIndex = startRowIndex;

      currentCol.eachCell({ includeEmpty: true }, (cell, rowNumber) => {
        if (rowNumber < startRowIndex) {
          return;
        }
        const cellValue = cell?.value?.toString();
        if (cellValue !== currentVal && cellValue !== currentCol.header) {
          if (preRowIndex !== rowNumber - 1) {
            const colLetter = this.getExcelColumnLetter(colIndex);
            const mergeRange = `${colLetter}${preRowIndex}:${colLetter}${rowNumber - 1}`;
            worksheet.mergeCells(mergeRange);
          }
          preRowIndex = rowNumber;
          currentVal = cellValue;
        }
      });

      // 处理列的最后一个合并范围
      if (preRowIndex !== actualRowCount) {
        const colLetter = this.getExcelColumnLetter(colIndex);
        const mergeRange = `${colLetter}${preRowIndex}:${colLetter}${actualRowCount}`;
        worksheet.mergeCells(mergeRange);
      }
    }
  }

  private getExcelColumnLetter(colIndex: number): string {
    let letter = '';
    while (colIndex > 0) {
      const remainder = (colIndex - 1) % 26;
      letter = String.fromCharCode(65 + remainder) + letter;
      colIndex = Math.floor((colIndex - 1) / 26);
    }
    return letter;
  }

  private getColumnIndexStr(index: number) {
    const columnLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (index < 0) {
      return null;
    }
    let result = '';
    while (index >= 0) {
      result = columnLetters[index % 26] + result;
      index = Math.floor(index / 26) - 1;
    }
    return result;
  }
}
