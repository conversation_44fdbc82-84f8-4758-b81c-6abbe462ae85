import { BadRequestException, Injectable } from '@nestjs/common';
import { RoverUser } from '../../../../libs/model/common';
import { SearchBiddingBatchResultRequest } from '../../../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { chain, cloneDeep } from 'lodash';
import { getTenderDimensionKeyName, TenderDimensions, TenderSettings } from '../../../../libs/constants/tender.dimension.constants';
import { DiligenceTenderHistoryEntity } from '../../../../libs/entities/DiligenceTenderHistoryEntity';
import * as Bluebird from 'bluebird';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import { BatchTenderDiligenceDetailEntity } from '../../../../libs/entities/BatchTenderDiligenceDetailEntity';
import { DimensionLevel2Enums } from '../../../../libs/enums/diligence/DimensionLevel2Enums';
import { BiddingCommonService } from '../../../bidding/service/bidding.common.service';
import { BiddingDetailResponse } from '../../model/BiddingDetailResponse';
import { BiddingStaticPO } from '../../model/BiddingStaticPO';
import { SearchDiligenceBiddingRequest } from '../../../../libs/model/bidding/SearchDiligenceBiddingRequest';
import { TenderDimensionDetailPO } from '../../model/TenderDimensionDetailPO';
import { HitDetailsBidBaseQueryParams } from '../../../../libs/model/diligence/pojo/req&res/details/request/HitDetailsBidBaseQueryParams';
import { JointBiddingAnalysisModel } from '../../../../libs/model/tender/JointBiddingAnalysisModel';
import { BiddingDiligenceHelper } from '../../../bidding/helper/bidding.diligence.helper';
import { DimensionTypeEnums } from '../../../../libs/enums/diligence/DimensionTypeEnums';
import { mergeRelationPaths, processRelationsDataPipeline, processRelationsDataPipeline2 } from '../../../utils/excel/excel-table.util';
import { DimensionLevel1Enums } from '../../../../libs/enums/diligence/DimensionLevel1Enums';
import * as moment from 'moment/moment';
import { DATE_TIME_FORMAT } from '../../../../libs/constants/common';

@Injectable()
export class BatchBiddingHelper {
  constructor(
    // @InjectEntityManager() private readonly entityManager: EntityManager,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchTenderDiligenceDetailEntity) private readonly batchDiligenceDetailRepo: Repository<BatchTenderDiligenceDetailEntity>,
    private readonly biddingCommonService: BiddingCommonService,
    private readonly biddingDiligenceHelper: BiddingDiligenceHelper,
  ) {}

  public handleTenderHistory4Export(tenderRecordList: DiligenceTenderHistoryEntity[]) {
    const TenderResultMap = { '0': '通过', '1': '审慎核实', '2': '不通过', '-1': '稍后核定' };

    return tenderRecordList.map((record) => {
      const result = record.status == 2 ? '排查失败' : TenderResultMap[record?.remarkResult ?? record?.result];

      const dimensionResults = Object.keys(getTenderDimensionKeyName()).reduce((acc, dimensionKey) => {
        const dimensionHit = record.details?.dimensionHitsDetails?.find((f) => f.key === dimensionKey);
        acc[dimensionKey] = dimensionHit ? TenderResultMap[dimensionHit.level] : '-';
        return acc;
      }, {});

      return {
        ...record,
        result,
        ...dimensionResults,
        companyList: record.companyList.map((c) => c.companyName).join('\n'),
        editor: record?.editor?.name,
        createDate: moment(record.createDate).format(DATE_TIME_FORMAT),
      };
    });
  }

  async getBiddingStatisticsV2(body: SearchBiddingBatchResultRequest): Promise<BiddingStaticPO[]> {
    if (!body.batchId) {
      throw new BadRequestException();
    }
    const qb = this.getBatchBiddingQueryBuilder(body);
    qb.select('COUNT(distinct(detail.diligenceId))', 'totalHits');
    const [group, groupDim, allData, relationTypes] = await Bluebird.all([
      qb.clone().addSelect('detail.groupKey', 'key').andWhere('detail.groupHits > 0').groupBy('detail.groupKey').getRawMany(),
      qb.clone().addSelect('detail.dimKey', 'key').andWhere('detail.dimHits > 0').groupBy('detail.dimKey').getRawMany(),
      qb.clone().addSelect('detail.diligenceId', 'diligenceId').groupBy('detail.diligenceId').getRawMany(),
      qb.clone().addSelect('detail.relationType', 'key').andWhere('detail.relationHits > 0').groupBy('detail.relationType').getRawMany(),
    ]);
    const defaultConfig = cloneDeep(TenderSettings).sort((a, b) => a.sort - b.sort);
    const result = [];
    result.push({ key: 'all', totalHits: allData.length });
    defaultConfig.forEach((cGroup) => {
      if (cGroup.key != DimensionLevel2Enums.BiddingCompanyCountSetting) {
        const groupCount = group.find((x) => x.key == cGroup.key)?.totalHits || 0;
        const aggGroup = { key: cGroup.key, totalHits: groupCount };
        if (cGroup.subDimensionList?.length) {
          aggGroup['subDimension'] = [{ key: 'all', totalHits: groupCount }];
          cGroup.subDimensionList.forEach((sub) => {
            const subCount = groupDim.find((x) => x.key == sub.key)?.totalHits || 0;
            if (sub.key == DimensionLevel2Enums.BiddingCompanyRelationship2 && relationTypes?.length > 0) {
              relationTypes.unshift({ key: 'all', totalHits: subCount });
              aggGroup['subDimension'].push({ key: sub.key, totalHits: subCount, relationTypes });
            } else {
              aggGroup['subDimension'].push({ key: sub.key, totalHits: subCount });
            }
          });
        } else if (cGroup.types?.length) {
          aggGroup['subDimension'] = [{ key: 'all', totalHits: groupCount }];
          cGroup.types.forEach((sub) => {
            const subCount = groupDim.find((x) => x.key == sub.key)?.totalHits || 0;
            aggGroup['subDimension'].push({ key: sub.key, totalHits: subCount });
          });
        }
        result.push(aggGroup);
      }
    });
    return result;
  }

  async searchBiddingDetailV2(user: RoverUser, body: SearchBiddingBatchResultRequest): Promise<BiddingDetailResponse> {
    const { currentOrg: orgId } = user;
    const { batchId, pageSize, pageIndex, diligenceIds } = body;
    const response = new BiddingDetailResponse();
    const batch = await this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .select(['batch', 'creator'])
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchId = :batchId', { batchId })
      .getOne();

    const qb = this.getBatchBiddingQueryBuilder(body);
    if (diligenceIds?.length) {
      qb.andWhere('detail.diligenceId in (:...diligenceIds)', { diligenceIds });
    }
    const data = await qb
      .select('detail.diligenceId', 'diligenceId')
      .addSelect('COUNT(distinct(detail.diligenceId))', 'count')
      .groupBy('detail.diligenceId')
      .getRawMany();

    let entitys: DiligenceTenderHistoryEntity[] = [];
    if (data?.length) {
      const ids = chain(data.map((x) => x.diligenceId))
        .sortBy()
        .chunk(pageSize)
        .value()[pageIndex - 1];
      if (ids?.length) {
        entitys = await this.biddingCommonService.searchDetail(orgId, ids);
      }
    }
    return Object.assign(response, {
      pageIndex: pageIndex,
      pageSize: pageSize,
      total: data.length,
      data: entitys,
      createDate: batch?.createDate,
      editor: batch?.creator,
    });
  }

  private getBatchBiddingQueryBuilder(body: SearchBiddingBatchResultRequest) {
    const { batchId, searchKey, status, dimensionLevel1, dimensionLevel2, relationType, diligenceIds } = body;

    const qb = this.batchDiligenceDetailRepo
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.diligence', 'diligence')
      .select(['detail.id', 'detail.diligenceId', 'detail.groupKey', 'detail.dimKey', 'detail.groupHits', 'detail.dimHits'])
      .where('detail.batchId = :batchId', { batchId });

    if (searchKey) {
      qb.leftJoin('diligence.companyList', 'diligenceCompany');
      qb.andWhere('(diligence.project_no like :searchKey or diligence.project_name like :searchKey or diligenceCompany.company_name like :searchKey) ', {
        searchKey: `%${searchKey}%`,
      });
    }

    if (status?.length) {
      qb.andWhere('(diligence.remark_result in (:...status) or (diligence.remark_result is null and diligence.result in (:...status))) ', { status });
    }

    if (dimensionLevel1) {
      qb.andWhere('(detail.groupKey = :dimensionLevel1 and detail.groupHits > 0)', { dimensionLevel1 });
    }

    if (dimensionLevel2) {
      qb.andWhere('(detail.dimKey = :dimensionLevel2 and detail.dimHits > 0)', { dimensionLevel2 });
    }

    if (relationType) {
      qb.andWhere('(detail.relationType in (:...relationType) and detail.dimHits > 0)', { relationType });
    }
    if (diligenceIds?.length) {
      qb.andWhere('detail.diligenceId in (:...diligenceIds)', { diligenceIds });
    }

    return qb;
  }

  async getTenderDiligenceHistory(user: RoverUser, searchParam: SearchDiligenceBiddingRequest): Promise<DiligenceTenderHistoryEntity[]> {
    //该查询是分页查，这里是查询导出，所以就一次查 200条数据，直到全部查完
    const pageSize = searchParam.pageSize;
    let pageIndex = searchParam.pageIndex;
    const allResults: DiligenceTenderHistoryEntity[] = [];
    while (true) {
      const response = await this.biddingCommonService.searchV2(user, { ...searchParam, pageIndex, pageSize });
      if (!response) {
        return allResults;
      }
      allResults.push(...response.data);
      if (response.total <= pageIndex * pageSize) {
        break;
      }
      pageIndex += 1;
    }
    return allResults;
  }

  public async getAllTenderDetails(user: RoverUser, searchParam: SearchBiddingBatchResultRequest): Promise<DiligenceTenderHistoryEntity[]> {
    const pageSize = searchParam.pageSize;
    let pageIndex = searchParam.pageIndex;
    const tenderResults: DiligenceTenderHistoryEntity[] = [];
    while (true) {
      const response = await this.searchBiddingDetailV2(user, { ...searchParam, pageIndex, pageSize });
      if (!response) {
        return tenderResults;
      }
      tenderResults.push(...response.data);
      if (response.total <= pageIndex * pageSize) {
        break;
      }
      pageIndex += 1;
    }
    if (tenderResults.length) {
      await Bluebird.map(
        tenderResults,
        async (dbDiligence) => {
          const queryParams = Object.assign(new HitDetailsBidBaseQueryParams(), {
            keyNos: dbDiligence.companyList?.map((c) => c.companyId),
            pageIndex: 1,
            pageSize: 99, //查前99条与风险快照保持一致
            keyNoAndNames: dbDiligence.companyList?.map((c) =>
              Object.assign(new JointBiddingAnalysisModel(), {
                companyId: c.companyId,
                companyName: c.companyName,
              }),
            ),
          });
          await this.biddingDiligenceHelper.getRiskDetails(user.currentOrg, dbDiligence, queryParams);
        },
        { concurrency: 5 },
      );
    }
    return tenderResults;
  }

  /**
   * 获取招标排查的维度详情数据
   * @param searchParam
   * @param diligenceList
   */
  async getDimensionHitsDetails(
    searchParam: SearchBiddingBatchResultRequest,
    diligenceList: DiligenceTenderHistoryEntity[],
  ): Promise<Map<string, TenderDimensionDetailPO[]>> {
    const dimensionHitDetails: Map<string, TenderDimensionDetailPO[]> = new Map();
    const { dimensionLevel1, dimensionLevel2, relationType } = searchParam;
    if (dimensionLevel2) {
      this.setDimensionHitDetailsValues(dimensionLevel1, dimensionLevel2, diligenceList, dimensionHitDetails);
    }

    const subDimensionKeys = this.getSubDimensionKeys(dimensionLevel1);
    if (dimensionLevel1 && !dimensionLevel2) {
      //判断该dimensionLevel1是否都包含dimensionLevel2,包含需要遍历 dimensionLevel2
      if (subDimensionKeys?.length) {
        subDimensionKeys.forEach((subDimensionLevel2) => {
          this.setDimensionHitDetailsValues(dimensionLevel1, subDimensionLevel2, diligenceList, dimensionHitDetails);
        });
      } else {
        //说明该维度没有子维度
        diligenceList.forEach((item) => {
          const { projectNo, projectName } = item;
          const dimensionResults: any[] = [];
          const dimensionDetailPOList = [];
          const dim = item.details.dimensionHitsDetails.find((f) => f.key === dimensionLevel1 && f.totalHits > 0);
          if (dim) {
            if (dim.data?.Result) {
              dimensionResults.push(...dim.data.Result);
              dimensionDetailPOList.push(new TenderDimensionDetailPO(projectNo, projectName, dimensionResults));
              if (dimensionHitDetails.has(dimensionLevel1)) {
                dimensionHitDetails.get(dimensionLevel1).push(...dimensionDetailPOList);
              } else {
                dimensionHitDetails.set(dimensionLevel1, dimensionDetailPOList);
              }
            }
            // else if (dim.subDimension?.length) {
            //   dim.subDimension.forEach((sub) => {
            //     if (sub.totalHits > 0) {
            //       const subDimensionResults: any[] = [];
            //       if (Array.isArray(sub.data)) {
            //         subDimensionResults.push(...sub.data);
            //       } else {
            //         subDimensionResults.push(...sub.data.Result);
            //       }
            //       dimensionDetailPOList.push(new TenderDimensionDetailPO(projectNo, projectName, subDimensionResults));
            //       if (dimensionHitDetails.has(sub.key)) {
            //         dimensionHitDetails.get(sub.key).push(...dimensionDetailPOList);
            //       } else {
            //         dimensionHitDetails.set(sub.key, dimensionDetailPOList);
            //       }
            //     }
            //   });
            // }
          }
        });
      }
    }
    return dimensionHitDetails;
  }

  private setDimensionHitDetailsValues(
    dimensionLevel1: DimensionTypeEnums,
    dimensionLevel2: DimensionTypeEnums,
    diligenceList: DiligenceTenderHistoryEntity[],
    dimensionHitDetails: Map<string, TenderDimensionDetailPO[]>,
  ) {
    diligenceList.forEach((item) => {
      const { projectNo, projectName } = item;
      const dimensionDetailPOList: TenderDimensionDetailPO[] = [];

      // 查找一级维度
      const dim = item.details.dimensionHitsDetails.find((f) => f.key === dimensionLevel1 && f.totalHits > 0);
      if (!dim?.subDimension?.length) return;

      // 查找二级维度
      const subDim = dim.subDimension.find((sub) => sub.key === dimensionLevel2 && sub.totalHits > 0);
      if (!subDim) return;

      // 获取结果数据
      const dimensionResults = subDim.data?.Result || subDim.data || [];
      if (!dimensionResults.length) return;

      // 创建并添加维度详情
      if (dimensionLevel1 === DimensionLevel2Enums.BiddingCompanyRelation) {
        switch (dimensionLevel2) {
          case DimensionLevel2Enums.BiddingCompanyRelationship: {
            const companyRelations = processRelationsDataPipeline(dimensionResults);
            const mergedData = mergeRelationPaths(companyRelations);
            dimensionDetailPOList.push(new TenderDimensionDetailPO(projectNo, projectName, mergedData));
            break;
          }
          case DimensionLevel2Enums.BiddingCompanyRelationship2: {
            const companyRelations = processRelationsDataPipeline2(dimensionResults);
            const mergedData = mergeRelationPaths(companyRelations);
            dimensionDetailPOList.push(new TenderDimensionDetailPO(projectNo, projectName, mergedData));
            break;
          }
          default: {
            break;
          }
        }
      } else {
        dimensionDetailPOList.push(new TenderDimensionDetailPO(projectNo, projectName, dimensionResults));
      }

      // 更新维度结果
      if (!dimensionHitDetails.has(dimensionLevel2)) {
        dimensionHitDetails.set(dimensionLevel2, []);
      }
      dimensionHitDetails.get(dimensionLevel2).push(...dimensionDetailPOList);
    });
  }

  /**
   * 获取招标排查子维度
   * @param dimensionLevel1
   * @private
   */
  public getSubDimensionKeys(dimensionLevel1: DimensionTypeEnums) {
    const copyTenderDimensions = cloneDeep(TenderDimensions);
    const dimensionPO = copyTenderDimensions.items.find((f) => f.key === dimensionLevel1);
    if (dimensionPO?.key === DimensionLevel1Enums.Risk_InnerBlacklist) {
      //内部黑名单维度合并过，硬编码返回这两个维度
      return [DimensionLevel2Enums.DirectConnection, DimensionLevel2Enums.BlackListInvestigations];
    }
    if (dimensionPO?.subDimensionList) {
      return dimensionPO.subDimensionList.map((m) => m.key);
    } else {
      return null;
    }
  }
}
