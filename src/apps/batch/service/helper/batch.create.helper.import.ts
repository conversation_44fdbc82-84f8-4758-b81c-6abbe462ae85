import { BadRequestException, HttpException, Injectable } from '@nestjs/common';
import { RoverUser } from '../../../../libs/model/common';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { FileParseResult } from '../../../../libs/model/batch/po/parse/FileParseResult';
import { BatchConstants } from '../../common/batch.constants';
import { RoverExceptions } from '../../../../libs/exceptions/exceptionConstants';
import { BatchBaseHelper } from './batch.base.helper';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchMatchCompanyEntity } from '../../../../libs/entities/BatchMatchCompanyEntity';
import { Repository } from 'typeorm';
import { BatchMatchCompanyItemEntity } from '../../../../libs/entities/BatchMatchCompanyItemEntity';
import * as Bluebird from 'bluebird';
import { BatchBundleService } from '../batch.bundle.service';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchCreatorHelperBase } from './batch.creator.helper.base';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import { ParsedRecordBase } from '../../../../libs/model/batch/po/parse/ParsedRecordBase';

@Injectable()
export class BatchCreateHelperImport {
  private readonly logger = QccLogger.getLogger('BatchCustomerHelper');

  constructor(
    @InjectRepository(BatchMatchCompanyEntity) private readonly batchMatchCompanyRepo: Repository<BatchMatchCompanyEntity>,
    private readonly batchBaseHelperService: BatchBaseHelper,
    @InjectRepository(BatchMatchCompanyItemEntity) private readonly batchMatchCompanyItemRepo: Repository<BatchMatchCompanyItemEntity>,
    private readonly batchBundleService: BatchBundleService,
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
  ) {}

  async executeBatchImport(user: RoverUser, batchId: number, batchBusinessType: BatchBusinessTypeEnums, isUpdate = true): Promise<BatchEntity> {
    // 当前已有离线任务正在进行，请勿重复进行操作！
    await this.batchBaseHelperService.checkBatchCount(user, batchBusinessType);
    const batchMatchEntity = await this.batchMatchCompanyRepo.findOne({ id: batchId, orgId: user.currentOrg });
    if (!batchMatchEntity) {
      throw new BadRequestException('不存在的batch');
    }
    const batchItems = await this.batchMatchCompanyItemRepo
      .createQueryBuilder('item')
      .where('item.batchId = :batchId', { batchId })
      .andWhere('item.flag = 0')
      .andWhere('item.matchBy is not null')
      .select(['item.name', 'item.companyId', 'item.parsedItem'])
      .getMany();
    if (batchItems.length == 0) {
      throw new BadRequestException('不存在匹配成功的企业，无法添加到第三方列表');
    }
    try {
      const fileParseResult: FileParseResult = {
        succeedItems: batchItems
          .map((d) => {
            return { ...(d.parsedItem as ParsedRecordBase) };
          })
          .filter((d) => d),
        failedItems: [],
        isUpdate,
      };
      const jobItemSize = BatchConstants.getJobItemSize(batchBusinessType); //通过调整大小还控制 最终的job的数量（一个job对应一个mq message.handler）
      const batchEntity = await this.batchCreatorHelperService.createBatchEntity(
        user,
        fileParseResult,
        batchBusinessType,
        jobItemSize,
        batchMatchEntity.fileName,
        batchMatchEntity.originFile,
      );
      await Bluebird.all([this.batchMatchCompanyRepo.delete({ id: batchId }), this.batchMatchCompanyItemRepo.delete({ batchId })]);
      return batchEntity;
    } catch (e) {
      if (e instanceof HttpException) {
        throw e;
      }
      this.logger.error(e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.CreateFailed,
        internalMessage: e.message,
      });
    }
  }
}
