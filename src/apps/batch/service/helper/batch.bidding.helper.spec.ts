import { Test } from '@nestjs/testing';
import { RoverUser } from 'libs/model/common';
import { getRepository, Repository } from 'typeorm';
import { AppTestModule } from '../../../app/app.test.module';
import { BatchBiddingHelper } from './batch.bidding.helper';
import { SearchBiddingBatchResultRequest } from '../../../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { UserService } from '../../../user/user.service';
import * as Bluebird from 'bluebird';
import { BatchTenderDiligenceEntity } from '../../../../libs/entities/BatchTenderDiligenceEntity';
import { chunk, shuffle, sortBy } from 'lodash';
import { BiddingCommonService } from '../../../bidding/service/bidding.common.service';
import { BiddingDiligenceService } from '../../../bidding/service/bidding.diligence.service';
import { DimensionLevel1Enums } from '../../../../libs/enums/diligence/DimensionLevel1Enums';
import { BatchModule } from '../../batch.module';

// jest.setTimeout(60 * 1000);
describe.skip('debug-batch.bidding.helper', () => {
  let batchDiligenceRepo: Repository<BatchTenderDiligenceEntity>;
  let helper: BatchBiddingHelper;
  let biddingDiligenceService: BiddingDiligenceService;
  let biddingCommonService: BiddingCommonService;
  let userService: UserService;
  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();

    batchDiligenceRepo = getRepository(BatchTenderDiligenceEntity);
    helper = moduleFixture.get(BatchBiddingHelper);
    biddingDiligenceService = moduleFixture.get(BiddingDiligenceService);
    biddingCommonService = moduleFixture.get(BiddingCommonService);
    userService = moduleFixture.get(UserService);
  });

  afterAll(async () => {
    jest.restoreAllMocks();
    await batchDiligenceRepo.manager.connection.close();
  });

  it.skip('debug-getBiddingStatisticsV2', async () => {
    const result = await helper.getBiddingStatisticsV2(Object.assign(new SearchBiddingBatchResultRequest(), { batchId: 39661 }));
    expect(result.length).toBeGreaterThan(0);
  });

  it.skip('debug-searchBiddingDetailV2', async () => {
    const testUserId = 101230;
    const testOrgId = 1001652;
    const user: RoverUser = await userService.getRoverUser(testUserId, testOrgId);

    const result = await helper.searchBiddingDetailV2(
      user,
      Object.assign(new SearchBiddingBatchResultRequest(), {
        pageIndex: 1,
        pageSize: 10,
        batchId: '17000',
        dimensionLevel1: 'BiddingCompanyRelation',
        dimensionLevel2: '',
        status: [0, 1, 2, -1],
      }),
    );
    expect(result.total).toBeGreaterThan(0);
  });

  it.skip('debug-sort and paging', async () => {
    const pageIndex = 2;
    const pageSize = 5;

    const ids = shuffle([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]);
    const newIds = sortBy(ids);
    const chunkedArray = chunk(newIds, pageSize);
    const result = chunkedArray[pageIndex - 1];

    // const result2 = chain(ids).sortBy().chunk(pageSize).value()[pageIndex - 1];
    expect(result).toStrictEqual([6, 7, 8, 9, 10]);
  });

  it.skip('debug-saveTenderBatchDiligenceDetail', async () => {
    const testOrgId = 208;
    // const user: RoverUser = await userService.getRoverUser(testUserId, testOrgId);
    const batchId = 32448;
    const batchDiligences = await batchDiligenceRepo.find({ batchId });
    const diligences = await biddingCommonService.searchDetail(
      testOrgId,
      batchDiligences.map((bd) => bd.diligenceId),
    );
    let total = 0;
    await Bluebird.map(
      diligences,
      async (diligence) => {
        const count = await biddingDiligenceService.saveBatchDiligenceDetail(batchId, diligence);
        total += count;
        expect(count).toBeGreaterThanOrEqual(0);
      },
      { concurrency: 10 },
    );

    expect(total).toBeGreaterThan(0);
  });

  it.skip('debug saveTenderBatchDiligenceDetail', async () => {
    const diligenceId = 215199;
    const batchId = 32448;
    const orgId = 208;
    // const diligence = await tenderDiligenceRepo.findOne(diligenceId);

    const diligences = await biddingCommonService.searchDetail(orgId, [diligenceId]);
    const count = await biddingDiligenceService.saveBatchDiligenceDetail(batchId, diligences[0]);
    expect(count);
  });

  it.skip('debug-getSubDimensionKeys', async () => {
    const res = helper.getSubDimensionKeys(DimensionLevel1Enums.Risk_InnerBlacklist);
    expect(res.length).toBeGreaterThan(0);
  });
});
