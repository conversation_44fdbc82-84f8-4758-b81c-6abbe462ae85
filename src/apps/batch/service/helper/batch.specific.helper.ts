import { BadRequestException, Injectable } from '@nestjs/common';
import { SearchBiddingBatchResultRequest } from '../../../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { chain, cloneDeep } from 'lodash';
import * as Bluebird from 'bluebird';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import { Repository } from 'typeorm';
import { BatchSpecificDiligenceDetailEntity } from '../../../../libs/entities/BatchSpecificDiligenceDetailEntity';
import { SpecificDimensionSettings } from '../../../../libs/constants/specific.dimension.constant';
import { RoverUser } from '../../../../libs/model/common';
import { SpecificDetailResponse } from '../../model/BiddingDetailResponse';
import { SpecificInterestRecordEntity } from '../../../../libs/entities/SpecificInterestRecordEntity';
import { SpecificFacadeService } from '../../../specific/specific.facade.service';

@Injectable()
export class BatchSpecificHelper {
  constructor(
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchSpecificDiligenceDetailEntity) private readonly batchSpecificDetailRepo: Repository<BatchSpecificDiligenceDetailEntity>,
    private readonly specificFacadeService: SpecificFacadeService,
  ) {}

  async getSpecificStatistics(body: SearchBiddingBatchResultRequest) {
    if (!body.batchId) {
      throw new BadRequestException();
    }
    const qb = this.getBatchBiddingQueryBuilder(body);
    qb.select('COUNT(distinct(detail.diligenceId))', 'totalHits');
    const [
      group,
      groupDim,
      allData,
      // relationTypes
    ] = await Bluebird.all([
      qb.clone().addSelect('detail.groupKey', 'key').andWhere('detail.groupHits > 0').groupBy('detail.groupKey').getRawMany(),
      qb.clone().addSelect('detail.dimKey', 'key').andWhere('detail.dimHits > 0').groupBy('detail.dimKey').getRawMany(),
      qb.clone().addSelect('detail.diligenceId', 'diligenceId').groupBy('detail.diligenceId').getRawMany(),
      // qb.clone().addSelect('detail.relationType', 'key').andWhere('detail.relationHits > 0').groupBy('detail.relationType').getRawMany(),
    ]);
    const defaultConfig = cloneDeep(SpecificDimensionSettings);
    const result = [];
    result.push({ key: 'all', totalHits: allData.length });
    defaultConfig.forEach((cGroup) => {
      // if (cGroup.key != DimensionLevel2Enums.BiddingCompanyCountSetting) {
      const groupCount = group.find((x) => x.key == cGroup.key)?.totalHits || 0;
      const aggGroup = { key: cGroup.key, totalHits: groupCount };
      // if (cGroup.subDimensionList?.length) {
      //   aggGroup['subDimension'] = [{ key: 'all', totalHits: groupCount }];
      //   cGroup.subDimensionList.forEach((sub) => {
      //     const subCount = groupDim.find((x) => x.key == sub.key)?.totalHits || 0;
      //     if (sub.key == DimensionLevel2Enums.BiddingCompanyRelationship2 && relationTypes?.length > 0) {
      //       relationTypes.unshift({ key: 'all', totalHits: subCount });
      //       aggGroup['subDimension'].push({ key: sub.key, totalHits: subCount, relationTypes });
      //     } else {
      //       aggGroup['subDimension'].push({ key: sub.key, totalHits: subCount });
      //     }
      //   });
      // } else if (cGroup.types?.length) {
      aggGroup['subDimension'] = [{ key: 'all', totalHits: groupCount }];
      cGroup.types?.forEach((sub) => {
        const subCount = groupDim.find((x) => x.key == sub.key)?.totalHits || 0;
        aggGroup['subDimension'].push({ key: sub.key, totalHits: subCount });
      });
      aggGroup['level'] = cGroup.strategyModel.level;
      // }
      result.push(aggGroup);
      // }
    });
    return result;
  }

  private getBatchBiddingQueryBuilder(body: SearchBiddingBatchResultRequest) {
    const { batchId, searchKey, status, dimensionLevel1, dimensionLevel2, relationType, diligenceIds } = body;

    const qb = this.batchSpecificDetailRepo
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.diligence', 'diligence')
      .select(['detail.id', 'detail.diligenceId', 'detail.groupKey', 'detail.dimKey', 'detail.groupHits', 'detail.dimHits'])
      .where('detail.batchId = :batchId', { batchId });

    if (searchKey) {
      qb.leftJoin('diligence.companyList', 'diligenceCompany');
      qb.andWhere('(diligence.project_no like :searchKey or diligence.project_name like :searchKey or diligenceCompany.company_name like :searchKey) ', {
        searchKey: `%${searchKey}%`,
      });
    }

    if (status?.length) {
      qb.andWhere('(diligence.remark_result in (:...status) or (diligence.remark_result is null and diligence.result in (:...status))) ', { status });
    }

    if (dimensionLevel1) {
      qb.andWhere('(detail.groupKey = :dimensionLevel1 and detail.groupHits > 0)', { dimensionLevel1 });
    }

    if (dimensionLevel2) {
      qb.andWhere('(detail.dimKey = :dimensionLevel2 and detail.dimHits > 0)', { dimensionLevel2 });
    }

    if (relationType) {
      qb.andWhere('(detail.relationType in (:...relationType) and detail.dimHits > 0)', { relationType });
    }
    if (diligenceIds?.length) {
      qb.andWhere('detail.diligenceId in (:...diligenceIds)', { diligenceIds });
    }

    return qb;
  }

  async searchSpecificDetail(user: RoverUser, body: SearchBiddingBatchResultRequest) {
    const { currentOrg: orgId } = user;
    const { batchId, pageSize, pageIndex, diligenceIds } = body;
    const response = new SpecificDetailResponse();
    const batch = await this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .select(['batch', 'creator'])
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchId = :batchId', { batchId })
      .getOne();

    const qb = this.getBatchBiddingQueryBuilder(body);
    if (diligenceIds?.length) {
      qb.andWhere('detail.diligenceId in (:...diligenceIds)', { diligenceIds });
    }
    const data = await qb
      .select('detail.diligenceId', 'diligenceId')
      .addSelect('COUNT(distinct(detail.diligenceId))', 'count')
      .groupBy('detail.diligenceId')
      .getRawMany();

    let entitys: SpecificInterestRecordEntity[] = [];
    if (data?.length) {
      const ids = chain(data.map((x) => x.diligenceId))
        .sortBy()
        .chunk(pageSize)
        .value()[pageIndex - 1];
      if (ids?.length) {
        entitys = await this.specificFacadeService.searchDetail(orgId, ids);
      }
    }
    return Object.assign(response, {
      pageIndex: pageIndex,
      pageSize: pageSize,
      total: data.length,
      data: entitys,
      createDate: batch?.createDate,
      editor: batch?.creator,
    });
  }
}
