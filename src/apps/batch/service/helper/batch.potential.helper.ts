import { BadRequestException, HttpException, Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RoverUser } from '../../../../libs/model/common';
import { BatchCreatorHelperBase } from './batch.creator.helper.base';
import { BatchMatchCompanyEntity } from '../../../../libs/entities/BatchMatchCompanyEntity';
import { BatchMatchCompanyItemEntity } from '../../../../libs/entities/BatchMatchCompanyItemEntity';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchTypeEnums } from '../../../../libs/enums/batch/BatchTypeEnums';
import { BatchConstants } from '../../common/batch.constants';
import { FileParseResult } from '../../../../libs/model/batch/po/parse/FileParseResult';
import * as Bluebird from 'bluebird';
import { RoverExceptions } from '../../../../libs/exceptions/exceptionConstants';
import { BatchBaseHelper } from './batch.base.helper';
import { BatchPotentialDiligenceDetailEntity } from '../../../../libs/entities/BatchPotentialDiligenceDetailEntity';
import { SearchPotentialBatchResultRequest } from 'libs/model/batch/request/SearchPotentialBatchResultRequest';
import { PotentialDimensionSettings } from 'libs/constants/potential.dimension.constants';
import { cloneDeep } from 'lodash';
import { PotentialDetailResponse } from 'apps/batch/model/PotentialDetailResponse';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { BatchPotentialDiligenceEntity } from 'libs/entities/BatchPotentialDiligenceEntity';
import e from 'express';
import { PotentialDiligenceEntity } from 'libs/entities/PotentialDiligenceEntity';

@Injectable()
export class BatchPotentialHelper {
  private readonly logger: Logger = QccLogger.getLogger('BatchPotentialHelper');

  constructor(
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
    private readonly batchBaseHelperService: BatchBaseHelper,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchMatchCompanyEntity) private readonly batchMatchCompanyRepo: Repository<BatchMatchCompanyEntity>,
    @InjectRepository(BatchMatchCompanyItemEntity) private readonly batchMatchCompanyItemRepo: Repository<BatchMatchCompanyItemEntity>,
    @InjectRepository(BatchPotentialDiligenceDetailEntity) private readonly batchPotentialDetailRepo: Repository<BatchPotentialDiligenceDetailEntity>,
    @InjectRepository(BatchPotentialDiligenceEntity) private readonly batchPotentialDeligenceRepo: Repository<BatchPotentialDiligenceEntity>,
    @InjectRepository(PotentialDiligenceEntity) private readonly potentialDiligenceRepo: Repository<PotentialDiligenceEntity>,
  ) {}

  async searchPotentialDetail(user: any, body: SearchPotentialBatchResultRequest) {
    const { currentOrg: orgId } = user;
    const { batchId, pageSize, pageIndex } = body;
    const response = new PotentialDetailResponse();
    const batch = await this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .select(['batch', 'creator'])
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchId = :batchId', { batchId })
      .getOne();

    if (!batch || batch.orgId != orgId) {
      throw new BadRequestException();
    }
    // 先查符合条件的diligenceId
    // 再根据diligenceId查询detail
    const qb = this.getBatchPotentialQueryBuilder(body);
    qb.select('distinct(detail.diligenceId)', 'diligenceId');
    const diligenceIdsResult = await qb.getRawMany();
    const diligenceIdsList = diligenceIdsResult.map((x) => x.diligenceId);
    if (diligenceIdsList?.length) {
      const [data, total] = await this.potentialDiligenceRepo
        .createQueryBuilder('potentialDligence')
        .andWhere('potentialDligence.id in (:...diligenceIdsList)', { diligenceIdsList })
        .orderBy('potentialDligence.createDate', 'DESC')
        .skip(pageSize * (pageIndex - 1))
        .take(pageSize)
        .getManyAndCount();

      return Object.assign(response, {
        pageIndex: pageIndex,
        pageSize: pageSize,
        total,
        data,
        createDate: batch?.createDate,
        editor: batch?.creator,
      });
    }

    return Object.assign(response, {
      pageIndex: pageIndex,
      pageSize: pageSize,
      total: 0,
      data: [],
      createDate: batch?.createDate,
      editor: batch?.creator,
    });
  }

  /**
   * 获取潜在利益关系排查统计信息
   * @param user 用户信息
   * @param data 请求参数
   * @returns 统计信息
   */
  async getPotentialStatistics(data: SearchPotentialBatchResultRequest) {
    if (!data.batchId) {
      throw new BadRequestException();
    }
    const qb = this.getBatchPotentialQueryBuilder(data);
    qb.select('COUNT(distinct(detail.diligenceId))', 'totalHits');
    const [group, allData] = await Bluebird.all([
      qb.clone().addSelect('detail.groupKey', 'key').andWhere('detail.groupHits > 0').groupBy('detail.groupKey').getRawMany(),
      qb.clone().addSelect('detail.diligenceId', 'diligenceId').groupBy('detail.diligenceId').getRawMany(),
    ]);

    const defaultConfig = cloneDeep(PotentialDimensionSettings);
    const result = [];
    result.push({ key: 'all', totalHits: allData.length });
    Object.keys(defaultConfig).forEach((cGroup) => {
      const groupCount = group.find((x) => x.key == cGroup)?.totalHits || 0;
      const aggGroup = { key: cGroup, totalHits: groupCount };
      result.push(aggGroup);
    });
    return result;
  }

  getBatchPotentialQueryBuilder(data: SearchPotentialBatchResultRequest) {
    const { batchId, result, groupKey, diligenceIds, searchKey } = data;

    const qb = this.batchPotentialDetailRepo
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.diligence', 'diligence')
      .select(['detail.id', 'detail.diligenceId', 'detail.groupKey', 'detail.dimKey', 'detail.groupHits', 'detail.dimHits'])
      .where('detail.batchId = :batchId', { batchId });

    if (result?.length) {
      qb.andWhere(' diligence.result in (:...result) ', { result });
    }

    if (groupKey) {
      qb.andWhere('(detail.groupKey = :groupKey and detail.groupHits > 0)', { groupKey });
    }
    if (diligenceIds?.length) {
      qb.andWhere('detail.diligenceId in (:...diligenceIds)', { diligenceIds });
    }
    if (searchKey?.length) {
      qb.andWhere('diligence.name like :searchKey', { searchKey: `%${searchKey}%` });
    }
    return qb;
  }

  /**
   * 根据导入的匹配结果，创建批量排查任务
   * @param user
   * @param batchMatchCompanyId
   * @param settingId
   * @returns
   */
  async executeBatchPotentialCompany(user: RoverUser, batchMatchCompanyId: number, settingId?: number) {
    // 当前已有离线任务正在进行，请勿重复进行操作！
    await this.batchBaseHelperService.checkBatchCount(user, BatchBusinessTypeEnums.Potential_Batch_Excel);
    const batchMatchEntity = await this.batchMatchCompanyRepo.findOne({ id: batchMatchCompanyId, orgId: user.currentOrg });
    if (!batchMatchEntity) {
      throw new BadRequestException('不存在的batch');
    }
    const batchItems = await this.batchMatchCompanyItemRepo
      .createQueryBuilder('item')
      .where('item.batchId = :batchMatchCompanyId', { batchMatchCompanyId })
      .andWhere('item.flag = 0')
      .select(['item.name', 'item.companyId'])
      .getMany();
    if (batchItems.length == 0) {
      throw new BadRequestException('不存在匹配成功的企业，无法执行批量排查');
    }
    // 批量排查每天的次数上限卡控
    // await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Increase, 1);
    try {
      const fileParseResult: FileParseResult = {
        succeedItems: batchItems.map((d) => {
          return { companyId: d.companyId, companyName: d.name };
        }),
        failedItems: [],
      };
      const jobItemSize = BatchConstants.getJobItemSize(BatchBusinessTypeEnums.Potential_Batch_Excel); //通过调整大小还控制 最终的job的数量（一个job对应一个mq message.handler）
      const batchEntity = await this.batchCreatorHelperService.createBatchEntity(
        user,
        fileParseResult,
        BatchBusinessTypeEnums.Potential_Batch_Excel,
        jobItemSize,
        batchMatchEntity.fileName,
        batchMatchEntity.originFile,
        BatchTypeEnums.Import,
        { settingId },
      );
      await Bluebird.all([
        this.batchMatchCompanyRepo.delete({ id: batchMatchCompanyId }),
        this.batchMatchCompanyItemRepo.delete({ batchId: batchMatchCompanyId }),
      ]);
      return batchEntity;
    } catch (e) {
      // await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Decrease, 1);
      if (e instanceof HttpException) {
        throw e;
      }
      this.logger.error(e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.CreateFailed,
        internalMessage: e.message,
      });
    }
  }
}
