import { BadRequestException, HttpException, Injectable } from '@nestjs/common';
import { RoverUser } from '../../../../libs/model/common';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { RoverBundleLimitationType } from '@kezhaozhao/saas-bundle-service';
import { CounterOperation } from '../../../../libs/constants/common';
import { FileParseResult } from '../../../../libs/model/batch/po/parse/FileParseResult';
import { BatchConstants } from '../../common/batch.constants';
import { BatchTypeEnums } from '../../../../libs/enums/batch/BatchTypeEnums';
import { RoverExceptions } from '../../../../libs/exceptions/exceptionConstants';
import { BatchBaseHelper } from './batch.base.helper';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchMatchCompanyEntity } from '../../../../libs/entities/BatchMatchCompanyEntity';
import { Repository } from 'typeorm';
import { BatchMatchCompanyItemEntity } from '../../../../libs/entities/BatchMatchCompanyItemEntity';
import * as Bluebird from 'bluebird';
import { BatchBundleService } from '../batch.bundle.service';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchCreatorHelperBase } from './batch.creator.helper.base';

@Injectable()
export class BatchDiligenceHelper {
  private readonly logger = QccLogger.getLogger('BatchDiligenceHelper');

  constructor(
    @InjectRepository(BatchMatchCompanyEntity) private readonly batchMatchCompanyRepo: Repository<BatchMatchCompanyEntity>,
    private readonly batchBaseHelperService: BatchBaseHelper,
    @InjectRepository(BatchMatchCompanyItemEntity) private readonly batchMatchCompanyItemRepo: Repository<BatchMatchCompanyItemEntity>,
    private readonly batchBundleService: BatchBundleService,
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
  ) {}

  /**
   * 根据导入的匹配结果，创建批量排查任务
   * @param user
   * @param batchMatchCompanyId
   * @param settingId
   * @returns
   */
  async executeBatchDiligenceCompany(user: RoverUser, batchMatchCompanyId: number, settingId?: number) {
    // 当前已有离线任务正在进行，请勿重复进行操作！
    await this.batchBaseHelperService.checkBatchCount(user, BatchBusinessTypeEnums.Diligence_File);
    const batchMatchEntity = await this.batchMatchCompanyRepo.findOne({ id: batchMatchCompanyId, orgId: user.currentOrg });
    if (!batchMatchEntity) {
      throw new BadRequestException('不存在的batch');
    }
    const batchItems = await this.batchMatchCompanyItemRepo
      .createQueryBuilder('item')
      .where('item.batchId = :batchMatchCompanyId', { batchMatchCompanyId })
      .andWhere('item.flag = 0')
      .select(['item.name', 'item.companyId'])
      .getMany();
    if (batchItems.length == 0) {
      throw new BadRequestException('不存在匹配成功的企业，无法执行批量排查');
    }
    // 批量排查每天的次数上限卡控
    await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Increase, 1);
    try {
      const fileParseResult: FileParseResult = {
        succeedItems: batchItems.map((d) => {
          return { companyId: d.companyId, companyName: d.name };
        }),
        failedItems: [],
      };
      const jobItemSize = BatchConstants.getJobItemSize(BatchBusinessTypeEnums.Diligence_File); //通过调整大小还控制 最终的job的数量（一个job对应一个mq message.handler）
      const batchEntity = await this.batchCreatorHelperService.createBatchEntity(
        user,
        fileParseResult,
        BatchBusinessTypeEnums.Diligence_File,
        jobItemSize,
        batchMatchEntity.fileName,
        batchMatchEntity.originFile,
        BatchTypeEnums.Import,
        { settingId },
      );
      await Bluebird.all([
        this.batchMatchCompanyRepo.delete({ id: batchMatchCompanyId }),
        this.batchMatchCompanyItemRepo.delete({ batchId: batchMatchCompanyId }),
      ]);
      return batchEntity;
    } catch (e) {
      await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Decrease, 1);
      if (e instanceof HttpException) {
        throw e;
      }
      this.logger.error(e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.CreateFailed,
        internalMessage: e.message,
      });
    }
  }
}
