import { BadRequestException, Injectable } from '@nestjs/common';
import { BatchResultEntity } from '../../../../libs/entities/BatchResultEntity';
import { BatchJobResultTypeEnums } from '../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, In, Not, Repository } from 'typeorm';
import { KysCompanyResponseDetails } from '@kezhaozhao/company-search-api';
import { RoverUser } from '../../../../libs/model/common';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchStatusEnums } from '../../../../libs/enums/batch/BatchStatusEnums';
import { RoverExceptions } from '../../../../libs/exceptions/exceptionConstants';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import { SearchBatchResultRequest } from '../../../../libs/model/batch/request/SearchBatchResultRequest';
import { DiligenceHistoryResponse } from '../../../../libs/model/diligence/pojo/history/DiligenceHistoryResponse';
import { QueryBuilderHelper } from '../../../../libs/common/sql.helper';
import { ChildDimensionHit } from '../../../../libs/model/batch/request/ChildDimensionHit';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';
import { DiligenceHistoryEntity } from '../../../../libs/entities/DiligenceHistoryEntity';
import { BatchDiligenceEntity } from '../../../../libs/entities/BatchDiligenceEntity';
import { CustomerEntity } from '../../../../libs/entities/CustomerEntity';
import { CustomerLabelEntity } from '../../../../libs/entities/CustomerLabelEntity';
import { CompanyEntity } from '../../../../libs/entities/CompanyEntity';
import { BatchJobEntity } from '../../../../libs/entities/BatchJobEntity';
import { DimensionLevel1Enums } from '../../../../libs/enums/diligence/DimensionLevel1Enums';
import { DimensionTypeEnums } from '../../../../libs/enums/diligence/DimensionTypeEnums';
import { CompanySearchService } from '../../../company/company-search.service';
import { EvaluationService } from '../../../diligence/evaluation/evaluation.service';
import * as Bluebird from 'bluebird';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchResultPO } from '../../model/BatchResultPO';
import { JobMonitorMessagePO, JobMonitorMessageTypeEnums } from '../../../../libs/model/batch/po/message/JobMonitorMessagePO';
import { QueueService } from '../../../../libs/config/queue.service';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { getBatchProcessLockKey, getUniqueJobResultKey } from '../../common/batch.utils';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Redis } from 'ioredis';
import { chunk, compact } from 'lodash';
import { BatchProgressLock } from '../../../../libs/common/distributed_lock/BatchProgressLock';
import { SearchBatchRequest } from '../../../../libs/model/batch/request/SearchBatchRequest';
import { SearchBatchResponse } from '../../../../libs/model/batch/response/SearchBatchResponse';

@Injectable()
export class BatchBaseHelper {
  private readonly logger = QccLogger.getLogger(BatchBaseHelper.name);
  public batchJobMonitorQueue: RabbitMQ;
  private readonly redisClient: Redis;

  constructor(
    @InjectRepository(BatchResultEntity) protected readonly batchResultRepo: Repository<BatchResultEntity>,
    @InjectRepository(BatchEntity) protected readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchJobEntity) private readonly batchJobRepo: Repository<BatchJobEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    protected readonly companySearchService: CompanySearchService,
    protected readonly evaluationService: EvaluationService,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    private readonly queueService: QueueService,
    private readonly redisService: RedisService,
  ) {
    this.batchJobMonitorQueue = this.queueService.batchJobMonitorQueue;
    this.redisClient = this.redisService.getClient();
  }

  /**
   * 处理job 的result， 这里的result一定是归属为同一个batchId，但是不一定是同一个jobId(batch失败的时候会把所有的job一并生成result)
   * @param items
   */
  public async handleJobResult(items: BatchResultPO[]) {
    if (!items?.length) {
      this.logger.error(`handleJobResult(): no items to handle`);
      return;
    }
    const batchId = items[0]?.batchId;
    const batchProgressLock = new BatchProgressLock(this.redisClient, batchId);
    const chunks = chunk(items, 300);

    const lockKey = getBatchProcessLockKey(batchId);
    this.logger.info(`handleJobResult(): batchId=${batchId}, jobId=${items.map((r) => r.jobId).join(',')}, ${items?.length} results, chunks=${chunks.length}`);
    await Bluebird.map(
      chunks,
      async (results: BatchResultPO[]) => {
        const originalLength = results.length;
        const hashKeys = results.map((r) => r.resultHashkey).filter((t) => t);
        let toInsert = [];
        try {
          // const { newKeys } = await RedisUtils.addKeysToSet(this.redisClient, lockKey, hashKeys, 3600 * 24);
          const newKeys = await batchProgressLock.addKeys(hashKeys);
          if (newKeys.length) {
            toInsert = results.filter((r) => newKeys.includes(r.resultHashkey));
            this.logger.info(
              `handleJobResult(): batchId=${batchId}, jobId=${items.map((r) => r.jobId).join(',')},original=${originalLength},toInsert=${
                newKeys.length
              }, countIssue=${newKeys.length + toInsert.length === originalLength}`,
            );
          } else {
            this.logger.warn(
              `handleJobResult(): batchId=${batchId}, jobId=${items
                .map((r) => r.jobId)
                .join(',')},original=${originalLength}, no items(after existing check) to insert`,
            );
            return;
          }
          // const batchStatisticInfo: BatchStatisticsBasePO = results.reduce(
          //   (statisticsInfo, r) => {
          //     const c = 1;
          //     switch (r.resultType) {
          //       // 执行成功-忽略部分错误
          //       case BatchJobResultTypeEnums.SUCCEED_IGNORE:
          //       // 执行成功-付费
          //       case BatchJobResultTypeEnums.SUCCEED_PAID: {
          //         statisticsInfo.paidCount += c;
          //         statisticsInfo.successCount += c;
          //         break;
          //       }
          //       // 执行成功-未付费
          //       case BatchJobResultTypeEnums.SUCCEED_UNPAID: {
          //         statisticsInfo.successCount += c;
          //         break;
          //       }
          //       // 数据重复
          //       case BatchJobResultTypeEnums.SUCCEED_DUPLICATED: {
          //         statisticsInfo.duplicatedCount = c;
          //         break;
          //       }
          //       //更新数量
          //       case BatchJobResultTypeEnums.UPDATE_DUPLICATED: {
          //         statisticsInfo.updatedCount += c;
          //         break;
          //       }
          //       // 执行失败 超出套餐额度
          //       case BatchJobResultTypeEnums.FAILED_BUNDLE_LIMITED:
          //       // 执行失败 数据校验失败
          //       case BatchJobResultTypeEnums.FAILED_VERIFICATION:
          //       // 执行失败 （代码执行过程中失败）
          //       case BatchJobResultTypeEnums.FAILED_CODE:
          //         // 可以重试的失败
          //         statisticsInfo.errorCount += c;
          //         break;
          //       // 不支持的企业类型
          //       case BatchJobResultTypeEnums.FAILED_UNSUPPORTED:
          //       // 解析时候发现的错误数据
          //       case BatchJobResultTypeEnums.PARSER_ERROR:
          //       // 执行失败 未识别或者格式错误的数据
          //       case BatchJobResultTypeEnums.FAILED_UNMATCHED: {
          //         statisticsInfo.errorCount += c;
          //         break;
          //       }
          //     }
          //     return statisticsInfo;
          //   },
          //   {
          //     paidCount: 0,
          //     errorCount: 0,
          //     successCount: 0,
          //     updatedCount: 0,
          //     duplicatedCount: 0,
          //   } as BatchStatisticsBasePO,
          // );
          // 不实时更新batch统计的count，避免死锁，等待 jobMonitor或者jobScan时候更新
          // return this.batchRepo.manager.transaction(async (transactionalEntityManager) => {
          //   const batchId = results[0].batchId;
          //   await transactionalEntityManager.insert(BatchResultEntity, results);
          //   const toIncrease = Object.keys(batchStatisticInfo)
          //     .filter((k) => batchStatisticInfo[k] > 0)
          //     .reduce((prev, cur) => {
          //       prev[cur] = () => `${snakeCase(cur)} + ${batchStatisticInfo[cur]}`;
          //       return prev;
          //     }, {});
          //   const qb = transactionalEntityManager.createQueryBuilder().update(BatchEntity).set(toIncrease).where('batchId = :batchId', { batchId });
          //   await qb.execute();
          // });
          const t = await this.batchResultRepo.insert(toInsert);
          return t;
        } catch (e) {
          this.logger.error(`handleJobResult(): batchId=${results[0]?.batchId}, error=${e.message}`);
          this.logger.error(e);
          const notInsertedResults = await this.batchResultRepo.find({
            where: { resultHashkey: Not(In(hashKeys)) },
            select: ['resultHashkey', 'resultId'],
          });
          if (notInsertedResults?.length) {
            // 需要从redis set中删除，避免下次无法重新插入
            await batchProgressLock.removeKeys(notInsertedResults.map((r) => r.resultHashkey));
            // await RedisUtils.removeKeysFromSet(
            //   this.redisClient,
            //   lockKey,
            //   notInsertedResults.map((r) => r.resultHashkey),
            // );
          }
        }
      },
      { concurrency: 1 },
    );
  }

  public async saveUnMatchedCompanyBatch(unmatchedNames: string[], batchId: number, jobId: number) {
    const results = unmatchedNames.map((n) => {
      return Object.assign(new BatchResultEntity(), {
        batchId,
        jobId,
        resultType: BatchJobResultTypeEnums.FAILED_UNMATCHED,
        resultInfo: { companyName: n },
        resultHashkey: getUniqueJobResultKey(batchId, jobId, `matched_${n}`),
      });
    });
    // return this.batchResultRepo.insert(results);
    return this.handleJobResult(results);
  }

  /**
   * 保存不支持类型的公司到batch结果中
   * @private
   * @param unsupported
   * @param batchId
   * @param jobId
   */
  public async saveUnsupportedCompanyBatch(unsupported: string[], batchId: number, jobId: number) {
    const results = unsupported.map((n) => {
      return Object.assign(new BatchResultEntity(), {
        batchId,
        jobId,
        resultType: BatchJobResultTypeEnums.FAILED_UNSUPPORTED,
        resultInfo: { companyName: n },
        resultHashkey: getUniqueJobResultKey(batchId, jobId, `unmatched_${n}`),
      });
    });
    // return this.batchResultRepo.insert(results);
    return this.handleJobResult(results);
  }

  /**
   * 保存不匹配的公司到batch结果中
   * @private
   * @param name
   * @param company
   */
  public matchCompany(name: string, company: KysCompanyResponseDetails) {
    return name == company.name || name == company.creditcode || company.regno == name || company?.originalname?.includes(name);
  }

  /**
   * 是否存在执行中同类型批量任务
   * @param user
   * @param businessType
   */
  public async checkBatchCount(user: RoverUser, businessType: BatchBusinessTypeEnums) {
    const { currentOrg: orgId, userId } = user;
    const batchExists = await this.batchRepo.count({
      businessType,
      orgId,
      creatorId: userId,
      status: In([BatchStatusEnums.Waiting, BatchStatusEnums.Processing, BatchStatusEnums.Queuing]),
    });
    const exportTypes = [
      BatchBusinessTypeEnums.Diligence_Batch_Detail,
      BatchBusinessTypeEnums.Tender_Diligence_Record_Export,
      BatchBusinessTypeEnums.Specific_Batch_Export,
      BatchBusinessTypeEnums.Tender_Dimension_Detail_Export,
      BatchBusinessTypeEnums.Tender_All_Dimension_Detail_Export,
      BatchBusinessTypeEnums.Dimension_Detail_Export,
    ];
    if (exportTypes.includes(businessType) && batchExists > 5) {
      throw new BadRequestException(RoverExceptions.Batch.OutOfLimit);
    }
    // 当前已有离线任务正在进行，请勿重复进行操作！
    if (batchExists && !exportTypes.includes(businessType)) {
      throw new BadRequestException(RoverExceptions.Batch.OnlyOneBatchProcess);
    }
  }

  public async getOneBatch(batchId: number) {
    return this.batchRepo.findOne({ batchId });
  }

  public async getBatchResult(batchId: number) {
    return this.batchResultRepo.find({ batchId });
  }

  async getBatch(user: RoverUser, postData: SearchBatchResultRequest, notUsePage = false): Promise<DiligenceHistoryResponse> {
    let response: DiligenceHistoryResponse;
    const { currentOrg: orgId } = user;
    const {
      batchId,
      searchKey,
      pageSize,
      pageIndex,
      result,
      createDate,
      dimensionLevel1,
      dimensionLevel2,
      groupIds,
      labelIds,
      departments,
      province,
      existCustomer,
      diligenceIds,
      preBatchId,
      changedOnly,
    } = postData;
    const batchEntity = await this.batchRepo.findOne({
      where: {
        batchId,
      },
      relations: ['creator'],
    });
    const params = {
      batchId,
      orgId,
      searchKey,
      result,
      groupIds,
      labelIds,
      departments,
      province,
      existCustomer,
      changedOnly,
      preBatchId,
      diligenceIds,
    };
    const qb = this.getBatchQB(params);

    // 创建时间
    QueryBuilderHelper.applyDateRangeQuery(qb, createDate, 'createDate');
    qb.orderBy('diligence.id', 'DESC');
    //记录的是企业数，不是维度命中数
    const childDimensionHits: ChildDimensionHit[] = [];
    if (dimensionLevel1) {
      let data = await qb.getMany();
      if (data?.length) {
        this.generateChildDimensionHits(data, childDimensionHits, dimensionLevel1, dimensionLevel2);
        data = data.filter((d) => d?.[dimensionLevel1] > 0);
        const level1Total = data?.length;
        if (dimensionLevel2) {
          data = data.filter(
            (d) =>
              d.details.dimensionScoreDetails.find((e) => e.groupKey === dimensionLevel1)?.scoreDetails?.find((e) => e.key === dimensionLevel2)?.totalHits > 0,
          );
        }
        if (!data?.length) {
          return { pageSize, pageIndex, total: 0, data: [] };
        }
        if (childDimensionHits?.length) {
          childDimensionHits.unshift(
            Object.assign(new ChildDimensionHit(), {
              key: 'allDimension',
              totalHits: level1Total,
            }),
          );
        }
        const start = pageSize * (pageIndex - 1);
        if (notUsePage) {
          response = {
            pageSize,
            pageIndex,
            total: data.length,
            data,
            childDimensionHits,
          };
        } else {
          response = {
            pageSize: pageSize,
            pageIndex: pageIndex,
            total: data.length,
            data: data.slice(start, start + pageSize),
            childDimensionHits,
            editor: batchEntity?.creator,
            createDate: batchEntity?.updateDate,
          };
        }
      }
    } else {
      if (!notUsePage) {
        qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
      }
      const [data, total] = await qb.getManyAndCount();
      this.generateChildDimensionHits(data, childDimensionHits, dimensionLevel1);
      if (childDimensionHits?.length) {
        childDimensionHits.unshift(
          Object.assign(new ChildDimensionHit(), {
            key: 'allDimension',
            totalHits: total,
          }),
        );
      }
      response = {
        pageSize,
        pageIndex,
        data,
        total,
        childDimensionHits,
        editor: batchEntity?.creator,
        createDate: batchEntity?.updateDate,
      };
    }
    if (response?.data?.length) {
      const companyIds = response.data.map((x) => x.companyId);
      const companyDetails = await this.companySearchService.searchCompanyDetail(companyIds);
      const detailMap: Map<string, KysCompanyResponseDetails> = new Map<string, KysCompanyResponseDetails>();
      companyDetails.forEach((x) => detailMap.set(x.id, x));
      response.data.forEach((d) => (d.creditcode = detailMap.get(d.companyId)?.creditcode));
      response.data.forEach((d) => this.evaluationService.postUpdateDiligence(d));
    }
    if (!response.data) {
      response.data = [];
    }
    return response;
  }

  protected getBatchQB(params): SelectQueryBuilder<DiligenceHistoryEntity> {
    const { batchId, orgId, searchKey, result, groupIds, labelIds, departments, province, existCustomer, changedOnly, preBatchId, diligenceIds } = params;
    const qb = this.diligenceHistoryRepo
      .createQueryBuilder('diligence')
      .leftJoinAndSelect('diligence.editor', 'editor')
      .leftJoinAndSelect(BatchDiligenceEntity, 'batchDiligence', 'diligence.id = batchDiligence.diligenceId')
      .leftJoinAndSelect(BatchEntity, 'batch', 'batch.batchId = batchDiligence.batchId')
      .select(['diligence', 'editor.name', 'editor.phone'])
      .where('batch.batchId = :batchId', { batchId })
      .andWhere('batch.orgId = :orgId', { orgId });

    if (diligenceIds?.length) {
      qb.andWhere('diligence.id in (:...diligenceIds)', { diligenceIds });
    }

    if (changedOnly && preBatchId) {
      qb.innerJoin(BatchDiligenceEntity, 'pb', 'pb.batchId = :preBatchId', { preBatchId })
        .innerJoin(DiligenceHistoryEntity, 'pd', 'pd.companyId = diligence.companyId and pd.id = pb.diligenceId')
        .andWhere('batchDiligence.changingVersion - pb.changingVersion > 0');
    }

    if (searchKey) {
      qb.andWhere('diligence.name like :name', { name: `%${searchKey}%` });
    }
    if (result?.length > 0) {
      qb.andWhere('diligence.result in (:...result)', { result });
    }
    if (existCustomer || groupIds?.length || labelIds?.length || departments?.length || province?.length) {
      qb.innerJoin(CustomerEntity, 'customer', 'diligence.orgId = customer.orgId and diligence.companyId = customer.companyId')
        .leftJoin(CustomerLabelEntity, 'label', 'customer.customerId = label.customerId')
        .leftJoin(CompanyEntity, 'company', 'customer.companyId = company.companyId');
      if (groupIds?.length) {
        if (groupIds.includes(-1)) {
          qb.andWhere('(customer.groupId is null or customer.groupId in (:...groupIds))', { groupIds });
        } else {
          qb.andWhere('customer.groupId in (:...groupIds)', { groupIds });
        }
      }
      if (departments?.length) {
        if (departments.includes('未分配部门')) {
          qb.andWhere('(customer.customerDepartment is null or customer.customerDepartment in (:...departments))', { departments });
        } else {
          qb.andWhere('customer.customerDepartment in (:...departments)', { departments });
        }
      }
      if (labelIds?.length) {
        if (labelIds.includes(-1)) {
          qb.andWhere('(label.labelId is null or label.labelId in (:...labelIds))', { labelIds });
        } else {
          qb.andWhere('label.labelId in (:...labelIds)', { labelIds });
        }
      }
      if (province?.length) {
        qb.andWhere('company.province in (:...province)', { province });
      }
    }
    return qb;
  }

  private generateChildDimensionHits(
    data: any[],
    childDimensionHits: ChildDimensionHit[],
    dimensionLevel1: DimensionLevel1Enums,
    dimensionLevel2?: DimensionTypeEnums,
  ) {
    const childDimensionHit: Record<string, ChildDimensionHit> = {};
    if (data?.length) {
      data.forEach((d) => {
        d?.details?.dimensionScoreDetails.forEach((e) => {
          d[e.groupKey] = e.totalHits;
          if (e.groupKey === dimensionLevel1) {
            e.scoreDetails?.map((s) => {
              //childDimensionHit[s.key] = (childDimensionHit?.[s.key] || 0) + (s.totalHits > 0 ? 1 : 0);
              childDimensionHit[s.key] = {
                key: s.key,
                totalHits: (childDimensionHit?.[s.key]?.totalHits || 0) + (s.totalHits > 0 ? 1 : 0),
                sort: s.sort,
              };
              if (s.key === dimensionLevel2) {
                d['currentDimension'] = s.totalHits;
                d['childLevel'] = s.level;
              }
            });
            if (!dimensionLevel2) {
              d['currentDimension'] = e.totalHits;
            }
          }
        });
      });
    }
    Object?.keys(childDimensionHit)?.map((c) => {
      if (childDimensionHit[c]?.totalHits > 0) {
        childDimensionHits.push(
          Object.assign(new ChildDimensionHit(), {
            key: c,
            totalHits: childDimensionHit[c]?.totalHits,
            sort: childDimensionHit[c].sort,
          }),
        );
      }
    });
    return childDimensionHits?.sort((a, b) => a.sort - b.sort);
  }

  /**
   * 创建 batch失败时候，回滚
   * @param batchEntity
   * @param e
   */
  public async rollBackBatch(batchEntity: BatchEntity, e: any) {
    if (batchEntity) {
      //回退生成的 batchJob 和 batchResult 并标记batch 失败
      await Bluebird.all([
        this.batchJobRepo.delete({ batchId: batchEntity.batchId }),
        this.batchResultRepo.delete({ batchId: batchEntity.batchId }),
        this.batchDiligenceRepo.delete({ batchId: batchEntity.batchId }),
        this.batchRepo.update(batchEntity.batchId, {
          endDate: new Date(),
          status: BatchStatusEnums.Error,
          comment: e.message?.substr(0, 200),
        }),
      ]);
      this.logger.info(`batch ${batchEntity.batchId} rolled back to failed`);
    }
  }

  /**
   * batch 创建后，发送一条monitor 消息
   * @param batchEntity
   * @param user
   */
  public async sendBatchMonitorMessage(batchEntity: BatchEntity, user: RoverUser) {
    const msgBody: JobMonitorMessagePO = {
      batchId: batchEntity.batchId,
      batchType: batchEntity.batchType,
      businessType: batchEntity.businessType,
      startDate: Date.now(),
      batchTotalRecords: batchEntity.recordCount,
      operatorId: user.userId,
      orgId: user.currentOrg,
      index: 1,
      type: JobMonitorMessageTypeEnums.Scan,
      isUpdate: batchEntity.batchInfo?.isUpdate,
    };
    const messageResponse = await this.batchJobMonitorQueue.sendMessageV2(msgBody, {
      ttl: 1,
      retries: 1,
      traceTags: [
        // @ts-ignore
        { key: 'batchId', val: batchEntity.batchId, overridable: true },
        {
          key: 'businessType',
          val: batchEntity.businessType.toString(),
          overridable: true,
        },
        // { key: 'businessId', val: _businessId, overridable: true },
      ],
    });
    return messageResponse;
  }

  async searchAnalyzeRecordBatch(user: RoverUser, body: SearchBatchRequest) {
    const { currentOrg: orgId } = user;
    const { pageSize, pageIndex, batchType, businessType, status, createDate, field, order, batchIds } = body;

    const qb = this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .select(['batch', 'creator.name', 'creator.userId', 'creator.phone'])
      .where('batch.batchType = :batchType', { batchType })
      .andWhere('batch.orgId = :orgId', { orgId });

    const response = new SearchBatchResponse();
    Object.assign(response, { pageSize, pageIndex, total: 0, data: [] });
    if (!businessType?.length) {
      return response;
    }
    if (status?.length) {
      qb.andWhere('batch.status in (:...status)', { status });
    }
    if (batchIds?.length) {
      qb.andWhere('batch.batchId in (:...batchIds)', { batchIds });
    }
    // 提交时间
    if (field && order) {
      qb.orderBy(`batch.${field}`, order);
    } else {
      qb.orderBy('batch.createDate', 'DESC');
    }
    QueryBuilderHelper.applyDateRangeQuery(qb, createDate, 'createDate');
    qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    const [items, total] = await qb.getManyAndCount();
    response.total = total;
    response.data = items;
    return response;
  }
}
