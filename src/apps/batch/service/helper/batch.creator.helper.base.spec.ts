import {
  BundleReachedLimitationException,
  MockBundleService,
  Product,
  RoverBundleCounterType,
  RoverBundleLimitationType,
  RoverBundleService,
} from '@kezhaozhao/saas-bundle-service';
import { BadRequestException, ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { FieldParseErrorTypeEnums } from 'libs/enums/batch/FieldParseErrorTypeEnums';
import { getRepository, Repository } from 'typeorm';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import { BatchJobEntity } from '../../../../libs/entities/BatchJobEntity';
import { BatchResultEntity } from '../../../../libs/entities/BatchResultEntity';
import { CustomerEntity } from '../../../../libs/entities/CustomerEntity';
import { OrgSettingsLogEntity } from '../../../../libs/entities/OrgSettingsLogEntity';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchStatusEnums } from '../../../../libs/enums/batch/BatchStatusEnums';
import { BatchTypeEnums } from '../../../../libs/enums/batch/BatchTypeEnums';
import { RoverExceptions } from '../../../../libs/exceptions/exceptionConstants';
import { FileParseResult } from '../../../../libs/model/batch/po/parse/FileParseResult';
import { DimensionDefinitionPO } from '../../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { AppTestModule } from '../../../app/app.test.module';
import { SettingsService } from '../../../settings/settings.service';
import { TestHelper } from '../../../test_utils_module/test.helper';
import { BatchModule } from '../../batch.module';
import { BatchBundleService } from '../batch.bundle.service';
import { BatchBaseHelper } from './batch.base.helper';
import { BatchCreatorHelperBase } from './batch.creator.helper.base';

const [testOrgId, testUserId] = generateUniqueTestIds('batch.creator.helper.base.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(60 * 1000);

describe('BatchCreatorHelperBase', () => {
  let module: TestingModule;
  let batchCreatorHelperBase: BatchCreatorHelperBase;
  let batchBaseHelper: BatchBaseHelper;
  let settingsService: SettingsService;
  let batchBundleService: BatchBundleService;
  let mockedBundleService: MockBundleService<RoverBundleCounterType, RoverBundleLimitationType>;
  let batchEntityRepo: Repository<BatchEntity>;
  let batchJobRepo: Repository<BatchJobEntity>;
  let batchResultRepo: Repository<BatchResultEntity>;
  let customerRepo: Repository<CustomerEntity>;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();

    batchCreatorHelperBase = module.get(BatchCreatorHelperBase);
    batchBaseHelper = module.get(BatchBaseHelper);
    settingsService = module.get(SettingsService);
    batchBundleService = module.get(BatchBundleService);
    mockedBundleService = module.get(RoverBundleService);

    batchEntityRepo = getRepository(BatchEntity);
    batchJobRepo = getRepository(BatchJobEntity);
    batchResultRepo = getRepository(BatchResultEntity);
    customerRepo = getRepository(CustomerEntity);
  });
  afterAll(async () => {
    await batchEntityRepo.manager.connection.close();
  });

  beforeEach(async () => {
    // 清理测试数据
    await TestHelper.clearAllTestData(batchEntityRepo.manager, testUser.currentOrg, testUser.userId);
    jest.clearAllMocks();
  });

  afterEach(async () => {
    // 清理测试数据
    await TestHelper.clearAllTestData(batchEntityRepo.manager, testUser.currentOrg, testUser.userId);
  });

  // 单元测试部分
  describe('单元测试', () => {
    it('createBatchEntity - 成功创建批量任务', async () => {
      // Mock依赖
      const mockSendBatchMonitorMessage = jest.spyOn(batchBaseHelper, 'sendBatchMonitorMessage').mockResolvedValue('success');

      jest.spyOn(batchCreatorHelperBase, 'checkDiligenceBundleUsage').mockResolvedValue({
        companyCounter: null,
        historyCounter: null,
        dailyCounter: null,
        withholdingCount: 0,
        withholdingRecordCount: 0,
      });

      const fileParseResult: FileParseResult = {
        succeedItems: [
          { companyId: 'company1', companyName: 'Company One' },
          { companyId: 'company2', companyName: 'Company Two' },
        ],
        failedItems: [],
      };

      const result = await batchCreatorHelperBase.createBatchEntity(
        testUser,
        fileParseResult,
        BatchBusinessTypeEnums.Customer_File,
        10,
        'test.xlsx',
        'http://example.com/test.xlsx',
      );

      expect(result).toBeDefined();
      expect(result.orgId).toBe(testUser.currentOrg);
      expect(result.creatorId).toBe(testUser.userId);
      expect(result.businessType).toBe(BatchBusinessTypeEnums.Customer_File);
      expect(result.status).toBe(BatchStatusEnums.Waiting);
      expect(mockSendBatchMonitorMessage).toHaveBeenCalled();
    });

    it.skip('createBatchEntity - 创建失败抛出异常', async () => {
      jest.spyOn(batchCreatorHelperBase, 'checkDiligenceBundleUsage').mockResolvedValue({
        companyCounter: {
          decrease: jest.fn().mockResolvedValue(true),
        } as any,
        historyCounter: {
          decrease: jest.fn().mockResolvedValue(true),
        } as any,
        dailyCounter: {
          decrease: jest.fn().mockResolvedValue(true),
        } as any,
        withholdingCount: 5,
        withholdingRecordCount: 5,
      });

      jest.spyOn(batchBaseHelper, 'handleJobResult').mockRejectedValue(new Error('处理结果失败'));

      const fileParseResult: FileParseResult = {
        succeedItems: [{ companyId: 'company1', companyName: 'Company One' }],
        failedItems: [],
      };

      await expect(
        batchCreatorHelperBase.createBatchEntity(
          testUser,
          fileParseResult,
          BatchBusinessTypeEnums.Customer_File,
          10,
          'test.xlsx',
          'http://example.com/test.xlsx',
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it.skip('checkDiligenceBundleUsage - 非尽调任务不检查套餐', async () => {
      const fileParseResult: FileParseResult = {
        succeedItems: Array(10)
          .fill(0)
          .map((_, i) => ({
            companyId: `company${i}`,
            companyName: `Company ${i}`,
          })),
        failedItems: [],
      };

      const result = await batchCreatorHelperBase.checkDiligenceBundleUsage(testUser, fileParseResult, BatchBusinessTypeEnums.Customer_File);

      expect(result).toEqual({
        companyCounter: null,
        historyCounter: null,
        dailyCounter: null,
        withholdingCount: 0,
        withholdingRecordCount: 0,
      });
    });

    it.skip('checkDiligenceBundleUsage - 额度不足抛出异常', async () => {
      // Mock计数器
      const mockCounter = {
        increase: jest.fn().mockImplementation(() => {
          throw new BundleReachedLimitationException('BUNDLE_LIMITATION_REACHED', Product.Rover, 1000);
        }),
        decrease: jest.fn().mockResolvedValue(true),
        getStock: jest.fn().mockResolvedValue({ stock: 0 }),
      };

      jest.spyOn(mockedBundleService, 'getBundleCounter').mockResolvedValue(mockCounter as any);

      jest.spyOn(mockedBundleService, 'getBundle').mockResolvedValue({
        [RoverBundleCounterType.DiligenceCompanyQuantity]: { value: 1000 },
        [RoverBundleCounterType.DiligenceHistoryQuantity]: { value: 1000 },
        [RoverBundleCounterType.BatchInspectionQuantity]: { value: 100 },
      } as any);

      jest.spyOn(batchCreatorHelperBase, 'checkDiligenceBundleStock').mockResolvedValue({
        companyCount: 10,
        bundleUse: 10,
        bundleStock: 0,
        batchInspectionStock: 0,
      });

      const fileParseResult: FileParseResult = {
        succeedItems: Array(10)
          .fill(0)
          .map((_, i) => ({
            companyId: `company${i}`,
            companyName: `Company ${i}`,
          })),
        failedItems: [],
      };

      await expect(batchCreatorHelperBase.checkDiligenceBundleUsage(testUser, fileParseResult, BatchBusinessTypeEnums.Diligence_File)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('createBatchDiligenceByData - 成功创建尽调排查任务', async () => {
      // Mock依赖
      jest.spyOn(batchBaseHelper, 'checkBatchCount').mockResolvedValue(undefined);

      jest.spyOn(batchCreatorHelperBase, 'createBatchEntity').mockResolvedValue({
        batchId: 123,
        status: BatchStatusEnums.Waiting,
        businessType: BatchBusinessTypeEnums.Diligence_ID,
      } as any);

      jest.spyOn(batchBundleService, 'useOrglimitationCounter').mockResolvedValue(1);

      jest.spyOn(batchBundleService, 'bundleCounter').mockResolvedValue(1);

      const data = [
        { companyId: 'company1', companyName: 'Company One' },
        { companyId: 'company2', companyName: 'Company Two' },
      ];

      const result = await batchCreatorHelperBase.createBatchDiligenceByData(testUser, data, BatchBusinessTypeEnums.Diligence_ID, BatchTypeEnums.Import, {
        settingId: 123,
      });

      expect(result).toBeDefined();
      expect(result.batchId).toBe(123);
      expect(batchBundleService.useOrglimitationCounter).toHaveBeenCalled();
    });

    it('createBatchDiligenceByData - 创建失败抛出异常并回退额度', async () => {
      // Mock依赖
      jest.spyOn(batchBaseHelper, 'checkBatchCount').mockResolvedValue(undefined);

      jest.spyOn(batchCreatorHelperBase, 'createBatchEntity').mockRejectedValue(new BadRequestException(RoverExceptions.Batch.CreateFailed));

      jest.spyOn(batchBundleService, 'useOrglimitationCounter').mockResolvedValue(1);

      jest.spyOn(batchBundleService, 'bundleCounter').mockResolvedValue(1);

      const data = [
        { companyId: 'company1', companyName: 'Company One' },
        { companyId: 'company2', companyName: 'Company Two' },
      ];

      await expect(
        batchCreatorHelperBase.createBatchDiligenceByData(testUser, data, BatchBusinessTypeEnums.Diligence_ID, BatchTypeEnums.Import, { settingId: 123 }),
      ).rejects.toThrow(BadRequestException);

      // 检查是否进行了额度回退
      expect(batchBundleService.useOrglimitationCounter).toHaveBeenCalledTimes(2);
    });
  });

  // 集成测试部分
  describe('集成测试', () => {
    it.skip('集成测试-创建批量排查任务', async () => {
      // 模拟套餐服务
      const mockCounter = {
        increase: jest.fn().mockResolvedValue(true),
        decrease: jest.fn().mockResolvedValue(true),
        getStock: jest.fn().mockResolvedValue({ stock: 100 }),
        check: jest.fn().mockResolvedValue(true),
        clear: jest.fn().mockResolvedValue(true),
        getValue: jest.fn().mockResolvedValue(0),
        getLimit: jest.fn().mockResolvedValue(1000),
      };

      jest.spyOn(mockedBundleService, 'getBundleCounter').mockResolvedValue(mockCounter as any);

      jest.spyOn(mockedBundleService, 'getOrgBundleCounter').mockResolvedValue(mockCounter as any);

      jest.spyOn(mockedBundleService, 'getOrgLimitationCounter').mockResolvedValue(mockCounter as any);

      jest.spyOn(mockedBundleService, 'getBundle').mockResolvedValue({
        [RoverBundleCounterType.DiligenceCompanyQuantity]: { value: 1000 },
        [RoverBundleCounterType.DiligenceHistoryQuantity]: { value: 1000 },
        [RoverBundleCounterType.BatchInspectionQuantity]: { value: 100 },
      } as any);

      // 模拟发送消息
      jest.spyOn(batchBaseHelper, 'sendBatchMonitorMessage').mockResolvedValue('success');

      const fileParseResult: FileParseResult = {
        succeedItems: [
          { companyId: 'company1', companyName: 'Company One' },
          { companyId: 'company2', companyName: 'Company Two' },
        ],
        failedItems: [],
      };

      // 创建模拟组织设置
      const mockOrgSetting = new OrgSettingsLogEntity();
      mockOrgSetting.id = 123; // Changed from string to number
      mockOrgSetting.name = 'Test Setting';
      mockOrgSetting.orgId = testUser.currentOrg;
      // Removed invalid properties status and settingInfo that don't exist on OrgSettingsLogEntity

      jest.spyOn(settingsService, 'getOrgSettings').mockResolvedValue(mockOrgSetting);

      jest.spyOn(settingsService, 'getAllDimension').mockResolvedValue([Object.assign(new DimensionDefinitionPO(), { name: 'dim1', status: 1 })]);

      // 执行创建批量任务
      const batchEntity = await batchCreatorHelperBase.createBatchEntity(
        testUser,
        fileParseResult,
        BatchBusinessTypeEnums.Diligence_File,
        10,
        'test.xlsx',
        'http://example.com/test.xlsx',
        BatchTypeEnums.Import,
        { settingId: 123 },
      );

      expect(batchEntity).toBeDefined();
      expect(batchEntity.businessType).toBe(BatchBusinessTypeEnums.Diligence_File);
      expect(batchEntity.batchType).toBe(BatchTypeEnums.Import);
      expect(batchEntity.orgId).toBe(testUser.currentOrg);
      expect(batchEntity.creatorId).toBe(testUser.userId);
      expect(batchEntity.status).toBe(BatchStatusEnums.Waiting);
      expect(batchEntity.batchInfo).toEqual({ settingId: '123', settingName: 'Test Setting' });

      // 验证job创建
      const jobs = await batchJobRepo.find({ batchId: batchEntity.batchId });
      expect(jobs.length).toBeGreaterThan(0);
    });

    it.skip('集成测试-处理排查任务中的失败项', async () => {
      // 模拟套餐服务
      jest.spyOn(mockedBundleService, 'getBundleCounter').mockResolvedValue({
        increase: jest.fn().mockResolvedValue(true),
        decrease: jest.fn().mockResolvedValue(true),
        getStock: jest.fn().mockResolvedValue({ stock: 100 }),
        check: jest.fn().mockResolvedValue(true),
      } as any);

      // 模拟发送消息
      jest.spyOn(batchBaseHelper, 'sendBatchMonitorMessage').mockResolvedValue('success');

      jest.spyOn(batchCreatorHelperBase, 'checkDiligenceBundleUsage').mockResolvedValue({
        companyCounter: null,
        historyCounter: null,
        dailyCounter: null,
        withholdingCount: 0,
        withholdingRecordCount: 0,
      });

      const fileParseResult: FileParseResult = {
        succeedItems: [{ companyId: 'company1', companyName: 'Company One' }],
        failedItems: [
          {
            data: { companyId: 'company2', companyName: 'Company Two' },
            errorType: FieldParseErrorTypeEnums.Duplicated,
            errorMsg: '重复数据',
          },
          {
            data: { companyId: 'company3', companyName: 'Company Three' },
            errorType: FieldParseErrorTypeEnums.Failed_Valid,
            errorMsg: '数据验证失败',
          },
        ],
      };

      // 执行创建批量任务
      const batchEntity = await batchCreatorHelperBase.createBatchEntity(
        testUser,
        fileParseResult,
        BatchBusinessTypeEnums.Customer_File,
        10,
        'test.xlsx',
        'http://example.com/test.xlsx',
      );

      expect(batchEntity).toBeDefined();

      // 验证失败项记录
      const results = await batchResultRepo.find({ batchId: batchEntity.batchId });
      expect(results.length).toBe(2);
    });

    it('debug-检查无数据时抛出异常', async () => {
      jest.spyOn(batchBaseHelper, 'checkBatchCount').mockResolvedValue(undefined);

      const data = [];

      await expect(
        batchCreatorHelperBase.createBatchDiligenceByData(testUser, data, BatchBusinessTypeEnums.Diligence_ID, BatchTypeEnums.Import),
      ).rejects.toThrow(BadRequestException);
    });

    it('debug-测试getDiligenceAnalyzeCustomers获取数据', async () => {
      // 模拟数据库查询结果
      jest.spyOn(customerRepo, 'createQueryBuilder').mockReturnValue({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([
          { companyId: 'company1', name: 'Company One', groupId: 1 },
          { companyId: 'company2', name: 'Company Two', groupId: 2 },
        ]),
      } as any);

      const result = await batchCreatorHelperBase.getDiligenceAnalyzeCustomers(testUser);

      expect(result).toBeDefined();
      expect(result.length).toBe(2);
      expect(result[0].companyId).toBe('company1');
    });
  });
});
