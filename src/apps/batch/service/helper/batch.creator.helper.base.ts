import { BadRequestException, ForbiddenException, HttpException, Injectable } from '@nestjs/common';
import { RoverUser } from '../../../../libs/model/common';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import {
  BundleReachedLimitationException,
  ICounter,
  RoverBundleCounterType,
  RoverBundleEntityConfig,
  RoverBundleLimitationType,
  RoverBundleService,
} from '@kezhaozhao/saas-bundle-service';
import { CounterOperation } from '../../../../libs/constants/common';
import { PermissionByEnum } from '../../../../libs/enums/PermissionScopeEnum';
import { Brackets, Repository } from 'typeorm';
import { RoverExceptions } from '../../../../libs/exceptions/exceptionConstants';
import { BatchTypeEnums } from '../../../../libs/enums/batch/BatchTypeEnums';
import { BatchStatusEnums } from '../../../../libs/enums/batch/BatchStatusEnums';
import { BatchJobEntity } from '../../../../libs/entities/BatchJobEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchResultEntity } from '../../../../libs/entities/BatchResultEntity';
import { BatchBaseHelper } from './batch.base.helper';
import { BatchBundleService } from '../batch.bundle.service';
import { DiligenceHistoryEntity } from '../../../../libs/entities/DiligenceHistoryEntity';
import { SecurityService } from '../../../../libs/config/security.service';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { FileParseResult } from '../../../../libs/model/batch/po/parse/FileParseResult';
import { OrgSettingsLogEntity } from '../../../../libs/entities/OrgSettingsLogEntity';
import { UserConfigurationTypeEnums } from '../../../../libs/model/settings/UserConfigurationTypeEnums';
import { SettingTypeEnums } from '../../../../libs/model/settings/SettingTypeEnums';
import { ConfigurationTypeEnums } from '../../../../libs/model/settings/ConfigurationTypeEnums';
import { FieldParseErrorTypeEnums } from '../../../../libs/enums/batch/FieldParseErrorTypeEnums';
import { chunk, uniq } from 'lodash';
import { BatchJobInfoPO } from '../../../../libs/model/batch/po/BatchJobInfoPO';
import { BatchJobResultTypeEnums } from '../../../../libs/enums/batch/BatchJobResultTypeEnums';
import * as Bluebird from 'bluebird';
import { SettingsService } from '../../../settings/settings.service';
import { BatchResultExportService } from '../batch.result.export.service';
import { DimensionDefinitionPO } from '../../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { ParsedRecordBase } from '../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { BatchConstants } from '../../common/batch.constants';
import { CustomerEntity } from '../../../../libs/entities/CustomerEntity';
import { v4 } from 'uuid';
import { getUniqueJobResultKey } from '../../common/batch.utils';
import { getBundleStart } from '../../../../libs/utils/diligence/diligence.utils';
import { BatchInfoPO } from '../../model/BatchInfoPO';
import { CustomerBO } from '../../model/CustomerBO';
import { UserConfigurationService } from '../../../user_configuration/user.configuration.service';
import { GroupsEntity } from '../../../../libs/entities/GroupsEntity';
import { LabelEntity } from '../../../../libs/entities/LabelEntity';
import { PotentialHelperService } from 'apps/potential/helper/potential.helper.service';

@Injectable()
export class BatchCreatorHelperBase {
  private readonly logger = QccLogger.getLogger(BatchCreatorHelperBase.name);

  constructor(
    @InjectRepository(BatchEntity) protected readonly batchRepo: Repository<BatchEntity>,
    private readonly batchBaseHelperService: BatchBaseHelper,
    private readonly batchBundleService: BatchBundleService,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    private readonly securityService: SecurityService,
    private readonly bundleService: RoverBundleService,
    @InjectRepository(BatchJobEntity) private readonly batchJobRepo: Repository<BatchJobEntity>,
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
    @InjectRepository(GroupsEntity) private readonly groupRepo: Repository<GroupsEntity>,
    @InjectRepository(LabelEntity) private readonly labelRepo: Repository<LabelEntity>,
    private readonly settingService: SettingsService,
    private readonly resultExportService: BatchResultExportService,
    private readonly userConfigurationService: UserConfigurationService,
    private readonly potentialHelperService: PotentialHelperService,
  ) {}

  @TraceLog({ throwError: true })
  public async createBatchEntity(
    user: RoverUser,
    fileParseResult: FileParseResult,
    businessType: BatchBusinessTypeEnums,
    jobItemSize: number,
    fileName: string,
    originFileUrl: string,
    batchType?: BatchTypeEnums,
    params?: BatchInfoPO,
  ): Promise<BatchEntity> {
    const { succeedItems, failedItems, isUpdate } = fileParseResult;
    let batchEntity = null;
    const { companyCounter, historyCounter, dailyCounter, withholdingCount, withholdingRecordCount } = await this.checkDiligenceBundleUsage(
      user,
      fileParseResult,
      businessType,
    );
    const { potentialCounter, potentialCount } = await this.checkPotentialBundleUsage(user, fileParseResult, businessType);
    try {
      let orgSetting: OrgSettingsLogEntity;
      switch (businessType) {
        case BatchBusinessTypeEnums.Diligence_File:
        case BatchBusinessTypeEnums.Diligence_ID:
        case BatchBusinessTypeEnums.Diligence_Customer: {
          const settingId = params?.settingId;
          const userConfigVersion = (await this.userConfigurationService.getUserConfiguration(user, UserConfigurationTypeEnums.diligence_default_setting))
            ?.settingVersion;
          orgSetting = await this.settingService.getOrgSettings(user.currentOrg, SettingTypeEnums.diligence_risk, settingId, userConfigVersion);
          params = { settingId: orgSetting.id, settingName: orgSetting.name };
          break;
        }
        case BatchBusinessTypeEnums.Diligence_Customer_Analyze: {
          // const orgConfig = await this.settingService.getConfiguration(user.currentOrg, ConfigurationTypeEnums.diligence_analyze);
          // params = { ...params };
          // params = Object.assign(params, {});
          break;
        }
        case BatchBusinessTypeEnums.Potentail_Batch_Customer:
        case BatchBusinessTypeEnums.Potential_Batch_Data:
        case BatchBusinessTypeEnums.Potential_Batch_Excel:
        case BatchBusinessTypeEnums.Monitor_File:
        case BatchBusinessTypeEnums.Customer_File:
        case BatchBusinessTypeEnums.InnerBlacklist_File: {
          if (!params) {
            params = { type: businessType };
          }
          params.isUpdate = isUpdate;
          break;
        }
        default:
          break;
      }
      if (orgSetting) {
        await this.checkSetting(orgSetting);
      }
      batchEntity = await this.batchRepo.save(
        Object.assign(new BatchEntity(), {
          createDate: new Date(),
          orgId: user.currentOrg,
          depId: user.departments?.[0],
          creatorId: user.userId,
          batchType: batchType || BatchTypeEnums.Import,
          businessType,
          recordCount: succeedItems.length + failedItems.length,
          status: succeedItems.length > 0 ? BatchStatusEnums.Waiting : BatchStatusEnums.Error,
          comment: succeedItems.length === 0 ? '未识别到符合格式的数据' : '',
          fileName,
          originFile: originFileUrl,
          statisticsInfo: {
            recordCount: succeedItems.length + failedItems.length,
            duplicatedCount: failedItems.filter((item) => item.errorType == FieldParseErrorTypeEnums.Duplicated).length,
            withholdingCount,
            withholdingRecordCount,
          },
          batchInfo: params,
        }),
      );
      if (succeedItems.length > 0) {
        // 生成job并保存到数据库
        const chunks = chunk(succeedItems, jobItemSize);
        const jobItems: BatchJobEntity[] = chunks.map((chunk) => {
          const jobInfo: BatchJobInfoPO = {
            items: chunk.map((t) => {
              // @ts-ignore 防止前端传参进来时候使用了首字母大写
              t.companyId = t.companyId || t.CompanyId;
              // @ts-ignore
              t.companyName = t.companyName || t.CompanyName;
              t.recordHashkey = v4();
              t.recordParams = t.recordParams || {
                settingId: orgSetting?.id,
              };
              return t;
            }),
            itemSize: chunk.length,
          };
          return Object.assign(new BatchJobEntity(), {
            batchId: batchEntity.batchId,
            jobInfo,
            status: BatchStatusEnums.Waiting,
          });
        });
        //批量保存 size=50
        await Bluebird.map(chunk(jobItems, 100), (chunkData) => this.batchJobRepo.insert(chunkData), { concurrency: 5 });
        // 发送消息到 batch monitor 队列，3s后开始扫描 batch_job表并处理任务
        const messageResponse = await this.batchBaseHelperService.sendBatchMonitorMessage(batchEntity, user);
        this.logger.info(`batch(batchId=${batchEntity.batchId}) monitor message sent..., response=${messageResponse}`);
      }
      if (failedItems.length > 0) {
        // await this.batchResultRepo.insert(
        await this.batchBaseHelperService.handleJobResult(
          failedItems.map((f) => {
            const resultEntity = Object.assign(new BatchResultEntity(), {
              batchId: batchEntity.batchId,
              comment: f.errorMsg || f.errorType,
              resultInfo: f.data,
              resultHashkey: getUniqueJobResultKey(batchEntity.batchId, null),
              jobId: 0,
            });
            if (f.errorType == FieldParseErrorTypeEnums.Duplicated) {
              resultEntity.resultType = BatchJobResultTypeEnums.SUCCEED_DUPLICATED;
            } else if (f.errorType == FieldParseErrorTypeEnums.Failed_Valid) {
              resultEntity.resultType = BatchJobResultTypeEnums.FAILED_VERIFICATION;
            } else {
              resultEntity.resultType = BatchJobResultTypeEnums.FAILED_UNMATCHED;
            }
            return resultEntity;
          }),
        );
      }
      // 失败也生成结果文件
      if (batchEntity.status == BatchStatusEnums.Error) {
        const url = await this.resultExportService.generateResultFile(batchEntity.batchId, businessType);
        if (url) {
          batchEntity.resultFile = url;
          await this.batchRepo.save(batchEntity);
        }
      }
      return batchEntity;
    } catch (e) {
      this.logger.error(e);
      if (companyCounter) {
        // 退回 公司数量额度
        await companyCounter.decrease(withholdingCount);
      }
      if (historyCounter) {
        // 退回 排查记录数量额度
        await historyCounter.decrease(withholdingRecordCount);
      }
      if (dailyCounter) {
        // 退回 每日尽调上限校验 额度
        await dailyCounter?.decrease(withholdingRecordCount);
      }
      if (potentialCounter) {
        // 退回 潜在客户额度
        await potentialCounter.decrease(potentialCount);
      }
      await this.batchBaseHelperService.rollBackBatch(batchEntity, e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.CreateFailed,
        internalMessage: e.message,
      });
    }
  }

  //检查是否关闭了所有维度
  private async checkSetting(orgSetting: OrgSettingsLogEntity) {
    const allDefs: DimensionDefinitionPO[] = await this.settingService.getAllDimension(orgSetting, true, false);
    // 过滤未开启的维度
    const definitions: DimensionDefinitionPO[] = allDefs.filter((a) => a?.status > 0);
    if (!definitions?.length) {
      throw new BadParamsException(RoverExceptions.Diligence.Setting.Empty);
    }
  }

  /**
   * 验证是否有足够的套餐余量来执行批量排查任务
   * 如果 businessType=Diligence_Customer_Analyze，可以忽略data的值，通过表关联的方式进行判断需要付费的尽调会更高效
   * @param user
   * @param data 即将进行批量排查的记录
   * @param businessType
   * @param isRetry
   */
  public async checkDiligenceBundleStock(user: RoverUser, data: FileParseResult, businessType: BatchBusinessTypeEnums, isRetryBatch?: false) {
    const { succeedItems } = data;
    let shouldPaidCompanyCount = 0;
    let bundleStock = 0;
    let checkDiligence = false;
    let companyIds = uniq(succeedItems?.map((x) => x.companyId));
    let batchInspectionStock = -1;

    switch (businessType) {
      case BatchBusinessTypeEnums.Diligence_File:
      case BatchBusinessTypeEnums.Diligence_ID:
      case BatchBusinessTypeEnums.Diligence_Customer: {
        checkDiligence = true;
        break;
      }
      case BatchBusinessTypeEnums.Diligence_Customer_Analyze: {
        if (!isRetryBatch && companyIds.length == 0) {
          const allCustomer = await this.getDiligenceAnalyzeCustomers(user);
          if (!allCustomer?.length) {
            //第三方列表数据为空时，不进行全量排查
            throw new BadRequestException(RoverExceptions.Batch.ThirdPartyEmpty);
          }
          companyIds = allCustomer.map((x) => x.companyId);
        }
        checkDiligence = true;
        break;
      }
      default:
        break;
    }

    if (checkDiligence) {
      const companyCount = companyIds.length;
      const existedCount = await this.getExistedDiligenceCount(user, companyIds);
      shouldPaidCompanyCount = companyCount - existedCount;
      const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(user);
      if (userBundle[RoverBundleCounterType.DiligenceCompanyQuantity].value !== -1) {
        //如果需要计费，需要检测套餐是否还有余量
        const companyCounter = await this.bundleService.getBundleCounter(user, RoverBundleCounterType.DiligenceCompanyQuantity);
        bundleStock = (await companyCounter.getStock()).stock;
      }
      if (userBundle[RoverBundleCounterType.BatchInspectionQuantity].value !== -1) {
        const batchCounter = await this.bundleService.getBundleCounter(user, RoverBundleCounterType.BatchInspectionQuantity);
        batchInspectionStock = (await batchCounter.getStock()).stock;
      }
    }
    return {
      /**
       * 需要执行记录总条数
       */
      companyCount: companyIds.length,
      /**
       * 需要消耗的额度
       */
      bundleUse: shouldPaidCompanyCount,
      /**
       * 当前剩余库存额度
       */
      bundleStock,
      /**
       * 年检剩余库存额度
       */
      batchInspectionStock,
    };
  }

  private async getExistedDiligenceCount(user: RoverUser, companyIds?: string[]) {
    const { currentOrg: orgId, bundle } = user;
    const chunks = chunk(uniq(companyIds), 50);
    const bundleStartDate = getBundleStart(bundle.startDate);
    let existCount = 0;
    // const paidCompanyIds = [];
    if (companyIds?.length) {
      await Bluebird.map(
        chunks,
        async (chunk) => {
          const qb = this.diligenceHistoryRepo
            .createQueryBuilder('history')
            .select('COUNT(distinct history.companyId) as count')
            .andWhere('history.orgId = :orgId', { orgId })
            .andWhere('history.companyId in (:...companyIds)', { companyIds: chunk })
            .andWhere('history.operator != -1')
            .andWhere('history.createDate > :bundleStartDate', { bundleStartDate });
          // const chunkPaidCompanyIds = (await qb.getRawMany()).map((x) => x.companyId);
          // paidCompanyIds.push(...chunkPaidCompanyIds);
          const { count } = await qb.getRawOne();
          existCount += +count;
        },
        { concurrency: 10 },
      );
    }
    return existCount;
    // return uniq(paidCompanyIds).length;
  }

  /**
   * 创建批量排查任务时(当时排查相关的批量任务的时候)，先检查参数并预估套餐用量
   * @param user
   * @param fileParseResult
   * @param businessType
   * @private
   */
  public async checkDiligenceBundleUsage(user: RoverUser, fileParseResult: FileParseResult, businessType: BatchBusinessTypeEnums, isRetryBatch?: false) {
    // case BatchBusinessTypeEnums.Diligence_File:
    // case BatchBusinessTypeEnums.Diligence_ID:
    // case BatchBusinessTypeEnums.Diligence_Customer: {
    //
    //   }
    const targetBusinessTypes = [
      BatchBusinessTypeEnums.Diligence_File,
      BatchBusinessTypeEnums.Diligence_ID,
      BatchBusinessTypeEnums.Diligence_Customer,
      BatchBusinessTypeEnums.Diligence_Customer_Analyze,
    ];
    if (!targetBusinessTypes.includes(businessType)) {
      return {
        companyCounter: null,
        historyCounter: null,
        dailyCounter: null,
        withholdingCount: 0,
        withholdingRecordCount: 0,
      };
    }
    let companyCounter: ICounter = null;
    let historyCounter: ICounter = null;
    let dailyCounter: ICounter = null;
    // 需要消耗的排查公司额度
    let withholdingCount = 0;
    // 需要消耗排查次数额度
    let withholdingRecordCount = 0;
    const increasedCounters = [];
    try {
      const checkResult = await this.checkDiligenceBundleStock(user, fileParseResult, businessType, isRetryBatch);
      withholdingCount = checkResult.bundleUse;
      withholdingRecordCount = checkResult.companyCount;
      if (withholdingRecordCount > 0) {
        const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(user);
        // 因为 catch 中 对 error的需求，代码执行顺序要保证：判断是否套餐计费，再判断是否单独计费，再最后判断是否超过每日尽调次数
        if (withholdingCount > 0) {
          if (userBundle[RoverBundleCounterType.DiligenceCompanyQuantity].value !== -1) {
            //如果需要计费，需要检测套餐是否还有余量
            companyCounter = await this.bundleService.getBundleCounter(user, RoverBundleCounterType.DiligenceCompanyQuantity);
            // await companyCounter.clear();
            await companyCounter.increase(withholdingCount);
            increasedCounters.push('companyCounter');
          }
        }
        if (userBundle[RoverBundleCounterType.DiligenceHistoryQuantity].value !== -1) {
          // 说明开启了 按排查次数数量 计费模式,
          historyCounter = await this.bundleService.getOrgBundleCounter(user, RoverBundleCounterType.DiligenceHistoryQuantity);
          await historyCounter.increase(withholdingRecordCount);
          increasedCounters.push('historyCounter');
        }
        // 每日尽调上限校验
        dailyCounter = await this.bundleService.getOrgLimitationCounter(user, RoverBundleLimitationType.DiligenceDailyQuantity);
        await dailyCounter.increase(withholdingRecordCount);
        increasedCounters.push('dailyCounter');
      }
    } catch (e) {
      if (increasedCounters.includes('companyCounter')) {
        // 退回 公司数量额度
        await companyCounter.decrease(withholdingCount);
      }
      if (increasedCounters.includes('historyCounter')) {
        // 退回 排查记录数量额度
        await historyCounter.decrease(withholdingRecordCount);
      }
      if (increasedCounters.includes('dailyCounter')) {
        // 退回 每日尽调上限校验 额度
        await dailyCounter?.decrease(withholdingRecordCount);
      }
      if (e instanceof BundleReachedLimitationException) {
        let remainCount = 0;
        let errorType;
        let holdingCount;
        if (dailyCounter) {
          remainCount = (await dailyCounter.getStock()).stock;
          holdingCount = withholdingRecordCount;
          errorType = '(每日排查次数)';
        } else if (historyCounter) {
          remainCount = (await historyCounter.getStock()).stock;
          holdingCount = withholdingRecordCount;
          errorType = '(排查次数)';
        } else if (companyCounter) {
          remainCount = (await companyCounter.getStock()).stock;
          holdingCount = withholdingCount;
          errorType = '(公司数量)';
        }
        throw new ForbiddenException({
          code: 400008,
          error: `剩余额度不足！本次排查预计消耗额度：${holdingCount}家，当前剩余额度：${remainCount}家${errorType}.`,
        });
      } else {
        throw e;
      }
    }
    return {
      companyCounter,
      historyCounter,
      dailyCounter,
      withholdingCount,
      withholdingRecordCount,
    };
  }

  /**
   * 创建批量排查任务时(当时排查相关的批量任务的时候)，先检查参数并预估套餐用量
   * @param user
   * @param fileParseResult
   * @param businessType
   * @private
   */
  public async checkPotentialBundleUsage(user: RoverUser, fileParseResult: FileParseResult, businessType: BatchBusinessTypeEnums) {
    const targetBusinessTypes = [
      BatchBusinessTypeEnums.Potential_Batch_Data,
      BatchBusinessTypeEnums.Potential_Batch_Excel,
      BatchBusinessTypeEnums.Potentail_Batch_Customer,
    ];
    const successCount = fileParseResult.succeedItems?.length || 0;
    if (!targetBusinessTypes.includes(businessType) || !successCount) {
      return {
        companyCounter: null,
        historyCounter: null,
        dailyCounter: null,
        withholdingCount: 0,
        withholdingRecordCount: 0,
      };
    }
    let potentialCounter: ICounter = null;
    const potentialSetting = await this.settingService.getOrgSettings(user.currentOrg, SettingTypeEnums.potential);
    const potentialCount = this.potentialHelperService.getPaidCount(potentialSetting) * successCount;
    const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(user);
    if (userBundle[RoverBundleCounterType.PotentialDiligenceQuantity]?.value !== -1) {
      potentialCounter = await this.bundleService.getBundleCounter(user, RoverBundleCounterType.PotentialDiligenceQuantity);
      // 批量数据预扣额度
      await potentialCounter.increase(potentialCount);
    }
    return { potentialCounter, potentialCount };
  }

  /**
   * 创建批量排查任务
   *
   * @param user
   * @param data 如果 businessType = Diligence_Customer_Analyze，则需要先查询第三方列表，然后再创建批量任务
   * @param businessType
   * @returns
   */
  @TraceLog({ throwError: true })
  async createBatchDiligenceByData(
    user: RoverUser,
    data: ParsedRecordBase[],
    businessType: BatchBusinessTypeEnums,
    batchType?: BatchTypeEnums,
    params?: BatchInfoPO,
  ) {
    const { fromSystem, settingId } = params || {};
    await this.batchBaseHelperService.checkBatchCount(user, businessType);
    const jobItemSize = BatchConstants.getJobItemSize(businessType);
    let fileName = '-';
    let records = data;
    switch (businessType) {
      case BatchBusinessTypeEnums.Potential_Batch_Excel: {
        fileName = '从Excel文件中导入';
        await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Increase, 1);
        break;
      }
      case BatchBusinessTypeEnums.Potential_Batch_Data:
      case BatchBusinessTypeEnums.Diligence_ID: {
        await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Increase, 1);
        fileName = '输入框手动填写';
        break;
      }
      case BatchBusinessTypeEnums.Potentail_Batch_Customer:
      case BatchBusinessTypeEnums.Diligence_Customer: {
        await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Increase, 1);
        fileName = '从第三方列表中选择';
        break;
      }
      case BatchBusinessTypeEnums.Diligence_Customer_Analyze: {
        fileName = '全量风险排查巡检';
        // 定时系统巡检任务，不校验每日上限
        if (!fromSystem) {
          // 全量风险排查巡检 每日上限
          await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchInspectionDailyQuantity, CounterOperation.Increase, 1);
        }
        //   全量风险排查巡检 套餐总次数上限
        await this.batchBundleService.bundleCounter(user, RoverBundleCounterType.BatchInspectionQuantity, CounterOperation.Increase, 1);
        // 原方案采用先按 groupId 分组，再寻找对应 versionSetting，这样处理会遗漏 groupId 为 null 的情况，
        const allCustomers = await this.getDiligenceAnalyzeCustomers(user, fromSystem);
        //第三方列表数据为空时，不进行全量排查
        if (!allCustomers?.length) {
          throw new BadRequestException(RoverExceptions.Batch.ThirdPartyEmpty);
        }
        const defaultOrgSetting: OrgSettingsLogEntity = await this.settingService.getOrgSettings(user.currentOrg, SettingTypeEnums.diligence_risk, settingId);
        const allSettingVersions: Set<number> = new Set();
        allCustomers.forEach((c) => {
          if (c.settingVersion) {
            allSettingVersions.add(c.settingVersion);
          }
        });
        const versionSettings: { settingVersion: number; orgSetting: OrgSettingsLogEntity }[] = [];
        if (allSettingVersions.size > 0) {
          // 存在指定 version 情况
          await Bluebird.map(Array.from(allSettingVersions), async (settingVersion: number) => {
            const groupOrgSetting: OrgSettingsLogEntity = await this.settingService.getOrgSettings(
              user.currentOrg,
              SettingTypeEnums.diligence_risk,
              settingId,
              settingVersion,
            );
            versionSettings.push({ settingVersion, orgSetting: groupOrgSetting });
          });
        }
        records = allCustomers.map((c) => {
          const settingVersion = c.settingVersion;
          const settingsLogEntity = versionSettings.find((x) => x.settingVersion === settingVersion)?.orgSetting || defaultOrgSetting;
          return {
            companyId: c.companyId,
            companyName: c.name,
            recordParams: {
              settingId: settingsLogEntity.id,
            },
          };
        });
        break;
      }
    }
    if (records.length < 1) {
      throw new BadRequestException(RoverExceptions.BadParams.Common);
    }
    let batchEntity = null;
    try {
      batchEntity = await this.createBatchEntity(
        user,
        {
          // succeedItems: records.map((d) => {
          //   return { companyId: d.companyId, companyName: d.companyName };
          // }),
          succeedItems: records,
          failedItems: [],
        },
        businessType,
        jobItemSize,
        fileName,
        '',
        batchType,
        params,
      );
      // if (BatchBusinessMessageTitle[businessType]) {
      //   this.messageService.addMessage({
      //     title: BatchBusinessMessageTitle[businessType],
      //     content: '后台正在处理中，排查完成后我们将第一时间提醒您',
      //     userId,
      //     msgType: MsgType.TaskMsg,
      //     objectId: batchEntity.batchId + '',
      //   });
      // }
      return batchEntity;
    } catch (e) {
      switch (businessType) {
        case BatchBusinessTypeEnums.Potential_Batch_Data:
        case BatchBusinessTypeEnums.Potential_Batch_Excel:
        case BatchBusinessTypeEnums.Potentail_Batch_Customer:
        case BatchBusinessTypeEnums.Diligence_ID:
        case BatchBusinessTypeEnums.Diligence_Customer: {
          await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Decrease, 1);
          break;
        }

        case BatchBusinessTypeEnums.Diligence_Customer_Analyze: {
          await Bluebird.all([
            // 全量风险排查巡检 每日上限
            this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchInspectionDailyQuantity, CounterOperation.Decrease, 1),
            //   全量风险排查巡检 套餐总次数上限
            this.batchBundleService.bundleCounter(user, RoverBundleCounterType.BatchInspectionQuantity, CounterOperation.Decrease, 1),
          ]);
          break;
        }
      }
      if (e instanceof HttpException) {
        throw e;
      }
      this.logger.error(e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.CreateFailed,
        internalMessage: e.message,
      });
    }
  }

  /**
   * TODO 需要考虑客户列表很多的时候，分页循环获取数据
   * @param user
   * @param fromSystem  true-系统任务获取数据，例如定时巡检任务
   * @returns
   */
  public async getDiligenceAnalyzeCustomers(user: RoverUser, fromSystem = false) {
    const { currentOrg: orgId, userId } = user;
    const orgConfig = await this.settingService.getConfiguration(orgId, ConfigurationTypeEnums.diligence_analyze);
    const allCustomers: CustomerBO[] = [];
    const [groups, labels] = await Bluebird.all([
      this.groupRepo.find({
        orgId,
        groupType: 1,
      }),
      this.labelRepo.find({ orgId, labelType: 1 }),
    ]);
    //默认带上默认分组
    const groupIds = [-1, ...groups.map((g) => g.groupId)];
    const labelIds = labels.map((l) => l.labelId);
    if (orgConfig.configs?.length) {
      await Bluebird.map(
        orgConfig.configs,
        async (config) => {
          // 查询每个方案的公司列表
          const qb = this.customerRepo
            .createQueryBuilder('customer')
            .leftJoinAndSelect('customer.labels', 'label')
            .select(['customer.name', 'customer.companyId', 'customer.groupId'])
            .where('customer.orgId = :orgId', { orgId })
            .andWhere('customer.status = :status', { status: BatchStatusEnums.Done });
          if (!fromSystem) {
            // 系统定时任务，不校验权限
            const { by, userIds } = this.securityService.checkScope(user, 2013);
            if (by == PermissionByEnum.USER) {
              qb.andWhere('customer.createBy in (:...userIds)', { userIds });
            }
          }
          //group可能已经被删除了，但是 groupId 还设置在 config.groupIds中
          //获取config.groupIds中存在的group
          const existGroupIds = config.groupIds?.filter((groupId) => groupIds.includes(groupId));
          if (existGroupIds?.length) {
            if (existGroupIds?.includes(-1) && existGroupIds?.length === 1) {
              qb.andWhere('customer.groupId is null');
            }
            if (!existGroupIds?.includes(-1)) {
              qb.andWhere('customer.groupId in (:...groupIds)', { groupIds: existGroupIds });
            }
            if (existGroupIds?.includes(-1) && existGroupIds?.length > 1) {
              const filterGroupIds = existGroupIds.filter((r) => r >= 0);
              qb.andWhere(
                new Brackets((qb1) => {
                  qb1.orWhere('customer.groupId is null');
                  qb1.orWhere('customer.groupId in (:...filterGroupIds)', { filterGroupIds });
                }),
              );
            }
          }
          //label可能已经被删除了，但是 labelId 还设置在 config.labelIds中
          //获取config.labelIds中存在的label
          const existLabelIds = config.labelIds?.filter((labelId) => labelIds.includes(labelId));
          if (existLabelIds?.length) {
            qb.andWhere('label.labelId in (:...labelIds)', { labelIds: existLabelIds });
          }
          const customers = await qb.getMany();
          customers.forEach((c) => allCustomers.push(Object.assign(new CustomerBO(), c, { settingVersion: config.settingVersion })));
        },
        { concurrency: 3 },
      );
    } else {
      const qb = this.customerRepo
        .createQueryBuilder('customer')
        .leftJoinAndSelect('customer.labels', 'label')
        .select(['customer.name', 'customer.companyId', 'customer.groupId'])
        .where('customer.orgId = :orgId', { orgId })
        .andWhere('customer.status = :status', { status: BatchStatusEnums.Done });
      if (!fromSystem) {
        // 系统定时任务，不校验权限
        const { by, userIds } = this.securityService.checkScope(user, 2013);
        if (by == PermissionByEnum.USER) {
          qb.andWhere('customer.createBy in (:...userIds)', { userIds });
        }
      }
      const customers = await qb.getMany();
      customers.forEach((c) => allCustomers.push(Object.assign(new CustomerBO(), c)));
    }
    return allCustomers;
  }
}
