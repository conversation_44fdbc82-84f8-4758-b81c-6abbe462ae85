import { BadRequestException, HttpException, Injectable } from '@nestjs/common';
import { RoverUser } from '../../../../libs/model/common';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { FileParseResult } from '../../../../libs/model/batch/po/parse/FileParseResult';
import { BatchConstants } from '../../common/batch.constants';
import { RoverBundleLimitationType } from '@kezhaozhao/saas-bundle-service';
import { CounterOperation } from '../../../../libs/constants/common';
import { v1 as uuidv1 } from 'uuid';
import { RoverExceptions } from '../../../../libs/exceptions/exceptionConstants';
import { BatchBaseHelper } from './batch.base.helper';
import { BatchBundleService } from '../batch.bundle.service';
import { DiligenceFileParser } from '../../file.parser/DiligenceFileParser';
import { CustomerImportFileParser } from '../../file.parser/CustomerImportFileParser';
import { PersonImportFileParser } from '../../file.parser/PersonImportFileParser';
import { InnerBlacklistImportFileParser } from '../../file.parser/InnerBlacklistImportFileParser';
import { MonitorImportFileParser } from '../../file.parser/MonitorImportFileParser';
import { BiddingDiligenceFileParser } from '../../file.parser/BiddingDiligenceFileParser';
import { CustomerService } from '../../../customer/customer.service';
import { PersonService } from '../../../person/person.service';
import { BlacklistInnerService } from '../../../blacklist/blacklist.inner.service';
import { MonitorCompanyService } from '../../../monitor/monitor.company.service';
import { BatchResultExportService } from '../batch.result.export.service';
import { BatchCreatorHelperBase } from './batch.creator.helper.base';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BiddingDiligenceService } from '../../../bidding/service/bidding.diligence.service';
import { SpecificFileParser } from '../../file.parser/SpecificFileParser';
import { SpecificFacadeService } from '../../../specific/specific.facade.service';
import { getExceptionDescription } from '../../../../libs/utils/diligence/diligence.utils';

@Injectable()
export class BatchCreatorHelperFile {
  private readonly logger = QccLogger.getLogger('BatchCreatorHelperFile');

  constructor(
    private readonly batchBaseHelperService: BatchBaseHelper,
    private readonly batchBundleService: BatchBundleService,
    private readonly diligenceFileParser: DiligenceFileParser,
    private readonly customerFileParser: CustomerImportFileParser,
    private readonly personFileParser: PersonImportFileParser,
    private readonly innerBlacklistImportFileParser: InnerBlacklistImportFileParser,
    private readonly monitorImportFileParser: MonitorImportFileParser,
    private readonly biddingDiligenceFileParser: BiddingDiligenceFileParser,
    private readonly specificFileParser: SpecificFileParser,
    private readonly customerService: CustomerService,
    private readonly personService: PersonService,
    private readonly blacklistInnerService: BlacklistInnerService,
    private readonly biddingService: BiddingDiligenceService,
    private readonly monitorCompanyService: MonitorCompanyService,
    private readonly resultExportService: BatchResultExportService,
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
    private readonly specificFacadeService: SpecificFacadeService,
  ) {}

  async checkBatchBiddingDiligenceLimint(user: RoverUser, filePath: string) {
    // 批量招标排查
    const fileParseResult = await this.biddingDiligenceFileParser.parseFile(user, filePath);
    const bundleStock = await this.biddingService.getStock(user);
    let errorMsg = fileParseResult?.failedItems?.[0]?.errorMsg || '';
    //如果库存不足，直接返回错误信息
    if (bundleStock < fileParseResult.paid) {
      errorMsg = `招标排查额度不足，剩余数量${bundleStock}`;
    }

    return {
      /**
       * 错误信息
       */
      errorMsg,
      /**
       * 需要执行记录总条数
       */
      itemCount: fileParseResult.succeedItems.length,
      /**
       * 需要消耗的额度
       */
      bundleUse: fileParseResult.paid,
      /**
       * 当前剩余库存额度
       */
      bundleStock,
    };
  }

  async createBatchByFile(user: RoverUser, filePath: string, fileName: string, businessType: BatchBusinessTypeEnums) {
    // let batchEntity: BatchEntity = null;
    // 当前已有离线任务正在进行，请勿重复进行操作！
    await this.batchBaseHelperService.checkBatchCount(user, businessType);

    try {
      // const promiseList: Promise<any>[] = [];
      let fileParseResult: FileParseResult = null;
      const jobItemSize = BatchConstants.getJobItemSize(businessType); //通过调整大小还控制 最终的job的数量（一个job对应一个mq message.handler）
      switch (businessType) {
        case BatchBusinessTypeEnums.Diligence_File: {
          // 批量排查每天的次数上限卡控
          await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Increase, 1);
          fileParseResult = await this.diligenceFileParser.parseFile(user, filePath);
          break;
        }
        case BatchBusinessTypeEnums.Customer_File: {
          fileParseResult = await this.customerFileParser.parseFile(user, filePath);
          await this.customerService.checkCountLimit(user, fileParseResult.succeedItems.length);
          break;
        }
        case BatchBusinessTypeEnums.Person_File: {
          //人员导入，要优先处理主要人员，主要人员导入成功，既可导入亲属
          fileParseResult = await this.personFileParser.parseFile(user, filePath);
          await this.personService.checkCountLimit(user, fileParseResult.succeedItems.length);
          break;
        }
        case BatchBusinessTypeEnums.InnerBlacklist_File: {
          //内部黑名单导入
          fileParseResult = await this.innerBlacklistImportFileParser.parseFile(user, filePath);
          await this.blacklistInnerService.checkCountLimit(user, fileParseResult.succeedItems.length);
          break;
        }
        case BatchBusinessTypeEnums.Bidding_Diligence_File: {
          // 批量招标排查
          fileParseResult = await this.biddingDiligenceFileParser.parseFile(user, filePath);
          await this.biddingService.checkCountLimit(user, fileParseResult.paid);
          break;
        }
        case BatchBusinessTypeEnums.Specific_Diligence_File: {
          // 批量特定利益关系排查
          fileParseResult = await this.specificFileParser.parseFile(user, filePath);
          await this.specificFacadeService.checkCountLimit(user, fileParseResult.paid);
          break;
        }
        case BatchBusinessTypeEnums.Monitor_File: {
          //合作监控公司导入
          fileParseResult = await this.monitorImportFileParser.parseFile(user, filePath);
          await this.monitorCompanyService.checkCountLimit(user, fileParseResult.succeedItems.length);
          break;
        }
        default: {
          const msg = `unknown batch business type: ${businessType}`;
          this.logger.warn(msg);
          throw new Error(msg);
        }
      }
      let extension = '';
      if (fileName.lastIndexOf('.') > 0) {
        extension = fileName.substring(fileName.lastIndexOf('.'), fileName.length);
      }
      const originFileUrl = await this.resultExportService.putFileToOss(filePath, uuidv1() + extension, fileName);
      return this.batchCreatorHelperService.createBatchEntity(user, fileParseResult, businessType, jobItemSize, fileName, originFileUrl);

      // if (BatchBusinessMessageTitle[businessType]) {
      //   const messageBody: MessageBody = {
      //     title: BatchBusinessMessageTitle[businessType],
      //     content: `您上传的<b>${batchEntity.fileName}</b>文件导入任务-正在执行，请耐心等待！`,
      //     userId: user.userId,
      //     msgType: MsgType.TaskMsg,
      //     objectId: batchEntity.batchId + '',
      //     url: `/tasklist/import-task-list?batchId=${batchEntity?.batchId}`,
      //   };
      //   if (businessType === BatchBusinessTypeEnums.Diligence_File) {
      //     delete messageBody.url;
      //   }
      // }
      // return batchEntity;
    } catch (e) {
      if (businessType == BatchBusinessTypeEnums.Diligence_File) {
        await this.batchBundleService.useOrglimitationCounter(user, RoverBundleLimitationType.BatchDiligenceDailyQuantity, CounterOperation.Decrease, 1);
      }
      if (e instanceof HttpException) {
        throw e;
      }
      this.logger.error(e);
      throw new BadRequestException({
        ...RoverExceptions.Batch.CreateFailed,
        internalMessage: e.message,
      });
    }
  }

  async checkBatchSpecificDiligenceLimit(user: RoverUser, filePath: string) {
    // 批量特定利益关系排查
    const fileParseResult = await this.specificFileParser.parseFile(user, filePath);
    const bundleStock = await this.specificFacadeService.getStock(user);

    return {
      /**
       * 需要执行记录总条数
       */
      itemCount: fileParseResult.succeedItems.length,
      /**
       * 需要消耗的额度
       */
      bundleUse: fileParseResult.paid,
      /**
       * 当前剩余库存额度
       */
      bundleStock,
    };
  }
}
