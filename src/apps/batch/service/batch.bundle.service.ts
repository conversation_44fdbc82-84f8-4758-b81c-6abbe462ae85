import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { RoverUser } from 'libs/model/common';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { In, MoreThan, Repository } from 'typeorm';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { BatchStatusEnums } from 'libs/enums/batch/BatchStatusEnums';
import { ConfigService } from 'libs/config/config.service';
import * as moment from 'moment';
import { ICounter, RoverBundleCounterType, RoverBundleEntityConfig, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { CounterOperation } from 'libs/constants/common';
import { UserService } from '../../user/user.service';

@Injectable()
export class BatchBundleService {
  constructor(
    configService: ConfigService,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    private readonly bundleService: RoverBundleService,
    private readonly userService: UserService,
  ) {
    this.bundleService.setSyncFunctions({
      // [RoverBundleCounterType.BatchInspectionQuantity]: { fn: this.syncInspectionCount.bind(this) },
      [RoverBundleLimitationType.BatchInspectionDailyQuantity]: { fn: this.syncInspectionDailyCount.bind(this) },
      // [RoverBundleCounterType.DiligenceReportQuantity]: { fn: this.syncReportCount.bind(this) },
      [RoverBundleLimitationType.BatchDiligenceDailyQuantity]: { fn: this.syncBatchDiligenceDailyCount.bind(this) },
      [RoverBundleLimitationType.DiligenceExportDailyQuantity]: { fn: this.syncDiligenceExportDailyCount.bind(this) },
    });
  }

  /**
   * 获取batchId对应的counter并处理对应的数量
   *
   * @param batchId
   * @param counterType
   * @param quantity  >0 增加数量  <0 减少数量
   */
  public async handleBatchCounterEvent(batchId: number, counterType: RoverBundleCounterType, quantity: number) {
    if (quantity !== 0) {
      const counter = await this.getBundleCounterByBatch(batchId, counterType);
      await counter.increase(quantity);
    }
  }

  /**
   * 根据batch id 获取该batch 对应的创建者的bundle counter
   * @param batchId
   * @param counterType
   */
  public async getBundleCounterByBatch(batchId: number, counterType: RoverBundleCounterType) {
    const batchEntity = await this.batchRepo.findOne(batchId);
    if (!batchEntity) {
      return null;
    }
    const user = await this.userService.getRoverUser(batchEntity.creatorId, batchEntity.orgId);
    const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(user);
    if (userBundle[counterType].value !== -1) {
      const counter = await this.bundleService.getBundleCounter(user, counterType);
      return counter;
    }
    return null;
  }

  // private async syncReportCount(orgId: number) {
  //   const bundle = await this.bundleService.getOrgBundle(orgId);
  //   const batchReportCount = await this.batchRepo.count({
  //     businessType: BatchBusinessTypeEnums.Diligence_Report_Export,
  //     orgId,
  //     status: BatchStatusEnums.Done,
  //     createDate: Between(bundle.activeDate, bundle.expireDate),
  //   });
  //   return batchReportCount;
  // }

  // private async syncInspectionCount(orgId: number) {
  //   const bundle = await this.bundleService.getOrgBundle(orgId);
  //   const batchInspectionCount = await this.batchRepo.count({
  //     businessType: BatchBusinessTypeEnums.Diligence_Customer_Analyze,
  //     orgId,
  //     status: BatchStatusEnums.Done,
  //     createDate: Between(bundle.activeDate, bundle.expireDate),
  //   });
  //   return batchInspectionCount;
  // }

  private async syncInspectionDailyCount(orgId: number) {
    return this.batchRepo.count({
      businessType: BatchBusinessTypeEnums.Diligence_Customer_Analyze,
      orgId,
      status: BatchStatusEnums.Done,
      createDate: MoreThan(moment().startOf('day').toDate()),
    });
  }

  private async syncBatchDiligenceDailyCount(orgId: number) {
    return this.batchRepo.count({
      businessType: In([BatchBusinessTypeEnums.Diligence_File, BatchBusinessTypeEnums.Diligence_ID, BatchBusinessTypeEnums.Diligence_Customer]),
      orgId,
      status: BatchStatusEnums.Done,
      createDate: MoreThan(moment().startOf('day').toDate()),
    });
  }

  // 尽调记录导出次数
  private async syncDiligenceExportDailyCount(orgId: number) {
    return this.batchRepo.count({
      businessType: In([BatchBusinessTypeEnums.Diligence_Batch_Detail, BatchBusinessTypeEnums.Diligence_Record]),
      orgId,
      status: BatchStatusEnums.Done,
      createDate: MoreThan(moment().startOf('day').toDate()),
    });
  }

  /**
   * 批量排查每天的次数上限卡控
   * @param user
   * @param counterType
   * @param operation
   * @param num
   */
  async useOrglimitationCounter(user: RoverUser, counterType: RoverBundleLimitationType, operation: CounterOperation, num: number) {
    const counter = await this.bundleService.getOrgLimitationCounter(user, counterType);
    return this.useCounter(counter, operation, num);
  }

  async bundleCounter(user: RoverUser, counterType: RoverBundleCounterType, operation: CounterOperation, num: number) {
    const counter = await this.bundleService.getBundleCounter(user, counterType);
    return this.useCounter(counter, operation, num);
  }

  // async orgBundleCounter(user: RoverUser, counterType: RoverBundleCounterType, operation: CounterOperation, num: number) {
  //   const counter = await this.bundleService.getOrgBundleCounter(user, counterType);
  //   return this.useCounter(counter, operation, num);
  // }

  private async useCounter(counter: ICounter, operation: CounterOperation, num: number) {
    // await counter.clear();
    switch (operation) {
      case CounterOperation.Increase:
        return counter.increase(num);
      case CounterOperation.Decrease:
        return counter.decrease(num);
      case CounterOperation.Check:
        return counter.check(num);
      default:
        throw new InternalServerErrorException(RoverExceptions.Bundle.CounterOperationNotFound);
    }
  }
}
