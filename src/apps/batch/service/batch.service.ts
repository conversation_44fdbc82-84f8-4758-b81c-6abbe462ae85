/**
 * @file 批量任务相关 统一管理batch和batchJob的创建
 */
import { BadRequestException, Injectable } from '@nestjs/common';
import { PaginationParams, RoverUser } from 'libs/model/common';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { SearchBatchRequest } from 'libs/model/batch/request/SearchBatchRequest';
import { Brackets, EntityManager, In, QueryFailedError, Repository } from 'typeorm';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { SearchBatchResponse } from 'libs/model/batch/response/SearchBatchResponse';
import { BatchTypeEnums } from 'libs/enums/batch/BatchTypeEnums';
import { DiligenceFileParser } from '../file.parser/DiligenceFileParser';
import { FileParseResult } from 'libs/model/batch/po/parse/FileParseResult';
import * as Bluebird from 'bluebird';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { chunk, compact, omit, pick, sum, uniq } from 'lodash';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { DiligenceHistoryResponse } from 'libs/model/diligence/pojo/history/DiligenceHistoryResponse';
import { SearchBatchResultRequest } from 'libs/model/batch/request/SearchBatchResultRequest';
import { ParsedRecordBase } from 'libs/model/batch/po/parse/ParsedRecordBase';
import { BatchResultExportService } from './batch.result.export.service';
import { v1 as uuidv1 } from 'uuid';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { CompanySearchService } from '../../company/company-search.service';
import { BatchMatchCompanyItemEntity } from 'libs/entities/BatchMatchCompanyItemEntity';
import { BatchMatchCompanyEntity } from '../../../libs/entities/BatchMatchCompanyEntity';
import { SearchMatchCompanyRequest } from '../../../libs/model/batch/request/SearchMatchCompanyRequest';
import { UpdateMatchCompanyRequest } from '../../../libs/model/batch/request/UpdateMatchCompanyRequest';
import { KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { SearchMatchCompanyResponse } from '../../../libs/model/batch/response/SearchMatchCompanyResponse';
import { ChildDimensionHit } from '../../../libs/model/batch/request/ChildDimensionHit';
import { SecurityService } from '../../../libs/config/security.service';
import { SearchBiddingBatchResultRequest } from '../../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { FindConditions } from 'typeorm/find-options/FindConditions';
import { BatchRemoveRequest } from '../../../libs/model/batch/request/BatchRemoveRequest';
import { QueryBuilderHelper } from '../../../libs/common/sql.helper';
import MyOssService from '../../basic/my-oss.service';
import { BatchBaseHelper } from './helper/batch.base.helper';
import { BatchCreatorHelperBase } from './helper/batch.creator.helper.base';
import { BatchBiddingHelper } from './helper/batch.bidding.helper';
import { BatchDiligenceHelper } from './helper/batch.diligence.helper';
import { BatchCreatorHelperFile } from './helper/batch.creator.helper.file';
import { BatchCreatorHelperExport } from './helper/batch.creator.helper.export';
import { CustomerImportFileParser } from '../file.parser/CustomerImportFileParser';
import { InnerBlacklistImportFileParser } from '../file.parser/InnerBlacklistImportFileParser';
import { BatchCreateHelperImport } from './helper/batch.create.helper.import';
import { CustomerService } from '../../customer/customer.service';
import { BlacklistInnerService } from '../../blacklist/blacklist.inner.service';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { allowType, TimeInterval3Hour } from '../../../libs/constants/common';
import { BatchInfoPO } from '../model/BatchInfoPO';
import { MonitorImportFileParser } from '../file.parser/MonitorImportFileParser';
import { MonitorCompanyService } from '../../monitor/monitor.company.service';
import { BatchSpecificHelper } from './helper/batch.specific.helper';
import { BatchPotentialHelper } from './helper/batch.potential.helper';
import { SearchPotentialBatchResultRequest } from 'libs/model/batch/request/SearchPotentialBatchResultRequest';
import { BatchJobEntity } from '../../../libs/entities/BatchJobEntity';

@Injectable()
export class BatchService {
  private readonly logger: Logger = QccLogger.getLogger(BatchService.name);

  constructor(
    @InjectEntityManager() private readonly entityManager: EntityManager,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchJobEntity) private readonly batchJobRepo: Repository<BatchJobEntity>,
    @InjectRepository(BatchMatchCompanyItemEntity) private readonly batchMatchCompanyItemRepo: Repository<BatchMatchCompanyItemEntity>,
    @InjectRepository(BatchMatchCompanyEntity) private readonly batchMatchCompanyRepo: Repository<BatchMatchCompanyEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    private readonly resultExportService: BatchResultExportService,
    private readonly companySearchService: CompanySearchService,
    private readonly securityService: SecurityService,
    private readonly myOssService: MyOssService,
    private readonly batchBaseHelperService: BatchBaseHelper,
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
    private readonly batchBiddingHelperService: BatchBiddingHelper,
    private readonly batchPotentialHelperService: BatchPotentialHelper,
    private readonly batchDiligenceHelperService: BatchDiligenceHelper,
    private readonly batchCreateHelperImportService: BatchCreateHelperImport,
    private readonly batchCreatorFileHelperService: BatchCreatorHelperFile,
    private readonly batchCreatorExportHelperService: BatchCreatorHelperExport,
    private readonly diligenceFileParser: DiligenceFileParser,
    private readonly customerFileParser: CustomerImportFileParser,
    private readonly innerBlacklistImportFileParser: InnerBlacklistImportFileParser,
    private readonly monitorFileParser: MonitorImportFileParser, //合作监控文件解析器
    private readonly customerService: CustomerService,
    private readonly blacklistInnerService: BlacklistInnerService,
    private readonly monitorService: MonitorCompanyService,
    private readonly batchSpecificHelper: BatchSpecificHelper,
  ) {}

  async searchBatch(user: RoverUser, body: SearchBatchRequest) {
    const { currentOrg: orgId } = user;
    const { pageSize, pageIndex, batchType, businessType, searchKey, status, createDate, field, order, createUsers, depIds } = body;

    const qb = this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .select(['batch', 'creator.name', 'creator.userId', 'creator.phone'])
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchType = :batchType', { batchType });

    const response = new SearchBatchResponse();
    Object.assign(response, { pageSize, pageIndex, total: 0, data: [] });
    if (!businessType?.length) {
      return response;
    }

    // 找到当前用户 有数据权限的 businessType，加入查询条件
    const querySqlList = compact(businessType.map((x) => this.securityService.getBatchPermissionSql(x, user)));
    if (querySqlList.length) {
      qb.andWhere(`(${querySqlList.join(' or ')})`);
    } else {
      // 没找到有数据权限的businessType，直接返回空列表
      return response;
    }

    if (searchKey) {
      qb.andWhere('batch.fileName like :name', { name: `%${searchKey}%` });
    }
    if (status?.length) {
      qb.andWhere('batch.status in (:...status)', { status });
    }
    if (createUsers?.length > 0) {
      if (createUsers.includes(-1) && createUsers.length == 1) {
        // 系统自动巡检
        qb.andWhere(`batch.batchInfo -> '$.fromSystem' = true`);
      }
      if (!createUsers.includes(-1)) {
        qb.andWhere('batch.creatorId in (:...createUsers)', { createUsers });
        // 不包含系统自动巡检
        qb.andWhere(`batch.batchInfo -> '$.fromSystem' is null`);
      }
      if (createUsers.includes(-1) && createUsers.length > 1) {
        qb.andWhere(
          new Brackets((qb1) => {
            qb1.orWhere('batch.creatorId in (:...createUsers)', { createUsers });
            qb1.orWhere(`batch.batchInfo -> '$.fromSystem' = true`);
          }),
        );
      }
    }
    if (depIds?.length) {
      qb.andWhere('batch.depId in (:...depIds)', { depIds });
    }
    // 提交时间
    QueryBuilderHelper.applyDateRangeQuery(qb, createDate, 'createDate');
    if (field && order) {
      qb.orderBy(`batch.${field}`, order);
    } else {
      qb.orderBy('batch.createDate', 'DESC');
    }
    qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    const [items, total] = await qb.getManyAndCount();
    // 对于距离任务开始时间超出 3 小时，状态为待处理状态的任务，直接失败处理
    const nowDate = new Date();
    const exceptionBatchIds = items
      .filter((item) => {
        return item.status == 0 && item.updateDate.getTime() < nowDate.getTime() - TimeInterval3Hour;
      })
      .map((item) => item.batchId);
    if (exceptionBatchIds?.length > 0) {
      this.logger.info(`batchIds: ${exceptionBatchIds.join(',')} 超过3小时，状态为待处理状态，直接失败处理`);
      await this.batchRepo.update({ batchId: In(exceptionBatchIds) }, { status: 3 });
      await this.batchJobRepo.update({ batchId: In(exceptionBatchIds) }, { status: 3 });
    }
    items.forEach((item) => {
      this.signatureUrl(item);
      if (exceptionBatchIds?.includes(item.batchId)) {
        item.status = 3;
      }
      return item;
    });
    response.total = total;
    response.data = items;
    return response;
  }

  /**
   * 获取 batch entity
   * @param batchId
   * @param user
   */
  async getBatchEntity(user: RoverUser, batchId: number, withSignatureUrl = true) {
    const { currentOrg: orgId } = user;
    const batchEntity = await this.batchRepo.findOne({
      where: {
        batchId,
        orgId,
      },
    });
    if (!batchEntity) {
      return null;
    }
    if (withSignatureUrl) {
      this.signatureUrl(batchEntity);
    }
    return batchEntity;
  }

  async getBatchEntityWithCreator(user: RoverUser, batchId: number) {
    const { currentOrg: orgId } = user;
    const batchEntity = await this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchId = :batchId', { batchId })
      .getOne();
    if (!batchEntity) {
      return null;
    }
    return batchEntity;
  }

  async searchUser(user: RoverUser, body: SearchBatchRequest) {
    const { currentOrg: orgId } = user;
    const { batchType, businessType } = body;
    const qb = this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .select(['batch', 'creator.name', 'creator.userId'])
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchType = :batchType', { batchType });

    if (!businessType?.length) {
      return [];
    }
    // 找到当前用户 有数据权限的 businessType，加入查询条件
    const querySqlList = compact(businessType.map((x) => this.securityService.getBatchPermissionSql(x, user)));
    if (querySqlList.length) {
      qb.andWhere(`(${querySqlList.join(' or ')})`);
    } else {
      // 没找到有数据权限的businessType，直接返回空列表
      return [];
    }
    qb.groupBy('batch.creatorId');
    const batch = await qb.getMany();
    return uniq(batch?.map((b) => b.creator)) || [];
  }

  // private getQuerySql(businessType: BatchBusinessTypeEnums, user: RoverUser) {
  //   let permissionIds = [];
  //   switch (businessType) {
  //     case BatchBusinessTypeEnums.Specific_Diligence_File:
  //       permissionIds = [10194];
  //       break;
  //     case BatchBusinessTypeEnums.Specific_Record_List_Export:
  //     case BatchBusinessTypeEnums.Specific_Report_Export:
  //     case BatchBusinessTypeEnums.Specific_Batch_Export:
  //       permissionIds = [10191];
  //       break;
  //     case BatchBusinessTypeEnums.Diligence_File:
  //     case BatchBusinessTypeEnums.Diligence_ID:
  //       permissionIds = [2011];
  //       break;
  //     case BatchBusinessTypeEnums.Diligence_Customer:
  //     case BatchBusinessTypeEnums.Diligence_Customer_Analyze:
  //       permissionIds = [2014, 2071]; //风险巡检查看权限, 巡检分析查看权限
  //       break;
  //     case BatchBusinessTypeEnums.Customer_File:
  //     case BatchBusinessTypeEnums.Customer_ID:
  //       permissionIds = [2032];
  //       break;
  //     case BatchBusinessTypeEnums.Person_File:
  //       permissionIds = [2062];
  //       break;
  //     case BatchBusinessTypeEnums.Monitor_File:
  //       //合作监控添加权限
  //       permissionIds = [2102];
  //       break;
  //     case BatchBusinessTypeEnums.InnerBlacklist_File:
  //       permissionIds = [2042];
  //       break;
  //     case BatchBusinessTypeEnums.Diligence_Batch_Detail:
  //     case BatchBusinessTypeEnums.Dimension_Detail_Export:
  //       permissionIds = [2011];
  //       break;
  //     case BatchBusinessTypeEnums.Diligence_Record:
  //       permissionIds = [2022];
  //       break;
  //     case BatchBusinessTypeEnums.Person_Export:
  //       permissionIds = [2061];
  //       break;
  //     case BatchBusinessTypeEnums.InnerBlacklist_Export:
  //       permissionIds = [2041];
  //       break;
  //     case BatchBusinessTypeEnums.Diligence_Report_Export:
  //     case BatchBusinessTypeEnums.Diligence_Report_Batch_Export:
  //       permissionIds = [2002];
  //       break;
  //     case BatchBusinessTypeEnums.Customer_Export:
  //       permissionIds = [2031];
  //       break;
  //     case BatchBusinessTypeEnums.Tender_Detail_Export:
  //     case BatchBusinessTypeEnums.Tender_Export:
  //       permissionIds = [2136];
  //       break;
  //     case BatchBusinessTypeEnums.Bidding_Diligence_File:
  //       permissionIds = [2114];
  //       break;
  //     case BatchBusinessTypeEnums.Risk_Export:
  //       permissionIds = [2093];
  //       break;
  //     case BatchBusinessTypeEnums.Sentiment_Export:
  //       permissionIds = [2094];
  //       break;
  //     case BatchBusinessTypeEnums.Analyze_Record_Export:
  //     case BatchBusinessTypeEnums.Analyze_Dimension_Detail:
  //       permissionIds = [2015];
  //       break;
  //     case BatchBusinessTypeEnums.Tender_Report_Export:
  //       //招标排查导出报告 2115
  //       permissionIds = [2115];
  //       break;
  //     case BatchBusinessTypeEnums.Tender_Diligence_Record_Export:
  //     case BatchBusinessTypeEnums.Tender_Dimension_Detail_Export:
  //       permissionIds = [10134];
  //       break;
  //     case BatchBusinessTypeEnums.Tender_Diligence_History_Export:
  //       permissionIds = [2116];
  //       break;
  //   }
  //   try {
  //     const { by, userIds } = this.securityService.checkScopeByPermissionIds(user, permissionIds);
  //     if (by == PermissionByEnum.USER) {
  //       return `(batch.businessType = ${businessType} and batch.creatorId in (${userIds.join(',')}))`;
  //     } else {
  //       return `(batch.businessType = ${businessType})`;
  //     }
  //   } catch (error) {
  //     // 当前用户没有对应的 BatchBusiness权限时， 不抛异常，直接返回空
  //     return '';
  //   }
  // }

  /**
   * 获取指定批量任务详情
   * @param user
   * @param postData
   * @param notUsePage
   * @returns
   */
  async getBatch(user: RoverUser, postData: SearchBatchResultRequest, notUsePage = false): Promise<DiligenceHistoryResponse> {
    return this.batchBaseHelperService.getBatch(user, postData, notUsePage);
  }

  public async getCompanyDimensionDetail(orgId: number, postData: SearchBatchResultRequest) {
    const childDimensionHit: Record<string, number> = {};
    //此处统计的是纬度命中的数量
    const res: ChildDimensionHit[] = [];
    const { diligenceId, dimensionLevel1, dimensionLevel2 } = postData;
    const diligenceHis = await this.diligenceHistoryRepo.findOne({ id: diligenceId, orgId });
    if (diligenceHis) {
      diligenceHis?.details?.dimensionScoreDetails.map((e) => {
        if (dimensionLevel2) {
          e?.scoreDetails?.map((s) => {
            if (dimensionLevel2 === s?.key) {
              s?.subDimension?.map((sb) => {
                childDimensionHit[sb.key] = (childDimensionHit?.[sb.key] || 0) + sb.totalHits;
              });
            }
          });
        } else if (e.groupKey === dimensionLevel1) {
          e.scoreDetails?.map((s) => {
            childDimensionHit[s.key] = (childDimensionHit?.[s.key] || 0) + s.totalHits;
          });
        }
      });
    }
    Object?.keys(childDimensionHit)?.map((c) => {
      res.push(Object.assign(new ChildDimensionHit(), { key: c, totalHits: childDimensionHit[c] }));
    });
    return res;
  }

  async searchMatchCompanyRequest(user: RoverUser, body: SearchMatchCompanyRequest): Promise<SearchMatchCompanyResponse> {
    const { pageSize, pageIndex } = body;
    const response: SearchMatchCompanyResponse = Object.assign(new SearchMatchCompanyResponse(), {
      pageIndex,
      pageSize,
    });

    const batchMatchCompanyEntity = await this.batchMatchCompanyRepo.findOne({
      orgId: user.currentOrg,
      id: body.batchId,
    });
    if (!batchMatchCompanyEntity) {
      throw new BadRequestException();
    }
    const qb = this.batchMatchCompanyItemRepo
      .createQueryBuilder('item')
      .andWhere('item.batchId = :batchId', { batchId: body.batchId })
      .addOrderBy('item.flag', 'DESC')
      .addOrderBy('item.id', 'ASC');
    qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    const [data, total] = await qb.getManyAndCount();
    const companyIds = data.filter((x) => x.companyId).map((x) => x.companyId);
    if (companyIds.length) {
      const companyInfos = await this.companySearchService.companySearchForKys(
        Object.assign(new KysCompanySearchRequest(), {
          searchIndex: ['name'],
          includeFields: ['creditcode', 'id', 'reccap', 'reccapmount', 'originalname'],
          pageIndex: 1,
          pageSize: companyIds.length,
          filter: { ids: companyIds },
        }),
      );
      if (companyInfos.Result?.length) {
        data
          .filter((x) => x.companyId)
          .forEach((x) => {
            const info = companyInfos.Result.find((t) => t.id == x.companyId);
            Object.assign(x, omit(info, ['id', 'name']));
          });
      }
    }
    Object.assign(response, { data, total });

    const qb2 = this.batchMatchCompanyItemRepo
      .createQueryBuilder('item')
      .select(['item.flag as flag', `count(item.id) as 'count'`])
      .andWhere('item.batchId = :batchId', { batchId: body.batchId })
      .addGroupBy('item.flag');
    const groupCount = await qb2.getRawMany();
    const statistic = batchMatchCompanyEntity.statisticsInfo;
    statistic.successCount = sum(groupCount.filter((x) => x.flag == '0').map((x) => Number(x.count)));
    statistic.errorCount = sum(groupCount.filter((x) => x.flag != '0').map((x) => Number(x.count)));
    statistic.duplicatedCount = statistic.recordCount - statistic.successCount - statistic.errorCount;
    if (statistic.duplicatedCount < 0) {
      statistic.duplicatedCount = 0;
    }
    Object.assign(response, { statistic: pick(statistic, ['successCount', 'errorCount', 'duplicatedCount']) });
    return response;
  }

  async matchBatchDiligenceCompany(user: RoverUser, filePath: string, fileName: string) {
    const fileParseResult: FileParseResult = await this.diligenceFileParser.parseFile(user, filePath);
    const { succeedItems, failedItems } = fileParseResult;
    if (!succeedItems?.length) {
      throw new BadRequestException('未识别到符合格式的数据');
    }
    let extension = '';
    if (fileName.lastIndexOf('.') > 0) {
      extension = fileName.substring(fileName.lastIndexOf('.'), fileName.length);
    }
    const originFileUrl = await this.resultExportService.putFileToOss(filePath, uuidv1() + extension, fileName);
    const matchCompany = await this.batchMatchCompanyRepo.save(
      Object.assign(new BatchMatchCompanyEntity(), {
        createDate: new Date(),
        orgId: user.currentOrg,
        creatorId: user.userId,
        fileName,
        originFile: originFileUrl,
        statisticsInfo: { recordCount: succeedItems.length + failedItems.length },
      }),
    );
    let plusRecordCount = 0; // 通过曾用名匹配时，一个曾用名可能匹配多个公司
    const successChunks = chunk(succeedItems, 100);
    await Bluebird.map(
      successChunks,
      async (chunk) => {
        const names = chunk.map((x) => x.companyName);
        const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfo(names);
        const entities: BatchMatchCompanyItemEntity[] = [];
        matchedCompanyInfos.forEach((x) => {
          let matchBy;
          if (names.includes(x.name)) {
            matchBy = 0;
          } else if (names.includes(x.creditcode)) {
            matchBy = 1;
          } else if (names.includes(x.regno)) {
            matchBy = 2;
          } else {
            matchBy = 3;
          }
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: matchCompany.id,
              companyId: x.id,
              name: x.name,
              flag: 0,
              matchBy,
            }),
          );
        });
        unmatchedNames.forEach((x) =>
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: matchCompany.id,
              name: x,
              flag: 2,
            }),
          ),
        );
        unsupported.forEach((x) =>
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: matchCompany.id,
              name: x,
              flag: 1,
            }),
          ),
        );
        if (entities.length > names.length) {
          plusRecordCount += entities.length - names.length;
        }
        await this.batchMatchCompanyItemRepo.upsert(entities, ['batchId', 'name', 'companyId']);
      },
      { concurrency: 10 },
    );
    if (plusRecordCount > 0) {
      matchCompany.statisticsInfo.recordCount += plusRecordCount;
      await this.batchMatchCompanyRepo.save(matchCompany);
    }
    return matchCompany;
  }

  async matchBatchCompany(user: RoverUser, filePath: string, fileName: string, businessType: BatchBusinessTypeEnums) {
    let fileParseResult: FileParseResult = null;
    let supportOverSeas = true;
    switch (businessType) {
      case BatchBusinessTypeEnums.Diligence_File: {
        fileParseResult = await this.diligenceFileParser.parseFile(user, filePath);
        break;
      }
      case BatchBusinessTypeEnums.Customer_File: {
        fileParseResult = await this.customerFileParser.parseFile(user, filePath);
        break;
      }
      case BatchBusinessTypeEnums.InnerBlacklist_File: {
        fileParseResult = await this.innerBlacklistImportFileParser.parseFile(user, filePath);
        break;
      }
      case BatchBusinessTypeEnums.Monitor_File: {
        fileParseResult = await this.monitorFileParser.parseFile(user, filePath);
        supportOverSeas = false;
        break;
      }
    }
    const { succeedItems, failedItems } = fileParseResult;
    if (!succeedItems?.length) {
      throw new BadRequestException('未识别到符合格式的数据');
    }
    let extension = '';
    if (fileName.lastIndexOf('.') > 0) {
      extension = fileName.substring(fileName.lastIndexOf('.'), fileName.length);
    }
    const originFileUrl = await this.resultExportService.putFileToOss(filePath, uuidv1() + extension, fileName);
    const batchEntity = await this.batchMatchCompanyRepo.save(
      Object.assign(new BatchMatchCompanyEntity(), {
        createDate: new Date(),
        orgId: user.currentOrg,
        creatorId: user.userId,
        fileName,
        originFile: originFileUrl,
        statisticsInfo: { recordCount: succeedItems.length + failedItems.length },
      }),
    );
    let plusRecordCount = 0; // 通过曾用名匹配时，一个曾用名可能匹配多个公司
    const successChunks = chunk(succeedItems, 100);
    //3:香港企业，4:机关单位，5：台湾企业
    await Bluebird.map(
      successChunks,
      async (chunk) => {
        const names = chunk.map((x) => x.companyName);
        const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfo(
          names,
          [
            'id',
            'name',
            'creditcode',
            'regno',
            'province',
            'areacode',
            'address',
            'industry',
            'subind',
            'econkindcode',
            'registcapi',
            'startdatecode',
            'registcapiamount',
            'status',
          ],
          allowType,
          supportOverSeas, //支持境外企业，香港企业，机关单位，台湾企业
        );
        const entities: BatchMatchCompanyItemEntity[] = [];
        matchedCompanyInfos.forEach((x) => {
          if (unsupported.includes(x.name)) {
            return;
          }
          let matchBy;
          if (names.includes(x.name)) {
            matchBy = 0;
          } else if (names.includes(x.creditcode)) {
            matchBy = 1;
          } else if (names.includes(x.regno)) {
            matchBy = 2;
          } else {
            matchBy = 3;
          }
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: batchEntity.id,
              companyId: x.id,
              name: x.name,
              flag: 0,
              matchBy,
              parsedItem: chunk.find((c) => compact([x.name, ...(x?.originalname ? x.originalname : []), x?.creditcode, x?.regno]).includes(c.companyName)) || {
                //originalname实际上是一个数组
                companyId: x.id,
                companyName: x.name,
              },
            }),
          );
        });
        unmatchedNames.forEach((x) =>
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: batchEntity.id,
              name: x,
              flag: 2,
              parsedItem: chunk.find((c) => c.name === x) || { companyName: x },
            }),
          ),
        );
        unsupported.forEach((x) =>
          entities.push(
            Object.assign(new BatchMatchCompanyItemEntity(), {
              batchId: batchEntity.id,
              name: x,
              flag: 1,
              parsedItem: chunk.find((c) => c.name === x) || { companyName: x },
            }),
          ),
        );
        if (entities.length > names.length) {
          plusRecordCount += entities.length - names.length;
        }
        await this.batchMatchCompanyItemRepo.upsert(entities, ['batchId', 'name', 'companyId']);
      },
      { concurrency: 10 },
    );
    if (plusRecordCount > 0) {
      batchEntity.statisticsInfo.recordCount += plusRecordCount;
      await this.batchMatchCompanyRepo.save(batchEntity);
    }
    return batchEntity;
  }

  async createBatchByFile(user: RoverUser, filePath: string, fileName: string, businessType: BatchBusinessTypeEnums) {
    return this.batchCreatorFileHelperService.createBatchByFile(user, filePath, fileName, businessType);
  }

  async checkBatchBiddingDiligenceLimint(user: RoverUser, filePath: string) {
    return this.batchCreatorFileHelperService.checkBatchBiddingDiligenceLimint(user, filePath);
  }

  /**
   * 创建批量导出任务
   * batchJob直接被启动，无须通过batch来启动
   * @param user
   * @param businessType
   * @param condition
   * @returns
   */
  async createExportBatchEntity<T extends PaginationParams>(user: RoverUser, businessType: BatchBusinessTypeEnums, condition: T) {
    return this.batchCreatorExportHelperService.createExportBatchEntity(user, businessType, condition);
  }

  /**
   * 根据指定的data创建批量排查任务
   * @param user
   * @param data
   * @param businessType
   * @returns
   */
  @TraceLog({ throwError: true })
  async createBatchDiligenceTask(
    user: RoverUser,
    data: ParsedRecordBase[],
    businessType: BatchBusinessTypeEnums,
    batchType?: BatchTypeEnums,
    params?: BatchInfoPO,
  ) {
    return this.batchCreatorHelperService.createBatchDiligenceByData(user, data, businessType, batchType, params);
  }

  async deleteMatchCompany(user: RoverUser, data: BatchRemoveRequest) {
    const { batchId, ids: itemIds, flag } = data;
    if ((!itemIds?.length && !flag) || (flag && !batchId && !itemIds?.length)) {
      return { affected: 0 };
    }
    const conditions: FindConditions<BatchMatchCompanyItemEntity> = {};
    if (itemIds?.length) {
      Object.assign(conditions, { id: In(itemIds) });
    }
    if (flag && batchId) {
      Object.assign(conditions, { flag: In([1, 2]), batchId });
    }
    const batchMatchCompanyItems = await this.batchMatchCompanyItemRepo.find(conditions);
    if (batchMatchCompanyItems?.length) {
      const batchMatchCompanyEntity = await this.batchMatchCompanyRepo.findOne({
        id: batchId,
        orgId: user.currentOrg,
      });
      if (batchMatchCompanyEntity) {
        batchMatchCompanyEntity.statisticsInfo.recordCount -= batchMatchCompanyItems.length;
        await this.batchMatchCompanyRepo.save(batchMatchCompanyEntity);
        return await this.batchMatchCompanyItemRepo.delete(conditions);
      }
    }
    return { affected: 0 };
  }

  async updateMatchCompany(user: RoverUser, itemId: number, body: UpdateMatchCompanyRequest) {
    try {
      return await this.batchMatchCompanyItemRepo.update(
        { id: itemId },
        {
          name: body.companyName,
          companyId: body.companyId,
          flag: 0,
        },
      );
    } catch (e) {
      if (e instanceof QueryFailedError && e.message.startsWith('ER_DUP_ENTRY')) {
        return await this.batchMatchCompanyItemRepo.delete({ id: itemId });
      } else {
        throw e;
      }
    }
  }

  /**
   * 根据导入的匹配结果，创建批量排查任务
   * @param user
   * @param batchMatchCompanyId
   * @param settingId
   * @returns
   */
  async executeBatchDiligenceCompany(user: RoverUser, batchMatchCompanyId: number, settingId?: number) {
    return this.batchDiligenceHelperService.executeBatchDiligenceCompany(user, batchMatchCompanyId, settingId);
  }

  async executeBatchPotentialCompany(user: RoverUser, batchId: number, settingId?: number) {
    return this.batchPotentialHelperService.executeBatchPotentialCompany(user, batchId, settingId);
  }

  /**
   * 执行批量导入，创建批量导入任务
   * @param user
   * @param batchId
   * @param businessType
   * @param isUpdate 是否更新，默认 true
   */
  async executeBatchImport(user: RoverUser, batchId: number, businessType: BatchBusinessTypeEnums, isUpdate = true): Promise<BatchEntity> {
    //统计识别成功条数进行套餐量限制校验
    const groupCount = await this.batchMatchCompanyItemRepo
      .createQueryBuilder('item')
      .select(['item.flag as flag', `count(item.id) as 'count'`])
      .andWhere('item.batchId = :batchId', { batchId })
      .andWhere('item.flag = 0') //只查询识别成功条数
      .getRawMany();
    const successCount = sum(groupCount?.map((x) => Number(x?.count)));
    if (!successCount) {
      this.logger.error(`批量导入失败，未识别到有效数据，batchId: ${batchId}`);
      throw new BadRequestException(RoverExceptions.Import.DataNotFount);
    }
    switch (businessType) {
      case BatchBusinessTypeEnums.Customer_File: {
        await this.customerService.checkCountLimit(user, successCount);
        break;
      }
      case BatchBusinessTypeEnums.InnerBlacklist_File: {
        await this.blacklistInnerService.checkCountLimit(user, successCount);
        break;
      }
      case BatchBusinessTypeEnums.Monitor_File: {
        await this.monitorService.checkCountLimit(user, successCount);
        break;
      }
      default:
        break;
    }
    return this.batchCreateHelperImportService.executeBatchImport(user, batchId, businessType, isUpdate);
  }

  private signatureUrl(entity: BatchEntity) {
    entity.resultFile = this.myOssService.signSingleUrl(entity.resultFile);
    entity.originFile = this.myOssService.signSingleUrl(entity.originFile);
    entity.detailFile = this.myOssService.signSingleUrl(entity.detailFile);
    entity.previewUrl = this.myOssService.signSingleUrl(entity.previewUrl, true);
  }

  async getPotentialStatistics(user: any, data: SearchPotentialBatchResultRequest) {
    return this.batchPotentialHelperService.getPotentialStatistics(data);
  }

  async getBiddingStatistics(user: RoverUser, body: SearchBiddingBatchResultRequest) {
    return this.batchBiddingHelperService.getBiddingStatisticsV2(body);
  }

  async searchBiddingDetail(user: RoverUser, body: SearchBiddingBatchResultRequest) {
    return this.batchBiddingHelperService.searchBiddingDetailV2(user, body);
  }

  async checkBatchSpecificDiligenceLimit(user: RoverUser, filePath: string) {
    return this.batchCreatorFileHelperService.checkBatchSpecificDiligenceLimit(user, filePath);
  }

  async getSpecificStatistics(user: RoverUser, body: SearchBiddingBatchResultRequest) {
    return this.batchSpecificHelper.getSpecificStatistics(body);
  }

  async searchSpecificDetail(user: RoverUser, body: SearchBiddingBatchResultRequest) {
    return this.batchSpecificHelper.searchSpecificDetail(user, body);
  }

  async searchPotentialDetail(user: any, data: SearchPotentialBatchResultRequest) {
    return this.batchPotentialHelperService.searchPotentialDetail(user, data);
  }
}
