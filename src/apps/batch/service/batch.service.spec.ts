import { AppTestModule } from 'apps/app/app.test.module';
import { BatchModule } from '../batch.module';
import { BatchService } from './batch.service';
import { Test, TestingModule } from '@nestjs/testing';
import { SearchPotentialBatchResultRequest } from 'libs/model/batch/request/SearchPotentialBatchResultRequest';
import { da } from '@faker-js/faker';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { getTestUser } from 'apps/test_utils_module/test.user';
import { BatchTypeEnums } from 'libs/enums/batch/BatchTypeEnums';

jest.setTimeout(600000);
describe('BatchService test', () => {
  let batchService: BatchService;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();

    batchService = module.get<BatchService>(BatchService);
  });

  it('should be defined', () => {
    expect(batchService).toBeDefined();
  });

  it.skip('searchPotentialDetail test', async () => {
    const user = {
      currentOrg: 1001652,
      currentUser: 101348,
    };
    const data = Object.assign(new SearchPotentialBatchResultRequest(), {
      pageIndex: 3,
      pageSize: 10,
      batchId: 56298,
      groupKey: 'StaffWorkingOutsideForeignInvestment',
    });
    const res = await batchService.searchPotentialDetail(user, data);
    expect(res).toBeDefined();
  });

  it.skip('searchPotentialDetail test', async () => {
    const user = getTestUser(1003924, 103648);
    const data = [
      {
        companyId: '127fcf53de2598be86ab1b3cad165cf1',
        companyName: '珠海格力电器股份有限公司',
        recordParams: {},
        recordHashkey: '15b4f302-61d3-4906-8773-e08b2f439e0b',
      },
      {
        companyId: 'f625a5b661058ba5082ca508f99ffe1b',
        companyName: '企查查科技股份有限公司',
        recordParams: {},
        recordHashkey: '3be37c7e-8d1e-42f6-a116-f32879809506',
      },
    ];
    const res = await batchService.createBatchDiligenceTask(user, data, BatchBusinessTypeEnums.Potential_Batch_Data, BatchTypeEnums.Import);
  });
});
