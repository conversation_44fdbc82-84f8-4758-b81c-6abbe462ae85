import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../app/app.test.module';
import { BatchService } from './service/batch.service';
import { BatchBusinessTypeEnums } from '../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchBaseHelper } from './service/helper/batch.base.helper';
import { BatchMessageService } from './message.handler/batch.message.service';
import { BatchMessageHandlerSpecific } from './message.handler/specific/batch.message.handler.specific';
import { UserService } from '../user/user.service';
import { SpecificTestUtils } from '../test_utils_module/specific.test.utils';
import { EntityManager } from 'typeorm';
import { SearchBiddingBatchResultRequest } from '../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { BatchModule } from './batch.module';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
const [testOrgId, testUserId] = generateUniqueTestIds('batch.specific.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(100 * 1000);
describe('集成测试-批量特定利益关系排查', () => {
  let batchService: BatchService;
  let batchBaseHelper: BatchBaseHelper;
  let batchMessageService: BatchMessageService;
  let batchMessageHandlerSpecific: BatchMessageHandlerSpecific;
  let userService: UserService;
  let entityManager: EntityManager;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();

    batchService = module.get<BatchService>(BatchService);
    batchBaseHelper = module.get<BatchBaseHelper>(BatchBaseHelper);
    batchMessageService = module.get<BatchMessageService>(BatchMessageService);
    batchMessageHandlerSpecific = module.get<BatchMessageHandlerSpecific>(BatchMessageHandlerSpecific);
    userService = module.get<UserService>(UserService);
    entityManager = module.get<EntityManager>(EntityManager);
  });

  afterAll(async () => {
    await entityManager.connection.close();
  });

  afterEach(async () => {
    // 数据清理
    await SpecificTestUtils.clearSpecificTestUtils(entityManager, testUser.currentOrg, testUser.userId);
  });

  it('excel导入执行批量特定利益关系排查', async () => {
    const filePath = 'src/apps/batch/__tests__/批量特定利益关系项目导入模版.xlsx';

    jest.spyOn(batchMessageHandlerSpecific.batchSpecificQueue, 'sendMessageV2').mockImplementation((msg) => {
      return batchMessageHandlerSpecific.handleJobMessage(msg);
    });
    jest.spyOn(batchBaseHelper.batchJobMonitorQueue, 'sendMessageV2').mockImplementation((msg) => {
      return batchMessageService.processJobMonitorMessage(msg);
    });
    jest.spyOn(userService, 'getRoverUser').mockReturnValue(Promise.resolve(testUser));

    const response = await batchService.createBatchByFile(
      testUser,
      filePath,
      '批量特定利益关系项目导入模版.xlsx',
      BatchBusinessTypeEnums.Specific_Diligence_File,
    );
    expect(response.batchId).not.toBeNull();

    // 查询统计情况
    const body = new SearchBiddingBatchResultRequest();
    body.batchId = response.batchId;
    const statistics = await batchService.getSpecificStatistics(testUser, body);
    expect(statistics).toBeDefined();

    // 详情列表
    body.pageIndex = 1;
    body.pageSize = 10;
    body.status = [0, 1, 2, -1];
    const detailList = await batchService.searchSpecificDetail(testUser, body);
    expect(detailList).toBeDefined();
  }, 600000);
});
