import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../../app/app.test.module';
import { BatchModule } from '../batch.module';
import { BatchService } from '../service/batch.service';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchTypeEnums } from '../../../libs/enums/batch/BatchTypeEnums';
import { In, Repository } from 'typeorm';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BatchPotentialDiligenceEntity } from '../../../libs/entities/BatchPotentialDiligenceEntity';
import { BatchPotentialDiligenceDetailEntity } from '../../../libs/entities/BatchPotentialDiligenceDetailEntity';
import { BatchMessageService } from '../message.handler/batch.message.service';
import { BatchJobEntity } from '../../../libs/entities/BatchJobEntity';
import { BatchStatusEnums } from '../../../libs/enums/batch/BatchStatusEnums';
import { PotentialDiligenceEntity } from '../../../libs/entities/PotentialDiligenceEntity';
import { BatchResultEntity } from '../../../libs/entities/BatchResultEntity';
import { BatchJobResultTypeEnums } from '../../../libs/enums/batch/BatchJobResultTypeEnums';
import { JobMonitorMessagePO } from '../../../libs/model/batch/po/message/JobMonitorMessagePO';
import { QueueService } from '../../../libs/config/queue.service';
import { PotentialFacadeService } from '../../potential/potential.facade.service';
import { UserService } from '../../user/user.service';

jest.setTimeout(600000);
describe('批量潜在利益排查任务 集成测试', () => {
  let batchService: BatchService;
  let batchMessageService: BatchMessageService;
  let potentialFacadeService: PotentialFacadeService;
  let userService: UserService;
  let queueService: QueueService;
  let batchRepo: Repository<BatchEntity>;
  let batchJobRepo: Repository<BatchJobEntity>;
  let batchResultRepo: Repository<BatchResultEntity>;
  let batchPotentialRepo: Repository<BatchPotentialDiligenceEntity>;
  let batchPotentialDetailRepo: Repository<BatchPotentialDiligenceDetailEntity>;
  let potentialDiligenceRepo: Repository<PotentialDiligenceEntity>;

  const [testOrgId, testUserId] = generateUniqueTestIds('potential.data.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, BatchModule],
    }).compile();

    batchService = module.get<BatchService>(BatchService);
    batchMessageService = module.get<BatchMessageService>(BatchMessageService);
    potentialFacadeService = module.get<PotentialFacadeService>(PotentialFacadeService);
    userService = module.get<UserService>(UserService);
    queueService = module.get<QueueService>(QueueService);
    batchRepo = module.get<Repository<BatchEntity>>(getRepositoryToken(BatchEntity));
    batchJobRepo = module.get<Repository<BatchJobEntity>>(getRepositoryToken(BatchJobEntity));
    batchResultRepo = module.get<Repository<BatchResultEntity>>(getRepositoryToken(BatchResultEntity));
    batchPotentialRepo = module.get<Repository<BatchPotentialDiligenceEntity>>(getRepositoryToken(BatchPotentialDiligenceEntity));
    batchPotentialDetailRepo = module.get<Repository<BatchPotentialDiligenceDetailEntity>>(getRepositoryToken(BatchPotentialDiligenceDetailEntity));
    potentialDiligenceRepo = module.get<Repository<PotentialDiligenceEntity>>(getRepositoryToken(PotentialDiligenceEntity));

    // 模拟 UserService.getRoverUser 方法返回 testUser
    jest.spyOn(userService, 'getRoverUser').mockImplementation(async () => {
      return testUser;
    });
  });

  afterAll(async () => {
    await batchRepo.manager.connection.close();
  });

  afterEach(async () => {
    // 清理测试数据
    const batches = await batchRepo.find({ where: { orgId: testOrgId } });
    const batchIds = batches.map((b) => b.batchId);

    if (batchIds.length > 0) {
      await batchResultRepo.delete({ batchId: In(batchIds) });
      await batchPotentialDetailRepo.delete({ batchId: In(batchIds) });
      await batchPotentialRepo.delete({ batchId: In(batchIds) });
      await batchJobRepo.delete({ batchId: In(batchIds) });
    }

    await potentialDiligenceRepo.delete({ orgId: testOrgId });
    await batchRepo.delete({ orgId: testOrgId });
  });

  describe('createPotentialCompanyIdJob', () => {
    it('应该成功创建批量潜在利益排查任务并处理消息队列', async () => {
      // Given: 准备测试数据
      const testData = [
        {
          companyId: 'f24b74d1dbef45f5eac0c228ed780bad',
          companyName: '上海智元新创技术有限公司',
        },
        {
          companyId: 'b3905b6a94d52b8826fbe66c119b245b',
          companyName: '上海强生集团有限公司',
        },
      ];

      // When: 创建批量任务
      const result = await batchService.createBatchDiligenceTask(testUser, testData, BatchBusinessTypeEnums.Potential_Batch_Data, BatchTypeEnums.Import);

      // Then: 验证结果
      expect(result).toBeDefined();
      expect(result.orgId).toBe(testOrgId);
      expect(result.creatorId).toBe(testUserId);
      expect(result.businessType).toBe(BatchBusinessTypeEnums.Potential_Batch_Data);
      expect(result.batchType).toBe(BatchTypeEnums.Import);

      // 验证任务创建后的状态
      const batch = await batchRepo.findOne(result.batchId);
      expect(batch).toBeDefined();
      expect(batch.status).toBe(BatchStatusEnums.Waiting);

      // 验证任务作业创建
      const jobs = await batchJobRepo.find({ where: { batchId: result.batchId } });
      expect(jobs[0].status).toBe(BatchStatusEnums.Waiting);

      // 模拟消息队列处理 - 处理监控消息
      const monitorMessage: JobMonitorMessagePO = {
        batchId: result.batchId,
        businessType: BatchBusinessTypeEnums.Potential_Batch_Data,
        index: 0,
        type: 0, // Scan
        startDate: Date.now(),
        batchType: BatchTypeEnums.Import,
        operatorId: testUserId,
        orgId: testOrgId,
        batchTotalRecords: result.recordCount,
      };

      // 处理监控消息，这会触发作业消息的创建
      await batchMessageService.processJobMonitorMessage(monitorMessage);

      // 获取批量任务的作业
      const batchJobs = await batchJobRepo.find({
        where: { batchId: result.batchId },
      });
      expect(batchJobs.length).toBeGreaterThan(0);

      // 获取潜在利益排查的消息处理器
      const potentialHandler = batchMessageService['allHandlers'].find((handler) => handler.constructor.name === 'BatchMessageHandlerPotential');
      expect(potentialHandler).toBeDefined();

      // 手动构造作业消息并处理
      for (const job of batchJobs) {
        const jobMessage = {
          batchId: result.batchId,
          batchType: BatchTypeEnums.Import,
          businessType: BatchBusinessTypeEnums.Potential_Batch_Data,
          startDate: Date.now(),
          operatorId: testUserId,
          orgId: testOrgId,
          jobId: job.jobId,
          items: job.jobInfo.items.map((item) => ({
            ...item,
            recordHashkey: item.recordHashkey || `test-hashkey-${Date.now()}`,
          })),
          isUpdate: true,
        };

        // 直接处理作业消息
        await potentialHandler.handleJobMessage(jobMessage);
      }

      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 验证作业状态更新
      const updatedJobs = await batchJobRepo.find({ where: { batchId: result.batchId } });
      for (const job of updatedJobs) {
        expect([BatchStatusEnums.Done, BatchStatusEnums.Error]).toContain(job.status);
      }

      // 验证批量任务结果
      const batchResults = await batchResultRepo.find({ where: { batchId: result.batchId } });
      expect(batchResults.length).toBeGreaterThanOrEqual(testData.length);

      // 验证潜在利益排查结果
      const potentialResults = await potentialDiligenceRepo.find({ where: { orgId: testOrgId } });
      expect(potentialResults.length).toBeGreaterThan(0);

      // 验证批量潜在利益排查关联
      const potentialBatchLinks = await batchPotentialRepo.find({ where: { batchId: result.batchId } });
      expect(potentialBatchLinks.length).toBeGreaterThan(0);

      // 验证批量任务最终状态
      const finalBatch = await batchRepo.findOne(result.batchId);
      expect([BatchStatusEnums.Done, BatchStatusEnums.Error, BatchStatusEnums.Processing]).toContain(finalBatch.status);
    });

    it('当输入数据为空时应该抛出错误', async () => {
      // Given: 空数据
      const emptyData = [];

      // When & Then: 验证错误处理
      await expect(
        batchService.createBatchDiligenceTask(testUser, emptyData, BatchBusinessTypeEnums.Potential_Batch_Data, BatchTypeEnums.Import),
      ).rejects.toThrow();
    });
  });
});
