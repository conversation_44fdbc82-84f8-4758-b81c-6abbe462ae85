import { Body, Controller, Post, Query, Request, UploadedFile, UseFilters, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiCookieAuth, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { BatchService } from './service/batch.service';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { FileInterceptor } from '@nestjs/platform-express';
import { defaultFileUploadOptions } from 'libs/common/file/config';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { FileSizeLimitExceptionFilter } from 'libs/exceptions/FileSizeLimitExceptionFilter';
import { SearchBiddingBatchResultRequest } from '../../libs/model/batch/request/SearchBiddingBatchResultRequest';
import { RoverSessionGuard } from '../../libs/guards/RoverSession.guard';
import { RoverRolesGuard } from '../../libs/guards/rover.roles.guard';
import { SearchPotentialBatchResultRequest } from 'libs/model/batch/request/SearchPotentialBatchResultRequest';

@Controller('batch/import/potential')
@ApiTags('批量潜在利益关系排查')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class BatchPotentialController {
  private readonly logger: Logger = QccLogger.getLogger(BatchPotentialController.name);
  constructor(private readonly batchService: BatchService) {}

  //   @Post('diligence_check')
  //   @UseFilters(FileSizeLimitExceptionFilter)
  //   @UseInterceptors(FileInterceptor('file', { ...defaultFileUploadOptions }))
  //   @ApiParam({ name: 'file', type: 'file', description: 'excel上传' })
  //   @ApiOperation({ summary: '批量潜在利益关系排查任务的额度检查' })
  //   async checkBatchPotentialJob(@UploadedFile() file, @Request() req) {
  //     return this.batchService.checkBatchBiddingDiligenceLimint(req.user, file.path);
  //   }

  /**
   * 查询批量潜在利益关系排查统计信息
   * @param req - 请求对象，包含用户信息
   * @param data - 查询批量潜在利益关系排查统计信息的请求数据
   * @returns 批量潜在利益关系排查统计信息
   */
  @Post('statistics')
  @ApiOperation({ summary: '查询批量潜在利益关系排查统计信息' })
  async searchPotentialStatistics(@Request() req, @Body() data: SearchPotentialBatchResultRequest) {
    return this.batchService.getPotentialStatistics(req.user, data);
  }

  @Post('detail')
  @ApiOperation({ summary: '查询批量潜在利益关系排查详情' })
  async searchPotentialDetail(@Request() req, @Body() data: SearchPotentialBatchResultRequest) {
    return this.batchService.searchPotentialDetail(req.user, data);
  }
}
