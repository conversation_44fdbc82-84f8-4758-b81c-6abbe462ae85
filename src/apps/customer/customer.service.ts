import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { CustomerEntity } from 'libs/entities/CustomerEntity';
import { Brackets, EntityManager, In, Repository } from 'typeorm';
import { CreateCustomerModel } from 'libs/model/customer/CreateCustomerModel';
import { AffectedResponse, RoverUser } from 'libs/model/common';
import { CustomerLabelEntity } from 'libs/entities/CustomerLabelEntity';
import { SearchCustomerModel } from 'libs/model/customer/SearchCustomerModel';
import { AggsResponse, FieldAggsResponse, SearchCustomerResponse } from 'libs/model/customer/SearchCustomerResponse';
import { difference, flatten, groupBy, omit, pick, uniq, uniqBy } from 'lodash';
import { UpdateCustomerModel } from 'libs/model/customer/UpdateCustomerModel';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BadParamsException, CommonExceptions, RequestUtils } from '@kezhaozhao/qcc-utils';
import { GroupChangeRequest } from 'libs/model/customer/GroupChangeRequest';
import { GroupsEntity } from 'libs/entities/GroupsEntity';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { LabelModifyRequest } from 'libs/model/customer/LabelModifyRequest';
import { BatchStatusEnums } from 'libs/enums/batch/BatchStatusEnums';
import { LabelEntity } from 'libs/entities/LabelEntity';
import { RoverBundleCounterType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { CompanySearchService } from '../company/company-search.service';
import { EntityDuplicatedException } from 'libs/exceptions/EntityDuplicatedException';
import { SearchCustomerMonitorStatusModel } from '../../libs/model/customer/SearchCustomerMonitorStatusModel';
import { DiligenceHistoryCacheHelper } from 'apps/basic/diligence.history.cache.helper';
import { OpLogService } from '../oplog/oplog.service';
import { OperatorTypeEnums } from '../../libs/enums/oplog/OperatorTypeEnums';
import * as Bluebird from 'bluebird';
import { LabelService } from '../element/label.service';
import { MatchCustomerModel } from '../../libs/model/customer/MatchCustomerModel';
import { DiligenceHistoryEntity } from '../../libs/entities/DiligenceHistoryEntity';
import { SecurityService } from '../../libs/config/security.service';
import { PermissionByEnum } from '../../libs/enums/PermissionScopeEnum';
import { QueryBuilderHelper } from '../../libs/common/sql.helper';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';
import { StreamTableEnums } from '../../libs/enums/data/StreamTableEnums';
import { StreamOperationEnum } from '../../libs/enums/data/StreamOperationEnum';
import { RoverGraphService } from '../data/source/rover.graph.service';
import { CustomerDepartmentEntity } from '../../libs/entities/CustomerDepartmentEntity';
import { DepartmentTypeEnum } from '../../libs/enums/department/DepartmentTypeEnum';
import { DepartmentService } from '../element/department.service';
import { DepartmentEntity } from '../../libs/entities/DepartmentEntity';
import { CustomerAggsRequest } from '../../libs/model/customer/CustomerAggsRequest';
import { GroupType } from '../../libs/model/element/CreateGroupModel';
import { UserEntity } from '../../libs/entities/UserEntity';
import { CompanyBasicInfo } from '../../libs/model/blacklist/RemoveCustomerResponse';
import { CompanyBusinessInfo } from '../company/model/CompanyBusinessInfo';
import { ContactService } from '../element/contact.service';
import { CustomerAggsFieldEnums } from '../../libs/enums/customer/CustomerAggsFieldEnums';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { DATE_TIME_FORMAT } from '../../libs/constants/common';
import { ConfigService } from '../../libs/config/config.service';
import { EnterpriseDepartment } from '../../libs/model/customer/EnterpriseDepartment';
import { HttpUtilsService } from '../../libs/config/httputils.service';
import { CustomerController } from './customer.controller';

@Injectable()
export class CustomerService {
  private readonly logger: Logger = QccLogger.getLogger(CustomerService.name);

  constructor(
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
    @InjectRepository(CustomerLabelEntity) private readonly customerLabelRepo: Repository<CustomerLabelEntity>,
    @InjectRepository(LabelEntity) private readonly labelRepo: Repository<LabelEntity>,
    @InjectRepository(GroupsEntity) private readonly groupRepo: Repository<GroupsEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(CustomerDepartmentEntity) private readonly customerDepartmentRepo: Repository<CustomerDepartmentEntity>,
    @InjectRepository(DepartmentEntity) private readonly departmentRepo: Repository<DepartmentEntity>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    private readonly bundleService: RoverBundleService,
    private readonly diligenceHistoryHelper: DiligenceHistoryCacheHelper,
    public readonly companySearchService: CompanySearchService,
    private readonly oplogService: OpLogService,
    private readonly labelService: LabelService,
    private readonly contactService: ContactService,
    private readonly departmentService: DepartmentService,
    private readonly securityService: SecurityService,
    private readonly graphService: RoverGraphService,
    private readonly configService: ConfigService,
    private readonly httpUtilsService: HttpUtilsService,
  ) {}

  public async getCustomersWithGroupAndLabel(orgId: number, companyIds: string[]) {
    const customers = await this.customerRepo
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.labels', 'label')
      .leftJoinAndSelect('customer.group', 'group')
      .select(['customer', 'label', 'group'])
      .where('customer.orgId = :orgId', { orgId })
      .andWhere('customer.companyId in (:...companyIds)', { companyIds })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done })
      .getMany();
    if (!customers) {
      return [];
    }
    return customers.map((customer) => {
      return {
        companyId: customer.companyId,
        labelNames: customer.labels.map((label) => label.name),
        groupName: customer.group?.name,
      };
    });
  }

  public async getCustomerDetail(orgId: number, companyId: string) {
    const customer = await this.customerRepo
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.labels', 'label')
      .select(['customer', 'label'])
      .where('customer.orgId = :orgId', { orgId })
      .andWhere('customer.companyId = :companyId', { companyId })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done })
      .getOne();
    if (!customer) {
      return {};
    }
    return customer;
  }

  public async getDetails(customerId: number) {
    return this.customerRepo.findOne(customerId);
  }

  /**
   * 页面更新使用
   * @param customerId
   * @param partialUpdate
   * @param currentUser
   */
  public async update(customerId: number, partialUpdate: UpdateCustomerModel, currentUser: RoverUser) {
    const { currentOrg: orgId } = currentUser;
    //查询当前第三方是否存在
    const dbInfo = await this.customerRepo.findOne({ where: { customerId } });
    if (!dbInfo) {
      throw new BadRequestException(RoverExceptions.BadParams.NotFound);
    }
    const dbCustomerEntity = await this.customerRepo.findOne({
      where: {
        orgId: currentUser.currentOrg,
        companyId: partialUpdate.companyId,
      },
    });
    if (dbCustomerEntity && dbCustomerEntity.customerId !== customerId) {
      throw new EntityDuplicatedException(RoverExceptions.Customer.DuplicatedCompany, [dbCustomerEntity.customerId]);
    }
    let currentCustomer: CustomerEntity;
    let preLabel = '-';
    try {
      const { labelIds, departmentNames, contacts } = partialUpdate;
      if (departmentNames?.length > 0) {
        partialUpdate.customerDepartment = departmentNames.join(',');
      }
      if (contacts?.length) {
        partialUpdate.contactNames = contacts.map((contact) => `${contact.name}-${contact.phone || ''}-${contact.email || ''}`).join(',');
      } else {
        partialUpdate.contactNames = null;
      }
      //创建company详情数据
      const companyEntity = await this.companySearchService.createCompanyInfo(partialUpdate.companyId, partialUpdate.name);
      if (!companyEntity) {
        throw new BadParamsException({ message: '公司不存在!', ...CommonExceptions.Common.Request.NotFound });
      }
      const updateResult = await this.entityManager.transaction(async (manager) => {
        const dbCustomer = await manager.update(
          CustomerEntity,
          { customerId, orgId: currentUser?.currentOrg },
          pick(partialUpdate, [
            'companyId',
            'name',
            'startDate',
            'endDate',
            'creditQuota',
            'contactQuota',
            'cost',
            'groupId',
            'principal',
            'customerDepartment',
            'contactNames',
          ]),
        );
        currentCustomer = await manager.findOne(CustomerEntity, customerId);
        if (labelIds?.length > 0) {
          preLabel = (await this.labelService.getLabelKV(currentUser.currentOrg, 1))[customerId];
          // 修改标签
          const customerLabels = await manager.find(CustomerLabelEntity, { customerId });
          const dbLabelIds = customerLabels.map((t) => t.labelId);
          if (difference(labelIds, dbLabelIds)) {
            // 有不同的labelId,先移除标签
            await manager.delete(CustomerLabelEntity, { customerId });
            await manager.save(
              labelIds.map((i) =>
                Object.assign(new CustomerLabelEntity(), {
                  labelId: i,
                  customerId,
                }),
              ),
            );
          }
        } else {
          // 移除标签
          await manager.delete(CustomerLabelEntity, { customerId });
        }
        if (departmentNames?.length > 0) {
          const departmentEntities = await this.departmentService.saveDepartments(orgId, departmentNames, DepartmentTypeEnum.Customer);
          // 修改部门
          const customerDepartments = await manager.find(CustomerDepartmentEntity, { customerId });
          const dbDepartmentIds = customerDepartments.map((t) => t.departmentId);
          const newDepartmentIds = departmentEntities.map((t) => t.departmentId);
          if (difference(newDepartmentIds, dbDepartmentIds)) {
            //有不同的departmentId,先移除部门
            await manager.delete(CustomerDepartmentEntity, { customerId });
            await manager.save(
              departmentEntities.map((i) =>
                Object.assign(new CustomerDepartmentEntity(), {
                  departmentId: i.departmentId,
                  customerId,
                }),
              ),
            );
          }
        } else {
          //移除部门
          await manager.delete(CustomerDepartmentEntity, { customerId });
        }
        await this.contactService.updateContactors(currentUser.currentOrg, customerId, contacts);

        //更新时间通过ON UPDATE CURRENT_TIMESTAMP更新，label更新没有更新主表字段，强制更新一下
        await manager.update(CustomerEntity, { customerId }, { updateDate: new Date() });
        return dbCustomer;
      });
      await Bluebird.all([
        this.oplogService.add(OperatorTypeEnums.Edit, CustomerEntity, currentUser, [customerId], [dbInfo], [currentCustomer], preLabel),
        this.diligenceHistoryHelper.makeHistoryForUpdate(currentUser.currentOrg),
      ]);
      return updateResult;
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }

  /**
   * 导入时数据强制更新使用
   * @param dbCustomer
   * @param partialUpdate
   * @param currentUser
   */
  public async updateWithCompanyId(dbCustomer: CustomerEntity, partialUpdate: UpdateCustomerModel, currentUser: RoverUser) {
    const customerId = dbCustomer.customerId;
    try {
      const { labelDelete, labelInsert, preLabel } = await this.compareCustomerLabels(dbCustomer, partialUpdate, currentUser);
      const { departmentDelete, departmentInsert } = await this.compareCustomerDepartments(dbCustomer, partialUpdate, currentUser);

      await this.entityManager.transaction(async (manager) => {
        // 更新客户信息
        await manager.update(
          CustomerEntity,
          { customerId, orgId: currentUser?.currentOrg },
          pick(partialUpdate, ['startDate', 'endDate', 'creditQuota', 'contactQuota', 'cost', 'groupId', 'principal', 'customerDepartment', 'contactNames']),
        );
        if (labelDelete) {
          // 移除标签
          await manager.delete(CustomerLabelEntity, { customerId });
        }
        if (labelInsert.length) {
          // 新增标签
          await manager.insert(CustomerLabelEntity, labelInsert);
        }
        if (departmentDelete) {
          // 移除部门
          await manager.delete(CustomerDepartmentEntity, { customerId });
        }
        if (departmentInsert.length) {
          // 新增部门
          await manager.insert(CustomerDepartmentEntity, departmentInsert);
        }
      });
      const currentCustomer = await this.entityManager.findOne(CustomerEntity, customerId);
      await Bluebird.all([
        this.oplogService.add(OperatorTypeEnums.Edit, CustomerEntity, currentUser, [customerId], [dbCustomer], [currentCustomer], preLabel),
        this.diligenceHistoryHelper.makeHistoryForUpdate(currentUser.currentOrg),
      ]);
      return currentCustomer;
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }

  /**
   * 从excel中更新
   * @param currentUser
   * @param partialUpdate
   */
  public async updateFromExcel(currentUser: RoverUser, partialUpdate: UpdateCustomerModel) {
    const { currentOrg: orgId } = currentUser;
    const dbCustomerEntity = await this.customerRepo.findOne({
      where: {
        orgId: orgId,
        companyId: partialUpdate.companyId,
      },
    });
    const { by, userIds } = this.securityService.checkScope(currentUser, 2032);
    if (dbCustomerEntity && (by == PermissionByEnum.ORG || userIds?.includes(dbCustomerEntity.createBy))) {
      //当前Customer存在，更新Customer信息
      const customer2 = Object.assign(new UpdateCustomerModel(), dbCustomerEntity, partialUpdate);
      await this.updateWithCompanyId(dbCustomerEntity, customer2, currentUser);
      return customer2;
    }
  }

  async checkCountLimit(currentUser: RoverUser, num: number) {
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.ThirdPartyQuantity);
    // await bundleCounter.clear();
    await bundleCounter.check(num);
  }

  public async create(currentUser: RoverUser, postData: CreateCustomerModel, isBatch = false) {
    const { userId, currentOrg: orgId } = currentUser;
    const dbCustomerEntity = await this.customerRepo.findOne({
      where: {
        orgId: orgId,
        companyId: postData.companyId,
      },
    });
    if (dbCustomerEntity) {
      throw new EntityDuplicatedException(RoverExceptions.Customer.DuplicatedCompany, [dbCustomerEntity.customerId]);
    }
    //创建company详情数据
    const companyEntity = await this.companySearchService.createCompanyInfo(postData.companyId, postData.name);

    if (!companyEntity) {
      throw new BadParamsException({ message: '公司不存在!', ...CommonExceptions.Common.Request.NotFound });
    }
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.ThirdPartyQuantity);
    // await bundleCounter.clear();
    await bundleCounter.increase(1);
    const diligence = await this.diligenceRepo.findOne({
      where: { orgId, companyId: postData.companyId },
      order: { updateDate: 'DESC' },
    });

    const customer = Object.assign(
      new CustomerEntity(postData?.contacts),
      omit(postData, [
        'econkind',
        'province',
        'city',
        'district',
        'industry1',
        'industry2',
        'industry3',
        'industry4',
        'registcapi',
        'registcapiAmount',
        'startDateCode',
        'statusCode',
      ]),
      {
        createBy: userId,
        orgId: orgId,
        depId: currentUser.departments?.[0],
        principal: postData.principal,
        customerDepartment: postData?.departmentNames ? postData.departmentNames.join(',') : null,
        status: postData.status,
        batchId: postData.batchId,
        result: diligence?.result,
        diligenceId: diligence?.id,
        diligenceDate: diligence?.createDate,
      },
    );

    let newCustomer;
    try {
      newCustomer = await this.customerRepo.save(customer);
    } catch (error) {
      await bundleCounter.decrease(1);
      throw error;
    }

    if (postData?.labelIds?.length) {
      // 保存客户标签
      await this.customerLabelRepo.save(
        postData.labelIds.map((labelId) =>
          Object.assign(new CustomerLabelEntity(), {
            customerId: newCustomer.customerId,
            labelId,
          }),
        ),
      );
    }
    const departmentEntities = await this.departmentService.saveDepartments(orgId, postData.departmentNames, DepartmentTypeEnum.Customer);
    if (departmentEntities?.length) {
      //保存客户部门关联
      await this.customerDepartmentRepo.save(
        departmentEntities.map((department) =>
          Object.assign(new CustomerDepartmentEntity(), {
            customerId: newCustomer.customerId,
            departmentId: department.departmentId,
          }),
        ),
      );
    }
    if (postData?.contacts?.length) {
      //保存联系人
      await this.contactService.updateContactors(orgId, newCustomer.customerId, postData.contacts);
    }
    await Bluebird.all([
      this.diligenceHistoryHelper.makeHistoryForUpdate(orgId),
      this.oplogService.add(
        isBatch ? OperatorTypeEnums.BatchCreate : OperatorTypeEnums.Create,
        CustomerEntity,
        currentUser,
        [newCustomer.customerId],
        [newCustomer],
      ),
    ]);
    return newCustomer;
  }

  public async count(currentUser: RoverUser) {
    return this.customerRepo.count({ orgId: currentUser.currentOrg, status: BatchStatusEnums.Done });
  }

  public async searchCompanySummaries(currentUser: RoverUser, companyIds: (number | string)[]): Promise<CustomerEntity[]> {
    const { currentOrg: orgId } = currentUser;
    let by;
    let userIds;
    try {
      const scope = this.securityService.checkScope(currentUser, 2031);
      by = scope.by;
      userIds = scope.userIds;
    } catch (e) {
      if (e instanceof ForbiddenException) {
        return [];
      }
      throw e;
    }

    const qb = this.customerRepo
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.creator', 'creator')
      .leftJoinAndSelect('customer.labels', 'label')
      .select([
        'customer.customerId',
        'customer.groupId',
        'customer.investigateStatus',
        'customer.creditQuota',
        'customer.contactQuota',
        'customer.cost',
        'customer.startDate',
        'customer.endDate',
        'customer.principal',
        'customer.customerDepartment',
        'customer.batchId',
        'customer.status',
        'customer.createDate',
        'label',
        'creator.name',
        'creator.userId',
        'customer.companyId',
      ])
      .where('customer.orgId = :orgId', { orgId })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done });
    if (by == PermissionByEnum.USER) {
      qb.andWhere('customer.createBy in (:...userIds)', { userIds });
    }
    qb.andWhere('customer.companyId in (:...companyIds)', { companyIds });

    return qb.getMany();
  }

  public async search(currentUser: RoverUser, postData: SearchCustomerModel, notUsePage = false): Promise<SearchCustomerResponse> {
    const { selectAll, pageSize, pageIndex, sortField, isSortAsc } = postData;
    let qb: SelectQueryBuilder<CustomerEntity>;
    try {
      qb = this.getBaseQueryBuilder(currentUser, postData);
      if (!qb) {
        return {
          pageSize,
          pageIndex,
          data: [],
          total: 0,
          customerIds: [],
        };
      }
    } catch (e) {
      throw e;
    }
    let sortFieldUse = sortField;
    if (sortFieldUse == 'registcapi') {
      sortFieldUse = 'registcapiAmount';
    }
    if (sortFieldUse) {
      if (['registcapiAmount', 'startDateCode', 'creditRate', 'reccapamount'].includes(sortFieldUse)) {
        qb.addOrderBy(`company.${sortFieldUse}`, isSortAsc ? 'ASC' : 'DESC');
      } else {
        qb.addOrderBy(`customer.${sortFieldUse}`, isSortAsc ? 'ASC' : 'DESC');
      }
    } else {
      qb.addOrderBy('customer.customerId', 'DESC');
    }
    const customerIds = [];
    const labels = [];
    const departments = [];
    if (selectAll) {
      const allRaw = await qb.getMany();
      Array.prototype.push.apply(customerIds, uniq(allRaw.map((a) => a.customerId)));
      Array.prototype.push.apply(
        labels,
        uniqBy(flatten(allRaw.map((a) => a.labels)), (r: LabelEntity) => r.labelId),
      );
      Array.prototype.push.apply(
        departments,
        uniqBy(flatten(allRaw.map((a) => a.departments)), (r: DepartmentEntity) => r.departmentId),
      );
    }
    if (!notUsePage) {
      qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    }
    const [data, total] = await qb.getManyAndCount();
    const companyBusinessInfoMap: Map<string, CompanyBusinessInfo> = await this.companySearchService.getCompanyBusinessInfoMap(
      data.map((x) =>
        Object.assign(new CompanyBasicInfo(), {
          companyId: x.company.companyId,
          companyName: x.company.name,
        }),
      ),
    );
    // companyDetails.forEach((x) => detailMap.set(x.id, x));
    const resData = data.map((d) => {
      const companyBusinessInfo = companyBusinessInfoMap.get(d.company.companyId);
      return Object.assign(
        pick(d.company, [
          'name',
          'companyId',
          'econkind',
          'econkindDesc',
          'province',
          'city',
          'district',
          'industry1',
          'industry2',
          'industry3',
          'industry4',
          'registcapi',
          'registcapiAmount',
          'startDateCode',
          'statusCode',
          'creditRate',
          'econType',
          'treasuryType',
          'enterpriseType',
          'listStatus',
          'reccap',
          'reccapamount',
        ]),
        d,
        {
          company: null,
          ...companyBusinessInfo,
        },
      );
    });
    return {
      pageSize,
      pageIndex,
      data: resData,
      total,
      customerIds,
      labels,
      departments,
    };
  }

  public async aggsForSearch(currentUser: RoverUser, postData: CustomerAggsRequest): Promise<AggsResponse[]> {
    let qb: SelectQueryBuilder<CustomerEntity>;
    try {
      qb = this.getBaseQueryBuilder(currentUser, postData.query);
      if (!qb) {
        return [];
      }
    } catch (e) {
      throw e;
    }
    const aggsField = postData.aggsField;
    if (!aggsField) {
      return [];
    }
    switch (aggsField) {
      case 'operator':
        qb.select('creator.userId', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('creator.userId');
        break;
      case 'group':
        qb.select('customer.groupId', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('customer.groupId');
        break;
      case 'department':
        qb.select('departments.departmentId', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('departments.departmentId');
        break;
      case 'label':
        // @ts-ignore
        qb.select('label.labelId', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('label.labelId');
        break;
      case 'econType': {
        // @ts-ignore
        qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`company`.`econ_type`, ',', numbers.n), ',', -1)", 'fieldValue')
          .addSelect('COUNT(distinct(customer.customerId))', 'count')
          .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
          .groupBy('fieldValue');
        break;
      }
      case 'treasuryType': {
        // @ts-ignore
        qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`company`.`treasury_type`, ',', numbers.n), ',', -1)", 'fieldValue')
          .addSelect('COUNT(distinct(customer.customerId))', 'count')
          .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
          .groupBy('fieldValue');
        break;
      }
      case 'principal': {
        // // @ts-ignore
        // qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`customer`.`principal`, ',', numbers.n), ',', -1)", 'fieldValue')
        //   .addSelect('COUNT(distinct(customer.customerId))', 'count')
        //   .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
        //   .groupBy('fieldValue');
        qb.select('customer.principal', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('customer.principal');
        break;
      }

      case 'enterpriseType': {
        // @ts-ignore
        qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`company`.`enterprise_type`, ',', numbers.n), ',', -1)", 'fieldValue')
          .addSelect('COUNT(distinct(customer.customerId))', 'count')
          .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
          .groupBy('fieldValue');
        break;
      }
      case 'statusCode': {
        // @ts-ignore
        qb.select('company.statusCode', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('company.statusCode');
        break;
      }
      case 'riskLevel': {
        qb.select('customer.result', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('customer.result');
        break;
      }
      case 'listStatus': {
        // @ts-ignore
        qb.select('company.listStatus', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('company.listStatus');
        break;
      }
      case 'region': {
        qb.select('company.province', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('company.province');
        break;
      }
      case 'scale': {
        qb.select('company.scale', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('company.scale');
        break;
      }
      case 'industry': {
        qb.select('company.industry1', 'fieldValue').addSelect('COUNT(distinct(customer.customerId))', 'count').groupBy('company.industry1');
        break;
      }
      case 'registcapiAmount': {
        qb.select(
          'CASE ' +
            'WHEN company.registcapiAmount >= 0 AND company.registcapiAmount < 100 THEN :range1 ' +
            'WHEN company.registcapiAmount >= 100 AND company.registcapiAmount < 500 THEN :range2 ' +
            'WHEN company.registcapiAmount >= 500 AND company.registcapiAmount < 1000 THEN :range3 ' +
            'WHEN company.registcapiAmount >= 1000 AND company.registcapiAmount < 5000 THEN :range4 ' +
            'WHEN company.registcapiAmount >= 5000 THEN :range5 ' +
            'ELSE :otherRange ' +
            'END',
          'fieldValue',
        )
          .addSelect('COALESCE(COUNT(DISTINCT customer.customerId), 0)', 'count')
          .groupBy('fieldValue')
          .setParameters({
            range1: '100万以内',
            range2: '100-500万',
            range3: '500-1000万',
            range4: '1000-5000万',
            range5: '5000万元以上',
            otherRange: '其他',
          });
        break;
      }
      case 'reccapamount': {
        qb.select(
          'CASE ' +
            'WHEN company.reccapamount >= 0 AND company.reccapamount < 100 THEN :range1 ' +
            'WHEN company.reccapamount >= 100 AND company.reccapamount < 500 THEN :range2 ' +
            'WHEN company.reccapamount >= 500 AND company.reccapamount < 1000 THEN :range3 ' +
            'WHEN company.reccapamount >= 1000 AND company.reccapamount < 5000 THEN :range4 ' +
            'WHEN company.reccapamount >= 5000 THEN :range5 ' +
            'ELSE :otherRange ' +
            'END',
          'fieldValue',
        )
          .addSelect('COALESCE(COUNT(DISTINCT customer.customerId), 0)', 'count')
          .groupBy('fieldValue')
          .setParameters({
            range1: '100万以内',
            range2: '100-500万',
            range3: '500-1000万',
            range4: '1000-5000万',
            range5: '5000万元以上',
            otherRange: '其他',
          });
        break;
      }
      case 'startDateCode': {
        const dateR1 = this.getStartEndDate(0, 5);
        const dateR2 = this.getStartEndDate(5, 10);
        const dateR3 = this.getStartEndDate(10, 20);
        const dateR4 = this.getStartEndDate(20, 30);
        const dateR5 = this.getStartEndDate(30, 300);
        qb.select(
          'CASE ' +
            `WHEN company.startDateCode >= '${dateR1.start}' and company.startDateCode < '${dateR1.end}' THEN :range1 ` +
            `WHEN company.startDateCode >= '${dateR2.start}' and company.startDateCode < '${dateR2.end}' THEN :range2 ` +
            `WHEN company.startDateCode >= '${dateR3.start}' and company.startDateCode < '${dateR3.end}' THEN :range3 ` +
            `WHEN company.startDateCode >= '${dateR4.start}' and company.startDateCode < '${dateR4.end}' THEN :range4 ` +
            `WHEN company.startDateCode >= '${dateR5.start}' and company.startDateCode < '${dateR5.end}' THEN :range5 ` +
            'END',
          'fieldValue',
        )
          .addSelect('COALESCE(COUNT(DISTINCT customer.customerId), 0)', 'count')
          .groupBy('fieldValue')
          .setParameters({
            range1: '5年内',
            range2: '5-10年',
            range3: '10-20年',
            range4: '20-30年',
            range5: '30年以上',
          });
        break;
      }
      case 'creditRate': {
        qb.select(
          'CASE ' +
            'WHEN company.creditRate >= 900 THEN :range1 ' +
            'WHEN company.creditRate >= 750 AND company.creditRate < 899 THEN :range2 ' +
            'WHEN company.creditRate >= 600 AND company.creditRate < 749 THEN :range3 ' +
            'WHEN company.creditRate >= 500 AND company.creditRate < 599 THEN :range4 ' +
            'WHEN company.creditRate < 500 THEN :range5 ' +
            'ELSE :otherRange ' +
            'END',
          'fieldValue',
        )
          .addSelect('COALESCE(COUNT(DISTINCT customer.customerId), 0)', 'count')
          .groupBy('fieldValue')
          .setParameters({
            range1: '900分以上',
            range2: '750-899分',
            range3: '600-749分',
            range4: '500-599分',
            range5: '500分以下',
            otherRange: '其他',
          });
        break;
      }
      case 'employeecount': {
        qb.select(
          'CASE ' +
            'WHEN company.employeecount >= 1 AND company.employeecount < 19 THEN :range1 ' +
            'WHEN company.employeecount >= 20 AND company.employeecount < 99 THEN :range2 ' +
            'WHEN company.employeecount >= 100 AND company.employeecount < 299 THEN :range3 ' +
            'WHEN company.employeecount >= 300 AND company.employeecount < 999 THEN :range4 ' +
            'WHEN company.employeecount >= 1000 THEN :range5 ' +
            'ELSE :otherRange ' +
            'END',
          'fieldValue',
        )
          .addSelect('COALESCE(COUNT(DISTINCT customer.customerId), 0)', 'count')
          .groupBy('fieldValue')
          .setParameters({
            range1: '1-19人',
            range2: '20-99人',
            range3: '100-299人',
            range4: '300-999人',
            range5: '1000人及以上',
            otherRange: '未披露',
          });
        break;
      }
      case 'companyRevenue': {
        qb.select(
          'CASE ' +
            'WHEN company.companyRevenue >= 0 AND company.companyRevenue < 100 THEN :range1 ' +
            'WHEN company.companyRevenue >= 100 AND company.companyRevenue < 500 THEN :range2 ' +
            'WHEN company.companyRevenue >= 500 AND company.companyRevenue < 2000 THEN :range3 ' +
            'WHEN company.companyRevenue >= 2000 AND company.companyRevenue < 5000 THEN :range4 ' +
            'WHEN company.companyRevenue >= 5000 AND company.companyRevenue < 10000 THEN :range5 ' +
            'WHEN company.companyRevenue >= 10000 AND company.companyRevenue < 100000 THEN :range6 ' +
            'WHEN company.companyRevenue >= 100000 THEN :range7 ' +
            'ELSE :otherRange ' +
            'END',
          'fieldValue',
        )
          .addSelect('COALESCE(COUNT(DISTINCT customer.customerId), 0)', 'count')
          .groupBy('fieldValue')
          .setParameters({
            range1: '100万以下',
            range2: '100-500万',
            range3: '500-2000万',
            range4: '2000-5000万',
            range5: '5000万-1亿元',
            range6: '1亿-10亿元',
            range7: '10亿元以上',
            otherRange: '未披露',
          });
        break;
      }
      case 'insuredCount': {
        qb.select(
          'CASE ' +
            'WHEN company.insuredCount = 0 THEN :range1 ' +
            'WHEN company.insuredCount >= 1 AND company.insuredCount <= 19 THEN :range2 ' +
            'WHEN company.insuredCount >= 20 AND company.insuredCount <= 99 THEN :range3 ' +
            'WHEN company.insuredCount >= 100 AND company.insuredCount <= 299 THEN :range4 ' +
            'WHEN company.insuredCount >= 300 AND company.insuredCount <= 999 THEN :range5 ' +
            'WHEN company.insuredCount >= 1000 THEN :range6 ' +
            'ELSE :otherRange ' +
            'END',
          'fieldValue',
        )
          .addSelect('COALESCE(COUNT(DISTINCT customer.customerId), 0)', 'count')
          .groupBy('fieldValue')
          .setParameters({
            range1: '0人',
            range2: '1-19人',
            range3: '20-99人',
            range4: '100-299人',
            range5: '300-999人',
            range6: '1000人及以上',
            otherRange: '未披露',
          });
        break;
      }
    }
    const rows = await qb.getRawMany();
    switch (aggsField) {
      case 'operator': {
        const operators = await this.userRepo.findByIds(rows.map((r) => r.fieldValue));
        return rows.map((r) => {
          if (!r.fieldValue) return;
          const operator = operators.find((d) => d.userId == r.fieldValue);
          r.fieldLabel = operator?.name || '';
          r.count = Number(r.count);
          return r;
        });
      }
      case 'department': {
        const departments = await this.departmentRepo.findByIds(rows.map((r) => r.fieldValue));
        return rows.map((r) => {
          if (!r.fieldValue && Number(r.count) > 0) {
            r.fieldValue = -1;
            r.fieldLabel = '未分配部门';
          } else {
            const department = departments.find((d) => d.departmentId == r.fieldValue);
            r.fieldLabel = department?.name || '';
            r.count = Number(r.count);
          }
          return r;
        });
      }

      case 'label': {
        const labels = await this.labelRepo.findByIds(rows.map((r) => r.fieldValue));
        return rows.map((r) => {
          if (!r.fieldValue && Number(r.count) > 0) {
            r.fieldValue = -1;
            r.fieldLabel = '无标签';
          } else {
            const label = labels.find((d) => d.labelId == r.fieldValue);
            r.fieldLabel = label?.name || '';
            r.count = Number(r.count);
          }
          return r;
        });
      }

      case 'principal': {
        const result = [];
        const noPerson = {
          fieldLabel: '无负责人',
          fieldValue: 'null',
          count: 0,
        };
        rows.forEach((r) => {
          if (r?.fieldValue) {
            result.push({
              fieldLabel: r?.fieldValue,
              fieldValue: r?.fieldValue,
              count: Number(r.count),
            });
          } else {
            noPerson.count += Number(r.count);
          }
        });
        if (noPerson.count > 0) {
          result.push(noPerson);
        }
        return result;
      }

      case 'group': {
        const groups = await this.groupRepo.find({
          where: {
            orgId: currentUser.currentOrg,
            groupType: GroupType.CustomerGroup,
          },
          order: { order: 'DESC' },
        });
        const result = [];
        const noGroupRow = rows.find((r) => r.fieldValue == null);
        result.push({
          fieldLabel: '未分组',
          fieldValue: -1,
          count: Number(noGroupRow?.count) || 0,
        });
        groups.forEach((g) => {
          const row = rows.find((r) => g.groupId == r.fieldValue);
          result.push({
            fieldLabel: g.name,
            fieldValue: g.groupId + '',
            count: Number(row?.count) || 0,
          });
        });
        return result;
      }

      case 'registcapiAmount':
      case 'reccapamount': {
        const labels = ['100万以内', '100-500万', '500-1000万', '1000-5000万', '5000万元以上'];
        return this.processAggsData(labels, rows);
      }
      case 'startDateCode': {
        const labels = ['5年内', '5-10年', '10-20年', '20-30年', '30年以上'];
        return this.processAggsData(labels, rows);
      }
      case 'creditRate': {
        const labels = ['900分以上', '750-899分', '600-749分', '500-599分', '500分以下'];
        return this.processAggsData(labels, rows);
      }
      case 'employeecount': {
        const labels = ['1-19人', '20-99人', '100-299人', '300-999人', '1000人及以上', '未披露'];
        return this.processAggsData(labels, rows);
      }
      case 'companyRevenue': {
        const labels = ['100万以下', '100-500万', '500-2000万', '2000-5000万', '5000万-1亿元', '1亿-10亿元', '10亿元以上', '未披露'];
        return this.processAggsData(labels, rows);
      }
      case 'insuredCount': {
        const labels = ['0人', '1-19人', '20-99人', '100-299人', '300-999人', '1000人及以上', '未披露'];
        return this.processAggsData(labels, rows);
      }

      default:
        return rows.map((r) => {
          r.count = Number(r.count);
          return r;
        });
    }
  }

  private getStartEndDate(min: number, max: number, unit = 'year') {
    const dateRange = Object.assign(new DateRangeRelative(), {
      currently: true,
      flag: 4,
      min,
      max,
      unit,
    });
    return RequestUtils.ProcessDRR(dateRange, DATE_TIME_FORMAT);
  }

  public async customerChart(currentUser: RoverUser) {
    const chartFields = [
      CustomerAggsFieldEnums.region,
      CustomerAggsFieldEnums.group,
      CustomerAggsFieldEnums.label,
      CustomerAggsFieldEnums.industry,
      CustomerAggsFieldEnums.scale,
      CustomerAggsFieldEnums.statusCode,
      CustomerAggsFieldEnums.econType,
      CustomerAggsFieldEnums.treasuryType,
      CustomerAggsFieldEnums.registcapiAmount,
      CustomerAggsFieldEnums.startDateCode,
      CustomerAggsFieldEnums.creditRate,
      CustomerAggsFieldEnums.reccapamount,
      CustomerAggsFieldEnums.employeecount,
      CustomerAggsFieldEnums.companyRevenue,
      CustomerAggsFieldEnums.insuredCount,
      CustomerAggsFieldEnums.department,
      CustomerAggsFieldEnums.principal,
    ];
    const customerSearch = Object.assign(new SearchCustomerModel(), {
      pageSize: 1,
      pageIndex: 1,
    });
    const fieldAggs: FieldAggsResponse[] = await Bluebird.map(
      chartFields,
      async (field) => {
        const result = await this.aggsForSearch(
          currentUser,
          Object.assign(new CustomerAggsRequest(), {
            query: customerSearch,
            aggsField: field,
          }),
        );
        if (result.length > 0) {
          return {
            field,
            aggs: result,
          };
        }
        return null;
      },
      { concurrency: 3 }, // 控制并发数，避免过多并发影响性能
    ).filter((item) => item !== null);
    const qb = this.getBaseQueryBuilder(currentUser, customerSearch);
    const totalCount = await qb.getCount();
    const fieldAggsMap = fieldAggs.reduce((acc, { field, aggs }) => {
      acc[field] = aggs;
      return acc;
    }, {});
    fieldAggsMap['totalCount'] = totalCount;
    return fieldAggsMap;
  }

  private processAggsData(labels: string[], rows: any[]) {
    const result = [];
    labels.forEach((r) => {
      const row = rows.find((rr) => rr.fieldValue === r);
      if (row) {
        result.push({
          fieldValue: r,
          count: Number(row.count) || 0,
        });
      } else {
        result.push({
          fieldValue: r,
          count: 0,
        });
      }
    });
    return result;
  }

  /**
   * 生产查询条件
   * @param currentUser
   * @param postData
   * @private
   */
  private getBaseQueryBuilder(currentUser: RoverUser, postData: SearchCustomerModel) {
    const { currentOrg: orgId } = currentUser;
    const {
      searchKey,
      groupIds,
      labelIds,
      companyIds,
      ids,
      registcapiAmount,
      startDateCode,
      statusCode,
      operators,
      result,
      customerDepartment,
      companyNames,
      companyNos,
      departmentIds,
      depIds,
      principals,
      listStatus,
      reccapamount,
      scale,
    } = postData;
    let by;
    let userIds;
    try {
      const scope = this.securityService.checkScope(currentUser, 2031);
      by = scope.by;
      userIds = scope.userIds;
    } catch (e) {
      if (e instanceof ForbiddenException) {
        return null;
      }
      throw e;
    }

    const qb = this.customerRepo
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.creator', 'creator')
      .leftJoinAndSelect('customer.labels', 'label')
      .leftJoinAndSelect('customer.company', 'company')
      .leftJoinAndSelect('customer.departments', 'departments')
      .leftJoinAndSelect('customer.contacts', 'contacts')
      .select([
        'customer.customerId',
        'customer.groupId',
        'customer.investigateStatus',
        'customer.creditQuota',
        'customer.contactQuota',
        'customer.cost',
        'customer.startDate',
        'customer.endDate',
        'customer.principal',
        'customer.customerDepartment',
        'customer.batchId',
        'customer.status',
        'customer.createDate',
        'customer.updateDate',
        'customer.depId',
        'customer.result',
        'customer.diligenceId',
        'customer.diligenceDate',
        'customer.contactNames',
        'company',
        'label',
        'departments',
        'contacts',
        'creator.name',
        'creator.userId',
      ])
      .where('customer.orgId = :orgId', { orgId })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done });
    if (by == PermissionByEnum.USER) {
      qb.andWhere('customer.createBy in (:...userIds)', { userIds });
    }
    if (searchKey) {
      qb.andWhere(
        new Brackets((qb1) => {
          qb1.orWhere('company.name like :name', { name: `%${searchKey}%` });
          qb1.orWhere('company.creditcode = :creditcode', { creditcode: searchKey });
        }),
      );
    }

    if (listStatus) {
      qb.andWhere('company.listStatus =:listStatus', { listStatus });
    }
    if (companyNames?.length) {
      qb.andWhere('company.name in (:...companyNames)', { companyNames });
    }
    if (companyNos?.length) {
      qb.andWhere('company.companyId in (:...companyNos)', { companyNos });
    }

    if (ids?.length) {
      qb.andWhere('customer.id in (:...ids)', { ids });
    }

    if (depIds?.length) {
      qb.andWhere('customer.depId in (:...depIds)', { depIds });
    }
    this.applyGroupIds(qb, groupIds);
    if (labelIds?.length) {
      qb.leftJoin(CustomerLabelEntity, 'customer_labels', 'customer_labels.customerId = customer.customerId');
      if (labelIds.includes(-1)) {
        if (labelIds.length === 1) {
          qb.andWhere('label.labelId is null');
        } else {
          const labelResult = labelIds.filter((r) => r >= 0);
          qb.andWhere(
            new Brackets((qb1) => {
              qb1.orWhere('label.labelId is null');
              qb1.orWhere('customer_labels.labelId in (:...labelIds)', { labelIds: labelResult });
            }),
          );
        }
      } else {
        qb.andWhere('customer_labels.labelId in (:...labelIds)', { labelIds });
      }
    }

    if (departmentIds?.length) {
      if (departmentIds.includes(-1)) {
        if (departmentIds.length === 1) {
          qb.andWhere('departments.departmentId is null');
        } else {
          const depIdResult = departmentIds.filter((r) => r >= 0);
          qb.andWhere(
            new Brackets((qb1) => {
              qb1.orWhere('departments.departmentId is null');
              qb1.orWhere('departments.departmentId in (:...depIdResult)', { depIdResult });
            }),
          );
        }
      } else {
        qb.andWhere('departments.departmentId in (:...departmentIds)', { departmentIds });
      }
    }
    if (operators?.length) {
      qb.andWhere('customer.createBy in (:...operators)', { operators });
    }
    if (companyIds?.length) {
      qb.andWhere('customer.companyId in (:...companyIds)', { companyIds });
    }

    if (principals?.length) {
      if (principals.includes('null')) {
        if (principals.length === 1) {
          qb.andWhere('(customer.principal is null or customer.principal = "")');
        } else {
          qb.andWhere(
            new Brackets((qb1) => {
              qb1.orWhere('customer.principal is null');
              qb1.orWhere('customer.principal = ""');
              qb1.orWhere('customer.principal in (:...principals)', { principals });
            }),
          );
        }
      } else {
        qb.andWhere('customer.principal in (:...principals)', { principals });
      }
    }

    if (result && result.length > 0) {
      qb.andWhere('customer.result in (:...result)', { result });
    }

    // 创建时间
    QueryBuilderHelper.applyDateRangeQuery(qb, postData?.createDate, 'createDate');
    //更新时间
    QueryBuilderHelper.applyDateRangeQuery(qb, postData?.updateDate, 'updateDate');
    // 企业类型
    if (postData?.econKindCode?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          const econKindMap = {};
          postData.econKindCode.forEach((econKind) => {
            econKindMap[econKind] = econKind;
          });
          postData.econKindCode.forEach((key) => {
            if (key) {
              qb1.orWhere(`find_in_set(:${key}, company.econkind)`, { [key]: econKindMap[key] });
            } else {
              qb1.orWhere('company.econkind is null');
            }
          });
        }),
      );
    }

    // 企业性质
    if (postData?.econType?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          postData.econType.forEach((key) => {
            qb1.orWhere(`find_in_set(:${key}, company.econType)`, { [key]: key });
          });
        }),
      );
    }

    // 司库类型
    if (postData?.treasuryType?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          postData.treasuryType.forEach((key) => {
            qb1.orWhere(`find_in_set(:${key}, company.treasuryType)`, { [key]: key });
          });
        }),
      );
    }

    // 机构类型
    if (postData?.enterpriseType?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          postData.enterpriseType.forEach((key) => {
            qb1.orWhere(`find_in_set(:${key}, company.enterpriseType)`, { [key]: key });
          });
        }),
      );
    }

    // 国民行业
    if (postData?.industry?.length) {
      const params = {};
      const industrySql = [];
      for (let i = 0; i < postData.industry?.length; i++) {
        const industry = postData.industry[i];
        if (industry.i4) {
          const key = 'i4' + i;
          params[key] = industry.i4;
          industrySql.push(`company.industry4 = :${key}`);
        } else if (industry.i3) {
          const key = 'i3' + i;
          params[key] = industry.i3;
          industrySql.push(`company.industry3 = :${key}`);
        } else if (industry.i2) {
          const key = 'i2' + i;
          params[key] = industry.i2;
          industrySql.push(`company.industry2 = :${key}`);
        } else if (industry.i1) {
          const key = 'i1' + i;
          params[key] = industry.i1;
          industrySql.push(`company.industry1 = :${key}`);
        }
      }
      qb.andWhere(`(${industrySql.join(' OR ')})`, params);
    }

    // 行政地区
    if (postData?.region?.length) {
      const params = {};
      const regionSql = [];
      for (let i = 0; i < postData.region?.length; i++) {
        const region = postData.region[i];
        if (region.dt) {
          const key = 'dt' + i;
          params[key] = region.dt;
          regionSql.push(`company.district = :${key}`);
        } else if (region.ct) {
          const key = 'ct' + i;
          params[key] = region.ct;
          regionSql.push(`company.city = :${key}`);
        } else if (region.pr) {
          const key = 'pr' + i;
          params[key] = region.pr;
          regionSql.push(`company.province = :${key}`);
        }
      }
      qb.andWhere(`(${regionSql.join(' OR ')})`, params);
    }

    //成立日期
    QueryBuilderHelper.applyDateRangeQuery(qb, startDateCode, 'company.startDateCode');

    //注册状态
    if (statusCode?.length) {
      qb.andWhere('company.statusCode in (:...statusCode)', { statusCode });
    }

    //注册资本
    QueryBuilderHelper.applyFilterModelRangeQuery(qb, registcapiAmount, 'company.registcapiAmount');

    //实缴资本
    QueryBuilderHelper.applyFilterModelRangeQuery(qb, reccapamount, 'company.reccapamount');
    //企业规模
    if (scale?.length) {
      qb.andWhere('company.scale in (:...scale)', { scale });
    }

    if (customerDepartment?.length) {
      qb.andWhere('customer.customerDepartment in (:...customerDepartment) ', { customerDepartment });
    }

    return qb;
  }

  async remove(currentUser: RoverUser, postData: SearchCustomerModel) {
    const { currentOrg: orgId } = currentUser;
    let operatorType = OperatorTypeEnums.Remove;
    const {
      searchKey,
      groupIds,
      labelIds,
      companyIds,
      ids,
      registcapiAmount,
      startDateCode,
      statusCode,
      operators,
      result,
      customerDepartment,
      companyNames,
      selectAll,
    } = postData;

    if (
      !selectAll &&
      (!searchKey || searchKey.length < 2) &&
      !groupIds?.length &&
      !labelIds?.length &&
      !companyIds?.length &&
      !ids?.length &&
      !registcapiAmount?.length &&
      !startDateCode?.length &&
      !statusCode?.length &&
      !operators?.length &&
      !result?.length &&
      !customerDepartment &&
      !companyNames?.length
    ) {
      throw new BadRequestException('删除企业时候，请至少选择一个条件');
    }
    let qb: SelectQueryBuilder<CustomerEntity>;
    try {
      qb = this.getBaseQueryBuilder(currentUser, postData);
      if (!qb) {
        return {};
      }
    } catch (e) {
      throw e;
    }
    const preCustomers = await qb.getMany();
    if (!preCustomers?.length) {
      return null;
    }
    const deleteIds = preCustomers.map((m) => m.customerId);
    preCustomers.forEach((f) => Object.assign(f, omit(f.company, ['id'])));
    if (deleteIds.length > 1) {
      operatorType = OperatorTypeEnums.BatchRemove;
    }
    //先做同步再删除
    await this.graphService.syncToNebula(currentUser.currentOrg, deleteIds, StreamTableEnums.Customer, StreamOperationEnum.DELETE);
    const deleteResult = await this.customerRepo.delete({ orgId, customerId: In(deleteIds) });
    // 恢复套餐额度
    const depIdsGroup = groupBy(preCustomers, (g) => g?.depId);
    await Bluebird.map(Object.keys(depIdsGroup), async (depId) => {
      const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.ThirdPartyQuantity, depId);
      await bundleCounter.decrease(depIdsGroup[depId].length);
    });
    await Bluebird.all([
      this.diligenceHistoryHelper.makeHistoryForUpdate(orgId),
      this.oplogService.add(operatorType, CustomerEntity, currentUser, deleteIds, preCustomers),
      //删除customer和label绑定记录
      this.customerLabelRepo.delete({ customerId: In(deleteIds) }),
      //删除customer和department绑定记录
      this.customerDepartmentRepo.delete({ customerId: In(deleteIds) }),
      this.contactService.batchDeleteContactors(orgId, deleteIds),
    ]);
    return deleteResult;
  }

  /**
   * 批量更新customer分组信息
   * @param currentUser
   * @param postData
   * @returns
   */
  public async changeBatchCustomers(currentUser: RoverUser, postData: GroupChangeRequest): Promise<AffectedResponse> {
    const { departments, currentOrg: orgId } = currentUser;
    const { customerIds, groupId } = postData;
    const affectedResponse: AffectedResponse = new AffectedResponse();
    affectedResponse.affected = 0;
    //1.判断分组是否存在
    //2.如果 groupId为-1，则表示移动分组到默认分组
    const updateData = {
      groupId,
      orgId,
      depId: departments?.[0],
    };
    const preCustomerLists = await this.customerRepo.findByIds(customerIds);
    if (groupId === -1) {
      updateData.groupId = null;
    } else {
      const resultGroup = await this.groupRepo.findOne({ groupId });
      if (!resultGroup) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Group.NotFound);
      }
    }
    //2.批量更新customer分组信息
    const updateCustomerResult = await this.customerRepo
      .createQueryBuilder()
      .update(CustomerEntity)
      .set(updateData)
      .where('customerId in (:ids)', { ids: customerIds })
      .execute();
    const currentCustomerLists = await this.customerRepo.findByIds(customerIds);
    await Bluebird.all([
      this.oplogService.add(OperatorTypeEnums.Edit, CustomerEntity, currentUser, customerIds, preCustomerLists, currentCustomerLists),
      this.diligenceHistoryHelper.makeHistoryForUpdate(orgId),
    ]);
    affectedResponse.affected = updateCustomerResult?.affected;
    return affectedResponse;
  }

  /**
   * 批量追加标签
   * @param currentUser
   * @param postData
   */
  public async customerBatchModify(currentUser: RoverUser, postData: LabelModifyRequest): Promise<AffectedResponse> {
    const affectedResponse: AffectedResponse = new AffectedResponse();
    affectedResponse.affected = 0;
    if (postData?.labelIds?.length === 0) {
      //全部删除所有标签
      await this.customerLabelRepo.delete({ customerId: In(postData.customerIds) });
      affectedResponse.affected = postData?.customerIds?.length || 0;
      return affectedResponse;
    }
    //1.判断标签是否存在
    const total = await this.labelRepo.count({ labelId: In(postData.labelIds) });
    if (total === 0 || total < postData?.labelIds?.length) {
      throw new BadRequestException(RoverExceptions.GroupRelated.Label.NotFound);
    }
    //2.批量更新
    const customers = await this.customerRepo.find({
      customerId: In(postData.customerIds),
      orgId: currentUser?.currentOrg,
    });
    if (customers) {
      await Bluebird.map(customers, async (customer: CustomerEntity) => {
        const customerLabels = await this.customerLabelRepo.find({ customerId: customer.customerId });
        const dbLabelIds = customerLabels.map((t) => t.labelId);
        if (difference(postData.labelIds, dbLabelIds)) {
          // 有不同的labelId,先移除标签
          await this.customerLabelRepo.delete({ customerId: customer.customerId });
          await this.customerLabelRepo.save(
            postData.labelIds.map((i) =>
              Object.assign(new CustomerLabelEntity(), {
                labelId: i,
                customerId: customer.customerId,
              }),
            ),
          );
        }
      });
      affectedResponse.affected = customers.length;
    }

    return affectedResponse;
  }

  async getMonitorStatus(currentUser: RoverUser, data: SearchCustomerMonitorStatusModel) {
    const { pageSize, pageIndex, searchKey, monitorStatus, groupIds } = data;
    const { currentOrg: orgId } = currentUser;
    let by;
    let userIds;
    try {
      const scope = this.securityService.checkScope(currentUser, 2031);
      by = scope.by;
      userIds = scope.userIds;
    } catch (e) {
      if (e instanceof ForbiddenException) {
        return null;
      }
      throw e;
    }
    const qb = this.customerRepo
      .createQueryBuilder('customer')
      .select('customer.name', 'companyName')
      .addSelect('customer.company_id', 'companyId')
      .addSelect('customer.group_id', 'groupId')
      .addSelect(`(SELECT COUNT(1) FROM monitor_group_company mgc WHERE customer.company_id = mgc.company_id AND mgc.org_id = ${orgId})`, 'monitorStatus')
      .addSelect(`(SELECT c.status_code FROM company c WHERE customer.company_id = c.company_id)`, 'statusCode')
      .where('customer.org_id = :orgId', { orgId })
      .distinct(true);
    if (by == PermissionByEnum.USER) {
      qb.andWhere('customer.createBy in (:...userIds)', { userIds });
    }
    if (searchKey) {
      qb.andWhere('customer.name like :name', { name: `%${searchKey}%` });
    }
    if (monitorStatus) {
      qb.andWhere(`(SELECT COUNT(1) FROM monitor_group_company mgc WHERE customer.company_id = mgc.company_id AND mgc.org_id = ${orgId}) > 0`);
    } else if (monitorStatus == 0) {
      qb.andWhere(`(SELECT COUNT(1) FROM monitor_group_company mgc WHERE customer.company_id = mgc.company_id AND mgc.org_id = ${orgId}) = 0`);
    }
    this.applyGroupIds(qb, groupIds);
    qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    const responseData = await qb.getRawMany();

    const companyBusinessInfoMap: Map<string, CompanyBusinessInfo> = await this.companySearchService.getCompanyBusinessInfoMap(
      responseData.map((x) =>
        Object.assign(new CompanyBasicInfo(), {
          companyId: x.companyId,
          companyName: x.companyName,
        }),
      ),
    );
    responseData.forEach((e) => {
      const companyBusinessInfo = companyBusinessInfoMap.get(e.companyId);
      e.monitorStatus = parseInt(e.monitorStatus);
      e.t_type = companyBusinessInfo?.t_type;
      return e;
    });
    const total = await qb.getCount();
    return {
      pageSize,
      pageIndex,
      responseData,
      total,
    };
  }

  /**
   * 获取第三方的部门和负责人列表
   * @param currentUser
   * @param key
   * @returns
   */
  async aggregation(currentUser: RoverUser, key: string) {
    const { currentOrg: orgId } = currentUser;
    try {
      const { by, userIds } = this.securityService.checkScope(currentUser, 2031);
      if (key === 'principal') {
        const qb = this.customerRepo.createQueryBuilder('customer').select(`customer.${key}`, key).distinct(true).where('customer.orgId = :orgId', { orgId });
        if (by == PermissionByEnum.USER) {
          qb.andWhere('customer.createBy in (:...userIds)', { userIds });
        }
        return qb.getRawMany().then((list) => list.map((t) => t[key]).filter((t) => t));
      } else if (key === 'customerDepartment') {
        const roverDepartments: DepartmentEntity[] = await this.departmentService.getDepartmentList(orgId, DepartmentTypeEnum.Customer, by, userIds);

        //获取企业中心的部门
        const eDepartments: EnterpriseDepartment[] = await this.httpUtilsService.postRequest(this.configService.enterpriseServer.getDepartments, { orgId });
        if (eDepartments) {
          const uniqEDepartment = eDepartments
            .filter((eDepartment) => !roverDepartments.some((roverDepartment) => roverDepartment.name === eDepartment.name))
            .map((eDepartment) => Object.assign(new DepartmentEntity(), { name: eDepartment.name }));
          roverDepartments.push(...uniqEDepartment);
        }
        return roverDepartments;
      }
    } catch (e) {
      if (e instanceof ForbiddenException) {
        return [];
      }
      throw e;
    }
  }

  public async match(currentUser: RoverUser, req: MatchCustomerModel) {
    const { companyIds } = req;
    const qb = this.customerRepo
      .createQueryBuilder('customer')
      .select(['customer'])
      .andWhere('customer.orgId = :orgId', { orgId: currentUser.currentOrg })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done })
      .andWhere('customer.companyId in (:...companyIds)', { companyIds });
    const [data] = await qb.getManyAndCount();
    return data;
  }

  /**
   * 查看第三方的查查信用分详情
   * @param currentUser
   * @param customerId
   * @returns
   */
  async creditRate(currentUser: RoverUser, customerId: number) {
    const c = await this.customerRepo.findOne(customerId);
    if (!c || c.orgId !== currentUser.currentOrg) {
      throw new BadRequestException(RoverExceptions.Customer.NotValidCompany);
    }
    return this.companySearchService.getCreditRate(c.companyId);
  }

  /**
   * 更新客户的调查信息
   *
   * @param orgId
   * @param companyIds
   */
  async updateCustomerDiligenceInfo(orgId: number, companyIds?: string[]) {
    this.logger.log(`开始更新客户的调查信息，orgId=${orgId}, companyIds=${companyIds}`);
    let count = 0;
    let potentialLoopTimes = 0;
    const pageSize = 300;
    do {
      const qb = this.customerRepo
        .createQueryBuilder('customer')
        .where('customer.orgId = :orgId', { orgId })
        .andWhere('customer.status = :status', { status: BatchStatusEnums.Done })
        .andWhere('( customer.result  is null or customer.result = -1) ');
      if (companyIds?.length) {
        qb.andWhere('customer.companyId in (:...companyIds)', { companyIds });
      }
      const items = await qb.take(pageSize).getMany();
      count += items.length;
      if (items.length) {
        const sql = `SELECT * FROM (SELECT company_id, org_id, id as diligenceId, result, create_date, update_date, ROW_NUMBER() OVER (PARTITION BY company_id ORDER BY id DESC) AS row_num FROM due_diligence WHERE org_id = ${orgId} and company_id IN (${items
          .map((i) => `'${i.companyId}'`)
          .join(',')}) ORDER BY id DESC) a WHERE a.row_num = 1;`;
        const rows = await this.customerRepo.query(sql);
        if (!rows.length) {
          potentialLoopTimes++;
        } else {
          await Bluebird.map(
            rows,
            async (row) => {
              const item = items.find((i) => i.companyId === row.company_id);
              if (item) {
                return this.customerRepo.update(item.customerId, {
                  result: row.result,
                  diligenceId: row.diligenceId,
                  diligenceDate: row.update_date,
                });
              }
            },
            { concurrency: 10 },
          );
        }
        // 因为 latest_diligence_result 可能会被查询过程中修改，所以需要防止死循环
        // 如果连续两次的客户列表都没有找到任何尽调记录
        if (!items.length || items.length < pageSize || potentialLoopTimes > 1) {
          break;
        }
      } else {
        break;
      }
    } while (true);

    return count;
  }

  private async compareCustomerLabels(dbCustomer: CustomerEntity, partialUpdate: UpdateCustomerModel, currentUser: RoverUser) {
    let preLabel = '-';
    let labelDelete = false;
    const labelInsert: CustomerLabelEntity[] = [];
    const { labelIds } = partialUpdate;
    const { customerId } = dbCustomer;
    if (labelIds?.length > 0) {
      preLabel = (await this.labelService.getLabelKV(currentUser.currentOrg, 1))[customerId];
      // 修改标签
      const customerLabels = await this.entityManager.find(CustomerLabelEntity, { customerId });
      const dbLabelIds = customerLabels.map((t) => t.labelId);
      if (difference(labelIds, dbLabelIds)) {
        labelDelete = true;
        labelIds.forEach((labelId) => {
          const customerLabel = Object.assign(new CustomerLabelEntity(), {
            labelId,
            customerId,
          });
          labelInsert.push(customerLabel);
        });
      }
    } else {
      // 移除标签
      labelDelete = true;
    }
    return { preLabel, labelDelete, labelInsert };
  }

  private async compareCustomerDepartments(dbCustomer: CustomerEntity, partialUpdate: UpdateCustomerModel, currentUser: RoverUser) {
    let departmentDelete = false;
    const { departmentNames } = partialUpdate;
    const { customerId } = dbCustomer;
    const { currentOrg: orgId } = currentUser;
    const departmentInsert = [];
    if (departmentNames?.length > 0) {
      partialUpdate.customerDepartment = departmentNames.join(',');
      const dbDepartments = await this.departmentRepo.find({
        name: In(departmentNames),
        orgId,
        departmentType: DepartmentTypeEnum.Customer,
      });
      const newDepartmentIds = dbDepartments.map((t) => t.departmentId);
      const customerDepartments = await this.entityManager.find(CustomerDepartmentEntity, { customerId });
      const dbDepartmentIds = customerDepartments.map((t) => t.departmentId);
      if (difference(newDepartmentIds, dbDepartmentIds)) {
        //有不同的departmentId,先移除部门
        departmentDelete = true;
        newDepartmentIds.forEach((departmentId) => {
          const customerDepartment = Object.assign(new CustomerDepartmentEntity(), {
            customerId,
            departmentId,
          });
          departmentInsert.push(customerDepartment);
        });
      }
    } else {
      departmentDelete = true;
    }
    return { departmentDelete, departmentInsert };
  }

  private applyGroupIds(qb: SelectQueryBuilder<CustomerEntity>, groupIds: number[]) {
    if (groupIds?.length) {
      if (groupIds?.includes(-1) && groupIds?.length === 1) {
        qb.andWhere('customer.groupId is null');
      }
      if (!groupIds?.includes(-1)) {
        qb.andWhere('customer.groupId in (:...groupIds)', { groupIds });
      }
      if (groupIds?.includes(-1) && groupIds?.length > 1) {
        const filterGroupIds = groupIds.filter((r) => r >= 0);
        qb.andWhere(
          new Brackets((qb1) => {
            qb1.orWhere('customer.groupId is null');
            qb1.orWhere('customer.groupId in (:...filterGroupIds)', { filterGroupIds });
          }),
        );
      }
    }
  }
}
