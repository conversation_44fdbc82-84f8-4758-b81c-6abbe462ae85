import { Injectable } from '@nestjs/common';
import * as moment from 'moment';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { RedisService } from '@kezhaozhao/nestjs-redis';

@Injectable()
export class DiligenceHistoryCacheHelper {
  private lastUpdateCachePrefix = 'external_con_last_update:';

  constructor(private readonly redisService: RedisService) {}

  /**
   * 存储最近一次更改了外部条件(影响尽调结果的条件)的时间
   *
   * @param orgId
   */
  async makeHistoryForUpdate(orgId: number) {
    const toady = new Date();
    toady.setHours(23, 59, 59, 999);
    await this.redisService.getClient().setex(`${this.lastUpdateCachePrefix}${orgId}`, Math.floor((toady.getTime() - Date.now()) / 1000), Date.now());
  }

  /**
   * 是否可以使用指定的 尽调记录作为缓存
   * @param dbDiligence
   * @param settingId 排查使用的setting
   */
  async shouldNotUseCache(dbDiligence: DiligenceHistoryEntity, settingId: number) {
    const cacheDays = 1; // TODO 这个后续可以加载用户的配置
    // return dbDiligence.shouldUpdate > 0 || dbDiligence.createDate.getTime() > (await this.getCacheExpiredDate(dbDiligence.orgId));
    const cachedLastTime = await this.redisService.getClient().get(`${this.lastUpdateCachePrefix}${dbDiligence.orgId}`);
    const lastUpdateTime: number = cachedLastTime ? parseInt(cachedLastTime) : -1;
    /** 1.shouldUpdate>0,需要更新，不使用缓存
     *  2.尽调创建时间早于redis最后更新时间，尽调记录可能已经过时，不使用缓存
     *  3.缓存时间过期，不使用缓存
     *  4. orgSetting设置发生了变化，不使用缓存*/
    return (
      dbDiligence.shouldUpdate > 0 ||
      (lastUpdateTime > 0 && dbDiligence.createDate.getTime() < lastUpdateTime) ||
      moment().diff(moment(dbDiligence.createDate).startOf('day'), 'day') > cacheDays - 1 ||
      (settingId && dbDiligence.orgSettingsId && dbDiligence.orgSettingsId != settingId)
    );
  }
}
