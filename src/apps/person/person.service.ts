import { BadRequestException, Injectable } from '@nestjs/common';
import { CreatePersonModel, Relative } from 'libs/model/person/CreatePersonModel';
import { UpdatePersonModel } from 'libs/model/person/UpdatePersonModel';
import { AffectedResponse, PaginationQueryParams, RoverUser } from 'libs/model/common';
import { PersonEntity } from 'libs/entities/PersonEntity';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { Brackets, EntityManager, In, MoreThan, Not, Repository } from 'typeorm';
import { GroupsEntity } from 'libs/entities/GroupsEntity';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BadParamsException, CommonExceptions } from '@kezhaozhao/qcc-utils';
import { compact, groupBy, isEqual, omit, pick, trim, uniq, uniqWith, xor } from 'lodash';
import { PersonGroupChangeRequest } from 'libs/model/person/PersonGroupChangeRequest';
import { SearchPersonModel } from 'libs/model/person/SearchPersonModel';
import { SearchPersonResponse } from 'libs/model/person/SearchPersonResponse';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { BatchStatusEnums } from 'libs/enums/batch/BatchStatusEnums';
import { MarkPersonModel } from 'libs/model/person/MarkPersonModel';
import { PersonOrgCompanyEntity } from 'libs/entities/PersonOrgCompanyEntity';
import { PersonStatusEnums } from 'libs/enums/person/PersonStatusEnums';
import { RoverBundleCounterType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { EntityDuplicatedException } from '../../libs/exceptions/EntityDuplicatedException';
import { PersonVerifyModel } from '../../libs/model/person/PersonVerifyModel';
import { PersonAssociationEnums } from '../../libs/enums/person/PersonAssociationEnums';
import { CompanyModel } from '../../libs/model/person/CompanyModel';
import { ConfigService } from '../../libs/config/config.service';
import { CompanyResponse } from '../../libs/model/person/CompanyResponse';
import * as Bluebird from 'bluebird';
import { EnterpriseLibService } from '../data/source/enterprise.lib.service';
import { PersonCompanyCountModel } from '../../libs/model/person/PersonCompanyCountModel';
import { CompanySearchEsService } from '../company/company-search-es.service';
import { EsCompanySearchRequest } from '../company/model/EsCompanySearchRequest';
import { DiligenceHistoryEntity } from '../../libs/entities/DiligenceHistoryEntity';
import * as moment from 'moment/moment';
import { OpLogService } from '../oplog/oplog.service';
import { OperatorTypeEnums } from '../../libs/enums/oplog/OperatorTypeEnums';
import { MatchPersonModel } from '../../libs/model/person/MatchPersonModel';
import { SecurityService } from '../../libs/config/security.service';
import { PermissionByEnum } from '../../libs/enums/PermissionScopeEnum';
import { QueryBuilderHelper } from '../../libs/common/sql.helper';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';
import { DiligenceHistoryCacheHelper } from '../basic/diligence.history.cache.helper';
import { GroupType } from '../../libs/model/element/CreateGroupModel';
import { PersonAggsRequest } from '../../libs/model/person/PersonAggsRequest';
import { UserEntity } from '../../libs/entities/UserEntity';
import { PersonHelper } from '../data/helper/person.helper';
import { PotentialDiligenceEntity } from 'libs/entities/PotentialDiligenceEntity';

@Injectable()
export class PersonService {
  private readonly logger: Logger = QccLogger.getLogger(PersonService.name);

  constructor(
    @InjectRepository(PersonEntity) private readonly personRepo: Repository<PersonEntity>,
    @InjectRepository(GroupsEntity) private readonly groupRepo: Repository<GroupsEntity>,
    @InjectRepository(PersonOrgCompanyEntity) private readonly personOrgCompanyRepo: Repository<PersonOrgCompanyEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(PotentialDiligenceEntity) private readonly potentialDiligenceRepo: Repository<PotentialDiligenceEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly bundleService: RoverBundleService,
    private readonly diligenceHistoryHelper: DiligenceHistoryCacheHelper,
    private readonly configService: ConfigService,
    private readonly enterpriseLibService: EnterpriseLibService,
    private readonly esService: CompanySearchEsService,
    private readonly oplogService: OpLogService,
    private readonly securityService: SecurityService,
    private readonly personHelper: PersonHelper,
  ) {
    // this.bundleService.setSyncFunctions({
    //   [RoverBundleCounterType.PersonQuantity]: { fn: this.syncPersonCount.bind(this) },
    // });
    // this.companySearchClient = new CompanyClient({
    //   server: this.configService.kzzServer.companySearchApi,
    //   requestFrom: process.env.PROJECT_NAME || 'qcc-rover-service',
    // });
  }

  // private async syncPersonCount(orgId: number) {
  //   const bundle = await this.bundleService.getOrgBundle(orgId);
  //   return this.personRepo.count({
  //     orgId,
  //     createDate: Between(bundle.activeDate, bundle.expireDate),
  //     relationPersonId: -1,
  //   });
  // }

  /**
   * 分组人员列表
   * @param currentUser
   */
  // async getPersonList(orgId: number): Promise<PersonEntity[]> {
  //   const qb = await this.createQb(orgId);
  //   // 过滤员工亲属
  //   qb.andWhere('person.relationPersonId = -1');
  //   return qb.getMany();
  // }

  async createQb(orgId: number) {
    return this.personRepo
      .createQueryBuilder('person')
      .leftJoinAndSelect('person.group', 'group')
      .select(['person', 'group.name'])
      .where('person.orgId = :orgId', { orgId })
      .andWhere('person.status = :status', { status: BatchStatusEnums.Done });
  }

  async checkCountLimit(currentUser: RoverUser, num: number) {
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.PersonQuantity);
    // await bundleCounter.clear();
    await bundleCounter.check(num);
    // const totalCount = await this.personRepo.count({ orgId: currentUser.currentOrg });
    // const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    // if (totalCount + num > userBundle[RoverBundleCounterType.PersonQuantity].value) {
    //   throw new BadRequestException(RoverExceptions.Bundle.PersonReachLimit);
    // }
  }

  /**
   * 创建新的人员记录
   * @param currentUser - 当前用户信息
   * @param postData - 创建人员的数据模型
   * @param isBatch - 是否为批量创建，默认为false
   * @returns 返回创建的人员实体
   * @throws EntityDuplicatedException - 当人员编号重复时抛出
   * @throws BadRequestException - 当手机号重复或人员已存在时抛出
   */
  async create(currentUser: RoverUser, postData: CreatePersonModel, isBatch = false) {
    const { userId, currentOrg: orgId } = currentUser;
    const relatives = postData.relatives;
    // 人员编号不能重复
    const dbPerson = await this.personRepo.findOne({ orgId, personNo: trim(postData.personNo), active: 1 });
    if (dbPerson) {
      throw new EntityDuplicatedException(RoverExceptions.GroupRelated.Person.DuplicatedPersonNoError, [dbPerson.id]);
    }

    await this.checkCardId(currentUser, postData);

    if (postData.phone) {
      const count = await this.personRepo.count({
        orgId: currentUser.currentOrg,
        phone: postData.phone,
        active: 1,
      });
      if (count && count > 0) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Person.DuplicatedPhoneError);
      }
    }
    if (postData.keyNo) {
      const associationRes = await this.isAssociation(currentUser, postData.keyNo);
      if (associationRes) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Person.PersonDuplicateError);
      }
    }
    // 计算需要增加的额度
    const count = 1 + (relatives?.length || 0);

    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.PersonQuantity);
    await bundleCounter.increase(count);

    if (postData?.groupId && postData?.groupId > 0) {
      //1.判断分组是否存在
      const resultGroup = await this.groupRepo.findOne({ groupId: postData.groupId });
      if (!resultGroup) {
        throw new BadParamsException(RoverExceptions.GroupRelated.Group.NotFound);
      }
    }

    const user = Object.assign(new PersonEntity(), omit(postData, ['relatives']), {
      createBy: userId,
      orgId: orgId,
      depId: currentUser?.departments?.[0],
      ownerId: userId,
    });
    let newPerson: PersonEntity;
    try {
      newPerson = await this.personRepo.save(user);
      if (postData.companyId) {
        //添加一条核实记录,确认本人
        await this.personOrgCompanyRepo.save(
          Object.assign(new PersonOrgCompanyEntity(), {
            personId: newPerson.id,
            keyNo: postData.keyNo,
            status: 1,
            companyId: newPerson.companyId,
            companyName: newPerson.companyName,
            createBy: userId,
            orgId: orgId,
          }),
        );
      }
      await Bluebird.all([
        // 人员亲属信息处理
        this.modifyRelatives(currentUser, relatives, newPerson),
        this.diligenceHistoryHelper.makeHistoryForUpdate(orgId),
        //操作记录
        this.oplogService.add(isBatch ? OperatorTypeEnums.BatchCreate : OperatorTypeEnums.Create, PersonEntity, currentUser, [newPerson.id], [newPerson]),
      ]);
    } catch (error) {
      await bundleCounter.decrease(count);
      throw error;
    }
    return newPerson;
  }

  async count(currentUser: RoverUser) {
    return this.personRepo.count({
      orgId: currentUser.currentOrg,
      status: BatchStatusEnums.Done,
      active: 1,
      relationPersonId: -1,
    });
    /*const { by, userIds } = this.securityService.checkScope(currentUser, 2061);
    if (by == PermissionByEnum.ORG) {
      return this.personRepo.count({ orgId: currentUser.currentOrg, status: BatchStatusEnums.Done, relationPersonId: -1 });
    } else {
      return this.personRepo.count({ orgId: currentUser.currentOrg, status: BatchStatusEnums.Done, relationPersonId: -1, createBy: In(userIds) });
    }*/
  }

  /**
   * 搜索人员列表
   * @param currentUser - 当前用户信息
   * @param postData - 搜索条件模型
   * @param notUsePage - 是否不使用分页，默认为false
   * @returns 返回SearchPersonResponse包含分页信息和人员列表数据
   */
  async searchPersonList(currentUser: RoverUser, postData: SearchPersonModel, notUsePage = false): Promise<SearchPersonResponse> {
    const { pageSize, pageIndex, sortField, isSortAsc, selectAll } = postData;
    const qb: SelectQueryBuilder<PersonEntity> = this.getBaseQueryBuilder(currentUser, postData);
    const personIds: number[] = [];
    if (selectAll) {
      const allRaw = await qb.andWhere('person.relationPersonId = -1').getMany();
      Array.prototype.push.apply(personIds, uniq(allRaw.map((a) => a.id)));
    }
    if (!notUsePage) {
      qb.andWhere('person.relationPersonId = -1');
      qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    }
    if (sortField) {
      qb.orderBy(`person.${sortField}`, isSortAsc ? 'ASC' : 'DESC');
    } else {
      qb.orderBy('person.createDate', 'DESC');
    }
    const [data, total] = await qb.getManyAndCount();

    data?.forEach((e) => {
      // 身份证脱敏
      if (e.cardId) {
        e.cardId = e.cardId.replace(/^(.{6})(.*)(.{4})$/, '$1********$3');
      }
    });
    if (!notUsePage) {
      // 使用分页，需要补充人员的公司数量信息
      // 补充人员亲属信息
      let relatives = [];
      let personMap = {};
      if (data.length > 0) {
        relatives = await this.personRepo.find({
          relationPersonId: In(data.map((e) => e.id)),
          active: 1,
        });
        personMap = groupBy(relatives, (e) => e.relationPersonId);
      }
      const allPerson = [...data, ...relatives];
      const companyCountList = await this.getPersonCompanyCountBatch(allPerson);
      const companyCountMap = groupBy(companyCountList, (e) => e.personId);

      data.forEach((e) => {
        const relativeList = personMap[e.id];
        if (relativeList) {
          relativeList.forEach((r) => {
            r['companyCountInfo'] = companyCountMap[r.id][0];
            // 身份证脱敏
            if (r.cardId) {
              r.cardId = r.cardId.replace(/^(.{6})(.*)(.{4})$/, '$1********$3');
            }
          });
        }
        e['relatives'] = relativeList || [];
        e['companyCountInfo'] = companyCountMap[e.id][0];
      });
      return {
        pageSize,
        pageIndex,
        data,
        total,
        personIds,
      };
    }
    return {
      pageSize,
      pageIndex,
      data,
      total,
    };
  }

  private getBaseQueryBuilder(currentUser: RoverUser, postData: SearchPersonModel) {
    const { currentOrg: orgId } = currentUser;
    const { searchKey, groupIds, ownerIds, ids, operators, personNos, depIds } = postData;
    const { by, userIds } = this.securityService.checkScope(currentUser, 2061);
    const qb = this.personRepo
      .createQueryBuilder('person')
      .leftJoinAndSelect('person.owner', 'owner')
      .leftJoinAndSelect('person.creator', 'creator')
      .select(['person', 'owner.name', 'creator.name'])
      .where('person.orgId = :orgId', { orgId })
      .andWhere('person.status = :status', { status: BatchStatusEnums.Done })
      .andWhere('person.active = :active', { active: 1 });
    if (by == PermissionByEnum.USER) {
      qb.andWhere('person.createBy in (:...userIds)', { userIds });
    }
    if (searchKey) {
      qb.andWhere('concat(IFNULL(person.name,""),IFNULL(person.personNo,""),IFNULL(person.cardId,"")) like :searchKey', { searchKey: `%${searchKey}%` });
    }

    if (groupIds?.length) {
      qb.andWhere('person.groupId in (:...groupIds)', { groupIds });
      // if (groupIds?.includes(-1) && groupIds?.length === 1) {
      //   qb.andWhere('person.groupId is null');
      // }
      // if (!groupIds?.includes(-1)) {
      //   qb.andWhere('person.groupId in (:...groupIds)', { groupIds });
      // }
      // if (groupIds?.includes(-1) && groupIds?.length > 1) {
      //   qb.andWhere(
      //     new Brackets((qb1) => {
      //       qb1.orWhere('person.groupId is null');
      //       qb1.orWhere('person.groupId in (:...groupIds)', { groupIds });
      //     }),
      //   );
      // }
    }

    if (ownerIds?.length) {
      qb.andWhere('person.ownerId in (:...ownerIds)', { ownerIds });
    }
    if (operators?.length) {
      qb.andWhere('person.createBy in (:...operators)', { operators });
    }

    if (depIds?.length) {
      qb.andWhere('person.depId in (:...depIds)', { depIds });
    }

    if (personNos?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          qb1.orWhere('person.personNo in (:...personNos)', { personNos });
          qb1.orWhere('person.name in (:...personNos)', { personNos });
          qb1.orWhere('person.phone in (:...personNos)', { personNos });
        }),
      );
    }
    if (ids?.length) {
      //导出需要导出亲属
      qb.andWhere(
        new Brackets((qb1) => {
          qb1.orWhere('person.id in (:...ids)', { ids });
          qb1.orWhere('person.relationPersonId in (:...ids)', { ids });
        }),
      );
    }
    // 创建时间
    QueryBuilderHelper.applyDateRangeQuery(qb, postData?.createDate, 'createDate');
    //更新时间
    QueryBuilderHelper.applyDateRangeQuery(qb, postData?.updateDate, 'createDate');
    // 行政地区
    if (postData?.region?.length) {
      const params = {};
      const regionSql = [];
      for (let i = 0; i < postData.region?.length; i++) {
        const region = postData.region[i];
        if (region.dt) {
          const key = 'dt' + i;
          params[key] = region.dt;
          regionSql.push(`person.district = :${key}`);
        } else if (region.ct) {
          const key = 'ct' + i;
          params[key] = region.ct;
          regionSql.push(`person.city = :${key}`);
        } else if (region.pr) {
          const key = 'pr' + i;
          params[key] = region.pr;
          regionSql.push(`person.province = :${key}`);
        }
      }
      qb.andWhere(`(${regionSql.join(' OR ')})`, params);
    }
    return qb;
  }

  async findOne(currentUser: RoverUser, id: number) {
    const { currentOrg: orgId } = currentUser;
    const person = await this.personRepo
      .createQueryBuilder('person')
      .leftJoinAndSelect('person.owner', 'owner')
      .leftJoinAndSelect('person.creator', 'creator')
      .select(['person', 'owner.name', 'creator.name'])
      .where('person.orgId = :orgId', { orgId })
      .andWhere('person.active = :active', { active: 1 })
      .andWhere('person.id = :id', { id })
      .andWhere('person.status = :status', { status: BatchStatusEnums.Done })
      .getOne();
    const relatives = await this.personRepo.find({
      relationPersonId: person.id,
      status: BatchStatusEnums.Done,
      active: 1,
    });
    if (person.cardId) {
      person.cardId = person.cardId.replace(/^(.{6})(.*)(.{4})$/, '$1********$3');
    }
    relatives.forEach((r) => {
      if (r.cardId) {
        r.cardId = r.cardId.replace(/^(.{6})(.*)(.{4})$/, '$1********$3');
      }
    });
    const companyCountList = await this.getPersonCompanyCountBatch([person]);
    const companyCountInfo = companyCountList[0];
    return Object.assign(person, { relatives, companyCountInfo });
  }

  /**
   * 更新人员信息
   * @param currentUser - 当前用户信息
   * @param id - 要更新的人员ID
   * @param postData - 更新的数据模型
   * @returns 返回更新结果
   * @throws BadParamsException - 当人员不存在时抛出
   * @throws BadRequestException - 当分组不存在、编号或手机号重复时抛出
   */
  async update(currentUser: RoverUser, id: number, postData: UpdatePersonModel) {
    let oldData = await this.personRepo.findOne({ id, active: 1 });
    if (!oldData) {
      throw new BadParamsException({ message: '该人员不存在!', ...CommonExceptions.Common.Request.NotFound });
    }
    if (postData.cardId) {
      if (oldData.cardId) {
        // 已存在身份证信息，不可更改
        postData.cardId = oldData.cardId;
      }
    }
    if (postData.relatives?.length > 0) {
      const oldRelativesIds = postData.relatives.map((x) => x.id).filter((x) => x);
      if (oldRelativesIds.length > 0) {
        const oldRelatives = await this.personRepo.findByIds(oldRelativesIds);
        postData.relatives.forEach((relative) => {
          if (relative.id) {
            const oldRelative = oldRelatives.find((x) => x.id === relative.id);
            if (oldRelative?.cardId) {
              // 已存在身份证信息，不可更改
              relative.cardId = oldRelative.cardId;
            }
          }
        });
      }
    }
    // 检查身份证相关校验
    await this.checkCardId(currentUser, postData, id);
    if (postData.groupId && postData.groupId != -1) {
      //判断分组是否存在
      const resultGroup = await this.groupRepo.findOne({ groupId: postData.groupId });
      if (!resultGroup) {
        throw new BadRequestException({ message: '该分组不存在!', ...CommonExceptions.Common.Request.NotFound });
      }
    }

    if (postData.personNo) {
      const count = await this.personRepo.count({
        orgId: currentUser.currentOrg,
        personNo: postData.personNo,
        id: Not(id),
        active: 1,
      });
      if (count && count > 0) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Person.DuplicatedPersonNoError);
      }
    }
    if (postData.keyNo) {
      const associationRes = await this.isAssociation(currentUser, postData.keyNo, id);
      if (associationRes) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Person.PersonDuplicateError);
      }
    }
    if (!postData.keyNo && oldData.keyNo) {
      //如果更新的公司为空，则解除绑定
      oldData = (await this.disassociate(currentUser, id)).currentPerson;
    }
    if (postData.phone) {
      const count = await this.personRepo.count({
        orgId: currentUser.currentOrg,
        phone: postData.phone,
        id: Not(id),
        active: 1,
      });
      if (count && count > 0) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Person.DuplicatedPhoneError);
      }
    }
    try {
      return await this.entityManager.transaction(async (manager) => {
        const updateResult = await manager.update(
          PersonEntity,
          { id },
          pick(postData, [
            'name',
            'personNo',
            'groupId',
            'province',
            'city',
            'district',
            'birthDay',
            'phone',
            'email',
            'keyNo',
            'companyId',
            'companyName',
            'cardId',
            'relationship',
          ]),
        );
        const currentPerson = await manager.findOne(PersonEntity, id);
        //旧数据的keyNo不为空，新数据keyNo不为空,添加人员确认记录
        if (postData.keyNo?.length > 0) {
          const assignPerson = Object.assign(new PersonOrgCompanyEntity(), {
            personId: id,
            keyNo: postData.keyNo,
            companyId: postData.companyId,
            companyName: postData.companyName,
            status: 1,
            createBy: currentUser.userId,
            orgId: currentUser.currentOrg,
          });
          if (oldData.keyNo?.length > 0) {
            if (oldData.keyNo !== postData.keyNo) {
              //已关联过公司核实的不同人员,解除所有的绑定信息，再添加新核实记录
              await this.personOrgCompanyRepo.update(
                { personId: id },
                Object.assign(new PersonOrgCompanyEntity(), { association: PersonAssociationEnums.ASSOCIATION }),
              );
              await this.personOrgCompanyRepo.save(assignPerson);
              await this.updateDiligenceHistory(currentUser.currentOrg);
            }
          } else {
            //添加一条核实记录,确认本人,解除所有绑定旧数据
            await this.personOrgCompanyRepo.update(
              {
                personId: id,
                keyNo: postData.keyNo,
              },
              { association: PersonAssociationEnums.ASSOCIATION },
            );
            await this.personOrgCompanyRepo.save(assignPerson);
            await this.updateDiligenceHistory(currentUser.currentOrg);
          }
        }
        //已关联过公司核实的不同人员,解除所有的绑定信息，再添加新核实记录
        else if (oldData.keyNo) {
          await this.personOrgCompanyRepo.update(
            { personId: id },
            Object.assign(new PersonOrgCompanyEntity(), { association: PersonAssociationEnums.ASSOCIATION }),
          );
        }

        const oldRelatives = await this.personRepo.find({ relationPersonId: id, status: BatchStatusEnums.Done });
        if (oldRelatives?.length > 0) {
          //和postData中的relatives比较差异
          const oldIds = oldRelatives.map((o) => o.id);
          const newIds =
            postData?.relatives?.map((r) => {
              if (r?.id) {
                return r.id;
              }
            }) || [];
          const minus = xor(oldIds, newIds);
          if (minus.length > 0) {
            const childPrePersons = await this.personRepo.findByIds(minus);

            const hardDeleteChildIds = childPrePersons.filter((c) => !c.cardId)?.map((c) => c.id);
            if (hardDeleteChildIds?.length > 0) {
              await this.personRepo.delete(hardDeleteChildIds);
              const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.PersonQuantity);
              await bundleCounter.decrease(hardDeleteChildIds.length); // 额度返回
            }
            const softDeleteChildIds = childPrePersons.filter((c) => c.cardId)?.map((c) => c.id);
            if (softDeleteChildIds?.length > 0) {
              await this.personRepo.update(
                {
                  id: In(softDeleteChildIds),
                },
                {
                  active: 2,
                },
              );
            }
            await this.oplogService.add(OperatorTypeEnums.Remove, PersonEntity, currentUser, minus, childPrePersons);
          }
        }
        await Bluebird.all([
          this.modifyRelatives(
            currentUser,
            postData.relatives,
            Object.assign(new PersonEntity(), {
              id,
              personNo: postData.personNo,
            }),
          ),
          this.diligenceHistoryHelper.makeHistoryForUpdate(currentUser.currentOrg),
          this.oplogService.add(OperatorTypeEnums.Edit, PersonEntity, currentUser, [id], [oldData], [currentPerson]),
        ]);
        return updateResult;
      });
    } catch (e) {
      this.logger.error(e);
      //entityManager.transaction 已经处理了回滚操作。如果在事务中发生异常，TypeORM 会自动回滚事务
      throw e;
    }
  }

  async checkCardId(currentUser: RoverUser, postData: CreatePersonModel, id?: number) {
    const relatives = postData.relatives;
    if (postData.cardId?.trim() == '') {
      postData.cardId = null;
    }
    if (postData.companyId?.trim() == '') {
      postData.companyId = null;
    }
    if (postData.keyNo?.trim() == '') {
      postData.keyNo = null;
    }
    if (!postData.groupId) {
      postData.groupId = -1;
    }
    if (relatives?.length > 0) {
      for (const relative of relatives) {
        if (relative.cardId?.trim() == '') {
          relative.cardId = null;
        }
        if (relative.companyId?.trim() == '') {
          relative.companyId = null;
        }
        if (relative.keyNo?.trim() == '') {
          relative.keyNo = null;
        }
        relative['groupId'] = postData.groupId;
      }
    }

    // 身份证号码格式校验正则
    const idCardRegex = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;
    // 收集所有需要校验的身份证号码
    const cardIds = [...(postData.cardId?.trim() ? [postData.cardId?.trim()] : []), ...(relatives?.map((r) => r.cardId?.trim()) || [])].filter((x) => x);
    if (cardIds.length === 0) {
      return;
    }
    const cardIdList = uniq(cardIds);
    if (cardIdList.length < cardIds.length) {
      throw new BadParamsException(RoverExceptions.GroupRelated.Person.CardIdDuplicateError);
    }

    // 校验身份证号码格式
    for (const cardId of cardIds) {
      if (!idCardRegex.test(cardId)) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Person.CardIdError);
      }
    }
    // 检查身份证号码是否重复
    if (cardIds.length > 0) {
      let count = 0;
      if (id) {
        const oldCardIds = [];
        const oldRelatives = await this.personRepo.find({ relationPersonId: id, status: BatchStatusEnums.Done });
        oldCardIds.push(id);
        oldCardIds.push(...(oldRelatives?.map((o) => o.id) || []));
        count = await this.personRepo.count({
          orgId: currentUser.currentOrg,
          cardId: In(cardIds),
          id: Not(In(oldCardIds)),
          active: 1,
        });
      } else {
        count = await this.personRepo.count({
          orgId: currentUser.currentOrg,
          cardId: In(cardIds),
          active: 1,
        });
      }
      if (count && count > 0) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Person.DuplicatedCardIdError);
      }
    }
  }

  /**
   * 批量修改人员分组
   * @param currentUser - 当前用户信息
   * @param postData - 包含分组ID和人员ID列表的请求数据
   * @returns 返回AffectedResponse表示影响的记录数
   * @throws BadRequestException - 当分组不存在时抛出
   */
  async changeBatchGroup(currentUser: RoverUser, postData: PersonGroupChangeRequest): Promise<AffectedResponse> {
    const affectedResponse: AffectedResponse = new AffectedResponse();
    const { groupId, personIds } = postData;
    affectedResponse.affected = 0;
    const updateData = {
      groupId,
      orgId: currentUser?.currentOrg,
      depId: currentUser?.departments?.[0],
    };
    const prePersons = await this.personRepo.findByIds(personIds);
    //如果groupId为-1，则表示移动分组到默认分组
    if (groupId === -1) {
      updateData.groupId = null;
    } else {
      //判断分组是否存在
      const resultGroup = await this.groupRepo.findOne({ orgId: currentUser?.currentOrg, groupId });
      if (!resultGroup) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Group.NotFound);
      }
    }
    //批量更新人员分组信息
    const updateUserResult = await this.personRepo
      .createQueryBuilder()
      .update(PersonEntity)
      .set(updateData)
      .where('id in (:ids)', { ids: personIds })
      .execute();
    const currentPersons = await this.personRepo.findByIds(personIds);
    await Bluebird.all([
      this.diligenceHistoryHelper.makeHistoryForUpdate(currentUser.currentOrg),
      //操作记录
      this.oplogService.add(OperatorTypeEnums.Edit, PersonEntity, currentUser, personIds, prePersons, currentPersons),
    ]);
    affectedResponse.affected = updateUserResult?.affected;
    return affectedResponse;
  }

  async remove(currentUser: RoverUser, postData: SearchPersonModel) {
    const { currentOrg: orgId } = currentUser;
    let operatorType = OperatorTypeEnums.Remove;
    const prePersons = await this.getBaseQueryBuilder(currentUser, postData).andWhere('person.relationPersonId = -1').getMany();
    if (!prePersons?.length) {
      return {};
    }
    const deleteIds = prePersons?.map((m) => m.id);
    const hardDeleteIds = prePersons?.filter((m) => !m.cardId).map((m) => m.id);
    const softDeleteIds = prePersons?.filter((m) => m.cardId).map((m) => m.id);
    if (deleteIds.length > 1) {
      operatorType = OperatorTypeEnums.BatchRemove;
    }
    const [deleteResult] = await Bluebird.all([
      this.personRepo.delete({ orgId, id: In(hardDeleteIds) }),
      this.personRepo.delete({ orgId, relationPersonId: In(hardDeleteIds) }),
      this.personRepo.update({ orgId, id: In(softDeleteIds) }, { active: 2 }),
      this.personRepo.update({ orgId, relationPersonId: In(softDeleteIds) }, { active: 2 }),
      this.personOrgCompanyRepo.delete({ orgId, personId: In(deleteIds) }),
    ]);
    // 恢复套餐额度(只恢复不含有身份证 ID 的人员额度)
    const personEntities = prePersons?.filter((m) => !m.cardId);
    const depIdsGroup = groupBy(personEntities, (g) => g?.depId);
    await Bluebird.map(Object.keys(depIdsGroup), async (depId) => {
      const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.PersonQuantity, depId);
      // await bundleCounter.clear();
      await bundleCounter.decrease(depIdsGroup[depId].length);
    });
    await Bluebird.all([
      this.diligenceHistoryHelper.makeHistoryForUpdate(orgId),
      this.oplogService.add(operatorType, PersonEntity, currentUser, deleteIds, prePersons),
    ]);
    return deleteResult;
  }

  /**
   * 检查人员是否已经完成核实
   * @param currentUser - 当前用户信息
   * @param postData - 核实检查请求数据
   * @returns 返回核实记录，如果未核实则返回空对象
   */
  async isVerify(currentUser: RoverUser, postData: PersonVerifyModel): Promise<any> {
    const { currentOrg: orgId } = currentUser;
    const { personId } = postData;
    let keyNo = postData.keyNo;
    const dbPerson = await this.personRepo.findOne({ id: personId });
    if (dbPerson.keyNo?.length) {
      keyNo = dbPerson.keyNo;
    } else {
      const [lastRecord] = await this.personOrgCompanyRepo
        .createQueryBuilder('p')
        .where({
          personId,
          orgId,
          association: PersonAssociationEnums.UN_ASSOCIATION,
        })
        .orderBy({ 'p.id': 'DESC' })
        .getMany();
      //该人员为核实过
      if (lastRecord) {
        //已核实过，存在核实记录，
        if (postData.keyNo !== lastRecord.keyNo) {
          return {};
        }
        return lastRecord;
      }
    }
    const dbRecord = await this.personOrgCompanyRepo.findOne({
      keyNo,
      personId,
      orgId,
      association: PersonAssociationEnums.UN_ASSOCIATION,
    });
    if (dbRecord) {
      //已核实过，存在核实记录，
      if (postData.keyNo !== dbPerson.keyNo) {
        dbRecord.status = 0;
      }
      return dbRecord;
    }
    return {};
  }

  async verify(currentUser: RoverUser, postData: MarkPersonModel) {
    const { userId, currentOrg: orgId } = currentUser;
    const { personId, keyNo, status, companyName, companyId } = postData;
    delete postData.key;
    delete postData.snapshotId;
    //添加标记记录
    const saveRecord = Object.assign(new PersonOrgCompanyEntity(), postData, {
      createBy: userId,
      orgId: orgId,
    });
    //查询该人员有效地绑定记录
    const dbRecord = await this.isVerify(currentUser, Object.assign(new PersonVerifyModel(), { personId, keyNo }));
    if (dbRecord?.id) {
      //已核实过，存在核实记录，
      return dbRecord;
    }
    const result = await this.personOrgCompanyRepo.save(saveRecord);
    //更新person记录
    if (status > 0) {
      await this.personRepo.update(
        personId,
        Object.assign(new PersonEntity(), {
          companyId,
          companyName,
          keyNo,
        }),
      );
    }
    await this.updateDiligenceHistory(currentUser.currentOrg);
    return result;
  }

  /**
   * 核实异常，删除更新数据
   * @param personId 人员 id
   * @param status 核实状态
   * @param personOrgCompanyRecordId 核实记录 id
   */
  async exceptionDelete(personId: number, status: number, personOrgCompanyRecordId: number) {
    if (status > 0) {
      await this.personRepo.update(
        personId,
        Object.assign(new PersonEntity(), {
          companyId: null,
          companyName: null,
          keyNo: null,
        }),
      );
    }
    return await this.personOrgCompanyRepo.delete(personOrgCompanyRecordId);
  }

  public async updateFromExcel(currentUser: RoverUser, personModel: CreatePersonModel) {
    const { currentOrg: orgId } = currentUser;
    const dbPerson = await this.personRepo.findOne({ orgId: orgId, personNo: personModel.personNo });
    const { by, userIds } = this.securityService.checkScope(currentUser, 2062);
    if (dbPerson && (by == PermissionByEnum.ORG || userIds?.includes(dbPerson.createBy))) {
      //更新用户信息
      const upPerson = Object.assign(new CreatePersonModel(), dbPerson, personModel);
      await this.update(currentUser, dbPerson.id, upPerson);
      return upPerson;
    }
  }

  private async getPersonCompanyCountBatch(personList: PersonEntity[]) {
    const promiseArray = [];
    personList.forEach((person) => {
      promiseArray.push(this.personCompanyCount(person));
    });
    const promiseResList: PersonCompanyCountModel[] = await Bluebird.all(promiseArray);
    return promiseResList;
  }

  private async personCompanyCount(person: PersonEntity): Promise<PersonCompanyCountModel> {
    const [res1, res2, res3, res4, res5] = await Bluebird.all([
      this.personHelper.getBossDJGData(person.keyNo, person.name, 1, 1),
      this.personHelper.getHoldingCompany(person.keyNo, 1, 1),
      this.searchCompanyListByPhone(person.id, {
        pageSize: 1,
        pageIndex: 1,
      }),
      this.personOrgCompanyRepo.count({ personId: person.id }),
      this.personRepo.count({ relationPersonId: person.id, status: BatchStatusEnums.Done, active: 1 }),
    ]);
    return Object.assign(new PersonCompanyCountModel(), {
      personId: person.id,
      verifyCount: res4,
      djgCount: res1.total,
      holdingCount: res2.total,
      phoneCount: res3.total,
      relativesCount: res5 || 0,
    });
  }

  async searchCompanyListByKeyNo(currentUser: RoverUser, id: number, postData: PaginationQueryParams) {
    const { pageSize, pageIndex } = postData;
    const person = await this.personRepo.findOne(id);
    const companyResponse = new CompanyResponse();
    if (!person) {
      return companyResponse;
    }
    //获取人员关联企业列表
    return this.personHelper.getBossDJGData(person.keyNo, person.name, pageSize, pageIndex);
    //获取boss控制企业
    // const res2 = await this.getHoldingCompany(person.keyNo, pageSize, pageIndex);
    // const totalHit = flatten([res1?.data, res2?.data]);
    // return this.returnBaseResponse(totalHit, pageSize, pageIndex);
  }

  private async modifyRelatives(currentUser: RoverUser, relatives: Relative[], newPerson: PersonEntity) {
    if (!relatives?.length) {
      return;
    }
    const { userId, currentOrg: orgId } = currentUser;
    // 人员亲属信息处理
    await Bluebird.map(
      relatives,
      async (r, index) => {
        const personNo = `${newPerson.personNo}_${r.relationship}_${r.name}`;
        const savePerson = Object.assign(new UpdatePersonModel(), r, {
          createBy: userId,
          orgId: orgId,
          ownerId: userId,
          personNo,
          relationPersonId: newPerson.id,
          relationship: r.relationship,
          status: BatchStatusEnums.Done,
        });
        if (r?.id) {
          await this.update(currentUser, r.id, savePerson);
        } else {
          //如果没有id,利用personNo查询是否存在该人员记录
          const dbPerson = await this.personRepo.findOne({ orgId, personNo, relationPersonId: MoreThan(-1) });
          if (dbPerson) {
            savePerson.id = dbPerson.id;
            await this.update(currentUser, dbPerson.id, savePerson);
          } else {
            await this.create(currentUser, savePerson);
          }
        }
      },
      { concurrency: 2 },
    );
  }

  /**
   * 获取用户控制的企业
   * @param currentUser
   * @param id
   * @param postData
   */
  async searchCompanyListFromHolding(currentUser: RoverUser, id: number, postData: PaginationQueryParams) {
    const { pageSize, pageIndex } = postData;
    const person = await this.personRepo.findOne(id);
    const companyResponse = new CompanyResponse();
    if (!person) {
      return companyResponse;
    }
    return this.personHelper.getHoldingCompany(person.keyNo, pageSize, pageIndex);
  }

  async searchCompanyListByPhone(id: number, postData: PaginationQueryParams): Promise<CompanyResponse> {
    const { pageSize, pageIndex } = postData;
    //const companyModels: CompanyModel[] = [];
    const companyResponse = new CompanyResponse();
    const person = await this.personRepo.findOne(id);
    if (!person?.phone && !person?.email) {
      return companyResponse;
    }
    const phoneList = person?.phone ? person?.phone?.split(',') : undefined;
    const emailList = person?.email ? person?.email?.split(',') : undefined;
    const res = await this.esService.companySearch(
      Object.assign(new EsCompanySearchRequest(), {
        phonekzz: phoneList,
        emailkzz: emailList,
        pageSize,
        pageIndex,
        relation: 'or',
      }),
    );
    const companyModels = res?.Result?.map((c) => {
      return Object.assign(new CompanyModel(), {
        companyName: c?.name,
        companyId: c?.id,
        name: c?.opername,
        phone: c?.phonekzz?.join(','),
        email: c?.emailkzz?.join(','),
      });
    });
    return Object.assign(new CompanyResponse(), {
      pageSize,
      pageIndex,
      total: res.Paging.TotalRecords.value,
      data: companyModels,
    });
  }

  /**
   * 根据人员名称，公司keyNo,获取公司下与该人员匹配的人员列表
   * @param personName
   * @param keyNo
   */
  async matchCompanyPerson(personName: string, keyNo: string) {
    const { personJobList } = await this.enterpriseLibService.getCompanyExecutivesKeyNosV2(keyNo, 'person', true, false, personName);
    if (personJobList?.length) {
      const personList = uniqWith(
        personJobList
          .filter((e) => e.name === personName)
          .map((e) => {
            return { name: e.name, keyNo: e.keyNo, job: e.job };
          }),
        isEqual,
      );
      const keySet = new Set<string>();
      return compact(
        personList.map((e) => {
          if (!keySet.has(e.keyNo)) {
            keySet.add(e.keyNo);
            return e;
          }
        }),
      );
    }
    return [];
  }

  private async updateDiligenceHistory(orgId: number) {
    await this.diligenceHistoryRepo.update(
      {
        orgId,
        createDate: MoreThan(moment().startOf('day').toDate()),
      },
      { shouldUpdate: 1 },
    );
    await this.potentialDiligenceRepo.update(
      {
        orgId,
        createDate: MoreThan(moment().startOf('day').toDate()),
      },
      { shouldUpdate: 1 },
    );
  }

  async disassociate(currentUser: RoverUser, id: number) {
    const { userId, currentOrg: orgId } = currentUser;
    const oldData = await this.personRepo.findOne({ id, active: 1 });
    if (!oldData) {
      throw new BadParamsException({ message: '该人员不存在!', ...CommonExceptions.Common.Request.NotFound });
    }
    //解除该人员所有绑定
    await this.personOrgCompanyRepo.update(
      {
        personId: id,
        keyNo: oldData.keyNo,
      },
      { association: PersonAssociationEnums.ASSOCIATION },
    );
    //添加标记记录
    const saveRecord = Object.assign(new PersonOrgCompanyEntity(), {
      companyId: oldData.companyId,
      companyName: oldData.companyName,
      personId: oldData.id,
      keyNo: oldData.keyNo,
      createBy: userId,
      orgId: orgId,
      status: PersonStatusEnums.RECORD,
      association: PersonAssociationEnums.ASSOCIATION,
      verifyType: 4,
    });
    await this.personRepo.update(
      id,
      Object.assign(new PersonEntity(), {
        keyNo: null,
        companyId: null,
        companyName: null,
      }),
    );
    const currentPerson = await this.personRepo.findOne(id);
    const [result] = await Bluebird.all([
      this.personOrgCompanyRepo.save(saveRecord),
      this.diligenceHistoryHelper.makeHistoryForUpdate(currentUser.currentOrg),
      this.oplogService.add(OperatorTypeEnums.Edit, PersonEntity, currentUser, [id], [oldData], [currentPerson]),
    ]);
    return { result, currentPerson };
  }

  public async match(currentUser: RoverUser, req: MatchPersonModel) {
    const { currentOrg: orgId } = currentUser;
    const { personNos } = req;
    const qb = this.personRepo
      .createQueryBuilder('person')
      .select(['person'])
      .andWhere('person.orgId = :orgId', { orgId })
      .andWhere('person.active = :active', { active: 1 })
      .andWhere('person.status = :status', { status: BatchStatusEnums.Done })
      .andWhere('person.personNo in (:...personNos)', { personNos });
    const [data] = await qb.getManyAndCount();
    return data;
  }

  private async isAssociation(currentUser: RoverUser, keyNo: string, personId?: number) {
    const { currentOrg: orgId } = currentUser;
    const person = await this.personRepo.findOne({ keyNo, orgId, active: 1 });
    if (person && person.id !== personId) {
      return person;
    }
    return null;
  }

  public async aggsForSearch(currentUser: RoverUser, postData: PersonAggsRequest) {
    const qb = this.getBaseQueryBuilder(currentUser, postData?.query);
    if (!qb) {
      return [];
    }
    const aggsField = postData.aggsField;
    if (!aggsField) {
      return [];
    }
    qb.andWhere('person.relationPersonId = -1'); //仅聚合本人（列表只显示本人）
    switch (aggsField) {
      case 'group': {
        qb.select('person.groupId', 'fieldValue').addSelect('COUNT(distinct(person.id))', 'count').groupBy('person.groupId');
        break;
      }
      case 'operator': {
        qb.select('creator.userId', 'fieldValue').addSelect('COUNT(distinct(person.id))', 'count').groupBy('creator.userId');
        break;
      }
    }
    const rows = await qb.getRawMany();
    switch (aggsField) {
      case 'operator': {
        const operators = await this.userRepo.findByIds(rows.map((r) => r.fieldValue));
        return compact(
          rows.map((r) => {
            if (!r.fieldValue) return;
            const operator = operators.find((d) => d.userId == r.fieldValue);
            r.fieldLabel = operator?.name || '';
            r.count = Number(r.count);
            return r;
          }),
        );
      }
      case 'group': {
        const groups = await this.groupRepo.find({
          where: {
            orgId: currentUser.currentOrg,
            groupType: GroupType.PersonGroup,
          },
          order: { order: 'DESC' },
        });
        const result = [];
        const noGroupRow = rows.find((r) => r.fieldValue == -1);
        result.push({
          fieldLabel: '未分组',
          fieldValue: '-1',
          count: Number(noGroupRow?.count) || 0,
        });
        groups.forEach((g) => {
          const row = rows.find((r) => g.groupId == r.fieldValue);
          result.push({
            fieldLabel: g.name,
            fieldValue: g.groupId + '',
            count: Number(row?.count) || 0,
          });
        });
        return result;
      }
      default: {
        this.logger.error(`aggsField:${aggsField} not support`);
        break;
      }
    }
    return rows.map((r) => {
      r.count = Number(r.count);
      return r;
    });
  }

  public async getPersonById(id: number): Promise<PersonEntity> {
    return await this.personRepo.findOne(id);
  }
}
