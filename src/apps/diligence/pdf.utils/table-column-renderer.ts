import { isEmpty } from 'lodash';

/**
 * 疑似关系
 */
export function suspectedRelationship(item: Record<string, any>) {
  const nodes: string[] = [];

  if (item.isSameName) {
    nodes.push(`
      <div>
        <span>疑似同名</span>
        <span>（${item.name}）</span>
      </div>
    `);
  }

  if (item.isSameContact) {
    const contacts: string[] = [];
    if (!isEmpty(item.phones)) {
      const phones = item.phones.map(({ n, t }) => `${n} ${t}`);
      contacts.push(...phones);
    }
    if (!isEmpty(item.emails)) {
      const emails = item.emails.map(({ e }) => e);
      contacts.push(...emails);
    }
    if (!isEmpty(contacts)) {
      nodes.push(`
        <div>
          <span>相同联系方式</span>
          <span>（${contacts.join('，')}）</span>
        </div>
      `);
    }
  }

  return nodes.join('');
}

/**
 * 渲染近期变理受益所有人
 * @param item
 * @param extractKey BeforeContent | AfterContent
 * @returns
 */
export function beneficiaryColumnRenderer(item: Record<string, any>, extractKey: string) {
  // 渲染受益人DOM结构
  const personInfoToDOM = (personInfo: Record<string, any>) => {
    return `<div>${personInfo.Name}${personInfo.PercentTotal ? `，受益股份(${personInfo.PercentTotal})` : ''}</div>`;
  };

  try {
    const parsedValue = JSON.parse(item?.ChangeExtend)[0];
    const personInfo = parsedValue[extractKey] ? JSON.parse(parsedValue[extractKey]) : '';
    if (!personInfo) {
      return '-';
    }
    if (Array.isArray(personInfo)) {
      return personInfo
        .map((content) => {
          return personInfoToDOM(content);
        })
        .join('');
    }
    return personInfoToDOM(personInfo);
  } catch (error) {
    return '-';
  }
}

export const renderStaffName = (item) => {
  const { relationPersonId, relationPersonName, relationship, name, personNo } = item;
  if (relationPersonId && relationPersonId !== -1) {
    const relationPersonNo = personNo.split(`_${relationship}_`)[0];
    return `${relationPersonNo}_${relationPersonName}—${name}（${relationship}）`;
  }
  return `${personNo}_${name}`;
};

/**
 * 人员带核实标签处理
 */
export function personNameWithTagRenderer(item: Record<string, any>) {
  const { personNo, status } = item;
  if (!personNo) {
    return '-';
  }
  const tag = status === 1 ? `<span class="status-tag danger">是本人</span>` : '';
  return `${renderStaffName(item)} ${tag}`;
}

export const renderCompanyNameRelated = (item) => {
  const elements = [`<span>${item.companyNameRelated}</span>`];
  if (item.history && item.role) {
    elements.push(`<span class="status-tag default">${item.role}</span>`);
  }
  return elements.join('') || '-';
};

/**
 * 相同实际控制人关联关系（控制路径/控制链）
 */
export function controlRelationsPath(_, item, originalData) {
  const paths = item?.details?.path || [];
  if (paths.length === 0) {
    return '';
  }
  return paths
    .map((pList, index) => {
      const description = `控制路径${index + 1}（占比约 ${pList[pList.length - 1]?.PercentTotal} ）`;
      const entities = pList.map((p) => `（${p.Percent}） → ${p.Name}`);
      // 添加实际控制人及其权占比
      const graph = [`${item.name}`, ...entities].join('');
      return `<div>
            <div style="font-weight: bold;">${description}</div>
            <div>${graph}</div>
          </div>`;
    })
    .join('');
}
