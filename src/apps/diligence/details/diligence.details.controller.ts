import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiCookieAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { RoverSessionGuard } from 'libs/guards/RoverSession.guard';
import { RoverUser } from 'libs/model/common';
import { DimensionDetailService } from './dimension.detail.service';
import {
  HitDetailsBaseResponse,
  HitDetailsResponseAdministrativePenalties,
  HitDetailsResponseAdministrativePenalties2,
  HitDetailsResponseBankruptcy,
  HitDetailsResponseBillDefaults,
  HitDetailsResponseBlacklistPartnerInvestigation,
  HitDetailsResponseBondDefaults,
  HitDetailsResponseBusinessAbnormal1,
  HitDetailsResponseBusinessAbnormal2,
  HitDetailsResponseBusinessAbnormal3,
  HitDetailsResponseBusinessAbnormal4,
  HitDetailsResponseBusinessAbnormal5,
  HitDetailsResponseBusinessAbnormal6,
  HitDetailsResponseBusinessAbnormal7,
  HitDetailsResponseBusinessAbnormal8,
  HitDetailsResponseBusinessAbnormal9,
  HitDetailsResponseCancellationOfFiling,
  HitDetailsResponseCertification,
  HitDetailsResponseChattelMortgage,
  HitDetailsResponseCompanyCredit,
  HitDetailsResponseCompanyShell,
  HitDetailsResponseContractBreach,
  HitDetailsResponseCriminalInvolve,
  HitDetailsResponseCustomerPartnerInvestigation,
  HitDetailsResponseEquityPledge,
  HitDetailsResponseEstablishedTime,
  HitDetailsResponseFakeSOES,
  HitDetailsResponseFinancialHealth,
  HitDetailsResponseFraudList,
  HitDetailsResponseFreezeEquity,
  HitDetailsResponseGuaranteeRisk,
  HitDetailsResponseHitInnerBlackList,
  HitDetailsResponseHitOuterBlackList,
  HitDetailsResponseJudicialAuction,
  HitDetailsResponseLaborContractDispute,
  HitDetailsResponseLandMortgage,
  HitDetailsResponseLiquidation,
  HitDetailsResponseLowCapital,
  HitDetailsResponseMainInfoUpdateAddress,
  HitDetailsResponseMainInfoUpdateBeneficiary,
  HitDetailsResponseMainInfoUpdateHolder,
  HitDetailsResponseMainInfoUpdateLegalPerson,
  HitDetailsResponseMainInfoUpdateName,
  HitDetailsResponseMainInfoUpdatePerson,
  HitDetailsResponseMainInfoUpdateScope,
  HitDetailsResponseMainMemberRestrictedConsumption,
  HitDetailsResponseMainMembersCriminalOffence,
  HitDetailsResponseMainMembersPersonCredit,
  HitDetailsResponseMainMembersRestrictedOutbound,
  HitDetailsResponseNegativeNews,
  HitDetailsResponseNoCapital,
  HitDetailsResponseNoCertification,
  HitDetailsResponseNoTender,
  HitDetailsResponseOperationAbnormal,
  HitDetailsResponsePersonCredit,
  HitDetailsResponsePersonExecution,
  HitDetailsResponseProductQualityProblem1,
  HitDetailsResponseProductQualityProblem2,
  HitDetailsResponseProductQualityProblem6,
  HitDetailsResponseProductQualityProblem7,
  HitDetailsResponseProductQualityProblem9,
  HitDetailsResponseRegulateFinance,
  HitDetailsResponseRestrictedConsumption,
  HitDetailsResponseSalesContractDispute,
  HitDetailsResponseSalesEndExecutionCase,
  HitDetailsResponseSecurityNotice,
  HitDetailsResponseSpotCheck,
  HitDetailsResponseStaffWorkingOutsideForeignInvestment,
  HitDetailsResponseTaxArrearsNotice,
  HitDetailsResponseTaxationOffences,
  HitDetailsResponseTaxCallNotice,
} from '../../../libs/model/diligence/pojo/req&res/details/response';
import {
  HitDetailsBaseQueryParams,
  HitDetailsCreditParam,
  HitDetailsOperationAbnormalParam,
  HitFreezeEquityParam,
  HitPenaltiesParam,
  HitPersonExecutionParam,
} from '../../../libs/model/diligence/pojo/req&res/details/request';
import { GetHitDetailsParamBase, GetHitDetailsParamForPro } from '../../../libs/model/diligence/pojo/req&res/details/GetHitDetailsParam';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { BlacklistInvestConnectionPO } from '../../../libs/model/diligence/pojo/graph/BlacklistInvestConnectionPO';
import { BlacklistPersonConnectionPO } from '../../../libs/model/diligence/pojo/graph/BlacklistPersonConnectionPO';
import { InvestConnectionPO } from '../../../libs/model/diligence/pojo/graph/InvestConnectionPO';
import { PersonConnectionPO } from '../../../libs/model/diligence/pojo/graph/PersonConnectionPO';

@Controller('diligence/details')
@ApiTags('准入调查/获取维度详情')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard)
export class DiligenceDetailsController {
  // constructor(private readonly dimensionDetailServiceHelper: DimensionServiceHelper) {}
  constructor(
    // private readonly dimensionRiskOpeService: DimensionRiskOperateComplianceService,
    private readonly dimensionDetailService: DimensionDetailService,
  ) {}

  // inject code start, do not change anything

  @Post('MainInfoUpdateRegisteredCapital')
  @ApiOperation({ summary: '获取详情: 注册资本变更' })
  @ApiOkResponse({ type: HitDetailsResponseCancellationOfFiling })
  async getMainInfoUpdateRegisteredCapital(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainInfoUpdateRegisteredCapital,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FakeRegister')
  @ApiOperation({ summary: '获取详情: 涉嫌冒名登记' })
  @ApiOkResponse({ type: HitDetailsResponseCancellationOfFiling })
  async getFakeRegister(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.FakeRegister,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PersistentBillOverdue')
  @ApiOperation({ summary: '获取详情: 票据持续逾期 升级为票据承兑风险' })
  @ApiOkResponse({ type: HitDetailsResponseCancellationOfFiling })
  async getPersistentBillOverdue(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.PersistentBillOverdue,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NoticeInTimePeriod')
  @ApiOperation({ summary: '获取详情: 短期多起开庭公告' })
  @ApiOkResponse({ type: HitDetailsResponseCancellationOfFiling })
  async getNoticeInTimePeriod(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.NoticeInTimePeriod,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CancellationOfFiling')
  @ApiOperation({ summary: '获取详情: 注销备案' })
  @ApiOkResponse({ type: HitDetailsResponseCancellationOfFiling })
  async getCancellationOfFiling(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.CancellationOfFiling,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal2')
  @ApiOperation({ summary: '获取详情: 简易注销' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal2 })
  async getBusinessAbnormal2(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.BusinessAbnormal2,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal1')
  @ApiOperation({ summary: '获取详情: 经营状态非存续' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal1 })
  async getBusinessAbnormal1(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.BusinessAbnormal1,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal5')
  @ApiOperation({ summary: '获取详情: 疑似停业歇业停产或被吊销证照' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal5 })
  async getBusinessAbnormal5(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.BusinessAbnormal5,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal7')
  @ApiOperation({ summary: '获取详情: 无统一社会信用代码' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal7 })
  async getBusinessAbnormal7(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.BusinessAbnormal7,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal3')
  @ApiOperation({ summary: '获取详情: 被列入经营异常名录' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal3 })
  async getBusinessAbnormal3(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.BusinessAbnormal3,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal4')
  @ApiOperation({ summary: '获取详情: 被列入非正常户' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal4 })
  async BusinessAbnormal4(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.BusinessAbnormal4,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NoCapital')
  @ApiOperation({ summary: '获取详情: 久无实缴' })
  @ApiOkResponse({ type: HitDetailsResponseNoCapital })
  async getNoCapital(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.NoCapital,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FakeSOES')
  @ApiOperation({ summary: '获取详情: 假冒国企' })
  @ApiOkResponse({ type: HitDetailsResponseFakeSOES })
  async getFakeSOES(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.FakeSOES,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyShell')
  @ApiOperation({ summary: '获取详情: 疑似空壳企业' })
  @ApiOkResponse({ type: HitDetailsResponseCompanyShell })
  async getCompanyShell(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.CompanyShell,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal6')
  @ApiOperation({ summary: '获取详情: 经营期限已过有效期' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal6 })
  async getBusinessAbnormal6(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.BusinessAbnormal6,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal8')
  @ApiOperation({ summary: '获取详情: 临近经营期限' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal8 })
  async getBusinessAbnormal8(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.BusinessAbnormal8,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal9')
  @ApiOperation({ summary: '获取详情: 经营期限已到期或临近到期' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal9 })
  async getBusinessAbnormal9(@Req() req, @Body() body: GetHitDetailsParamForPro) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.BusinessAbnormal9,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PersonCreditCurrent')
  @ApiOperation({ summary: '获取详情: 被列入失信被执行人（当前有效）' })
  @ApiOkResponse({ type: HitDetailsResponsePersonCredit })
  async getPersonCreditCurrent(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.PersonCreditCurrent,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PersonCreditHistory')
  @ApiOperation({ summary: '获取详情: 被列入失信被执行人（历史）' })
  @ApiOkResponse({ type: HitDetailsResponsePersonCredit })
  async getPersonCreditHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.PersonCreditHistory,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RestrictedConsumptionCurrent')
  @ApiOperation({ summary: '获取详情: 限制高消费（当前有效）' })
  @ApiOkResponse({ type: HitDetailsResponseRestrictedConsumption })
  async getRestrictedConsumptionCurrent(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.RestrictedConsumptionCurrent,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RestrictedConsumptionHistory')
  @ApiOperation({ summary: '获取详情: 历史限制高消费' })
  @ApiOkResponse({ type: HitDetailsResponseRestrictedConsumption })
  async getRestrictedConsumptionHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.RestrictedConsumptionHistory,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxationOffences')
  @ApiOperation({ summary: '获取详情: 重大税收违法' })
  @ApiOkResponse({ type: HitDetailsResponseTaxationOffences })
  async getTaxationOffences(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.TaxationOffences,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('Bankruptcy')
  @ApiOperation({ summary: '获取详情: 破产重整' })
  @ApiOkResponse({ type: HitDetailsResponseBankruptcy })
  async getBankruptcy(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.Bankruptcy,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FreezeEquity')
  @ApiOperation({ summary: '获取详情: 股权冻结' })
  @ApiOkResponse({ type: HitDetailsResponseFreezeEquity })
  async getFreezeEquity(@Req() req, @Body() body: HitFreezeEquityParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.FreezeEquity,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PersonExecution')
  @ApiOperation({ summary: '获取详情: 被执行人' })
  @ApiOkResponse({ type: HitDetailsResponsePersonExecution })
  async getPersonExecution(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.PersonExecution,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainMembersPersonCreditCurrent')
  @ApiOperation({ summary: '获取详情: 主要人员被列入失信被执行人（当前有效）' })
  @ApiOkResponse({ type: HitDetailsResponseMainMembersPersonCredit })
  async getMainMembersPersonCreditCurrent(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainMembersPersonCreditCurrent,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainMembersRestrictedConsumptionCurrent')
  @ApiOperation({ summary: '获取详情: 主要人员限制高消费（当前有效）' })
  @ApiOkResponse({ type: HitDetailsResponseMainMemberRestrictedConsumption })
  async getMainMembersRestrictedConsumptionCurrent(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainMembersRestrictedOutbound')
  @ApiOperation({ summary: '获取详情: 主要人员限制出境' })
  @ApiOkResponse({ type: HitDetailsResponseMainMembersRestrictedOutbound })
  async getMainMembersRestrictedOutbound(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainMembersRestrictedOutbound,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SubsidiaryPersonCreditCurrent')
  @ApiOperation({ summary: '获取详情: 子公司被列入失信被执行人（当前有效）' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getSubsidiaryPersonCreditCurrent(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.SubsidiaryPersonCreditCurrent,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SubsidiaryRestrictedConsumptionCurrent')
  @ApiOperation({ summary: '获取详情: 子公司限制高消费（当前有效）' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getSubsidiaryRestrictedConsumptionCurrent(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.SubsidiaryRestrictedConsumptionCurrent,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SubsidiaryRestrictedOutbound')
  @ApiOperation({ summary: '获取详情: 子公司限制出境' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getSubsidiaryRestrictedOutbound(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.SubsidiaryRestrictedOutbound,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ChattelSeizure')
  @ApiOperation({ summary: '获取详情: 动产查封' })
  @ApiOkResponse({ type: HitDetailsResponseContractBreach })
  async getChattelSeizure(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.ChattelSeizure,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ContractBreach')
  @ApiOperation({ summary: '获取详情: 合同违约' })
  @ApiOkResponse({ type: HitDetailsResponseContractBreach })
  async getContractBreach(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.ContractBreach,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('OperationAbnormal')
  @ApiOperation({ summary: '获取详情: 多次被列入经营异常名录【当下未列入】' })
  @ApiOkResponse({ type: HitDetailsResponseOperationAbnormal })
  async getOperationAbnormal(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.OperationAbnormal,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyCredit')
  @ApiOperation({ summary: '获取详情: 被列入严重违法失信企业名录' })
  @ApiOkResponse({ type: HitDetailsResponseCompanyCredit })
  async getCompanyCredit(@Req() req, @Body() body: HitDetailsOperationAbnormalParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.CompanyCredit,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyCreditHistory')
  @ApiOperation({ summary: '获取详情: 被列入严重违法失信企业名录（历史）' })
  @ApiOkResponse({ type: HitDetailsResponseCompanyCredit })
  async getCompanyCreditHistory(@Req() req, @Body() body: HitDetailsOperationAbnormalParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.CompanyCreditHistory,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyOrMainMembersCriminalOffence')
  @ApiOperation({ summary: '获取详情: 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为' })
  @ApiOkResponse({ type: HitDetailsResponseMainMembersCriminalOffence })
  async getCompanyOrMainMembersCriminalOffence(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyOrMainMembersCriminalOffenceHistory')
  @ApiOperation({ summary: '获取详情: 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为（3年以上）' })
  @ApiOkResponse({ type: HitDetailsResponseMainMembersCriminalOffence })
  async getCompanyOrMainMembersCriminalOffenceHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EnvironmentalPenalties')
  @ApiOperation({ summary: '获取详情: 环保处罚' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties })
  async getEnvironmentalPenalties(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.EnvironmentalPenalties,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('AdministrativePenalties')
  @ApiOperation({ summary: '获取详情: 行政处罚' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties })
  async getAdministrativePenalties(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.AdministrativePenalties,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxPenalties')
  @ApiOperation({ summary: '获取详情: 税务处罚' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties })
  async getTaxPenalties(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.TaxPenalties,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('AdministrativePenalties2')
  @ApiOperation({ summary: '获取详情: 涉及商业贿赂、垄断行为或政府采购活动违法行为' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties2 })
  async getAdministrativePenalties2(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.AdministrativePenalties2,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('AdministrativePenalties3')
  @ApiOperation({ summary: '获取详情: 3年前涉及商业贿赂、垄断行为或政府采购活动违法行为' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties2 })
  async getAdministrativePenalties3(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.AdministrativePenalties3,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SpotCheck')
  @ApiOperation({ summary: '获取详情: 抽查检查-不合格' })
  @ApiOkResponse({ type: HitDetailsResponseSpotCheck })
  async getSpotCheck(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.SpotCheck,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxCallNotice')
  @ApiOperation({ summary: '获取详情: 税务催缴公告' })
  @ApiOkResponse({ type: HitDetailsResponseTaxCallNotice })
  async getTaxCallNotice(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.TaxCallNotice,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxArrearsNotice')
  @ApiOperation({ summary: '获取详情: 欠税公告' })
  @ApiOkResponse({ type: HitDetailsResponseTaxArrearsNotice })
  async getTaxArrearsNotice(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.TaxArrearsNotice,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getProductQualityProblem(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.ProductQualityProblem,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem1')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-产品召回' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem1 })
  async getProductQualityProblem1(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.ProductQualityProblem1,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem2')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-产品抽查不合格' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem2 })
  async getProductQualityProblem2(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.ProductQualityProblem2,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem6')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-未准入境' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem6 })
  async getProductQualityProblem6(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.ProductQualityProblem6,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem7')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-药品抽查【检验不合格】' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem7 })
  async getProductQualityProblem7(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.ProductQualityProblem7,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem9')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-食品安全【检查不合格】' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem9 })
  async getProductQualityProblem9(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.ProductQualityProblem9,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BillDefaults')
  @ApiOperation({ summary: '获取详情: 票据违约' })
  @ApiOkResponse({ type: HitDetailsResponseBillDefaults })
  async getBillDefaults(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.BillDefaults,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BondDefaults')
  @ApiOperation({ summary: '获取详情: 债券违约' })
  @ApiOkResponse({ type: HitDetailsResponseBondDefaults })
  async getBondDefaults(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.BondDefaults,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('LandMortgage')
  @ApiOperation({ summary: '获取详情: 土地抵押' })
  @ApiOkResponse({ type: HitDetailsResponseLandMortgage })
  async getLandMortgage(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.LandMortgage,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ChattelMortgage')
  @ApiOperation({ summary: '获取详情: 动产抵押' })
  @ApiOkResponse({ type: HitDetailsResponseChattelMortgage })
  async getChattelMortgage(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.ChattelMortgage,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EquityPledge')
  @ApiOperation({ summary: '获取详情: 股权出质' })
  @ApiOkResponse({ type: HitDetailsResponseEquityPledge })
  async getEquityPledge(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.EquityPledge,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('JudicialAuction')
  @ApiOperation({ summary: '获取详情: 司法拍卖' })
  @ApiOkResponse({ type: HitDetailsResponseJudicialAuction })
  async getJudicialAuction(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.JudicialAuction,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  // @Post('JudicialAuction1')
  // @ApiOperation({ summary: '获取详情: 司法拍卖(机器设备)' })
  // @ApiOkResponse({ type: HitDetailsBaseResponse })
  // async getJudicialAuction1(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
  //   const currentUser: RoverUser = req.user;
  //   const hitDetailParam: GetHitDetailsParamBase = {
  //     key: DimensionLevel3Enums.JudicialAuction1,
  //     proDetailId: body['proDetailId'],
  //     orgId: currentUser.currentOrg,
  //   };
  //   return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  // }

  @Post('GuaranteeRisk')
  @ApiOperation({ summary: '获取详情: 担保风险' })
  @ApiOkResponse({ type: HitDetailsResponseGuaranteeRisk })
  async getGuaranteeRisk(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.GuaranteeRisk,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }
  // 维度已移除
  // @Post('GuaranteeInfo')
  // @ApiOperation({ summary: '获取详情: 担保明细' })
  // @ApiOkResponse({ type: HitDetailsBaseResponse })
  // async getGuaranteeInfo(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
  //   const currentUser: RoverUser = req.user;
  //   const hitDetailParam: GetHitDetailsParamBase = {
  //     key: DimensionLevel2Enums.GuaranteeInfo,
  //     proDetailId: body['proDetailId'],
  //     orgId: currentUser.currentOrg,
  //   };
  //   return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  // }

  @Post('EstablishedTime')
  @ApiOperation({ summary: '获取详情: 新成立企业' })
  @ApiOkResponse({ type: HitDetailsResponseEstablishedTime })
  async getEstablishedTime(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.EstablishedTime,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('LowCapital')
  @ApiOperation({ summary: '获取详情: 注册资本小于100万' })
  @ApiOkResponse({ type: HitDetailsResponseLowCapital })
  async getLowCapital(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.LowCapital,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateScope')
  @ApiOperation({ summary: '获取详情: 经营范围变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateScope })
  async getMainInfoUpdateScope(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainInfoUpdateScope,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateAddress')
  @ApiOperation({ summary: '获取详情: 注册地址变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateAddress })
  async getMainInfoUpdateAddress(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainInfoUpdateAddress,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateName')
  @ApiOperation({ summary: '获取详情: 企业名称变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateName })
  async getMainInfoUpdateName(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainInfoUpdateName,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateLegalPerson')
  @ApiOperation({ summary: '获取详情: 法定代表人变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateLegalPerson })
  async getMainInfoUpdateLegalPerson(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainInfoUpdateLegalPerson,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateHolder')
  @ApiOperation({ summary: '获取详情: 大股东变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateHolder })
  async getMainInfoUpdateHolder(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainInfoUpdateHolder,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateBeneficiary')
  @ApiOperation({ summary: '获取详情: 近期变更受益所有人' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateBeneficiary })
  async getMainInfoUpdateBeneficiary(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainInfoUpdateBeneficiary,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateManager')
  @ApiOperation({ summary: '获取详情: 董监高变更' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getMainInfoUpdateManager(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainInfoUpdateManager,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdatePerson')
  @ApiOperation({ summary: '获取详情: 实际控制人变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdatePerson })
  async getMainInfoUpdatePerson(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MainInfoUpdatePerson,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('HitInnerBlackList')
  @ApiOperation({ summary: '获取详情: 被列入黑名单' })
  @ApiOkResponse({ type: HitDetailsResponseHitInnerBlackList })
  async getHitInnerBlackList(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.HitInnerBlackList,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('Shareholder')
  @ApiOperation({ summary: '获取详情: 参股股东与黑名单存在关联关系' })
  @ApiOkResponse({ type: BlacklistInvestConnectionPO })
  async getShareholder(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.Shareholder,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ForeignInvestment')
  @ApiOperation({ summary: '获取详情: 对外投资主体被列入黑名单' })
  @ApiOkResponse({ type: BlacklistInvestConnectionPO })
  async getForeignInvestment(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.ForeignInvestment,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EmploymentRelationship')
  @ApiOperation({ summary: '获取详情: 董监高法被列入黑名单' })
  @ApiOkResponse({ type: BlacklistPersonConnectionPO })
  async getEmploymentRelationship(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.EmploymentRelationship,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BlacklistSameSuspectedActualController')
  @ApiOperation({ summary: '获取详情: 与内部黑名单列表存在相同实际控制人关联' })
  @ApiOkResponse({ type: BlacklistPersonConnectionPO })
  async getBlacklistSameSuspectedActualController(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.BlacklistSameSuspectedActualController,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BlacklistPartnerInvestigation')
  @ApiOperation({ summary: '获取详情: 与内部黑名单企业存在投资任职关联' })
  @ApiOkResponse({ type: HitDetailsResponseBlacklistPartnerInvestigation })
  async getBlacklistPartnerInvestigation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.BlacklistPartnerInvestigation,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('HitOuterBlackList')
  @ApiOperation({ summary: '获取详情: 黑名单（外部）被列入黑名单' })
  @ApiOkResponse({ type: HitDetailsResponseHitOuterBlackList })
  async getHitOuterBlackList(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.HitOuterBlackList,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('StaffWorkingOutside')
  @ApiOperation({ summary: '获取详情: 疑似潜在利益冲突-在外任职' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getStaffWorkingOutside(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.StaffWorkingOutside,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('StaffForeignInvestment')
  @ApiOperation({ summary: '获取详情: 潜在利益冲突-对外投资' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getStaffForeignInvestment(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.StaffForeignInvestment,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SamePhone')
  @ApiOperation({ summary: '获取详情: 潜在利益冲突-相同电话' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getSamePhone(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.SamePhone,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('StaffWorkingOutsideForeignInvestment')
  @ApiOperation({ summary: '获取详情: 潜在利益冲突' })
  @ApiOkResponse({ type: HitDetailsResponseStaffWorkingOutsideForeignInvestment })
  async getStaffWorkingOutsideForeignInvestment(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SuspectedInterestConflict')
  @ApiOperation({ summary: '获取详情: 疑似潜在利益冲突' })
  @ApiOkResponse({ type: HitDetailsResponseStaffWorkingOutsideForeignInvestment })
  async getSuspectedInterestConflict(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.SuspectedInterestConflict,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PunishedEmployeesWorkingOutside')
  @ApiOperation({ summary: '获取详情: 曾被处罚的现任员工或前员工-在外任职' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getPunishedEmployeesWorkingOutside(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.PunishedEmployeesWorkingOutside,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PunishedEmployeesForeignInvestment')
  @ApiOperation({ summary: '获取详情: 曾被处罚的现任员工或前员工-对外投资' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getPunishedEmployeesForeignInvestment(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.PunishedEmployeesForeignInvestment,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('InvestorsRelationship')
  @ApiOperation({ summary: '获取详情: 目标主体与客商列表主体投资关联' })
  @ApiOkResponse({ type: InvestConnectionPO })
  async getInvestorsRelationship(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.InvestorsRelationship,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ShareholdingRelationship')
  @ApiOperation({ summary: '获取详情: 目标主体与客商列表主体持股关联' })
  @ApiOkResponse({ type: InvestConnectionPO })
  async getShareholdingRelationship(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.ShareholdingRelationship,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ServeRelationship')
  @ApiOperation({ summary: '获取详情: 目标主体与客商列表主体任职关联' })
  @ApiOkResponse({ type: PersonConnectionPO })
  async getServeRelationship(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.ServeRelationship,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SameSuspectedActualController')
  @ApiOperation({ summary: '获取详情: 与第三方列表主体存在相同实际控制人关联' })
  @ApiOkResponse({ type: PersonConnectionPO })
  async getSameSuspectedActualController(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.SameSuspectedActualController,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CustomerPartnerInvestigation')
  @ApiOperation({ summary: '获取详情: 与第三方列表企业存在投资任职关联' })
  @ApiOkResponse({ type: HitDetailsResponseCustomerPartnerInvestigation })
  async getCustomerPartnerInvestigation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.CustomerPartnerInvestigation,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    body.userId = currentUser.userId;
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NoTender')
  @ApiOperation({ summary: '获取详情: 无招投标记录' })
  @ApiOkResponse({ type: HitDetailsResponseNoTender })
  async getNoTender(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.NoTender,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NegativeNewsRecent')
  @ApiOperation({ summary: '获取详情: 近三年负面新闻' })
  @ApiOkResponse({ type: HitDetailsResponseNegativeNews })
  async getNegativeNewsRecent(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.NegativeNewsRecent,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NegativeNewsHistory')
  @ApiOperation({ summary: '获取详情: 三年前负面新闻' })
  @ApiOkResponse({ type: HitDetailsResponseNegativeNews })
  async getNegativeNewsHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.NegativeNewsHistory,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('LaborContractDispute')
  @ApiOperation({ summary: '获取详情: 劳动纠纷' })
  @ApiOkResponse({ type: HitDetailsResponseLaborContractDispute })
  async getLaborContractDispute(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.LaborContractDispute,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('UnfairCompetition')
  @ApiOperation({ summary: '获取详情: 不正当竞争' })
  @ApiOkResponse({ type: HitDetailsResponseLaborContractDispute })
  async getUnfairCompetition(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.UnfairCompetition,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SalesContractDispute')
  @ApiOperation({ summary: '获取详情: 买卖合同纠纷' })
  @ApiOkResponse({ type: HitDetailsResponseSalesContractDispute })
  async getSalesContractDispute(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.SalesContractDispute,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MajorDispute')
  @ApiOperation({ summary: '获取详情: 重大纠纷' })
  @ApiOkResponse({ type: HitDetailsResponseSalesContractDispute })
  async getMajorDispute(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.MajorDispute,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyOrMainMembersCriminalInvolve')
  @ApiOperation({ summary: '获取详情: 近3年涉刑事裁判相关提及方' })
  @ApiOkResponse({ type: HitDetailsResponseCriminalInvolve })
  async getCompanyOrMainMembersCriminalInvolve(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyOrMainMembersCriminalInvolveHistory')
  @ApiOperation({ summary: '获取详情: 涉刑事裁判相关提及方（3年以上及其他）' })
  @ApiOkResponse({ type: HitDetailsResponseCriminalInvolve })
  async getCompanyOrMainMembersCriminalInvolveHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EndExecutionCase')
  @ApiOperation({ summary: '获取详情: 终本案件' })
  @ApiOkResponse({ type: HitDetailsResponseSalesEndExecutionCase })
  async getEndExecutionCase(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.EndExecutionCase,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FraudList')
  @ApiOperation({ summary: '获取详情:涉诈高风险名单' })
  @ApiOkResponse({ type: HitDetailsResponseFraudList })
  async getFraudList(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.FraudList,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NoQualityCertification')
  @ApiOperation({ summary: '获取详情: 无有效质量管理体系认证资质' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getNoQualityCertification(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.NoQualityCertification,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NoCertification')
  @ApiOperation({ summary: '获取详情: 无有效资质证书' })
  @ApiOkResponse({ type: HitDetailsResponseNoCertification })
  async getNoCertification(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.NoCertification,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('Certification')
  @ApiOperation({ summary: '获取详情: 资质筛查' })
  @ApiOkResponse({ type: HitDetailsResponseCertification })
  async getCertification(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.Certification,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SecurityNotice')
  @ApiOperation({ summary: '获取详情: 公安通告' })
  @ApiOkResponse({ type: HitDetailsResponseSecurityNotice })
  async getSecurityNotice(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.SecurityNotice,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CapitalReduction')
  @ApiOperation({ summary: '获取详情: 减资公告' })
  @ApiOkResponse({ type: HitDetailsResponseSecurityNotice })
  async getCapitalReduction(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.CapitalReduction,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('Liquidation')
  @ApiOperation({ summary: '获取详情: 清算信息' })
  @ApiOkResponse({ type: HitDetailsResponseLiquidation })
  async getLiquidation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.Liquidation,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RegulateFinance')
  @ApiOperation({ summary: '获取详情: 监管处罚' })
  @ApiOkResponse({ type: HitDetailsResponseRegulateFinance })
  async getRegulateFinance(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.RegulateFinance,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FinancialHealth')
  @ApiOperation({ summary: '获取详情: 财务健康度' })
  @ApiOkResponse({ type: HitDetailsResponseFinancialHealth })
  async getFinancialHealth(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.FinancialHealth,
      orgId: currentUser.currentOrg,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxReminder')
  @ApiOperation({ summary: '获取详情: 税务催报' })
  @ApiOkResponse({ type: HitDetailsResponseFinancialHealth })
  async getTaxReminder(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.TaxReminder,
      orgId: currentUser.currentOrg,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxCallNoticeV2')
  @ApiOperation({ summary: '获取详情: 税务催缴' })
  @ApiOkResponse({ type: HitDetailsResponseTaxCallNotice })
  async getTaxCallNoticeV2(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.TaxCallNoticeV2,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CustomerSuspectedRelation')
  @ApiOperation({ summary: '获取详情: 与第三方列表企业存在交叉重叠疑似关联' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getCustomerSuspectedRelation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.CustomerSuspectedRelation,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    body.userId = currentUser.userId;
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BlacklistSuspectedRelation')
  @ApiOperation({ summary: '获取详情: 与内部黑名单企业存在疑似关联关系' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getBlacklistSuspectedRelation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.BlacklistSuspectedRelation,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('StockPledge')
  @ApiOperation({ summary: '获取详情: 股权质押' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getStockPledge(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.StockPledge,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('IPRPledge')
  @ApiOperation({ summary: '获取详情: 知识产权质押' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getIPRPledge(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel2Enums.IPRPledge,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EmployeeReduction')
  @ApiOperation({ summary: '获取详情: 员工人数减少' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getEmployeeReduction(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.EmployeeReduction,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('DebtOverdue')
  @ApiOperation({ summary: '获取详情: 债务逾期' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getDebtOverdue(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: RoverUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionLevel3Enums.DebtOverdue,
      proDetailId: body['proDetailId'],
      orgId: currentUser.currentOrg,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  // inject code end, do not change anything
}
