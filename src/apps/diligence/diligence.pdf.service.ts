import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { DiligenceSnapshotHelper } from './snapshot/diligence.snapshot.helper';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ConfigService } from 'libs/config/config.service';
import { DueDiligenceResult } from 'libs/model/diligence/pojo/model/ModelScorePO';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import * as HBS from 'hbs';
import * as fs from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { cloneDeep, flattenDeep, get, find, forEach, map, replace, upperFirst, entries } from 'lodash';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { AxiosRequestConfig } from 'axios';
import { TABLE_COLUMNS } from './pdf.utils/table-columns.config';
import { EMPTY_SHELL_TABLE_COLUMNS } from './pdf.utils/table-columns-empty-shell.config';
import * as Bluebird from 'bluebird';
import { GroupDimensionScorePO } from 'libs/model/diligence/pojo/dimension/GroupDimensionScorePO';
import { LevelGroupDimensionPO } from 'libs/model/diligence/pojo/dimension/LevelGroupDimensionPO';
import { CompanySearchService } from 'apps/company/company-search.service';
import * as moment from 'moment';
import { EnterpriseLibService } from '../data/source/enterprise.lib.service';
import { PDF_REPORT_PAGE_HEADER_LOGO } from './pdf.utils/page-assets';
import * as uuidTime from 'uuid-time';
import { DiligenceRemarkEntity } from 'libs/entities/DiligenceRemarkEntity';
import { isOrganism } from 'apps/company/utils';
import { templateHelperRegister } from 'apps/utils/pdf/pdf-template.util';
import { Cacheable } from '@type-cacheable/core';
import { Benefit } from 'libs/model/diligence/pojo/diligence/Benefit';
import { CompanyDetailService } from '../company/company-detail.service';
import { NodeHtmlMarkdown } from 'node-html-markdown';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';

enum ActualControllerTypeMaps {
  /** 疑似实际控制人, 判定依据 (李宁（中国）体育用品有限公司) */
  SuspectActual = 'ActualControllerV5YisiActual', // 李宁（中国）体育用品有限公司: 疑似实际控制人, 判定依据
  /** 实际控制人、表决权比例 (乐视网信息技术（北京）股份有限公司) */
  Actual1 = 'ActualControllerV5Actual1',
  /** 实际控制人、直接持股比例、表决权比例 (?) */
  Actual2 = 'ActualControllerV5Actual2',
  Actual2PercentTotal = 'ActualControllerV5Actual2WithPercentTotal',
  Actual2TitleAndControlPercent = 'ActualControllerV5Actual2WithTitleAndControlPercent',
  Actual2ControlPercent = 'ActualControllerV5Actual2WithControlPercent',
  /** 实际控制人、总持股比例 (?) */
  Actual3 = 'ActualControllerV5Actual3',
  /** 实际控制人（公示信息）、总持股比例 (宁德时代新能源科技股份有限公司) */
  Actual3Title = 'ActualControllerV5Actual3WithTitle',
  /** 实际控制人、直接持股比例、总持股比例 (锐奇控股股份有限公司) */
  Actual4 = 'ActualControllerV5Actual4',
  /** 实际控制人、直接持股比例、总持股比例 (锐奇控股股份有限公司) */
  Actual4Title = 'ActualControllerV5Actual4WithTitle',
  /** 实际控制人、总持股比例、表决权比例 (中铁投资集团有限公司/北京天眼查科技有限公司) */
  Actual5 = 'ActualControllerV5Actual5',
  Actual5PercentTotal = 'ActualControllerV5Actual5WithPercentTotal',
  Actual6 = 'ActualControllerV5Actual6',
  /** 实际控制人（公示信息）、直接持股比例 (中国供销集团有限公司) */
  Actual6Title = 'ActualControllerV5Actual6WithTitle',
}

/** 实际控制人: 路径中的角色映射 */
const shareHoldTypeMap = [
  {
    type: 1,
    typeDesc: '参控股-直接持股',
    lineType: 'solid',
    tips: '数据源于xxx的公告披露',
    lineTextPrefix: '参控股',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 2,
    typeDesc: '参控股-间接持股',
    lineType: 'dashed',
    tips: '数据源于xxx的公告披露',
    lineTextPrefix: '参控股',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 3,
    typeDesc: '参控股-未持股',
    lineType: 'solid',
    isSpecial: false,
  },
  {
    type: 0,
    typeDesc: '参控股-未披露',
    lineType: 'solid',
    isSpecial: false,
  },
];
/** 实际控制人: 路径中的控制类型映射 */
const controlTypeMap = [
  {
    type: 1,
    typeDesc: '普通持股',
    lineType: 'solid',
    isSpecial: false,
  },
  {
    type: 2,
    typeDesc: '协议控制',
    lineType: 'dashed',
    tips: '数据源于xxx的公告披露，中间可能会存在折叠路径。',
    lineTextPrefix: '协议控制',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 3,
    typeDesc: '信托控制',
    lineType: 'dashed',
    tips: '数据源于xxx的公告披露',
    lineTextPrefix: '信托控制',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 4,
    typeDesc: '普通合伙人',
    lineType: 'solid',
    lineTextPrefix: '普通合伙人',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 5,
    typeDesc: '疑似实际控制人',
    lineType: 'dashed',
    tips: '',
    isSpecial: true,
  },
  {
    type: 6,
    typeDesc: '执行事务合伙人',
    lineType: 'solid',
    lineTextPrefix: '执行事务合伙人',
    isSpecial: false,
  },
  {
    type: 7,
    typeDesc: '同股不同权',
    lineType: 'dashed',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 0,
    typeDesc: '其他',
    lineType: 'solid',
    isSpecial: true,
  },
];

@Injectable()
export class DiligencePDFService {
  private logger: Logger = QccLogger.getLogger(DiligencePDFService.name);
  private readonly pdfTemplate;
  private readonly pdfCoverTemplate;
  private readonly markdownTemplate;

  constructor(
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(DiligenceRemarkEntity) private readonly diligenceRemarkRepo: Repository<DiligenceRemarkEntity>,
    private readonly snapshotHelper: DiligenceSnapshotHelper,
    redisService: RedisService,
    private readonly configService: ConfigService,
    private readonly httpUtils: HttpUtilsService,
    private readonly searchService: CompanySearchService,
    private readonly entLibService: EnterpriseLibService,
    private readonly companyDetailService: CompanyDetailService,
  ) {
    //useAdapter(redisService.getClient());
    const hbs = HBS.create();

    /**
     * 注册通用渲染模板工具函数
     */
    templateHelperRegister(hbs, {
      tableColumns: {
        ...TABLE_COLUMNS,
        ...EMPTY_SHELL_TABLE_COLUMNS,
      },
    });

    /**
     * 人工审核风险等级
     */
    hbs.registerHelper('risk_manual_level_to_label', function (level: number) {
      switch (level) {
        case 2:
          return '【高风险】';
        case 1:
          return '【中风险】';
        case 0:
          return '【低风险】';
        default:
          return '-';
      }
    });

    hbs.registerHelper('risk_verify_type_to_label', function (type: number) {
      switch (type) {
        case 1:
          return '【电话核实】';
        case 2:
          return '【实地核实】';
        case 3:
          return '【网络核实】';
        case 4:
          return '【其他】';
        default:
          return '-';
      }
    });

    hbs.registerHelper('risk_level_to_alias', function (level: number, type: string) {
      enum RISK_LEVEL {
        HIGH = 2,
        MEDIUM = 1,
        LOW = 0,
      }

      const RISK_LEVEL_NAME_MAP = {
        [RISK_LEVEL.HIGH]: 'high',
        [RISK_LEVEL.MEDIUM]: 'medium',
        [RISK_LEVEL.LOW]: 'low',
      };
      const RISK_LEVEL_LABEL_MAP = {
        [RISK_LEVEL.HIGH]: '警示',
        [RISK_LEVEL.MEDIUM]: '关注',
        [RISK_LEVEL.LOW]: '无风险',
      };
      const RISK_LEVEL_RESULT_MAP = {
        [RISK_LEVEL.HIGH]: '不通过',
        [RISK_LEVEL.MEDIUM]: '不通过',
        [RISK_LEVEL.LOW]: '通过',
      };
      switch (type) {
        case 'label':
          return RISK_LEVEL_LABEL_MAP[level];
        case 'result':
          return RISK_LEVEL_RESULT_MAP[level];
        case 'name':
        default:
          return RISK_LEVEL_NAME_MAP[level];
      }
    });

    /**
     * 合同违约风险
     */
    hbs.registerHelper('contract_breach_to_alias', function (level: string) {
      const CONTRACT_BREACH_RISK_LEVEL_MAP = {
        L5: 'L5/极高风险',
        L4: 'L4/高风险',
        L3: 'L3/中风险',
        L2: 'L2/低风险',
        L1: 'L1/极低风险',
      };
      return CONTRACT_BREACH_RISK_LEVEL_MAP[level];
    });

    /**
     * 是否为风险维度
     */
    hbs.registerHelper('is_risk_dimension', function (detail) {
      // scoreDetails 为最终维度
      return !detail.subDimension;
    });

    /**
     * 根据字段（key）判断维度渲染类型
     */
    hbs.registerHelper('get_dimension_type', function (dimensionKey: string, dimensions = {}) {
      // 需要判断返回的数据是否包含IsValid， 如果是，说明是新的排查接过，MULTI_COLUMNS_JSON_TYPE删除BusinessAbnormal4
      let MULTI_COLUMNS_JSON_TYPE = ['BusinessAbnormal4'];
      const dataSource = get(dimensions, [dimensionKey, 'data'], dimensions[dimensionKey]) || [];
      if (dimensionKey === 'BusinessAbnormal4' && dataSource.length > 0 && dataSource[0].hasOwnProperty('IsValid')) {
        MULTI_COLUMNS_JSON_TYPE = [];
      }
      const FIELD_TYPES = {
        // 合同违约 ContractBreach
        CONTRACT_BREACH: ['ContractBreach'],
        // 空壳公司 CompanyShell
        COMPANY_SHELL: ['CompanyShell'],
        // 纯文本类型
        TEXT_TYPE: ['NoTender', 'NoQualityCertification', 'BusinessAbnormal7', 'FraudList', 'NoCertification'],
        // JSON 类型
        JSON_TYPE: ['BusinessAbnormal1', 'BusinessAbnormal6', 'BusinessAbnormal8', 'EstablishedTime', 'LowCapital', 'FinancialHealth'],
        // 多列单行 JSON 类型
        MULTI_COLUMNS_JSON_TYPE,
        // 清算信息
        LIQUIDATION: ['Liquidation'],
        // 经营期限已到期或临近到期
        BusinessAbnormal9: ['BusinessAbnormal9'],
        // 员工人数减少
        EmployeeReduction: ['EmployeeReduction'],
        MULTI_COLUMNS_ARRAY_TYPE: ['NoCapital'],
      };
      const fieldType = Object.keys(FIELD_TYPES).reduce((result: string, type: string) => {
        const fields = FIELD_TYPES[type];
        if (fields.includes(dimensionKey)) {
          return type;
        }
        return result;
      }, 'COMMON');
      return fieldType;
    });

    /**
     * 查查信用分风险等级映射
     */
    hbs.registerHelper('credit_rate_level_to', function (level: string, type: string) {
      enum RiskCreditColor {
        High = '#FFDCB3',
        Middle = '#B8DDFA',
        Low = '#C4F5E0',
      }

      enum RiskCreditBackground {
        High = 'rgba(255, 137, 0, 1)',
        Middle = 'rgba(18, 139, 237, 1)',
        Low = 'rgba(0, 173, 101, 1)',
      }

      const mapping = {
        'L-1': {
          short: '较差',
          reason: '企业内外部存在较多不确定因素',
          summary: '违约风险高',
          color: RiskCreditColor.High,
          background: RiskCreditBackground.High,
        },
        'L-2': {
          short: '较差',
          reason: '受内外部不确定因素的影响大',
          summary: '违约风险较高',
          color: RiskCreditColor.High,
          background: RiskCreditBackground.High,
        },
        'L-3': {
          short: '较差',
          reason: '受内外部不确定因素的影响较大',
          summary: '有一定的违约风险',
          color: RiskCreditColor.High,
          background: RiskCreditBackground.High,
        },
        'L-4': {
          short: '一般',
          reason: '较易受不利经济环境的影响',
          summary: '违约风险中等',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-5': {
          short: '一般',
          reason: '受不利经济环境的影响偏大',
          summary: '违约风险中等',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-6': {
          short: '中等',
          reason: '受不利经济环境的影响适中',
          summary: '违约风险较低',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-7': {
          short: '中等',
          reason: '经营和风险管理能力尚可',
          summary: '违约风险较低',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-8': {
          short: '中等',
          reason: '经营和风险管理能力相对较好',
          summary: '违约风险较低',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-9': {
          short: '良好',
          reason: '经营和风险管理能力较好',
          summary: '违约风险低',
          color: RiskCreditColor.Low,
          background: RiskCreditBackground.Low,
        },
        'L-10': {
          short: '良好',
          reason: '经营和风险管理能力优秀',
          summary: '违约风险低',
          color: RiskCreditColor.Low,
          background: RiskCreditBackground.Low,
        },
        'L-11': {
          short: '优秀',
          reason: '当前处于良性循环状态',
          summary: '违约风险很低',
          color: RiskCreditColor.Low,
          background: RiskCreditBackground.Low,
        },
        'L-12': {
          short: '卓越',
          reason: '当前处于良性循环状态',
          summary: '违约风险很低',
          color: RiskCreditColor.Low,
          background: RiskCreditBackground.Low,
        },
      };
      return mapping?.[level]?.[type];
    });

    /**
     * 根据打分给予风险等级（星）
     */
    hbs.registerHelper('get_credit_rate_star_by_score', function (score: number) {
      const star0 = `<img width="10" height="10" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUBAMAAAB/pwA+AAAAMFBMVEUAAADf39/f39/j4+Pk5OTk5OTh4eHj4+Pi4uLj4+Pi4uLj4+Pi4uLk5OTk5OTj4+OPxBILAAAAD3RSTlMAIBC/389w72BAsJCAnzD/onSqAAAAZ0lEQVQI12NAA4wKcCZzAJwp/xHOPP8NzrT/DNUjKPn//0RBISBz/X8w+ApkpkOYFSAF8SDWFwGQYm4Q8wBEoz9IJRgwgUQFwEw2ELMB4oL/flP+G4CZ6n8VmPZ/AjO1gJJsRgyEAQCwiDK1XzvmgwAAAABJRU5ErkJggg==" />`;
      const star1 = `<img width="10" height="10" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUBAMAAAB/pwA+AAAAMFBMVEUAAAAQh+oQj+8TjO4SjO4Ri+4Si+0Si+0TiuwUi+sRi+wSiuwSiuwSjO0Qiu8Si+3rPTfdAAAAD3RSTlMAIBC/389w72BAsJCAnzD/onSqAAAAZ0lEQVQI12NAA4wKcCZzAJwp/xHOPP8NzrT/DNUjKPn//0RBISBz/X8w+ApkpkOYFSAF8SDWFwGQYm4Q8wBEoz9IJRgwgUQFwEw2ELMB4oL/flP+G4CZ6n8VmPZ/AjO1gJJsRgyEAQCwiDK1XzvmgwAAAABJRU5ErkJggg==" />`;
      const starHalf = `<img width="10" height="10" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAMAAAC6V+0/AAAAWlBMVEUAAADf399tsujf399ttuQqlezk5OTj4+Mcj+0jk+zk5OQumOzh4eHi4uLj4+NVqurj4+MdkOwkkus0muwymevj4+M8nevj4+MnkuowlutLouheq+jj4+MSi+1V2M0/AAAAHHRSTlMAIDgQHNfPv/vz38twYEAw79uwq5iQjIB8cFhMN05qJAAAAHNJREFUGNOljtkKhDAQBOMmu96u99n9/7+pMEoSIr5YT0PB0KWe+esb+f2F7kNEgeyJIvwmYu/zYCWBKjqQvZwCBNkbE1fW5VnYWJnZhO6Sk3JIRXr9hiK9/vmSpVfOdKsyuP0mYWuU0gOgrcwXOYpY5Dt2SfsLXuKIcwEAAAAASUVORK5CYII=" />`;

      const stars: string[] = [];
      const allHalfStars = Math.floor(score / 10) + 1;
      const fullStars = Math.floor(allHalfStars / 2);
      const halfStar = allHalfStars % 2;

      for (let i = 0; i < 5; i++) {
        if (i + 1 <= fullStars) {
          stars.push(star1);
        } else if (i === fullStars && halfStar) {
          stars.push(starHalf);
        } else {
          stars.push(star0);
        }
      }
      return stars.join('');
    });

    /**
     * 生成风险等级说明条形图
     */
    hbs.registerHelper('get_credit_rate_bar_chart', function (score: number) {
      const theme = {
        low: 'rgba(255, 137, 0, 1)',
        middle: 'rgba(18, 139, 237, 1)',
        high: 'rgba(0, 173, 101, 1)',
      };
      const locale = {
        'L-1': '信用表现较差，企业内外部存在较多不确定因素，违约风险高',
        'L-2': '信用表现较差，受内外部不确定因素的影响大，违约风险较高',
        'L-3': '信用表现较差，受内外部不确定因素的影响较大，有一定的违约风险',
        'L-4': '信用表现一般，较易受不利经济环境的影响，违约风险中等',
        'L-5': '信用表现一般，受不利经济环境的影响偏大，违约风险中等',
        'L-6': '信用表现中等，受不利经济环境的影响适中，违约风险较低',
        'L-7': '信用表现中等，经营和风险管理能力尚可，违约风险较低',
        'L-8': '信用表现中等，经营和风险管理能力相对较好，违约风险较低',
        'L-9': '信用表现良好，经营和风险管理能力较好，违约风险低',
        'L-10': '信用表现良好，经营和风险管理能力优秀，违约风险低',
        'L-11': '信用表现优秀，当前处于良性循环状态，违约风险很低',
        'L-12': '信用表现卓越，当前处于良性循环状态，违约风险很低',
      };
      const scale = [
        {
          level: 'low',
          percentage: 25,
          min: 0,
          max: 499,
          range: [
            { levelName: 'L-1', min: 0, max: 299, opacity: 0.5, tickLabel: 'min' },
            { levelName: 'L-2', min: 300, max: 399, opacity: 0.4 },
            { levelName: 'L-3', min: 400, max: 499, opacity: 0.3 },
          ],
        },
        {
          level: 'middle',
          percentage: 42,
          min: 500,
          max: 749,
          range: [
            { levelName: 'L-4', min: 500, max: 549, opacity: 0.2, tickLabel: 'min' },
            { levelName: 'L-5', min: 550, max: 599, opacity: 0.3 },
            { levelName: 'L-6', min: 600, max: 649, opacity: 0.4, tickLabel: 'min' },
            { levelName: 'L-7', min: 650, max: 699, opacity: 0.5 },
            { levelName: 'L-8', min: 700, max: 749, opacity: 0.6 },
          ],
        },
        {
          level: 'high',
          percentage: 33,
          min: 750,
          max: 2000,
          range: [
            { levelName: 'L-9', min: 750, max: 799, opacity: 0.2, tickLabel: 'min' },
            { levelName: 'L-10', min: 800, max: 899, opacity: 0.3 },
            { levelName: 'L-11', min: 900, max: 999, opacity: 0.4, tickLabel: 'min' },
            { levelName: 'L-12', min: 1000, max: 2000, opacity: 0.5, tickLabel: 'max' },
          ],
        },
      ];

      const levelDirectionMap = {
        low: 'left',
        middle: 'center',
        high: 'right',
      };

      const getIndicator = (score: number) => {
        let level; // high
        let levelName; // L-1
        let color; // #ff8900
        let direction; // left | right | middle
        let percentage;
        let residualPercentage = 0;

        for (const item of scale) {
          const rangeList = item.range; // 将范围列表提取到循环外部

          for (let j = 0; j < rangeList.length; j++) {
            const range = rangeList[j];

            if (score >= range.min && score <= range.max) {
              level = item.level;
              levelName = range.levelName;
              color = theme[item.level];
              direction = levelDirectionMap[item.level];

              const unitPercentage = item.percentage / rangeList.length;
              const initPercentage = unitPercentage * j;
              const scorePercentage = unitPercentage * ((score - range.min) / (range.max - range.min));
              percentage = residualPercentage + initPercentage + scorePercentage;

              // 提前终止内部循环
              break;
            }
          }
          residualPercentage += item.percentage;
        }
        return {
          level,
          levelName,
          color,
          direction,
          percentage,
        };
      };
      const indicator = getIndicator(score);

      const labelInnerDOM = (item) => {
        return item.range
          .map((step, stepIndex, stepList) => {
            return `
              <div
                class="bar"
                style="width: ${100 / stepList.length}%;"
              >
                <span style="opacity: ${step.tickLabel === 'min' ? 1 : 0}">
                  ${step.min}
                </span>
                <span style="opacity: ${step.tickLabel === 'max' ? 1 : 0}">
                  ${step.max}
                </span>
              </div>
            `;
          })
          .join('');
      };

      const labelDOM = scale
        .map((item) => {
          return `
          <div class="range" style="width: ${item.percentage}%">
            ${labelInnerDOM(item)}
          </div>
        `;
        })
        .join('');

      const scaleInnerDOM = scale
        .map((item, index, scaleList) => {
          const color = theme[item.level];

          const barDOM = item.range
            .map((step) => {
              return `
              <div
                class="bar"
                style="width: ${100 / item.range.length}%; background: ${color}; opacity: 0.5"
                data-index="${step.levelName}"
              >
              </div>
          `;
            })
            .join('');

          return `
          <div
            class="range ${index === 0 ? 'first' : ''} ${index === scaleList.length - 1 ? 'last' : ''}"
            style="width: ${item.percentage}%;"
          >
            ${barDOM}
          </div>
        `;
        })
        .join('');

      let descriptionOffset: string;
      if (indicator.direction === 'right') {
        descriptionOffset = `right: ${100 - indicator.percentage}%;`;
      } else {
        descriptionOffset = `left: ${indicator.percentage}%;`;
      }

      return `
        <div class="credit-rate-bar-chart">
          <div class="credit-rate-bar-chart__tick">
          ${labelDOM}
          </div>

          <div class="credit-rate-bar-chart__scale">
            <div class="credit-rate-bar-chart__scale__inner">
              ${scaleInnerDOM}

              <div class="credit-rate-bar-chart__indicator" style="background: ${indicator.color}; left: ${indicator.percentage}%;"></div>

              <div
                class="credit-rate-bar-chart__description ${indicator.direction}"
                style="color: ${indicator.color}; ${descriptionOffset}"
              >
                <p>
                  ${indicator.levelName} ${locale[indicator.levelName]}
                </p>
              </div>

            </div>
          </div>

        </div>
      `;
    });

    /**
     * 维度分组是否为空
     */
    hbs.registerHelper('is_level_group_empty', function (item: Record<string, any>): boolean {
      if (item.totalHits === 0 && Array.isArray(item.levelGroups) && item.levelGroups.every(({ groups }) => groups?.length === 0)) {
        return true;
      }
      return false;
    });

    hbs.registerPartials(join(__dirname, '../../../templates/partials'), {
      rename(name) {
        return name.replace(/\W/g, '_');
      },
    });

    // 生成封面封底
    this.pdfCoverTemplate = hbs.compile(fs.readFileSync(join(__dirname, '../../../templates/cover.pdf.hbs'), { encoding: 'utf-8' }).toString(), {
      preventIndent: true,
    });

    // 生成内容
    this.pdfTemplate = hbs.compile(fs.readFileSync(join(__dirname, '../../../templates/index.pdf.hbs'), { encoding: 'utf-8' }).toString(), {
      preventIndent: true,
    });

    // Markdown 模版
    this.markdownTemplate = hbs.compile(fs.readFileSync(join(__dirname, '../../../templates/markdown.pdf.hbs'), { encoding: 'utf-8' }).toString(), {
      preventIndent: true,
    });
  }

  /**
   * 风险等级分组
   * @param levelGroup
   */
  private levelGroupFilter(levelGroup, groupKey: string) {
    return [DueDiligenceResult.serious, DueDiligenceResult.highRisk, DueDiligenceResult.pass].map((level: DueDiligenceResult) => {
      return {
        level,
        groups: levelGroup[level].filter((g) => g.groupKey === groupKey),
      };
    });
  }

  /**
   * 风险维度: 输出 维度 -> 风险等级分组 -> 子维度
   */
  private getRiskDetails(scoreDetails: GroupDimensionScorePO[], levelGroups: LevelGroupDimensionPO) {
    const result = [];

    scoreDetails.forEach((detail) => {
      // if (detail.totalHits > 0) {
      const dimension = {
        name: detail.name,
        totalHits: detail.totalHits,
        groupKey: detail.groupKey,
        levelGroups: this.levelGroupFilter(levelGroups, detail.groupKey),
      };
      // 不输出 `命中为0(空维度)` 或 `levelGroups子集group为空(不含关键项)` 的维度
      // if (dimension.totalHits > 0 || dimension?.levelGroups?.some((g) => g.groups.length > 0)) {
      //   result.push(dimension);
      // }
      result.push(dimension);
    });
    return result;
  }

  private async getDimensionHit(groups: GroupDimensionScorePO[]): Promise<string[]> {
    const result = await Bluebird.map(groups, (group) => {
      return group.scoreDetails.map((d) => {
        if (d?.subDimension?.length) {
          return d?.subDimension.map((sd) => sd.description2);
        } else {
          return d.description2;
        }
      });
    });
    return flattenDeep(result.filter((t) => t));
  }

  async getCompanyInfo(keyno: string) {
    const getResDataByField = (field: string) => (res) => {
      return res ? res[field] : res;
    };
    const takeResResult = getResDataByField('Result');

    const getCompanyInfo = async () => {
      if (isOrganism(keyno)) {
        const { Result = {} } = await this.searchService.organismDetailsQcc(keyno);
        return {
          KeyNo: keyno,
          Oper: Result.DJInfo?.Oper || {},
          MultipleOper: {
            OperType: Result.DJInfo?.Oper?.OperType,
            OperList: Result.DJInfo?.Oper ? [Result.DJInfo?.Oper] : [],
          },
          Address: Result.DJInfo?.address,
          ...Object.entries(Result.DJInfo).reduce((acc, [k, v]) => ({ ...acc, [upperFirst(k)]: v })),
          ...Result,
          CertDate: Result.DJInfo?.certificatePeriod || '',
          ShortStatus: Result.DJInfo?.status || '',
          Type: 1,
          IsOrganism: true, //是社会组织
          ContactNo: Result.contactNo,
        };
      }
      return this.searchService.companyDetailsQcc(keyno, [
        'Address', // 注册地址
        'CreditCode', // 统一社会信用代码
        'No', // 工商注册号
        'OrgNo', // 组织机构代码
        'KeyNo',
        'LatestAnnualReportAddrInfo', // 最新年报信息  LatestAnnualReportAddrInfo.0.Address
        'Name', // 企业名称
        'EnglishName', // 英文名称
        'OriginalName', // 曾用名
        'Oper', // Oper.Name 法定代表人
        'MultipleOper',
        'RecCap', // 实缴资本
        'RegistCapi', // 注册资本
        'ShortStatus',
        'Staffs', // 员工人数 Staffs.c   Staffs.s 年报信息
        'StartDate', // 成立日期
        'CheckDate', // 核准日期
        'TaxNo', // 	纳税人识别号
        'VTList',
        'QccIndustry',
        'TermStart',
        'TeamEnd',
        'StartDate',
        'Scale', // 资产规模
        'ContactInfo',
        'CompanyRevenue', // CompanyRevenue.Revenue 营业收入
        'Industry', // 所属行业 Industry.Industry
        'EconKind', // 企业类型
        'BelongOrg', //等级机关
        'Scope', //经营范围
        'ChangeDiffInfo', // 变更记录
        // 'Partners', // 股东信息
        // 'Employees', // 主要人员
        'TaxpayerType', // 纳税人资质
        'CountInfo', // 计数信息
      ]);
    };
    const companyInfo = await getCompanyInfo();

    // todo 如果是社会组织走独立逻辑
    if (isOrganism(keyno)) {
      // 变更记录 在详情的 ChangeDiffInfo 中有
      // 主要人员
      companyInfo['Employees'] = await this.entLibService.getOrganismPersonPaging(keyno);

      // 控制企业  api/VIP/GetHoldingCompany
      companyInfo['HoldingCompany'] = await this.entLibService.getHoldingCompany(keyno).then(takeResResult);
      return companyInfo;
    }

    const changeInfo = await this.entLibService.getChangeInfo(keyno, 10, 1);
    // 变更记录
    companyInfo['ChangeDiffInfo'] = {
      KeyNo: companyInfo.KeyNo,
      CompanyName: companyInfo.Name,
      ChangeList: changeInfo?.Result || [],
      TotalCount: changeInfo?.Paging?.TotalRecords || 0,
    };

    // 股东信息（最新公示）
    companyInfo['IpoPartners'] = await this.entLibService.getPartner(keyno, 'IpoPartners');
    // 股东信息（工商登记）
    companyInfo['Partners'] = await this.entLibService.getPartner(keyno, 'Partners');
    // 主要人员（最新公示）
    companyInfo['IpoEmployees'] = await this.entLibService.getEmployee(keyno, 'IpoEmployees');
    // 主要人员（工商登记）
    companyInfo['Employees'] = await this.entLibService.getEmployee(keyno, 'Employees');
    // 分支机构 api/ECILocal/GetECIBigNodePagingInfo => /api/QccSearch/List/Branch
    companyInfo['Branches'] = await this.entLibService.getBranchList(keyno);
    // 实际控制人 （疑似） api/Relation/GetSuspectedActualControllerV5
    const controllersRes = await this.entLibService.getActualController(keyno);
    // 转换实际控制人数据: v5数据结构转换
    const { actual, yisiActual } = this.getActualControllerV5(controllersRes);
    const type = this.getActualControllerV5Type(controllersRes, actual, yisiActual);
    companyInfo['ActualController'] = {
      type,
      actual,
      yisiActual,
    };
    // 控制企业  api/VIP/GetHoldingCompany
    companyInfo['HoldingCompany'] = await this.entLibService.getHoldingCompany(keyno).then(takeResResult);

    // 受益人所有人 api/QccDetail/Benefit/Detail
    companyInfo['BeneficialOwner'] = await this.getBenefitList(keyno, true);
    // 受益人自然人 api/QccDetail/Benefit/Detail
    companyInfo['BeneficialNaturalPerson'] = await this.getBenefitList(keyno, false);
    return companyInfo;
  }

  /**
   * 获取表格配置名称
   */
  private getActualControllerV5Type(data, actual, yisiActual) {
    // 疑似实际控制人
    if (Array.isArray(yisiActual) && yisiActual.length > 0) {
      return ActualControllerTypeMaps.SuspectActual;
    }

    // 实际控制人
    const { ActualControl, FinalActualControl } = data || {};
    // 是否上市 - 变更Name的title为: 实际控制人（公示信息）
    const isPublicName = ActualControl && !FinalActualControl;
    // NOTE: 表决权比例
    const hasControlPercent = actual.some(({ Names }) => Names?.ControlPercent);
    // NOTE: 4.needRowspan 表决权比例 / 表决权比例(共同持有)
    // const hasMoreThanOnPartner = actual.some(({ Names }) => Names?.PersonList?.length > 1);
    const hasMoreThanOnPartner = actual?.length > 1 || actual.some(({ Names }) => Names?.PersonList?.length > 1);

    // NOTE: 直接持股比例
    const hasPartnerPercent = actual.some(({ Names }) => Names?.PersonList.some(({ Percent }) => Percent));
    // NOTE: 总持股比例
    let hasPartnerPercentTotal = actual.some(({ Names }) => Names?.PersonList.some(({ PercentTotal }) => PercentTotal));
    if (hasPartnerPercentTotal) {
      // 最终受益股份与直接持股比例相同时，不呈现最终受益股份
      const isAllEqual = actual.every(({ Names }) => Names?.PersonList.every(({ Percent, PercentTotal }) => parseFloat(Percent) === parseFloat(PercentTotal)));
      hasPartnerPercentTotal = !isAllEqual;
    }

    /**
     * 实际控制人
     */
    // 实际控制人, 直接持股比例, **总持股比例**, 表决权比例
    if (hasPartnerPercent && hasControlPercent && hasPartnerPercentTotal) {
      return ActualControllerTypeMaps.Actual2PercentTotal;
    }
    // 实际控制人(公示信息), 直接持股比例, 表决权比例(共同持有)
    if (hasPartnerPercent && hasControlPercent && hasMoreThanOnPartner && isPublicName) {
      return ActualControllerTypeMaps.Actual2TitleAndControlPercent;
    }
    // 实际控制人, 直接持股比例, 表决权比例(共同持有)
    if (hasPartnerPercent && hasControlPercent && hasMoreThanOnPartner) {
      return ActualControllerTypeMaps.Actual2ControlPercent;
    }
    // 实际控制人, 直接持股比例, 表决权比例
    if (hasPartnerPercent && hasControlPercent) {
      return ActualControllerTypeMaps.Actual2;
    }

    // 实际控制人(公示信息), 直接持股比例, 总持股比例';
    if (hasPartnerPercent && hasPartnerPercentTotal && isPublicName) {
      return ActualControllerTypeMaps.Actual4Title;
    }
    // 实际控制人, 直接持股比例, 总持股比例';
    if (hasPartnerPercent && hasPartnerPercentTotal) {
      return ActualControllerTypeMaps.Actual4;
    }
    // 公示信息
    if (hasPartnerPercent && isPublicName) {
      return ActualControllerTypeMaps.Actual6Title;
    }
    // 实际控制人
    if (hasPartnerPercent) {
      return ActualControllerTypeMaps.Actual6;
    }

    /**
     * 表决权比例
     */
    // 5.2. 实际控制人, 总持股比例,	表决权比例(共同持有)
    if (hasControlPercent && hasPartnerPercentTotal && hasMoreThanOnPartner) {
      return ActualControllerTypeMaps.Actual5PercentTotal;
    }
    // 5.1 实际控制人, 总持股比例,	表决权比例
    if (hasControlPercent && hasPartnerPercentTotal) {
      return ActualControllerTypeMaps.Actual5;
    }
    // 1. 实际控制人, 表决权比例
    if (hasControlPercent) {
      return ActualControllerTypeMaps.Actual1;
    }

    /**
     * 总持股比例
     */
    // 4. 实际控制人(公示信息), 总持股比例
    if (hasPartnerPercentTotal && isPublicName) {
      return ActualControllerTypeMaps.Actual3Title;
    }
    if (hasPartnerPercentTotal) {
      return ActualControllerTypeMaps.Actual3;
    }

    return ActualControllerTypeMaps.Actual1;
  }

  /**
   * 实际控制人（V5）
   * http://gitlab.greatld.com:18888/qcc/pc-web/-/blob/1e9944b62d693b3e250043a5d1bfeca94fbf026f/src/components/app-datalist/components/companieslist/handle.js#L90
   * https://gitlab.greatld.com:18888/qcc/pc-web/-/blob/d1b3e8ecc2841be6039dc8caaf2d2c7b29ffa6a0/src/routers/company/detail/sections/base/kzrtupu/component.js#L60
   */
  private getActualControllerV5(data) {
    let actual = [];
    let yisiActual = [];
    const { ActualControl, Names, FinalActualControl, MergedActualControl, NameCount } = data || {};
    if (ActualControl || FinalActualControl) {
      let PersonList;
      let ControlPercent;
      if (FinalActualControl) {
        PersonList = FinalActualControl.PersonList;
        ControlPercent = FinalActualControl.ControlPercent;
      } else if (ActualControl) {
        PersonList = ActualControl.PersonList;
        ControlPercent = ActualControl.ControlPercent;
      }

      if (PersonList?.length > 0) {
        const ControlPercentDesc = Number(ControlPercent) > 0 ? ControlPercent + '%' : '';
        const realName = [];
        forEach(PersonList, (item) => {
          realName.push({
            Name: item.Name,
            KeyNo: item.KeyNo,
            Org: item.Org,
            Tags: item.Tags || [],
          });
        });
        forEach(PersonList, (item, index: number) => {
          if (index === 0) {
            item.mutiActual = true;
            item.realNames = realName;
            item.ControlPercent = ControlPercentDesc;
            const obj = {
              mutiActual: true,
              Name: realName,
              lineType: 'dashed',
              lineTextSuffix: '(表决权)',
              percent: item.ControlPercent,
              ControlPercentDesc,
            };
            item.RealPaths = [[[obj]]];
          }
          actual.push({
            ...item,
            Names: {
              ControlPercent: ControlPercentDesc,
              PersonList: [item],
            },
          });
        });
      }
    } else if (Names?.length) {
      forEach(Names, (item) => {
        if (Number(item.IsActual)) {
          actual.push(item);
        } else {
          yisiActual.push(item);
        }
      });
      actual = this.handleRealPaths(actual);
      // 如果NameCount大于1，就处理数据
      if (NameCount > 1 && !yisiActual.length) {
        const pathList = [];
        forEach(actual, (item) => {
          const obj = {
            ...item,
            ...item?.Names?.PersonList?.[0],
            ControlPercent: item?.Names?.ControlPercent,
          };
          const RealPaths = [];
          forEach(item.Paths, (path) => {
            forEach(path, (sub, index: number) => {
              const { lineType, lineTextPrefix, lineTextSuffix, percent } = this.handleLinkForKzr(sub) || {};
              sub.lineType = lineType;
              sub.lineTextPrefix = lineTextPrefix;
              sub.lineTextSuffix = lineTextSuffix;
              sub.percent = percent;
              if (index === path.length - 1) {
                if (index >= 1 && path?.[index - 1].IsPublic) {
                  sub.lineType = 'dashed';
                }
              }
            });
            RealPaths.push([path]);
          });

          obj.Paths = RealPaths;
          pathList.push(obj);
        });
        actual = [
          {
            Names: MergedActualControl,
            isShowMoreInfo: true,
            pathList,
          },
        ];
      }

      yisiActual = this.handleRealPaths(yisiActual);
    }

    // 疑似实际控制人数据处理
    if (yisiActual?.length) {
      yisiActual = map(yisiActual, (item) => {
        return {
          ControlPercent: item.Names.ControlPercent,
          ...item,
          ...item.Names?.PersonList?.[0],
        };
      });
    }

    // 实际控制人数据处理: 补齐关键字段(ControlPercent, Name)
    if (actual.length) {
      // 如果是 `表决权比例(共同持有)` item.Names.PersonList 会存在多个实控人，需要将数据转换为多个打平的数据
      actual = actual.reduce((acc, item) => {
        if (item?.Names?.PersonList?.length) {
          const personList = item.Names.PersonList.map((person) => {
            return {
              ControlPercent: item.Names.ControlPercent,
              ...item,
              ...person,
            };
          });
          return [...acc, ...personList];
        }
        return acc;
      }, []);
    }

    return {
      actual,
      yisiActual,
    };
  }

  private handleRealPaths(list) {
    const actual = cloneDeep(list);
    forEach(actual, (item) => {
      const CollapsedPaths = cloneDeep(item.CollapsedPaths);
      const RealPaths = [];
      forEach(CollapsedPaths, (path) => {
        const { Paths, Children } = path;
        const arr = [Paths, ...Children];
        forEach(arr, (subarr) => {
          forEach(subarr, (sub, index: number) => {
            // const { lineType, lineTextPrefix, lineTextSuffix, percent } = kzrHelper.handleLinkForKzr(sub) || {};
            const { lineType, lineTextPrefix, lineTextSuffix, percent } = this.handleLinkForKzr(sub) || {};
            sub.lineType = lineType;
            sub.lineTextPrefix = lineTextPrefix;
            sub.lineTextSuffix = lineTextSuffix;
            sub.percent = percent;
            if (index === subarr.length - 1) {
              if (index >= 1 && subarr?.[index - 1].IsPublic) {
                sub.lineType = 'dashed';
              }
            }
          });
        });
        RealPaths.push(arr);
      });
      item.RealPaths = RealPaths;
    });
    return actual;
  }

  private handleLinkForKzr(itemData): { lineType: string; percent: string; lineTextPrefix?: string; lineTextSuffix?: string } {
    const getPercent = ({ itemData, isSpecial }) => {
      if (!isSpecial) {
        return +(itemData.Percent || '').split('%')[0] ? itemData.Percent : '';
      }
      if (itemData.ControlPercent) {
        return itemData.ControlPercent + '%';
      }
      if (itemData.ControlRatio) {
        return itemData.ControlRatio + '%';
      }
      if (+itemData.DataType === 5) {
        return '100%';
      }
      return '';
    };
    if (+itemData.DataType === 5) {
      itemData.ControllerType = 6;
    }
    let returnData = {
      type: -1,
      typeDesc: '默认',
      lineType: 'solid',
      lineText: itemData.Percent,
      percent: itemData.Percent,
    };
    let target = null;
    if ([1, 8].indexOf(itemData.ControllerType) > -1 && itemData.ShareHoldType >= 0) {
      target = find(shareHoldTypeMap, (stype) => stype.type === itemData.ShareHoldType);
    } else if (itemData.ControllerType >= 0) {
      target = find(controlTypeMap, (ctype) => ctype.type === itemData.ControllerType);
    }
    if (target) {
      target.percent = getPercent({
        itemData,
        isSpecial: target.isSpecial,
      });
      if (itemData.ControllerType === 5) {
        target.lineText = '';
        target.percent = '';
      } else {
        target.lineText = (target.lineTextPrefix || '') + (target.percent || '');
        if (target.lineText.indexOf('%') < 0 && target.percent) {
          target.lineText += '%';
        }
        target.lineText += target.lineTextSuffix || '';
        if (target.tips && target.tips.indexOf('xxx') > -1) {
          target.tips = itemData.OriginKeyNo ? replace(target.tips, 'xxx', itemData.OriginName) : '';
        } else if (!target.tips) {
          target.tips = '';
        }
      }
      returnData = target;
    }
    return returnData;
  }

  private async getDiligenceDate(orgId: number, diligenceId: number) {
    const dbDiligence = await this.diligenceHistoryRepo.findOne({ id: diligenceId, orgId });
    if (!dbDiligence?.snapshotId) {
      throw new BadParamsException(RoverExceptions.BadParams.NotFound);
    }

    const resultRemark = await this.diligenceRemarkRepo
      .createQueryBuilder('remark')
      .leftJoinAndSelect('remark.editor', 'editor')
      .select(['remark', 'editor.name'])
      .where('remark.diligenceId = :diligenceId', { diligenceId })
      .andWhere('editor.orgId = :orgId', { orgId })
      .orderBy('remark.updateDate', 'DESC')
      .getOne();

    const companyInfo = await this.getCompanyInfo(dbDiligence.companyId);
    const riskInfo = dbDiligence.details;
    const riskDetails = this.getRiskDetails(
      riskInfo.dimensionScoreDetails.sort((a, b) => a.sort - b.sort),
      riskInfo.levelGroup,
    );

    const riskDimensions = await this.snapshotHelper.getDiligenceDetails(dbDiligence.snapshotId, riskInfo.dimensionHits);

    const riskReview = {
      level: riskInfo.result,
      levelGroups: {
        // 高风险（警示）
        2: await this.getDimensionHit(riskInfo.levelGroup[2]),
        // 中风险（关注）
        1: await this.getDimensionHit(riskInfo.levelGroup[1]),
      },
    };

    return {
      dbDiligence,
      companyInfo,
      riskInfo,
      riskReview,
      riskDetails,
      riskDimensions,
      resultRemark,
    };
  }

  async generatePdfView(orgId: number, diligenceId: number) {
    // 恒大地产集团有限公司  diligenceId =  13816  snapshotId: "097ca220-c3a0-11ed-8d1e-db7942b7c9b2"
    const { dbDiligence, companyInfo, riskInfo, riskReview, riskDetails, riskDimensions, resultRemark } = await this.getDiligenceDate(orgId, diligenceId);

    return this.pdfTemplate({
      orgId,
      diligenceInfo: dbDiligence,
      companyInfo,
      riskInfo,
      riskReview,
      riskDetails,
      riskDimensions,
      resultRemark,
    });
  }

  private async htmlToMarkdown(html: string) {
    const markdown = NodeHtmlMarkdown.translate(html);
    return markdown;
  }

  async generateMarkdownView(orgId: number, diligenceId: number) {
    // 恒大地产集团有限公司  diligenceId =  13816  snapshotId: "097ca220-c3a0-11ed-8d1e-db7942b7c9b2"
    const {
      dbDiligence,
      companyInfo,
      riskInfo,
      riskReview,
      riskDimensions: originRiskDimensions,
      resultRemark,
      riskDetails,
    } = await this.getDiligenceDate(orgId, diligenceId);

    // 过滤不必要的维度详情
    // riskDetails.forEach(({ levelGroups }) => {
    //   levelGroups.forEach(({ groups }) => {
    //     groups.forEach((group) => {
    //       const availableKeys = [];
    //       if (!availableKeys.includes(group?.groupKey)) {
    //         delete group.scoreDetails;
    //       }
    //     });
    //   });
    // });
    const riskDimensions: typeof originRiskDimensions = {};

    entries(originRiskDimensions).forEach(([key, result]: [string, any]) => {
      const keepFields: string[] = [
        DimensionLevel2Enums.CustomerSuspectedRelation, // 与第三方列表企业存在潜在关联/利益关联方
        DimensionLevel2Enums.CustomerPartnerInvestigation, // 与第三方列表企业存在投资任职关联
        DimensionLevel3Enums.SuspectedInterestConflict, // 潜在利益冲突
        DimensionLevel2Enums.HitInnerBlackList, // 被列入内部黑名单
        DimensionLevel2Enums.CompanyShell, // 疑似空壳企业
      ];
      if (keepFields.includes(key)) {
        if (DimensionLevel2Enums.CompanyShell === key) {
          result?.data.forEach((data) => {
            data?.labelHasDetail.forEach((item) => {
              item.hasDetail = false;
            });
            data?.labelHasDetail.forEach((item) => {
              item.hasDetail = false;
            });
          });
          riskDimensions[key] = result;
        } else {
          riskDimensions[key] = result;
        }
      } else {
        riskDimensions[key] = Object.assign({}, result, {
          data: [],
        });
      }
    });

    const html = this.markdownTemplate({
      orgId,
      diligenceInfo: dbDiligence,
      companyInfo,
      riskInfo,
      riskReview,
      riskDetails,
      riskDimensions,
      resultRemark,
    });
    return this.htmlToMarkdown(html);
  }

  async generatePdf(orgId: number, diligenceId: number, fileName?: string) {
    try {
      const { dbDiligence, companyInfo, riskInfo, riskReview, riskDetails, riskDimensions, resultRemark } = await this.getDiligenceDate(orgId, diligenceId);

      const cover = await this.pdfCoverTemplate({
        title: '风险排查报告',
        reportName: companyInfo.Name,
        createDate: dbDiligence.createDate,
        pdfType: 'diligence',
      });

      const content = this.pdfTemplate({
        orgId,
        diligenceInfo: dbDiligence,
        companyInfo,
        riskInfo,
        riskReview,
        riskDetails,
        riskDimensions,
        resultRemark,
      });

      // PDF 页头
      const defaultStyles = {
        wrapper: `width: calc(100%); margin: -12px 0 0 0; padding: 0 30px;`,
        content: `font-size: 13px; padding: 8px 0 10px 0; line-height: 22px; display: flex; align-items: center; justify-content: space-between; font-family: 'Microsoft YaHei', 'Noto Sans SC', Arial, sans-serif;`,
      };
      const headerTemplate = `
        <div style="${defaultStyles.wrapper}">
          <div style="${defaultStyles.content} border-bottom: 1px solid #eee;">
            <img style="width: 234px; height: 22px;" src="${PDF_REPORT_PAGE_HEADER_LOGO}"/>
            <span>联系电话：400-088-8275</span>
          </div>
        </div>
      `;
      // PDF 页脚
      const footerTemplate = `
        <div style="${defaultStyles.wrapper}">
          <div style="${defaultStyles.content}">
            <span>企查查科技股份有限公司</span>
            <span>
              <span class="pageNumber"></span>
              <span> / </span>
              <span class="totalPages"></span>
            </span>
          </div>
        </div>
      `;

      const time = uuidTime.v1(dbDiligence.snapshotId);
      const displayFileName = `风险排查报告-${dbDiligence.name}${moment(time).format('YYYYMMDDHHmmssSSS')}`;
      const requestParams: AxiosRequestConfig = {
        method: 'POST',
        url: `${this.configService.kzzServer.pdfService}/generate`,
        data: {
          product: 'rover',
          title: displayFileName,
          content: Buffer.from(content).toString('base64'),
          coverContent: Buffer.from(cover).toString('base64'),
          id: dbDiligence.snapshotId + '-' + moment(dbDiligence.updateDate).unix(),
          output: 0,
          remoteContent: 0,
          forceUpdate: 0,
          pdfOptions: {
            format: 'A4',
            printBackground: true,
            displayHeaderFooter: true,
            outline: true,
            scale: 1,
            margin: {
              top: '80px',
              bottom: '80px',
              left: '0',
              right: '0',
            },
            headerTemplate,
            footerTemplate,
          },
          // aclGrade: 'private',
          needPreView: true,
        },
        headers: {
          'x-kzz-request-id': uuidv4(),
        },
        timeout: 300000,
      };
      const result = await this.httpUtils.sendResquest(requestParams);
      return { fileUrl: result.filePath, fileName: `风险排查报告-${dbDiligence.name}`, previewUrl: result.previewPath };
    } catch (error) {
      this.logger.error(`generatePdf error diligenceId: ${diligenceId}, error: ${error} `);
      this.logger.error(error);
      throw error;
    }
  }

  /**
   * 获取最终受益人，受益自然人
   * @param keyNo
   * @param isBenefit 是否为最终受益人 true=最终受益人（受益所有人） false=受益自然人，默认为 true
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getBenefitList(keyNo: string, isBenefit = true): Promise<{ Names: Benefit[]; NamesCount: number }> {
    try {
      const res = await this.companyDetailService.getBenefitDetail({
        keyNo,
        isBenefit,
      });
      return {
        Names: res?.Result?.Names ?? [],
        NamesCount: res?.Result?.NameCount ?? 0,
      };
    } catch (error) {
      this.logger.error('getBenefitList error:', error);
      return {
        Names: [],
        NamesCount: 0,
      };
    }
  }
}
