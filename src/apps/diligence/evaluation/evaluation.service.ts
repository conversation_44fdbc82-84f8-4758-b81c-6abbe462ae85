/* eslint-disable @typescript-eslint/naming-convention */
import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { RiskSocreService } from './risk.score.service';
import { PaginationResponse, RoverUser } from 'libs/model/common';
import { MoreThanOrEqual, Not, Repository } from 'typeorm';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { DiligenceSnapshotHelper } from '../snapshot/diligence.snapshot.helper';
import { DiligenceResponseV2 } from 'libs/model/diligence/pojo/req&res/DiligenceResponseV2';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DATE_TIME_FORMAT, ForbiddenStandardCode, ZEISSDimensions } from 'libs/constants/common';
import { GetDiligenceResultParams } from 'libs/model/diligence/pojo/req&res/GetDiligenceResultParams';
import { ModelScorePO } from 'libs/model/diligence/pojo/model/ModelScorePO';
import { DiligenceSnapshotService } from '../snapshot/diligence.snapshot.service';
import { ICounter, RoverBundleCounterType, RoverBundleEntityConfig, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { AccessResourceDeniedException } from '../../../libs/exceptions/AccessResourceDeniedException';
import * as moment from 'moment';
import { QueueService } from '../../../libs/config/queue.service';
import { DiligenceHistoryCacheHelper } from '../../basic/diligence.history.cache.helper';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { intersection, xor } from 'lodash';
import { SettingsService } from '../../settings/settings.service';
import { getBundleStart, getExceptionDescription } from '../../../libs/utils/diligence/diligence.utils';
import { getDefaultDimensionGroupDefinition } from '../../../libs/constants/dimension.constants';
import { CompanySearchService } from '../../company/company-search.service';
import { OrgSettingsLogEntity } from '../../../libs/entities/OrgSettingsLogEntity';
import { UserConfigurationTypeEnums } from '../../../libs/model/settings/UserConfigurationTypeEnums';
import { SettingTypeEnums } from '../../../libs/model/settings/SettingTypeEnums';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { CustomerEntity } from '../../../libs/entities/CustomerEntity';
import { DiligenceSnapshotEsService } from '../snapshot/diligence.snapshot.es.service';
import { UpdateDiligenceHitsPO } from '../../../libs/model/diligence/pojo/diligence/UpdateDiligenceHitsPO';
import * as Bluebird from 'bluebird';
import { MarkPersonModel } from '../../../libs/model/person/MarkPersonModel';
import { generateSnapshotId, getDimensionRecordIdForSnapshot } from '../snapshot/utils.snapshot';
import { RelatedPersonResponse } from '../../../libs/model/diligence/pojo/req&res/RelatedPersonResponse';
import { SnapshotStatus } from '../../../libs/model/diligence/pojo/model/SnapshotDetail';
import { DimensionScorePO } from '../../../libs/model/diligence/pojo/dimension/DimensionScorePO';
import { reCalculateScore, updateDimensionHits } from './evaluation.utils';
import { DiligenceAnalyzeService } from '../analyze/diligence.analyze.service';
import { UserConfigurationService } from '../../user_configuration/user.configuration.service';
import Redlock from 'redlock';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { InnerBlacklistEntity } from '../../../libs/entities/InnerBlacklistEntity';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { RoverGraphService } from '../../data/source/rover.graph.service';

@Injectable()
export class EvaluationService {
  public snapshotQueue: RabbitMQ;
  private logger: Logger = QccLogger.getLogger(EvaluationService.name);
  private DimensionNameMap: Map<string, string> = new Map<string, string>();
  protected redlock: Redlock;

  constructor(
    private readonly modelRiskService: RiskSocreService,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
    @InjectRepository(InnerBlacklistEntity) private readonly innerBlacklistRepo: Repository<InnerBlacklistEntity>,
    private readonly snapshotHelper: DiligenceSnapshotHelper,
    private readonly snapshotService: DiligenceSnapshotService,
    public readonly settingService: SettingsService,
    private readonly bundleService: RoverBundleService,
    private readonly queueService: QueueService,
    private readonly diligenceHistoryHelper: DiligenceHistoryCacheHelper,
    private readonly companySearchService: CompanySearchService,
    private readonly diligenceAnalyzeService: DiligenceAnalyzeService,
    private readonly snapshotEsService: DiligenceSnapshotEsService,
    private readonly userConfigurationService: UserConfigurationService,
    private readonly roverGraphService: RoverGraphService,
    protected readonly redisService: RedisService,
  ) {
    this.redlock = new Redlock([this.redisService.getClient()], {
      retryCount: 2, // retry only 2 times then fails
    });
    this.snapshotQueue = this.queueService.snapshotQueue;
    //useAdapter(redisService.getClient());
    this.bundleService.setSyncFunctions({
      // [RoverBundleCounterType.DiligenceCompanyQuantity]: { fn: this.syncDiligenceCompanyCount.bind(this) },
      // [RoverBundleCounterType.DiligenceHistoryQuantity]: { fn: this.syncDiligenceCount.bind(this) },
      [RoverBundleLimitationType.DiligenceDailyQuantity]: { fn: this.syncDiligenceDaily.bind(this) },
    });
    // 维度名称会更改，统一用当前setting的维度名称
    const dimensionDefinition = getDefaultDimensionGroupDefinition('v1');
    Object.keys(dimensionDefinition).forEach((key) => {
      if (key == 'version') {
        return;
      }
      const group = dimensionDefinition[key];
      if (this.DimensionNameMap.has(key)) {
        throw new InternalServerErrorException(`duplicated key ${key}`);
      }
      this.DimensionNameMap.set(key, group.name);
      group.items?.forEach((item) => {
        if (this.DimensionNameMap.has(item.key)) {
          throw new InternalServerErrorException(`duplicated key ${item.key}`);
        }
        this.DimensionNameMap.set(item.key, item.name);
        item.subDimensionList?.forEach((sub) => {
          if (this.DimensionNameMap.has(sub.key)) {
            throw new InternalServerErrorException(`duplicated key ${sub.key}`);
          }
          this.DimensionNameMap.set(sub.key, sub.name);
          sub.subDimensionList?.forEach((ssub) => {
            if (this.DimensionNameMap.has(ssub.key)) {
              throw new InternalServerErrorException(`duplicated key ${ssub.key}`);
            }
            this.DimensionNameMap.set(ssub.key, ssub.name);
          });
        });
      });
    });
  }

  async getDiligenceById(currentUser: RoverUser, diligenceId: number) {
    const dbDiligence = (await this.diligenceHistoryRepo.findByIds([diligenceId]))?.[0] || null;
    if (!dbDiligence || dbDiligence.orgId !== currentUser.currentOrg) {
      throw new AccessResourceDeniedException();
    }
    return dbDiligence;
  }

  /**
   * 存储最近一次更改了外部条件(影响尽调结果的条件)的时间
   *
   * @param orgId
   */
  async makeDiligenceForUpdate(currentUser: RoverUser, companyId?: string) {
    const { currentOrg: orgId } = currentUser;
    const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    if (userBundle[RoverBundleCounterType.DiligenceHistoryQuantity].value !== -1) {
      // 说明开启了 按排查次数数量 计费模式,
      const historyCounter = await this.bundleService.getOrgBundleCounter(currentUser, RoverBundleCounterType.DiligenceHistoryQuantity);
      await historyCounter.increase(1);
    }

    // 每日尽调上限校验
    const dailyCounter = await this.bundleService.getOrgLimitationCounter(currentUser, RoverBundleLimitationType.DiligenceDailyQuantity);
    await dailyCounter.increase(1);

    if (orgId && companyId) {
      // 只需要找出指定公司最新的一条尽调记录，把shouldUpdate 修改成1 ，确保下次不使用他做为缓存即可
      const dbDiligence = await this.diligenceHistoryRepo.findOne(
        {
          orgId,
          companyId,
        },
        { order: { createDate: 'DESC' } },
      );
      if (dbDiligence) {
        return this.diligenceHistoryRepo.update(dbDiligence.id, { shouldUpdate: 1 });
      }
    } else if (orgId) {
      return this.diligenceHistoryRepo.update({ orgId }, { shouldUpdate: 1 });
    }
  }

  @TraceLog({ throwError: true })
  async getRiskListV2(currentUser: RoverUser, params: GetDiligenceResultParams): Promise<DiligenceResponseV2> {
    const { currentOrg: orgId, userId, departments, bundle } = currentUser;
    const companyInfo = await this.companySearchService.companyDetailsQcc(params.companyId);
    //判断是否港澳台海外企业allowType = ['0', '1', '11', '12']
    // if (!['0', '1', '11', '12'].includes(String(companyInfo.Type))) {
    //   throw new BadParamsException(RoverExceptions.Diligence.Common.Nonsupport);
    // }
    if ((companyInfo?.standardCode?.length && intersection(companyInfo.standardCode, ForbiddenStandardCode).length) || !companyInfo?.standardCode?.length) {
      throw new BadParamsException(RoverExceptions.Diligence.Common.Nonsupport);
    }
    params.companyName = companyInfo.Name;
    const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    const { companyId, companyName, diligenceId, settingId, ambiguousSettingId } = params;
    // 用户默认模版
    let userConfigVersion;
    let orgSetting: OrgSettingsLogEntity;
    if (ambiguousSettingId) {
      // 仅提取版本号
      const s = await this.settingService.getOrgSettings(orgId, SettingTypeEnums.diligence_risk, ambiguousSettingId);
      userConfigVersion = s.version;
    } else {
      userConfigVersion = (await this.userConfigurationService.getUserConfiguration(currentUser, UserConfigurationTypeEnums.diligence_default_setting))
        ?.settingVersion;
    }
    if (!orgSetting) {
      orgSetting = await this.settingService.getOrgSettings(orgId, SettingTypeEnums.diligence_risk, settingId, userConfigVersion);
    }
    let diligenceRes: DiligenceResponseV2 = new DiligenceResponseV2();
    Object.assign(diligenceRes, { realtime: false, notMatch: false });
    let forceDD;
    let dbDiligence: DiligenceHistoryEntity = null;
    if (diligenceId) {
      dbDiligence = await this.diligenceHistoryRepo.findOne(diligenceId, { relations: ['editor'] });
      if (dbDiligence?.orgId !== orgId) {
        throw new AccessResourceDeniedException();
      }
      if (dbDiligence.snapshotDetails.status !== SnapshotStatus.SUCCESS) {
        //兜底策略，如果快照还在处理中，则判断 diligenceRes.snapshotDetails.successHits和 diligenceRes.dimensionHits的数组内容是否有差集，如果无差集，则说明快照处理完成，更新快照状态为成功
        const xorArr = xor(dbDiligence.snapshotDetails.successHits, dbDiligence.details.dimensionHits);
        this.logger.info(
          `snapshotId=${dbDiligence.snapshotId} successHits=${dbDiligence.snapshotDetails.successHits} dimensionHits=${dbDiligence.details.dimensionHits} xorArr=${xorArr}`,
        );
        if (xorArr.length === 0) {
          await this.snapshotService.updateSnapshotIfSuccess(dbDiligence.snapshotId);
        }
      }
    } else {
      const bundleStartDate = getBundleStart(bundle.startDate);
      // 查找套餐开始时间之后后是否存在该公司的排查记录
      dbDiligence = await this.diligenceHistoryRepo.findOne(
        {
          orgId,
          companyId,
          operator: Not(-1),
          createDate: MoreThanOrEqual(bundleStartDate),
        },
        { order: { createDate: 'DESC' } },
      );
    }
    diligenceRes.notMatch = dbDiligence?.companyId !== companyId;
    if (dbDiligence && dbDiligence.companyId === companyId) {
      if (diligenceId) {
        forceDD = false;
      } else {
        // 是否发生影响排查的变更需要重新排查
        forceDD = await this.diligenceHistoryHelper.shouldNotUseCache(dbDiligence, orgSetting.id);
      }
    } else {
      forceDD = true;
    }
    if (forceDD) {
      // 校验第三方列表及内部黑名单列表交叉重叠排查范围
      await this.checkRelationsRange(orgId, orgSetting, companyId, userId);
      const lockKey = `getRiskListV2:${orgId}:settingId:${orgSetting.id}:keyNo:${params.companyId}`;
      let redlock;
      try {
        // 同一时间是否已有相同请求在处理
        redlock = await this.redlock.acquire([lockKey], 10 * 1000);
        const { result } = await this.doDiligenceForce(currentUser, userBundle, diligenceRes, orgSetting, companyId, companyName, dbDiligence, departments);
        dbDiligence = result.dbDiligence;
        diligenceRes = result.diligenceRes;
      } catch (e) {
        if (e instanceof BadRequestException) {
          throw e;
        }
        this.logger.info('Redlock acquire failed', { error: e, lockKey });
        throw new BadRequestException(RoverExceptions.Diligence.Detail.ApplyDuplicated);
      } finally {
        if (redlock) {
          try {
            await redlock.release();
          } catch (e2) {}
        }
      }
      // let companyCounter: ICounter = null;
      // let historyCounter: ICounter = null;
      // let dailyCounter: ICounter = null;
      // if (userBundle[RoverBundleCounterType.DiligenceCompanyQuantity].value !== -1) {
      //   // 说明开启了 按排查公司数量 计费模式,
      //   if (!dbDiligence) {
      //     //套餐有效期内无排查记录，标记需要计费，扣减套餐额度
      //     companyCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceCompanyQuantity);
      //     // await companyCounter.clear();
      //     await companyCounter.increase(1);
      //     paid = true;
      //   }
      // }
      //
      // if (userBundle[RoverBundleCounterType.DiligenceHistoryQuantity].value !== -1) {
      //   // 说明开启了 按排查次数数量 计费模式,
      //   historyCounter = await this.bundleService.getOrgBundleCounter(currentUser, RoverBundleCounterType.DiligenceHistoryQuantity);
      //   await historyCounter.increase(1);
      //   if (userBundle[RoverBundleCounterType.DiligenceCompanyQuantity].value == -1) {
      //     paid = true;
      //   }
      // }
      //
      // // 每日尽调上限校验
      // dailyCounter = await this.bundleService.getOrgLimitationCounter(currentUser, RoverBundleLimitationType.DiligenceDailyQuantity);
      // await dailyCounter.increase(1);
      //
      // diligenceRes.realtime = forceDD;
      // try {
      //   const modelScore = await this.modelRiskService.getScore(orgId, userId, companyId, companyName, orgSetting);
      //   const snapshotId = generateSnapshotId();
      //   const diligenceHis = Object.assign(new DiligenceHistoryEntity(), {
      //     orgId,
      //     depId: departments?.[0],
      //     operator: userId,
      //     name: companyName,
      //     companyId,
      //     score: modelScore.totalScore, // 评价分数
      //     result: modelScore.result, // 尽调结果
      //     details: modelScore,
      //     snapshotDate: new Date(),
      //     snapshotId: snapshotId, // 生成快照ID，用于保存维度详情列表
      //     orgSettingsId: orgSetting.id,
      //     creditRate: modelScore.creditRateResult?.Score,
      //   });
      //   dbDiligence = await this.diligenceHistoryRepo.save(diligenceHis);
      //   // 对排查结果生成快照
      //   await this.snapshotService.prepareSnapshot(snapshotId, dbDiligence);
      // } catch (e) {
      //   if (companyCounter) {
      //     // 退回 公司数量额度
      //     await companyCounter.decrease(1);
      //   }
      //   if (historyCounter) {
      //     // 退回 排查记录数量额度
      //     await historyCounter.decrease(1);
      //   }
      //   if (dailyCounter) {
      //     // 退回 每日尽调上限校验 额度
      //     await dailyCounter?.decrease(1);
      //   }
      //   throw e;
      // } finally {
      //   if (redlock) {
      //     await redlock.release();
      //   }
      // }
    }
    Object.assign(diligenceRes, dbDiligence);
    this.postUpdateDiligence(diligenceRes);
    return diligenceRes;
  }

  @TraceLog({ throwError: true })
  /**
   * 批量排查&巡检 执行排查动作,  批量排查&巡检的模型设置会在 batch.batchInfo中
   */
  async getRiskListForBatchJob(currentUser: RoverUser, params: GetDiligenceResultParams, batchId: number, orgSettingId?: number): Promise<DiligenceResponseV2> {
    const { currentOrg: orgId, userId, bundle } = currentUser;
    const { companyId, companyName, diligenceId } = params;
    // const batch = await this.batchRepo.findOne(batchId);
    // const settingId = batch.batchInfo?.settingId;
    // let settingVersion;
    // if (batch.businessType == BatchBusinessTypeEnums.Diligence_Customer_Analyze) {
    //   //  巡检的setting需要看巡检方案的配置
    //   const batchInfo = batch.batchInfo;
    //   const orgConfig = batchInfo?.orgConfig;
    //   if (orgConfig?.configs?.length > 1) {
    //     const customer = await this.customerRepo.findOne({ orgId, companyId });
    //     settingVersion = orgConfig?.configs?.find((x) => x.groupIds?.includes(customer.groupId))?.settingVersion;
    //   } else {
    //     settingVersion = orgConfig?.configs?.[0]?.settingVersion;
    //   }
    // }
    const orgSetting: OrgSettingsLogEntity = await this.settingService.getOrgSettings(orgId, SettingTypeEnums.diligence_risk, orgSettingId);
    this.logger.info(`batch diligence batchId=${batchId} ,settingId=${orgSetting.id} settingVersion=${orgSetting.version} useSettingId=${orgSetting.id}`);

    let paid = false;
    const diligenceRes: DiligenceResponseV2 = new DiligenceResponseV2();
    Object.assign(diligenceRes, { realtime: false, notMatch: false });
    let forceDD;
    let dbDiligence: DiligenceHistoryEntity = null;
    if (diligenceId) {
      dbDiligence = await this.diligenceHistoryRepo.findOne(diligenceId, { relations: ['editor'] });
      if (dbDiligence?.orgId !== orgId) {
        throw new AccessResourceDeniedException();
      }
    } else {
      const bundleStartDate = getBundleStart(bundle?.startDate);
      dbDiligence = await this.diligenceHistoryRepo.findOne(
        {
          orgId,
          companyId,
          operator: Not(-1),
          createDate: MoreThanOrEqual(bundleStartDate),
        },
        { order: { createDate: 'DESC' } },
      );
    }
    diligenceRes.notMatch = dbDiligence?.companyId !== companyId;
    if (dbDiligence && dbDiligence.companyId === companyId) {
      if (diligenceId) {
        forceDD = false;
      } else {
        // 是否发生影响排查的变更需要重新排查
        forceDD = await this.diligenceHistoryHelper.shouldNotUseCache(dbDiligence, orgSetting.id);
      }
    } else {
      forceDD = true;
    }
    if (forceDD) {
      if (!dbDiligence) {
        //套餐有效期内无排查记录，标记需要计费
        paid = true;
      }

      diligenceRes.realtime = forceDD;
      try {
        const modelScore = await this.modelRiskService.getScore(orgId, userId, companyId, companyName, orgSetting);
        const snapshotId = generateSnapshotId();
        const diligenceHis = Object.assign(new DiligenceHistoryEntity(), {
          orgId,
          depId: currentUser.departments?.[0],
          operator: userId,
          name: companyName,
          companyId,
          score: modelScore.totalScore, // 评价分数
          result: modelScore.result, // 尽调结果
          details: modelScore,
          creditRate: modelScore.creditRateResult?.Score,
          snapshotDate: new Date(),
          snapshotId: snapshotId, // 生成快照ID，用于保存维度详情列表
          orgSettingsId: orgSetting.id,
        });
        if (modelScore.creditRateResult) {
          diligenceHis.creditRate = modelScore.creditRateResult.Score;
        }
        dbDiligence = await this.diligenceHistoryRepo.save(diligenceHis);
        // 对排查结果生成快照
        await this.snapshotService.prepareBatchSnapshot(snapshotId, batchId);
      } catch (e) {
        this.logger.error('getRiskListForBatchJob error', e);
        throw e;
      }
    }
    Object.assign(diligenceRes, dbDiligence);
    diligenceRes.paid = paid;
    return diligenceRes;
  }

  async updateSpecificDiligenceV3(currentUser: RoverUser, markPerson: MarkPersonModel) {
    //判断该风险扫描的快照是否已经生成完，快照尚未生成时不让进行核实
    const dbDiligence: DiligenceHistoryEntity = await this.diligenceHistoryRepo.findOne(markPerson.diligenceId);
    if (xor(dbDiligence.snapshotDetails.successHits, dbDiligence.details.dimensionHits).length) {
      throw new BadRequestException(RoverExceptions.Diligence.Snapshot.NotGenerated);
    }
    markPerson.snapshotId = dbDiligence.snapshotId;
    const { personId, key: dimensionKey, keyNo, diligenceId } = markPerson;
    const changes: UpdateDiligenceHitsPO[] = [];
    const { orgId, companyId, snapshotId, details } = dbDiligence;
    const orgSetting = await this.settingService.getOrgSettings(orgId, SettingTypeEnums.diligence_risk, dbDiligence.orgSettingsId);
    //获取当前要更新的记录，如果在另一个被处罚员工分组中也有该人员记录需要同时同步该人员核实记录
    const change1 = await this.getUpdateDiligenceHistByDimension(
      currentUser,
      markPerson,
      dbDiligence.details.originalHits.find((f) => f.key === dimensionKey),
    );
    change1 ? changes.push(change1) : null;
    if (ZEISSDimensions.find((f) => f === markPerson.key)) {
      this.logger.info(`updateSpecificDiligenceZEISS: ${dimensionKey}, ${personId}, ${diligenceId}`);
      //获取另一个维度被处罚员工分组的记录
      const dimensionKey2 = ZEISSDimensions.filter((f) => f !== dimensionKey)[0];
      if (dbDiligence.details.dimensionHits.find((d) => d == dimensionKey2)) {
        //如果存在，则需要核实
        const change2 = await this.getUpdateDiligenceHistByDimension(
          currentUser,
          { ...markPerson, key: dimensionKey2 },
          dbDiligence.details.originalHits.find((f) => f.key === dimensionKey2),
        );
        change2 ? changes.push(change2) : null;
      }
    } else {
      this.logger.info(`updateSpecificDiligenceSAAS: ${dimensionKey}, ${personId}, ${diligenceId}`);
      if (markPerson.status === 1) {
        //当核实为本人时获取潜在利益冲突分组的记录
        const dimensionKey2 = DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment;
        const change2 = Object.assign(new UpdateDiligenceHitsPO(), {
          dimensionKey: dimensionKey2,
          hitsCountChanged: 1,
        });
        changes.push(change2);
      }
    }
    //修改维度的hits
    this.logger.info(`updateSpecificDiligenceSAAS orgId:${orgId},diligenceId:${diligenceId},changes: ${JSON.stringify(changes)}`);
    updateDimensionHits(details, changes, orgSetting);
    //计算最终分数
    const updatedScorePO: ModelScorePO = reCalculateScore(details, orgSetting);
    const partialUpdate = {
      result: updatedScorePO.result,
      details: updatedScorePO,
      snapshotDetails: { status: SnapshotStatus.SUCCESS, successHits: updatedScorePO.dimensionHits },
      updateDate: new Date(),
    };
    await this.diligenceHistoryRepo.update(diligenceId, partialUpdate);
    this.logger.info(`emit event  RefreshDiligenceAnalyze orgId: ${orgId},diligenceId: ${diligenceId}  `);
    // this.eventEmitter.emit(DiligenceRelatedEventEnums.RefreshDiligenceAnalyze, { orgId, diligenceId });
    await this.diligenceAnalyzeService.reAnalyzeByRelatedDiligence(orgId, diligenceId);
    return updatedScorePO;
  }

  /**
   * 获取更新维度的hits
   * @param user
   * @param markPerson
   * @param dimensionScorePo
   */
  async getUpdateDiligenceHistByDimension(user: RoverUser, markPerson: MarkPersonModel, dimensionScorePo: DimensionScorePO): Promise<UpdateDiligenceHitsPO> {
    const { userId, currentOrg: orgId } = user;
    const { personId, key: dimensionKey, diligenceId, snapshotId } = markPerson;
    this.logger.info(
      `getUpdateDiligenceHistByDimension: dimensionKey=${dimensionKey}, personId=${personId}, diligenceId=${diligenceId}, snapshotId=${snapshotId}`,
    );
    const updateDiligenceHitsPO = Object.assign(new UpdateDiligenceHitsPO(), {
      dimensionKey,
      hitsCountChanged: 0,
    });
    //如果 dimensionScorePo 为空，说明该维度没有该人员记录，则直接返回
    if (!dimensionScorePo || !dimensionScorePo?.totalHits) {
      this.logger.info(
        `getUpdateDiligenceHistByDimension dimensionScorePo is null,dimensionKey=${dimensionKey}, personId=${personId}, diligenceId=${diligenceId},timestamp=${new Date().getTime()}`,
      );
      return updateDiligenceHitsPO;
    }
    //如果dimensionScorePo.totalHits > 0, 说明该维度有该人员记录，则需要核实
    //如果获取不到 dimensionItemsResponse ，则需要重复获取5次，直到获取到数据
    let dimensionItemsResponse: PaginationResponse = new PaginationResponse();
    let retryCount = 0;
    let pageSize = 100;
    while (!dimensionItemsResponse?.data?.length && retryCount < 3) {
      try {
        dimensionItemsResponse = await this.snapshotEsService.searchSnapshotData(
          {
            orgId,
            diligenceId: [diligenceId],
            dimensionKey: [dimensionKey],
            pageIndex: 1,
            pageSize,
          },
          true,
        );
        if (!dimensionItemsResponse?.data?.length) {
          retryCount++;
          //立即补充快照
          const dbDiligence: DiligenceHistoryEntity = await this.diligenceHistoryRepo.findOne(markPerson.diligenceId);
          await this.snapshotHelper.reFillSnapshotData(dbDiligence, [dimensionKey], true);
          await Bluebird.delay(500);
          pageSize++;
        }
      } catch (e) {
        retryCount++;
        this.logger.error(`getUpdateDiligenceHistByDimension error: ${e.message}`);
        throw new BadRequestException(RoverExceptions.Diligence.Snapshot.NotGenerated);
      }
    }
    this.logger.info(`getUpdateDiligenceHistByDimension dimensionItemsResponse: ${JSON.stringify(dimensionItemsResponse)}`);
    if (!dimensionItemsResponse?.data?.length || dimensionScorePo?.totalHits > dimensionItemsResponse.data.length) {
      //应该有数据但是还是没有获取到，这种情况就直接报错
      this.logger.info(
        `no dimensionItemsResponse, dimensionKey=${dimensionKey}, personId=${personId}, diligenceId=${diligenceId},timestamp=${new Date().getTime()}`,
      );
      throw new BadRequestException(RoverExceptions.Diligence.Snapshot.NotGenerated);
      //return updateDiligenceHitsPO;
    }
    //遍历dimensionItems, 找到与markPerson相关的记录，并更新hits
    const verifyName = dimensionItemsResponse?.data?.find((f) => f.personId === personId)?.name;
    if (!verifyName) {
      return updateDiligenceHitsPO;
    }
    const updateItems: RelatedPersonResponse[] =
      (
        await Bluebird.map(
          dimensionItemsResponse.data,
          async (item) => {
            return await this.modifyDimensionSnapshotContent(userId, orgId, diligenceId, verifyName, item, markPerson);
          },
          { concurrency: 1 }, //fix: 并发导致数据不一致
        )
      ).filter((t) => t) || [];
    // const updateItems: RelatedPersonResponse[] =
    //   (
    //     await Promise.all(
    //       dimensionItemsResponse.data.map(async (item) => {
    //         return await this.modifyDimensionSnapshotContent(userId, orgId, diligenceId, verifyName, item, markPerson);
    //       }),
    //     )
    //   )?.filter((t) => t) || [];
    this.logger.info(
      `updateItems diligenceId=${diligenceId},dimensionKey=${dimensionKey},personId=${personId},verifyName=${verifyName},updateItems.length=${
        updateItems?.length || 0
      }`,
    );
    if (updateItems?.length) {
      this.logger.info(`override snapshot data, snapshotId=${snapshotId}, dimensionKey=${dimensionKey}, personId=${personId}, verifyName=${verifyName}`);
      // 以下方法会删除原来快照维度子文档diligence，然后重新创建,已经被修改为无效的子文档关联会丢弃
      await this.snapshotHelper.saveDimensionData(snapshotId, dimensionKey, updateItems, true);
    }
    //比较 dimensionItemsResponse.Result 和 dimensionItemsResponseAfter.Result，找出相差的条数，dimensionItemsResponseAfter.total - dimensionItemsResponse.total
    Object.assign(updateDiligenceHitsPO, {
      hitsCountChanged: (updateItems?.length || 0) - dimensionScorePo.totalHits || 0,
    });
    this.logger.info(`updateDiligenceHitsPO: ${JSON.stringify(updateDiligenceHitsPO)}`);
    return updateDiligenceHitsPO;
  }

  /**
   * 人员核实
   * @param userId
   * @param orgId
   * @param diligenceId
   * @param verifyPersonName
   * @param item
   * @param markPerson
   * @private
   */
  private async modifyDimensionSnapshotContent(
    userId: number,
    orgId: number,
    diligenceId: number,
    verifyPersonName: string,
    item: RelatedPersonResponse,
    markPerson: MarkPersonModel,
  ): Promise<RelatedPersonResponse> {
    const { personId, status, key: dimensionKey, keyNo } = markPerson;
    this.logger.info(
      `更新维度 ${dimensionKey} 的快照数据，orgId=${orgId}, markPerson.personId=${personId},item.personId==${item.personId}, name=${item.name}, status=${status}`,
    );
    if (item.personId === personId) {
      //更新该条数据记录为已核实,如果核实是本人则该条记录保留并标记状态为是本人，如果核实为不是本人，则该条记录删除
      item.status = status;
      if (status) {
        //核实为本人,更新快照数据 status = 1
        if (dimensionKey === DimensionLevel3Enums.SuspectedInterestConflict) {
          //疑似潜在利益冲突维度比较特殊，核实了是本人之后需要向潜在利益冲突维度中添加该人员信息,并且在疑似中标记该数据失效
          this.logger.info(`特殊处理疑似潜在利益冲突维度，personId=${item.personId}, name=${item.name}, status=1`);
          await this.handlerSuspectedInterestConflict(markPerson, orgId, userId, diligenceId);
          return null;
        } else {
          return item;
        }
      } else {
        //核实非本人,标记该快照记录为失效
        this.logger.info(`标记 ${dimensionKey} 的快照数据为失效，personId=${item.personId}, name=${item.name}, status=0`);
        const toIgnoreId = getDimensionRecordIdForSnapshot(
          dimensionKey,
          {
            personId: markPerson.personId,
            keyNo: markPerson.keyNo,
          },
          markPerson.companyId,
          orgId,
        );
        await this.snapshotEsService.changeSnapshotDataStatus(
          {
            docIds: [toIgnoreId],
            diligenceId,
            status: 0,
            operatorId: userId,
          },
          true,
        );
        return null;
      }
    } else {
      if (verifyPersonName === item.name) {
        //该维度中也有该人员同名人员的记录，需要同步该同名人员核实记录，如果核实是本人，则这个同名的人员记录标记为核实非本人，如果核实非本人，则这个记录状态不变
        if (status) {
          //核实为本人,更新快照数据 status = 0，递归去调用人员核实记录接口，更新快照数据 status = 0
          this.logger.info(`同名人员核实 ${dimensionKey} 的快照数据，personId=${item.personId}, name=${item.name}, status=0`);
          const markPerson2: MarkPersonModel = { ...markPerson, personId: item.personId, status: 0 };
          await this.modifyDimensionSnapshotContent(userId, orgId, diligenceId, verifyPersonName, item, markPerson2);
          return null;
        }
      }
      return item;
    }
  }

  /**
   * 特殊处理疑似潜在利益冲突维度
   * @param markPerson
   * @param orgId
   * @param userId
   * @param diligenceId
   * @private
   */
  private async handlerSuspectedInterestConflict(markPerson: MarkPersonModel, orgId: number, userId: number, diligenceId: number) {
    const { personId, keyNo, companyId, snapshotId } = markPerson;
    const dimensionKey = DimensionLevel3Enums.SuspectedInterestConflict;
    const diligence: DiligenceHistoryEntity = await this.diligenceHistoryRepo.findOne(diligenceId, { relations: ['batchEntities'] });
    if (!diligence) {
      throw new BadParamsException(RoverExceptions.Diligence.Snapshot.NotFound);
    }
    //预先计算出来 recordId，然后走忽略recordId的流程
    const toIgnoreId = getDimensionRecordIdForSnapshot(
      dimensionKey,
      {
        personId,
        keyNo,
      },
      companyId,
      orgId,
    );
    //获取当前要更新的记录，把它插入到维度 StaffWorkingOutsideForeignInvestment 中
    const targetDimensionSnapshot = await this.snapshotEsService.getDimensionSnapshotByRecordId(toIgnoreId);
    if (targetDimensionSnapshot?.dimensionContent) {
      //先标记数据失效，再插入到维度 StaffWorkingOutsideForeignInvestment 中
      this.logger.info(`设置数据为失效，recordId=${toIgnoreId}, orgId=${orgId}, keyNo=${keyNo}, companyId=${companyId}, personId=${personId}`);
      await this.snapshotEsService.changeSnapshotDataStatus(
        {
          docIds: [toIgnoreId],
          diligenceId,
          status: 0,
          operatorId: userId,
        },
        true,
      );
      const dimensionContent = JSON.parse(targetDimensionSnapshot.dimensionContent) as RelatedPersonResponse;
      dimensionContent.status = 1; //标记为本人
      await this.snapshotEsService.insertSnapshotData(
        {
          dimensionKey: DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment,
          orgId,
          companyId: companyId,
          diligenceAt: diligence.createDate,
          diligenceId,
          snapshotId,
          batchId: diligence.batchEntities?.map((t) => t.batchId),
          items: [dimensionContent],
        },
        true,
      );
    } else {
      this.logger.error(`找不到维度 ${DimensionLevel3Enums.SuspectedInterestConflict} 的记录,recordId=${toIgnoreId}, content=${JSON.stringify(markPerson)}`);
    }
  }

  private async syncDiligenceDaily(orgId: number) {
    const sql = `select count(distinct (company_id)) as cnt
                 from due_diligence
                 where org_id = ${orgId}
                   and create_date >= '${moment().startOf('day').format(DATE_TIME_FORMAT)}'
                   and create_date <= '${moment().endOf('day').format(DATE_TIME_FORMAT)}'
                   and operator != -1`;
    // this.logger.info(sql);
    return this.runQuery(sql);
  }

  private async runQuery(sql) {
    if (sql) {
      const r = await this.diligenceHistoryRepo.query(sql);
      return r[0]?.cnt;
    }
    return 0;
  }

  public postUpdateDiligence(diligenceRes) {
    Object.keys(diligenceRes.details?.levelGroup).forEach((key) => {
      diligenceRes.details?.levelGroup?.[key].forEach((group) => {
        group.name = this.DimensionNameMap.get(group.groupKey) || group.name;
        group.scoreDetails?.forEach((item) => {
          item.name = this.DimensionNameMap.get(item.key) || item.name;
          item.subDimension?.forEach((sub) => {
            sub.name = this.DimensionNameMap.get(sub.key) || sub.name;
            sub.subDimension?.forEach((ssub) => {
              ssub.name = this.DimensionNameMap.get(ssub.key) || ssub.name;
            });
          });
        });
      });
    });
    diligenceRes.details?.originalHits?.forEach((item) => {
      item['name'] = this.DimensionNameMap.get(item.key) || item.name;
      item.subDimension?.forEach((sub) => {
        sub['name'] = this.DimensionNameMap.get(sub.key) || sub.name;
        sub.subDimension?.forEach((ssub) => {
          ssub['name'] = this.DimensionNameMap.get(ssub.key) || ssub.name;
        });
      });
    });
    diligenceRes.details?.dimensionScoreDetails?.forEach((group) => {
      group.name = this.DimensionNameMap.get(group.groupKey) || group.name;
      group.scoreDetails?.forEach((item) => {
        item['name'] = this.DimensionNameMap.get(item.key) || item.name;
        item.subDimension?.forEach((sub) => {
          sub['name'] = this.DimensionNameMap.get(sub.key) || sub.name;
          sub.subDimension?.forEach((ssub) => {
            ssub['name'] = this.DimensionNameMap.get(ssub.key) || ssub.name;
          });
        });
      });
    });
  }

  private async doDiligenceForce(
    currentUser: RoverUser,
    userBundle: RoverBundleEntityConfig,
    diligenceRes: DiligenceResponseV2,
    orgSetting: OrgSettingsLogEntity,
    companyId: string,
    companyName: string,
    dbDiligence: DiligenceHistoryEntity,
    departments: number[],
  ) {
    diligenceRes.paid = false;
    const orgId = currentUser.currentOrg;
    const userId = currentUser.userId;
    let companyCounter: ICounter = null;
    let historyCounter: ICounter = null;
    let dailyCounter: ICounter = null;
    if (userBundle[RoverBundleCounterType.DiligenceCompanyQuantity].value !== -1) {
      // 说明开启了 按排查公司数量 计费模式,
      if (!dbDiligence) {
        //套餐有效期内无排查记录，标记需要计费，扣减套餐额度
        companyCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.DiligenceCompanyQuantity);
        // await companyCounter.clear();
        await companyCounter.increase(1);
        diligenceRes.paid = true;
      }
    }

    if (userBundle[RoverBundleCounterType.DiligenceHistoryQuantity].value !== -1) {
      // 说明开启了 按排查次数数量 计费模式,
      historyCounter = await this.bundleService.getOrgBundleCounter(currentUser, RoverBundleCounterType.DiligenceHistoryQuantity);
      await historyCounter.increase(1);
      if (userBundle[RoverBundleCounterType.DiligenceCompanyQuantity].value == -1) {
        diligenceRes.paid = true;
      }
    }

    // 每日尽调上限校验
    dailyCounter = await this.bundleService.getOrgLimitationCounter(currentUser, RoverBundleLimitationType.DiligenceDailyQuantity);
    await dailyCounter.increase(1);

    diligenceRes.realtime = true;
    try {
      const modelScore = await this.modelRiskService.getScore(orgId, userId, companyId, companyName, orgSetting);
      const snapshotId = generateSnapshotId();
      const diligenceHis = Object.assign(new DiligenceHistoryEntity(), {
        orgId,
        depId: departments?.[0],
        operator: userId,
        name: companyName,
        companyId,
        score: modelScore.totalScore, // 评价分数
        result: modelScore.result, // 尽调结果
        details: modelScore,
        snapshotDate: new Date(),
        snapshotId: snapshotId, // 生成快照ID，用于保存维度详情列表
        orgSettingsId: orgSetting.id,
        creditRate: modelScore.creditRateResult?.Score,
      });
      dbDiligence = await this.diligenceHistoryRepo.save(diligenceHis);
      // 对排查结果生成快照
      await this.snapshotService.prepareSnapshot(snapshotId, dbDiligence);
    } catch (e) {
      if (companyCounter) {
        // 退回 公司数量额度
        await companyCounter.decrease(1);
      }
      if (historyCounter) {
        // 退回 排查记录数量额度
        await historyCounter.decrease(1);
      }
      if (dailyCounter) {
        // 退回 每日尽调上限校验 额度
        await dailyCounter?.decrease(1);
      }
      this.logger.error('doDiligenceForce error', e);
      throw new BadRequestException(RoverExceptions.Diligence.Detail.Failed);
    }
    const result = { dbDiligence, diligenceRes };
    return { result };
  }

  /**
   * 交叉重叠关系、内部黑名单范围校验
   * @param orgId
   * @param orgSetting
   * @param companyId
   * @param userId
   * @private
   */
  private async checkRelationsRange(orgId: number, orgSetting: OrgSettingsLogEntity, companyId: string, userId: number) {
    // 当前列表数量是否 > 7000
    const maxCount = 7000;
    const customerCount = await this.customerRepo.count({ orgId });
    const blackListCount = await this.innerBlacklistRepo.count({ orgId });
    if (customerCount <= maxCount && blackListCount <= maxCount) {
      return;
    }
    const allDefs: DimensionDefinitionPO[] = await this.settingService.getAllDimension(orgSetting, true, false);
    // 过滤未开启的维度
    const activeDefs: DimensionDefinitionPO[] = allDefs.filter((a) => a?.status > 0);
    let overRange = false;
    for (const def of activeDefs) {
      let err;
      switch (def.key) {
        case DimensionLevel2Enums.CustomerPartnerInvestigation:
        case DimensionLevel2Enums.CustomerSuspectedRelation:
          if (customerCount > maxCount) {
            const count = await this.getRangeCount(orgId, def, customerCount, companyId, userId);
            if (count > maxCount) {
              overRange = true;
              err = { ...RoverExceptions.Diligence.Detail.CustomerOverRange };
            }
          }
          break;
        case DimensionLevel2Enums.BlacklistPartnerInvestigation:
        case DimensionLevel2Enums.BlacklistSuspectedRelation:
          if (blackListCount > maxCount) {
            const count = await this.getRangeCount(orgId, def, customerCount, companyId, userId);
            if (count > maxCount) {
              overRange = true;
              err = { ...RoverExceptions.Diligence.Detail.BlackListOverRange };
            }
          }
          break;
        default:
      }
      if (overRange) {
        err.error = getExceptionDescription(err.error, maxCount);
        throw new BadRequestException(err);
      }
    }
  }

  /**
   * 获取设置排查范围数量
   * @param orgId
   * @param dimensionPO
   * @param listCount
   * @param companyId
   * @param userId
   * @private
   */
  private async getRangeCount(orgId: number, dimensionPO: DimensionDefinitionPO, listCount: number, companyId: string, userId: number) {
    const dataRangeFieldVal = dimensionPO.strategyModel?.detailsParams?.find((f) => f.field == QueryParamsEnums.dataRange);
    if (!dataRangeFieldVal) {
      // 没有相关配置，默认全量
      return listCount;
    }
    const companyIds = await this.roverGraphService.getRangeCompanyIds(dimensionPO, orgId, userId, companyId, dataRangeFieldVal);
    return companyIds?.length || 0;
  }
}
