import { BadRequestException, Injectable } from '@nestjs/common';
import { DiligenceSnapshotEsService } from './diligence.snapshot.es.service';
import { CompareSnapshotPO } from './po/CompareSnapshotPO';
import { CompareBatchSnapshotPO } from './po/CompareBatchSnapshotPO';
import { SearchAnalyzedDimensionDiffsResponse } from './po/SearchAnalyzedDimensionDiffsResponse';
import { SearchDimensionDiffsFromSnapshotRequest } from './po/SearchDimensionDiffsFromSnapshotRequest';
import { CompanyAnalyzedDiffsPO } from './po/CompanyAnalyzedDiffsPO';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';

@Injectable()
export class DiligenceSnapshotEsCompareService extends DiligenceSnapshotEsService {
  /**
   * 对比指定的两个快照，根据指定类型返回值:
   * @param params
   */
  public async compareSnapshotByDiligenceId(params: CompareSnapshotPO) {}

  /**
   * 对比两个batchId 指定维度的差异
   * 返回 第一个batchId包含的，第二个batchId中不包含的指定数据(batchIds[0] 相对于 batchIds[1]多出来的数据)
   * @param params
   */
  public async searchDimensionDiffsByBatch(params: SearchDimensionDiffsFromSnapshotRequest): Promise<SearchAnalyzedDimensionDiffsResponse> {
    const { batchIds, onlyDimension, changesType } = params;
    const pageSize = params.pageSize || 10;
    const pageIndex = params.pageIndex || 1;
    if (batchIds?.filter((t) => t)?.length !== 2) {
      throw new BadRequestException('batchIds length must be 2 ');
    }
    const query: any = this.generateDimensionQuery(params, changesType).parentQuery;
    const body: any = {
      query,
    };
    const response = await this.esClientRead.search({
      index: this.getReadIndexName(),
      body: body,
      size: pageSize,
      from: (pageIndex - 1) * pageSize,
    });
    const paginationResponse = new SearchAnalyzedDimensionDiffsResponse();
    paginationResponse.pageSize = pageSize;
    paginationResponse.pageIndex = pageIndex;
    paginationResponse.total = response.body.hits.total.value;
    paginationResponse.data =
      response.body.hits.hits.map((item: { _source: { dimensionContent: string } }) => {
        if (onlyDimension) {
          return item._source.dimensionContent ? JSON.parse(item._source.dimensionContent) : null;
        } else {
          const t = { ...item._source };
          t.dimensionContent = t.dimensionContent ? JSON.parse(t.dimensionContent) : null;
          return t;
        }
      }) || [];
    return paginationResponse;
  }

  /**
   * 获取两个batchId 有维度变化的公司列表
   * @param params
   */
  @TraceLog({ throwError: true, spanName: 'analyzeDiffsAggsByCompany' })
  public async analyzeDiffsAggsByCompany(params: CompareBatchSnapshotPO): Promise<CompanyAnalyzedDiffsPO[]> {
    const { companyIds, batchIds } = params;
    if (!batchIds?.length && !companyIds?.length) {
      throw new BadRequestException('batchIds or companyIds must be provided');
    }
    const parentQuery: any = this.generateDimensionQuery(params, 4).parentQuery;
    //batchIds[0]中比batchIds[1]中增加的数据,
    const type0Query = this.generateDimensionQuery(
      Object.assign(
        { ...params },
        {
          changesType: 0,
        },
      ),
      0,
    );

    //batchIds[0]中比batchIds[1]中减少的数据),
    const type1Query = this.generateDimensionQuery(
      Object.assign(
        { ...params },
        {
          changesType: 1,
        },
      ),
      1,
    );

    //batchIds[0]当前命中的数量
    const type3Query = this.generateDimensionQuery(
      Object.assign(
        { ...params },
        {
          changesType: 3,
        },
      ),
      3,
    );
    // }
    // terms: {
    //   field: 'diligence.companyId',
    //     size: Math.min(companyIds?.length || 2000),
    // },
    const aggs: any = {
      currentAggs: {
        filter: {
          bool: { must: type3Query.parentQuery },
        },
        aggs: {
          dimensionCount: {
            terms: {
              field: 'dimensionKey',
              size: 500,
            },
            aggs: {
              companyInfo: {
                children: {
                  type: 'diligence',
                },
                aggs: {
                  companyInfoDiligence: {
                    filter: type3Query.aggsFilter,
                    aggs: {
                      companyCount: {
                        terms: {
                          field: 'diligence.companyId',
                          size: companyIds?.length || 1000,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      increaseAggs: {
        filter: {
          bool: { must: type0Query.parentQuery },
        },
        aggs: {
          dimensionCount: {
            terms: {
              field: 'dimensionKey',
              size: 500,
            },
            aggs: {
              companyInfo: {
                children: {
                  type: 'diligence',
                },
                aggs: {
                  companyInfoDiligence: {
                    filter: type0Query.aggsFilter,
                    aggs: {
                      companyCount: {
                        terms: {
                          field: 'diligence.companyId',
                          size: companyIds?.length || 1000,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      decreaseAggs: {
        filter: {
          bool: { must: type1Query.parentQuery },
        },
        aggs: {
          dimensionCount: {
            terms: {
              field: 'dimensionKey',
              size: 500,
            },
            aggs: {
              companyInfo: {
                children: {
                  type: 'diligence',
                },
                aggs: {
                  companyInfoDiligence: {
                    filter: type1Query.aggsFilter,
                    aggs: {
                      companyCount: {
                        terms: {
                          field: 'diligence.companyId',
                          size: companyIds?.length || 1000,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    };
    const body: any = {
      query: parentQuery,
      aggs,
    };
    const response = await this.esClientRead.search({
      index: this.getReadIndexName(),
      body: body,
      size: 0,
      from: 0,
    });

    const resultItems: CompanyAnalyzedDiffsPO[] = [];
    response.body.aggregations?.currentAggs.dimensionCount.buckets.forEach((bucket) => {
      const dimensionKey = bucket.key;
      bucket.companyInfo.companyInfoDiligence.companyCount.buckets.forEach((companyBucket) => {
        const companyId = companyBucket.key;
        const value = companyBucket.doc_count;
        let companyItem: CompanyAnalyzedDiffsPO = resultItems.find((item) => item.companyId === companyId);
        let newCompanyItem = false;
        if (!companyItem) {
          companyItem = {
            companyId,
            changes: [],
          };
          newCompanyItem = true;
        }
        const changeItem = companyItem.changes.find((item) => item.dimensionKey === dimensionKey);
        if (changeItem) {
          changeItem.currentHitValue = value;
        } else {
          companyItem.changes.push({
            increasedValue: 0,
            decreasedValue: 0,
            currentHitValue: value,
            dimensionKey,
          });
        }
        if (newCompanyItem) {
          resultItems.push(companyItem);
        }
      });
    });
    response.body.aggregations?.increaseAggs.dimensionCount.buckets.forEach((bucket) => {
      const dimensionKey = bucket.key;
      bucket.companyInfo.companyInfoDiligence.companyCount.buckets.forEach((companyBucket) => {
        const companyId = companyBucket.key;
        const value = companyBucket.doc_count;
        const companyItem: CompanyAnalyzedDiffsPO = resultItems.find((item) => item.companyId === companyId);
        if (companyItem) {
          const changeItem = companyItem.changes.find((item) => item.dimensionKey === dimensionKey);
          if (changeItem) {
            changeItem.increasedValue = value;
          } else {
            companyItem.changes.push({
              increasedValue: value,
              decreasedValue: 0,
              currentHitValue: 0,
              dimensionKey,
            });
          }
        }
      });
    });
    response.body.aggregations?.decreaseAggs.dimensionCount.buckets.forEach((bucket) => {
      const dimensionKey = bucket.key;
      bucket.companyInfo.companyInfoDiligence.companyCount.buckets.forEach((companyBucket) => {
        const companyId = companyBucket.key;
        const value = companyBucket.doc_count;
        const companyItem: CompanyAnalyzedDiffsPO = resultItems.find((item) => item.companyId === companyId) || {
          companyId,
          changes: [],
        };
        if (companyItem) {
          const changeItem = companyItem.changes.find((item) => item.dimensionKey === dimensionKey);
          if (changeItem) {
            changeItem.decreasedValue = value;
          } else {
            companyItem.changes.push({
              increasedValue: 0,
              decreasedValue: value,
              currentHitValue: 0,
              dimensionKey,
            });
          }
        }
      });
    });
    return resultItems;
  }

  /**
   *
   * @param params
   * @param changesType 0 默认返回batchIds[0]中比batchIds[1]中增加的数据),
   *              1 batchIds[0]中比batchIds[1]缺少的数据,
   *              2 返回两次对比的差异
   *              3. 返回batchIds[0]命中的数字
   *              4. 返回并集
   * @private
   */
  private generateDimensionQuery(params: CompareBatchSnapshotPO, changesType: number) {
    const { batchIds, dimensionKey, companyIds, recordIds } = params;
    const query: any = {
      bool: {
        must: [],
        must_not: [],
        should: [],
      },
    };
    const childQuery = {
      bool: {
        must: [],
        must_not: [],
        should: [],
      },
    };
    childQuery.bool.must.push({ term: { 'diligence.status': 1 } });
    if (recordIds?.length > 0) {
      query.bool.must.push({
        terms: {
          id: recordIds,
        },
      });
    }
    if (dimensionKey?.length > 0) {
      query.bool.must.push({
        terms: {
          dimensionKey,
        },
      });
    }
    const aggsFilter: any = {
      bool: {
        must: [],
        must_not: [],
        should: [],
      },
    };
    // 只查询有效的记录
    const term0 = [];
    const term1 = [];
    const term2 = [];
    term0.push({ term: { 'diligence.batchId': batchIds[0] } }, { term: { 'diligence.status': 1 } });
    term1.push({ term: { 'diligence.batchId': batchIds[1] } }, { term: { 'diligence.status': 1 } });
    term2.push({ terms: { 'diligence.batchId': batchIds } }, { term: { 'diligence.status': 1 } });

    if (companyIds?.length > 0) {
      term0.push({ terms: { 'diligence.companyId': companyIds } });
      term1.push({ terms: { 'diligence.companyId': companyIds } });
      term2.push({ terms: { 'diligence.companyId': companyIds } });
    }

    if (changesType == 1) {
      aggsFilter.bool.must.push(...term1);
      // aggsFilter.bool.must_not.push(...term0);
      query.bool.must.push({
        has_child: {
          type: 'diligence',
          query: { bool: { must: term1 } },
        },
      });
      query.bool.must_not.push({
        has_child: {
          type: 'diligence',
          query: { bool: { must: term0 } },
        },
      });
    } else if (changesType == 2) {
      aggsFilter.bool.must.push(...term2);
      aggsFilter.bool.must_not.push({
        bool: {
          must: [{ term: { 'diligence.batchId': batchIds[0] } }, ...term1],
        },
      });
      query.bool.should.push({
        has_child: {
          type: 'diligence',
          query: { bool: { must: term1 } },
        },
      });
      query.bool.should.push({
        has_child: {
          type: 'diligence',
          query: { bool: { must: term0 } },
        },
      });

      // 排除同时包含两个批量排查记录的记录
      query.bool.must_not.push({
        bool: {
          must: [
            {
              has_child: {
                type: 'diligence',
                query: { bool: { must: term0 } },
              },
            },
            {
              has_child: {
                type: 'diligence',
                query: { bool: { must: term1 } },
              },
            },
          ],
        },
      });
    } else if (changesType == 3) {
      aggsFilter.bool.must.push(...term0);
      query.bool.must.push({
        has_child: {
          type: 'diligence',
          query: { bool: { must: term0 } },
        },
      });
    } else if (changesType == 4) {
      aggsFilter.bool.must.push(...term2);

      query.bool.must.push({
        has_child: {
          type: 'diligence',
          query: { bool: { must: term2 } },
        },
      });
    } else {
      aggsFilter.bool.must.push(...term0);
      // aggsFilter.bool.must_not.push(...term1);
      query.bool.must.push({
        has_child: {
          type: 'diligence',
          query: { bool: { must: term0 } },
        },
      });
      query.bool.must_not.push({
        has_child: {
          type: 'diligence',
          query: { bool: { must: term1 } },
        },
      });
    }

    if (query.bool.should.length > 0) {
      query.bool['minimum_should_match'] = 1;
    }

    return {
      parentQuery: query,
      aggsFilter,
    };
    // return {
    //   combinedQuery: query,
    //   // diligenceChildQuery: diligenceChildQueryMust,
    //   // diligenceChildQueryMustNot: diligenceShouldNotExistBoth, //for type 2, 数组里面的条件不能同时满足
    // };
  }
}
