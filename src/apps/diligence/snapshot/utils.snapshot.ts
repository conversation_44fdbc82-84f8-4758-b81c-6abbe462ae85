import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import * as crypto from 'crypto';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { RelatedPersonResponse } from '../../../libs/model/diligence/pojo/req&res/RelatedPersonResponse';
import { CompanyModel } from '../../../libs/model/person/CompanyModel';
import { v1 as uuidv1 } from 'uuid';

/**
 * 存储 diligence子文档的时候，预先计算出来的ID
 * @param dimensionKey
 * @param diligenceId
 */
export const getChildIdForDiligence = (diligenceId: number, parentId: string) => {
  return crypto.createHash('md5').update(`diligence_${diligenceId}_${parentId}`).digest('hex');
};

// /**
//  * 存储 维度的更新记录的时候，预先计算的ID
//  * @param dimensionKey
//  * @param dimensionId
//  * @param orgId
//  */
// export const getChildIdForExcludedOrg = (dimensionKey: DimensionTypeEnums, dimensionId: string, orgId: number) => {
//   return crypto.createHash('md5').update(`excluded_org_${orgId}_dimension_${dimensionId}`).digest('hex');
// };

/**
 * 获取指定维度的唯一性id
 * NOTE: 该方法非常重要，修改时候一定要注意
 * @param dimensionKey
 * @param item
 * @param companyId 被排查的公司的id
 */
export const getDimensionIdForSnapshot = (dimensionKey: DimensionTypeEnums, item: object, companyId: string, orgId: number) => {
  if (!item) {
    return null;
  }
  let businessId = item?.['id'] || item?.['Id'] || item?.['_id'] || item?.['No'];

  switch (dimensionKey) {
    //以下是rover service中支持的维度
    case DimensionLevel3Enums.SamePhone:
    case DimensionLevel3Enums.PunishedEmployeesForeignInvestment: //曾被处罚的现任员工或前员工-对外投资
    case DimensionLevel3Enums.PunishedEmployeesWorkingOutside: //曾被处罚的现任员工或前员工-在外任职
    case DimensionLevel3Enums.StaffForeignInvestment: // 潜在利益冲突-对外投资
    case DimensionLevel3Enums.StaffWorkingOutside: // 疑似潜在利益冲突-在外任职
    case DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment:
    case DimensionLevel3Enums.SuspectedInterestConflict: {
      //RelatedPersonResponse[]
      const obj = item as RelatedPersonResponse;
      const personId = obj?.personId;
      const personKeyno = obj?.keyNo;
      const suffix = crypto.createHash('md5').update(`orgId_${orgId}_companyId_${companyId}`).digest('hex');
      businessId = `personId_${personId}_${personKeyno}_${suffix}`;
      break;
    }
    case DimensionLevel2Enums.BlacklistSameSuspectedActualController:
    case DimensionLevel2Enums.SameSuspectedActualController: {
      //CompanyModel[]
      const obj = item as CompanyModel;
      //obj.sourceCompanyId 和 companyId 应该相等
      businessId = crypto.createHash('md5').update(`${obj.companyId}_${obj.typeDesc}_${companyId}`).digest('hex');
      break;
    }
    //rover graph partner types 参考 roverGraphService.getPartnerTypes()
    case DimensionLevel2Enums.InvestorsRelationship:
    case DimensionLevel2Enums.ShareholdingRelationship:
    case DimensionLevel2Enums.ServeRelationship:
    //rover graph blacklist types 参考 roverGraphService.getBlacklistTypes()
    case DimensionLevel2Enums.HitInnerBlackList:
    case DimensionLevel2Enums.Shareholder:
    case DimensionLevel2Enums.ForeignInvestment:
    case DimensionLevel2Enums.EmploymentRelationship:
    //rover graph other types
    case DimensionLevel2Enums.CustomerPartnerInvestigation:
    case DimensionLevel2Enums.BlacklistPartnerInvestigation: {
      let prefix = '';
      let hashSuffix = `orgId_${orgId}_companyId_${companyId}`;
      if (item?.['blacklistId']) {
        //BlacklistDirectConnectionPO,BlacklistInvestConnectionPO,BlacklistPersonConnectionPO
        prefix = `blacklistId_${businessId}`;
        if (item?.['personId']) {
          hashSuffix += `_${item['personKeyno']}`;
        }
        if (item?.['duration']) {
          hashSuffix += `_${item['duration']}`;
        }
        if (item?.['joinDate']) {
          hashSuffix += `_${item['joinDate']}`;
        }
        if (item?.['reason']) {
          hashSuffix += `_${item['reason']}`;
        }
        if (item?.['expiredDate']) {
          hashSuffix += `_${item['expiredDate']}`;
        }
      } else if (item?.['personId']) {
        //PersonConnectionPO
        prefix = `personId_${item['personId']}`;
        hashSuffix += `_${item['personKeyno']}`;
      } else if (item?.['companyKeynoRelated'] && (item?.['role'] || item?.['direction'])) {
        //InvestConnectionPO
        prefix = 'invest';
        hashSuffix += `_related_${item['companyKeynoRelated']}_role_${item['role']}_direction_${item['direction']}`;
      } else {
        //DirectConnectionPO
        prefix = 'direct';
        hashSuffix += `_related_${item['companyKeynoRelated']}`;
      }
      businessId = `${prefix}_${crypto.createHash('md5').update(hashSuffix).digest('hex')}`;
    }
    case DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence: {
      if (item?.['mainKeyNo']) {
        const params = `${item?.['Id']}_${item?.['mainKeyNo']}`;
        businessId = `mainKeyNo_${crypto.createHash('md5').update(params).digest('hex')}`;
      }
    }
  }

  if (!businessId) {
    businessId =
      'md5_' +
      crypto
        .createHash('md5')
        .update(
          JSON.stringify(
            Object.keys(item)
              .sort()
              .map((key) => ({ [key]: item[key] })),
          ),
        )
        .digest('hex');
  }
  return businessId;
};

/**
 * 推算 维度的记录id
 * NOTE: 该方法非常重要，修改时候一定要注意
 * @param dimensionKey
 * @param item
 * @param companyId
 * @param orgId
 */
export const getDimensionRecordIdForSnapshot = (dimensionKey: DimensionTypeEnums, item: object, companyId: string, orgId: number) => {
  const dimensionId = getDimensionIdForSnapshot(dimensionKey, item, companyId, orgId);
  return crypto.createHash('md5').update(`${dimensionKey}_${dimensionId}`).digest('hex');
};

/**
 * 转换dimension 快照中的字段，用于排序
 * 目前发现 一般只有数字类型的字段需要转换，其他类型字段不需要转换
 */
export const convertSnapshotDimensionFieldForSort = (fieldValue: any) => {
  try {
    if (!fieldValue) {
      return fieldValue;
    }
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (typeof fieldValue === 'string' && dateRegex.test(fieldValue)) {
      return new Date(fieldValue).getTime();
    } else if (typeof fieldValue === 'string' && !isNaN(parseFloat(fieldValue)) && isFinite(parseFloat(fieldValue))) {
      return parseFloat(fieldValue);
    }
    return fieldValue;
  } catch (e) {
    console.error('convertSnapshotDimensionFieldForSort error:', e);
    console.error(e);
  }
  return fieldValue;
};

export const generateSnapshotId = () => {
  return uuidv1();
};
