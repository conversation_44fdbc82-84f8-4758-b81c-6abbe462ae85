import { Injectable } from '@nestjs/common';
import { ConfigService } from '../../../libs/config/config.service';
import { Client } from '@elastic/elasticsearch';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { AnalyzedCompanySearchedRequest } from './po/AnalyzedCompanySearchedRequest';
import { DiligenceAnalyzeChangesItemPO, DiligenceAnalyzeEsDoc } from './po/DiligenceAnalyzeEsDoc';
import { chunk, find, flatMap, flatten } from 'lodash';
import * as crypto from 'crypto';
import { DiligenceAnalyzeResponse } from './po/DiligenceAnalyzeResponse';
import { plainToInstance } from 'class-transformer';
import { OverwriteCustomerInfoPO } from './po/OverwriteCustomerInfoPO';
import { DiligenceHistoryEntity } from '../../../libs/entities/DiligenceHistoryEntity';
import * as Bluebird from 'bluebird';
import { CompanyAnalyzedDiffsPO } from '../snapshot/po/CompanyAnalyzedDiffsPO';
import { DiligenceSnapshotEsCompareService } from '../snapshot/diligence.snapshot.es.compare.service';
import { OrgSettingsLogEntity } from '../../../libs/entities/OrgSettingsLogEntity';
import { SettingTypeEnums } from '../../../libs/model/settings/SettingTypeEnums';
import { SettingsService } from '../../settings/settings.service';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { RoverEventEmitter, SystemEvents } from '../../../libs/common/RoverEventEmitter';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';

@Injectable()
export class DiligenceAnalyzeEsService {
  public esClientRead: Client;
  public esClientWrite: Client;
  private readonly indexName: string;
  private readonly logger = QccLogger.getLogger(DiligenceAnalyzeEsService.name);
  private eventEmitter: RoverEventEmitter = RoverEventEmitter.getInstance();
  // private docsToInsert: DiligenceAnalyzeEsDoc[] = [];
  private esBatchSize = 200;

  constructor(
    private readonly configService: ConfigService,
    private readonly snapshotCompareEsService: DiligenceSnapshotEsCompareService,
    private readonly settingsService: SettingsService,
  ) {
    this.esClientRead = new Client({
      nodes: this.configService.esConfig.diligenceAnalyze.nodesQuery,
      ssl: { rejectUnauthorized: false },
    });
    this.esClientWrite = new Client({
      nodes: this.configService.esConfig.diligenceAnalyze.nodesWrite,
      ssl: { rejectUnauthorized: false },
    });
    this.indexName = this.configService.esConfig.diligenceAnalyze.indexName;
    // setInterval(this.flushEs.bind(this), 3000); //每隔3s执行一次写入
    this.eventEmitter.on(SystemEvents.ProcessExit, this.flushEs.bind(this, [true]));
  }

  public async flushEs(force = false): Promise<void> {
    // if (this.docsToInsert.length > 0) {
    //   do {
    //     const items = this.docsToInsert.splice(0, this.esBatchSize);
    //     if (items.length > 0) {
    //       await this._insertDoc(items);
    //     } else {
    //       break;
    //     }
    //   } while (true);
    // }
  }

  public async _insertDoc(toSaveItems: DiligenceAnalyzeEsDoc[]): Promise<void> {
    if (toSaveItems.length > 0) {
      await Bluebird.map(
        chunk(toSaveItems, 500),
        async (items) => {
          this.logger.info(`Flush ${items.length} items to ${this.getWriteIndexName()}`);
          const writeIndexName = this.getWriteIndexName();
          const body = flatMap(items.map((doc) => [{ index: { _index: writeIndexName, _id: doc.id } }, doc]));
          try {
            await this.esClientWrite.bulk({
              index: writeIndexName,
              body,
              refresh: true,
            });
            this.logger.info(`Flush ${items.length} items to ${writeIndexName} success`);
          } catch (error) {
            this.logger.error(`Flush ${items.length} items to ${writeIndexName} failed`, error);
          }
        },
        { concurrency: 2 },
      );
    }
  }

  public getUniqueId(i: { companyId: string; batchIdCurrent: number; batchIdPrevious: number }) {
    return crypto.createHash('md5').update(`${i.companyId}_${i.batchIdCurrent}_${i.batchIdPrevious}`).digest('hex');
  }

  public async saveAnalyzeItem(item: DiligenceAnalyzeEsDoc[]): Promise<void> {
    if (item.length > 0) {
      item.forEach((i) => {
        if (!i.id) {
          i.id = this.getUniqueId(i);
        }
      });
      await this._insertDoc(item);
      // this.docsToInsert.push(...item);
    }
    // if (this.docsToInsert.length >= this.esBatchSize) {
    //   await this._insertDoc(this.docsToInsert.splice(0, this.esBatchSize));
    // }
  }

  /**
   * 搜索分析结果
   * @param searchParams
   * @param includeFields
   * @param levelGroupAssociatedDimensionkeys 当 dimensionLevel1 存在时，需要关联的维度组维度值
   */
  @TraceLog({ throwError: true, spanName: 'searchAnalyzeItems' })
  public async searchAnalyzeItems(
    searchParams: AnalyzedCompanySearchedRequest,
    allDimensionDefinitions: DimensionDefinitionPO[],
    includeFields?: string[],
  ): Promise<DiligenceAnalyzeResponse> {
    const {
      pageSize,
      pageIndex,
      companyIds,
      dimensionLevel1,
      batchIdCurrent,
      batchIdPrevious,
      result: customerRiskLevel,
      labelIds: customerLabels,
      groupIds: customerGroups,
      departments: customerDeps,
      principals: customerPrincipals,
      aggsInfo,
      onlyChangedCompany,
      dimensionKeys,
      searchKey,
      province,
      forCharts,
      isMulitDep,
      diligenceIds,
      creditRateMin,
      creditRateMax,
      creditRateChangeMin,
      creditRateChangeMax,
      onlyCreditRateChanged,
    } = searchParams;
    this.logger.info(`Search analyze items with params: ${JSON.stringify(searchParams)}`);
    // const allLevel2Keys = Array.from(new Set(flatten(allDimensionDefinitions?.map((d) => d.groupKeyList || [])))) || [];
    const level2OwnLevel2keys =
      Array.from(new Set(flatten(allDimensionDefinitions?.filter((d) => d.groupKey == dimensionLevel1).map((d) => d.groupKeyList || [])))) || [];
    let changesDimensionField = 'changes.dimensionKey';
    //1. dimensionLevel1 传值了并且二级维度有值
    if (
      dimensionLevel1 &&
      (level2OwnLevel2keys.length > 1 || (level2OwnLevel2keys.length == 1 && level2OwnLevel2keys[0] != DimensionLevel2Enums.HitOuterBlackList))
    ) {
      //TODO 这里是假设按照现在蔡司的维度设置，如果传了一级维度，二级维度一定有值
      // 这里逻辑判断可能存在潜在风险， 目前发现非蔡司组织也有一个二级维度- HitOuterBlackList
      changesDimensionField = 'changes.dimensionLevel2';
    }
    const query: any = {
      bool: {
        must: [
          {
            term: { batchIdCurrent },
          },
          {
            term: { batchIdPrevious },
          },
        ],
        should: [],
      },
    };
    if (searchKey) {
      query.bool.must.push({
        wildcard: {
          companyName: {
            value: `*${searchKey}*`,
          },
        },
      });
    }
    if (onlyChangedCompany) {
      // 判断两次排查的 风险记录数发生变化
      if (!dimensionLevel1) {
        query.bool.should.push({
          range: {
            increasedTotal: {
              gt: 0,
            },
          },
        });
      } else {
        const scriptParams: any = {};
        if (dimensionKeys?.length) {
          //如果子维度传了值，则只计算子维度变化的公司
          scriptParams.key_to_check = changesDimensionField;
          scriptParams.key_value = dimensionKeys[0];
        } else {
          scriptParams.key_to_check = 'changes.dimensionLevel1';
          scriptParams.key_value = dimensionLevel1;
        }
        query.bool.should.push({
          bool: {
            filter: {
              nested: {
                path: 'changes',
                query: {
                  bool: {
                    must: [
                      {
                        term: {
                          [scriptParams.key_to_check]: {
                            value: scriptParams.key_value,
                          },
                        },
                      },
                      {
                        range: {
                          'changes.increasedValue': {
                            gt: 0,
                          },
                        },
                      },
                    ],
                  },
                },
              },
            },
          },
        });
      }

      // 判断两次排查的 风险等级发生变化
      // 仅当 dimensionLevel不传值的时候，才增加这个
      if (!dimensionLevel1) {
        query.bool.should.push({
          bool: {
            filter: {
              script: {
                script: {
                  source: `
                  if (doc['previousCustomerRiskLevel'].size() > 0 
                     && doc['previousCustomerRiskLevel'].value != null
                     && doc['previousCustomerRiskLevel'].value != '-1'
                     && doc['customerRiskLevel'].size() > 0
                     && doc['customerRiskLevel'].value != null
                     && doc['customerRiskLevel'].value != '-1')
                  { Integer.parseInt(doc['previousCustomerRiskLevel'].value)- Integer.parseInt(doc['customerRiskLevel'].value) != 0 }
                  else { false  }
                  `,
                },
              },
            },
          },
        });
      }

      // 判断两次排查的 企查分发生变化
      if (onlyCreditRateChanged) {
        query.bool.should.push({
          bool: {
            filter: {
              script: {
                script: {
                  source: `
                  if (doc['previousCreditRate'].size() > 0 
                     && doc['previousCreditRate'].value != null
                     && doc['previousCreditRate'].value != -1
                     && doc['creditRate'].size() > 0
                     && doc['creditRate'].value != null
                     && doc['creditRate'].value != -1)
                  { doc['creditRate'].value - doc['previousCreditRate'].value != 0 }
                  else { false  }
                  `,
                },
              },
            },
          },
        });
      }

      query.bool.minimum_should_match = 1;
    }
    if (dimensionKeys?.length) {
      query.bool.must.push({
        nested: {
          path: 'changes',
          query: {
            terms: { [changesDimensionField]: dimensionKeys },
          },
        },
      });
    }
    if (companyIds?.length) {
      query.bool.must.push({
        terms: { companyId: companyIds },
      });
    }
    if (diligenceIds?.length) {
      query.bool.must.push({
        terms: { diligenceIdCurrent: diligenceIds },
      });
    }
    if (dimensionLevel1) {
      query.bool.must.push({
        nested: {
          path: 'changes',
          query: {
            term: { 'changes.dimensionLevel1': dimensionLevel1 },
          },
        },
      });
    }
    if (customerRiskLevel?.length) {
      query.bool.must.push({
        terms: { customerRiskLevel },
      });
    }
    if (customerLabels?.length) {
      query.bool.must.push({
        terms: { customerLabels },
      });
    }
    if (customerPrincipals?.length) {
      query.bool.must.push({
        terms: { customerPrincipals },
      });
    }
    if (customerGroups?.length) {
      query.bool.must.push({
        terms: { customerGroups },
      });
    }
    if (customerDeps?.length) {
      if (customerDeps.includes('-1')) {
        // 包含未分配部门查询
        query.bool.must.push({
          bool: {
            should: [
              { terms: { customerDeps } },
              {
                script: {
                  script: "doc['customerDeps'].length == 0",
                },
              },
            ],
            minimum_should_match: 1,
          },
        });
      } else {
        // 普通部门查询
        query.bool.must.push({
          terms: { customerDeps },
        });
      }
    }
    if (province?.length) {
      query.bool.must.push({
        terms: { customerProvinceCode: province },
      });
    }
    // 企查分范围查询
    if (creditRateMin !== undefined || creditRateMax !== undefined) {
      const creditRateQuery: any = {};
      if (creditRateMin !== undefined) {
        creditRateQuery.gte = creditRateMin;
      }
      if (creditRateMax !== undefined) {
        creditRateQuery.lte = creditRateMax;
      }
      query.bool.must.push({
        range: { creditRate: creditRateQuery },
      });
    }
    // 企查分变化范围查询
    if (creditRateChangeMin !== undefined || creditRateChangeMax !== undefined) {
      const creditRateChangeQuery: any = {};
      if (creditRateChangeMin !== undefined) {
        creditRateChangeQuery.gte = creditRateChangeMin;
      }
      if (creditRateChangeMax !== undefined) {
        creditRateChangeQuery.lte = creditRateChangeMax;
      }
      query.bool.must.push({
        range: { creditRateChange: creditRateChangeQuery },
      });
    }
    if (isMulitDep) {
      query.bool.must.push({
        script: {
          script: "doc['customerDeps'].length > 1",
        },
      });
    }
    let sortObj: any = {};
    const sortOrder = searchParams.sortOrder || 'DESC';
    const sortField = searchParams.sortByKey || searchParams.dimensionLevel1 || 'createDate';
    //如果排序字段是一级维度， 按照一级维度排序
    if (allDimensionDefinitions.map((t) => t.groupKey).some((key) => key === sortField)) {
      sortObj = [
        {
          _script: {
            type: 'number',
            script: {
              source: `
                def sum = 0;
                if(null != params._source.changes) {
                  for (def obj : params._source.changes) {
                    if (obj != null && obj.dimensionLevel1 == params.filterValue) {
                      if (obj.currentHitValue != null) {
                        sum += obj.currentHitValue;
                      }
                      
                    }
                  }
                }
                return sum;
          `,
              params: {
                filterValue: sortField,
              },
            },
            order: sortOrder, // 也可以使用 "asc" 以升序排序
          },
        },
      ];
    } else {
      sortObj = {
        [sortField]: {
          order: sortOrder,
        },
      };
    }

    const body: any = {
      query,
      sort: sortObj,
      size: pageSize,
      from: (pageIndex - 1) * pageSize,
    };
    // 是否返回关联的diligence信息,默认0, 当值为1时，返回的关联信息中包含公司的diligence信息
    if (aggsInfo) {
      const aggsObj: any = {
        allDimensions: {
          filter: {
            match_all: {},
          },
        },
        customerLabels: {
          terms: {
            field: 'customerLabels',
            size: 100,
          },
        },
        customerPrincipals: {
          terms: {
            field: 'customerPrincipals',
            size: 100,
          },
        },
        customerGroups: {
          terms: {
            field: 'customerGroups',
            size: 100,
          },
        },
        customerDeps: {
          terms: {
            field: 'customerDeps',
            size: 100,
          },
        },
        customerRiskLevel: {
          terms: {
            field: 'customerRiskLevel',
            size: 100,
          },
        },
      };
      // 风险看板额外增加的统计项
      if (forCharts) {
        // 包含多个部门的customer统计
        aggsObj['mulit_deps'] = {
          filter: {
            script: {
              script: "doc['customerDeps'].length > 1",
            },
          },
        };
        // 未分配部门
        aggsObj['missing_customerDeps'] = {
          filter: {
            script: {
              script: "doc['customerDeps'].length == 0",
            },
          },
          aggs: {
            riskLevel: {
              terms: {
                field: 'customerRiskLevel',
                size: 100,
              },
            },
          },
        };
        // 部门统计调整
        aggsObj['customerDeps'] = {
          terms: {
            field: 'customerDeps',
            size: 100,
          },
          aggs: {
            riskLevel: {
              terms: {
                field: 'customerRiskLevel',
                size: 100,
              },
            },
          },
        };
        // 省份统计
        aggsObj['customerProvice'] = {
          terms: {
            field: 'customerProvinceCode',
            size: 50,
          },
        };

        // 企查分变化统计
        aggsObj['creditRateChangeStats'] = {
          filters: {
            filters: {
              increased: {
                range: {
                  creditRateChange: {
                    gt: 0,
                  },
                },
              },
              decreased: {
                range: {
                  creditRateChange: {
                    lt: 0,
                  },
                },
              },
              unchanged: {
                term: {
                  creditRateChange: 0,
                },
              },
            },
          },
        };
      }
      if (!dimensionLevel1) {
        const level1Keys = Array.from(new Set(allDimensionDefinitions.map((t) => t.groupKey)));
        level1Keys.forEach((key) => {
          const mustArray: any[] = [
            {
              term: {
                'changes.dimensionLevel1': {
                  value: key,
                },
              },
            },
          ];
          if (onlyChangedCompany) {
            mustArray.push({
              range: {
                'changes.increasedValue': {
                  gt: 0,
                },
              },
            });
          }
          aggsObj[key] = {
            filter: {
              bool: {
                must: [
                  {
                    nested: {
                      path: 'changes',
                      query: {
                        bool: {
                          must: mustArray,
                        },
                      },
                    },
                  },
                ],
              },
            },
          };
        });
      } else {
        //默认先获取二级分组，
        let subDimensions: DimensionTypeEnums[] =
          Array.from(new Set(allDimensionDefinitions.filter((d) => d.groupKey == dimensionLevel1).map((d) => d.key))) || [];
        //如果判断是使用二级维度字段
        if (changesDimensionField === 'changes.dimensionLevel2') {
          subDimensions = level2OwnLevel2keys;
        }
        if (onlyChangedCompany) {
          aggsObj.allDimensions.filter = {
            bool: {
              must: [
                {
                  nested: {
                    path: 'changes',
                    query: {
                      bool: {
                        must: [
                          {
                            range: {
                              'changes.increasedValue': {
                                gt: 0,
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                },
              ],
            },
          };
        }
        subDimensions.forEach((key) => {
          const mustArray: any[] = [
            {
              term: {
                [changesDimensionField]: {
                  value: key, // 这里需要根据二级维度字段来判断
                },
              },
            },
          ];
          if (onlyChangedCompany) {
            mustArray.push({
              range: {
                'changes.increasedValue': {
                  gt: 0,
                },
              },
            });
          }

          aggsObj[key] = {
            filter: {
              bool: {
                must: [
                  {
                    nested: {
                      path: 'changes',
                      query: {
                        bool: {
                          must: mustArray,
                        },
                      },
                    },
                  },
                ],
              },
            },
          };
        });
      }
      body.aggs = aggsObj;
    }
    const params = {
      index: this.getReadIndexName(),
      body,
      track_total_hits: true,
      preference: batchIdCurrent + '',
    };
    if (includeFields?.length) {
      Object.assign(params, { _source_includes: includeFields });
    }

    try {
      const res = await this.esClientRead.search(params);

      const paginationResponse = new DiligenceAnalyzeResponse();
      paginationResponse.total = res.body.hits.total.value;
      paginationResponse.data = res.body.hits.hits.map((item) => plainToInstance(DiligenceAnalyzeEsDoc, item._source));
      paginationResponse.pageIndex = pageIndex;
      paginationResponse.pageSize = pageSize;
      if (aggsInfo) {
        const aggsO = res.body.aggregations;
        paginationResponse.aggs = Object.keys(aggsO).reduce((acc, cur) => {
          if (['customerLabels', 'customerPrincipals', 'customerGroups', 'customerDeps', 'customerRiskLevel', 'customerProvice'].includes(cur)) {
            acc[cur] = aggsO[cur].buckets;
          } else if (cur == 'missing_customerDeps') {
            // 未分配部门
            acc[cur] = aggsO[cur];
          } else if (cur == 'creditRateChangeStats') {
            // 企查分变化统计
            acc[cur] = aggsO[cur].buckets;
          } else {
            acc[cur] = aggsO[cur].doc_count;
          }
          return acc;
        }, {});
      }
      return paginationResponse;
    } catch (error) {
      this.logger.error(`searchAnalyzeItems error: ${JSON.stringify(error)}`);
      this.logger.error(error);
      throw error;
    }
  }

  public async searchByIds(ids: string[]) {
    const res = await this.esClientRead.search({
      index: this.getReadIndexName(),
      body: {
        query: {
          terms: {
            id: ids,
          },
        },
      },
      _source_includes: ['id', 'companyId', 'orgId'],
      track_total_hits: true,
      size: ids.length,
    });
    return res.body.hits.hits.map((i) => i._source);
  }

  /**
   * 当diligence result变更的时候， 更新 kys_diligence_analyze_索引中冗余的 previousCustomerRiskLevel 和  customerRiskLevel
   * @param diligenceId
   * @param riskLevel
   */
  public async updateDiligenceRiskLevel(diligenceId: number, riskLevel: number) {
    return Bluebird.all([
      this.esClientWrite.updateByQuery({
        index: this.getWriteIndexName(),
        body: {
          query: {
            term: {
              diligenceIdCurrent: diligenceId,
            },
          },
          script: {
            source: `
            ctx._source.customerRiskLevel = params.riskLevel;
          `,
            lang: 'painless',
            params: {
              riskLevel,
            },
          },
        },
      }),
      this.esClientWrite.updateByQuery({
        index: this.getWriteIndexName(),
        body: {
          query: {
            term: {
              diligenceIdPrevious: diligenceId,
            },
          },
          script: {
            source: `
            ctx._source.previousCustomerRiskLevel = params.riskLevel;
          `,
            lang: 'painless',
            params: {
              riskLevel,
            },
          },
        },
      }),
    ]);
  }

  /**
   * 当diligence creditRate变更的时候， 更新 kys_diligence_analyze_索引中冗余的 previousCreditRate 和  creditRate
   * @param diligenceId
   * @param creditRate
   */
  public async updateDiligenceCreditRate(diligenceId: number, creditRate: number) {
    return Bluebird.all([
      this.esClientWrite.updateByQuery({
        index: this.getWriteIndexName(),
        body: {
          query: {
            term: {
              diligenceIdCurrent: diligenceId,
            },
          },
          script: {
            source: `
            if (ctx._source.previousCreditRate != null && ctx._source.previousCreditRate != -1) {
              ctx._source.creditRateChange = params.creditRate - ctx._source.previousCreditRate;
            }
            ctx._source.creditRate = params.creditRate;
          `,
            lang: 'painless',
            params: {
              creditRate,
            },
          },
        },
      }),
      this.esClientWrite.updateByQuery({
        index: this.getWriteIndexName(),
        body: {
          query: {
            term: {
              diligenceIdPrevious: diligenceId,
            },
          },
          script: {
            source: `
            if (ctx._source.creditRate != null && ctx._source.creditRate != -1) {
              ctx._source.creditRateChange = ctx._source.creditRate - params.creditRate;
            }
            ctx._source.previousCreditRate = params.creditRate;
          `,
            lang: 'painless',
            params: {
              creditRate,
            },
          },
        },
      }),
    ]);
  }

  /**
   * 当diligence有忽略维度操作的时候 ，找出关联的分析结果并更新他们
   * 这边只负责修改  increasedTotal, decreasedTotal, changes
   * @param diligenceList
   */
  public async reAnalyzeByRelatedDiligence(diligence: DiligenceHistoryEntity) {
    const batchIds =
      diligence?.batchEntities
        ?.filter((b) =>
          [
            BatchBusinessTypeEnums.Diligence_ID,
            BatchBusinessTypeEnums.Diligence_File,
            BatchBusinessTypeEnums.Diligence_Customer,
            BatchBusinessTypeEnums.Diligence_Customer_Analyze,
          ].includes(b.businessType),
        )
        ?.map((i) => i.batchId) || [];

    this.logger.info(`reAnalyzeByRelatedDiligence batchIds: ${batchIds}`);
    if (!batchIds.length) {
      return 0;
    }

    const query = {
      bool: {
        must: [
          {
            term: {
              companyId: diligence.companyId,
            },
          },
        ],
        should: [
          {
            terms: {
              batchIdCurrent: batchIds,
            },
          },
          {
            terms: {
              batchIdPrevious: batchIds,
            },
          },
        ],
        minimum_should_match: 1,
      },
    };
    this.logger.info(`reAnalyzeByRelatedDiligence batchIds: ${batchIds}, query: ${JSON.stringify(query)}`);
    const response = await this.esClientRead.search({
      index: this.getReadIndexName(),
      body: { query },
    });
    const docs: DiligenceAnalyzeEsDoc[] = response.body.hits.hits.map((i) => plainToInstance(DiligenceAnalyzeEsDoc, i._source));
    this.logger.info(`reAnalyzeByRelatedDiligence docs.length: ${docs.length}`);
    if (docs.length) {
      const settings: OrgSettingsLogEntity = await this.settingsService.getOrgSettings(diligence.orgId, SettingTypeEnums.diligence_risk);
      const allDimensionDefinitions = await this.settingsService.getAllDimension(settings);
      const array = await Bluebird.map(docs, async (doc: DiligenceAnalyzeEsDoc) => {
        const compareResult: CompanyAnalyzedDiffsPO[] = await this.snapshotCompareEsService.analyzeDiffsAggsByCompany({
          batchIds: [doc.batchIdCurrent, doc.batchIdPrevious],
          orgId: diligence.orgId,
          companyIds: [diligence.companyId],
        });
        const item = compareResult[0];
        this.logger.info(`reAnalyzeByRelatedDiligence  analyzeDiffsAggsByCompany compareResult : ${JSON.stringify(compareResult)}`);
        if (item) {
          let increasedTotal = 0;
          let decreasedTotal = 0;
          const changes: DiligenceAnalyzeChangesItemPO[] = item.changes.map((c) => {
            const df = find(allDimensionDefinitions, (d) => d.key === c.dimensionKey);
            increasedTotal += c.increasedValue;
            decreasedTotal += c.decreasedValue;
            return {
              ...c,
              dimensionLevel1: df?.groupKey,
            };
          });
          const body = {
            query: {
              term: {
                id: doc.id,
              },
            },
            script: {
              source: `
                ctx._source.changes = params.changes;
                ctx._source.increasedTotal = params.increasedTotal;
                ctx._source.decreasedTotal = params.decreasedTotal;
              `,
              lang: 'painless',
              params: {
                changes: changes || [],
                increasedTotal: increasedTotal || 0,
                decreasedTotal: decreasedTotal || 0,
              },
            },
          };
          this.logger.info(`reAnalyzeByRelatedDiligence  updateByQuery body : ${JSON.stringify(body)}`);
          try {
            const res = await this.esClientWrite.updateByQuery(
              {
                index: this.getWriteIndexName(),
                body,
                refresh: true,
              },
              {
                maxRetries: 3,
              },
            );
            this.logger.info(`reAnalyzeByRelatedDiligence  updateByQuery res : ${JSON.stringify(res.body.updated)}`);
            return res.body.updated;
          } catch (error) {
            this.logger.error(`reAnalyzeByRelatedDiligence updateByQuery error: ${JSON.stringify(error)}`);
            this.logger.error(error);
            if (error?.version_conflicts) {
              await Bluebird.delay(1000);
              this.logger.info(`reAnalyzeByRelatedDiligence updateByQuery 发生版本冲突，重试中`);
              const res = await this.esClientWrite.updateByQuery(
                {
                  index: this.getWriteIndexName(),
                  body,
                  refresh: true,
                },
                {
                  maxRetries: 3,
                },
              );
              return res.body.updated;
            } else {
              throw error;
            }
          }
        }
      });
      const result = array.reduce((acc, cur) => acc + cur, 0);
      this.logger.info(`reAnalyzeByRelatedDiligence  finished result : ${result}`);
      return result;
    }
    return 0;
  }

  /**
   * 覆盖指定客户的指定字段
   * 客户本身的信息   覆盖字段： groupIds, depIds, labels
   * @param customerId
   * @param updateFields
   */
  public async refreshCustomerInfo(customerId: number, updateFields: OverwriteCustomerInfoPO, batchIdCurrent = 0) {
    this.logger.info(`Overwrite customer ${customerId} info`);
    const { groupIds, depIds, labels, principals, customerRiskLevel, province } = updateFields;
    if (!customerId || (!groupIds && !depIds && !labels && !customerRiskLevel && customerRiskLevel != 0 && !province)) {
      return 0;
    }
    const array = [];
    if (groupIds) {
      array.push('ctx._source.customerGroups = params.groupIds;');
    }
    if (depIds) {
      array.push('ctx._source.customerDeps = params.depIds;');
    }
    if (labels) {
      array.push('ctx._source.customerLabels = params.labels;');
    }
    if (principals) {
      array.push('ctx._source.customerPrincipals = params.principals;');
    }
    if (customerRiskLevel || customerRiskLevel == 0) {
      array.push('ctx._source.customerRiskLevel = params.customerRiskLevel;');
    }
    if (province) {
      array.push('ctx._source.customerProvinceCode = params.province;');
    }

    const body: any = {
      query: {
        bool: {
          filter: [{ term: { customerId } }],
        },
      },
      script: {
        source: array.join('\n'),
        lang: 'painless',
        params: {
          groupIds: groupIds,
          depIds: depIds,
          labels: labels,
          customerRiskLevel: customerRiskLevel,
          principals: principals,
          province,
        },
      },
    };
    if (batchIdCurrent) {
      body.query.bool.filter.push({ term: { batchIdCurrent } });
    }
    const res = await this.esClientWrite.updateByQuery({
      index: this.getWriteIndexName(),
      body,
      refresh: true,
    });
    return res.body.updated;
  }

  /**
   * 设置Analyze索引的groupIds, depIds, labels设置为空
   * 同步第三方信息到Analyze索引前，先用该接口将groupIds, depIds, labels设置为空，
   * 然后调用refreshCustomerInfo更新第三方的信息，目的是将已经在第三方列表删除的数据 groupIds, depIds, labels, customerProvinceCode 清空
   * @param batchId
   * @returns
   */
  public async resetAnalyzeCustomerInfo(orgId: number) {
    this.logger.info(`Overwrite org: ${orgId} customer info to empty`);
    const array = [];
    array.push('ctx._source.customerGroups = [];');
    array.push('ctx._source.customerDeps = [];');
    array.push('ctx._source.customerLabels = [];');
    array.push('ctx._source.customerPrincipals = [];');
    array.push('ctx._source.customerProvinceCode = null;');
    const body: any = {
      query: {
        term: { orgId },
      },
      script: {
        source: array.join('\n'),
        lang: 'painless',
        params: {},
      },
    };
    const res = await this.esClientWrite.updateByQuery({
      index: this.getWriteIndexName(),
      body,
      refresh: true,
    });
    return res.body.updated;
  }

  /**
   * 删除指定 batchIdCurrent 不是最新巡检batchId 并且 batchIdCurrent!=-1 的 AnalyzeInfo
   * @param orgId
   * @param batchIdCurrent
   * @returns
   */
  public async deleteBatchAnalyzeInfo(orgId: number, batchIdCurrent: number) {
    this.logger.info(`delete Analyze orgId: ${orgId}, batchIdCurrent: ${batchIdCurrent}, atchIdPrevious != -1 `);
    const body: any = {
      query: {
        bool: {
          must: [{ term: { orgId } }],
          must_not: [{ term: { batchIdPrevious: -1 } }, { term: { batchIdCurrent } }],
        },
      },
    };
    const res = await this.esClientWrite.deleteByQuery({
      index: this.getWriteIndexName(),
      body,
      refresh: true,
    });
    return res.body.deleted;
  }

  /**
   * 增加或者删除 customer 的label
   * @param customerId
   * @param labelId
   * @param operation 0: 增加 1: 删除
   */
  public async updateCustomerLabelId(customerId: number, labelId: string, operation: 0 | 1) {
    this.logger.info(`updateCustomerLabelId customer ${customerId} , labelId: ${labelId}, operation: ${operation}`);
    const script: any = {
      source: `
            if (ctx._source.containsKey('customerLabels')) {
              if (!ctx._source.customerLabels.contains(params.label)) {
                ctx._source.customerLabels.add(params.label);
              }
            } else {
              ctx._source.customerLabels = [params.label];
            }
      `,
      lang: 'painless',
      params: {
        label: labelId,
      },
    };
    if (operation) {
      script.source = `
            if (ctx._source.containsKey('customerLabels')) {
              def labelToRemove = params.label;
              if (ctx._source.customerLabels.contains(labelToRemove)) {
                ctx._source.customerLabels.remove(ctx._source.customerLabels.indexOf(params.label));
              }
            } else {
              ctx._source.customerLabels = [];
            }
      `;
    }
    const body: any = {
      query: {
        term: {
          customerId,
        },
      },
      script,
    };
    this.logger.info(`updateCustomerLabelId es body ${JSON.stringify(body)} `);
    return this.esClientWrite.updateByQuery({
      index: this.getWriteIndexName(),
      body,
      refresh: true,
    });
  }

  /**
   * 增加或者删除 customer 的 dep
   * @param customerId
   * @param depId
   * @param operation 0: 增加 1: 删除
   */
  public async updateCustomerDepId(customerId: number, depId: string, operation: 0 | 1) {
    this.logger.info(`updateCustomerDepId customer ${customerId} , depId: ${depId}, operation: ${operation}`);
    const script = {
      source: `
            if (ctx._source.containsKey('customerDeps')) {
              if (!ctx._source.customerDeps.contains(params.dep)) {
                ctx._source.customerDeps.add(params.dep);
              }
            } else {
              ctx._source.customerDeps = [params.dep];
            }
      `,
      lang: 'painless',
      params: {
        dep: depId,
      },
    };

    if (operation) {
      script.source = `
              if (ctx._source.containsKey('customerDeps')) {
                def depToRemove = params.dep;
                if (ctx._source.customerDeps.contains(depToRemove)) {
                  ctx._source.customerDeps.remove(ctx._source.customerDeps.indexOf(params.dep));
                }
              } else {
                ctx._source.customerDeps = [];
              }
        `;
    }
    const body: any = {
      query: {
        term: {
          customerId,
        },
      },
      script,
    };
    this.logger.info(`updateCustomerDepId es body ${JSON.stringify(body)} `);
    return this.esClientWrite.updateByQuery({
      index: this.getWriteIndexName(),
      body,
      refresh: true,
    });
  }

  /**
   * 删除指定批次的分析结果
   * @param orgId
   * @param batchId
   */
  public async removeAnalyzeResult(orgId: number, batchId?: number, batchField?: 'batchIdCurrent' | 'batchIdPrevious') {
    const body: any = {
      query: {
        bool: {
          must: [
            {
              term: {
                orgId,
              },
            },
          ],
          should: [],
        },
      },
    };
    if (batchId) {
      if (batchField) {
        body.query.bool.must.push({
          term: {
            [batchField]: batchId,
          },
        });
      } else {
        body.query.bool.should.push({
          term: {
            batchIdCurrent: batchId,
          },
        });
        body.query.bool.should.push({
          term: {
            batchIdPrevious: batchId,
          },
        });
        body.query.bool.minimum_should_match = 1;
      }
    }
    const res = await this.esClientWrite.deleteByQuery({
      index: this.getWriteIndexName(),
      body,
      refresh: true,
    });
    return res.body.deleted;
  }

  public getWriteIndexName(): string {
    return `${this.indexName}_write`;
  }

  protected getReadIndexName(): string {
    return `${this.indexName}_query`;
  }
}
