import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { DiligenceAnalyzeService } from './diligence.analyze.service';
import { AnalyzedCompanySearchedRequest } from './po/AnalyzedCompanySearchedRequest';
import { DiligenceAnalyzeResponse } from './po/DiligenceAnalyzeResponse';
import { RoverSessionGuard } from '../../../libs/guards/RoverSession.guard';
import { RoverRolesGuard } from '../../../libs/guards/rover.roles.guard';
import { SearchAnalyzedDimensionDiffsResponse } from '../snapshot/po/SearchAnalyzedDimensionDiffsResponse';
import { SearchDimensionDiffsFromSnapshotRequest } from '../snapshot/po/SearchDimensionDiffsFromSnapshotRequest';

@Controller('diligence/analyze')
@ApiTags('分析巡检/批量排查')
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class DiligenceAnalyzeController {
  constructor(private readonly diligenceAnalyzeService: DiligenceAnalyzeService) {}

  @Post('batch')
  @ApiOperation({ description: '批次公司命中情况分析统计列表' })
  @ApiOkResponse({ type: DiligenceAnalyzeResponse })
  analyzeByBatch(@Body() body: AnalyzedCompanySearchedRequest, @Req() req: any) {
    return this.diligenceAnalyzeService.analyzeAsync(body, req.user?.currentOrg, body.companyDetail);
  }

  @Post('search/dimension/diffs')
  @ApiOperation({ description: '两次批量任务指定公司制定维度差异详情' })
  @ApiOkResponse({ type: SearchAnalyzedDimensionDiffsResponse })
  searchAnalyzedDimensionDiffs(@Body() body: SearchDimensionDiffsFromSnapshotRequest) {
    return this.diligenceAnalyzeService.searchAnalyzedDimension(body);
  }
}
