import { Test, TestingModule } from '@nestjs/testing';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { AppTestModule } from '../../app/app.test.module';
import { DiligenceModule } from '../diligence.module';
import { DiligenceAnalyzeService } from './diligence.analyze.service';
const [testOrgId, testUserId] = generateUniqueTestIds('diligence.analyze.unittest.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(600 * 1000);
describe('DiligenceAnalyzeService - Unit Test', () => {
  let diligenceAnalyzeService: DiligenceAnalyzeService;
  // let diligenceAnalyzeEsService: DiligenceAnalyzeEsService;
  // let snapshotCompareService: DiligenceSnapshotEsCompareService;
  // let batchDiligenceRepo: Repository<BatchDiligenceEntity>;
  // // let savedPos: SnapshotSavedPO[] = [];
  // let batchIds;
  // let orgId: number;
  // let batchRepo: Repository<BatchEntity>;
  // let snapshotTestData: SnapshotTestData;
  // let diligenceHistoryRepo: Repository<DiligenceHistoryEntity>;
  // let entityManager: EntityManager;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DiligenceModule],
    }).compile();
    diligenceAnalyzeService = module.get<DiligenceAnalyzeService>(DiligenceAnalyzeService);
    // // customerRepo = getRepository(CustomerEntity);
    // snapshotCompareService = module.get<DiligenceSnapshotEsCompareService>(DiligenceSnapshotEsCompareService);
    // // snapshotService = module.get<DiligenceSnapshotService>(DiligenceSnapshotService);
    // batchRepo = getRepository(BatchEntity);
    // batchDiligenceRepo = getRepository(BatchDiligenceEntity);
    // diligenceHistoryRepo = getRepository(DiligenceHistoryEntity);
    // diligenceAnalyzeEsService = module.get<DiligenceAnalyzeEsService>(DiligenceAnalyzeEsService);
    // entityManager = getManager();
  });
  afterAll(async () => {
    // await entityManager.connection.close();
  });

  beforeEach(async () => {});
  afterEach(async () => {});

  it.skip('debug- getAllAnalyzeData', async () => {
    const result = await diligenceAnalyzeService.getAllAnalyzeData(56671);
    expect(result.data.length).toBe(5);
  });
});
