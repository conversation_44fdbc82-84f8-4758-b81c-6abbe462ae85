**批量年检分析**

分析看板功能和第三方管理->风险年检功能 类似， 分析看板对蔡司开放， 风险年检对其他用户开发

**业务逻辑**

- 风险年检
  - 用户在风险年检页面手动触发风险年检
  - 创建批量任务
  - 批量任务完成后，发送风险年检消息到 `diligenceAnalyzeQueue` 队列
  - 风险年检消息消费者消费 `diligenceRiskQueue` 队列，触发`DiligenceAnalyzeService.analyze`方法，分析数据保存到 es
    索引 `kys_diligence_analyze_*`中 (分析数据来自快照索引`kys_diligence_snapshot_*`)
  - 用户访问风险年检页面时候，从 es 索引`kys_diligence_analyze_*`中读取分析数据展示
  - 点击单个公司查看详情的时候，具体详情来自 es 索引 `kys_diligence_snapshot_*`

## kys_diligence_analyze 文档结构

```
{
  "kys_diligence_analyze_test" : {
    "mappings" : {
      "properties" : {
        "batchIdCurrent" : {
          "type" : "keyword"
        },
        "batchIdPrevious" : {
          "type" : "keyword"
        },
        "changes" : {
          "type" : "nested",
          "properties" : {
            "currentHitValue" : {
              "type" : "integer"
            },
            "decreasedValue" : {
              "type" : "integer"
            },
            "dimensionKey" : {
              "type" : "keyword"
            },
            "dimensionLevel1" : {
              "type" : "keyword"
            },
            "dimensionLevel2" : {
              "type" : "keyword"
            },
            "increasedValue" : {
              "type" : "integer"
            }
          }
        },
        "companyId" : {
          "type" : "keyword"
        },
        "companyName" : {
          "type" : "keyword"
        },
        "createDate" : {
          "type" : "date"
        },
        "creditRate" : {
          "type" : "long"
        },
        "previousCreditRate" : {
          "type" : "long"
        },
        "creditRateChange" : {
          "type" : "long"
        },
        "customerDepNames" : {
          "type" : "text",
          "fields" : {
            "keyword" : {
              "type" : "keyword",
              "ignore_above" : 256
            }
          }
        },
        "customerDeps" : {
          "type" : "keyword"
        },
        "customerGroupNames" : {
          "type" : "text",
          "fields" : {
            "keyword" : {
              "type" : "keyword",
              "ignore_above" : 256
            }
          }
        },
        "customerGroups" : {
          "type" : "keyword"
        },
        "customerId" : {
          "type" : "keyword"
        },
        "customerLabelNames" : {
          "type" : "text",
          "fields" : {
            "keyword" : {
              "type" : "keyword",
              "ignore_above" : 256
            }
          }
        },
        "customerLabels" : {
          "type" : "keyword"
        },
        "customerPrincipals" : {
          "type" : "keyword"
        },
        "customerProviceCode" : {
          "type" : "keyword"
        },
        "customerProvinceCode" : {
          "type" : "keyword"
        },
        "customerRiskLevel" : {
          "type" : "keyword"
        },
        "decrease" : {
          "type" : "integer"
        },
        "decreasedTotal" : {
          "type" : "long"
        },
        "diligenceIdCurrent" : {
          "type" : "keyword"
        },
        "diligenceIdPrevious" : {
          "type" : "keyword"
        },
        "dimensionKey" : {
          "type" : "keyword"
        },
        "id" : {
          "type" : "keyword"
        },
        "increase" : {
          "type" : "integer"
        },
        "increasedTotal" : {
          "type" : "long"
        },
        "newCustomer" : {
          "type" : "keyword"
        },
        "orgId" : {
          "type" : "keyword"
        },
        "previousCustomerRiskLevel" : {
          "type" : "keyword"
        }
      }
    }
  }
}
```

## kys_snapshot_test 文档结构

```
{
  "kys_snapshot_test_v2" : {
    "mappings" : {
      "dynamic_templates" : [
        {
          "strings" : {
            "match_mapping_type" : "string",
            "mapping" : {
              "type" : "keyword"
            }
          }
        }
      ],
      "properties" : {
        "createDate" : {
          "type" : "date"
        },
        "diligence" : {
          "properties" : {
            "batchId" : {
              "type" : "keyword"
            },
            "companyId" : {
              "type" : "keyword"
            },
            "createDate" : {
              "type" : "date"
            },
            "diligenceAt" : {
              "type" : "date"
            },
            "diligenceId" : {
              "type" : "keyword"
            },
            "id" : {
              "type" : "keyword"
            },
            "orgId" : {
              "type" : "keyword"
            },
            "snapshotId" : {
              "type" : "keyword"
            },
            "status" : {
              "type" : "long"
            },
            "updateDate" : {
              "type" : "date"
            },
            "updateHistory" : {
              "type" : "nested",
              "properties" : {
                "content" : {
                  "type" : "text"
                },
                "createDate" : {
                  "type" : "date"
                },
                "operator" : {
                  "type" : "keyword"
                },
                "operatorId" : {
                  "type" : "long"
                },
                "status" : {
                  "type" : "keyword"
                }
              }
            }
          }
        },
        "dimensionContent" : {
          "type" : "text",
          "index" : false
        },
        "dimensionContentSearch" : {
          "properties" : {
            "amount" : {
              "type" : "long"
            },
            "area" : {
              "type" : "keyword"
            },
            "caseType" : {
              "type" : "keyword"
            },
            "companyName" : {
              "type" : "keyword"
            },
            "courtLevel" : {
              "type" : "keyword"
            },
            "courtName" : {
              "type" : "keyword"
            },
            "dimensionStatus" : {
              "type" : "keyword"
            },
            "judgmentType" : {
              "type" : "keyword"
            },
            "principals" : {
              "type" : "nested",
              "properties" : {
                "keyNo" : {
                  "type" : "keyword"
                },
                "name" : {
                  "type" : "keyword"
                },
                "role" : {
                  "type" : "keyword"
                }
              }
            },
            "publishTime" : {
              "type" : "long"
            },
            "reason" : {
              "type" : "keyword"
            },
            "reasonType" : {
              "type" : "keyword"
            },
            "sourceName" : {
              "type" : "keyword"
            },
            "tags" : {
              "type" : "keyword"
            },
            "trialRound" : {
              "type" : "keyword"
            },
            "year" : {
              "type" : "keyword"
            }
          }
        },
        "dimensionId" : {
          "type" : "keyword"
        },
        "dimensionKey" : {
          "type" : "keyword"
        },
        "dimensionStrategy" : {
          "properties" : {
            "batchId" : {
              "type" : "keyword"
            },
            "companyId" : {
              "type" : "keyword"
            },
            "diligenceId" : {
              "type" : "keyword"
            },
            "id" : {
              "type" : "keyword"
            },
            "orgId" : {
              "type" : "keyword"
            },
            "snapshotId" : {
              "type" : "keyword"
            },
            "strategyId" : {
              "type" : "keyword"
            }
          }
        },
        "id" : {
          "type" : "keyword"
        },
        "relation" : {
          "type" : "join",
          "eager_global_ordinals" : true,
          "relations" : {
            "dimension" : [
              "dimensionStrategy",
              "diligence"
            ]
          }
        }
      }
    }
  }
}
```
