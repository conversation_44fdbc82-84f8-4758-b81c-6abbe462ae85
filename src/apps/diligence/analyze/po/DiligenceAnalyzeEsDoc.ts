import { ApiProperty } from '@nestjs/swagger';
import { DimensionLevel1Enums } from '../../../../libs/enums/diligence/DimensionLevel1Enums';
import { DimensionLevel2Enums } from '../../../../libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '../../../../libs/enums/diligence/DimensionLevel3Enums';
import { CompanyBasicInfo } from '../../../../libs/model/blacklist/RemoveCustomerResponse';

export class DiligenceAnalyzeChangesItemPO {
  @ApiProperty({ description: '变更的字段名' })
  dimensionKey: string;

  @ApiProperty({ description: '维度的level1 级别分组', enum: Object.keys(DimensionLevel1Enums) })
  dimensionLevel1?: string;

  @ApiProperty({
    description: '维度的level2 级别分组',
    enum: [...Object.keys(DimensionLevel1Enums), ...Object.keys(DimensionLevel2Enums), ...Object.keys(DimensionLevel3Enums)],
  })
  dimensionLevel2?: string;

  @ApiProperty({ description: 'batchIdCurrent命中的数量' })
  currentHitValue: number;

  @ApiProperty({ description: 'batchIdCurrent命中的数量相对batchIdPrevious的增量' })
  increasedValue: number;

  @ApiProperty({ description: 'batchIdCurrent命中的数量相对batchIdPrevious的减量' })
  decreasedValue: number;
}

export class DiligenceAnalyzeEsDoc extends CompanyBasicInfo {
  id?: string;

  @ApiProperty({ type: Number })
  batchIdCurrent: number;

  @ApiProperty({ type: Number })
  batchIdPrevious: number;

  @ApiProperty({ type: Number })
  diligenceIdCurrent: number;

  @ApiProperty({ type: Number })
  diligenceIdPrevious: number;

  customerId: number;

  @ApiProperty({ type: Number, description: '1-本批次排查新增的企业，0-上批次排查该企业已经存在' })
  newCustomer: number;

  orgId: number;

  customerRiskLevel: number;

  previousCustomerRiskLevel: number;

  customerGroups: number[];

  customerGroupNames: string;

  customerLabels: string[];

  customerLabelNames: string;

  @ApiProperty({ type: String, isArray: true, description: '客户负责人' })
  customerPrincipals: string[];

  customerDeps: string[];

  customerDepNames: string;

  createDate: Date;

  increasedTotal: number;

  decreasedTotal: number;

  customerProvinceCode: string;

  changes: DiligenceAnalyzeChangesItemPO[];

  creditRate: number; // 企查分

  @ApiProperty({ type: Number, description: '上次排查时的企查分' })
  previousCreditRate: number;

  @ApiProperty({ type: Number, description: '企查分变化值，正数表示上升，负数表示下降' })
  creditRateChange: number;
}
