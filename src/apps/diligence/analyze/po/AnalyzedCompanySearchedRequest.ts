import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayMinSize, IsIn, IsOptional, MaxLength, Min, MinLength } from 'class-validator';
import { PaginationParams } from '../../../../libs/model/common';
import { DimensionLevel1Enums } from '../../../../libs/enums/diligence/DimensionLevel1Enums';
import { Type } from 'class-transformer';
import { DimensionLevel2Enums } from '../../../../libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '../../../../libs/enums/diligence/DimensionLevel3Enums';

export class AnalyzedCompanySearchedRequest extends PaginationParams {
  @ApiProperty({ type: String, description: '当前的batchId' })
  @Min(1)
  @Type(() => Number)
  batchIdCurrent: number;

  @ApiProperty({ type: String, description: '上一次的batchId, -1 表示没有上一次的batchId(分析当前的批次)' })
  @Min(-1)
  @Type(() => Number)
  batchIdPrevious: number;

  @ApiPropertyOptional({ type: String, description: '搜索关键字' })
  @IsOptional()
  @MinLength(2)
  @MaxLength(100)
  searchKey?: string;

  @ApiPropertyOptional({
    description: '是否只返回有变化的公司,默认0 ',
    enum: [0, 1],
    default: 0,
  })
  @IsOptional()
  @IsIn([0, 1])
  onlyChangedCompany?: number;

  @ApiPropertyOptional({
    description: '1-是否分析看板统计,会增加额外聚合条件（部门下子聚合，多部门聚合）， 0-不是,默认0 ',
    enum: [0, 1],
    default: 0,
  })
  @IsOptional()
  @IsIn([0, 1])
  forCharts?: number;

  @ApiPropertyOptional({
    description:
      '是否返回聚合信息,默认0 , 当 值为1 时，返回的聚合信息中包含公司的维度信息（如果dimensionLevel1参数存在,则返回二级维度的聚合，否则返回一级维度的聚合） ',
    enum: [0, 1],
    default: 0,
  })
  @IsOptional()
  @IsIn([0, 1])
  aggsInfo?: number;

  @ApiPropertyOptional({
    type: String,
    isArray: false,
    description: '一级维度key',
    enum: Object.values(DimensionLevel1Enums),
  })
  @IsOptional()
  @IsIn(Object.values(DimensionLevel1Enums))
  dimensionLevel1?: string;

  @ApiPropertyOptional({
    type: String,
    isArray: false,
    description: '二级维度key ，跟 dimensionKeys 二选一',
    enum: [...Object.values(DimensionLevel1Enums), ...Object.values(DimensionLevel2Enums), ...Object.values(DimensionLevel3Enums)],
  })
  @IsOptional()
  @IsIn([...Object.values(DimensionLevel1Enums), ...Object.values(DimensionLevel2Enums), ...Object.values(DimensionLevel3Enums)])
  dimensionLevel2?: string;

  @ApiPropertyOptional({
    type: String,
    isArray: true,
    description: '维度key',
  })
  @IsOptional()
  @ArrayMinSize(0)
  @ArrayMaxSize(50)
  dimensionKeys?: string[];

  @ApiPropertyOptional({ type: String, isArray: true, description: '公司id' })
  @IsOptional()
  @ArrayMinSize(0)
  @ArrayMaxSize(1000)
  companyIds?: string[];

  @ApiPropertyOptional({ description: '客户的当前风险级别，0-低风险，1-中风险，2-高风险' })
  @IsOptional()
  @IsIn([0, 1, 2], { each: true, message: '客户的当前风险级别，0-低风险，1-中风险，2-高风险' })
  @ArrayMinSize(0)
  @ArrayMaxSize(3)
  result?: number[];

  @ApiPropertyOptional({ description: '是否返回关联的diligence信息,默认0, 当值为1时，返回的关联信息中包含公司的diligence信息' })
  @IsOptional()
  @IsIn([0, 1])
  diligenceInfo?: number;

  @ApiProperty({ description: '第三方分组' })
  groupIds?: number[];

  @ApiProperty({ description: '第三方标签' })
  labelIds?: number[];

  @ApiProperty({ description: '第三方部门' })
  departments?: string[];

  @ApiProperty({ description: '第三方省份' })
  province?: string[];

  @ApiProperty({ description: '第三方负责人' })
  principals?: string[];

  @ApiPropertyOptional({
    description: '1-查询归属多个部门的第三方， 0-不是,默认0 ',
    enum: [0, 1],
    default: 0,
  })
  @IsOptional()
  @IsIn([0, 1])
  isMulitDep?: number;

  @ApiPropertyOptional({ type: String, description: '排序字段', enum: Object.values(DimensionLevel1Enums) })
  @IsOptional()
  @IsIn([...Object.values(DimensionLevel1Enums), 'customerRiskLevel', 'companyName', 'creditRate', 'creditRateChange'])
  sortByKey?: string;

  @ApiPropertyOptional({ type: String, description: '排序方式', enum: ['ASC', 'DESC'] })
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC';

  @ApiPropertyOptional({ description: '企查分最小值' })
  @IsOptional()
  creditRateMin?: number;

  @ApiPropertyOptional({ description: '企查分最大值' })
  @IsOptional()
  creditRateMax?: number;

  @ApiPropertyOptional({ description: '企查分变化最小值' })
  @IsOptional()
  creditRateChangeMin?: number;

  @ApiPropertyOptional({ description: '企查分变化最大值' })
  @IsOptional()
  creditRateChangeMax?: number;

  @ApiPropertyOptional({
    description: '是否只查询企查分有变化的公司,默认0 ',
    enum: [0, 1],
    default: 0,
  })
  @IsOptional()
  @IsIn([0, 1])
  onlyCreditRateChanged?: number;

  @ApiPropertyOptional({ description: 'diligenceId 在导出列表时会作为查询条件' })
  @IsOptional()
  diligenceIds?: number[];

  @ApiPropertyOptional({ description: '是否返回公司工商详情' })
  @IsOptional()
  companyDetail?: boolean;
}
