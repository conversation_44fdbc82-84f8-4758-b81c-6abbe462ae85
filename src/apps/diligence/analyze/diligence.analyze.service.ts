import { BadRequestException, Injectable } from '@nestjs/common';
import { DiligenceAnalyzeEsService } from './diligence.analyze.es.service';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { InjectRepository } from '@nestjs/typeorm';
import { CustomerEntity } from '../../../libs/entities/CustomerEntity';
import { Repository } from 'typeorm';
import { DiligenceSnapshotEsCompareService } from '../snapshot/diligence.snapshot.es.compare.service';
import { CompanyAnalyzedDiffsPO } from '../snapshot/po/CompanyAnalyzedDiffsPO';
import { DiligenceAnalyzeChangesItemPO, DiligenceAnalyzeEsDoc } from './po/DiligenceAnalyzeEsDoc';
import { chunk, find } from 'lodash';
import * as Bluebird from 'bluebird';
import Redlock from 'redlock';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { DiligenceAnalyzeResponse } from './po/DiligenceAnalyzeResponse';
import { AnalyzedCompanySearchedRequest } from './po/AnalyzedCompanySearchedRequest';
import { SettingsService } from '../../settings/settings.service';
import { OrgSettingsLogEntity } from '../../../libs/entities/OrgSettingsLogEntity';
import { SettingTypeEnums } from '../../../libs/model/settings/SettingTypeEnums';
import { SearchDimensionDiffsFromSnapshotRequest } from '../snapshot/po/SearchDimensionDiffsFromSnapshotRequest';
import { SearchAnalyzedDimensionDiffsResponse } from '../snapshot/po/SearchAnalyzedDimensionDiffsResponse';
import { DiligenceHistoryEntity } from '../../../libs/entities/DiligenceHistoryEntity';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionScorePO } from '../../../libs/model/diligence/pojo/dimension/DimensionScorePO';
import { BatchDiligenceEntity } from '../../../libs/entities/BatchDiligenceEntity';
import { GroupsCustomerService } from '../../element/groups.customer.service';
import { GroupType } from '../../../libs/model/element/CreateGroupModel';
import { resultKv } from '../../../libs/constants/common';
import { LabelService } from '../../element/label.service';
import { DepartmentService } from '../../element/department.service';
import { LabelType } from '../../../libs/model/element/CreateLabelModel';
import { DepartmentTypeEnum } from '../../../libs/enums/department/DepartmentTypeEnum';
import { CompanySearchService } from '../../company/company-search.service';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { BatchStatusEnums } from '../../../libs/enums/batch/BatchStatusEnums';
import { SecurityService } from '../../../libs/config/security.service';
import { RoverUser } from '../../../libs/model/common';
import { DiligenceAnalyzeResponseItemPO } from './po/DiligenceAnalyzeResponseItemPO';
import { fetchAll } from '../../../libs/utils/utils';
import { AnalyzedInfoResponse } from './po/AnalyzedInfoResponse';
import { ExcelHelper } from '../../utils/excel/excel.helper';
import { DiligenceAnalyzeTypeEnums } from '../../../libs/model/charts/DiligenceAnalyzeMessagePO';
import { QueueService } from '../../../libs/config/queue.service';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { UserService } from 'apps/user/user.service';

@Injectable()
export class DiligenceAnalyzeService {
  public diligenceAnalyzeQueue: RabbitMQ;
  private readonly logger = QccLogger.getLogger(DiligenceAnalyzeService.name);
  private readonly redlock: Redlock;

  constructor(
    private readonly analyzeEsService: DiligenceAnalyzeEsService,
    private readonly snapshotEsCompareService: DiligenceSnapshotEsCompareService,
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    private readonly redisService: RedisService,
    private readonly settingsService: SettingsService,
    private readonly groupsCustomerService: GroupsCustomerService,
    private readonly labelService: LabelService,
    private readonly departmentService: DepartmentService,
    private readonly companySearchService: CompanySearchService,
    private readonly securityService: SecurityService,
    private readonly queueService: QueueService,
    private readonly userService: UserService,
  ) {
    this.redlock = new Redlock([this.redisService.getClient()], {
      retryCount: 2, // retry only 2 times then fails
    });
    this.diligenceAnalyzeQueue = this.queueService.diligenceAnalyzeQueue;
  }

  // @Cacheable({ ttlSeconds: 60 })
  public async getCustomers(orgId: number, batchIdCurrent: number) {
    return this.getCustomerQb(orgId, batchIdCurrent).getMany();
  }

  public getCustomerQb(orgId: number, batchIdCurrent: number) {
    return this.customerRepo
      .createQueryBuilder('c')
      .innerJoin(
        (qb) => {
          return qb
            .select('DISTINCT c.id')
            .from('customer', 'c')
            .innerJoin('due_diligence', 'diligence', 'c.company_id = diligence.company_id')
            .innerJoin('batch_diligence', 'batch', 'diligence.id = batch.diligence_id')
            .where('c.org_id = :orgId AND batch.batch_id IN (:...batchIds)', {
              orgId: orgId,
              batchIds: [batchIdCurrent],
            });
        },
        'sub',
        'c.id = sub.id',
      )
      .leftJoinAndSelect('c.labels', 'l')
      .leftJoinAndSelect('c.departments', 'd')
      .leftJoinAndSelect('c.company', 'company');
  }

  private async analyzeSearch(params: AnalyzedCompanySearchedRequest, orgId: number, companyDetail) {
    const { batchIdCurrent, batchIdPrevious } = params;
    const toCompareBatchIds = [batchIdCurrent];
    if (batchIdPrevious > -1) {
      toCompareBatchIds.push(batchIdPrevious);
    }

    const [batchList, batchCount] = await this.batchRepo
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.creator', 'creator')
      .select(['batch.batchId', 'batch.createDate', 'batch.batchInfo', 'creator.name', 'creator.userId', 'creator.phone'])
      .where('batch.orgId = :orgId', { orgId })
      .andWhere('batch.batchId in (:...toCompareBatchIds)', { toCompareBatchIds })
      .getManyAndCount();
    // 判断两个batchId在不在传过来的指定org中
    if (batchCount != toCompareBatchIds.length) {
      throw new BadRequestException(`batchId ${toCompareBatchIds.join(',')} are not exist in current org=${orgId}`);
    }
    let response = new DiligenceAnalyzeResponse();
    response.status = 'Analyzing';
    const analyzedRes: DiligenceAnalyzeResponse = await this.searchAnalyzedCompany(
      {
        batchIdCurrent,
        batchIdPrevious,
        pageSize: 0,
        pageIndex: 1,
      },
      orgId,
      false,
    );

    // 判断是否已经全部分析完毕， 如果es中 统计出来的公司的数量 大于等于 客户的数量 - 一页，说明已经全部分析完毕
    if (analyzedRes.total > 0) {
      //返回查询结果
      response = await this.searchAnalyzedCompany(params, orgId, companyDetail);
      response.status = 'OK';
    }
    response.currentBatchInfo = batchList.find((b) => b.batchId === batchIdCurrent);
    return response;
  }

  @TraceLog({ throwError: true })
  async analyze(params: AnalyzedCompanySearchedRequest, orgId: number, companyDetail = false): Promise<DiligenceAnalyzeResponse> {
    const { batchIdCurrent, batchIdPrevious } = params;

    const searchResponse = await this.analyzeSearch(params, orgId, companyDetail);
    if (searchResponse.status == 'OK') {
      return searchResponse;
    }

    // 未查到分析数据，开始执行分析
    try {
      await this.redlock.acquire([`batchIdCurrent_${batchIdCurrent}_batchIdPrevious_${batchIdPrevious}_${orgId}`], 30000);
    } catch (e) {
      searchResponse.message = '分析已经执行(重复请求)，请稍后获取结果';
      searchResponse.status = 'Analyzing';
      return searchResponse;
    }

    const settings: OrgSettingsLogEntity = await this.settingsService.getOrgSettings(orgId, SettingTypeEnums.diligence_risk);
    const allDimensionDefinitions: DimensionDefinitionPO[] = await this.settingsService.getAllDimension(settings);
    const pagination = {
      pageSize: 1000,
      pageIndex: 1,
    };
    do {
      const batchDiligenceList = await this.batchDiligenceRepo.find({
        where: {
          batchId: batchIdCurrent,
        },
        skip: (pagination.pageIndex - 1) * pagination.pageSize,
        take: pagination.pageSize,
        relations: ['diligenceEntity'],
      });
      if (batchDiligenceList.length > 0) {
        let toAnalyzeCompanyIds = batchDiligenceList.map((batchDiligence) => batchDiligence.diligenceEntity?.companyId).filter((t) => t);
        if (toAnalyzeCompanyIds.length) {
          const existingSearchRes = await this.analyzeEsService.searchAnalyzeItems(
            {
              batchIdCurrent,
              batchIdPrevious,
              pageSize: batchDiligenceList.length,
              pageIndex: 1,
              companyIds: toAnalyzeCompanyIds,
            },
            allDimensionDefinitions,
            ['id', 'companyId'],
          );
          if (existingSearchRes.data.length > 0) {
            toAnalyzeCompanyIds = toAnalyzeCompanyIds.filter((companyId) => {
              return !existingSearchRes.data.some((doc) => doc.companyId === companyId);
            });
          }

          if (toAnalyzeCompanyIds.length > 0) {
            const chunkSize = Math.ceil(toAnalyzeCompanyIds.length / 500);
            await Bluebird.map(
              chunk(toAnalyzeCompanyIds, 50),
              async (chunkCompanyIds: string[]) => {
                const [compareResult, customers, lastDiligenceEntities] = await Bluebird.all([
                  this.snapshotEsCompareService.analyzeDiffsAggsByCompany({
                    batchIds: [batchIdCurrent, batchIdPrevious],
                    orgId,
                    companyIds: chunkCompanyIds,
                  }),
                  this.getCustomerQb(orgId, batchIdCurrent).where('c.company_id IN (:...companyIds)', { companyIds: chunkCompanyIds }).getMany(),
                  this.getDiligenceWithBatch(orgId, batchIdPrevious, chunkCompanyIds),
                ]);
                const currentDiligenceEntities = batchDiligenceList
                  .map((batchDiligence) => batchDiligence.diligenceEntity)
                  .filter((t) => chunkCompanyIds.includes(t?.companyId));

                const docs: DiligenceAnalyzeEsDoc[] = currentDiligenceEntities.map((currentDiligence: DiligenceHistoryEntity) => {
                  const lastDiligence: DiligenceHistoryEntity = find(lastDiligenceEntities, (d) => d.companyId === currentDiligence.companyId);
                  // 是否当前batch新增加的company, lastDiligence不存在 newCustomer = 1
                  const newCustomer = lastDiligence?.id ? 0 : 1;

                  const customerEntity: CustomerEntity = find(customers, (c) => c.companyId === currentDiligence.companyId);
                  const comparedPO: CompanyAnalyzedDiffsPO = find(compareResult, (item) => item.companyId === currentDiligence.companyId);
                  let increasedTotal = 0;
                  let decreasedTotal = 0;
                  const changes: DiligenceAnalyzeChangesItemPO[] =
                    currentDiligence.details?.originalHits?.map((originalHitScorePO: DimensionScorePO) => {
                      const df = find(allDimensionDefinitions, (d) => d.key === originalHitScorePO.key);
                      // newCustomer = 1 时不计算 increasedValue 和 decreasedValue
                      if (!newCustomer && comparedPO) {
                        const comparedDimension = find(comparedPO.changes, (d) => d.dimensionKey === originalHitScorePO.key);
                        if (comparedDimension) {
                          increasedTotal += comparedDimension.increasedValue;
                          decreasedTotal += comparedDimension.decreasedValue;

                          const obj = {
                            ...comparedDimension,
                            // currentHitValue: originalHitScorePO.totalHits,
                            dimensionLevel1: df?.groupKey,
                            dimensionLevel2: df?.groupKeyList ? df.groupKeyList[0] : null,
                          };
                          if (batchIdPrevious < 0) {
                            //bathIdPrevious < 0 说明是统计自身，不需要计算增量值, 否则就是跟其他batch增量分析，需要计算增量值
                            increasedTotal = 0;
                            decreasedTotal = 0;
                            obj.increasedValue = 0;
                            obj.decreasedValue = 0;
                          }
                          return obj;
                        } else {
                          return {
                            increasedValue: 0,
                            decreasedValue: 0,
                            currentHitValue: originalHitScorePO.totalHits,
                            dimensionKey: originalHitScorePO.key,
                            dimensionLevel1: df?.groupKey,
                            dimensionLevel2: df?.groupKeyList ? df.groupKeyList[0] : null,
                          };
                        }
                      } else {
                        return {
                          increasedValue: 0,
                          decreasedValue: 0,
                          currentHitValue: originalHitScorePO.totalHits,
                          dimensionKey: originalHitScorePO.key,
                          dimensionLevel1: df?.groupKey,
                          dimensionLevel2: df?.groupKeyList ? df.groupKeyList[0] : null,
                        };
                      }
                    }) || [];

                  // 添加企查分变化作为特殊的dimension
                  if (currentDiligence.creditRate && lastDiligence?.creditRate) {
                    const creditRateChange = currentDiligence.creditRate - lastDiligence.creditRate;
                    if (creditRateChange !== 0) {
                      changes.push({
                        dimensionKey: 'creditRate',
                        dimensionLevel1: 'creditRate',
                        dimensionLevel2: null,
                        currentHitValue: currentDiligence.creditRate,
                        increasedValue: creditRateChange > 0 ? creditRateChange : 0,
                        decreasedValue: creditRateChange < 0 ? Math.abs(creditRateChange) : 0,
                      });

                      if (creditRateChange > 0) {
                        increasedTotal += creditRateChange;
                      } else {
                        decreasedTotal += Math.abs(creditRateChange);
                      }
                    }
                  }

                  const obj: DiligenceAnalyzeEsDoc = {
                    id: this.analyzeEsService.getUniqueId({
                      batchIdCurrent,
                      batchIdPrevious,
                      companyId: currentDiligence.companyId,
                    }),
                    companyId: currentDiligence.companyId,
                    batchIdCurrent,
                    batchIdPrevious,
                    increasedTotal: increasedTotal || 0,
                    decreasedTotal: decreasedTotal || 0,
                    orgId,
                    changes,
                    newCustomer,
                    companyName: currentDiligence.name,
                    customerLabels: customerEntity?.labels?.map((label) => label.labelId + '') || [],
                    customerLabelNames: customerEntity?.labels?.map((label) => label.name).join(',') || '-',
                    // 公司负责人
                    customerPrincipals: customerEntity?.principal?.split('|') || [''],
                    customerId: customerEntity?.customerId,
                    customerGroups: customerEntity?.groupId ? [customerEntity.groupId] : [-1],
                    customerGroupNames: customerEntity?.group?.name || '-',
                    customerDeps: customerEntity?.departments?.map((dep) => dep.departmentId + '') || [],
                    customerDepNames: customerEntity?.departments?.map((dep) => dep.name).join(',') || '-',
                    // 公司归属的省份
                    customerProvinceCode: customerEntity?.company?.province,
                    diligenceIdCurrent: currentDiligence.id,
                    customerRiskLevel: currentDiligence.result || currentDiligence.result == 0 ? currentDiligence.result : -1,
                    previousCustomerRiskLevel: lastDiligence && (lastDiligence.result || lastDiligence.result == 0) ? lastDiligence.result : -1,
                    diligenceIdPrevious: lastDiligence?.id || -1,
                    createDate: new Date(),
                    creditRate: currentDiligence.creditRate,
                    previousCreditRate: lastDiligence?.creditRate || -1,
                    creditRateChange: currentDiligence.creditRate && lastDiligence?.creditRate ? currentDiligence.creditRate - lastDiligence.creditRate : 0,
                  };
                  return obj;
                });

                await this.analyzeEsService.saveAnalyzeItem(docs);
              },
              { concurrency: Math.max(5, chunkSize) },
            );
          }
        }

        if (batchDiligenceList.length < pagination.pageSize || batchDiligenceList.length === 0) {
          break;
        }
        pagination.pageIndex++;
      } else {
        this.logger.info(`batchId ${batchIdCurrent} has no company to analyze`);
        break;
      }
    } while (true);
    // await this.analyzeEsService.flushEs();
    const analyzeResponse = await this.searchAnalyzedCompany(params, orgId, companyDetail);
    analyzeResponse.currentBatchInfo = searchResponse.currentBatchInfo;
    return analyzeResponse;
  }

  async getBatchItemsPageForOpenApi(params: AnalyzedCompanySearchedRequest, orgId: number) {
    return await this.searchAnalyzedCompany(params, orgId, false);
  }

  @TraceLog({ throwError: true })
  async analyzeAsync(params: AnalyzedCompanySearchedRequest, orgId: number, companyDetail = false): Promise<DiligenceAnalyzeResponse> {
    const { batchIdCurrent, batchIdPrevious } = params;
    const searchResponse = await this.analyzeSearch(params, orgId, companyDetail);
    if (searchResponse.status == 'OK') {
      return searchResponse;
    }

    searchResponse.message = '分析已经执行(重复请求)，请稍后获取结果';
    searchResponse.status = 'Analyzing';

    // 未查到分析数据，开始执行分析
    try {
      const lock = await this.redlock.acquire([`batchIdCurrent_${batchIdCurrent}_batchIdPrevious_${batchIdPrevious}_${orgId}`], 60000);
      if (!lock) {
        this.logger.info(`analyzeAsync sendMessage: batchIdCurrent_${batchIdCurrent}_batchIdPrevious_${batchIdPrevious}_${orgId}`);
        await this.diligenceAnalyzeQueue.sendMessage({
          orgId,
          operationType: DiligenceAnalyzeTypeEnums.AnalyzeDiligence,
          data: {
            batchIdCurrent,
            batchIdPrevious,
          },
        });
        // } else {
        //   this.logger.info(`analyzeAsync locked key: batchIdCurrent_${batchIdCurrent}_batchIdPrevious_${batchIdPrevious}_${orgId}`);
      }
      // 执行分析
      return searchResponse;
    } catch (e) {
      return searchResponse;
    }
  }

  public async getDiligenceWithBatch(orgId: number, batchId: number, companyIds: string[]): Promise<DiligenceHistoryEntity[]> {
    const diligenceEntities = await this.diligenceRepo
      .createQueryBuilder('diligence')
      .where('diligence.orgId = :orgId AND diligence.companyId IN (:...companyIds)', {
        companyIds,
        orgId,
      })
      .leftJoin('diligence.batchEntities', 'batch')
      .andWhere('batch.batchId = :batchId', { batchId })
      .andWhere('batch.businessType IN (:...types)', {
        types: [
          BatchBusinessTypeEnums.Diligence_ID,
          BatchBusinessTypeEnums.Diligence_File,
          BatchBusinessTypeEnums.Diligence_Customer,
          BatchBusinessTypeEnums.Diligence_Customer_Analyze,
        ],
      })
      // .select(['diligence.id', 'diligence.companyId', 'diligence.result',])
      .orderBy('diligence.id', 'DESC')
      .getMany();
    return diligenceEntities;
  }

  /**
   * 查询分析结果
   * @param params
   * @param orgId
   * @param companyDetail 是否需要返回公司工商详情
   */
  @TraceLog({ throwError: true, spanName: 'searchAnalyzedCompany' })
  async searchAnalyzedCompany(params: AnalyzedCompanySearchedRequest, orgId: number, companyDetail = false): Promise<DiligenceAnalyzeResponse> {
    const settings: OrgSettingsLogEntity = await this.settingsService.getOrgSettings(orgId, SettingTypeEnums.diligence_risk);
    const allDimensionDefinitions = await this.settingsService.getAllDimension(settings);

    // 针对蔡司组织，可能存在的二级分组

    const response = await this.analyzeEsService.searchAnalyzeItems(params, allDimensionDefinitions);
    if (params.diligenceInfo && response.data.length > 0) {
      //补充最新batchId 关联的company的diligenceInfo
      const diligenceIds = response.data.map((doc) => doc.diligenceIdCurrent);
      const diligenceEntities = await this.diligenceRepo
        .createQueryBuilder('diligence')
        .leftJoinAndSelect('diligence.editor', 'editor')
        .select(['diligence', 'editor.name', 'editor.phone'])
        .where('diligence.id in (:...diligenceIds)', { diligenceIds })
        .getMany();
      response.data.forEach((doc) => {
        const diligenceEntity = find(diligenceEntities, (d) => d.companyId === doc.companyId);
        doc.diligenceInfo = diligenceEntity;
      });
    }

    if (response?.aggs) {
      // 处理聚合返回的部门、分组、标签、等级
      const aggsKeys = Object.keys(response.aggs);
      if (aggsKeys.includes('customerGroups')) {
        const groupKV = await this.groupsCustomerService.getGroupKV(orgId, GroupType.CustomerGroup);
        response.aggs['customerGroups'] = response.aggs['customerGroups'].map((v) => {
          return { groupId: +v.key, count: v.doc_count, name: groupKV[v.key] };
        });
      }
      if (aggsKeys.includes('customerLabels')) {
        const labelKV = await this.labelService.getLabelKV(orgId, LabelType.CustomerLabel, true);
        response.aggs['customerLabels'] = response.aggs['customerLabels'].map((v) => {
          return { labelId: +v.key, count: v.doc_count, name: labelKV[v.key] };
        });
      }
      if (aggsKeys.includes('customerRiskLevel')) {
        response.aggs['customerRiskLevel'] = response.aggs['customerRiskLevel'].map((v) => {
          return { level: +v.key, count: v.doc_count, name: resultKv[v.key] };
        });
      }
      if (aggsKeys.includes('customerPrincipals')) {
        response.aggs['customerPrincipals'] = response.aggs['customerPrincipals'].map((v) => {
          return { value: v.key, count: v.doc_count, name: v?.key || '无负责人' };
        });
      }
      if (aggsKeys.includes('customerProvice')) {
        response.aggs['customerProvice'] = response.aggs['customerProvice'].map((v) => {
          return { code: v.key, count: v.doc_count };
        });
      }
      if (aggsKeys.includes('customerDeps')) {
        const depKV = await this.departmentService.getDepartmentKV(orgId, DepartmentTypeEnum.Customer);
        response.aggs['customerDeps'] = response.aggs['customerDeps'].map((v) => {
          const level = {};
          // 分析看板需要聚合部门下每个风险等级的公司数
          if (v?.riskLevel?.buckets?.length) {
            for (const bucket of v.riskLevel.buckets) {
              level[bucket.key] = bucket.doc_count;
            }
          }
          return { departmentId: +v.key, count: v.doc_count, name: depKV[v.key], level };
        });
      }
      if (aggsKeys.includes('missing_customerDeps')) {
        const count = response.aggs['missing_customerDeps']?.doc_count || 0;
        const level = {};
        // 分析看板需要聚合部门下每个风险等级的公司数
        if (response.aggs['missing_customerDeps']?.riskLevel?.buckets?.length) {
          for (const bucket of response.aggs['missing_customerDeps'].riskLevel.buckets) {
            level[bucket.key] = bucket.doc_count;
          }
        }
        response.aggs['missing_customerDeps'] = { departmentId: '-1', count, name: '未分配部门', level };
      }
    }
    if (companyDetail && response?.data?.length) {
      //load company detail
      const companyBusinessInfoMap = await this.companySearchService.getCompanyBusinessInfoMap(response.data);
      response.data.forEach((doc) => {
        const companyBusinessInfo = companyBusinessInfoMap.get(doc.companyId);
        if (companyBusinessInfo) {
          doc.companyDetail = companyBusinessInfo;
        }
        if (response?.aggs) {
          doc.customerGroupNames =
            response?.aggs['customerGroups']
              ?.filter((m) => doc.customerGroups.includes(Number(m.groupId)))
              ?.map((m) => m.name)
              ?.join(',') || '-';
          doc.customerLabelNames =
            response?.aggs['customerLabels']
              ?.filter((m) => doc.customerLabels.includes(m.labelId.toString()))
              ?.map((m) => m.name)
              ?.join(',') || '-';
          doc.customerDepNames =
            response?.aggs['customerDeps']
              ?.filter((m) => doc.customerDeps.includes(m.departmentId.toString()))
              ?.map((m) => m.name)
              ?.join(',') || '-';
        }
      });
    }
    return response;
  }

  async searchAnalyzedDimension(params: SearchDimensionDiffsFromSnapshotRequest): Promise<SearchAnalyzedDimensionDiffsResponse> {
    return this.snapshotEsCompareService.searchDimensionDiffsByBatch(params);
  }

  /**
   * 当指定的diligence 有维度被忽略了，需要更新相关联的所有batch的分析结果
   * @param orgId
   * @param diligenceId
   */
  public async reAnalyzeByRelatedDiligence(orgId: number, diligenceId: number) {
    const diligenceHistoryEntity = await this.getDiligenceHasTargetBatch(orgId, diligenceId);
    if (diligenceHistoryEntity) {
      const docs = await this.analyzeEsService.reAnalyzeByRelatedDiligence(diligenceHistoryEntity);
    }
  }

  /**
   * 根据diligenceId 过滤批量排查的diligence
   * @param orgId
   * @param batchId
   * @private
   */
  private async getDiligenceHasTargetBatch(orgId: number, diligenceId: number): Promise<DiligenceHistoryEntity> {
    const businessTypes = [
      BatchBusinessTypeEnums.Diligence_File,
      BatchBusinessTypeEnums.Diligence_ID,
      BatchBusinessTypeEnums.Diligence_Customer,
      BatchBusinessTypeEnums.Diligence_Customer_Analyze,
    ];
    const diligenceEntity = await this.diligenceRepo
      .createQueryBuilder('diligence')
      .where('diligence.orgId = :orgId AND diligence.id = :diligenceId', { orgId, diligenceId })
      .leftJoinAndSelect('diligence.batchEntities', 'batchEntities')
      .andWhere('batchEntities.businessType in (:...businessTypes)', { businessTypes })
      .getOne();
    if (diligenceEntity) {
      diligenceEntity.batchEntities = diligenceEntity.batchEntities.filter((batch) => {
        return businessTypes.includes(batch.businessType);
      });
    }
    return diligenceEntity?.batchEntities?.length > 0 ? diligenceEntity : null;
  }

  /**
   * 获取当前年检与上次年检对比的分析数据
   * @param currentBatchId
   */
  public async getAllAnalyzeData(currentBatchId: number): Promise<AnalyzedInfoResponse> {
    //判断当前 batch是否存在
    const currentBatch = await this.batchRepo.findOne(currentBatchId);
    // const user = Object.assign(new RoverUser(), { currentOrg: currentBatch.orgId, userId: currentBatch.creatorId });
    // 使用 getRoverUser 获取当前用户
    const user = await this.userService.getRoverUser(currentBatch.creatorId, currentBatch.orgId);
    //先查询 previousBatchId
    const previousBatch = await this.getPreviousBatch(user, currentBatchId);
    const searchCondition = new AnalyzedCompanySearchedRequest();
    searchCondition.batchIdCurrent = currentBatchId;
    searchCondition.batchIdPrevious = previousBatch ? previousBatch.batchId : -1;
    searchCondition.companyDetail = false;
    searchCondition.diligenceInfo = 1; //需要返回diligenceInfo
    searchCondition.pageSize = 100; //默认一次查询100条
    const analyzeData: DiligenceAnalyzeResponseItemPO[] = await fetchAll(this.analyze.bind(this), [], searchCondition, currentBatch.orgId, true);
    //获取 orgSetting
    const orgSetting = await this.settingsService.getOrgSettings(
      currentBatch.orgId,
      SettingTypeEnums.diligence_risk,
      analyzeData[0]?.diligenceInfo.orgSettingsId,
    );
    return new AnalyzedInfoResponse(
      ExcelHelper.transAnalyzeDataToExcelItemData(analyzeData, orgSetting, BatchBusinessTypeEnums.Analyze_Record_Export),
      currentBatch,
      previousBatch,
    );
  }

  /**
   * 获取该用户上一次年检的 batch
   * @param user
   * @param currentBatchId
   * @private
   */
  private async getPreviousBatch(user: RoverUser, currentBatchId: number): Promise<BatchEntity> {
    const qb = this.batchRepo
      .createQueryBuilder('batch')
      .where('batch.orgId = :orgId AND batch.batchId < :currentBatchId', { orgId: user.currentOrg, currentBatchId })
      .andWhere('batch.businessType = :businessType', { businessType: BatchBusinessTypeEnums.Diligence_Customer_Analyze })
      .andWhere('batch.status = :status', { status: BatchStatusEnums.Done })
      .orderBy('batch.batchId', 'DESC');
    // 找到当前用户 有数据权限的 businessType，加入查询条件
    const querySql = this.securityService.getBatchPermissionSql(BatchBusinessTypeEnums.Diligence_Customer_Analyze, user);
    if (querySql) {
      qb.andWhere(`(${querySql})`);
    } else {
      // 没找到有数据权限的businessType，返回0
      return null;
    }
    return qb.limit(1).getOne();
  }
}
