import { BadRequestException, Body, Controller, Get, Param, ParseIntPipe, Post, Request, UseGuards } from '@nestjs/common';
import { ApiCookieAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { EvaluationService } from './evaluation/evaluation.service';
import { GetCompanyDiligenceHistoryParams, GetDiligenceResultParams } from 'libs/model/diligence/pojo/req&res/GetDiligenceResultParams';
import { PaginationParams, RoverUser } from 'libs/model/common';
import { RoverSessionGuard } from 'libs/guards/RoverSession.guard';
import { DiligenceHistoryService } from './details/diligence.history.service';
import { DiligenceHistoryRequest } from 'libs/model/diligence/pojo/history/DiligenceHistoryRequest';
import { DiligenceHistoryResponse } from 'libs/model/diligence/pojo/history/DiligenceHistoryResponse';
import { UpdateDiligenceResultRequest } from 'libs/model/diligence/pojo/req&res/UpdateDiligenceResultRequest';
import { DiligenceResponseV2 } from 'libs/model/diligence/pojo/req&res/DiligenceResponseV2';
import { DiligenceRemarkEntity } from 'libs/entities/DiligenceRemarkEntity';
import { DiligenceModifyService } from './details/diligence.modify.service';
import { DiligencePDFService } from './diligence.pdf.service';
import { RoverRolesGuard } from 'libs/guards/rover.roles.guard';
import { DiligenceHistoryEntity } from '../../libs/entities/DiligenceHistoryEntity';
import { SecurityService } from '../../libs/config/security.service';
import { RoverExceptions } from '../../libs/exceptions/exceptionConstants';
import { CompaniesResultRequest } from '../../libs/model/diligence/pojo/history/CompaniesResultRequest';
import {
  DiligenceSuspectRelationsRemarkRequest,
  GetDiligenceVerificationRequest,
} from '../../libs/model/diligence/pojo/req&res/DiligenceSuspectRelationsRemarkRequest';
import { DiligenceSuspectRelationsRemarkEntity } from '../../libs/entities/DiligenceSuspectRelationsRemarkEntity';

@Controller('diligence')
@ApiTags('准入调查')
@ApiCookieAuth()
@UseGuards(RoverSessionGuard, RoverRolesGuard)
export class DiligenceController {
  constructor(
    private readonly evaluationService: EvaluationService,
    private readonly diligenceHistoryService: DiligenceHistoryService,
    private readonly dataFilterService: DiligenceModifyService,
    private readonly diligencePDFService: DiligencePDFService,
    private readonly securityService: SecurityService,
  ) {}

  @Post('scanRisk/v2')
  @ApiOperation({ summary: '针对指定公司执行 风险扫描 + 风险评价 , version=2' })
  @ApiOkResponse({ type: DiligenceResponseV2, isArray: false })
  async scanRiskV2(@Body() params: GetDiligenceResultParams, @Request() req): Promise<DiligenceResponseV2> {
    const currentUser: RoverUser = req.user;
    return this.evaluationService.getRiskListV2(currentUser, params);
  }

  @Post('/refresh')
  @ApiOperation({ description: '修改指定尽调的状态，下次尽调会强制刷新' })
  async forceUpdateDD(@Request() req, @Body() params: GetDiligenceResultParams) {
    const currentUser: RoverUser = req.user;
    return this.evaluationService.makeDiligenceForUpdate(currentUser, params.companyId);
  }

  @Post('/:diligenceId/exportPDF')
  @ApiOperation({ description: '导出pdf报告（开发调试使用,暂不开放）' })
  async diligenceExport(@Request() req, @Param('diligenceId', ParseIntPipe) diligenceId: number) {
    const currentUser: RoverUser = req.user;
    if ([208, 295, 1001652].includes(currentUser.currentOrg)) {
      return this.diligencePDFService.generatePdf(currentUser.currentOrg, diligenceId, `${currentUser.currentOrg}_${diligenceId}`);
    }
    throw new BadRequestException(RoverExceptions.Common.RequestFailed);
  }

  @Get('/:diligenceId/exportPDF')
  @ApiOperation({ description: '实时查看pdf报告（开发调试使用,暂不开放）' })
  async diligencePdfView(@Request() req, @Param('diligenceId', ParseIntPipe) diligenceId: number) {
    const currentUser: RoverUser = req.user;
    if ([208, 295, 1001652].includes(currentUser.currentOrg)) {
      return this.diligencePDFService.generatePdfView(currentUser.currentOrg, diligenceId);
    }
    throw new BadRequestException(RoverExceptions.Common.RequestFailed);
  }

  @Get('/:diligenceId/exportMarkdown')
  @ApiOperation({ description: '导出报告的markdown版（开发调试使用,暂不开放）' })
  async diligenceMarkdown(@Request() req, @Param('diligenceId', ParseIntPipe) diligenceId: number) {
    const currentUser: RoverUser = req.user;
    if ([208, 295].includes(currentUser.currentOrg)) {
      return this.diligencePDFService.generateMarkdownView(currentUser.currentOrg, diligenceId);
    }
    throw new BadRequestException(RoverExceptions.Common.RequestFailed);
  }

  // @Get('diligence/getCompanyInfo/:keyNo')
  // @ApiOperation({ description: '导出pdf报告' })
  // async diligenceExportGet(@Param('keyNo') keyNo: string) {
  //   return this.diligencePDFService.getCompanyInfo(keyNo);
  // }

  @Post('/:diligenceId/remark')
  @ApiOperation({ description: '修改尽调的结果' })
  async remarkRisk(@Param('diligenceId', ParseIntPipe) diligenceId: number, @Body() postData: UpdateDiligenceResultRequest, @Request() req) {
    const currentUser: RoverUser = req.user;
    await this.securityService.allowWithinOrg(DiligenceHistoryEntity, req.user, [diligenceId]);
    return this.diligenceHistoryService.addRemark(currentUser, diligenceId, postData);
  }

  @Post('/:diligenceId/remark/list')
  @ApiOperation({ description: '获取指定尽职调查的备注记录' })
  @ApiOkResponse({ type: DiligenceRemarkEntity, isArray: true })
  async remarkHistory(@Request() req, @Param('diligenceId', ParseIntPipe) diligenceId: number, @Body() data: PaginationParams) {
    const currentUser: RoverUser = req.user;
    await this.securityService.allowWithinOrg(DiligenceHistoryEntity, req.user, [diligenceId]);
    return this.diligenceHistoryService.getRemarkList(currentUser, diligenceId, data);
  }

  @Post('/group')
  @ApiOperation({ description: '查看指定公司的尽调记录' })
  @ApiOkResponse({ type: DiligenceHistoryResponse, isArray: false })
  async ddHistory(@Body() body: GetCompanyDiligenceHistoryParams, @Request() req): Promise<DiligenceHistoryResponse> {
    const currentUser: RoverUser = req.user;
    return this.diligenceHistoryService.getCompanyHistory(currentUser.currentOrg, body);
  }

  @Post('snapshot')
  @ApiOperation({ summary: '保存尽调结果集风险筛查快照', deprecated: true })
  @ApiOkResponse({ type: String, isArray: false })
  async createSnapshot(@Request() req) {
    return null;
    //return this.diligenceSnapshotService.createSnapshot(currentUser, params);
  }

  @Post('search')
  @ApiOperation({ summary: '查询准入排查记录' })
  @ApiOkResponse({ type: DiligenceHistoryResponse })
  async search(@Request() req, @Body() data: DiligenceHistoryRequest) {
    const currentUser: RoverUser = req.user;
    return this.diligenceHistoryService.search(currentUser, data);
  }

  @Post('companiesResult')
  @ApiOperation({ summary: '查询一批指定公司的准入排查记录' })
  @ApiOkResponse({ type: DiligenceHistoryResponse })
  async searchCompaniesDiligenceResult(@Request() req, @Body() data: CompaniesResultRequest) {
    const currentUser: RoverUser = req.user;
    return this.diligenceHistoryService.searchCompaniesDiligenceResult(currentUser, data.companyIds);
  }

  @Post('remove')
  async remove(@Request() req): Promise<boolean> {
    const currentUser: RoverUser = req.user;
    return this.diligenceHistoryService.remove(currentUser);
  }

  @ApiOperation({ summary: '人工标记疑似关系核实结果', description: '标记交叉重叠关系和黑名单的疑似关系核实状态' })
  @Post('/suspectRelations/mark')
  @ApiOkResponse({ type: DiligenceSuspectRelationsRemarkEntity, isArray: true })
  public async markSuspectRelations(@Request() req, @Body() body: DiligenceSuspectRelationsRemarkRequest) {
    const currentUser: RoverUser = req.user;
    return this.diligenceHistoryService.markSuspectRelations(currentUser, body);
  }

  @ApiOperation({ summary: '获取疑似关系核实记录', description: '查询交叉重叠关系和黑名单的已核实记录' })
  @Post('/suspectRelations/records')
  @ApiOkResponse({ type: DiligenceSuspectRelationsRemarkEntity, isArray: true })
  public async getSuspectRelationsRecords(@Request() req, @Body() body: GetDiligenceVerificationRequest) {
    const currentUser: RoverUser = req.user;
    body.orgId = currentUser.currentOrg;
    return this.diligenceHistoryService.getSuspectRelationsRecords(body);
  }
}
