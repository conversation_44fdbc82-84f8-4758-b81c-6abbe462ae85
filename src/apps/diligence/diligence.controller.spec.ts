import { Test, TestingModule } from '@nestjs/testing';
import { EvaluationService } from './evaluation/evaluation.service';
import { DimensionLevel2Enums } from '../../libs/enums/diligence/DimensionLevel2Enums';
import { DiligenceHistoryCacheHelper } from '../basic/diligence.history.cache.helper';
import { DiligenceHistoryService } from './details/diligence.history.service';
import { DiligenceHistoryRequest } from '../../libs/model/diligence/pojo/history/DiligenceHistoryRequest';
import { DimensionLevel3Enums } from '../../libs/enums/diligence/DimensionLevel3Enums';
import { BundleTestUtils } from '../test_utils_module/bundle.test.utils';
import { BundleHelperService } from '@kezhaozhao/saas-bundle-service/dist_client/client/bundle.helper.service';
import { generateUniqueTestIds, getTestUser } from '../test_utils_module/test.user';
import { RoverUser } from '../../libs/model/common';
import { TestHelper } from '../test_utils_module/test.helper';
import { EntityManager, getManager } from 'typeorm';
import { AppTestModule } from '../app/app.test.module';
import { SnapshotQueueTypeEnums } from './snapshot/po/SnapshotQueueTypeEnums';
import { OrgSettingsLogEntity } from '../../libs/entities/OrgSettingsLogEntity';
import { DiligenceSnapshotService } from './snapshot/diligence.snapshot.service';
import { getDefaultDimensionGroupDefinition } from '../../libs/constants/dimension.constants';
import { SettingsService } from '../settings/settings.service';
import { DiligenceModule } from './diligence.module';

jest.setTimeout(600 * 1000);
describe('diligence controller spec', () => {
  let evaluationService: EvaluationService;
  let diligenceHistoryService: DiligenceHistoryService;
  let helper: DiligenceHistoryCacheHelper;
  let snapshotService: DiligenceSnapshotService;
  const [testOrgId, testUserId] = generateUniqueTestIds('diligence.controller.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  let entityManager: EntityManager;
  let settingService: SettingsService;

  const defaultDimensionGroupDefinitionV1 = getDefaultDimensionGroupDefinition('v1');

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DiligenceModule],
    }).compile();
    BundleTestUtils.spy(module.get(BundleHelperService));
    evaluationService = module.get(EvaluationService);
    helper = module.get(DiligenceHistoryCacheHelper);
    jest.spyOn(helper, 'shouldNotUseCache').mockImplementation(() => {
      //// console.log('shouldNotUseCache mock return true');
      return Promise.resolve(true);
    });
    diligenceHistoryService = module.get(DiligenceHistoryService);
    entityManager = getManager();
    snapshotService = module.get<DiligenceSnapshotService>(DiligenceSnapshotService);
    settingService = module.get<SettingsService>(SettingsService);
  });

  afterAll(async () => {
    await TestHelper.clearAllTestData(entityManager, testOrgId, testUserId);
    await entityManager.connection.close();
  });

  jest.setTimeout(100 * 1000);
  it('排查普通公司', async () => {
    const spy1 = jest.spyOn(snapshotService.snapshotQueue, 'sendMessageV2').mockImplementation((msg) => {
      return snapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.Diligence);
    });
    const orgSetting = Object.assign(new OrgSettingsLogEntity(), { content: defaultDimensionGroupDefinitionV1 });
    // 排查配置使用默认配置维度，确保内部黑名单、潜在利益冲突维度开启
    const spy4 = jest.spyOn(evaluationService.settingService, 'getOrgSettings').mockReturnValue(Promise.resolve(orgSetting));
    // 执行排查
    const riskListV2 = await evaluationService.getRiskListV2(
      getTestUser(1001652, 101348),
      Object.assign(
        {},
        {
          companyId: 'f625a5b661058ba5082ca508f99ffe1b',
          companyName: '企查查科技股份有限公司',
        },
      ),
    );
    expect(spy1).toBeCalled();
    expect(spy4).toBeCalled();
    // console.log(JSON.stringify(riskListV2));
  });

  it('排查 社会组织', async () => {
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyId: 's18802036b61c598218b7c77380843de',
      companyName: '四川省墙体材料工业协会',
    });
    expect(resp).not.toBeNull();
    expect(resp.realtime).toBeTruthy();
    expect(resp.paid).toBeTruthy();
    expect(resp.details).not.toBeNull();
    expect(resp.details.dimensionHits).not.toBeNull();
    expect(resp.details.dimensionHits.length).toBeGreaterThan(0);
    expect(resp.details.dimensionHits.length).toEqual(resp.details.originalHits.length);
  });

  it('港澳台企业排查测试', async () => {
    // const user = await userService.getRoverUser(6893);
    const data = { companyId: 'hfb90e67f2fc58e062fa93f14789ee1a', companyName: '康博電子亞洲有限公司' };

    try {
      const res = await evaluationService.getRiskListV2(testUser, data);
      expect(res);
    } catch (e) {
      expect(e).not.toBeNull();
    }
  });

  it('getRiskListV2 使用过时的settingId 查查信用分 ', async () => {
    const user = getTestUser(1001652, 101348);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '深圳市旺中丰广告有限公司',
      companyId: '12dd0b6fbcb85cb4ef0c1490c4d4f721',
      settingId: 33270,
    });
    // // console.log(resp);
    expect(resp.result).toBeDefined();
    // expect(resp.creditRate).toBeGreaterThan(0);
  });

  it.skip('getRiskListV2 股权出质', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '文思海辉技术有限公司',
      companyId: 'b5447b798bc27a8ab6dcabc918a9ddc1',
      settingId: 3524,
    });
    expect(JSON.stringify(resp)).toContain('股权出质');
  });

  it.skip('getRiskListV2 公安通告 不排查', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '纵通（厦门）信息科技有限公司',
      companyId: 'a793f73d14fb20c1d3290335e3518091',
      settingId: 3524,
    });
    // // console.log(resp);
    expect(JSON.stringify(resp)).not.toContain('公安通告');
  });

  it.skip('getRiskListV2 公安通告 排查', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '纵通（厦门）信息科技有限公司',
      companyId: 'a793f73d14fb20c1d3290335e3518091',
      settingId: 3655,
    });
    const actual = JSON.stringify(resp);
    // // console.log(actual);
    expect(actual).toContain('公安通告');
  });

  it.skip('getRiskListV2 公安通告 排查 2', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '成都钱坤智能系统有限公司',
      companyId: '947d813c228e94807b7cbbbb252ef695',
      settingId: 3655,
    });
    const actual = JSON.stringify(resp);
    // // console.log(actual);
    expect(actual).toContain('公安通告');

    // 确定levelgroup 顺序
    for (const levelGroupKey in resp.details.levelGroup) {
      const data = resp.details.levelGroup[levelGroupKey];
      for (let i = 0; i < data.length - 1; i++) {
        expect(data[i].sort < data[i + 1].sort).toBe(true);
      }
    }
  });

  it.skip('getRiskListV2 公安通告 排查 3', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '成都钱坤智能系统有限公司',
      companyId: '947d813c228e94807b7cbbbb252ef695',
      settingId: 3744,
    });
    const actual = JSON.stringify(resp);
    // // console.log(actual);
    expect(actual).not.toContain('公安通告');
  });

  it.skip('getRiskListV2 注册资本', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '爱孚迪（上海）制造系统工程有限公司',
      companyId: '6b966cc7820974a69b06dd847f0a161f',
      settingId: 3655,
    });
    const actual = JSON.stringify(resp);
    // // console.log(actual);
    expect(actual).not.toContain('注册资本过低');
  });

  it.skip('getRiskListV2 国央企黑名单', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '福建建工集团有限责任公司',
      companyId: '7517f0647e3a3215db8be6ca81876299',
    });
    const actual = JSON.stringify(resp);
    //// console.log(actual);
    expect(actual).toContain('黑名单');
  });

  it.skip('风险排查 -套餐消费详情', async () => {
    // const user = await userService.getRoverUser(217, 208);
    const resp = await diligenceHistoryService.search(
      getTestUser(208, 5171),
      Object.assign(new DiligenceHistoryRequest(), {
        pageSize: 20,
        pageIndex: 1,
        mode: 'bundle',
      }),
    );
    expect(resp.total).toBeGreaterThan(0);
  });

  it('test searchCompaniesDiligenceResult', async () => {
    const response = await diligenceHistoryService.searchCompaniesDiligenceResult(getTestUser(208, 5171), [
      '917720a16d08bdde171681983391aed0',
      '84c17a005a759a5e0d875c1ebb6c9846',
      '84c17a005a759a5e0d875c1ebb6c9846',
      '77f780cb301b86c4f68814190c6709a9',
    ]);
    expect(response).not.toBeNull();
  });

  it.skip('getRiskListV2 建筑工程领域黑名单', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '黑龙江力泽消防工程有限公司',
      companyId: 'b9828d1718c831348eeceebce8c95a68',
    });
    const actual = JSON.stringify(resp);
    // // console.log(actual);
    expect(actual).toContain('黑名单');
  });

  it('getRiskListV2 发改委黑名单', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '内蒙古恒久钢构（集团）有限公司',
      companyId: '8e11379ff6d40439d01ca717e5b7c4cd',
    });
    const actual = JSON.stringify(resp);
    // // console.log(actual);
    expect(actual).toContain('黑名单');
  });

  it.skip('getRiskListV2 疑似空壳', async () => {
    // const user = await userService.getRoverUser(6893);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '揭阳市牧畔堂餐饮管理有限公司',
      companyId: '00760e3de07b1f503a98a63d22fd28e6',
    });
    const actual = JSON.stringify(resp);
    // // console.log(actual);
    expect(actual).toContain('疑似空壳企业');
  });

  it.skip('getRiskListV2 出口管制合规风险企业清单', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '北京朗迪锋科技有限公司',
      companyId: '990de1e0d1133bd495f3292111208132',
    });
    const item = resp.details.dimensionScoreDetails.find((it) => it.groupKey === 'risk_outer_blacklist');
    expect(item.totalHits).toBeGreaterThan(0);
  });

  it.skip('getRiskListV2 世界银行黑名单', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '九江卓泰建筑工程有限公司',
      companyId: 'e30d1f33892730560699f7b8e5aa06a9',
    });
    const item = resp.details.dimensionScoreDetails.find((it) => it.groupKey === 'risk_outer_blacklist');
    expect(item.totalHits).toBeGreaterThan(0);
  });

  it.skip('getRiskListV2 减资公告', async () => {
    // const user = await userService.getRoverUser(101144);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '山东合润石油技术服务有限公司',
      companyId: 'd8db177a1ae87a11a914c65ad29ea1be',
    });
    const s = JSON.stringify(resp);
    // // console.log(s);
    const item = resp.details.dimensionScoreDetails.find((it) => it.groupKey === 'risk_operate_stability');
    expect(s.includes('减资公告')).toBe(true);
    expect(item.totalHits).toBeGreaterThan(0);
  });

  it.skip('getRiskListV2 OuterBlacklist', async () => {
    // const user = await userService.getRoverUser(6893);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '陕西万葫堂中医药有限责任公司',
      companyId: '0c5a42026e7c4d9fce1cc3c9e8a62482',
      settingId: 3524,
    });
    expect(resp.details.dimensionHits).toContain(DimensionLevel2Enums.HitOuterBlackList);
  });

  it.skip('getRiskListV2 MainInfoUpdateHolder', async () => {
    // const user = await userService.getRoverUser(6893);
    const resp = await evaluationService.getRiskListV2(testUser, {
      companyName: '大熊（恩施州）信息科技有限公司',
      companyId: '1f36057b4a435f136b1a8c070f881a3e',
      settingId: 6203,
    });
    expect(resp.details.dimensionHits).toContain(DimensionLevel3Enums.MainInfoUpdateHolder);
  });

  jest.setTimeout(10000 * 1000);
  it('getRiskListV2 公安通告', async () => {
    // const user = await userService.getRoverUser(6893);
    const spy1 = jest.spyOn(snapshotService.snapshotQueue, 'sendMessageV2').mockImplementation((msg) => {
      return snapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.Diligence);
    });
    const resp = await evaluationService.getRiskListV2(getTestUser(208, 5171), {
      companyName: '深圳市旅游（集团）股份有限公司',
      companyId: '4e475f8db68414123bf5e409a185e50c',
      settingId: 33878,
    });
    // console.log(JSON.stringify(resp));
    // expect(resp.details.dimensionHits).toContain(DimensionLevel2Enums.SecurityNotice);
  });

  it.skip('getRiskListV2 交叉重叠排查V2', async () => {
    // const user = await userService.getRoverUser(6893);
    const spy1 = jest.spyOn(snapshotService.snapshotQueue, 'sendMessageV2').mockImplementation((msg) => {
      return snapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.Diligence);
    });
    const resp = await evaluationService.getRiskListV2(getTestUser(208, 5171), {
      companyName: '企查查科技股份有限公司',
      companyId: 'f625a5b661058ba5082ca508f99ffe1b',
      settingId: 35423,
    });
    console.log(JSON.stringify(resp));
    // expect(resp.details.dimensionHits).toContain(DimensionLevel2Enums.SecurityNotice);
  });
});
