import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { Product } from '@kezhaozhao/saas-bundle-service';
import { OrgBundleEntity } from '@kezhaozhao/saas-bundle-service/dist_client/entities/OrgBundleEntity';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { RequireBundlePermission } from 'libs/decorators/bundle-permission.decorator';
import * as moment from 'moment/moment';
import { EntityManager, FindConditions, FindOneOptions, getManager, Repository } from 'typeorm';
import { RoverBundleReminderType } from '../../libs/constants/common';
import { DefaultMonitorDimension } from '../../libs/constants/monitor.dimension.constants';
import { BatchEntity } from '../../libs/entities/BatchEntity';
import { UserConfigurationEntity } from '../../libs/entities/UserConfigurationEntity';
import { BatchBusinessTypeEnums } from '../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchTypeEnums } from '../../libs/enums/batch/BatchTypeEnums';
import { DimensionRiskLevelEnum } from '../../libs/enums/diligence/DimensionRiskLevelEnum';
import { MonitorRiskEnums } from '../../libs/enums/monitor/MonitorRiskEnums';
import { PushSettingEnum } from '../../libs/enums/monitor/PushSettingEnum';
import { MonitorPushDimensionType, PushMethodType } from '../../libs/model/monitor/MonitorPushDimensionType';
import { UserConfigurationTypeEnums } from '../../libs/model/settings/UserConfigurationTypeEnums';
import { AppTestModule } from '../app/app.test.module';
import { BatchService } from '../batch/service/batch.service';
import { MonitorNotificationService } from '../monitor/notification/monitor.notification.service';
import { SettingsService } from '../settings/settings.service';
import { UserService } from '../user/user.service';
import { BundleSyncService } from './bundle.sync.service';
import { RoverScheduleModule } from './schedule.module';
import { ScheduleService } from './schedule.service';

jest.setTimeout(600 * 1000);
describe('schedule test', () => {
  let scheduleService: ScheduleService;
  let pushLogService: MonitorNotificationService;
  let batchService: BatchService;
  let userService: UserService;
  let settingsService: SettingsService;
  let bundleSyncService: BundleSyncService;
  let userConfigurationRepo: Repository<UserConfigurationEntity>;
  let spy: jest.SpyInstance<
    Promise<UserConfigurationEntity>,
    [conditions?: FindConditions<UserConfigurationEntity>, options?: FindOneOptions<UserConfigurationEntity>]
  >;
  let module: TestingModule;
  let entityManager: EntityManager;
  const [testOrgId, testUserId] = generateUniqueTestIds('schedule.service.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [AppTestModule, RoverScheduleModule],
    }).compile();
    const app = module.createNestApplication();
    await app.init();
    scheduleService = module.get(ScheduleService);
    pushLogService = module.get(MonitorNotificationService);
    batchService = module.get(BatchService);
    userService = module.get(UserService);
    settingsService = module.get(SettingsService);
    bundleSyncService = module.get(BundleSyncService);
    userConfigurationRepo = module.get<Repository<UserConfigurationEntity>>(getRepositoryToken(UserConfigurationEntity));
    spy = jest.spyOn(userConfigurationRepo, 'findOne').mockResolvedValue(
      Object.assign(new UserConfigurationEntity(), {
        type: UserConfigurationTypeEnums.monitor_notify2,
        orgId: testOrgId,
        userId: testUserId,
        content: {
          Groups: [302],
          LevelPushs: [
            { level: 2, period: 'everyday', actualDay: '1', notifyTime: '06:00', notifyWeekDay: '', notifyMonthDay: '' },
            { level: 1, period: 'everyday', actualDay: '1', notifyTime: '06:00', notifyWeekDay: '', notifyMonthDay: '' },
            { level: 0, period: 'everyday', actualDay: '1', notifyTime: '06:00', notifyWeekDay: '', notifyMonthDay: '' },
          ],
          PushMethod: {
            system: true,
            pushType: 1,
            mailEnable: false,
            mailAddress: null,
            mobileEnable: false,
            mobileNumber: '***********',
            interfaceEnable: false,
            interfaceSetting: { isEncrypted: false },
          },
          NegativeNews: [
            { key: 'Manually', status: false },
            { key: '2', status: true },
            { key: '1', status: true },
            { key: '0', status: true },
          ],
          RealTimePushContent: [
            'MonitorUpdateHolder',
            'MonitorUpdateRegisteredCapital',
            'MonitorBankruptcy',
            'MonitorSeriousIllegality',
            'MonitorCancellationOfFiling',
            'MonitorDishonestDebtor',
            'MonitorBusinessAbnormal',
            'MonitorSimpleCancellation',
            'MonitorUpdatePerson',
            'MonitorRestrictedConsumption',
            'MonitorAbnormalHousehold',
            'MonitorBillDefaults',
            'MonitorUpdateBeneficiary',
            'MonitorRestrictedGoingAbroad',
            'MonitorTaxationOffences',
            'MonitorTaxArrearsNotice',
            'MonitorInquiryEvaluation',
            'MonitorBlacklist',
            'MonitorJudicialAuction',
            'MonitorPublicSecurityNotice',
            'MonitorBusinessStatus',
            'MonitorBillDispute',
            'MonitorCertificationExpired',
            'BondDefault',
            'MonitorCorruptionAndBribery',
            'MonitorBankruptcyDispute',
            'NoticeInTimePeriod',
            'MonitorTaxCall',
          ],
        },
      }),
    );
    entityManager = getManager();
  });

  afterAll(async () => {
    await module.close();
    spy.mockRestore();
    await entityManager.connection.close();
  });

  it('sample test', () => {
    expect(scheduleService).toBeDefined();
    const res1 = RoverBundleReminderType.includes('diligenceCompanyQuantity');
    expect(res1).toBe(true);
    const res2 = RoverBundleReminderType.includes('personQuantity');
    expect(res2).toBe(false);
  });

  it('test search not pushed dynamic', async () => {
    const user: UserConfigurationEntity = await userConfigurationRepo.findOne({
      type: UserConfigurationTypeEnums.monitor_notify2,
      orgId: testOrgId,
      userId: testUserId,
    });
    expect(spy).toHaveBeenCalled();
    expect(user).toBeDefined();
    expect(user.userId).toEqual(testUserId);
    const now = new Date();
    let start = now;
    const config = user.content[PushSettingEnum.PushMethod] as PushMethodType;
    if (config.notifyWeekDay) {
      start = moment(now).subtract(6, 'day').toDate();
    } else if (config.notifyMonthDay) {
      start = moment(now).subtract(1, 'month').toDate();
      start = moment(start).add(1, 'day').toDate();
    }
    const dayDiff = moment(now).diff(start, 'day') + 1;

    const diligenceAt = Object.assign(new DateRangeRelative(), {
      currently: true,
      flag: 1,
      number: dayDiff,
      unit: 'day',
    });
    const dynamics = await pushLogService.searchNotPushedRiskDynamics(testUserId, testOrgId, user.content as MonitorPushDimensionType, diligenceAt);
    expect(dynamics?.length).toBeGreaterThanOrEqual(0);
  });

  it('test search not pushed sentiment', async () => {
    const user: UserConfigurationEntity = await userConfigurationRepo.findOne({
      type: UserConfigurationTypeEnums.monitor_notify2,
      orgId: testOrgId,
      userId: testUserId,
    });
    expect(spy).toHaveBeenCalled();
    expect(user).not.toBeUndefined();
    expect(user.userId).toEqual(testUserId);
    const now = new Date();
    let start = now;
    const config = user.content[PushSettingEnum.PushMethod] as PushMethodType;
    if (config.notifyWeekDay) {
      start = moment(now).subtract(6, 'day').toDate();
    } else if (config.notifyMonthDay) {
      start = moment(now).subtract(1, 'month').toDate();
      start = moment(start).add(1, 'day').toDate();
    }
    const dayDiff = moment(now).diff(start, 'day') + 1;

    const diligenceAt = Object.assign(new DateRangeRelative(), {
      currently: true,
      flag: 1,
      number: dayDiff,
      unit: 'day',
    });
    user.content[PushSettingEnum.NegativeNews] = [
      {
        key: DimensionRiskLevelEnum.High,
        status: true,
      },
      { key: DimensionRiskLevelEnum.Medium, status: true },
      { key: DimensionRiskLevelEnum.Alert, status: true },
    ];
    const dynamics = await pushLogService.searchNotPushedSentimentDynamics(testUserId, testOrgId, user.content as MonitorPushDimensionType, diligenceAt);
    expect(dynamics?.length).toBeGreaterThanOrEqual(0);
  });

  it('test 312', async () => {
    const monitorDimension = DefaultMonitorDimension.reduce((acc, d) => {
      const k = d.key;
      return { ...acc, [k]: 3 };
    }, {});
    expect(monitorDimension[MonitorRiskEnums.MonitorMainInfoRisk]).toEqual(3);
  });

  it.skip('test DiligenceCustomerAnalyze 测试定时系统风险巡检', async () => {
    const userList1 = await userService.getOrgOwner([1001492]);
    const user = userList1[0];
    const roverUser = await userService.getRoverUser(user.userId, 1001492);
    const batch: BatchEntity = await batchService.createBatchDiligenceTask(
      roverUser,
      [],
      BatchBusinessTypeEnums.Diligence_Customer_Analyze,
      BatchTypeEnums.Import,
      {
        fromSystem: true,
      },
    );
    expect(batch.batchInfo.fromSystem).toBe(true);
  });

  it.skip('test DiligenceCustomerAnalyze 测试定时系统风险巡检', async () => {
    const batch: BatchEntity = await batchService.createBatchDiligenceTask(testUser, [], BatchBusinessTypeEnums.Diligence_Customer_Analyze);

    expect(batch.batchInfo.fromSystem).toBe(true);
  });

  it('测试定时任务合作监控', async () => {
    await scheduleService.monitorNotify2();
  });

  it('测试定时任务合作监控动态消息', async () => {
    await scheduleService.monitorRiskMessageSchedule([-1]);
  });

  // 添加独立的测试套件专注于RequireBundlePermission装饰器的集成测试
  describe('集成测试-RequireBundlePermission Integration Tests', () => {
    // 帮助函数：创建模拟的OrgBundleEntity
    function createMockBundle(orgId: number, notExpired = true, isActive = true, serviceCode: Product = Product.Rover): OrgBundleEntity {
      const today = new Date();
      const expireDate = notExpired
        ? new Date(today.getFullYear() + 1, today.getMonth(), today.getDate()) // 一年后
        : new Date(today.getFullYear() - 1, today.getMonth(), today.getDate()); // 一年前

      return {
        orgId,
        orgBundleId: orgId * 10, // 添加必要的属性
        bundleId: orgId * 100, // 添加必要的属性
        expireDate: expireDate.toISOString(),
        active: isActive ? 1 : 0,
        bundle: {
          serviceCode,
          // 添加其他必要的属性
        },
        // 实际使用时，应该根据OrgBundleEntity的定义添加其他必要属性
      } as unknown as OrgBundleEntity; // 使用unknown做类型转换以解决类型不匹配的问题
    }

    // 测试装饰器在有效捆绑包情况下的行为
    it('应该使用有效的orgIds执行方法当所有捆绑包都有效', async () => {
      // 准备模拟数据
      const orgIds = [1001, 1002, 1003];
      const validBundles = orgIds.map((orgId) => createMockBundle(orgId, true, true));

      // 模拟依赖服务的方法
      jest.spyOn(settingsService, 'getOrgIds').mockResolvedValue(orgIds);
      jest.spyOn(bundleSyncService, 'getBundleList').mockResolvedValue(validBundles);

      // 模拟tenderAlertNotify方法以便能够跟踪它被调用的情况
      const tenderAlertNotifySpy = jest.spyOn(scheduleService, 'tenderAlertNotify');

      // 执行测试的方法
      await scheduleService.tenderAlertNotify(orgIds);

      // 验证结果：方法应该被调用且传入有效的orgIds
      expect(tenderAlertNotifySpy).toHaveBeenCalledWith(orgIds);
    });

    // 测试装饰器在过期捆绑包情况下的行为
    it('应该过滤掉过期的捆绑包', async () => {
      // 准备模拟数据：混合有效和过期的捆绑包
      const orgIds = [1001, 1002, 1003];
      const mixedBundles = [
        createMockBundle(1001, true, true), // 有效
        createMockBundle(1002, false, true), // 过期
        createMockBundle(1003, true, true), // 有效
      ];

      // 模拟依赖服务
      jest.spyOn(settingsService, 'getOrgIds').mockResolvedValue(orgIds);
      jest.spyOn(bundleSyncService, 'getBundleList').mockResolvedValue(mixedBundles);

      // 模拟测试方法
      const monitorNotify2Spy = jest.spyOn(scheduleService, 'monitorNotify2');

      // 执行测试
      await scheduleService.monitorNotify2([1001, 1003]);

      // 验证结果：方法应该被调用，但只传入有效的orgIds
      expect(monitorNotify2Spy).toHaveBeenCalledWith([1001, 1003]);
    });

    // 测试装饰器在非激活捆绑包情况下的行为
    it('应该过滤掉非激活的捆绑包', async () => {
      // 准备模拟数据：混合激活和非激活的捆绑包
      const orgIds = [1001, 1002, 1003];
      const mixedBundles = [
        createMockBundle(1001, true, true), // 有效且激活
        createMockBundle(1002, true, false), // 有效但未激活
        createMockBundle(1003, true, true), // 有效且激活
      ];

      // 模拟依赖服务
      jest.spyOn(settingsService, 'getOrgIds').mockResolvedValue(orgIds);
      jest.spyOn(bundleSyncService, 'getBundleList').mockResolvedValue(mixedBundles);

      // 模拟测试方法
      const bundleReminderSpy = jest.spyOn(scheduleService, 'bundleReminder');

      // 执行测试
      await scheduleService.bundleReminder([1001, 1003]);

      // 验证结果：方法应该被调用，但只传入激活的orgIds
      expect(bundleReminderSpy).toHaveBeenCalledWith([1001, 1003]);
    });

    // 测试装饰器在服务代码不匹配情况下的行为
    it('应该过滤掉服务代码不匹配的捆绑包', async () => {
      // 准备模拟数据：混合不同服务代码的捆绑包
      const anotherProduct = 'OtherProduct' as unknown as Product; // 模拟另一个产品代码
      const orgIds = [1001, 1002, 1003];
      const mixedBundles = [
        createMockBundle(1001, true, true, Product.Rover), // 匹配服务代码
        createMockBundle(1002, true, true, anotherProduct), // 不匹配服务代码
        createMockBundle(1003, true, true, Product.Rover), // 匹配服务代码
      ];

      // 创建装饰器测试类和测试方法
      class TestService {
        @RequireBundlePermission({ checkExpire: true, checkActive: true, serviceCode: Product.Rover })
        async testMethod(validOrgIds: number[]) {
          return validOrgIds;
        }
      }

      const testService = new TestService();
      Object.defineProperty(testService, 'settingService', { value: settingsService });
      Object.defineProperty(testService, 'bundleSyncService', { value: bundleSyncService });

      // 模拟依赖服务
      jest.spyOn(settingsService, 'getOrgIds').mockResolvedValue(orgIds);
      jest.spyOn(bundleSyncService, 'getBundleList').mockResolvedValue(mixedBundles);

      // 执行测试
      const result = await testService.testMethod([]);

      // 验证结果：只返回服务代码匹配的orgIds
      expect(result).toEqual([1001, 1003]);
    });

    // 测试装饰器在无有效捆绑包情况下的行为
    it('当没有有效的捆绑包时应该返回null', async () => {
      // 准备模拟数据：所有捆绑包都无效
      const orgIds = [1001, 1002];
      const invalidBundles = [
        createMockBundle(1001, false, true), // 过期
        createMockBundle(1002, true, false), // 未激活
      ];

      // 模拟依赖服务
      jest.spyOn(settingsService, 'getOrgIds').mockResolvedValue(orgIds);
      jest.spyOn(bundleSyncService, 'getBundleList').mockResolvedValue(invalidBundles);

      // 执行测试
      const result = await scheduleService.monitorRiskMessageSchedule();

      // 验证结果：方法应该返回null
      expect(result).toBeNull();
    });

    // 测试装饰器在服务抛出异常情况下的行为
    it('当服务抛出异常时应该捕获异常并返回null', async () => {
      // 准备模拟数据
      const orgIds = [1001, 1002, 1003];

      // 模拟依赖服务抛出异常
      jest.spyOn(settingsService, 'getOrgIds').mockResolvedValue(orgIds);
      jest.spyOn(bundleSyncService, 'getBundleList').mockRejectedValue(new Error('模拟的服务异常'));

      // 执行测试
      const result = await scheduleService.tenderAlertUpdateCount(orgIds);

      // 验证结果：方法应该捕获异常并返回null
      expect(result).toBeNull();
    });

    // 测试装饰器处理空组织ID列表的情况
    it('当没有组织IDs时应该返回null', async () => {
      // 模拟依赖服务返回空数组
      jest.spyOn(settingsService, 'getOrgIds').mockResolvedValue([]);

      // 执行测试
      const result = await scheduleService.tenderAlertNotify([]);

      // 验证结果：方法应该返回null（没有组织IDs可处理）
      expect(result).toBeNull();
    });
  });
});
