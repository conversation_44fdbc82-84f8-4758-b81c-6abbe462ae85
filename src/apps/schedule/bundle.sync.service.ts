import { BadRequestException, Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { EntityManager } from 'typeorm';
import { InjectEntityManager } from '@nestjs/typeorm';
import { ConfigService } from '../../libs/config/config.service';
import { Product } from '@kezhaozhao/saas-auth';
import { OrgBundleEntity } from '@kezhaozhao/saas-bundle-service/dist_client/entities/OrgBundleEntity';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import * as Bluebird from 'bluebird';
import { getBundleStart } from '../../libs/utils/diligence/diligence.utils';
import { Client } from '@elastic/elasticsearch';
import { flatMap } from 'lodash';
import * as moment from 'moment';
import { captureException } from '@sentry/node';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { BadParamsException } from '@kezhaozhao/qcc-utils';

@Injectable()
export class BundleSyncService {
  private readonly logger: Logger = QccLogger.getLogger(BundleSyncService.name);
  private esClientWrite: Client;
  private indexBase: string;

  constructor(
    protected readonly redisService: RedisService,
    protected readonly httpService: HttpService,
    protected readonly configService: ConfigService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
  ) {
    this.esClientWrite = new Client({
      nodes: this.configService.esConfig.bundleUsage.nodesWrite,
      ssl: { rejectUnauthorized: false },
    });
    this.indexBase = `${this.configService.esConfig.bundleUsage.indexName}-v2`;
  }

  /**
   * 获取指定组织saasBundleService的实时套餐使用情况
   * @param orgId
   * @returns
   */
  async getTotalUsage(orgId) {
    const response = await this.httpService.axiosRef.post(`${this.configService.kzzServer.bundleService}/usage`, {
      orgId,
      onlyActualUsage: true,
      productCode: Product.Rover,
    });
    return response?.data?.['totalUsage'];
  }

  /**
   * 查询套餐未过期的orgBundle
   * @param orgIds
   * @returns
   */
  public async getBundleList(orgIds: number[]): Promise<OrgBundleEntity[]> {
    const url = `${this.configService.kzzServer.bundleService}/search/org`;
    try {
      const data = { productCode: Product.Rover, active: 1 };
      if (orgIds.length) {
        Object.assign(data, { orgIds });
      }
      const response = await this.httpService.axiosRef.post(url, data);
      if (response?.data?.length) {
        return response.data;
      }
    } catch (e) {
      this.logger.error(`获取租户套餐列表:${url} 异常${e}`);
    }
    return [];
  }

  async getDbUsage(orgBundle: OrgBundleEntity) {
    const { orgId, activeDate } = orgBundle;
    const startDate = getBundleStart(activeDate);
    // batchInspectionQuantity  巡检额度
    const [batchInspectionQuantity] = await this.entityManager.query(
      'select COUNT(1) as batchInspectionQuantity from batch where org_id = ? and business_type = 13 and status = 2 and create_date > ?',
      [orgId, startDate],
    );

    // -- diligenceCompanyQuantity  排查公司额度
    const [diligenceCompanyQuantity] = await this.entityManager.query(
      'select count(1) as diligenceCompanyQuantity from (select company_id from due_diligence where org_id = ?  and create_date > ? and operator != -1 group by company_id  ) as temp ;',
      [orgId, startDate],
    );

    // -- diligenceReportQuantity  导出报告额度
    const [diligenceReportQuantity] = await this.entityManager.query(
      'select COUNT(1) as diligenceReportQuantity from batch where org_id = ? and business_type in (34,44,48) and status = 2 and create_date > ?',
      [orgId, startDate],
    );

    // -- innerBlacklistQuantity  内部黑名单额度
    const [innerBlacklistQuantity] = await this.entityManager.query(
      'select COUNT(1) AS innerBlacklistQuantity from inner_blacklist ib  where status = 2 and org_id =  ?',
      [orgId],
    );

    // -- personQuantity  人员列表额度(已删除状态的也计费)
    const [personQuantity] = await this.entityManager.query('select COUNT(1) AS personQuantity from person where status = 2 and org_id = ?', [orgId]);

    // -- thirdPartyQuantity  第三方额度
    const [thirdPartyQuantity] = await this.entityManager.query('select COUNT(1) AS thirdPartyQuantity from customer where status = 2 and org_id = ?', [orgId]);

    // -- monitorCompanyQuantity  监控企业额度
    const [monitorCompanyQuantity] = await this.entityManager.query(
      'select COUNT(1) AS monitorCompanyQuantity from monitor_group_company where status in (2,5) and org_id = ?',
      [orgId],
    );

    // -- diligenceTenderQuantity  招标排查额度
    const [diligenceTenderQuantity] = await this.entityManager.query(
      'select SUM(CEIL(company_count /20)) AS diligenceTenderQuantity from due_diligence_tender_history where org_id = ? and status = 1 and `create_date` > ?',
      [orgId, startDate],
    );

    // -- diligenceTenderQuantity  特定利益关系排查额度
    const [diligenceSpecificQuantity] = await this.entityManager.query(
      'select SUM(CEIL(company_count /20)) AS diligenceSpecificQuantity from specific_interest_record where org_id = ? and status = 1 and `create_date` > ?',
      [orgId, startDate],
    );
    // -- potentialCompanyQuantity  潜在利益排查额度
    const [potentialDiligenceQuantity] = await this.entityManager.query(
      "select sum(paid) as potentialDiligenceQuantity from package_usage_records where org_id =? and active=1 and package_type='potentialDiligenceQuantity' and create_date > ?",
      [orgId, startDate],
    );

    return {
      ...batchInspectionQuantity,
      ...diligenceCompanyQuantity,
      ...diligenceReportQuantity,
      ...innerBlacklistQuantity,
      ...personQuantity,
      ...thirdPartyQuantity,
      ...monitorCompanyQuantity,
      ...diligenceTenderQuantity,
      ...diligenceSpecificQuantity,
      ...potentialDiligenceQuantity,
    };
  }

  public async recordUsage(items) {
    try {
      if (items?.length > 0) {
        const index = this.indexBase + '_write';
        const body = flatMap(items, (doc) => [{ index: { _index: index } }, doc]);
        const { body: bulkResponse } = await this.esClientWrite.bulk({ refresh: false, body });
        if (bulkResponse.errors) {
          const erroredDocuments = [];
          // The items array has the same order of the dataset we just indexed.
          // The presence of the `error` key indicates that the operation
          // that we did for the document has failed.
          bulkResponse.items.forEach((action: any, i: number) => {
            const operation = Object.keys(action)[0];
            if (action[operation].error) {
              erroredDocuments.push({
                // If the status is 429 it means that you can retry the document,
                // otherwise it's very likely a mapping error, and you should
                // fix the document before to try it again.
                status: action[operation].status,
                error: action[operation].error,
                operation: body[i * 2],
                document: body[i * 2 + 1],
              });
            }
          });
          this.logger.error(`BundleSyncService recordUsage erroredDocuments: ${JSON.stringify(erroredDocuments)}`);
        }
      } else {
        return;
      }
    } catch (e) {
      this.logger.error(`BundleSyncService recordUsage error! message:${e.message}`);
      this.logger.error(e);
    }
  }

  /**
   * 将es中的套餐用量保持跟数据库的套餐使用量同步
   * @param orgIds 空数组表示全部未过期的组织
   * @returns
   */
  async syncRoverBundleUseage(orgIds: number[]) {
    const orgBundles = await this.getBundleList(orgIds);
    this.logger.info(`syncRoverBundleUseage get all org num: ${orgBundles.length} !`);
    if (orgBundles?.length < 1) {
      throw new BadRequestException({ error: `syncRoverBundleUseage error orgBundles not found! orgIds: ${orgIds}` });
    }
    const syncOrgIds = [];
    const redisClient = this.redisService.getClient();
    const today = new Date();
    await Bluebird.map(
      orgBundles,
      async (orgBundle: OrgBundleEntity) => {
        const { orgId, orgBundleId } = orgBundle;
        try {
          const dbUsage = await this.getDbUsage(orgBundle);
          const usage = await this.getTotalUsage(orgId);
          const dos2Write = [];
          if (Object.keys(dbUsage)?.length > 0) {
            Object.keys(dbUsage).forEach((key) => {
              let dif = +dbUsage[key];
              if (usage[key]) {
                dif = +dbUsage[key] - (usage[key].limitation - usage[key].stock);
              }
              if (dif == 0) {
                return;
              } else if (usage[key].stock >= 0 && usage[key].limitation - usage[key].stock >= 0 && Math.abs(dif) <= usage[key].limitation) {
                // dif != 0  &&  usage[key].stock >= 0  && (usage[key].limitation - usage[key].stock >= 0) && Math.abs(dif) <= usage[key].limitation 正常情况，需要增加或减少的额度
                // 正常情况，需要增加或减少的额度
                this.logger.info(
                  `env:${this.configService.nodeEnv}, syncRoverBundleUseage ${orgId} : ${key} 套餐项需要同步! dbUsage(${dbUsage[key]}) - totalUsage(${usage[key].limitation} - ${usage[key].stock}) = ${dif}`,
                );
                let fieldCountCycle = 5;
                if (['innerBlacklistQuantity', 'thirdPartyQuantity', 'monitorCompanyQuantity', 'personQuantity'].includes(key)) {
                  fieldCountCycle = 0;
                }
                dos2Write.push({
                  usedType: 0,
                  bundleType: 0,
                  userId: 0,
                  orgId: orgId,
                  orgBundleId: orgBundleId,
                  productCode: 'SAAS_ROVER',
                  createDate: moment().format('yyyy-MM-DDTHH:mm:ss.SSSZ'),
                  fieldKey: key,
                  fieldValue: dif,
                  fieldCategory: 0,
                  fieldCountCycle,
                });
              } else {
                // 异常数据情况，需要记录，不做数据修正，报 sentry，后续人工修正
                const msgStr = `env:${this.configService.nodeEnv}, syncRoverBundleUseage ${orgId} : ${key} 存在异常! dbUsage(${dbUsage[key]}) - totalUsage(${usage[key].limitation} - ${usage[key].stock}) = ${dif}`;
                this.logger.error(msgStr);
                captureException(new Error(msgStr), {
                  extra: {
                    messageBody: msgStr,
                  },
                });
              }
            });
          }
          if (dos2Write.length > 0) {
            syncOrgIds.push(orgId);
            await this.recordUsage(dos2Write);
            this.logger.info(` syncRoverBundleUseage ${orgId} : success!`);
            // 删除对应的 Redis 缓存
            const redisKey = `bc:${today.getMonth() + 1}-${today.getDate()}:SAAS_ROVER:org:${orgId}:orgBundleId:${orgBundleId}`;
            this.logger.info(` syncRoverBundleUseage ${orgId} : delete redisKey: ${redisKey}`);
            await redisClient.del(redisKey);
            if (orgBundle.bundleDeps?.length > 0) {
              orgBundle.bundleDeps.forEach(async (bundleDep) => {
                const redisKey = `bc:${today.getMonth() + 1}-${today.getDate()}:SAAS_ROVER:org:${orgId}:orgBundleId:${orgBundleId}:depId:${
                  bundleDep.depId
                }:depBundleId:${bundleDep.depBundleId}`;
                this.logger.info(` syncRoverBundleUseage ${orgId} : delete redisKey with dep: ${redisKey}`);
                await redisClient.del(redisKey);
              });
            }
          }
        } catch (e) {
          this.logger.error(`syncRoverBundleUseage error orgId:${orgId} 异常${e.message}`);
          this.logger.error(e);
        }
      },
      { concurrency: 10 },
    );
    this.logger.info(`syncRoverBundleUseage  err org num: ${syncOrgIds.length} !, orgIds: ${syncOrgIds}`);
    return syncOrgIds;
  }
}
