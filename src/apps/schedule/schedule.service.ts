import { RedisService } from '@kezhaozhao/nestjs-redis';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { Product } from '@kezhaozhao/saas-bundle-service';
import { OrgBundleEntity } from '@kezhaozhao/saas-bundle-service/dist_client/entities/OrgBundleEntity';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { RequireBundlePermission } from 'libs/decorators/bundle-permission.decorator';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { RoverUser } from 'libs/model/common';
import { groupBy, isNumber } from 'lodash';
import { Logger } from 'log4js';
import * as moment from 'moment';
import { Cron, NestDistributedSchedule } from 'nest-schedule';
import process from 'process';
import Redlock from 'redlock';
import { In, LessThan, Repository } from 'typeorm';
import { ConfigService } from '../../libs/config/config.service';
import { HttpUtilsService } from '../../libs/config/httputils.service';
import { RoverBundleCounterTypeName, RoverBundleReminderType } from '../../libs/constants/common';
import { BatchMatchCompanyItemEntity } from '../../libs/entities/BatchMatchCompanyItemEntity';
import { OrgConfigurationEntity } from '../../libs/entities/OrgConfigurationEntity';
import { ReminderLogEntity } from '../../libs/entities/ReminderLogEntity';
import { TenderAlertSettingEntity } from '../../libs/entities/TenderAlertSettingEntity';
import { UserConfigurationEntity } from '../../libs/entities/UserConfigurationEntity';
import { UserEntity } from '../../libs/entities/UserEntity';
import { BatchTypeEnums } from '../../libs/enums/batch/BatchTypeEnums';
import { AutoDiligenceAnalyzeIntervalEnum } from '../../libs/enums/diligence/AutoDiligenceAnalyzeIntervalEnum';
import { PushSettingEnum } from '../../libs/enums/monitor/PushSettingEnum';
import { MessageBody } from '../../libs/model/message/MessageBody';
import { MsgType } from '../../libs/model/message/MessageRequest';
import { LevelPushesType, MonitorPushDimensionType, PushMethodType } from '../../libs/model/monitor/MonitorPushDimensionType';
import { ConfigurationTypeEnums } from '../../libs/model/settings/ConfigurationTypeEnums';
import { UserConfigurationTypeEnums } from '../../libs/model/settings/UserConfigurationTypeEnums';
import { SearchTenderAlertProjectRequest } from '../../libs/model/tenderAlert/SearchTenderAlertProjectRequest';
import { BatchService } from '../batch/service/batch.service';
import { CompanySearchService } from '../company/company-search.service';
import { ModulesChangedRequest } from '../internal/model/ModulesChangedRequest';
import { MessageService } from '../message/message.service';
import { MonitorCompanyService } from '../monitor/monitor.company.service';
import { MonitorNotificationService } from '../monitor/notification/monitor.notification.service';
import { SettingsService } from '../settings/settings.service';
import { TenderAlertService } from '../tenderAlert/tenderAlert.service';
import { UserService } from '../user/user.service';
import { BundleSyncService } from './bundle.sync.service';
import { GroupsEntity } from 'libs/entities/GroupsEntity';
import { MonitorGroupEntity } from 'libs/entities/MonitorGroupEntity';

@Injectable()
export class ScheduleService extends NestDistributedSchedule {
  private readonly schedulelogger: Logger = QccLogger.getLogger('schedule');
  private readonly redlock: Redlock;

  constructor(
    redisService: RedisService,
    private readonly batchService: BatchService,
    private readonly monitorNotificationService: MonitorNotificationService,
    private readonly httpUtils: HttpUtilsService,
    private readonly configService: ConfigService,
    private readonly settingService: SettingsService,
    private readonly companySearchService: CompanySearchService,
    private readonly userService: UserService,
    private readonly tenderAlertService: TenderAlertService,
    private readonly messageService: MessageService,
    protected readonly httpService: HttpService,
    private readonly monitorCompanyService: MonitorCompanyService,
    private readonly bundleSyncService: BundleSyncService,
    @InjectRepository(OrgConfigurationEntity) private readonly configurationRepo: Repository<OrgConfigurationEntity>,
    @InjectRepository(UserConfigurationEntity) private readonly userConfRepo: Repository<UserConfigurationEntity>,
    @InjectRepository(TenderAlertSettingEntity) private readonly tenderAlertRepo: Repository<TenderAlertSettingEntity>,
    @InjectRepository(BatchMatchCompanyItemEntity) private readonly companyItemRepo: Repository<BatchMatchCompanyItemEntity>,
    @InjectRepository(ReminderLogEntity) private readonly reminderLogRepo: Repository<ReminderLogEntity>,
    @InjectRepository(MonitorGroupEntity) private readonly monitorGroupRepo: Repository<MonitorGroupEntity>,
  ) {
    super();
    this.redlock = new Redlock([redisService.getClient()], {
      retryCount: 1, // retry only 2 times then fails
    });
  }

  async tryLock(method: string): Promise<TryRelease> {
    this.schedulelogger.info(`schedule tryLock:  ${process?.env?.JEST_WORKER_ID}`);
    if (process?.env?.JEST_WORKER_ID) {
      // jest 启动测试的时候禁用定时任务
      return false;
    }
    this.schedulelogger.info('try apply lock: ' + method);

    try {
      await this.redlock.acquire(['lock_' + method], 300 * 1000); // lock 5 minutes

      this.schedulelogger.info('applied lock: ' + method);

      return () => {
        this.schedulelogger.info('cron job done: ' + method);
      };
    } catch (err) {
      // this.schedulelogger.error(err.message);
      return false;
    }
  }

  @Cron('0/30 * * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每半小时执行投标预警消息推送
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  @RequireBundlePermission({
    checkExpire: true,
    checkActive: true,
  })
  async tenderAlertNotify(validOrgIds: number[]) {
    try {
      this.schedulelogger.info(`Run cron job for tender alert start`);
      let tenderAlerts: TenderAlertSettingEntity[] = await this.tenderAlertRepo.find({
        where: {
          enabled: 1,
          orgId: In(validOrgIds),
        },
      });
      const now = new Date();
      const hour = now.getHours();
      const minute = now.getMinutes();
      now.setSeconds(0);
      now.setMilliseconds(0);
      if (minute < 30) {
        now.setMinutes(0);
      } else {
        now.setMinutes(30);
      }
      tenderAlerts = tenderAlerts.filter((x) => {
        if (x.content?.notify?.pushWays?.[0] == 1) {
          if (x.content?.notify?.strategy == 1 && hour == x.content.notify.hour && minute < 30) {
            return true;
          }
          if (x.content?.notify?.strategy == 2) {
            return true;
          }
        }
        return false;
      });
      this.schedulelogger.info(`Run cron job for tender alert notify, ${tenderAlerts.length} settings need notify`);
      if (tenderAlerts.length == 0) return;

      await Bluebird.map(tenderAlerts, async (t) => this.executeTenderAlertNotify(t, now), { concurrency: 6 });
      this.schedulelogger.info('Run cron job for tender alert notify end');
    } catch (error) {
      this.schedulelogger.error('Run cron job for tender alert failed', error);
    }
  }

  @Cron('0 * * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每隔一小时更新投标预警方案的投标
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  @RequireBundlePermission({
    checkExpire: true,
    checkActive: true,
  })
  async tenderAlertUpdateCount(validOrgIds: number[]) {
    try {
      this.schedulelogger.info(`Run cron job for tender alert update count start`);
      const tenderAlerts: TenderAlertSettingEntity[] = await this.tenderAlertRepo.find({ where: { orgId: In(validOrgIds) } });
      await Bluebird.map(tenderAlerts, async (t) => this.tenderAlertService.updateCount(Object.assign(new RoverUser(), { currentOrg: t.orgId }), t), {
        concurrency: 6,
      });
      this.schedulelogger.info('Run cron job for tender alert update count end');
    } catch (error) {
      this.schedulelogger.error('Run cron job for tender alert update count failed', error);
    }
  }

  /**
   * 检查当天过期的证书
   */
  @Cron('30 0 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每天0点30分执行
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  async jobPeriodicalRiskScan() {
    this.schedulelogger.info(`Run cron job for Periodical_Risk_Scan start`);
    return this.monitorCompanyService.periodicalCheckCertRisks();
  }

  @Cron('36 1 1 * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每月1号凌晨1点36分执行 客商的尽调巡检分析
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  @RequireBundlePermission({
    checkExpire: true,
    checkActive: true,
  })
  async startDiligenceCustomerAnalyze(orgId: number, validOrgIds?: number[]) {
    try {
      this.schedulelogger.info(`Run cron job for Diligence_Customer_Analyze start`);
      let orgs: OrgConfigurationEntity[] = [];
      if (orgId) {
        orgs = await this.configurationRepo.find({ orgId });
      } else {
        // 查询所有开启 diligence_analyze 的org
        orgs = await this.configurationRepo.find({ where: "json_extract(content, '$.diligence_analyze.on') = true" });
      }
      if (validOrgIds?.length) {
        orgs = orgs.filter((x) => validOrgIds.includes(x.orgId));
      }
      this.schedulelogger.info(`Run cron job for Diligence_Customer_Analyze find orgIds: ${orgs.map((x) => x.orgId)}`);
      await Bluebird.map(
        orgs,
        async (org) => {
          this.schedulelogger.info(
            `Diligence_Customer_Analyze orgId: ${org.orgId} on: ${org?.content?.diligence_analyze?.on} interval: ${org?.content?.diligence_analyze?.interval}`,
          );
          try {
            // disabled表示套餐是否有效
            if (org?.content?.[ConfigurationTypeEnums.diligence_analyze]?.on && !org?.content?.disabled) {
              const month: number = moment().month() + 1; // 当前月份
              let execute = false;
              switch (org.content[ConfigurationTypeEnums.diligence_analyze].interval) {
                case AutoDiligenceAnalyzeIntervalEnum.Monthly:
                  execute = true;
                  break;
                case AutoDiligenceAnalyzeIntervalEnum.Quarterly:
                  execute = [1, 4, 7, 10].includes(month);
                  break;
                case AutoDiligenceAnalyzeIntervalEnum.HalfYearly:
                  execute = [1, 7].includes(month);
                  break;
                case AutoDiligenceAnalyzeIntervalEnum.Yearly:
                  execute = month == 1;
                  break;
              }
              if (execute) {
                this.schedulelogger.info(`createBatch for Diligence_Customer_Analyze  orgId: ${org.orgId}`);
                const userList1 = await this.userService.getOrgOwner([org.orgId]);
                const user = userList1[0];
                const roverUser = await this.userService.getRoverUser(user.userId, org.orgId);
                await this.batchService.createBatchDiligenceTask(roverUser, [], BatchBusinessTypeEnums.Diligence_Customer_Analyze, BatchTypeEnums.Import, {
                  fromSystem: true,
                });
              }
            }
          } catch (error) {
            this.schedulelogger.error(`Diligence_Customer_Analyze orgId: ${org.orgId} error: ${error.message} `);
            this.schedulelogger.error(error);
          }
        },
        { concurrency: 1 },
      );

      this.schedulelogger.info('Run cron job for Diligence_Customer_Analyze end');
    } catch (error) {
      this.schedulelogger.info('Run cron job for start CustomerDiligenceAnalyze failed');
      this.schedulelogger.error(error);
    }
  }

  @Cron('0 2 * * *', { enable: !process?.env?.JEST_WORKER_ID }) //每日凌晨 2 点删除垃圾数据
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  async cleanBatchCompanyItems() {
    //清理十天前的垃圾数据
    this.schedulelogger.info('clean 10 days ago batch_match_company_item data');
    const tenDaysAgo = new Date();
    tenDaysAgo.setDate(tenDaysAgo.getDate() - 10);
    await this.companyItemRepo.delete({ createDate: LessThan(tenDaysAgo) });
  }

  @Cron('22 2 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每天凌晨2点22分 执行company信息同步
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  async startSyncCompanyInfo(day = 2) {
    this.schedulelogger.info(`sync company info in ${day} days`);
    await this.companySearchService.updateCompanyInfo(day);
    this.schedulelogger.info('sync company info success');
  }

  @Cron('10 3 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每天凌晨3点10执行套餐同步，将es中的套餐用量保持跟数据库的套餐使用量同步
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  async syncRoverBundleUseage() {
    try {
      this.schedulelogger.info(`Run cron job for syncRoverBundleUseage start`);
      await this.bundleSyncService.syncRoverBundleUseage([]);
      this.schedulelogger.info('Run cron job for syncRoverBundleUseage end');
    } catch (error) {
      this.schedulelogger.error('Run cron job for syncRoverBundleUseage failed', error);
    }
  }

  @Cron('33 3 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每天凌晨3点33分 执行company司库类型信息同步
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  async startSyncTreasuryTypeForCentralGov() {
    this.schedulelogger.info(`[定时任务] 开始执行司库类型同步...`);
    await this.companySearchService.syncTreasuryTypeForCentralGov();
    this.schedulelogger.info('[定时任务] 执行司库类型同步完成...');
  }

  @Cron('0 6-23 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每天6至23点执行合作监控风险视图消息推送
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  @RequireBundlePermission({
    checkExpire: true,
    checkActive: true,
  })
  async monitorNotify2(validOrgIds?: number[]) {
    try {
      this.schedulelogger.info(`Run cron job for monitor notify start`);
      let users: UserConfigurationEntity[] = await this.userConfRepo
        .createQueryBuilder()
        .where('type = :type', { type: UserConfigurationTypeEnums.monitor_notify2 })
        .andWhere('org_id IN (:...orgIds)', { orgIds: validOrgIds })
        .getMany();
      this.schedulelogger.info(`${users.length} users has notify setting`);
      const now = new Date();
      users = users.filter((x) => this.monitorNotificationService.isNeedNotify(x, now));
      this.schedulelogger.info(`Run cron job for monitor notify, ${users.length} users need notify`);
      if (users.length == 0) return;
      // 校验当前用户的组织套餐是否正常
      const orgIds: number[] = users.map((u) => u.orgId);
      const bundleList: OrgBundleEntity[] = await this.bundleSyncService.getBundleList(orgIds);
      if (!bundleList?.length) return;
      const orgIds2 = bundleList.map((b) => b.orgId);
      users = users.filter((u) => orgIds2.includes(u.orgId));
      await Bluebird.map(
        users,
        async (user) => {
          const pushSettings = user.content as MonitorPushDimensionType;
          const levelPushs = pushSettings[PushSettingEnum.LevelPushs] as LevelPushesType[];
          const pushMethod = pushSettings[PushSettingEnum.PushMethod] as PushMethodType;
          // 获取当前组织监控分组 和 设置的监控分组 id 的交集（过滤掉已被删除但是还在设置中到分组）, 并设置到 pushSettings 中
          const groupIds = await this.getMonitorGroupIds(user.orgId, pushSettings[PushSettingEnum.Groups]);
          user.content[PushSettingEnum.Groups] = groupIds;
          if (levelPushs?.length > 0 && pushMethod.pushType == 1) {
            // 按风险等级推送
            await this.monitorNotificationService.monitorNotifyByLevel(user);
          } else {
            // 统一设置推送
            await this.monitorNotificationService.monitorNotifyByUser(user);
          }
        },
        { concurrency: 6 },
      );
      this.schedulelogger.info('Run cron job for monitor notify end');
    } catch (error) {
      this.schedulelogger.error('Run cron job for monitor notify failed', error);
    }
  }

  @Cron('0 0 9 * * ?', { enable: !process?.env?.JEST_WORKER_ID }) // 每天9点整 短信权益少于15%提醒
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  @RequireBundlePermission({
    checkExpire: true,
    checkActive: true,
  })
  async stockReminder(validOrgIds?: number[]) {
    this.schedulelogger.info('开始执行权益提醒通知的定时任务');
    const reminderPercent = 0.15;
    let orgIds: number[] = await this.settingService.getOrgIds();
    if (!orgIds?.length) {
      return null;
    }
    if (validOrgIds?.length) {
      orgIds = orgIds.filter((x) => validOrgIds.includes(x));
    }
    //获取套餐列表（针对付费组织发送权益提醒）
    const bundleList: OrgBundleEntity[] = await this.bundleSyncService.getBundleList(orgIds);
    if (!bundleList?.length) {
      return null;
    }
    const orgIds2 = bundleList.map((b) => b.orgId);
    //获取org系统管理员列表
    const userList1: UserEntity[] = await this.userService.getAdminList(orgIds2);
    if (!userList1?.length) {
      return null;
    }
    const orgAdmins = groupBy(userList1, (u) => u.orgId);
    await Bluebird.map(
      orgIds2,
      async (orgId) => {
        const url = `${this.configService.kzzServer.bundleService}/usage`;
        try {
          const response = await this.httpService.axiosRef.post(url, {
            orgId,
            productCode: Product.Rover,
            onlyActualUsage: true,
          });
          //获取当前org管理员列表
          const userList: UserEntity[] = orgAdmins[orgId];
          if (!userList?.length) {
            return null;
          }
          if (response) {
            const totalUsage = response.data.totalUsage;
            for (const param of Object.keys(totalUsage)) {
              //权益在可通知权益中RA-5052
              if (
                RoverBundleReminderType.includes(param) &&
                isNumber(totalUsage[param].limitation) &&
                totalUsage[param].limitation !== 0 &&
                totalUsage[param].limitation !== -1
              ) {
                if (totalUsage[param].stock / totalUsage[param].limitation <= reminderPercent) {
                  //满足额度权益到期提醒，发系统消息+短信
                  //expireDay，quantityParam，reminderPercent
                  const message = `尊敬的用户，您的第三方风险排查-${RoverBundleCounterTypeName[param]}剩余不足 ${
                    reminderPercent * 100
                  } %，为保证正常使用请您及时联系商务人员。`;
                  //发短信提醒，系统消息
                  const tplId = 'SMS_463930647';
                  const tplValue = `#quantityParam#=${RoverBundleCounterTypeName[param]}&#reminderPercent#=${reminderPercent * 100}`;
                  this.schedulelogger.log(`权益 param:${param},tplValue:${tplValue}`);
                  //1.获取已经发送过提醒信息的log
                  const reminders = await this.reminderLogRepo.find({
                    reminderType: 1, //1：套餐权益，2：套餐到期
                    paramKey: param,
                    userId: In(userList.map((u) => u.userId)),
                  });
                  const recordUserIds = reminders?.map((r) => r.userId);
                  //2.过滤未发送过提醒信息的 user
                  const filterUsers = userList.filter((u) => !recordUserIds?.includes(u.userId));
                  //3.记录套餐项提醒记录
                  if (filterUsers?.length) {
                    //推送提醒
                    await this.sendBundleMessage(filterUsers, tplId, tplValue, message);
                    //记录日志
                    const logs = filterUsers.map((f) => {
                      return Object.assign(new ReminderLogEntity(), {
                        orgId: f.orgId,
                        userId: f.userId,
                        reminderType: 1,
                        paramKey: param,
                      });
                    });
                    await this.reminderLogRepo.save(logs);
                  }
                }
              }
            }
          }
        } catch (e) {
          this.schedulelogger.error(`获取用户套餐接口:${url} 异常${e}`);
        }
      },
      { concurrency: 2 },
    );
  }

  @Cron('30 9 * * *', { enable: !process?.env?.JEST_WORKER_ID }) //每天9点半
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  @RequireBundlePermission({
    checkExpire: true,
    checkActive: true,
  })
  async bundleReminder(validOrgIds?: number[]) {
    this.schedulelogger.info('开始执行套餐剩余 15, 30, 60天通知的定时任务');
    const reminderDays = [15, 30, 60];
    const now = Date.now();
    let orgIds: number[] = await this.settingService.getOrgIds();
    if (validOrgIds?.length) {
      orgIds = orgIds.filter((x) => validOrgIds.includes(x));
    }
    if (!orgIds?.length) {
      return null;
    }
    //获取套餐列表
    const bundleList: OrgBundleEntity[] = await this.bundleSyncService.getBundleList(orgIds);
    if (!bundleList?.length) {
      return null;
    }
    const orgIds2 = bundleList.map((b) => b.orgId);
    //获取org系统管理员列表
    const userList1: UserEntity[] = await this.userService.getAdminList(orgIds2);
    if (!userList1?.length) {
      return null;
    }
    const orgAdmins = groupBy(userList1, (u) => u.orgId);
    if (bundleList?.length) {
      await Bluebird.map(
        bundleList,
        async (b) => {
          const expireDay = moment(b.expireDate).startOf('day').diff(moment(now), 'day');
          if (reminderDays.includes(expireDay)) {
            this.schedulelogger.log(`发送orgId:${b?.orgId}套餐剩余${expireDay}天通知`);
            const message = `尊敬的用户，您的第三方风险排查产品套餐还剩余${expireDay}天到期，为保证服务正常使用请您及时联系商务人员。`;
            //获取当前org管理员列表
            const userList: UserEntity[] = orgAdmins[b.orgId];
            if (!userList?.length) {
              return null;
            }
            //发短信提醒，系统消息
            const tplId = 'SMS_463960672';
            const tplValue = `#expireDay#=${expireDay}`;
            this.schedulelogger.log(`套餐到期message,tplValue:${tplValue}`);
            //1.获取已经发送过提醒信息的log
            const reminders = await this.reminderLogRepo.find({
              reminderType: 2,
              expireDay,
              userId: In(userList.map((u) => u.userId)),
            });
            const recordUserIds = reminders?.map((r) => r.userId);
            //2.过滤未发送过提醒信息的 user
            const filterUsers = userList.filter((u) => !recordUserIds?.includes(u.userId));
            //3.记录套餐项提醒记录
            if (filterUsers?.length) {
              //推送提醒
              await this.sendBundleMessage(filterUsers, tplId, tplValue, message);
              //记录日志
              const logs = filterUsers.map((f) => {
                return Object.assign(new ReminderLogEntity(), {
                  orgId: f.orgId,
                  userId: f.userId,
                  reminderType: 2,
                  expireDay,
                });
              });
              await this.reminderLogRepo.save(logs);
            }
          }
        },
        { concurrency: 10 },
      );
    }
  }

  public async sendBundleMessage(userList: UserEntity[], tplId: string, tplValue: string, message: string) {
    if (!userList?.length) {
      return;
    }
    //发短信提醒
    await Bluebird.map(
      userList,
      async (u) => {
        try {
          const r = await this.httpUtils.postRequest(this.configService.enterpriseServer.sendQccCode, {
            phone: u.phone,
            tplId,
            tplValue,
          });
          this.schedulelogger.info(`sms notify stock send mobile resp ${JSON.stringify(r)}`);
        } catch (e) {
          this.schedulelogger.error(`sms notify stock send mobile for user ${u.userId} failed`, e);
        }
        //发系统消息
        const messageBody: MessageBody = {
          title: '系统消息',
          content: message,
          userId: u.userId,
          msgType: MsgType.SystemMsg,
        };
        try {
          await this.messageService.addMessage(messageBody);
        } catch (e) {
          this.schedulelogger.error(`system message for user ${u.userId} failed`, e);
        }
      },
      { concurrency: 10 },
    );
  }

  private async executeTenderAlertNotify(setting: TenderAlertSettingEntity, now: Date) {
    try {
      this.schedulelogger.info(`alert notify for setting ${setting.id} start`);
      let pre: Date;
      if (setting.content.notify.strategy == 1) {
        // 每日推送，查询创建时间过去24小时，发布时间3天内的
        pre = moment(now).subtract(24, 'hour').add('1', 'millisecond').toDate();
      } else {
        // 实时推送，查询创建时间过去半小时，发布时间3天内的
        pre = moment(now).subtract(30, 'minute').add('1', 'millisecond').toDate();
      }
      const dateRange = [
        {
          currently: true,
          flag: 5,
          number: 1,
          unit: 'day',
          min: pre.toISOString(),
          max: now.toISOString(),
        },
      ];
      const searchRequest = Object.assign(new SearchTenderAlertProjectRequest(), {
        settingId: setting.id,
        pageIndex: 1,
        pageSize: 3,
        filter: {
          createdate: dateRange,
          publishdate: [{ currently: true, flag: 1, number: 3, unit: 'day' }],
        },
      });
      const user = Object.assign(new RoverUser(), { currentOrg: setting.orgId });
      const projectResp = await this.tenderAlertService.searchProject(user, searchRequest);
      if (projectResp?.Result?.length) {
        this.schedulelogger.info(`alert notify for setting ${setting.id} has ${projectResp?.Result?.length} tender to send`);
        const userIds = await this.userService.getPermissionUser(setting.orgId, 2135); // 有投标权限的用户
        if (userIds?.length) {
          await Bluebird.map(
            userIds,
            async (userId) => {
              this.schedulelogger.info(`alert notify for setting ${setting.id} send to user ${userId}`);
              const messageBody: MessageBody = {
                title: '投标预警订阅',
                content: `您订阅的"${setting.name}"更新了${projectResp.Paging.TotalRecords}条标讯，快来查看<br/>${projectResp?.Result[0].title}${
                  projectResp?.Result.length > 1 ? '<br/>' + projectResp?.Result[1].title : ''
                }${projectResp?.Result.length > 2 ? '<br/>' + projectResp?.Result[2].title : ''}`,
                userId: userId,
                msgType: MsgType.TrendMsg,
                objectId: setting.id + '',
                url: `/supplier/bidding-warning?settingId=${setting.id}`,
              };
              await this.messageService.addMessage(messageBody);
            },
            { concurrency: 5 },
          );
        }
      }
      this.schedulelogger.info(`alert notify for setting ${setting.id} finished`);
    } catch (e) {
      this.schedulelogger.error(`alert notify for setting ${setting.id} failed`, e);
    }
  }

  async orgChangeStatus(body: ModulesChangedRequest) {
    const org = await this.configurationRepo.findOne(body.orgId);
    if (org) {
      const currentStatus = org.content?.disabled !== true;
      if (currentStatus != body.active) {
        if (currentStatus) {
          delete org.content.disabled;
        } else {
          if (!org.content) {
            org.content = {};
          }
          org.content.disabled = true;
        }
        await this.configurationRepo.save(org);
      }
    }
    if (body.active) {
      if (body.modules.includes('monitor')) {
        await this.monitorCompanyService.onOrganizationActivated({
          orgId: body.orgId,
        });
      } else {
        await this.monitorCompanyService.onOrganizationSuspended({
          orgId: body.orgId,
        });
      }
      if (body.modules.includes('tenderAlert')) {
        await this.tenderAlertService.onOrganizationActivated({
          orgId: body.orgId,
        });
      } else {
        await this.tenderAlertService.onOrganizationSuspended({
          orgId: body.orgId,
        });
      }
    } else {
      await this.monitorCompanyService.onOrganizationSuspended({
        orgId: body.orgId,
      });
      await this.tenderAlertService.onOrganizationSuspended({
        orgId: body.orgId,
      });
    }
  }

  /**
   * 风险动态记录处理
   */
  @Cron('10 0 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每天0点10分执行
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  @RequireBundlePermission({
    checkExpire: true,
    checkActive: true,
  })
  async monitorRiskMessageSchedule(validOrgIds?: number[]) {
    this.schedulelogger.info(`Run cron job for monitor_risk_message start`);
    await this.monitorCompanyService.monitorRiskMessageSchedule(validOrgIds);
    this.schedulelogger.info(`Run cron job for monitor_company_sanctions start`);
    await this.monitorCompanyService.monitorCompanySanctionsSchedule(validOrgIds);
    // this.schedulelogger.info('run cron job for qcc credit change start');
    // await this.monitorCompanyService.processQccCreditChange(validOrgIds);
    // this.schedulelogger.info('run cron job for qcc credit change end');
  }

  /**
   * 获取当前组织监控分组 id 和 设置的监控分组 id 的交集
   * @param orgId
   * @param groupIds
   * @returns
   */
  public async getMonitorGroupIds(orgId: number, groupIds: number[]): Promise<number[]> {
    // 当 groupIds 为 undefined 或 null 时直接返回空数组
    if (!groupIds || !Array.isArray(groupIds)) {
      return [];
    }
    const groups = await this.monitorGroupRepo.find({ orgId });
    const validGroupIds = groups.map((g) => g.id);
    // 使用提前构建的数组进行过滤，提高性能
    return groupIds.filter((groupId) => validGroupIds.includes(groupId)) || [];
  }
}
