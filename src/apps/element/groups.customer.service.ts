import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GroupsEntity } from 'libs/entities/GroupsEntity';
import { Not, Repository } from 'typeorm';
import { CreateGroupModel, GroupType } from 'libs/model/element/CreateGroupModel';
import { SearchElementInfo } from 'libs/model/element/SearchElementInfo';
import { RoverUser } from 'libs/model/common';
import { UpdateGroupModel } from 'libs/model/element/UpdateGroupModel';
import { BadParamsException, CommonExceptions } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { PersonEntity } from 'libs/entities/PersonEntity';
import { CustomerEntity } from 'libs/entities/CustomerEntity';
import { GroupScene } from 'libs/constants/common';
import { InnerBlacklistEntity } from '../../libs/entities/InnerBlacklistEntity';
import { SecurityService } from '../../libs/config/security.service';
import { PermissionByEnum } from '../../libs/enums/PermissionScopeEnum';

@Injectable()
export class GroupsCustomerService {
  constructor(
    @InjectRepository(GroupsEntity) private readonly groupRepo: Repository<GroupsEntity>,
    @InjectRepository(PersonEntity) private readonly personRepo: Repository<PersonEntity>,
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
    @InjectRepository(InnerBlacklistEntity) private readonly innerBlacklistRepo: Repository<InnerBlacklistEntity>,
    private readonly securityService: SecurityService,
  ) {}

  async addGroup(currentUser: RoverUser, data: CreateGroupModel) {
    //分组数量判断
    const groupCount = await this.groupRepo.count({
      groupType: data.groupType,
      orgId: currentUser.currentOrg,
    });
    if (groupCount && groupCount > 99) {
      throw new BadRequestException(RoverExceptions.GroupRelated.Group.Limited);
    }
    //分组名称重复性判断
    const count = await this.groupRepo.count({
      groupType: data.groupType,
      orgId: currentUser.currentOrg,
      name: data.name,
    });
    if (count && count > 0) {
      throw new BadRequestException(RoverExceptions.GroupRelated.Group.DuplicatedError);
    }
    return this.groupRepo.save(Object.assign(new GroupsEntity(), data, { orgId: currentUser.currentOrg }));
  }

  async upsertGroups(orgId: number, data: CreateGroupModel[]) {
    return this.groupRepo.upsert(data, ['orgId', 'name', 'groupType']);
  }

  async updateGroup(currentUser: RoverUser, groupId: number, data: CreateGroupModel) {
    const groupsEntity = await this.groupRepo.findOne({ groupId });
    if (!groupsEntity) {
      throw new BadRequestException(RoverExceptions.GroupRelated.Group.NotFound);
    } else if (groupsEntity?.scene > GroupScene.Normal) {
      throw new BadParamsException(RoverExceptions.GroupRelated.Group.CanNotEdited);
    }

    const count = await this.groupRepo.count({
      groupType: groupsEntity.groupType,
      name: data.name,
      orgId: currentUser.currentOrg,
      groupId: Not(groupId),
    });
    if (count && count > 0) {
      throw new BadRequestException(RoverExceptions.GroupRelated.Group.DuplicatedError);
    }
    return this.groupRepo.update(
      {
        orgId: currentUser.currentOrg,
        groupId,
      },
      data,
    );
  }

  async searchGroup(currentUser: RoverUser, data: SearchElementInfo) {
    const { name, groupType } = data;
    const qb = this.groupRepo
      .createQueryBuilder('group')
      .select([
        'group.id as groupId',
        'group.name as name',
        'group.order as `order`',
        'group.groupType as groupType',
        'group.createDate as createDate',
        'group.updateDate as updateDate',
        'group.scene as scene',
      ])
      .andWhere('group.orgId = :orgId', { orgId: currentUser.currentOrg });
    if (name) {
      qb.andWhere('group.name like :name', { name: `%${name}%` });
    }
    if (groupType) {
      qb.andWhere('group.groupType = :groupType', { groupType });
      switch (groupType) {
        case GroupType.CustomerGroup:
          qb.leftJoin(CustomerEntity, 'customer', 'group.id = customer.groupId').addSelect(['count(customer.customerId) as `count`']);
          break;
        case GroupType.PersonGroup: {
          // 这里只是查询人员分组，2061 对应的是人员列表权限，不需要校验
          // const { by, userIds } = this.securityService.checkScope(currentUser, 2061);
          qb.leftJoin(PersonEntity, 'person', 'group.id = person.groupId').addSelect(['count(person.id) as `count`']);
          // if (by == PermissionByEnum.USER) {
          //   qb.andWhere('person.createBy in (:...userIds)', { userIds });
          // }
          break;
        }
        case GroupType.InnerBlacklistGroup:
          qb.leftJoin(InnerBlacklistEntity, 'innerBlackList', 'group.id = innerBlackList.groupId').addSelect(['count(innerBlackList.id) as `count`']);
          break;
        default:
      }
    }
    return qb.groupBy('group.id').orderBy('group.order', 'DESC').getRawMany();
  }

  async deleteGroup(currentUser: RoverUser, groupId: number) {
    const { currentOrg: orgId } = currentUser;
    const groupsEntity = await this.groupRepo.findOne({ groupId, orgId });
    if (groupsEntity?.scene > GroupScene.Normal) {
      throw new BadParamsException(RoverExceptions.GroupRelated.Group.CanNotEdited);
    }
    const result = await this.groupRepo.delete({
      orgId,
      groupId,
    });
    if (groupsEntity?.groupType === GroupType.CustomerGroup) {
      await this.customerRepo
        .createQueryBuilder()
        .update(CustomerEntity)
        .set({ groupId: null })
        .where('groupId = :groupId', { groupId: groupsEntity.groupId })
        .andWhere('orgId = :orgId', { orgId })
        .execute();
    } else if (groupsEntity?.groupType === GroupType.PersonGroup) {
      await this.personRepo
        .createQueryBuilder()
        .update(PersonEntity)
        .set({ groupId: null })
        .where('groupId = :groupId', { groupId: groupsEntity.groupId })
        .andWhere('orgId = :orgId', { orgId })
        .execute();
    } else if (groupsEntity?.groupType === GroupType.InnerBlacklistGroup) {
      await this.innerBlacklistRepo
        .createQueryBuilder()
        .update(InnerBlacklistEntity)
        .set({ groupId: null })
        .where('groupId = :groupId', { groupId: groupsEntity.groupId })
        .andWhere('orgId = :orgId', { orgId })
        .execute();
    }
    return result;
  }

  async updateBatchGroup(currentUser: RoverUser, postData: UpdateGroupModel) {
    //校验list中的groupId是否存在
    const groupItems: number[] = postData?.groupModels.map((item) => item.groupId);
    const groupsEntities = await this.groupRepo.findByIds(groupItems);
    if (groupsEntities?.length != groupItems?.length) {
      throw new BadParamsException({ message: '分组不存在!', ...CommonExceptions.Common.Request.NotFound });
    }
    return this.groupRepo.save(
      postData.groupModels.map((item) =>
        Object.assign(new GroupsEntity(), {
          orgId: currentUser.currentOrg,
          groupId: item.groupId,
          order: item.order,
        }),
      ),
    );
  }

  /**
   * 获取分组信息并构建成map
   * @param orgId
   * @param groupType
   * @private
   */
  public async getGroupKV(orgId: number, groupType: GroupType) {
    const groupList = await this.groupRepo.find({ orgId, groupType });
    const groupMap = { '-1': '未分组' };
    groupList?.map((group) => {
      Object.assign(groupMap, { [group.groupId]: group.name });
    });
    return groupMap;
  }
}
