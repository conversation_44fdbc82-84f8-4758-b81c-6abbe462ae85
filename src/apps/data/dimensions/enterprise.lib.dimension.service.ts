import { Injectable } from '@nestjs/common';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { HitEnterpriseDimensionQueryParam } from '../../../libs/model/diligence/pojo/req&res/details/request/HitEnterpriseDimensionQueryParam';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/pojo/req&res/details/response';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { cloneDeep, compact, isNumber, pick, sum } from 'lodash';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { ContractBreachDegree } from '../../../libs/constants/model.constants';
import { getIsValidValue } from '../../../libs/utils/diligence/diligence.utils';
import { formatDate, getAll, getStartTimeByCycle, toRoundFixed } from '../../../libs/utils/utils';
import * as moment from 'moment/moment';
import { DimensionQueryPO, getCompareResult, getStartEnd, OperatorEnums } from '../../../libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { isOrganism } from '../../company/utils';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import { DATE_FORMAT } from '../../../libs/constants/common';
import { getExpirationDate } from '../../../libs/utils/date.utils';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ConfigService } from '../../../libs/config/config.service';
import { CompanySearchService } from '../../company/company-search.service';
import { HttpUtilsService } from '../../../libs/config/httputils.service';
import { CompanyDetailService } from '../../company/company-detail.service';

@Injectable()
export class EnterpriseLibDimensionService {
  private readonly logger: Logger = QccLogger.getLogger(EnterpriseLibDimensionService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly companySearchService: CompanySearchService,
    private readonly httpUtils: HttpUtilsService,
    private readonly companyDetailsService: CompanyDetailService,
  ) {}

  async getDimensionDetail(dimension: DimensionDefinitionPO, data: HitEnterpriseDimensionQueryParam): Promise<HitDetailsBaseResponse> {
    // this.logger.info(`get hit details for dimension: ${dimension.key}`);
    const sourcePath = dimension?.detailSourcePath ? dimension.detailSourcePath : dimension.sourcePath;
    let dimensionDetails = HitDetailsBaseResponse.ok();
    try {
      //查询参数使用优先级： 1. data 参数 2. dimension.strategyModel.detailsParams  如果有值，需要生成条件
      const validField = dimension?.strategyModel?.detailsParams?.find((q) => q.field === QueryParamsEnums.isValid);
      switch (dimension.key) {
        case DimensionLevel2Enums.CompanyShell:
          return await this.getShellCompanyLabel(data, sourcePath);
        case DimensionLevel3Enums.ProductQualityProblem9:
          // 食品安全检查不合格
          return await this.getFoodQualityProblem(data, dimension, sourcePath);
        case DimensionLevel2Enums.ContractBreach:
          //合同违约
          return await this.getContractBreach(data, sourcePath);
        case DimensionLevel2Enums.ChattelMortgage:
          //动产抵押
          return await this.getChattelMortgage(data, validField, sourcePath, dimension);
        case DimensionLevel2Enums.LandMortgage:
          //土地抵押
          return await this.getLandMortgage(data, dimension, validField, sourcePath);
        case DimensionLevel3Enums.CancellationOfFiling:
          //注销备案
          return await this.getCancellationOfFiling(data, validField, sourcePath);
        case DimensionLevel2Enums.TaxArrearsNotice:
          //欠税公告
          return await this.getTaxArrearsNotice(data, dimension, validField, sourcePath);
        case DimensionLevel2Enums.TaxReminder:
          //税务催报
          return await this.getTaxReminder(data, validField, sourcePath, dimension);
        case DimensionLevel2Enums.TaxCallNoticeV2:
          //税务催缴
          return await this.getTaxCallNotice(data, validField, sourcePath, dimension);
        case DimensionLevel3Enums.PersonCreditCurrent:
          //被列入失信被执行人
          return await this.getPersonCreditCurrent(data, sourcePath);
        case DimensionLevel3Enums.PersonCreditHistory:
          //历史失信被执行人
          return await this.getPersonCreditHistory(data, dimension, sourcePath);
        case DimensionLevel2Enums.PersonExecution:
          //被执行人
          return await this.getPersonExecution(data, dimension, validField, sourcePath, dimensionDetails);
        case DimensionLevel3Enums.EndExecutionCase:
          //终本案件
          return await this.getEndExecutionCase(data, dimension, validField, sourcePath, dimensionDetails);
        case DimensionLevel3Enums.MainInfoUpdateHolder: {
          // 大股东变更
          const holderRes = await this.getMainInfoUpdateHolder(dimension, data, '24');
          return Object.assign(dimensionDetails, pick(holderRes?.data, ['Result', 'Paging', 'GroupItems']));
        }
        case DimensionLevel3Enums.MainInfoUpdateBeneficiary: {
          //近期变更受益所有人
          const holderRes = await this.getMainInfoUpdateHolder(dimension, data, '114');
          return Object.assign(dimensionDetails, pick(holderRes?.data, ['Result', 'Paging', 'GroupItems']));
        }
        case DimensionLevel3Enums.MainInfoUpdatePerson: {
          // 实际控制人变更
          const personRes = await this.getMainInfoUpdateHolder(dimension, data, '25');
          return Object.assign(dimensionDetails, pick(personRes?.data, ['Result', 'Paging', 'GroupItems']));
        }
        case DimensionLevel3Enums.MainInfoUpdateScope: // 经营范围变更,
        case DimensionLevel3Enums.MainInfoUpdateAddress: // 注册地址变更
        case DimensionLevel3Enums.MainInfoUpdateName: // 企业名称变更
        case DimensionLevel3Enums.MainInfoUpdateLegalPerson: // 法定代表人变更
          return this.getCoyHisInfo(dimension, data);
        case DimensionLevel3Enums.MainInfoUpdateManager: // 董监高变更
          return this.getMainInfoUpdate(dimension, data);
        case DimensionLevel2Enums.JudicialAuction:
        case DimensionLevel2Enums.CompanyCredit:
        case DimensionLevel2Enums.GuaranteeRisk:
        case DimensionLevel2Enums.CompanyCreditHistory:
          return this.getQccDimensionDetail(dimension, data);
        case DimensionLevel2Enums.NoQualityCertification:
          return await this.getNoQualityCertification(data, sourcePath, dimensionDetails);
        case DimensionLevel2Enums.NoCertification:
          return await this.getNoCertification(data, sourcePath, dimension, dimensionDetails);
        case DimensionLevel2Enums.Certification:
          // 资质筛查
          return await this.getCertification(data, sourcePath, dimension, dimensionDetails);
        case DimensionLevel2Enums.BillDefaults:
          //票据违约
          return await this.getBillDefaults(data, sourcePath, dimension, dimensionDetails);
        case DimensionLevel2Enums.BondDefaults:
          //债券违约
          return await this.getBondDefaults(data, validField, sourcePath, dimensionDetails);
        case DimensionLevel3Enums.Liquidation: //清算信息
          return await this.getLiquidation(data, sourcePath, dimensionDetails);
        case DimensionLevel3Enums.FinancialHealth:
          // 财务健康度
          return await this.getFinancialHealth(data, sourcePath, dimension, dimensionDetails);
        case DimensionLevel2Enums.StockPledge:
          return await this.getStockPledgeDetails(data, dimension, sourcePath);
        case DimensionLevel2Enums.IPRPledge: {
          return await this.getIprPledgeDetails(data, dimension, sourcePath);
        }
        case DimensionLevel2Enums.FakeRegister:
          return await this.getFakeRegister(data, sourcePath, dimensionDetails);
        case DimensionLevel3Enums.BusinessAbnormal2: {
          return await this.getBusinessAbnormal2Details(data, dimension);
        }
        case DimensionLevel3Enums.EmployeeReduction:
          //员工人数减少
          return await this.getEmployeeReduction(data, sourcePath, dimension, dimensionDetails);
        case DimensionLevel3Enums.BusinessAbnormal5:
          //疑似停产停业
          return await this.getBusinessAbnormal5Details(data, sourcePath, dimensionDetails);
        case DimensionLevel3Enums.DebtOverdue:
          //债务逾期
          return await this.getDebtOverdue(data, sourcePath, dimensionDetails);
        case DimensionLevel2Enums.ChattelSeizure:
          //动产查封
          return await this.getChattelSeizure(data, validField, sourcePath, dimension, dimensionDetails);
        default:
          // 其他默认
          break;
      }
    } catch (error) {
      this.logger.error(`EnterpriseLibService getDimensionDetail err: ${error},dimension: ${dimension.key}`);
      dimensionDetails = HitDetailsBaseResponse.failed(error.response?.error || error.message, DimensionSourceEnums.EnterpriseLib, error.response?.code);
    }
    return dimensionDetails;
  }

  /**
   * 动产查封
   * @param data
   * @param validField
   * @param sourcePath
   * @param dimension
   * @param dimensionDetails
   * @private
   */
  private async getChattelSeizure(
    data: HitEnterpriseDimensionQueryParam,
    validField: DimensionQueryPO,
    sourcePath: string,
    dimension: DimensionDefinitionPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const { keyNo, pageIndex, pageSize } = data;

    const chattelSeizureResult = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      isValid: validField?.fieldVal,
      sortField: dimension.strategyModel?.sortField?.field ? dimension.strategyModel?.sortField?.field : 'publishdate',
      isSortAsc: false,
      pageIndex,
      pageSize,
    });
    if (chattelSeizureResult?.Result) {
      return Object.assign(dimensionDetails, pick(chattelSeizureResult, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  private async getDebtOverdue(data: HitEnterpriseDimensionQueryParam, sourcePath: string, dimensionDetails: HitDetailsBaseResponse) {
    const queryParam = {
      keyNo: data.keyNo,
      pageIndex: 1,
      pageSize: 500,
    };

    let allResults = [];
    allResults = await getAll(
      queryParam,
      this.httpUtils.postRequest.bind(this.httpUtils, this.configService.proxyServer.dataService + sourcePath),
      'Result',
      allResults,
    );
    const resultData = allResults?.filter((item) => ['未偿还', '部分偿还'].includes(item.RepaymentSchedule)).sort((a, b) => b.StartDate - a.StartDate);

    if (resultData?.length) {
      const pageSize = data?.pageSize || 10;
      const pageIndex = data?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      const paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: resultData.length,
        TotalAmount: parseFloat(toRoundFixed(sum(resultData.map((e) => parseFloat(e.OverdueSum))), 2)),
      };
      const result = resultData.slice(start, end).map((item) => {
        return {
          debtorName: item?.Debtor?.[0]?.Name || '-',
          debtorKeyNo: item?.Debtor?.[0]?.KeyNo || '-',
          creditorName: item?.Creditor?.[0]?.Name || '-',
          creditorKeyNo: item?.Creditor?.[0]?.KeyNo || '-',
          debtType: item?.DebtType,
          overdueSum: parseFloat(item?.OverdueSum),
          overdueCapital: parseFloat(item?.OverdueCapital),
          overdueInterest: parseFloat(item.OverdueInterest),
          refundAmount: item?.RefundAmount,
          publicPartyName: item?.PublicParty?.[0]?.Name || '-',
          publicPartyKeyNo: item?.PublicParty?.[0]?.KeyNo || '-',
          startDate: formatDate(item?.StartDate, 'YYYY-MM-DD'),
          repaymentSchedule: item?.RepaymentSchedule,
        };
      });
      return Object.assign(dimensionDetails, {
        Paging: paging,
        Result: result,
      });
    }

    return dimensionDetails;
  }

  private async getBusinessAbnormal5Details(data: HitEnterpriseDimensionQueryParam, sourcePath: string, dimensionDetails: HitDetailsBaseResponse) {
    const originPageSize = data?.pageSize;
    const originPageIndex = data?.pageIndex;
    const postData = {
      keyNo: data.keyNo,
      //A008|吊销许可证件 A009|限制开展生产经营活动 A010|责令停产停业 A011|责令关闭
      punishCategory: 'A008,A009,A010,A011',
      //数据量不会过多，需要过滤A010类型，拉全量数据自行分页
      pageSize: 1000,
      pageIndex: 1,
    };
    const response = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, postData);

    if (response?.Result) {
      const result = response.Result.filter((item) => {
        const punishCategories = item.PunishCategory ?? [];

        // A010类型需要额外验证是否已过停业天数,如果处罚日期和处罚天数这两个字段命中不了，则认定处罚永久有效，命中风险
        if (punishCategories?.includes('A010')) {
          if (!item.PunishDate || !item.ClosedDay) return true;

          const punishDate = moment.unix(item.PunishDate);
          if (!punishDate.isValid()) return true;

          const closedDays = Number(item.ClosedDay) || 0;
          if (closedDays <= 0) return true;

          return punishDate.add(closedDays, 'days').isSameOrAfter(moment(), 'day');
        }
        return true;
      }).map((i) => {
        return {
          companyId: i?.KeyNo,
          companyName: i?.CompanyName,
          docNo: i?.DocNo,
          riskId: i?.Id,
          punishCategory: i?.PunishCategory?.join(','),
          punishReason: i?.PunishReason,
          punishResult: i.PunishResult,
          punishOffice: i?.PunishOffice,
          punishDate: formatDate(i?.PunishDate, 'YYYY-MM-DD'),
          closedDays: i?.ClosedDay,
          sourceName: i?.SourceName,
        };
      });

      response.Paging = {
        PageSize: originPageSize,
        PageIndex: originPageIndex,
        TotalRecords: result?.length,
      };
      if (originPageSize) {
        const startIndex = (originPageIndex - 1) * originPageSize;
        response.Result = result.slice(startIndex, startIndex + originPageSize);
      }
      return response;
    }
    return dimensionDetails;
  }

  private async getEmployeeReduction(
    data: HitEnterpriseDimensionQueryParam,
    sourcePath: string,
    dimension: DimensionDefinitionPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const { Result } = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, data);
    if (!Result) return dimensionDetails;

    const queryPo = dimension?.strategyModel?.detailsParams?.find((d) => d.field === QueryParamsEnums.employeeReductionRatio);
    if (!queryPo) return dimensionDetails;

    const [prevYear, currentYear] = Result.slice(-2);
    if (!prevYear || !currentYear) return dimensionDetails;

    const { ReportYear: prevYearVal, InsuredCount: prevCount } = prevYear;
    const { ReportYear: currentYearVal, InsuredCount: currentCount } = currentYear;

    const yearDiff = parseInt(currentYearVal) - parseInt(prevYearVal);
    if (yearDiff !== 1 || prevCount === null || currentCount === null) {
      return dimensionDetails;
    }

    const employeeReductionRatio = ((prevCount - currentCount) / prevCount) * 100;
    const isMatch = getCompareResult(employeeReductionRatio, queryPo.fieldVal, queryPo.fieldOperator);
    const result = [
      {
        prevCount: prevCount,
        prevYear: prevYearVal,
        currentCount: currentCount,
        currentYear: currentYearVal,
      },
    ];

    return isMatch ? Object.assign(dimensionDetails, { Result: result, Paging: { TotalRecords: 1 } }) : dimensionDetails;
  }

  private async getFakeRegister(data: HitEnterpriseDimensionQueryParam, sourcePath: string, dimensionDetails: HitDetailsBaseResponse) {
    const postData = {
      keyNo: data.keyNo,
      auctionResult: ['1', '2', '4'],
      pageSize: data?.pageSize || 5,
      pageIndex: data?.pageIndex || 1,
    };

    const response = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, postData);
    if (response?.Result) {
      const result = response.Result.map((i) => {
        return {
          companyId: i?.KeyNo,
          companyName: i?.CompanyName,
          fakeRegisterItem: i?.IllegalUseItem,
          partyKeyNo: i?.PartyKeyNo,
          partyName: i?.PartyName,
          fakeRegisterTime: formatDate(i?.IllegalRegDate, 'YYYY-MM-DD'),
          processResult: i?.ProcessResult,
          decisionDate: formatDate(i?.DecisionDate, 'YYYY-MM-DD'),
          startDate: formatDate(i?.StartDate, 'YYYY-MM-DD'),
          endDate: formatDate(i?.EndDate, 'YYYY-MM-DD'),
          executeGov: i?.ExecuteGov,
          auctionResult: i?.AuctionResult,
        };
      });
      return Object.assign(dimensionDetails, pick(response, ['Paging']), { Result: result });
    }
    return dimensionDetails;
  }

  /**
   * 获取动态数据（工商维度）
   * @param dimension
   * @param data
   * @param category
   */
  async getMainInfoUpdateHolder(dimension: DimensionDefinitionPO, data: Record<string, any>, category: string): Promise<any> {
    const sourcePath = dimension?.detailSourcePath ? dimension.detailSourcePath : dimension.sourcePath;

    const params = {
      keyNo: data.keyNo,
      pageIndex: data.pageIndex,
      pageSize: data.pageSize,
      category,
    };

    const cycle: number = dimension?.strategyModel?.cycle || 1;

    if (cycle > 0) {
      const dateStart = moment().subtract(cycle, 'year').format(DATE_FORMAT);
      const dateEnd = moment().format(DATE_FORMAT);

      Object.assign(params, { dateStart, dateEnd });
    }

    return await this.httpUtils.getRequest(this.configService.proxyServer.roverService + sourcePath, params);
  }

  /**
   * 营业执照处理
   * @param keyNo
   * @param dateType
   * @param expirationDate
   * @param nowDate
   * @private
   */
  private async getBusinessLicenseResult(keyNo: string, dateType: number, expirationDate: number, nowDate: number) {
    const companyDetailsQcc = await this.companySearchService.companyDetailsQcc(keyNo);
    let expirationDesc = '缺失';
    let startDate = null;
    let endDate = null;
    if (companyDetailsQcc?.TeamEnd >= 0) {
      endDate = companyDetailsQcc.TeamEnd;
      startDate = companyDetailsQcc.TermStart;
      expirationDesc = this.getExpirationDesc(endDate, expirationDate, nowDate, dateType);
    }
    if (expirationDesc != '有效') {
      return {
        index: 0,
        name: '营业执照',
        startDate,
        endDate,
        expirationDesc,
        certificationType: '营业执照',
      };
    }
    return null;
  }

  // private async httpGet(url: string, params: Record<string, any>) {
  //   try {
  //     const timestamp = moment().unix().toString();
  //     const token = crypto
  //       .createHash('md5')
  //       .update(params.key + timestamp + this.configService.server.qccPro.secretKey)
  //       .digest('hex')
  //       .toUpperCase();
  //     const requestParams: AxiosRequestConfig = {
  //       method: 'GET',
  //       url,
  //       params,
  //       headers: {
  //         Timespan: timestamp,
  //         Token: token,
  //       },
  //     };
  //     return this.httpUtils.sendResquest(requestParams);
  //   } catch (error) {
  //     this.logger.error(' pro interface error:', error);
  //     throw error;
  //   }
  // }

  /**
   * 经营范围，注册地址，企业名称变更列表
   * @param dimension
   * @param data
   * @private
   */
  private async getCoyHisInfo(dimension: DimensionDefinitionPO, data: Record<string, any>): Promise<HitDetailsBaseResponse> {
    // if (dimension.key === DimensionLevel3Enums.MainInfoUpdateManager) {
    //   return this.getMainInfoUpdate(dimension, data);
    // }
    const sourcePath = dimension?.detailSourcePath ? dimension.detailSourcePath : dimension.sourcePath;
    const reqUrl = this.configService.proxyServer.dataService + sourcePath;
    const dimensionDetails = new HitDetailsBaseResponse();
    const MainInfoUpdatekeyMap = {
      [DimensionLevel3Enums.MainInfoUpdateLegalPerson]: 'OperList', // 法定代表人变更
      [DimensionLevel3Enums.MainInfoUpdateScope]: 'ScopeList', // 经营范围变更,
      [DimensionLevel3Enums.MainInfoUpdateAddress]: 'AddressList', // 注册地址变更
      [DimensionLevel3Enums.MainInfoUpdateName]: 'CompanyNameList', // 企业名称变更
    };

    // 工商变更记录分类型 对应不同的维度处理
    let updateInfos = [];
    if (isOrganism(data.keyNo)) {
      const organismList = await this.getOrganismChangeInfo(data.keyNo);
      organismList.forEach((e) => {
        if (e[MainInfoUpdatekeyMap[dimension.key]]) {
          updateInfos.push(e[MainInfoUpdatekeyMap[dimension.key]]);
        }
      });
    } else {
      const dataResult = await this.httpUtils.getRequest(reqUrl, data);
      if (dataResult?.Status !== 200) {
        return HitDetailsBaseResponse.failed('获取工商变更信息失败');
      }
      updateInfos = dataResult?.Result[MainInfoUpdatekeyMap[dimension.key]] || [];
    }
    return await this.getMainInfoUpdateInfos(dimension, data, updateInfos, dimensionDetails);
  }

  /**
   * 社会组织变更记录
   * @param keyNo
   * @private
   */
  private async getOrganismChangeInfo(keyNo: string) {
    try {
      const companyDetail = await this.companySearchService.companyDetailsQcc(keyNo);
      const changeList = companyDetail?.ChangeDiffInfo?.ChangeList;
      if (!changeList?.length) {
        return [];
      }
      const resList = changeList.map((c) => {
        if (c.ProjectName === '法定代表人变更') {
          return {
            OperList: {
              OperName: c.BeforeInfos[0].Content,
              KeyNo: c.BeforeInfos[0].KeyNo,
              ChangeDate: moment(Number(c.ChangeDate) * 1000).format(DATE_FORMAT),
            },
          };
        } else if (c.ProjectName === '住所变更') {
          return {
            AddressList: {
              ChangeDate: moment(Number(c.ChangeDate) * 1000).format(DATE_FORMAT),
              Address: c.BeforeInfos[0].Content,
            },
          };
        } else if (c.ProjectName === '变更业务范围') {
          return {
            ScopeList: {
              ChangeDate: moment(Number(c.ChangeDate) * 1000).format(DATE_FORMAT),
              Scope: c.BeforeInfos[0].Content,
            },
          };
        } else if (c.ProjectName === '名称变更') {
          return {
            CompanyNameList: {
              ChangeDate: moment(Number(c.ChangeDate) * 1000).format(DATE_FORMAT),
              CompanyName: c.BeforeInfos[0].Content,
            },
          };
        }
      });
      return compact(resList);
    } catch (e) {
      this.logger.error(`http Get List/OrganismChangeInfo err:`, e);
      return [];
    }
  }

  /**
   * 查询公司详情接口,获取当前的信息
   * @param dimension
   * @param keyNo
   */
  async getCompanyDetailCurrentScope(dimension: DimensionDefinitionPO, keyNo: string) {
    // const companyDetail = await this.httpUtils.getRequest(this.configService.dataServer.companyDeatil, {
    //   keyNo: keyNo,
    // });
    const companyDetail = await this.companySearchService.companyDetailsQcc(keyNo);
    if (companyDetail) {
      switch (dimension.key) {
        case DimensionLevel3Enums.MainInfoUpdateScope:
          return { Scope: companyDetail.Scope };
        case DimensionLevel3Enums.MainInfoUpdateAddress:
          return { Address: companyDetail.Address };
        case DimensionLevel3Enums.MainInfoUpdateName:
          return { CompanyName: companyDetail.Name };
        case DimensionLevel3Enums.MainInfoUpdateLegalPerson:
          return { OperName: companyDetail?.Oper?.Name, KeyNo: companyDetail?.Oper?.KeyNo };
        case DimensionLevel3Enums.MainInfoUpdateManager:
          break;
      }
    }
    return null;
  }

  /**
   * 工商变更相关维度
   * @param dimension
   * @param data
   * @returns
   */
  async getMainInfoUpdate(dimension: DimensionDefinitionPO, data: Record<string, any>) {
    const sourcePath = dimension?.detailSourcePath ? dimension.detailSourcePath : dimension.sourcePath;
    const dimensionDetails = new HitDetailsBaseResponse();
    const MainInfoUpdatekeyMap = {
      [DimensionLevel3Enums.MainInfoUpdateManager]: 'EmployeeList', // 董监高变更
    };

    const result = await this.httpUtils.postRequest(this.configService.proxyServer.roverService + sourcePath, data);
    // 工商变更记录分类型 对应不同的维度处理
    const updateInfos = result?.data?.Result[MainInfoUpdatekeyMap[dimension.key]] || [];
    return await this.getMainInfoUpdateInfos(dimension, data, updateInfos, dimensionDetails);
  }

  /**
   * qcc详情接口
   * @param dimension
   * @param data
   * @private
   */
  private async getQccDimensionDetail(dimension: DimensionDefinitionPO, data: Record<string, any>): Promise<HitDetailsBaseResponse> {
    const { keyNo } = data;
    const sourcePath = dimension?.detailSourcePath ? dimension.detailSourcePath : dimension.sourcePath;
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const reqUrl = this.configService.proxyServer.dataService + sourcePath;
    let method = 'GET';
    switch (dimension.key) {
      case DimensionLevel2Enums.JudicialAuction: {
        const cycle = dimension?.strategyModel?.cycle || 1;
        Object.assign(data, {
          KeyNo: keyNo,
          // 只查询非历史数据
          isValid: '1',
          isSortAsc: data?.order === 'ASC',
        });
        if (cycle !== -1) {
          Object.assign(data, {
            rangeFilters: [
              {
                type: 'liandate',
                rangeList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
              },
            ],
          });
        }
        method = 'POST';
        break;
      }
      case DimensionLevel2Enums.CompanyCredit: {
        // 被列入严重违法失信企业名录
        data.isValid = '1';
        if (data.relatedKeyNos && data.relatedKeyNos.length > 0) {
          data.keyNo = data.relatedKeyNos?.join(',');
        }
        break;
      }
      case DimensionLevel2Enums.CompanyCreditHistory: {
        data.isValid = getIsValidValue('0');
        const cycle = dimension?.strategyModel?.cycle;
        if (cycle && cycle !== -1) {
          Object.assign(data, {
            rangeFilters: JSON.stringify([
              {
                type: 'liandate',
                dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
              },
            ]),
          });
        }
        if (data.relatedKeyNos && data.relatedKeyNos.length > 0) {
          data.keyNo = data.relatedKeyNos?.join(',');
        }
        break;
      }
      case DimensionLevel2Enums.GuaranteeRisk: {
        // 数据范围：是否有效
        const isValid = dimension?.strategyModel?.detailsParams?.find((po) => po.field === QueryParamsEnums.isValid)?.fieldVal || undefined;
        data.isValid = getIsValidValue(isValid);

        //排序字段，可选
        // guaranteedprincipal 被保证债权本金
        // judgedate 裁判日期
        // submitdate 发布日期
        data.sortField = 'submitdate';
        data.role = '1,2'; //角色 1:担保方，2：被担保方，3：债权人
        const cycle = dimension?.strategyModel?.cycle;
        //担保风险
        const guaranteedPrincipal = dimension?.strategyModel?.detailsParams?.find((p) => p.field === QueryParamsEnums.guaranteedprincipal);
        if (cycle && cycle !== -1) {
          Object.assign(data, {
            rangeFilters: JSON.stringify([
              {
                type: 'submitdate',
                dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
              },
            ]),
          });
        }
        if (guaranteedPrincipal) {
          Object.assign(data, {
            rangeFilters: JSON.stringify([
              {
                type: QueryParamsEnums.guaranteedprincipal,
                dateList: [getStartEnd(guaranteedPrincipal.fieldVal, guaranteedPrincipal.fieldOperator)],
              },
            ]),
          });
        }
        break;
      }
    }
    let dataResult;
    if (method === 'GET') {
      dataResult = await this.httpUtils.getRequest(reqUrl, data);
    } else {
      dataResult = await this.httpUtils.postRequest(reqUrl, data);
    }
    return Object.assign(dimensionDetails, pick(dataResult, ['Result', 'Paging', 'GroupItems']));
  }

  /**
   * 食品安全检查不合格
   * @param data
   * @param dimension
   * @param sourcePath
   * @private
   */
  private async getFoodQualityProblem(data: HitEnterpriseDimensionQueryParam, dimension: DimensionDefinitionPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo, pageIndex, pageSize } = data;
    const foodQualityResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      searchKey: keyNo,
      checkResult: 2,
      sortField: dimension.strategyModel?.sortField?.field ? dimension.strategyModel?.sortField?.field : 'batch',
      isSortAsc: 'false',
      pageIndex,
      pageSize,
    });
    if (foodQualityResult?.Result) {
      // 数据处理，实现原 credit 数据源格式，兼容已有后续相关业务的处理
      foodQualityResult.Result.forEach((item) => {
        const AmountDesc = [];
        if (item.UnqualifiedDetails && item.UnqualifiedDetails.length > 0) {
          item.UnqualifiedDetails.forEach((detail) => {
            AmountDesc.push({
              check_normal: detail.CheckNormal,
              check_result: detail.CheckResult,
              check_name: detail.CheckName,
            });
          });
        }
        Object.assign(item, {
          Title: item.FoodName,
          Amount2: item.CheckRank,
          Address: item.ProdAddress,
          ActionRemark: item.TrademarkInfo,
          Specs: item.Specs,
          LianDate: item.Batch,
          NameAndKeyNo: item.SamplingCompanys,
          ApplicantInfo: item.ProdCompanys,
          ExecuteStatus: item.CheckResult,
          AmountDesc: JSON.stringify(AmountDesc),
          recordId: item.Id,
        });
      });
      return Object.assign(dimensionDetails, pick(foodQualityResult, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  /**
   * 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为
   * @param data
   * @param dimension
   * @param sourcePath
   * @private
   */
  private async getCriminalOffence(data: HitEnterpriseDimensionQueryParam, dimension: DimensionDefinitionPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo, pageIndex, pageSize } = data;
    // 公司的刑事案件
    const crimainalResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      caseTypeMain: 'xs',
      roleType: 'D,O',
      isB: true,
      pageIndex,
      pageSize,
    });
    // TOOD: 获取 法定代表人/股东/董监高 的 keyno 然后调用跟公司一样的接口只是换一下keyNo

    if (crimainalResult?.Result) {
      return Object.assign(dimensionDetails, pick(crimainalResult, ['Result']), {
        Paging: {
          PageSize: data.pageSize,
          PageIndex: data.pageIndex,
          TotalRecords: 1,
        },
      });
    }
    return dimensionDetails;
  }

  /**
   * 合同违约
   * @param data
   * @param sourcePath
   * @private
   */
  private async getContractBreach(data: HitEnterpriseDimensionQueryParam, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const breachResult = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
    });
    if (breachResult?.Result) {
      if (Object.keys(ContractBreachDegree).find((levelStr) => levelStr === breachResult.Result?.Revel)) {
        return Object.assign(dimensionDetails, {
          Result: [breachResult.Result],
          Paging: {
            PageSize: data.pageSize,
            PageIndex: data.pageIndex,
            TotalRecords: 1,
          },
        });
      }
    }
    return dimensionDetails;
  }

  /**
   * 动产抵押
   * @param data
   * @param validField
   * @param sourcePath
   * @param dimension
   * @private
   */
  private async getChattelMortgage(data: HitEnterpriseDimensionQueryParam, validField: DimensionQueryPO, sourcePath: string, dimension: DimensionDefinitionPO) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo, pageIndex, pageSize } = data;
    const dimensionQueryPO = dimension.strategyModel.detailsParams.find((d) => d.field === QueryParamsEnums.amount);
    let rangeFilters = [];
    if (dimensionQueryPO) {
      rangeFilters = this.getRangeFilters(dimensionQueryPO);
    }
    const chattelMortgageResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      type: 'mortgager', // 动产抵押 身份角色：mortgager-抵押人,pledgee-抵押权人, -全部
      isValid: getIsValidValue(validField?.fieldVal),
      rangeFilters,
      pageIndex,
      pageSize,
    });
    if (chattelMortgageResult?.Result) {
      return Object.assign(dimensionDetails, pick(chattelMortgageResult, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  /**
   * 土地抵押
   * @param data
   * @param dimension
   * @param validField
   * @param sourcePath
   * @private
   */
  private async getLandMortgage(data: HitEnterpriseDimensionQueryParam, dimension: DimensionDefinitionPO, validField: DimensionQueryPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo } = data;
    const dimensionQueryPO = dimension.strategyModel.detailsParams.find((d) => d.field === QueryParamsEnums.landMortgageAmount && d.fieldVal);
    const landMortgageAmount = dimensionQueryPO?.fieldVal || 0;
    const landMortgageParams = {
      keyNo,
      type: 'mortgagor', // 土地抵押 角色类型 mortgage 抵押权人, mortgagor 抵押人
      isValid: getIsValidValue(validField?.fieldVal),
      pageIndex: 1,
      pageSize: 500,
      rangeFilters: [{ type: 'mortgageprice', rangeList: [{ start: landMortgageAmount }] }],
    };
    // if (dimension.strategyModel?.cycle && dimension.strategyModel?.cycle !== -1) {
    //   landMortgageParams['start'] = getStartTimeByCycle(dimension.strategyModel.cycle) / 1000;
    //   landMortgageParams['end'] = moment().unix();
    // }
    const landMortgageResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, landMortgageParams);
    if (landMortgageResult?.Result) {
      // 抵押金额汇总处理
      const resultData = landMortgageResult.Result;
      const pageSize = data?.pageSize || 10;
      const pageIndex = data?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      const paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: landMortgageResult.Paging.TotalRecords,
        TotalAmount: parseFloat(toRoundFixed(sum(resultData.map((e) => parseFloat(e.MortgagePrice))), 2)),
      };
      const result = resultData.slice(start, end);
      return Object.assign(dimensionDetails, {
        Paging: paging,
        Result: result,
      });
      // return Object.assign(dimensionDetails, pick(landMortgageResult, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  /**
   * 注销备案
   * @param data
   * @param validField
   * @param sourcePath
   * @private
   */
  private async getCancellationOfFiling(data: HitEnterpriseDimensionQueryParam, validField: DimensionQueryPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const filingResult = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      isValid: validField?.fieldVal ?? '1',
    });
    if (filingResult?.Result) {
      return Object.assign(dimensionDetails, pick(filingResult, ['Result']), {
        Paging: {
          PageSize: data.pageSize,
          PageIndex: data.pageIndex,
          TotalRecords: 1,
        },
      });
    }
    return dimensionDetails;
  }

  /**
   * 欠税公告
   * @param data
   * @param dimension
   * @param validField
   * @param sourcePath
   * @private
   */
  private async getTaxArrearsNotice(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionDefinitionPO,
    validField: DimensionQueryPO,
    sourcePath: string,
  ) {
    //默认查欠税公告 当前有效， isValid = 1
    //0：历史 1：非历史 0,1：不限
    // let validDate = (validField?.fieldVal ?? '1') + ',' + IsValidNumbers;
    // if (validField?.fieldVal == -1) {
    //   validDate = '0,1' + ',' + IsValidNumbers;
    // }
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const validData = getIsValidValue(validField?.fieldVal);
    //如果设置了欠税余额排查条件，欠税公告需要根据欠税金额进行过滤
    const queryPo: DimensionQueryPO = dimension?.strategyModel?.detailsParams.find((d) => d.field === QueryParamsEnums.taxArrearsAmount);
    // this.logger.info('查欠税公告，不限');
    const taxReq = {
      searchKey: data.keyNo,
      pageSize: 500,
      pageIndex: 1,
      isValid: validData,
      isCombin: '1', //是否合并（默认0） 0：不合并 1：合并
      sortField: 'liandate',
      isSortAsc: false,
      rangeFilters: [],
    };
    const cycle = dimension?.strategyModel?.cycle;
    if (cycle && cycle !== -1) {
      Object.assign(taxReq, {
        rangeFilters: JSON.stringify([
          {
            type: 'liandate', //发布日期
            dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
          },
        ]),
      });
    }
    const taxArrearsNoticeRes1 = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, taxReq);
    const taxArrearsNoticeRes = [];
    Array.prototype.push.apply(taxArrearsNoticeRes, taxArrearsNoticeRes1?.Result);
    if (queryPo) {
      const taxRes = taxArrearsNoticeRes?.filter((e) => getCompareResult(e.Amount, queryPo?.fieldVal, queryPo.fieldOperator));
      const pageSize = data?.pageSize || 10;
      const pageIndex = data?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      const paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: taxRes?.length,
        TaxBalanceAmount: parseFloat(toRoundFixed(sum(taxRes?.map((e) => parseFloat(e.Amount))), 2)),
        TaxBalanceCurrentAmount: parseFloat(toRoundFixed(sum(taxRes?.map((e) => parseFloat(e.NewAmount))), 2)),
      };
      const result = taxRes?.slice(start, end);
      return Object.assign(dimensionDetails, {
        Paging: paging,
        PageIndex: pageIndex,
        Result: result,
      });
    }
    return dimensionDetails;
  }

  /**
   * 税务催缴公告
   * @param data
   * @param validField
   * @param sourcePath
   * @private
   */
  private async getTaxCallNotice(data: HitEnterpriseDimensionQueryParam, validField: DimensionQueryPO, sourcePath: string, dimension: DimensionDefinitionPO) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    let isValidVal = validField?.fieldVal;
    if (isValidVal === '1') {
      isValidVal = [1];
    } else {
      isValidVal = [0, 1];
    }
    const queryParam = {
      keyNo: data.keyNo,
      isValid: isValidVal,
      pageIndex: 1,
      pageSize: 500,
      dimension: 2, //1:税务催报 2:税务催缴
      sortField: 'publishdate',
      isSortAsc: false,
    };
    let resultData = [];
    resultData = await getAll(
      queryParam,
      this.httpUtils.postRequest.bind(this.httpUtils, this.configService.proxyServer.dataService + sourcePath),
      'Result',
      resultData,
    );
    if (resultData?.length) {
      if (dimension.strategyModel?.cycle && dimension.strategyModel?.cycle !== -1) {
        const startPublishTime = getStartTimeByCycle(dimension.strategyModel.cycle) / 1000;
        resultData = resultData.filter((e) => e.PublishDate >= startPublishTime);
      }
      const queryPo = dimension.strategyModel.detailsParams.find((f) => f.field === QueryParamsEnums.AmountOwed);
      if (queryPo) {
        resultData = resultData.filter((e) => getCompareResult(e.AmountOwed, queryPo.fieldVal, queryPo.fieldOperator));
      }
      const pageSize = data?.pageSize || 10;
      const pageIndex = data?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      const paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: resultData.length,
        TotalAmount: parseFloat(toRoundFixed(sum(resultData.map((e) => parseFloat(e.AmountOwed))), 2)),
      };
      const result = resultData.slice(start, end);
      return Object.assign(dimensionDetails, {
        Paging: paging,
        Result: result,
      });
    }
    return dimensionDetails;
  }

  /**
   * 税务催报
   * @param data
   * @param validField
   * @param sourcePath
   * @param dimension
   * @private
   */
  private async getTaxReminder(data: HitEnterpriseDimensionQueryParam, validField: DimensionQueryPO, sourcePath: string, dimension: DimensionDefinitionPO) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    let isValidVal = validField?.fieldVal;
    if (isValidVal === '1') {
      isValidVal = [1];
    } else {
      isValidVal = [0, 1];
    }
    const taxReminderResult = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
      isValid: isValidVal,
      pageIndex: 1,
      pageSize: 500,
      dimension: 1, //1:税务催报 2:税务催缴
      sortField: 'publishdate',
      isSortAsc: false,
    });
    if (taxReminderResult?.Result) {
      let resultData = taxReminderResult.Result;
      if (dimension.strategyModel?.cycle && dimension.strategyModel?.cycle !== -1) {
        const startPublishTime = getStartTimeByCycle(dimension.strategyModel.cycle) / 1000;
        resultData = taxReminderResult.Result.filter((e) => e.PublishDate >= startPublishTime);
      }
      const pageSize = data?.pageSize || 10;
      const pageIndex = data?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      const paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: resultData.length,
      };
      const result = resultData.slice(start, end);
      return Object.assign(dimensionDetails, {
        Paging: paging,
        Result: result,
      });
    }
    return dimensionDetails;
  }

  /**
   * 被列入失信被执行人
   * @param data
   * @param sourcePath
   * @private
   */
  private async getPersonCreditCurrent(data: HitEnterpriseDimensionQueryParam, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const shiXingRes = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      searchKey: data.keyNo,
      pageSize: data.pageSize,
      pageIndex: data.pageIndex,
      // 只查询非历史数据
      isValid: '1',
      sortField: data?.field || 'pubdate', //sortField必须是liandate,biaodiamount,pubdate其中之一 ，
      isSortAsc: data?.order === 'ASC',
    });
    return Object.assign(dimensionDetails, pick(shiXingRes, ['Result', 'Paging', 'GroupItems']));
  }

  /**
   * 历史失信被执行人
   * @param data
   * @param dimension
   * @param sourcePath
   * @private
   */
  private async getPersonCreditHistory(data: HitEnterpriseDimensionQueryParam, dimension: DimensionDefinitionPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const reqData = {
      searchKey: data.keyNo,
      pageSize: data.pageSize,
      pageIndex: data.pageIndex,
      // 只查询历史数据
      isValid: getIsValidValue('0'),
      sortField: data?.field || 'liandate', //sortField必须是liandate,biaodiamount,pubdate其中之一 ，
      isSortAsc: data?.order === 'ASC',
    };
    const cycle: number = dimension?.strategyModel?.cycle;
    if (cycle && cycle !== -1) {
      Object.assign(reqData, {
        rangeFilters: JSON.stringify([
          {
            type: 'liandate',
            dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
          },
        ]),
      });
    }
    const shiXingHisRes = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, reqData);
    return Object.assign(dimensionDetails, pick(shiXingHisRes, ['Result', 'Paging', 'GroupItems']));
  }

  /**
   * 被执行人
   * @param data
   * @param dimension
   * @param validField
   * @param sourcePath
   * @param dimensionDetails
   * @private
   */
  private async getPersonExecution(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionDefinitionPO,
    validField: DimensionQueryPO,
    sourcePath: string,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const zhiXingRes = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      searchKey: data.keyNo,
      pageSize: data.pageSize,
      pageIndex: data.pageIndex,
      isValid: getIsValidValue(validField?.fieldVal),
      sortField: data?.field || 'liandate', //sortField必须是liandate,biaodiamount其中之一 ，
      isSortAsc: data?.order === 'ASC',
    });
    const find1 = zhiXingRes?.GroupItems?.find((e) => e.key === 'companynames');
    if (find1) {
      const zhixinSum = find1?.items[0]?.['sum']; //被执行总金额，单位元
      if (dimension?.strategyModel?.detailsParams && zhixinSum) {
        const queryPo = dimension.strategyModel.detailsParams.find((d) => d.field == QueryParamsEnums.executionSum);
        if (queryPo?.fieldVal) {
          if (getCompareResult(zhixinSum, queryPo?.fieldVal, queryPo?.fieldOperator || OperatorEnums.ge)) {
            return Object.assign(dimensionDetails, pick(zhiXingRes, ['Result', 'Paging', 'GroupItems']));
          } else {
            return dimensionDetails;
          }
        }
      }
    }
    return Object.assign(dimensionDetails, pick(zhiXingRes, ['Result', 'Paging', 'GroupItems']));
  }

  /**
   * 终本案件
   * @param data
   * @param dimension
   * @param validField
   * @param sourcePath
   * @param dimensionDetails
   * @private
   */
  private async getEndExecutionCase(
    data: HitEnterpriseDimensionQueryParam,
    dimension: DimensionDefinitionPO,
    validField: DimensionQueryPO,
    sourcePath: string,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const reqData = {
      keyNo: data.keyNo,
      pageSize: data.pageSize,
      pageIndex: data.pageIndex,
      isValid: getIsValidValue(validField?.fieldVal),
      sortField: data?.field?.toLowerCase() || 'enddate', //enddate 终本日期 judgedate 立案日期 executeobject 执行标的 failureact 未履行金额 默认 enddate ，
      isSortAsc: data?.order === 'ASC',
      isAgg: true,
    };
    const cycle = dimension?.strategyModel?.cycle;
    if (cycle && cycle !== -1) {
      Object.assign(reqData, {
        rangeFilters: JSON.stringify([
          {
            type: 'judgedate', //judgedate立案日期
            dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
          },
        ]),
      });
    }
    const endCaseRes = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, reqData);
    const failure = dimension?.strategyModel?.detailsParams?.find((q) => q.field === QueryParamsEnums.failure);
    if (failure?.fieldVal) {
      const failureGroupItem: {
        count: number;
        desc: string;
        sum: number;
        value: number;
      } = endCaseRes?.GroupItems?.find((g) => g.key == 'failureGroup')?.items?.[0];
      if (failureGroupItem?.sum) {
        if (getCompareResult(failureGroupItem.sum, Number(failure.fieldVal), failure?.fieldOperator || OperatorEnums.ge)) {
          return Object.assign(dimensionDetails, pick(endCaseRes, ['Result', 'Paging', 'GroupItems']));
        } else {
          return dimensionDetails;
        }
      }
    }
    return Object.assign(dimensionDetails, pick(endCaseRes, ['Result', 'Paging', 'GroupItems']));
  }

  private async getBusinessAbnormal1(data: HitEnterpriseDimensionQueryParam, dimensionDetails: HitDetailsBaseResponse) {
    const keyNo = data.keyNo;
    const companyDetail = await this.companySearchService.companyDetailsQcc(keyNo);
    if (isOrganism(keyNo) && ['撤销', '吊销', '注销', '注销中', '名称核准不通过', '清算', '除名'].includes(companyDetail.ShortStatus)) {
      Object.assign(dimensionDetails, {
        Result: [
          {
            label: '登记状态',
            value: companyDetail.ShortStatus,
            ShortStatus: companyDetail.ShortStatus,
          },
        ],
        Paging: { TotalRecords: 1 },
      });
    }
    if (!isOrganism(keyNo) && ['吊销', '注销', '撤销', '停业', '歇业', '责令关闭', '清算', '除名'].includes(companyDetail.ShortStatus)) {
      Object.assign(dimensionDetails, {
        Result: [
          {
            label: '登记状态',
            value: companyDetail.ShortStatus,
            ShortStatus: companyDetail.ShortStatus,
          },
        ],
        Paging: { TotalRecords: 1 },
      });
    }
    return dimensionDetails;
  }

  // private async getShellCompanyLabel(data: HitEnterpriseDimensionQueryParam, dimensionDetails: HitDetailsBaseResponse) {
  //   const result1 = await this.httpGet(`${this.configService.server.qccPro.baseUrl}/api/corp/shellScan/getResult`, {
  //     key: this.configService.server.qccPro.key,
  //     searchKey: data.companyName,
  //   });
  //   if (result1.status === '200') {
  //     const tempResult = result1?.result?.data;
  //     Object.assign(dimensionDetails, {
  //       Result: tempResult,
  //       Paging: { TotalRecords: 1 },
  //     });
  //   } else {
  //     //获取失败？
  //     dimensionDetails = HitDetailsBaseResponse.failed('接口返回status != 200', DimensionSourceEnums.EnterpriseLib);
  //   }
  //   return dimensionDetails;
  // }

  /**
   *  被列入非正常户  TODO: 迁移到 companyDetail
   * @param data
   * @param dimensionDetails
   * @returns
   */
  private async getBusinessAbnormal4(data: HitEnterpriseDimensionQueryParam, dimensionDetails: HitDetailsBaseResponse) {
    const companyDetail = await this.companySearchService.companyDetailsQcc(data.keyNo);
    const taxUnnormalCount = companyDetail.CountInfo['TaxUnnormalCount'];
    if (!taxUnnormalCount) {
      return dimensionDetails;
    }
    const taxResponse = await this.companyDetailsService.getTaxUnnormals(data);
    if (!taxResponse) {
      return dimensionDetails;
    }
    const taxUnormals = taxResponse?.Result[0];
    const joinTime = taxUnormals.JoinDate || 0;
    if (joinTime == 0) {
      Object.assign(dimensionDetails, {
        Result: [
          [
            {
              label: '纳税人识别号',
              value: taxUnormals?.CaseNo,
            },
            {
              label: '列入机关',
              value: taxUnormals?.ExecuteGov,
            },
          ],
        ],
        Paging: { TotalRecords: 1 },
      });
    } else {
      Object.assign(dimensionDetails, {
        Result: [
          [
            {
              label: '纳税人识别号',
              value: taxUnormals?.CaseNo,
            },
            { label: '列入机关', value: taxUnormals?.ExecuteGov },
            {
              label: '列入日期',
              value: moment(joinTime * 1000).format(DATE_FORMAT),
            },
          ],
        ],
        Paging: { TotalRecords: 1 },
      });
    }
    return dimensionDetails;
  }

  private async getNoQualityCertification(data: HitEnterpriseDimensionQueryParam, sourcePath: string, dimensionDetails: HitDetailsBaseResponse) {
    const { keyNo, pageSize, pageIndex } = data;
    const result1 = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      isNew: true,
      isNewAgg: true,
    });
    if (result1?.Result?.some((x) => x?.CertificateCode == '*********')) {
      Object.assign(dimensionDetails, {
        Result: [],
        Paging: { TotalRecords: 0, PageIndex: pageIndex, PageSize: pageSize },
      });
    } else {
      Object.assign(dimensionDetails, {
        Result: [{ description: '该企业无有效质量管理体系认证资质' }],
        Paging: { TotalRecords: 1, PageIndex: pageIndex, PageSize: pageSize },
      });
    }
    return dimensionDetails;
  }

  private async getNoCertification(
    data: HitEnterpriseDimensionQueryParam,
    sourcePath: string,
    dimension: DimensionDefinitionPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const { keyNo, pageSize, pageIndex } = data;
    const result1 = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      isNew: true,
      isNewAgg: true,
    });
    const queryPo = dimension.strategyModel.detailsParams.find((d) => d.field == QueryParamsEnums.certification);
    const options = queryPo.fieldVal;

    Object.assign(dimensionDetails, {
      Result: [],
      Paging: { TotalRecords: 0, PageIndex: pageIndex, PageSize: pageSize },
    });
    for (const item of options) {
      if (item.status == 0) continue;
      const hasValidCerts = result1?.Result?.find((x) => x?.CertificateCode == item.key)?.ValidKeywords?.length;
      if (!hasValidCerts) {
        Object.assign(dimensionDetails, {
          Result: [{ description: '该企业无有效关键资质认证' }],
          Paging: { TotalRecords: 1, PageIndex: pageIndex, PageSize: pageSize },
        });
        break;
      }
    }
    return dimensionDetails;
  }

  private async getCertification(
    data: HitEnterpriseDimensionQueryParam,
    sourcePath: string,
    dimension: DimensionDefinitionPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const { keyNo, pageSize, pageIndex } = data;
    const result1 = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo,
      isNew: true,
      isNewAgg: true,
    });
    const nearExpirationType = dimension.strategyModel.detailsParams.find((d) => d.field == QueryParamsEnums.nearExpirationType);
    const dateType = nearExpirationType?.fieldVal ? nearExpirationType?.fieldVal : 2;
    const expirationDate = getExpirationDate(dateType);
    const nowDate = moment().unix();
    const Result = [];
    // 营业执照处理
    const businessLicense = dimension.strategyModel.detailsParams.find((d) => d.field == QueryParamsEnums.businessLicense && d.status == 1);
    if (businessLicense) {
      const businessLicenseResult = await this.getBusinessLicenseResult(keyNo, dateType, expirationDate, nowDate);
      if (businessLicenseResult) {
        Result.push(businessLicenseResult);
      }
    }
    // 纳税资质处理
    const taxpayerList = dimension.strategyModel.detailsParams.find((d) => d.field == QueryParamsEnums.taxpayerList);
    if (taxpayerList?.fieldVal?.length > 0) {
      const taxpayerResult = await this.getTaxpayerListResult(keyNo, dateType, expirationDate, nowDate, taxpayerList);
      if (taxpayerResult?.length > 0) {
        Result.push(...taxpayerResult);
      }
    }
    // 资质证书处理
    const queryCertification = dimension.strategyModel.detailsParams.find((d) => d.field == QueryParamsEnums.certification);
    const certifications = queryCertification.fieldVal;
    for (let i = 0; i < certifications.length; i++) {
      const item = certifications[i];
      if (item.status == 0) continue;
      const resultItem = result1?.Result?.find(
        (x) =>
          x?.CertificateCode == item.key ||
          x?.LatestDoc?.CommonInfo?.filter((y) => y?.Key == '002')?.[0]
            ?.Value?.split(',')
            ?.includes(item.key),
      );
      const startDate = resultItem?.LatestDoc?.StartDate;
      const endDate = resultItem?.LatestDoc?.EndDate;
      const certificationId = resultItem?.LatestDoc?.Id;
      let expirationDesc = '';
      let name = '';
      let index = i;
      if (resultItem) {
        if (resultItem.LatestDoc?.CertificateStatusDesc !== '有效' || (isNumber(endDate) && endDate == 0) || endDate > expirationDate) {
          // 非正常状态(过期失效 || 撤销 || 注销) || 正常有效状态（长期有效的 || 过期时间在预设的到期日期之后的 ）
          expirationDesc = resultItem.LatestDoc?.CertificateStatusDesc;
          if (expirationDesc !== '有效') {
            // 过期状态
            index = 1000 + i;
          }
        } else {
          // 有效状态（过期时间在预设的到期日期之内的）
          switch (dateType) {
            case 1:
              expirationDesc = '近7日到期';
              break;
            case 3:
              expirationDesc = '近3个月到期';
              break;
            case 2:
            default:
              expirationDesc = '近1个月到期';
          }
        }
        if (item.key == resultItem.CertificateCode) {
          name = resultItem.CertificateCodeDesc;
        } else {
          name = resultItem.LatestDoc.CommonInfo.find((y) => y.Key == '003')?.Value;
        }
      } else {
        name = item.keyName;
        index = 1000 + i;
        expirationDesc = '缺失';
      }
      if (expirationDesc != '有效') {
        Result.push({
          index,
          name,
          startDate,
          endDate,
          expirationDesc,
          certificationId,
          certificationType: '资质证书',
        });
      }
    }
    Result.sort((a, b) => {
      return b.index - a.index;
    });
    Object.assign(dimensionDetails, {
      Result: Result.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
      Paging: { TotalRecords: Result.length, PageIndex: pageIndex, PageSize: pageSize },
    });
    return dimensionDetails;
  }

  async getBillDefaults(
    data: HitEnterpriseDimensionQueryParam,
    sourcePath: string,
    dimension: DimensionDefinitionPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    // const validField = dimension.strategyModel.detailsParams.find((d) => d.field === QueryParamsEnums.isValid);
    // const validData = Number(validField?.fieldVal) === -1 ? '1,0' : String(validField?.fieldVal);
    const postData = {
      keyNo: data.keyNo,
      pageSize: data?.pageSize || 5,
      pageIndex: data?.pageIndex || 1,
      isOnlyMainData: true,
      sortField: data?.field || 'publishdate', //默认发布时间排序
      isSortAsc: data?.order === 'ASC',
      isValid: '1',
    };
    const cycle = dimension?.strategyModel?.cycle;
    if (cycle && cycle !== -1) {
      Object.assign(postData, {
        rangeFilters: [
          {
            type: 'publishdate', //发布日期
            dateList: [{ start: moment().subtract(cycle, 'year').unix(), end: moment().unix() }],
          },
        ],
      });
    }
    const response = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, postData);
    if (response?.Result) {
      return Object.assign(dimensionDetails, pick(response, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  private async getBondDefaults(
    data: HitEnterpriseDimensionQueryParam,
    validField: DimensionQueryPO,
    sourcePath: string,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const postData = {
      keyNo: data.keyNo,
      pageSize: data?.pageSize || 5,
      pageIndex: data?.pageIndex || 1,
      sortField: data?.field || 'maturitydate', //默认到期日期排序，支持[newdefaultdate, firstdefaultdate, overduecapitalval, maturitydate]
      isSortAsc: data?.order === 'ASC',
    };
    if (parseInt(validField.fieldVal) == -1) {
      postData['isValid'] = [0, 1];
      //不限，所有状态数据
      postData['statusList'] = [1, 2, 3];
    } else {
      postData['isValid'] = [1];
      //当前有效，过滤掉已兑付数据
      postData['statusList'] = [1, 2];
    }
    const response = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, postData);
    if (response?.Result) {
      const result = response.Result.map((i) => {
        return {
          Id: i.RelatedSec,
          BondShortName: i.Sname,
          BondTypeName: i.BondTypeSecondCodeDesc,
          FirstDefaultDate: moment(Number(i?.FirstDefaultDate) * 1000).format('YYYYMMDD') || '-',
          AccuOverdueCapital: i?.OverdueCapital || 0,
          AccuOverdueInterest: i?.OverDueInterest || 0,
          MaturityDate: moment(Number(i?.MaturityDate) * 1000).format('YYYYMMDD') || '-',
          DefaultStatusDesc: i?.NewStatusDesc,
        };
      });
      return Object.assign(dimensionDetails, pick(response, ['Paging']), { Result: result });
    }
    return dimensionDetails;
  }

  private async getLiquidation(data: HitEnterpriseDimensionQueryParam, sourcePath: string, dimensionDetails: HitDetailsBaseResponse) {
    const liquidationDetail = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, {
      keyNo: data.keyNo,
    });
    if (liquidationDetail?.Result) {
      return Object.assign(dimensionDetails, {
        Result: [liquidationDetail.Result],
        Paging: {
          PageSize: data.pageSize,
          PageIndex: data.pageIndex,
          TotalRecords: 1,
        },
      });
    }
    return dimensionDetails;
  }

  private async getFinancialHealth(
    data: HitEnterpriseDimensionQueryParam,
    sourcePath: string,
    dimension: DimensionDefinitionPO,
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const extractAssetLiabilityRatio = (result: any) => {
      const reportField = result.ReportFields.find((f: any) => f.AccountName === 'asset_liability_ratio');
      return reportField?.FieldList?.find((f: any) => f.FinancialAccount === 'asset_liability_ratio')?.ShowValue || null;
    };
    const url = `${this.configService.proxyServer.dataService}${sourcePath}`;
    const requestPayload = { keyNo: data.keyNo, reportType: 1 };

    // 获取财务数据
    const financialHealthRes = await this.httpUtils.postRequest(url, requestPayload);
    if (!financialHealthRes?.Result) return dimensionDetails;

    // 查找资产负债率策略参数
    const queryPo = dimension.strategyModel.detailsParams.find((d) => d.field === QueryParamsEnums.assetLiabilityRatio);
    if (!queryPo) return dimensionDetails;

    // 获取资产负债率值
    const assetLiabilityRatio = extractAssetLiabilityRatio(financialHealthRes.Result);
    if (!assetLiabilityRatio) return dimensionDetails;

    // 比较资产负债率值
    const isMatch = getCompareResult(parseFloat(assetLiabilityRatio), queryPo.fieldVal, queryPo.fieldOperator);

    // 返回匹配结果
    return isMatch
      ? Object.assign(dimensionDetails, {
          Result: [
            {
              label: '资产负债率(%)',
              value: assetLiabilityRatio,
            },
          ],
          Paging: { TotalRecords: 1 },
        })
      : dimensionDetails;
  }

  private async getMainInfoUpdateInfos(
    dimension: DimensionDefinitionPO,
    data: Record<string, any>,
    updateInfos: any[],
    dimensionDetails: HitDetailsBaseResponse,
  ) {
    const cycle: number = dimension?.strategyModel?.cycle || 1;
    const orginUpdateInfos = cloneDeep(updateInfos);
    //filter 根据自定义查询条件筛选
    const filterInfos = [];
    for (let i = updateInfos.length - 1; i >= 0; i--) {
      const item = updateInfos[i];
      if (cycle == -1 || moment().diff(moment(item?.ChangeDate, DATE_FORMAT), 'years', true) <= cycle) {
        const afterItem = orginUpdateInfos[i - 1] || {};
        if (i === 0) {
          item['after'] = await this.getCompanyDetailCurrentScope(dimension, data.keyNo);
        } else {
          item['after'] = afterItem;
        }
        filterInfos.push(item);
      }
    }
    //按照时间倒序
    filterInfos.sort((a, b) => {
      return moment(b?.ChangeDate).diff(moment(a?.ChangeDate));
    });
    const pageSize = data?.pageSize || 10;
    const pageIndex = data?.pageIndex || 1;
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    dimensionDetails.Paging = { PageSize: pageSize, PageIndex: pageIndex, TotalRecords: filterInfos.length };
    dimensionDetails.Result = filterInfos.slice(start, end);
    return dimensionDetails;
  }

  private async getStockPledgeDetails(data: HitEnterpriseDimensionQueryParam, dimension: DimensionDefinitionPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo, pageIndex, pageSize } = data;
    const pledgeStatus = dimension?.strategyModel?.detailsParams?.find((q) => q.field === QueryParamsEnums.pledgeStatus)?.fieldVal;
    const params = {
      keyNo,
      batchType: 'StockPledge', // Honor 荣誉,StockPledge 股权质押,Financing 融资信息,SupplierCustomer 供应商
      status: pledgeStatus,
      pageIndex,
      pageSize,
      rangeFilters: [],
      sortField: dimension.strategyModel.sortField.field,
      isSortAsc: dimension.strategyModel.sortField.order === 'ASC',
    };

    if (dimension.strategyModel?.cycle && dimension.strategyModel?.cycle !== -1) {
      const dateStartEnd = {};
      dateStartEnd['start'] = getStartTimeByCycle(dimension.strategyModel.cycle) / 1000;
      dateStartEnd['end'] = moment().unix();
      params.rangeFilters.push({
        type: 'publicdate',
        dateList: [dateStartEnd],
      });
    }

    const httpResponse = await this.httpUtils.postRequest(this.configService.proxyServer.dataService + sourcePath, params);
    if (httpResponse?.Result) {
      return Object.assign(dimensionDetails, pick(httpResponse, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  private async getIprPledgeDetails(data: HitEnterpriseDimensionQueryParam, dimension: DimensionDefinitionPO, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo, pageIndex, pageSize } = data;
    const isValid = dimension?.strategyModel?.detailsParams?.find((q) => q.field === QueryParamsEnums.isValid)?.fieldVal;
    const params = {
      keyNo,
      isValid: isValid == -1 ? '0,1' : isValid,
      pageIndex,
      pageSize,
    };

    if (dimension.strategyModel?.cycle && dimension.strategyModel?.cycle !== -1) {
      params['startDate'] = getStartTimeByCycle(dimension.strategyModel.cycle) / 1000;
      params['endDate'] = moment().unix();
    }
    const httpResponse = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + sourcePath, params);
    if (httpResponse?.Result) {
      return Object.assign(dimensionDetails, pick(httpResponse, ['Result', 'Paging']));
    }
    return dimensionDetails;
  }

  async getTaxpayerListResult(keyNo: string, dateType: number, expirationDate: number, nowDate: number, taxpayerList: DimensionQueryPO) {
    const result = await this.companyDetailsService.getGeneralTaxPayerList(keyNo);
    const taxpayerResult = [];
    for (const item of taxpayerList.fieldVal) {
      if (item.status == 0) {
        continue;
      }
      const itemResult = [];
      const taxpayerList = result?.Result;
      let hasValidTaxpayer = false;
      if (taxpayerList?.length > 0) {
        for (const taxpayer of taxpayerList) {
          if (taxpayer.TaxpayerType.includes(item.key) && taxpayer.DataStatus === 1) {
            hasValidTaxpayer = true;
            const startDate = taxpayer.DateFrom;
            const endDate = taxpayer.DateTo;
            const expirationDesc = this.getExpirationDesc(endDate, expirationDate, nowDate, dateType);
            if (expirationDesc != '有效') {
              itemResult.push({
                index: 1,
                name: taxpayer.TaxpayerType,
                startDate,
                endDate,
                expirationDesc,
                certificationType: '纳税人资质',
              });
            } else {
              // RA-15988 存在任一有效资质,则不再继续查询
              return [];
            }
          }
        }
      }
      if (!hasValidTaxpayer) {
        itemResult.push({
          index: 1,
          name: item.keyName,
          startDate: null,
          endDate: null,
          expirationDesc: '缺失',
          certificationType: '纳税人资质',
        });
      }
      if (itemResult.length > 0) {
        taxpayerResult.push(...itemResult);
      }
    }
    return taxpayerResult;
  }

  private getExpirationDesc(endDate: any, expirationDate: number, nowDate: number, dateType: number) {
    let expirationDesc = '';
    if ((isNumber(endDate) && endDate == 0) || endDate > expirationDate) {
      expirationDesc = '有效';
    } else if (nowDate >= endDate) {
      expirationDesc = '已到期';
    } else {
      switch (dateType) {
        case 1:
          expirationDesc = '近7日到期';
          break;
        case 3:
          expirationDesc = '近3个月到期';
          break;
        case 2:
        default:
          expirationDesc = '近1个月到期';
      }
    }
    return expirationDesc;
  }

  private getRangeFilters(dimensionQueryPO: DimensionQueryPO) {
    // [ { "type": "amount", "rangeList": [ { "start": 0, "end": 2000000000 } ] } ]
    const amount = dimensionQueryPO.fieldVal * 10000 || 0;
    const operator = dimensionQueryPO.fieldOperator || OperatorEnums.ge;
    if (operator === OperatorEnums.ge || operator === OperatorEnums.gt) {
      return [{ type: 'amount', rangeList: [{ start: amount }] }];
    }
    if (operator === OperatorEnums.le || operator === OperatorEnums.lt) {
      return [{ type: 'amount', rangeList: [{ end: amount }] }];
    }
    return [];
  }

  private async getShellCompanyLabel(data: HitEnterpriseDimensionQueryParam, sourcePath: string) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const { keyNo } = data;
    const companyShellLabel = await this.httpUtils.postRequest(this.configService.server.qccPro.slbUrl + sourcePath, {
      dataEntity: { keyno: keyNo },
    });
    if (companyShellLabel?.result?.companyLabelTO?.labelHasDetail?.length > 0 || companyShellLabel?.result?.companyLabelTO?.labelNoDetail?.length > 0) {
      dimensionDetails.Result = [companyShellLabel.result.companyLabelTO];
      dimensionDetails.Paging = { TotalRecords: 1, PageIndex: 1, PageSize: 5 };
    }
    return dimensionDetails;
  }

  private async getBusinessAbnormal2Details(data: HitEnterpriseDimensionQueryParam, dimension: DimensionDefinitionPO) {
    const dimensionDetails = HitDetailsBaseResponse.ok();
    const queryParam = {
      keyNo: data.keyNo,
      pageSize: data?.pageSize || 5,
      pageIndex: data?.pageIndex || 1,
    };
    const dimensionQueryPo = dimension.strategyModel?.detailsParams?.find((p) => p.field == QueryParamsEnums.simpleCancellationStep);
    if (dimensionQueryPo) {
      const caseTypes = dimensionQueryPo.fieldVal.filter((item) => item.status);
      if (caseTypes?.length) {
        const caseType = caseTypes.map((k) => k.key).join(',');
        Object.assign(queryParam, { caseType });
      }
    }
    const result = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + dimension.sourcePath, queryParam);
    if (result?.Status == 200) {
      const { Result, Paging } = result;
      const newRes = Result.map((r) => {
        return {
          annoName: `${r.CompanyName}简易注销公告`,
          id: r.No,
          publishDate: r.PublicDate,
          resultContent: r.CancellationResultList?.[0]?.ResultContent,
        };
      });
      return Object.assign(dimensionDetails, {
        Result: newRes,
        Paging,
      });
    } else {
      return HitDetailsBaseResponse.failed('接口返回status != 200', DimensionSourceEnums.EnterpriseLib);
    }
  }
}
