import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cacheable } from '@type-cacheable/core';
import * as Bluebird from 'bluebird';
import { GroupsEntity } from 'libs/entities/GroupsEntity';
import { LabelEntity } from 'libs/entities/LabelEntity';
import { QueryParamsEnums } from 'libs/model/diligence/pojo/dimension/dimension.filter.params';
import { isEmpty, some, union, uniq } from 'lodash';
import { Logger } from 'log4js';
import * as moment from 'moment';
import { Brackets, In, MoreThanOrEqual, Repository } from 'typeorm';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';
import { ConfigService } from '../../../libs/config/config.service';
import { HttpUtilsService } from '../../../libs/config/httputils.service';
import { CustomerEntity } from '../../../libs/entities/CustomerEntity';
import { DepartmentEntity } from '../../../libs/entities/DepartmentEntity';
import { InnerBlacklistEntity } from '../../../libs/entities/InnerBlacklistEntity';
import { BatchStatusEnums } from '../../../libs/enums/batch/BatchStatusEnums';
import { DurationEnums } from '../../../libs/enums/blacklist/DurationEnums';
import { StreamOperationEnum } from '../../../libs/enums/data/StreamOperationEnum';
import { StreamTableEnums } from '../../../libs/enums/data/StreamTableEnums';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { DirectConnectionsPo, TenderInnerBlackListResPo } from '../../../libs/model/bidding/model/TenderInnerBlackListResPo';
import { CompanyInvestigationPo } from '../../../libs/model/company/CompanyInvestigationPo';
import { DimensionQueryPO } from '../../../libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { BlacklistConnectionDDResponse } from '../../../libs/model/diligence/pojo/graph/BlacklistConnectionDDResponse';
import { BlacklistDirectConnectionPO } from '../../../libs/model/diligence/pojo/graph/BlacklistDirectConnectionPO';
import { CompanyAnalyzedV2PO } from '../../../libs/model/diligence/pojo/graph/CompanyAnalyzedV2PO';
import { CommonType, DepartmentType } from '../../../libs/model/diligence/pojo/graph/CustomerRelationRangeEnum';
import { GraphRelation, RelationPath } from '../../../libs/model/diligence/pojo/graph/GraphRelation';
import { PartnerConnectionDDResponse } from '../../../libs/model/diligence/pojo/graph/PartnerConnectionDDResponse';
import { RoverGraphParsedPO } from '../../../libs/model/diligence/pojo/graph/RoverGraphParsedPO';
import { isEdge } from '../../batch/message.handler/export/model/path-node/data-node';
import { generateEdgePathKey, processRelationRoles } from '../../batch/message.handler/export/model/path-node/data-node-utils';
import { getMergedResultV2 } from '../../batch/message.handler/export/processors/DimensionDetailValFormatUtil';
import { RoverService } from '../source/rover.service';
import { processPaths } from '../utils/path.util';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { ConditionOperatorEnums } from '../../../libs/enums/diligence/ConditionOperatorEnums';

@Injectable()
export class RoverGraphHelper {
  private readonly logger: Logger = QccLogger.getLogger(RoverGraphHelper.name);

  constructor(
    private readonly httpUtilsService: HttpUtilsService,
    private readonly configService: ConfigService,
    private readonly roverService: RoverService,
    @InjectRepository(InnerBlacklistEntity) private readonly innerBlacklistRepo: Repository<InnerBlacklistEntity>,
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
    @InjectRepository(DepartmentEntity) private readonly departmentRepo: Repository<DepartmentEntity>,
    @InjectRepository(LabelEntity) private readonly labelRepo: Repository<LabelEntity>,
    @InjectRepository(GroupsEntity) private readonly groupRepo: Repository<GroupsEntity>,
  ) {}

  public async syncToNebula(orgId: number, ids: number[], table: StreamTableEnums, operation: StreamOperationEnum) {
    this.logger.info(`sync changes to nebula: orgId=${orgId},table=${table},operation=${operation},ids=${ids}`);
    try {
      return await this.httpUtilsService.postRequest(this.configService.roverGraphServer.syncManually, {
        orgId,
        ids,
        table,
        operation,
      });
    } catch (e) {
      this.logger.error('sync data to nebula throw exception:' + e.message || e);
      this.logger.error(e);
    }
  }

  /**
   * 与第三方列表存在交叉重叠关系分支机构关系排查（支持穿透）
   * @param requestParam
   */
  public async getCustomerInvestigation(requestParam: CompanyInvestigationPo): Promise<CompanyAnalyzedV2PO[]> {
    const { orgId, companyId, depth, percentage, types, rangeCompanyIds, excludedTypes } = requestParam;
    this.logger.info(`scan CustomerInvestigation orgId:${orgId} companyId:${companyId} begin depth:${depth} percentage:${percentage} types:${types}`);
    try {
      const nebulaParam = {
        orgId,
        companyId,
        depth,
        percentage,
        types: this.convertRelationTypesToEdgeTypes(types),
        excludedTypes,
      };
      const startTime = Date.now();
      const partnerResponse: CompanyAnalyzedV2PO[] = await this.httpUtilsService.postRequest(
        this.configService.roverGraphServer.customerInvestigation,
        nebulaParam,
      );
      const endTime = Date.now();
      this.logger.info(`http cost time:${endTime - startTime}ms`);
      this.logger.debug(`scan CustomerInvestigation orgId:${orgId} companyId:${companyId}, result:${JSON.stringify(partnerResponse)}`);
      const rangeList = this.checkInvestigationList(partnerResponse, rangeCompanyIds);
      const res = this.processInvestigationList(rangeList);
      const endTime2 = Date.now();
      this.logger.info(`process path cost time2:${endTime2 - endTime}ms`);
      return res;
    } catch (e) {
      this.logger.error('scan CustomerInvestigation throw exception:' + e.message || e);
      return null;
    }
  }

  /**
   * 与第三方列表存在交叉重叠关系分支机构关系排查（支持穿透）V2版本
   * @param requestParam
   */
  public async getCustomerInvestigationV2(requestParam: CompanyInvestigationPo): Promise<CompanyAnalyzedV2PO[]> {
    const { orgId, companyId, depth, percentage, types, excludedTypes, dataRangeCondition } = requestParam;
    this.logger.info(`scan CustomerInvestigationV2 orgId:${orgId} companyId:${companyId} begin depth:${depth} percentage:${percentage} types:${types}`);
    try {
      const nebulaParam = {
        orgId,
        companyId,
        depth,
        percentage,
        types: this.convertRelationTypesToEdgeTypes(types),
        excludedTypes,
        dataRangeCondition,
      };
      const startTime = Date.now();
      const partnerResponse: CompanyAnalyzedV2PO[] = await this.httpUtilsService.postRequest(
        this.configService.roverGraphServer.customerInvestigationV2,
        nebulaParam,
      );
      const endTime = Date.now();
      this.logger.info(`http cost time:${endTime - startTime}ms`);
      this.logger.debug(`scan CustomerInvestigationV2 orgId:${orgId} companyId:${companyId}, result:${JSON.stringify(partnerResponse)}`);
      const res = this.processInvestigationList(partnerResponse);
      const endTime2 = Date.now();
      this.logger.info(`process path cost time2:${endTime2 - endTime}ms`);
      return res;
    } catch (e) {
      this.logger.error('scan CustomerInvestigationV2 throw exception:' + e.message || e);
      return null;
    }
  }

  /**
   * 与第三方列表存在交叉重叠关系分支机构关系排查（支持穿透）统计数字
   * @param requestParam
   */
  public async getCustomerInvestigationCount(requestParam: CompanyInvestigationPo): Promise<number> {
    const { orgId, companyId, depth, percentage, types, rangeCompanyIds, excludedTypes } = requestParam;
    this.logger.info(`scan CustomerInvestigation get count orgId:${orgId} companyId:${companyId} begin depth:${depth} percentage:${percentage} types:${types}`);
    try {
      const nebulaParam = {
        orgId,
        companyId,
        depth,
        percentage,
        types: this.convertRelationTypesToEdgeTypes(types),
        excludedTypes,
      };
      const relatedCompanyIds = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.customerInvestigationCount, nebulaParam);
      // 过滤掉不在范围内的公司
      const rangeCompanyIdSet = new Set(rangeCompanyIds);
      return relatedCompanyIds.filter((id) => rangeCompanyIdSet.has(id))?.length;
    } catch (e) {
      this.logger.error('scan CustomerInvestigation throw exception:' + e.message || e);
      return 0;
    }
  }

  /**
   * 检查investigationList是否为空
   * @param investigationList
   * @param rangeCompanyIds
   * @returns
   */
  private checkInvestigationList(investigationList: CompanyAnalyzedV2PO[], rangeCompanyIds: string[]) {
    if (!investigationList?.length || !rangeCompanyIds?.length) {
      return [];
    }
    if (!investigationList?.length || !rangeCompanyIds?.length) {
      return [];
    }
    // 使用Set而不是数组的includes方法进行查找，将O(n)的查找复杂度降为O(1)
    const rangeCompanyIdSet = new Set(rangeCompanyIds);
    // 一次性过滤出符合条件的公司
    const responseList = investigationList.filter((c) => rangeCompanyIdSet.has(c.companyKeynoRelated));
    if (!responseList.length) {
      return [];
    }
    return responseList;
  }

  /**
   * 取设置范围内的公司列表返回合并后的路径结果
   * @private
   * @param responseList
   */
  public processInvestigationList(responseList: CompanyAnalyzedV2PO[]) {
    // 结果数组，提前分配容量
    const result = new Array(responseList.length);

    // 处理每个公司的数据
    for (let i = 0; i < responseList.length; i++) {
      const item = responseList[i];

      // 跳过没有关系路径的项
      if (!item?.relationPaths?.length) {
        result[i] = item;
        continue;
      }

      // 处理路径
      item.relations = processPaths(item.relationPaths.map(processRelationRoles));

      // 使用Map代替对象进行边的去重，提高性能
      const dedupeByEdgeMap = new Map();

      // 处理所有的关系
      if (item.relations) {
        for (const nodes of item.relations) {
          // 生成边的唯一标识
          const dedupeEdgeId = generateEdgePathKey(nodes);

          // 检查是否已经存在这个边模式
          if (dedupeByEdgeMap.has(dedupeEdgeId)) {
            const existingNodes = dedupeByEdgeMap.get(dedupeEdgeId);

            // 合并节点属性
            for (let j = 0; j < existingNodes.length; j++) {
              const existingNode = existingNodes[j];
              const newNode = nodes[j];

              if (isEdge(existingNode) && isEdge(newNode)) {
                // 合并roles
                existingNode.roles = uniq([...(existingNode.roles || []), ...(newNode.roles || [])]);

                // 高效合并data属性
                if (newNode.data?.length) {
                  if (!existingNode.data) {
                    existingNode.data = [];
                  }

                  // 使用Map进行O(1)的查找
                  const dataTypeMap = new Map();
                  for (let k = 0; k < existingNode.data.length; k++) {
                    dataTypeMap.set(existingNode.data[k].type, k);
                  }

                  for (const dataItem of newNode.data) {
                    if (dataTypeMap.has(dataItem.type)) {
                      // 更新现有数据
                      const idx = dataTypeMap.get(dataItem.type);
                      existingNode.data[idx].data = [...existingNode.data[idx].data, ...dataItem.data];
                    } else {
                      // 添加新的数据类型
                      existingNode.data.push(dataItem);
                    }
                  }
                }

                // 使用Set合并groups
                if (newNode.groups?.length) {
                  if (!existingNode.groups) {
                    existingNode.groups = [...newNode.groups];
                  } else {
                    const groupSet = new Set(existingNode.groups);
                    for (const group of newNode.groups) {
                      groupSet.add(group);
                    }
                    existingNode.groups = Array.from(groupSet);
                  }
                }
              }
            }
          } else {
            // 存储新的关系模式
            dedupeByEdgeMap.set(dedupeEdgeId, nodes);
          }
        }
      }

      // 获取合并后的关系，限制99条
      const mergedRelations = Array.from(dedupeByEdgeMap.values()).slice(0, 99);

      // 创建结果对象
      result[i] = {
        ...item,
        relations: mergedRelations,
        relations2: mergedRelations, // 复用同一个数组引用节省内存
      };
    }

    return result;
  }

  /**
   * V2版本：优化后的处理方法
   */
  public processInvestigationListV2(investigationList: CompanyAnalyzedV2PO[], rangeCompanyIds: string[]) {
    if (!investigationList?.length || !rangeCompanyIds?.length) {
      return [];
    }
    const rangeCompanyIdSet = new Set(rangeCompanyIds);
    const responseList = investigationList.filter((c) => rangeCompanyIdSet.has(c.companyKeynoRelated));
    if (!responseList.length) {
      return [];
    }
    return getMergedResultV2(responseList);
  }

  /**
   * V3版本：优化处理逻辑，减少循环次数和数据转换
   */
  public processInvestigationListV3(investigationList: CompanyAnalyzedV2PO[], rangeCompanyIds: string[]) {
    if (!investigationList?.length || !rangeCompanyIds?.length) {
      return [];
    }

    // 使用Set优化查找性能
    const rangeCompanyIdSet = new Set(rangeCompanyIds);

    // 一次性完成过滤和处理
    return investigationList
      .filter((c) => rangeCompanyIdSet.has(c.companyKeynoRelated))
      .map((company) => {
        if (!company?.relationPaths?.length) {
          return company;
        }

        // 用于存储已处理的边的Map
        const dedupeByEdgeMap = {};

        // 处理所有路径
        company.relationPaths.forEach((path) => {
          // 先处理整个路径
          const processedPath = processRelationRoles(path);

          // 生成边的唯一ID
          const edges = processedPath.filter(isEdge);
          if (!edges.length) return;

          const edgeKeys = edges.map((edge: any) => `${edge.startid}-${edge.endid}`).join('|');

          // 合并相同路径的边属性
          if (dedupeByEdgeMap[edgeKeys]) {
            // 遍历路径中的每个节点
            processedPath.forEach((node: any, i: number) => {
              const existingNode = dedupeByEdgeMap[edgeKeys][i];
              if (isEdge(node) && isEdge(existingNode)) {
                // 合并roles
                existingNode.roles = uniq([...(existingNode.roles || []), ...(node.roles || [])]);

                // 合并data
                if (node.data?.length) {
                  existingNode.data = existingNode.data || [];
                  node.data.forEach((item: any) => {
                    const existingDataIndex = existingNode.data.findIndex((d: any) => d.type === item.type);
                    if (existingDataIndex > -1) {
                      // 如果已存在相同type的数据，合并data数组
                      existingNode.data[existingDataIndex].data = uniq([...existingNode.data[existingDataIndex].data, ...item.data]);
                    } else {
                      // 如果不存在，添加新的数据项
                      existingNode.data.push({ ...item });
                    }
                  });
                }

                // 合并groups
                if (node.groups?.length) {
                  existingNode.groups = [...new Set([...(existingNode.groups || []), ...node.groups])];
                }
              }
            });
          } else {
            // 如果是新的路径，直接存储
            dedupeByEdgeMap[edgeKeys] = processedPath;
          }
        });

        // 获取合并后的关系，限制99条
        const mergedRelations = Object.values(dedupeByEdgeMap)?.slice(0, 99);

        return {
          ...company,
          relations: mergedRelations,
          relations2: mergedRelations,
        };
      });
  }

  private isExcludeType(relationPath: RelationPath[]) {
    //relationPath奇数下标的 item 是边，获取所有边类型，判断是否包含 Blacklist,Customer
    for (let i = 1; i < relationPath?.length - 1; i += 2) {
      const edge = relationPath[i] as GraphRelation;
      if (['Customer', 'Blacklist'].includes(edge.type)) {
        return true;
      }
      return false;
    }
  }

  /**
   * 与内部黑名单列表存在关联关系分支机构关系排查（支持穿透）
   * @param requestParam
   */
  public async getBlacklistInvestigation(requestParam: CompanyInvestigationPo): Promise<CompanyAnalyzedV2PO[]> {
    const { orgId, companyId, depth, percentage, types, rangeCompanyIds, excludedTypes } = requestParam;
    this.logger.info(`scan blacklist Investigation orgId:${orgId} companyId:${companyId}，depth:${depth} percentage:${percentage} types:${types} begin`);
    try {
      const nebulaParam = {
        orgId,
        companyId,
        depth,
        percentage,
        types: this.convertRelationTypesToEdgeTypes(types),
        excludedTypes,
      };
      const blackListResponse: CompanyAnalyzedV2PO[] = await this.httpUtilsService.postRequest(
        this.configService.roverGraphServer.blacklistInvestigation,
        nebulaParam,
      );
      this.logger.debug(`scan blacklist Investigation orgId:${orgId} companyId:${companyId}, result:${JSON.stringify(blackListResponse)}`);
      const rangeList = this.checkInvestigationList(blackListResponse, rangeCompanyIds);
      return this.processInvestigationList(rangeList);
    } catch (e) {
      this.logger.error('scan blacklist Investigation throw exception:' + e.message || e);
      return null;
    }
  }

  /**
   * 与内部黑名单列表存在关联关系分支机构关系排查（支持穿透）V2版本
   * @param requestParam
   * @returns
   */
  public async getBlacklistInvestigationV2(requestParam: CompanyInvestigationPo): Promise<CompanyAnalyzedV2PO[]> {
    const { orgId, companyId, depth, percentage, types, excludedTypes, dataRangeCondition } = requestParam;
    this.logger.info(`scan blacklist InvestigationV2 orgId:${orgId} companyId:${companyId}，depth:${depth} percentage:${percentage} types:${types} begin`);
    try {
      const nebulaParam = {
        orgId,
        companyId,
        depth,
        percentage,
        types: this.convertRelationTypesToEdgeTypes(types),
        excludedTypes,
        dataRangeCondition,
      };
      const blackListResponse: CompanyAnalyzedV2PO[] = await this.httpUtilsService.postRequest(
        this.configService.roverGraphServer.blacklistInvestigationV2,
        nebulaParam,
      );
      this.logger.debug(`scan blacklist InvestigationV2 orgId:${orgId} companyId:${companyId}, result:${JSON.stringify(blackListResponse)}`);
      return this.processInvestigationList(blackListResponse);
    } catch (e) {
      this.logger.error('scan blacklist InvestigationV2 throw exception:' + e.message || e);
      return null;
    }
  }

  /**
   * 与内部黑名单列表存在关联关系分支机构关系排查（支持穿透）统计命中数量
   * @param requestParam
   */
  public async getBlacklistInvestigationCount(requestParam: CompanyInvestigationPo): Promise<number> {
    const { orgId, companyId, depth, percentage, types, rangeCompanyIds, excludedTypes } = requestParam;
    this.logger.info(
      `scan blacklist get count Investigation orgId:${orgId} companyId:${companyId}，depth:${depth} percentage:${percentage} types:${types} begin`,
    );
    try {
      const nebulaParam = {
        orgId,
        companyId,
        depth,
        percentage,
        types: this.convertRelationTypesToEdgeTypes(types),
        excludedTypes,
      };
      const relatedCompanyIds = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.blacklistInvestigationCount, nebulaParam);
      // 过滤掉不在范围内的公司
      const rangeCompanyIdSet = new Set(rangeCompanyIds);
      return relatedCompanyIds.filter((id) => rangeCompanyIdSet.has(id))?.length;
    } catch (e) {
      this.logger.error('scan blacklist Investigation get count throw exception:' + e.message || e);
      return 0;
    }
  }

  /**
   * 转换 relationalTypes 到 edgeTypes
   * @param relationTypes
   */
  private convertRelationTypesToEdgeTypes(relationTypes: string[]) {
    const relationEdgeMap = {
      [DetailsParamEnums.ShareholdingRelationship]: ['Invest'],
      [DetailsParamEnums.HisShareholdingRelationship]: ['HisInvest'],
      [DetailsParamEnums.InvestorsRelationship]: ['Invest'],
      [DetailsParamEnums.HisInvestorsRelationship]: ['HisInvest'],
      [DetailsParamEnums.EmploymentRelationship]: ['Employ', 'Legal'],
      [DetailsParamEnums.HisLegalAndEmploy]: ['HisEmploy', 'HisLegal'],
      [DetailsParamEnums.ActualController]: ['ActualController'],
      [DetailsParamEnums.MainInfoUpdateBeneficiary]: ['FinalBenefit'],
      [DetailsParamEnums.Branch]: ['Branch'],
      [DetailsParamEnums.ForeignInvestment]: ['Invest'],
      [DetailsParamEnums.HisForeignInvestment]: ['HisInvest'],
      [DetailsParamEnums.Shareholder]: ['Invest'],
      [DetailsParamEnums.HisShareholder]: ['HisInvest'],
    };
    const edgeTypes = [];
    for (const relationType of relationTypes) {
      if (relationEdgeMap[relationType]) {
        edgeTypes.push(...relationEdgeMap[relationType]);
      } else {
        edgeTypes.push(relationType);
      }
    }
    return uniq(edgeTypes.filter((e) => !['Customer', 'Blacklist'].includes(e)));
  }

  /**
   * 查询所有，交集AND逻辑
   * @param orgId
   * @param groupIds
   * @param labelIds
   * @param departmentIds
   */
  async searchCustomerWithAllConditions(orgId: number, groupIds: number[], labelIds: number[], departmentIds: number[]) {
    if (groupIds?.includes(-2) || labelIds?.includes(-2) || departmentIds?.includes(-2)) {
      return [];
    }

    const qb = this.customerRepo
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.labels', 'label')
      .leftJoinAndSelect('customer.departments', 'department')
      .where('customer.orgId = :orgId', { orgId })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done });
    this.applyCustomerFilter(qb, groupIds, labelIds, departmentIds);
    const companyList = await qb.getMany();
    return companyList?.map((c) => c.companyId) || [];
  }

  private applyCustomerFilter(qb: SelectQueryBuilder<CustomerEntity>, groupIds: number[], labelIds: number[], departmentIds: number[]) {
    this.applyCustomerGroupIds(qb, groupIds);
    this.applyCommonLabelIds(qb, labelIds);
    this.applyCommonDepartmentIds(qb, departmentIds);
  }

  private applyCommonDepartmentIds<T>(qb: SelectQueryBuilder<T>, departmentIds: number[]) {
    if (departmentIds?.length) {
      if (departmentIds.includes(-1)) {
        if (departmentIds.length === 1) {
          qb.andWhere('department.departmentId is null');
        } else {
          const depIdResult = departmentIds.filter((r) => r >= 0);
          qb.andWhere(
            new Brackets((qb1) => {
              qb1.orWhere('department.departmentId is null');
              qb1.orWhere('department.departmentId in (:...depIdResult)', { depIdResult });
            }),
          );
        }
      } else {
        qb.andWhere('department.departmentId in (:...departmentIds)', { departmentIds });
      }
    }
  }

  private applyCommonLabelIds<T>(qb: SelectQueryBuilder<T>, labelIds: number[]) {
    if (labelIds?.length) {
      if (labelIds.includes(-1)) {
        if (labelIds.length === 1) {
          qb.andWhere('label.labelId is null');
        } else {
          const validIds = labelIds.filter((r) => r >= 0);
          qb.andWhere(
            new Brackets((qb1) => {
              qb1.orWhere('label.labelId is null');
              qb1.orWhere('label.labelId IN (:...validIds)', { validIds });
            }),
          );
        }
      } else {
        qb.andWhere('label.labelId IN (:...labelIds)', { labelIds });
      }
    }
  }

  private applyCustomerGroupIds(qb: SelectQueryBuilder<CustomerEntity>, groupIds: number[]) {
    if (groupIds?.length) {
      if (groupIds.includes(-1)) {
        if (groupIds.length === 1) {
          qb.andWhere('customer.groupId is null');
        } else {
          const filterGroupIds = groupIds.filter((r) => r >= 0);
          qb.andWhere(
            new Brackets((qb1) => {
              qb1.orWhere('customer.groupId is null');
              qb1.orWhere('customer.groupId in (:...filterGroupIds)', { filterGroupIds });
            }),
          );
        }
      } else {
        qb.andWhere('customer.groupId in (:...groupIds)', { groupIds });
      }
    }
  }

  /*RA-14034 需求变更为或的关系，考虑到代码复杂度，索引失效（多个 or）问题，拆成三个查询(并集，或者关系)
   */
  async searchCustomerWithAnyCondition(orgId: number, groupIds: number[], labelIds: number[], departmentIds: number[]): Promise<string[]> {
    const [groupCompanyIds, labelCompanyIds, departmentCompanyIds] = await Bluebird.all([
      this.searchByCustomerGroup(orgId, groupIds),
      this.searchByCustomerLabel(orgId, labelIds),
      this.searchByCustomerDepartment(orgId, departmentIds),
    ]);
    return union(groupCompanyIds, labelCompanyIds, departmentCompanyIds);
  }

  private async searchByCustomerGroup(orgId: number, groupIds: number[]) {
    if (groupIds?.includes(-2)) {
      return [];
    }
    const qb = this.customerRepo
      .createQueryBuilder('customer')
      .select('customer.companyId')
      .where('customer.orgId = :orgId', { orgId })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done });

    this.applyCustomerGroupIds(qb, groupIds);
    const companyList = await qb.getMany();
    return companyList.map((c) => c.companyId);
  }

  private async searchByCustomerLabel(orgId: number, labelIds: number[]) {
    if (labelIds?.includes(-2)) {
      return [];
    }
    const qb = this.customerRepo
      .createQueryBuilder('customer')
      .select('customer.companyId')
      .leftJoinAndSelect('customer.labels', 'label')
      .where('customer.orgId = :orgId', { orgId })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done });

    this.applyCommonLabelIds(qb, labelIds);
    const companyList = await qb.getMany();
    return companyList.map((c) => c.companyId);
  }

  private async searchByCustomerDepartment(orgId: number, departmentIds: number[]) {
    if (departmentIds?.includes(-2)) {
      return [];
    }
    const qb = this.customerRepo
      .createQueryBuilder('customer')
      .select('customer.companyId')
      .leftJoinAndSelect('customer.departments', 'department')
      .where('customer.orgId = :orgId', { orgId })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done });

    this.applyCommonDepartmentIds(qb, departmentIds);
    const companyList = await qb.getMany();
    return companyList.map((c) => c.companyId);
  }

  /**
   * 获取第三方范围
   * @param queryPO 查询参数对象
   * @param orgId 组织ID
   * @param userId 用户ID
   * @param companyId 公司ID
   */
  async getRoverRanges(queryPO: DimensionQueryPO, orgId: number, userId: number, companyId: string) {
    try {
      // 根据查询条件判断是否需要从数据库获取客户实体信息
      const dbCustomerEntity = await this.getDbCustomerEntityIfNeeded(queryPO, orgId, companyId);
      const groupIds = await this.processCommonIds(queryPO, DetailsParamEnums.Groups, dbCustomerEntity);
      const labelIds = await this.processCommonIds(queryPO, DetailsParamEnums.Labels, dbCustomerEntity);
      const depIds = await this.processDepartmentIds(queryPO, dbCustomerEntity, orgId, userId);

      // 获取真实存在的分组、标签，将真实存在的Id和groupIds、labelIds取交集
      const [existingGroupIds, existingLabelIds] = await Promise.all([
        this.getExistingGroupIds(orgId, groupIds, 1),
        this.getExistingLabelIds(orgId, labelIds, 1),
      ]);

      return {
        groupIds: existingGroupIds,
        labelIds: existingLabelIds,
        depIds,
      };
    } catch (error) {
      this.logger.error('获取范围时出错:', error);
      return { groupIds: [], labelIds: [], depIds: [] };
    }
  }

  /**
   * 获取黑名单范围
   * @param orgId 组织ID
   * @param groupIds 分组ID列表
   * @param labelIds 标签ID列表
   * @returns 存在的分组ID列表和标签ID列表
   */
  async getBlacklistGroupLabelIds(orgId: number, groupIds: [], labelIds: []) {
    const [existingGroupIds, existingLabelIds] = await Promise.all([
      this.getExistingGroupIds(orgId, groupIds, 3),
      this.getExistingLabelIds(orgId, labelIds, 3),
    ]);
    return { groupIds: existingGroupIds, labelIds: existingLabelIds };
  }

  private async getDbCustomerEntityIfNeeded(queryPO: DimensionQueryPO, orgId: number, companyId: string) {
    // 检查查询参数中是否存在按企业类型筛选的条件
    const isByEnterprise = queryPO.fieldVal.some((f) => f.type === CommonType.ByEnterprise);
    if (!isByEnterprise) {
      return [];
    }

    //查询对应排查公司是否是否是存量企业
    let dbCustomerEntity = await this.customerRepo
      .createQueryBuilder('customer')
      .leftJoin('customer.labels', 'label')
      .leftJoin('customer.departments', 'dep')
      .select('customer.groupId', 'groupId')
      .addSelect('label.labelId', 'labelId')
      .addSelect('dep.departmentId', 'departmentId')
      .where('customer.orgId = :orgId', { orgId })
      .andWhere('customer.status = :status', { status: BatchStatusEnums.Done })
      .andWhere('customer.companyId = :companyId', { companyId })
      .getRawMany();

    this.logger.info(`getDbCustomerEntityIfNeeded orgId:${orgId} companyId:${companyId} dbCustomerEntity:${JSON.stringify(dbCustomerEntity)}`);
    //未分组也视为获取第三方企业范围时的一种情况
    dbCustomerEntity = dbCustomerEntity
      ? dbCustomerEntity.reduce((c, item) => {
          const newItem = {
            groupId: item.groupId ?? -1,
            labelId: item.labelId ?? -1,
            departmentId: item.departmentId ?? -1,
          };
          c.push(newItem);
          return c;
        }, [])
      : [];
    return dbCustomerEntity;
  }

  private async processCommonIds(queryPO: DimensionQueryPO, key: DetailsParamEnums, dbCustomerEntity: any[]) {
    const item = queryPO?.fieldVal?.find((f) => f.key === key);
    if (!item) {
      return [];
    }

    const fieldMap = {
      [DetailsParamEnums.Groups]: 'groupId',
      [DetailsParamEnums.Labels]: 'labelId',
    };

    switch (item.type) {
      case CommonType.ByEnterprise:
        // 如果有数据库实体信息，提取指定字段的值并去重，否则认为没命中
        return dbCustomerEntity?.length ? uniq(dbCustomerEntity.map((c) => c[fieldMap[key]])) : [-2];
      case CommonType.All:
        return item.value;
      default:
        return item.value;
    }
  }

  private async processDepartmentIds(queryPO: DimensionQueryPO, dbCustomerEntity: any[], orgId: number, userId: number) {
    const department = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Departments);
    if (!department) {
      return [];
    }

    const getDepartmentIds = async (nameList?: string[]) => {
      const departments = await this.departmentRepo.find({
        where: {
          orgId,
          name: In(nameList),
        },
      });
      const departmentIds = departments.map((d) => d.departmentId);
      return departmentIds.length ? departmentIds : [-2];
    };

    switch (department.type) {
      case DepartmentType.ByEnterprise:
        // 如果有数据库实体信息，说明第三方企业有相同部门，否则认定未命中
        return dbCustomerEntity?.length ? uniq(dbCustomerEntity.map((c) => c['departmentId'])) : [-2];
      case DepartmentType.All:
        return department.nameList?.length > 0 ? await getDepartmentIds(department.nameList) : department.value;
      case DepartmentType.ByUser:
        const dep = await this.httpUtilsService.getRequest(this.configService.enterpriseServer.getUserDepartment, {
          orgId,
          userId,
        });
        if (!dep || !dep.name) return [-2];
        // 如果有部门说明从企业中心部门匹配到了三方企业，否则认定未命中
        return await getDepartmentIds([dep.name]);
      default:
        return department.value;
    }
  }

  /**
   * 获取真实存在的分组IDs
   * @param orgId 组织ID
   * @param groupIds 分组ID列表
   * @returns 存在的分组ID列表
   */
  private async getExistingGroupIds(orgId: number, groupIds: number[], groupType: number): Promise<number[]> {
    if (!groupIds?.length) {
      return [];
    }

    // 特殊ID -1（未分组）和 -2（未命中）需要特殊处理
    const specialIds = groupIds.filter((id) => id < 0);
    const normalIds = groupIds.filter((id) => id >= 0);

    if (normalIds.length === 0) {
      return specialIds;
    }

    // 先查询该组织下所有groupType为1的分组
    const allGroups = await this.groupRepo.find({
      where: {
        groupType,
        orgId,
      },
      select: ['groupId'],
    });

    // 获取所有有效的groupId
    const validGroupIds = allGroups.map((group) => group.groupId);

    // 取参数groupIds和实际存在的groupIds的交集
    const existingGroupIds = normalIds.filter((id) => validGroupIds.includes(id));

    // 合并特殊ID和交集结果
    return [...specialIds, ...existingGroupIds];
  }

  /**
   * 获取真实存在的标签IDs
   * @param orgId 组织ID
   * @param labelIds 标签ID列表
   * @returns 存在的标签ID列表
   */
  private async getExistingLabelIds(orgId: number, labelIds: number[], labelType: number): Promise<number[]> {
    if (!labelIds?.length) {
      return [];
    }

    // 特殊ID -1（无标签）和 -2（未命中）需要特殊处理
    const specialIds = labelIds.filter((id) => id < 0);
    const normalIds = labelIds.filter((id) => id >= 0);

    if (normalIds.length === 0) {
      return specialIds;
    }

    // 先查询该组织下所有labelType为1的标签
    const allLabels = await this.labelRepo.find({
      where: {
        orgId,
        labelType,
      },
      select: ['labelId'],
    });

    // 获取所有有效的labelId
    const validLabelIds = allLabels.map((label) => label.labelId);

    // 取参数labelIds和实际存在的labelIds的交集
    const existingLabelIds = normalIds.filter((id) => validLabelIds.includes(id));

    // 合并特殊ID和交集结果
    return [...specialIds, ...existingLabelIds];
  }

  /**
   * 获取真实存在的部门IDs
   * @param orgId 组织ID
   * @param departmentIds 部门ID列表
   * @returns 存在的部门ID列表
   */
  private async getExistingDepartmentIds(orgId: number, departmentIds: number[]): Promise<number[]> {
    if (!departmentIds?.length) {
      return [];
    }

    // 特殊ID -1（无部门）和 -2（未命中）需要特殊处理
    const specialIds = departmentIds.filter((id) => id < 0);
    const normalIds = departmentIds.filter((id) => id >= 0);

    if (normalIds.length === 0) {
      return specialIds;
    }

    // 先查询该组织下所有部门
    const allDepartments = await this.departmentRepo.find({
      where: {
        orgId,
      },
      select: ['departmentId'],
    });

    // 获取所有有效的departmentId
    const validDepartmentIds = allDepartments.map((dep) => dep.departmentId);

    // 取参数departmentIds和实际存在的departmentIds的交集
    const existingDepartmentIds = normalIds.filter((id) => validDepartmentIds.includes(id));

    // 合并特殊ID和交集结果
    return [...specialIds, ...existingDepartmentIds];
  }

  /**
   * 不支持穿透（查 graph 交叉重叠排查接口 ）
   * @param orgId
   * @param companyId
   * @param isValid
   * @param shareholdingRatioValue 持股比例
   * @private
   */
  @Cacheable({ ttlSeconds: 2 })
  public async scanPartner(orgId: number, companyId: string, isValid: string, shareholdingRatioValue?: number): Promise<PartnerConnectionDDResponse> {
    this.logger.info(`scanPartner orgId:${orgId} companyId:${companyId} begin`);
    const scope: string[] = [];
    if (isValid === '1') {
      scope.push('1');
    } else {
      scope.push('0', '1');
    }
    const reqData = {
      orgId,
      companyId,
      scope,
    };
    if (shareholdingRatioValue) {
      //设置持股比例
      reqData['percentage'] = shareholdingRatioValue;
    }
    try {
      const partnerResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.partner, reqData);
      this.logger.debug(`scanPartner orgId:${orgId} companyId:${companyId}, result:${JSON.stringify(partnerResponse)}`);
      if (partnerResponse) {
        Object.keys(partnerResponse).forEach((key) => {
          /**
           * partnerListSet key:关联黑名单企业名称+人员名称，value:列表项
           */
          const partnerListSet = {};
          if (partnerResponse[key]?.length) {
            switch (key) {
              case 'investConnections':
                break;
              case 'personConnections': {
                partnerResponse[key].map((e) => {
                  if (partnerListSet[e?.companyNameRelated + e?.personName]) {
                    partnerListSet[e.companyNameRelated + e.personName]['roleDDList'].push(e?.roleDD);
                    partnerListSet[e.companyNameRelated + e.personName]['roleRelatedList'].push(e?.roleRelated);
                  } else {
                    e['roleDDList'] = [e?.roleDD];
                    e['roleRelatedList'] = [e?.roleRelated];
                    partnerListSet[e.companyNameRelated + e.personName] = e;
                  }
                });
                const resList = [];
                Object.keys(partnerListSet).forEach((b) => {
                  const res = {
                    ...partnerListSet[b],
                    roleDD: uniq(partnerListSet[b].roleDDList)
                      .filter((s: string) => s?.trim())
                      .join(','),
                    roleRelated: uniq(partnerListSet[b].roleRelatedList)
                      .filter((s: string) => s?.trim())
                      .join(','),
                  };
                  delete res['roleDDList'];
                  delete res['roleRelatedList'];
                  resList.push(res);
                });
                partnerResponse[key] = resList;
                break;
              }
            }
          }
        });
      }
      return partnerResponse;
    } catch (e) {
      this.logger.error('get partner throw exception:' + e.message || e);
      return new PartnerConnectionDDResponse();
    }
  }

  @Cacheable({ ttlSeconds: 2 })
  public async scanCustomerFinalBenefit(orgId: number, companyId: string) {
    this.logger.info(`scanFinalBenefit orgId:${orgId} companyId:${companyId} begin`);
    try {
      const benefitResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.customerFinalBenefit, {
        orgId,
        companyId,
      });
      this.logger.debug(`scanFinalBenefit orgId:${orgId} companyId:${companyId}, result:${JSON.stringify(benefitResponse)}`);
      return benefitResponse;
    } catch (e) {
      this.logger.error('get customer final benefit throw exception:' + e.message || e);
      return null;
    }
  }

  /**
   * 查询所有，交集AND逻辑
   * @param orgId
   * @param groupIds
   * @param labelIds
   */
  async searchBlackListWithAllConditions(orgId: number, groupIds: number[], labelIds: number[]) {
    if (groupIds?.includes(-2) || labelIds?.includes(-2)) {
      return [];
    }
    const qb = this.innerBlacklistRepo
      .createQueryBuilder('blacklist')
      .select('blacklist.companyId')
      .leftJoinAndSelect('blacklist.labels', 'label')
      .where('blacklist.orgId = :orgId', { orgId })
      .andWhere('blacklist.status = :status', { status: BatchStatusEnums.Done })
      .andWhere(
        new Brackets((qb1) => {
          qb1.orWhere({ expiredDate: MoreThanOrEqual(moment().toDate()) });
          qb1.orWhere({ duration: DurationEnums.ForEver });
        }),
      );
    this.applyBlackListGroupIds(qb, groupIds);
    this.applyCommonLabelIds(qb, labelIds);
    const companyList = await qb.getMany();
    return companyList?.map((c) => c.companyId) || [];
  }

  private applyBlackListGroupIds(qb: SelectQueryBuilder<InnerBlacklistEntity>, groupIds: number[]) {
    if (groupIds?.length) {
      if (groupIds.includes(-1)) {
        if (groupIds.length === 1) {
          qb.andWhere('blacklist.groupId is null');
        } else {
          const filterGroupIds = groupIds.filter((r) => r >= 0);
          qb.andWhere(
            new Brackets((qb1) => {
              qb1.orWhere('blacklist.groupId is null');
              qb1.orWhere('blacklist.groupId in (:...filterGroupIds)', { filterGroupIds });
            }),
          );
        }
      } else {
        qb.andWhere('blacklist.groupId in (:...groupIds)', { groupIds });
      }
    }
  }

  async searchBlackListWithAnyCondition(orgId: number, groupIds: number[], labelIds: number[]): Promise<string[]> {
    const [groupCompanyIds, labelCompanyIds] = await Bluebird.all([this.searchByBlackListGroup(orgId, groupIds), this.searchByBlackListLabel(orgId, labelIds)]);
    return union(groupCompanyIds, labelCompanyIds);
  }

  private async searchByBlackListGroup(orgId: number, groupIds: number[]) {
    if (groupIds?.includes(-2)) {
      return [];
    }
    const qb = this.innerBlacklistRepo
      .createQueryBuilder('blacklist')
      .select('blacklist.companyId')
      .where('blacklist.orgId = :orgId', { orgId })
      .andWhere('blacklist.status = :status', { status: BatchStatusEnums.Done })
      .andWhere(
        new Brackets((qb1) => {
          qb1.orWhere({ expiredDate: MoreThanOrEqual(moment().toDate()) });
          qb1.orWhere({ duration: DurationEnums.ForEver });
        }),
      );

    this.applyBlackListGroupIds(qb, groupIds);
    const companyList = await qb.getMany();
    return companyList.map((c) => c.companyId);
  }

  private async searchByBlackListLabel(orgId: number, labelIds: number[]) {
    if (labelIds?.includes(-2)) {
      return [];
    }
    const qb = this.innerBlacklistRepo
      .createQueryBuilder('blacklist')
      .leftJoinAndSelect('blacklist.labels', 'label')
      .select('blacklist.companyId')
      .where('blacklist.orgId = :orgId', { orgId })
      .andWhere('blacklist.status = :status', { status: BatchStatusEnums.Done })
      .andWhere(
        new Brackets((qb1) => {
          qb1.orWhere({ expiredDate: MoreThanOrEqual(moment().toDate()) });
          qb1.orWhere({ duration: DurationEnums.ForEver });
        }),
      );

    this.applyCommonLabelIds(qb, labelIds);
    const companyList = await qb.getMany();
    return companyList.map((c) => c.companyId);
  }

  @Cacheable({ ttlSeconds: 2 })
  public async scanBlacklistFinalBenefit(orgId: number, companyId: string) {
    this.logger.info(`scan black FinalBenefit orgId:${orgId} companyId:${companyId} begin`);
    try {
      const benefitResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.blacklistFinalBenefit, {
        orgId,
        companyId,
      });
      this.logger.debug(`scan black FinalBenefit orgId:${orgId} companyId:${companyId}, result:${JSON.stringify(benefitResponse)}`);
      return benefitResponse;
    } catch (e) {
      this.logger.error('get blacklist final benefit throw exception:' + e.message || e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 2 })
  public async scanInnerBlacklist(
    orgId: number,
    companyId: string,
    isValid: string,
    types?: string[],
    shareholdingRatioValue?: number,
  ): Promise<BlacklistConnectionDDResponse> {
    this.logger.info(`scanInnerBlacklist orgId:${orgId} companyId:${companyId} begin`);
    const scope: string[] = [];
    if (isValid === '1') {
      scope.push('1');
    } else if (isValid === '0') {
      scope.push('0');
    } else {
      scope.push('0', '1');
    }
    const reqData = {
      orgId,
      companyId,
      scope,
      types,
    };
    if (shareholdingRatioValue) {
      reqData['percentage'] = shareholdingRatioValue;
    }
    try {
      const blacklistResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.blacklist, reqData);
      this.logger.debug(`scanInnerBlacklist orgId:${orgId} companyId:${companyId}, result:${JSON.stringify(blacklistResponse)}`);
      //合并blacklistHit，当关联人员和关联黑名单企业名称一致时，合并人员的任职
      await this.mergeBlacklistResponse(blacklistResponse);
      return blacklistResponse;
    } catch (e) {
      this.logger.error('get inner blacklist throw exception:' + e.message || e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 2 })
  public async scanInnerBlacklistV2(orgId: number, companyId: string, dimension: DimensionDefinitionPO): Promise<BlacklistConnectionDDResponse> {
    //直接查MySQL 表
    this.logger.info(`scanInnerBlacklistV2 in db orgId:${orgId} companyId:${companyId} begin`);

    const blacklists = await this.searchInnerBlacklistV2(orgId, [companyId], dimension);

    if (blacklists?.length) {
      // 如果存在，构建BlacklistConnectionDDResponse
      const response = new BlacklistConnectionDDResponse();
      // 取第一个黑名单，准入排查只排查一家公司
      const blacklist = blacklists[0];

      // 创建直接关联数据
      const directConnection = new BlacklistDirectConnectionPO();
      directConnection.blacklistId = blacklist.id;
      directConnection.companyKeynoDD = blacklist.companyId;
      directConnection.companyNameDD = blacklist.companyName;
      directConnection.companyKeynoRelated = blacklist.companyId;
      directConnection.companyNameRelated = blacklist.companyName;
      directConnection.duration = blacklist.duration;
      directConnection.joinDate = blacklist.joinDate;
      directConnection.reason = blacklist.reason || '';
      directConnection.expiredDate = blacklist.expiredDate;

      // 将直接关联数据添加到响应对象中
      response.directConnection = [directConnection];
      response.personConnections = [];
      response.shareholderConnections = [];
      response.actualControllerConnections = [];
      response.branchConnections = [];
      await this.mergeBlacklistResponse(response);
      return response;
    }
    return null;
  }

  /**
   * 查询内部黑名单-准入 带范围设置
   * @param orgId
   * @param companyIds
   * @param dimension
   * @returns
   */
  public async searchInnerBlacklistV2(orgId: number, companyIds: string[], dimension: DimensionDefinitionPO): Promise<InnerBlacklistEntity[]> {
    if (dimension.key !== DimensionLevel2Enums.HitInnerBlackList) {
      return [];
    }

    const queryPO = dimension?.strategyModel?.detailsParams?.find((d) => d.field === QueryParamsEnums.dataRange);
    const conditionOperator = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.conditionOperator)?.value;
    const groupIds0 = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Groups)?.value;
    const labelIds0 = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Labels)?.value;
    const { groupIds, labelIds } = await this.getBlacklistGroupLabelIds(orgId, groupIds0, labelIds0);

    // 使用QueryBuilder构建带有OR条件的查询
    const blacklist = await this.innerBlacklistRepo
      .createQueryBuilder('blacklist')
      .leftJoinAndSelect('blacklist.labels', 'labels')
      .where('blacklist.orgId = :orgId', { orgId })
      .andWhere('blacklist.companyId in (:...companyIds)', { companyIds })
      .andWhere('blacklist.status = :status', { status: BatchStatusEnums.Done })
      .andWhere(
        new Brackets((qb) => {
          qb.where('blacklist.expiredDate >= :currentDate', { currentDate: moment().toDate() }).orWhere('blacklist.duration = :foreverDuration', {
            foreverDuration: DurationEnums.ForEver,
          });
        }),
      )
      .orderBy('blacklist.id', 'DESC')
      .getMany();
    if (queryPO) {
      return blacklist.filter((blacklist) => {
        // 处理分组条件（支持-1无分组）
        //groupIds无值说明不限制范围，包含未分组时若blacklist为null逻辑相同，blacklist的分组包含在设置的分组，以上都视为命中
        const groupMatch = !groupIds?.length || (groupIds.includes(-1) && !blacklist.groupId) || groupIds.includes(blacklist.groupId);

        // 处理标签条件（支持-1无标签）
        const labelMatch =
          !labelIds?.length || (labelIds.includes(-1) && !blacklist.labels?.length) || blacklist.labels?.some((label) => labelIds.includes(label.labelId));

        // 根据匹配符返回结果
        return conditionOperator === ConditionOperatorEnums.And ? groupMatch && labelMatch : groupMatch || labelMatch;
      });
    }
    return blacklist;
  }

  /**
   * 查询内部黑名单
   * @param orgId
   * @param companyIds
   * @returns
   */
  public async searchInnerBlacklist(orgId: number, companyIds: string[], types: string[]): Promise<InnerBlacklistEntity[]> {
    if (!types.includes(DimensionLevel2Enums.HitInnerBlackList)) {
      return [];
    }
    // 使用QueryBuilder构建带有OR条件的查询
    const blacklist = await this.innerBlacklistRepo
      .createQueryBuilder('blacklist')
      .where('blacklist.orgId = :orgId', { orgId })
      .andWhere('blacklist.companyId in (:...companyIds)', { companyIds })
      .andWhere('blacklist.status = :status', { status: BatchStatusEnums.Done })
      .andWhere(
        new Brackets((qb) => {
          qb.where('blacklist.expiredDate >= :currentDate', { currentDate: moment().toDate() }).orWhere('blacklist.duration = :foreverDuration', {
            foreverDuration: DurationEnums.ForEver,
          });
        }),
      )
      .orderBy('blacklist.id', 'DESC')
      .getMany();
    return blacklist;
  }

  // 招标相关接口
  /**
   * 批量内部黑名单排查
   * @param orgId
   * @param companyIds
   * @param isValid
   * @private
   */
  @Cacheable({ ttlSeconds: 30 })
  public async scanInnerBlacklistBatch(orgId: number, companyIds: string[], isValid: string): Promise<BlacklistConnectionDDResponse> {
    this.logger.info(`scanInnerBlacklistBatch orgId:${orgId} companyIds:${companyIds} begin`);
    const scope: string[] = [];
    if (isValid === '1') {
      scope.push('1');
    } else if (isValid === '0') {
      scope.push('0');
    } else {
      scope.push('0', '1');
    }
    try {
      const blacklistResponse: BlacklistConnectionDDResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.blacklistBatch, {
        orgId,
        companyIds,
        scope,
      });
      this.logger.debug(`scanInnerBlacklistBatch orgId:${orgId} companyIds:${companyIds}, result:${JSON.stringify(blacklistResponse)}`);
      //合并blacklistHit，当关联人员和关联黑名单企业名称一致时，合并人员的任职
      if (blacklistResponse?.personConnections?.length) {
        await this.mergeBlacklistResponse(blacklistResponse);
        blacklistResponse.personConnections.sort((a, b) => b.joinDate.valueOf() - a.joinDate.valueOf());
      }
      return blacklistResponse;
    } catch (e) {
      this.logger.error('get inner blacklist batch throw exception:' + e.message || e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 30 })
  public async scanInvestBatch(orgId: number, companyIds: string[], isValid: string): Promise<BlacklistConnectionDDResponse> {
    this.logger.info(`scanInvestBatch orgId:${orgId} companyIds:${companyIds} isValid:${isValid} begin`);
    const scope: string[] = [];
    if (isValid === '1') {
      scope.push('1');
    } else if (isValid === '0') {
      scope.push('0');
    } else {
      scope.push('0', '1');
    }
    try {
      const blacklistResponse: BlacklistConnectionDDResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.invest, {
        orgId,
        companyIds,
        scope,
      });
      if (blacklistResponse) {
        blacklistResponse.shareholderConnections.sort((a, b) => b.joinDate.valueOf() - a.joinDate.valueOf());
        return blacklistResponse;
      }
    } catch (e) {
      this.logger.error('get invest batch throw exception:' + e.message || e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 30 })
  public async scanDirectBatch(orgId: number, companyIds: string[], isValid: string): Promise<BlacklistConnectionDDResponse> {
    this.logger.info(`scanDirectBatch orgId:${orgId} companyIds:${companyIds} begin`);
    const scope: string[] = [];
    if (isValid === '1') {
      scope.push('1');
    } else if (isValid === '0') {
      scope.push('0');
    } else {
      scope.push('0', '1');
    }
    try {
      const blacklistResponse: BlacklistConnectionDDResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.direct, {
        orgId,
        companyIds,
        scope,
      });
      if (blacklistResponse) {
        //对 joinDate 进行排序,joinDate越大越靠前
        blacklistResponse.directConnection.sort((a, b) => b.joinDate.valueOf() - a.joinDate.valueOf());
        return blacklistResponse;
      }
      return null;
    } catch (e) {
      this.logger.error('get direct batch throw exception:' + e.message || e);
      return null;
    }
  }

  /**
   * 内部黑名单分支机构排查（批量）
   * @param orgId
   * @param companyIds
   * @private
   */
  public async scanBlacklistBranchBatch(orgId: number, companyIds: string[]): Promise<BlacklistConnectionDDResponse> {
    this.logger.info(`scanBlacklistBranch orgId:${orgId} companyIds:${companyIds} begin`);
    const reqData = {
      orgId,
      companyIds,
    };
    try {
      const partnerResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.blacklistBranchBatch, reqData);
      this.logger.debug(`scanBlacklistBranch orgId:${orgId} companyIds:${companyIds}, result:${JSON.stringify(partnerResponse)}`);
      return partnerResponse;
    } catch (e) {
      this.logger.error('get blacklist branch batch throw exception:' + e.message || e);
      return new BlacklistConnectionDDResponse();
    }
  }

  private async mergeBlacklistResponse(blacklistResponse: any) {
    //合并blacklistHit，当关联人员和关联黑名单企业名称一致时，合并人员的任职
    if (blacklistResponse) {
      Object.keys(blacklistResponse).forEach((key) => {
        /**
         * blacklistSet key:关联黑名单企业名称+人员名称，value:列表项
         */
        const blacklistSet = {};
        if (blacklistResponse[key]?.length) {
          switch (key) {
            case 'directConnection':
              break;
            case 'personConnections': {
              blacklistResponse[key].map((e) => {
                const uniqKey = [e.personName, e.companyNameRelated, e.companyNameDD].join();
                if (blacklistSet[uniqKey]) {
                  blacklistSet[uniqKey]['roleDDList'].push(e?.roleDD);
                  blacklistSet[uniqKey]['roleRelatedList'].push(e?.roleRelated);
                } else {
                  e['roleDDList'] = [e?.roleDD];
                  e['roleRelatedList'] = [e?.roleRelated];
                  blacklistSet[uniqKey] = e;
                }
              });
              const resList = [];
              Object.keys(blacklistSet).forEach((b) => {
                const res = {
                  ...blacklistSet[b],
                  roleDD: uniq(blacklistSet[b].roleDDList)
                    .filter((s: string) => s?.trim())
                    .join(','),
                  roleRelated: uniq(blacklistSet[b].roleRelatedList)
                    .filter((s: string) => s?.trim())
                    .join(','),
                };
                delete res['roleDDList'];
                delete res['roleRelatedList'];
                resList.push(res);
              });
              blacklistResponse[key] = resList;
              break;
            }
            case 'shareholderConnections':
              break;
          }
        }
      });
    }
  }

  /**
   * parse partner response
   * @param response
   */
  public parseGraphPartnerResponse(response: PartnerConnectionDDResponse): RoverGraphParsedPO {
    const obj: RoverGraphParsedPO = new RoverGraphParsedPO();
    if (response.personConnections) {
      obj[DimensionLevel2Enums.ServeRelationship] = response.personConnections;
    }
    if (response.investConnections) {
      response.investConnections.forEach((p) => {
        if (p.direction > 0) {
          if (p.history) {
            p.role = `历史${p?.role || '股东'}`;
          }
          obj[DimensionLevel2Enums.InvestorsRelationship].push(p);
        } else {
          if (p.history) {
            p.role = `历史${p?.role || '股东'}`;
          }
          obj[DimensionLevel2Enums.ShareholdingRelationship].push(p);
        }
      });
    }
    return obj;
  }

  public parseGraphBlacklistResponse(response: BlacklistConnectionDDResponse): RoverGraphParsedPO {
    const obj: RoverGraphParsedPO = new RoverGraphParsedPO();
    if (response.directConnection) {
      obj[DimensionLevel2Enums.HitInnerBlackList] = response.directConnection;
    }
    if (response.personConnections) {
      obj[DimensionLevel2Enums.EmploymentRelationship] = response.personConnections;
    }
    if (response.shareholderConnections) {
      response.shareholderConnections.forEach((p) => {
        if (p.direction > 0) {
          if (p.history) {
            p.role = `历史${p?.role || '股东'}`;
          }
          obj[DimensionLevel2Enums.ForeignInvestment].push(p);
        } else {
          if (p.history) {
            p.role = `历史${p?.role || '股东'}`;
          }
          obj[DimensionLevel2Enums.Shareholder].push(p);
        }
      });
    }
    return obj;
  }

  async getCustomerSuspectedRelation(orgId: number, companyId: string, typeList: string[], excludedTypes: DimensionQueryPO) {
    this.logger.info(`getCustomerSuspectedRelation orgId:${orgId}, companyId:${companyId},typeList:${JSON.stringify(typeList)} begin`);
    const reqData = {
      orgId,
      companyId,
      types: typeList,
      excludedTypes,
    };
    try {
      const partnerResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.customerPotentiallyInvestigation, reqData);
      this.logger.debug(
        `getCustomerSuspectedRelation orgId:${orgId}, companyId:${companyId},typeList:${JSON.stringify(typeList)}, result:${JSON.stringify(partnerResponse)}`,
      );
      return this.removeEmptyNodePath(partnerResponse);
    } catch (e) {
      this.logger.error('getCustomerSuspectedRelation throw exception:' + e.message || e);
      return [];
    }
  }

  async getBlackListSuspectedRelation(orgId: number, companyId: string, typeList: any, excludedTypes: DimensionQueryPO) {
    this.logger.info(`getBlackListSuspectedRelation orgId:${orgId}, companyId:${companyId},typeList:${JSON.stringify(typeList)} begin`);
    const reqData = {
      orgId,
      companyId,
      types: typeList,
      excludedTypes,
    };
    try {
      const partnerResponse = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.blacklistPotentiallyInvestigation, reqData);
      this.logger.debug(
        `getBlackListSuspectedRelation orgId:${orgId}, companyId:${companyId},typeList:${JSON.stringify(typeList)}, result:${JSON.stringify(partnerResponse)}`,
      );
      return this.removeEmptyNodePath(partnerResponse);
    } catch (e) {
      this.logger.error('getBlackListSuspectedRelation throw exception:' + e.message || e);
      return [];
    }
  }

  private removeEmptyNodePath(records: any[]) {
    if (records?.length) {
      records.forEach((record) => {
        record.relationPaths = record.relationPaths.filter((path: any[]) => {
          if (!some(path, isEmpty)) {
            return path;
          }
        });
      });
    }
    return records;
  }

  /**
   *  获取招标内部黑名单关联关系
   * @param orgId
   * @param companyIds
   * @param types
   */
  async getTenderInnerBlacklist(orgId: number, companyIds: string[], types: string[], detailsParams?: DimensionQueryPO[]): Promise<TenderInnerBlackListResPo> {
    const reqData = {
      orgId,
      companyIds,
      types,
    };
    if (detailsParams) {
      const depth = detailsParams.find((x) => x.field == QueryParamsEnums.depth)?.fieldVal;
      if (depth) {
        reqData['depth'] = depth;
      }
      const excludedTypes = detailsParams.find((x) => x.field == QueryParamsEnums.excludedTypes)?.fieldVal;
      if (excludedTypes) {
        reqData['excludedTypes'] = excludedTypes;
      }
    }
    let response: TenderInnerBlackListResPo = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.tenderInnerBlacklist, reqData);
    const blacklists = await this.searchInnerBlacklist(orgId, companyIds, types);
    if (blacklists.length) {
      const directConnections = [];
      blacklists.forEach((blacklist) => {
        const directConnection = new DirectConnectionsPo();
        directConnection.blacklistId = blacklist.id;
        directConnection.companyKeynoDD = blacklist.companyId;
        directConnection.companyNameDD = blacklist.companyName;
        directConnection.duration = blacklist.duration;
        directConnection.joinDate = Math.floor(blacklist.joinDate.getTime() / 1000);
        directConnection.reason = blacklist.reason || '';
        directConnection.expiredDate = blacklist.expiredDate;
        directConnections.push(directConnection);
      });
      //如果 response 为 null
      if (!response || !response.directConnection?.length) {
        response = new TenderInnerBlackListResPo();
        response.directConnection = directConnections;
      } else {
        response.directConnection = directConnections;
      }
    }
    this.logger.debug(`getTenderInnerBlacklist orgId:${orgId}, companyId:${companyIds},typeList:${JSON.stringify(types)}, result:${JSON.stringify(response)}`);
    return response;
  }
}
