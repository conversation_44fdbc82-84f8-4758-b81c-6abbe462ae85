import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from 'log4js';
import { InnerBlacklistEntity } from '../../../libs/entities/InnerBlacklistEntity';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { BlacklistConnectionDDResponse } from '../../../libs/model/diligence/pojo/graph/BlacklistConnectionDDResponse';
import { BlacklistDirectConnectionPO } from '../../../libs/model/diligence/pojo/graph/BlacklistDirectConnectionPO';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { DurationEnums } from '../../../libs/enums/blacklist/DurationEnums';
import { generateUniqueTestIds } from '../../test_utils_module/test.user';
import { RoverGraphHelper } from './rover.graph.helper';

jest.setTimeout(300000);
describe('RoverGraphHelper scanInnerBlacklistV2 单元测试', () => {
  let roverGraphHelper: RoverGraphHelper;
  let mockSearchInnerBlacklistV2: jest.SpyInstance;
  let mockMergeBlacklistResponse: jest.SpyInstance;
  let mockLogger: jest.Mocked<Logger>;

  const [testOrgId] = generateUniqueTestIds('rover.graph.helper.unittest.spec.ts');

  beforeEach(async () => {
    // 创建mock对象
    mockLogger = {
      info: jest.fn(),
      debug: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: RoverGraphHelper,
          useValue: {
            logger: mockLogger,
            searchInnerBlacklistV2: jest.fn(),
            mergeBlacklistResponse: jest.fn(),
            scanInnerBlacklistV2: RoverGraphHelper.prototype.scanInnerBlacklistV2,
          },
        },
      ],
    }).compile();

    roverGraphHelper = module.get<RoverGraphHelper>(RoverGraphHelper);

    // 设置spy
    mockSearchInnerBlacklistV2 = jest.spyOn(roverGraphHelper, 'searchInnerBlacklistV2');
    mockMergeBlacklistResponse = jest.spyOn(roverGraphHelper as any, 'mergeBlacklistResponse');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('当搜索到黑名单数据时', () => {
    it('应该返回正确构建的BlacklistConnectionDDResponse对象', async () => {
      // Arrange
      const inputOrgId = testOrgId;
      const inputCompanyId = 'test-company-id-001';
      const inputDimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.HitInnerBlackList,
        name: '内部黑名单维度',
        source: undefined,
        strategyModel: undefined,
        status: 1,
        sort: 0,
        template: '',
        template2: '',
        type: 'keyItems',
      };

      const mockBlacklistEntity: InnerBlacklistEntity = {
        id: 123,
        orgId: inputOrgId,
        companyId: inputCompanyId,
        companyName: '测试公司',
        duration: DurationEnums.AYear,
        joinDate: new Date('2024-01-01'),
        reason: '测试原因',
        expiredDate: new Date('2025-01-01'),
      } as InnerBlacklistEntity;

      const expectedResponse = new BlacklistConnectionDDResponse();
      const expectedDirectConnection = new BlacklistDirectConnectionPO();
      expectedDirectConnection.blacklistId = mockBlacklistEntity.id;
      expectedDirectConnection.companyKeynoDD = mockBlacklistEntity.companyId;
      expectedDirectConnection.companyNameDD = mockBlacklistEntity.companyName;
      expectedDirectConnection.companyKeynoRelated = mockBlacklistEntity.companyId;
      expectedDirectConnection.companyNameRelated = mockBlacklistEntity.companyName;
      expectedDirectConnection.duration = mockBlacklistEntity.duration;
      expectedDirectConnection.joinDate = mockBlacklistEntity.joinDate;
      expectedDirectConnection.reason = mockBlacklistEntity.reason;
      expectedDirectConnection.expiredDate = mockBlacklistEntity.expiredDate;

      expectedResponse.directConnection = [expectedDirectConnection];
      expectedResponse.personConnections = [];
      expectedResponse.shareholderConnections = [];
      expectedResponse.actualControllerConnections = [];
      expectedResponse.branchConnections = [];

      mockSearchInnerBlacklistV2.mockResolvedValue([mockBlacklistEntity]);
      mockMergeBlacklistResponse.mockResolvedValue(undefined);

      // Act
      const actualResult = await roverGraphHelper.scanInnerBlacklistV2(inputOrgId, inputCompanyId, inputDimension);

      // Assert
      expect(mockSearchInnerBlacklistV2).toHaveBeenCalledWith(inputOrgId, [inputCompanyId], inputDimension);
      expect(mockMergeBlacklistResponse).toHaveBeenCalledWith(expectedResponse);
      expect(actualResult).toEqual(expectedResponse);
      expect(actualResult.directConnection).toHaveLength(1);
      expect(actualResult.directConnection[0].blacklistId).toBe(mockBlacklistEntity.id);
      expect(actualResult.directConnection[0].companyNameDD).toBe(mockBlacklistEntity.companyName);
      expect(actualResult.directConnection[0].reason).toBe(mockBlacklistEntity.reason);
    });

    it('应该正确处理reason为空的情况', async () => {
      // Arrange
      const inputOrgId = testOrgId;
      const inputCompanyId = 'test-company-id-002';
      const inputDimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.HitInnerBlackList,
        name: '内部黑名单维度',
        source: undefined,
        strategyModel: undefined,
        status: 1,
        sort: 0,
        template: '',
        template2: '',
        type: 'keyItems',
      };

      const mockBlacklistEntity: InnerBlacklistEntity = {
        id: 456,
        orgId: inputOrgId,
        companyId: inputCompanyId,
        companyName: '测试公司2',
        duration: DurationEnums.ForEver,
        joinDate: new Date('2024-02-01'),
        reason: null, // reason为null的情况
        expiredDate: new Date('2025-02-01'),
      } as InnerBlacklistEntity;

      mockSearchInnerBlacklistV2.mockResolvedValue([mockBlacklistEntity]);
      mockMergeBlacklistResponse.mockResolvedValue(undefined);

      // Act
      const actualResult = await roverGraphHelper.scanInnerBlacklistV2(inputOrgId, inputCompanyId, inputDimension);

      // Assert
      expect(actualResult).not.toBeNull();
      expect(actualResult.directConnection[0].reason).toBe('');
    });

    it('应该只取第一个黑名单数据当搜索到多个黑名单时', async () => {
      // Arrange
      const inputOrgId = testOrgId;
      const inputCompanyId = 'test-company-id-003';
      const inputDimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.HitInnerBlackList,
        name: '内部黑名单维度',
        source: undefined,
        strategyModel: undefined,
        status: 1,
        sort: 0,
        template: '',
        template2: '',
        type: 'keyItems',
      };

      const mockBlacklistEntity1: InnerBlacklistEntity = {
        id: 789,
        orgId: inputOrgId,
        companyId: inputCompanyId,
        companyName: '第一个黑名单公司',
        duration: DurationEnums.AYear,
        joinDate: new Date('2024-01-01'),
        reason: '第一个原因',
        expiredDate: new Date('2025-01-01'),
      } as InnerBlacklistEntity;

      const mockBlacklistEntity2: InnerBlacklistEntity = {
        id: 101112,
        orgId: inputOrgId,
        companyId: inputCompanyId,
        companyName: '第二个黑名单公司',
        duration: DurationEnums.TwoYears,
        joinDate: new Date('2024-02-01'),
        reason: '第二个原因',
        expiredDate: new Date('2026-02-01'),
      } as InnerBlacklistEntity;

      mockSearchInnerBlacklistV2.mockResolvedValue([mockBlacklistEntity1, mockBlacklistEntity2]);
      mockMergeBlacklistResponse.mockResolvedValue(undefined);

      // Act
      const actualResult = await roverGraphHelper.scanInnerBlacklistV2(inputOrgId, inputCompanyId, inputDimension);

      // Assert
      expect(actualResult).not.toBeNull();
      expect(actualResult.directConnection).toHaveLength(1);
      expect(actualResult.directConnection[0].blacklistId).toBe(mockBlacklistEntity1.id);
      expect(actualResult.directConnection[0].companyNameDD).toBe(mockBlacklistEntity1.companyName);
    });
  });

  describe('当未搜索到黑名单数据时', () => {
    it('应该返回null', async () => {
      // Arrange
      const inputOrgId = testOrgId;
      const inputCompanyId = 'test-company-id-004';
      const inputDimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.HitInnerBlackList,
        name: '内部黑名单维度',
        source: undefined,
        strategyModel: undefined,
        status: 1,
        sort: 0,
        template: '',
        template2: '',
        type: 'keyItems',
      };

      mockSearchInnerBlacklistV2.mockResolvedValue([]);

      // Act
      const actualResult = await roverGraphHelper.scanInnerBlacklistV2(inputOrgId, inputCompanyId, inputDimension);

      // Assert
      expect(mockSearchInnerBlacklistV2).toHaveBeenCalledWith(inputOrgId, [inputCompanyId], inputDimension);
      expect(mockMergeBlacklistResponse).not.toHaveBeenCalled();
      expect(actualResult).toBeNull();
    });

    it('应该返回null当searchInnerBlacklistV2返回null时', async () => {
      // Arrange
      const inputOrgId = testOrgId;
      const inputCompanyId = 'test-company-id-005';
      const inputDimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.HitInnerBlackList,
        name: '内部黑名单维度',
        source: undefined,
        strategyModel: undefined,
        status: 1,
        sort: 0,
        template: '',
        template2: '',
        type: 'keyItems',
      };

      mockSearchInnerBlacklistV2.mockResolvedValue(null);

      // Act
      const actualResult = await roverGraphHelper.scanInnerBlacklistV2(inputOrgId, inputCompanyId, inputDimension);

      // Assert
      expect(actualResult).toBeNull();
    });
  });

  describe('输入参数验证', () => {
    it('应该正确传递参数给searchInnerBlacklistV2方法', async () => {
      // Arrange
      const inputOrgId = testOrgId;
      const inputCompanyId = 'test-company-id-006';
      const inputDimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.HitInnerBlackList,
        name: '内部黑名单维度',
        source: undefined,
        strategyModel: undefined,
        status: 1,
        sort: 0,
        template: '',
        template2: '',
        type: 'keyItems',
      };

      mockSearchInnerBlacklistV2.mockResolvedValue([]);

      // Act
      await roverGraphHelper.scanInnerBlacklistV2(inputOrgId, inputCompanyId, inputDimension);

      // Assert
      expect(mockSearchInnerBlacklistV2).toHaveBeenCalledWith(inputOrgId, [inputCompanyId], inputDimension);
      expect(mockSearchInnerBlacklistV2).toHaveBeenCalledTimes(1);
    });
  });

  describe('日志记录验证', () => {
    it('应该记录正确的开始日志信息', async () => {
      // Arrange
      const inputOrgId = testOrgId;
      const inputCompanyId = 'test-company-id-007';
      const inputDimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.HitInnerBlackList,
        name: '内部黑名单维度',
        source: undefined,
        strategyModel: undefined,
        status: 1,
        sort: 0,
        template: '',
        template2: '',
        type: 'keyItems',
      };

      mockSearchInnerBlacklistV2.mockResolvedValue([]);

      // Act
      await roverGraphHelper.scanInnerBlacklistV2(inputOrgId, inputCompanyId, inputDimension);

      // Assert
      expect(mockLogger.info).toHaveBeenCalledWith(`scanInnerBlacklistV2 in db orgId:${inputOrgId} companyId:${inputCompanyId} begin`);
    });
  });
});
