import { Email, Tel } from '@kezhaozhao/company-search-api/src/company/model/response.model';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { GroupScene } from 'libs/constants/common';
import { BatchStatusEnums } from 'libs/enums/batch/BatchStatusEnums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { PersonData } from 'libs/model/data/source/PersonData';
import { DimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { RelatedPersonResponse } from 'libs/model/diligence/pojo/req&res/RelatedPersonResponse';
import { compact, concat, groupBy, isEqual, unionWith, uniq, uniqBy } from 'lodash';
import { Logger } from 'log4js';
import { Brackets, In, Repository } from 'typeorm';
import { PersonEntity } from '../../../libs/entities/PersonEntity';
import { GroupsEntity } from '../../../libs/entities/GroupsEntity';
import { PersonOrgCompanyEntity } from '../../../libs/entities/PersonOrgCompanyEntity';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { PersonAssociationEnums } from '../../../libs/enums/person/PersonAssociationEnums';
import { PersonStatusEnums } from '../../../libs/enums/person/PersonStatusEnums';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { ConditionValPO } from '../../../libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { CompanyDetailModel } from '../../../libs/model/person/CompanyDetailModel';
import { CompanyModel } from '../../../libs/model/person/CompanyModel';
import { CompanyResponse } from '../../../libs/model/person/CompanyResponse';
import { CompanyDetailService } from '../../company/company-detail.service';
import { EnterpriseLibService } from '../source/enterprise.lib.service';

/**
 * rover自身数据源接口
 */
@Injectable()
export class PersonHelper {
  private readonly logger: Logger = QccLogger.getLogger(PersonHelper.name);

  constructor(
    protected readonly entLibService: EnterpriseLibService,
    @InjectRepository(PersonEntity) private readonly personRepo: Repository<PersonEntity>,
    @InjectRepository(GroupsEntity) private readonly groupRepo: Repository<GroupsEntity>,
    @InjectRepository(PersonOrgCompanyEntity) private readonly personOrgCompanyRepo: Repository<PersonOrgCompanyEntity>,
    private readonly companyDetailService: CompanyDetailService,
  ) {}

  public async getDbPersons(orgId: number, dimensionKey: string, groupIds: number[], createDate?: Date): Promise<PersonEntity[]> {
    let groupScene = GroupScene.Normal;
    if (dimensionKey == DimensionLevel3Enums.PunishedEmployeesForeignInvestment || dimensionKey == DimensionLevel3Enums.PunishedEmployeesWorkingOutside) {
      // 曾被处罚的现任员工或前员工 对应场景
      groupScene = GroupScene.ZEISS;
    }
    const existGroupIds = await this.getDbPersonGroupIds(orgId, groupIds);
    return this.getPersonListByScene(orgId, groupScene, existGroupIds, createDate);
  }

  private createPersonQb(orgId: number) {
    return this.personRepo
      .createQueryBuilder('person')
      .leftJoinAndSelect('person.group', 'group')
      .select(['person', 'group.name'])
      .where('person.orgId = :orgId', { orgId })
      .andWhere('person.status = :status', { status: BatchStatusEnums.Done })
      .andWhere('person.active = 1');
  }

  /**
   * 获取指定场景分组人员列表
   * @param orgId
   * @param groupScene
   * @param groupIds
   * @param createDate
   */
  private getPersonListByScene(orgId: number, groupScene: GroupScene, groupIds?: number[], createDate?: Date) {
    const qb = this.createPersonQb(orgId);
    if (groupScene == GroupScene.Normal) {
      qb.andWhere('(group.scene < 1 OR person.groupId = -1)');
    } else {
      qb.andWhere('group.scene = :groupScene', { groupScene });
    }
    if (groupIds?.length) {
      qb.andWhere('person.groupId in (:...groupIds)', { groupIds });
      // if (groupIds?.includes(-1) && groupIds?.length === 1) {
      //   qb.andWhere('person.groupId is null');
      // }
      // if (!groupIds?.includes(-1)) {
      //   qb.andWhere('person.groupId in (:...groupIds)', { groupIds });
      // }
      // if (groupIds?.includes(-1) && groupIds?.length > 1) {
      //   qb.andWhere(
      //     new Brackets((qb1) => {
      //       qb1.orWhere('person.groupId is null');
      //       qb1.orWhere('person.groupId in (:...groupIds)', { groupIds });
      //     }),
      //   );
      // }
    }
    if (createDate) {
      qb.andWhere('person.createDate < :createDate', { createDate });
    }
    return qb.getMany();
  }

  /**
   * 获取指定场景分组人员列表
   * @param orgId
   * @param phoneList
   * @param emailList
   * @param groupIds
   * @param createDate
   */
  public async getPersonListByPhones(orgId: number, phoneList: string[], emailList: string[], groupIds?: number[], createDate?: Date) {
    const qb = this.createPersonQb(orgId);
    const existGroupIds = await this.getDbPersonGroupIds(orgId, groupIds);
    if (existGroupIds?.length) {
      qb.andWhere('person.groupId in (:...groupIds)', { groupIds: existGroupIds });

      // //需要考虑 groupIds 中包含 -1 的情况
      // if (existGroupIds?.includes(-1) && existGroupIds?.length === 1) {
      //   qb.andWhere('person.groupId is null');
      // }
      // if (!existGroupIds?.includes(-1)) {
      //   qb.andWhere('person.groupId in (:...groupIds)', { groupIds: existGroupIds });
      // }
      // if (existGroupIds?.includes(-1) && existGroupIds?.length > 1) {
      //   const filterGroupIds = existGroupIds.filter((r) => r >= 0);
      //   qb.andWhere(
      //     new Brackets((qb1) => {
      //       qb1.orWhere('person.groupId is null');
      //       qb1.orWhere('person.groupId in (:...filterGroupIds)', { filterGroupIds });
      //     }),
      //   );
      // }
    }
    if (phoneList?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          phoneList.forEach((phone) => {
            if (phone) {
              qb1.orWhere(`find_in_set('${phone}', person.phone)`);
            }
          });
          if (emailList?.length) {
            emailList.forEach((email) => {
              if (email) {
                qb1.orWhere(`find_in_set('${email}', person.email)`);
              }
            });
          }
        }),
      );
    } else if (emailList?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          emailList.forEach((email) => {
            if (email) {
              qb1.orWhere(`find_in_set('${email}', person.email)`);
            }
          });
        }),
      );
    }

    if (createDate) {
      qb.andWhere('person.createDate < :createDate', { createDate });
    }
    return await qb.getMany();
  }

  /**
   * 获取最终实际控制人
   * 先从ActualControl取，ActualControl没值再从 FinalActualControl 里取，再没值从Names里取
   * @param keyNo
   */
  public async getFinalActualController(keyNo: string): Promise<PersonData[]> {
    const actualControllerResponse = await this.entLibService.getActualController(keyNo);
    const sourceCompanyId = actualControllerResponse?.KeyNo;
    const sourceCompanyName = actualControllerResponse?.CompanyName;
    let finalActPersonList: PersonData[] = [];
    if (!actualControllerResponse) {
      return finalActPersonList;
    }
    const getFilteredPersonList = (personList: any[]) => {
      if (!personList.length) {
        return finalActPersonList;
      }
      const filteredPersonList = personList
        .filter((p) => p?.KeyNo?.length > 0)
        .map((e) => ({
          keyNo: e.KeyNo,
          name: e.Name,
          sourceCompanyId,
          sourceCompanyName,
          stockPercent: e.PercentTotal,
        }));
      return uniqBy(filteredPersonList, (x) => x.keyNo).filter((p) => p.keyNo.startsWith('p')) || [];
    };
    finalActPersonList = getFilteredPersonList(actualControllerResponse?.ActualControl?.PersonList || []);
    // 如果没有取到实际控制人或者 FinalActualControl有值，再从 FinalActualControl 里取
    if (actualControllerResponse.FinalActualControl) {
      finalActPersonList = getFilteredPersonList(actualControllerResponse.FinalActualControl?.PersonList || []);
    }
    if (actualControllerResponse?.Names?.length) {
      actualControllerResponse.Names.forEach((nameGroup) => {
        nameGroup.Names.PersonList?.forEach((e) => {
          if (finalActPersonList?.length) {
            finalActPersonList.forEach((f) => {
              // if (f.keyNo === e.KeyNo) {
              //   f.tags = e?.Tags?.length ? e.Tags : ['实际控制人'];
              //   f.job = e.Job ? [...e.Job.split(','), '实际控制人'].join(',') : '实际控制人';
              // }
              f.tags = e?.Tags?.length ? e.Tags : ['实际控制人'];
              f.job = e.Job ? [...e.Job.split(','), '实际控制人'].join(',') : '实际控制人';
            });
          } else {
            finalActPersonList.push(
              Object.assign(new PersonData(), {
                name: e.Name,
                keyNo: e.KeyNo,
                tags: e?.Tags?.length ? e.Tags : ['实际控制人'],
                job: e.Job ? [...e.Job.split(','), '实际控制人'].join(',') : '实际控制人',
                stockPercent: e.PercentTotal,
                sourceCompanyId,
                sourceCompanyName,
              }),
            );
          }
        });
      });
    }
    return finalActPersonList;
  }

  public async sameConcatPersons(
    orgId: number,
    keyNo: string,
    dbPersonList: PersonEntity[],
    tellist: Tel[],
    emaillist: Email[],
    tenderResponse: any,
  ): Promise<RelatedPersonResponse[]> {
    // 给将命中的人员信息匹配到对应的公司联系人
    const personList2 = dbPersonList?.map((dbP) => {
      const phoneList = tellist.filter((t) => dbP?.phone?.includes(t.t));
      //const phones = compact(phoneList.map((t) => t.t));
      const tenderPhoneList = tenderResponse
        ?.filter((t) => dbP?.phone?.includes(t?.TelNo))
        .map((e) => {
          return { n: e?.Contact, t: e?.TelNo?.split('、').join(',') };
        });
      //const tenderPhones = compact(tenderPhoneList?.map((t) => t.t));
      const emailList = emaillist.filter((e) => dbP?.email?.includes(e.e));
      //const emails = uniq(emailList.map((e) => e.e)).join(',');
      const contacts = compact(
        uniq(
          concat(
            tenderPhoneList?.map((e) => e?.n),
            phoneList?.map((t) => t?.n),
            emailList?.map((e) => e?.n),
          ),
        ),
      ).join(',');
      // TODO: 通过公司id和联系人姓名 获取人员的keyno
      const keyNo = '';
      return Object.assign(new RelatedPersonResponse(), {
        phones: uniqBy(compact(concat(phoneList, tenderPhoneList)), (x) => x?.n && x?.t),
        emails: emailList,
        contacts,
        keyNo,
        name: dbP?.name,
        group: dbP?.group?.name,
        personNo: dbP?.personNo,
        personId: dbP?.id,
        relationship: dbP?.relationship,
        relationPersonId: dbP?.relationPersonId,
        status: dbP?.keyNo?.length ? 1 : 0,
      });
    });

    // 再次筛选personList,过滤personList中的状态为和公司存在关联状态的人
    return await this.filterInCompanyPerson(personList2, orgId);
  }

  /**
   * 筛选标记在peron_org_company中status = 1（确认相关的人）
   * @param personList
   * @param orgId
   * @param companyId
   */
  public async filterInCompanyPerson(personList: RelatedPersonResponse[], orgId: number): Promise<RelatedPersonResponse[]> {
    if (personList?.length === 0) {
      return [];
    }
    // 查询标记为不相关的人员
    const exceptPerson = await this.personOrgCompanyRepo.find({
      orgId,
      personId: In(personList.map((p) => p.personId)),
      status: PersonStatusEnums.NOT_IN_COMPANY,
      association: PersonAssociationEnums.UN_ASSOCIATION,
    });
    const inCompanyPerson = await this.personOrgCompanyRepo.find({
      orgId,
      personId: In(personList.map((p) => p.personId)),
      status: PersonStatusEnums.IN_COMPANY,
      association: PersonAssociationEnums.UN_ASSOCIATION,
    });
    // 排除不相干的人员
    const exceptPersonMap = groupBy(exceptPerson, (e) => e.personId);
    const exceptPersonKeyNoMap = groupBy(exceptPerson, (e) => e.keyNo);
    const relatedPersonList: RelatedPersonResponse[] = personList
      ?.filter((e) => !(exceptPersonMap[e.personId] && exceptPersonKeyNoMap[e.keyNo]))
      .map((p) => {
        const samePersons = inCompanyPerson.filter((i) => i.personId === p.personId);
        if (samePersons?.length) {
          if (samePersons.map((s) => s.keyNo).includes(p.keyNo)) {
            p.status = 1;
          }
        }
        return p;
      });
    //排除已确认过，但是依然存在同名的人
    //1.如果所有人都没被确认过
    if (!compact(relatedPersonList?.filter((n) => n?.status > 0)).length) {
      return relatedPersonList;
    }
    //2.有人被确认过
    const nameSet = new Set(
      relatedPersonList?.map((r) => {
        if (r.status && r.status > 0) {
          return r.name;
        }
      }),
    );
    return relatedPersonList.filter((r) => (nameSet.has(r.name) && r.status === 1) || !nameSet.has(r.name));
  }

  /**
   * 获取控制企业
   * @param personKeyNo
   * @param pageSize
   * @param pageIndex
   * @private
   */
  public async getHoldingCompany(personKeyNo: string, pageSize: number, pageIndex: number): Promise<CompanyResponse> {
    const companyResponse = new CompanyResponse();
    if (!personKeyNo) {
      return companyResponse;
    }
    try {
      const params = {
        keyNo: personKeyNo,
        pageSize,
        pageIndex,
      };
      const res = await this.companyDetailService.getHoldingCompany(params);
      const companyModels: CompanyModel[] = [];
      res?.Result?.Names?.forEach((e) => {
        const company = Object.assign(new CompanyModel(), {
          companyId: e.KeyNo,
          companyName: e.Name,
          typeDesc: '相同实际控制人',
          details: Object.assign(new CompanyDetailModel(), {
            path: e?.Paths,
            percentTotal: e?.PercentTotal,
          }),
        });
        companyModels.push(company);
      });
      return Object.assign(companyResponse, {
        pageSize: res?.Paging?.PageSize,
        pageIndex: res?.Paging?.PageIndex,
        total: res?.Paging?.TotalRecords,
        data: companyModels,
      });
    } catch (e) {
      this.logger.error(`personKeyNo:${personKeyNo} 查高管关联公司error:${JSON.stringify(e)}`);
      return companyResponse;
    }
  }

  public async analyzePerson(dimension: DimensionDefinitionPO, orgId: number, keyNo: string, createDate?: Date): Promise<RelatedPersonResponse[]> {
    const validDetailParamPO = dimension.strategyModel.detailsParams?.find((v) => v.field === QueryParamsEnums.isValid);
    const dimensionTypes: ConditionValPO[] = dimension.strategyModel?.detailsParams?.find((e) => e.field === QueryParamsEnums.types)?.fieldVal;
    const queryPO = dimension.strategyModel.detailsParams?.find((d) => d.field === QueryParamsEnums.dataRange);
    const groupIds = await this.getDbPersonGroupIds(orgId, queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Groups)?.value || []);
    let personData: PersonData[] = [];
    let personData2: PersonData[] = [];
    switch (dimension.key) {
      case DimensionLevel3Enums.StaffWorkingOutside:
      case DimensionLevel3Enums.PunishedEmployeesWorkingOutside: {
        if (validDetailParamPO?.fieldVal === '1') {
          //当前有效
          // 在外任职  获取排查公司董监高
          personData = await this.entLibService.getEmployeeList(keyNo);
          // 在外任职  获取排查公司的法人代表
          personData2 = await this.entLibService.getLegalPerson(keyNo);
        } else {
          //不限
          personData = await this.entLibService.getAllEmployeeList(keyNo);
          // 在外任职  获取排查公司的法人代表
          personData2 = await this.entLibService.getAllLegalPerson(keyNo);
        }
        break;
      }
      case DimensionLevel3Enums.StaffForeignInvestment:
      case DimensionLevel3Enums.PunishedEmployeesForeignInvestment: {
        //曾被处罚的现任员工或前员工-对外投资,曾被处罚的现任员工或前员工-在外任职
        if (validDetailParamPO?.fieldVal === '1') {
          // 对外投资  获取排查公司相关自然人股东（当前）
          personData = await this.entLibService.getPartnerList(keyNo, 'person');
        } else {
          // 对外投资  获取排查公司相关自然人股东（不限）
          personData = await this.entLibService.getAllPartnerList(keyNo);
        }
        // 对外投资  获取排查公司的实际控制人
        personData2 = await this.getFinalActualController(keyNo);
        break;
      }
      case DimensionLevel3Enums.StaffWorkingOutsideForeignInvestment: {
        //潜在利益冲突
        const usefulDimensionTypes = dimensionTypes?.filter((d) => d?.status > 0);
        personData = await this.getConditionTypesPerson(usefulDimensionTypes, keyNo);
        break;
      }
    }
    personData = personData.concat(personData2);
    const dbPersons = await this.getDbPersons(orgId, dimension.key, groupIds, createDate);
    //处理 personData 中重复出现的数据， 合并 job和tag
    personData = await this.mergeDuplicateBy(personData);
    //dbPersons = dbPersons.filter((d) => personData.map((p) => p.name).includes(d.name));
    // 根据配置项查询出的人员信息和人员配置取交集，返回结果列表
    let personList: RelatedPersonResponse[] = await this.mergeInterestPersonInDb(dbPersons, personData);
    //再次筛选personList,过滤personList中的状态为和公司存在关联状态的人
    personList = await this.filterInCompanyPerson(personList, orgId);
    return personList;
  }

  /**
   * 获取当前组织人员分组 id 和 设置的人员分组 id 的交集
   * @param orgId
   * @param groupIds
   * @returns
   */
  public async getDbPersonGroupIds(orgId: number, groupIds: number[]): Promise<number[]> {
    // 当 groupIds 为 undefined 或 null 时直接返回空数组
    if (!groupIds || !Array.isArray(groupIds)) {
      return [];
    }

    const groups = await this.groupRepo.find({ where: { orgId, groupType: 2 } });
    const validGroupIds = [-1, ...groups.map((g) => g.groupId)];

    // 过滤有效的 groupIds，确保安全访问
    return groupIds.filter((groupId) => validGroupIds.includes(groupId)) || [];
  }

  /**
   * 根据配置项查询出的人员信息和人员配置取交集，返回结果列表
   * @param dbPersons
   * @param personData
   * @private
   */
  public async mergeInterestPersonInDb(dbPersons: PersonEntity[], personData: PersonData[]): Promise<RelatedPersonResponse[]> {
    if (personData?.length === 0) {
      return [];
    }
    const dbPersons2 = dbPersons.filter((d) => personData.map((p) => p.name).includes(d.name));
    // 根据配置项查询出的人员信息和人员配置取交集，返回结果列表
    const personList: RelatedPersonResponse[] = [];
    if (personData?.length > 0 && dbPersons2?.length > 0) {
      const personDataMap = groupBy(personData, (e) => e.keyNo);
      dbPersons2.forEach((dbP) => {
        if (!dbP.keyNo?.length) {
          //当人员并未核实过，判断名字是否相同
          const sameNamePersons = personData.filter((e) => e.name === dbP.name);
          if (sameNamePersons.length) {
            sameNamePersons.forEach((tp) => {
              personList.push(
                Object.assign(new RelatedPersonResponse(), {
                  ...tp,
                  group: dbP?.group?.name,
                  personNo: dbP?.personNo,
                  personId: dbP?.id,
                  relationship: dbP?.relationship,
                  relationPersonId: dbP?.relationPersonId,
                }),
              );
            });
          }
        } else if (personDataMap[dbP.keyNo]?.length > 0) {
          const p = personDataMap[dbP.keyNo][0];
          personList.push({
            ...p,
            group: dbP?.group?.name,
            personNo: dbP?.personNo,
            personId: dbP?.id,
            relationship: dbP?.relationship,
            relationPersonId: dbP?.relationPersonId,
          });
        }
      });
    }
    return this.generatorPersonRelations(personList);
  }

  /**
   * 为亲属信息添加父级人员信息
   * @param personList
   * @private
   */
  public async generatorPersonRelations(personList: RelatedPersonResponse[]) {
    const relationPersonIds = personList?.map((p) => {
      if (p?.relationPersonId > -1) {
        return p?.relationPersonId;
      }
    });
    if (personList.length === 0 || relationPersonIds?.length === 0) {
      return personList;
    }
    const relationPersons = await this.getPersonListByIds(compact(relationPersonIds));
    const personMap = groupBy(relationPersons, (p) => p.id);
    return personList.map((p) => {
      if (p.relationPersonId > -1) {
        p.relationPersonName = personMap[p.relationPersonId][0].name;
        p.relationPersonKeyNo = personMap[p.relationPersonId][0].keyNo;
      }
      return p;
    });
  }

  public async getPersonListByIds(ids: number[]) {
    return this.personRepo.findByIds(ids);
  }

  /**
   * 潜在利益冲突 条件筛选排查对象
   * @param usefulDimensionTypes
   * @param keyNo
   * @private
   */
  public async getConditionTypesPerson(usefulDimensionTypes: ConditionValPO[], keyNo: string): Promise<PersonData[]> {
    const personData: PersonData[] = [];
    if (usefulDimensionTypes?.length === 0) {
      return personData;
    }
    await Bluebird.map(usefulDimensionTypes, async (dimensionType) => {
      switch (dimensionType.key) {
        case DetailsParamEnums.Invest: {
          const personDataTemp = await this.entLibService.getPartnerList(keyNo, 'person');
          Array.prototype.push.apply(personData, personDataTemp);
          break;
        }
        case DetailsParamEnums.LegalAndEmploy: {
          // 在外任职  获取排查公司董监高
          const personDataTemp = await this.entLibService.getEmployeeList(keyNo);
          Array.prototype.push.apply(personData, personDataTemp);
          // 在外任职  获取排查公司的法人代表
          const personDataTemp2 = await this.entLibService.getLegalPerson(keyNo);
          Array.prototype.push.apply(personData, personDataTemp2);
          break;
        }
        case DetailsParamEnums.ActualController: {
          // 对外投资  获取排查公司的实际控制人
          const personDataTemp = await this.getFinalActualController(keyNo);
          Array.prototype.push.apply(personData, personDataTemp);
          break;
        }
        case DetailsParamEnums.HisInvest: {
          const personDataTemp = await this.entLibService.getHistoryPartnerList(keyNo);
          Array.prototype.push.apply(personData, personDataTemp);
          break;
        }
        case DetailsParamEnums.HisLegalAndEmploy: {
          // 在外任职  获取历史排查公司董监高
          const personDataTemp = await this.entLibService.getHisEmployeeList(keyNo);
          Array.prototype.push.apply(personData, personDataTemp);
          // 在外任职  获取历史排查公司的法人代表
          const personDataTemp2 = await this.entLibService.getHisLegalPerson(keyNo);
          Array.prototype.push.apply(personData, personDataTemp2);
          break;
        }
        case DetailsParamEnums.MainInfoUpdateBeneficiary: {
          // 受益所有人
          const personDataTemp = await this.entLibService.getBenefitList(keyNo);
          if (personDataTemp?.length) {
            personDataTemp.forEach((pp) => (pp.job = '受益所有人'));
          }
          Array.prototype.push.apply(personData, personDataTemp);
          break;
        }
      }
    });
    return personData;
  }

  /**
   * 获取所有企业
   * @param personId
   * @param personName
   * @param pageSize
   * @param pageIndex
   * @private
   */
  public async getBossDJGData(personId: string, personName: string, pageSize: number, pageIndex: number): Promise<CompanyResponse> {
    const companyResponse = new CompanyResponse();
    if (!personId) {
      return companyResponse;
    }
    try {
      const params = {
        personName,
        personId,
        pageSize,
        pageIndex,
      };
      const res = await this.companyDetailService.getBossDJGData(params);
      //关联企业数
      const companyModels: CompanyModel[] = [];
      res?.Result?.Relative?.Result?.forEach((e) => {
        const companyModel = Object.assign(new CompanyModel(), {
          companyId: e.KeyNo,
          companyName: e.Name,
          typeDesc: uniq(e.Relation.map((e) => e.TypeDesc)).join(','),
          details: Object.assign(new CompanyDetailModel(), {
            detailDesc: uniq(
              e.Relation.map((e) => {
                if (e.Type === '0') {
                  return '法定代表人';
                } else if (e.Type === '1') {
                  //对外投资
                  return `持股${e.Value}`;
                } else {
                  return e.Value;
                }
              }),
            ).join(','),
          }),
        });
        companyModels.push(companyModel);
      });
      return Object.assign(companyResponse, {
        pageSize: res?.Result?.Relative?.Paging?.PageSize,
        pageIndex: res?.Result?.Relative?.Paging?.PageIndex,
        total: res?.Result?.Relative?.Paging.TotalRecords,
        data: companyModels,
      });
    } catch (e) {
      this.logger.error(`personId:${personId} 查高管关联公司error:${JSON.stringify(e)}`);
      return companyResponse;
    }
  }

  public async mergeDuplicateBy(personDataList: PersonData[], keyName = 'keyNo'): Promise<PersonData[]> {
    if (personDataList?.length === 0) {
      return [];
    }
    const keyMap: Record<string, PersonData> = {};
    personDataList.forEach((p) => {
      const uniqKey = p[keyName];
      if (Object.keys(keyMap).includes(uniqKey)) {
        const currentPerson = keyMap[uniqKey];
        Object.assign(currentPerson, {
          job: unionWith(compact(currentPerson?.job?.split(',')), compact(p?.job?.split(',')), isEqual).join(','),
          tags: unionWith(currentPerson.tags, p.tags, isEqual),
          keyNo: currentPerson.keyNo || p.keyNo,
          contacts: currentPerson['contacts'] || p['contacts'],
          emails: currentPerson['emails'] || p['emails'],
          phones: currentPerson['phones'] || p['phones'],
          dimension: unionWith(compact(currentPerson?.dimension?.split('、')), compact(p?.dimension?.split('、')), isEqual).join('、'),
          isSameName: currentPerson?.isSameName || p.isSameName,
          isSameContact: currentPerson?.isSameContact || p.isSameContact,
        });
        keyMap[uniqKey] = currentPerson;
      } else {
        keyMap[uniqKey] = p;
      }
    });
    return Object.keys(keyMap).map((k) => {
      return keyMap[k];
    });
  }
}
