import { Test } from '@nestjs/testing';
import { <PERSON>tityManager, getManager } from 'typeorm';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { CompanyInvestigationPo } from '../../../libs/model/company/CompanyInvestigationPo';
import { DimensionQueryPO } from '../../../libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { AppTestModule } from '../../app/app.test.module';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { DataModule } from '../data.module';
import { EnterpriseLibService } from '../source/enterprise.lib.service';
import { processPaths } from '../utils/path.util';
import { RoverGraphHelper } from './rover.graph.helper';
import { CommonType } from 'libs/model/diligence/pojo/graph/CustomerRelationRangeEnum';
import { RoverGraphService } from '../source/rover.graph.service';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { SubDimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { BlacklistInvestigationsPo, DirectConnectionsPo, TenderInnerBlackListResPo } from '../../../libs/model/bidding/model/TenderInnerBlackListResPo';
import { BiddingDimensionHitsDetails } from '../../../libs/model/bidding/DiligenceBiddingResponse';

// 模拟 getTransV2Detail 函数
jest.mock('../../batch/message.handler/export/model/path-node/data-node-utils', () => ({
  getTransV2Detail: jest.fn().mockReturnValue({}),
  parseRelationsData: jest.fn().mockReturnValue([{ relations: [] }]),
}));

jest.setTimeout(300000);
describe('test rover graph helper', () => {
  let roverGraphHelper: RoverGraphHelper;
  let roverGraphService: RoverGraphService;
  let entLibService: EnterpriseLibService;
  let entityManager: EntityManager;
  const [testOrgId, testUserId] = generateUniqueTestIds('rover.graph.helper.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    roverGraphHelper = module.get<RoverGraphHelper>(RoverGraphHelper);
    roverGraphService = module.get<RoverGraphService>(RoverGraphService);
    entLibService = module.get<EnterpriseLibService>(EnterpriseLibService);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('getCustomerInvestigation', async () => {
    const requestParam = Object.assign(new CompanyInvestigationPo(), {
      companyId: '9cce0780ab7644008b73bc2120479d31',
      depth: 3,
      orgId: 1001652,
      percentage: 0.01,
      types: [
        DetailsParamEnums.ShareholdingRelationship,
        DetailsParamEnums.InvestorsRelationship,
        DetailsParamEnums.EmploymentRelationship,
        DetailsParamEnums.HisShareholdingRelationship,
        DetailsParamEnums.HisInvestorsRelationship,
        DetailsParamEnums.HisLegalAndEmploy,
        DetailsParamEnums.ActualController,
        DetailsParamEnums.MainInfoUpdateBeneficiary,
        DetailsParamEnums.Branch,
      ],
      rangeCompanyIds: ['a7b792c0e1505f7665d3feb55829d705'],
    });
    const investigation = await roverGraphHelper.getCustomerInvestigation(requestParam);
    if (investigation) {
      investigation.forEach((item) => {
        const relationPaths = processPaths(item.relations);
        item.relationPaths = relationPaths;
      });
      expect(investigation?.length).toBeGreaterThanOrEqual(1);
    }
  });

  it.skip('test processPaths', async () => {
    const paths = [
      [
        {
          entityType: 'company',
          id: '9cce0780ab7644008b73bc2120479d31',
          label: '小米科技有限责任公司',
          name: '小米科技有限责任公司',
          type: 'node',
        },
        {
          create_time: 1728237352,
          direction: 'right',
          endid: 'a7b792c0e1505f7665d3feb55829d705',
          roleType: 'Branch',
          roles: ['分支机构'],
          startid: '9cce0780ab7644008b73bc2120479d31',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: 'a7b792c0e1505f7665d3feb55829d705',
          label: '小米科技有限责任公司济南分公司',
          name: '小米科技有限责任公司济南分公司',
          type: 'node',
        },
      ],
      [
        {
          entityType: 'company',
          id: '9cce0780ab7644008b73bc2120479d31',
          label: '小米科技有限责任公司',
          name: '小米科技有限责任公司',
          type: 'node',
        },
        {
          create_time: 1728237420,
          direction: 'right',
          endid: '48ef5404ebd06907c0d01b3fddd8959e',
          groups: [
            {
              create_time: 1728237420,
              direction: 'right',
              endid: '48ef5404ebd06907c0d01b3fddd8959e',
              percent: 80.1,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'right',
              endid: '48ef5404ebd06907c0d01b3fddd8959e',
              percent: 80.1,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'right',
              endid: '48ef5404ebd06907c0d01b3fddd8959e',
              percent: 80.1,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
          ],
          percent: 80.1,
          roleType: 'Hold',
          roles: ['控制关系'],
          startid: '9cce0780ab7644008b73bc2120479d31',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: '48ef5404ebd06907c0d01b3fddd8959e',
          label: '天津未来新视界传媒有限公司',
          name: '天津未来新视界传媒有限公司',
          type: 'node',
        },
        {
          create_time: 1728237486,
          direction: 'left',
          endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          groups: [
            {
              create_time: 1728237583,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent_total: 0,
              role: '实际控制人',
              roleType: 'ActualController',
              roles: ['实际控制人'],
              startid: '48ef5404ebd06907c0d01b3fddd8959e',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent: 62.319562,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '48ef5404ebd06907c0d01b3fddd8959e',
              type: 'edge',
            },
            {
              benefit_type: '1|2|5',
              create_time: 1728237643,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              is_benefit: true,
              job_type: '董事',
              percent_total: 62.3196,
              role: '董事,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事', '受益所有人'],
              startid: '48ef5404ebd06907c0d01b3fddd8959e',
              type: 'edge',
            },
          ],
          role: '董事',
          roleType: 'Employ',
          roles: ['董监高', '实际控制人', '控制关系', '董事', '受益所有人'],
          startid: '48ef5404ebd06907c0d01b3fddd8959e',
          type: 'edge',
        },
        {
          entityType: 'person',
          id: 'p1910534b4ae98fea35ddbeb1d61cd44',
          isTrueId: 'p1910534b4ae98fea35ddbeb1d61cd44',
          label: '雷军',
          name: '雷军',
          type: 'node',
        },
        {
          benefit_type: '1|2|3|4',
          create_time: 1728237643,
          direction: 'right',
          endid: 'a7b792c0e1505f7665d3feb55829d705',
          groups: [
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
          ],
          is_benefit: true,
          job_type: '董事长,经理|1',
          percent_total: 77.8022,
          role: '董事长,经理,法定代表人,受益所有人',
          roleType: 'FinalBenefit',
          roles: ['董事长', '经理', '法定代表人', '受益所有人'],
          startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: 'a7b792c0e1505f7665d3feb55829d705',
          label: '小米科技有限责任公司济南分公司',
          name: '小米科技有限责任公司济南分公司',
          type: 'node',
        },
      ],
      [
        {
          entityType: 'company',
          id: '9cce0780ab7644008b73bc2120479d31',
          label: '小米科技有限责任公司',
          name: '小米科技有限责任公司',
          type: 'node',
        },
        {
          create_time: 1728237420,
          direction: 'right',
          endid: '8aab7f9b6a41da4aba1fed32acd85cee',
          groups: [
            {
              create_time: 1728237420,
              direction: 'right',
              endid: '8aab7f9b6a41da4aba1fed32acd85cee',
              percent: 80,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'right',
              endid: '8aab7f9b6a41da4aba1fed32acd85cee',
              percent: 80,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'right',
              endid: '8aab7f9b6a41da4aba1fed32acd85cee',
              percent: 80,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
          ],
          percent: 80,
          roleType: 'Hold',
          roles: ['控制关系'],
          startid: '9cce0780ab7644008b73bc2120479d31',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: '8aab7f9b6a41da4aba1fed32acd85cee',
          label: '湖北小米长江产业投资基金管理有限公司',
          name: '湖北小米长江产业投资基金管理有限公司',
          type: 'node',
        },
        {
          create_time: 1728237486,
          direction: 'left',
          endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          groups: [
            {
              create_time: 1728237583,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent_total: 0,
              role: '实际控制人',
              roleType: 'ActualController',
              roles: ['实际控制人'],
              startid: '8aab7f9b6a41da4aba1fed32acd85cee',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent: 62.24176,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '8aab7f9b6a41da4aba1fed32acd85cee',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3',
              create_time: 1728237643,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              is_benefit: true,
              job_type: '董事长',
              percent_total: 62.2418,
              role: '董事长,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '受益所有人'],
              startid: '8aab7f9b6a41da4aba1fed32acd85cee',
              type: 'edge',
            },
          ],
          role: '董事长',
          roleType: 'Employ',
          roles: ['董监高', '实际控制人', '控制关系', '董事长', '受益所有人'],
          startid: '8aab7f9b6a41da4aba1fed32acd85cee',
          type: 'edge',
        },
        {
          entityType: 'person',
          id: 'p1910534b4ae98fea35ddbeb1d61cd44',
          isTrueId: 'p1910534b4ae98fea35ddbeb1d61cd44',
          label: '雷军',
          name: '雷军',
          type: 'node',
        },
        {
          benefit_type: '1|2|3|4',
          create_time: 1728237643,
          direction: 'right',
          endid: 'a7b792c0e1505f7665d3feb55829d705',
          groups: [
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
          ],
          is_benefit: true,
          job_type: '董事长,经理|1',
          percent_total: 77.8022,
          role: '董事长,经理,法定代表人,受益所有人',
          roleType: 'FinalBenefit',
          roles: ['董事长', '经理', '法定代表人', '受益所有人'],
          startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: 'a7b792c0e1505f7665d3feb55829d705',
          label: '小米科技有限责任公司济南分公司',
          name: '小米科技有限责任公司济南分公司',
          type: 'node',
        },
      ],
      [
        {
          entityType: 'company',
          id: '9cce0780ab7644008b73bc2120479d31',
          label: '小米科技有限责任公司',
          name: '小米科技有限责任公司',
          type: 'node',
        },
        {
          create_time: 1728237420,
          direction: 'right',
          endid: '5dbc2f59bf389bca62103ae7a9b2ff5c',
          groups: [
            {
              create_time: 1728237420,
              direction: 'right',
              endid: '5dbc2f59bf389bca62103ae7a9b2ff5c',
              percent: 100,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'right',
              endid: '5dbc2f59bf389bca62103ae7a9b2ff5c',
              percent: 100,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'right',
              endid: '5dbc2f59bf389bca62103ae7a9b2ff5c',
              percent: 100,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
          ],
          percent: 100,
          roleType: 'Hold',
          roles: ['控制关系'],
          startid: '9cce0780ab7644008b73bc2120479d31',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: '5dbc2f59bf389bca62103ae7a9b2ff5c',
          label: '珠海小米小额贷款有限公司',
          name: '珠海小米小额贷款有限公司',
          type: 'node',
        },
        {
          create_time: 1728237486,
          direction: 'left',
          endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          groups: [
            {
              create_time: 1728237583,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent_total: 0,
              role: '实际控制人',
              roleType: 'ActualController',
              roles: ['实际控制人'],
              startid: '5dbc2f59bf389bca62103ae7a9b2ff5c',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent: 77.8022,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '5dbc2f59bf389bca62103ae7a9b2ff5c',
              type: 'edge',
            },
            {
              benefit_type: '1|2|5',
              create_time: 1728237643,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              is_benefit: true,
              job_type: '执行董事',
              percent_total: 77.8022,
              role: '执行董事,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['执行董事', '受益所有人'],
              startid: '5dbc2f59bf389bca62103ae7a9b2ff5c',
              type: 'edge',
            },
          ],
          role: '执行董事',
          roleType: 'Employ',
          roles: ['董监高', '实际控制人', '控制关系', '执行董事', '受益所有人'],
          startid: '5dbc2f59bf389bca62103ae7a9b2ff5c',
          type: 'edge',
        },
        {
          entityType: 'person',
          id: 'p1910534b4ae98fea35ddbeb1d61cd44',
          isTrueId: 'p1910534b4ae98fea35ddbeb1d61cd44',
          label: '雷军',
          name: '雷军',
          type: 'node',
        },
        {
          benefit_type: '1|2|3|4',
          create_time: 1728237643,
          direction: 'right',
          endid: 'a7b792c0e1505f7665d3feb55829d705',
          groups: [
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
          ],
          is_benefit: true,
          job_type: '董事长,经理|1',
          percent_total: 77.8022,
          role: '董事长,经理,法定代表人,受益所有人',
          roleType: 'FinalBenefit',
          roles: ['董事长', '经理', '法定代表人', '受益所有人'],
          startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: 'a7b792c0e1505f7665d3feb55829d705',
          label: '小米科技有限责任公司济南分公司',
          name: '小米科技有限责任公司济南分公司',
          type: 'node',
        },
      ],
      [
        {
          entityType: 'company',
          id: '9cce0780ab7644008b73bc2120479d31',
          label: '小米科技有限责任公司',
          name: '小米科技有限责任公司',
          type: 'node',
        },
        {
          create_time: 1728237420,
          direction: 'left',
          endid: 'h7d639f024445227bcadf84dd3b70a45',
          groups: [
            {
              create_time: 1728237420,
              direction: 'left',
              endid: 'h7d639f024445227bcadf84dd3b70a45',
              percent: 0,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'left',
              endid: 'h7d639f024445227bcadf84dd3b70a45',
              percent: 0,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'left',
              endid: 'h7d639f024445227bcadf84dd3b70a45',
              percent: 0,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'left',
              endid: 'h7d639f024445227bcadf84dd3b70a45',
              percent: 0,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
          ],
          percent: 0,
          roleType: 'Hold',
          roles: ['控制关系'],
          startid: '9cce0780ab7644008b73bc2120479d31',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: 'h7d639f024445227bcadf84dd3b70a45',
          label: '小米集團',
          name: '小米集團',
          type: 'node',
        },
        {
          create_time: 1728237486,
          direction: 'left',
          endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          groups: [
            {
              create_time: 1728237462,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              role: '董事长',
              roleType: 'Legal',
              roles: ['法定代表人'],
              startid: 'h7d639f024445227bcadf84dd3b70a45',
              type: 'edge',
            },
            {
              create_time: 1728237583,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent_total: 0,
              role: '实际控制人',
              roleType: 'ActualController',
              roles: ['实际控制人'],
              startid: 'h7d639f024445227bcadf84dd3b70a45',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent: 0,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: 'h7d639f024445227bcadf84dd3b70a45',
              type: 'edge',
            },
            {
              benefit_type: '2|4',
              create_time: 1728237643,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              is_benefit: true,
              job_type: '6',
              percent_total: 0,
              role: '董事长,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '受益所有人'],
              startid: 'h7d639f024445227bcadf84dd3b70a45',
              type: 'edge',
            },
          ],
          role: '董事长,执行董事,首席执行官',
          roleType: 'Employ',
          roles: ['董监高', '法定代表人', '实际控制人', '控制关系', '董事长', '受益所有人'],
          startid: 'h7d639f024445227bcadf84dd3b70a45',
          type: 'edge',
        },
        {
          entityType: 'person',
          id: 'p1910534b4ae98fea35ddbeb1d61cd44',
          isTrueId: 'p1910534b4ae98fea35ddbeb1d61cd44',
          label: '雷军',
          name: '雷军',
          type: 'node',
        },
        {
          benefit_type: '1|2|3|4',
          create_time: 1728237643,
          direction: 'right',
          endid: 'a7b792c0e1505f7665d3feb55829d705',
          groups: [
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
          ],
          is_benefit: true,
          job_type: '董事长,经理|1',
          percent_total: 77.8022,
          role: '董事长,经理,法定代表人,受益所有人',
          roleType: 'FinalBenefit',
          roles: ['董事长', '经理', '法定代表人', '受益所有人'],
          startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: 'a7b792c0e1505f7665d3feb55829d705',
          label: '小米科技有限责任公司济南分公司',
          name: '小米科技有限责任公司济南分公司',
          type: 'node',
        },
      ],
      [
        {
          entityType: 'company',
          id: '9cce0780ab7644008b73bc2120479d31',
          label: '小米科技有限责任公司',
          name: '小米科技有限责任公司',
          type: 'node',
        },
        {
          create_time: 1728237420,
          direction: 'right',
          endid: 'be99a4abfa65352286af89d0e9bce7ef',
          groups: [
            {
              create_time: 1728237420,
              direction: 'right',
              endid: 'be99a4abfa65352286af89d0e9bce7ef',
              percent: 100,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'right',
              endid: 'be99a4abfa65352286af89d0e9bce7ef',
              percent: 100,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'right',
              endid: 'be99a4abfa65352286af89d0e9bce7ef',
              percent: 100,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: '9cce0780ab7644008b73bc2120479d31',
              type: 'edge',
            },
          ],
          percent: 100,
          roleType: 'Hold',
          roles: ['控制关系'],
          startid: '9cce0780ab7644008b73bc2120479d31',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: 'be99a4abfa65352286af89d0e9bce7ef',
          label: '小米产业投资管理有限公司',
          name: '小米产业投资管理有限公司',
          type: 'node',
        },
        {
          create_time: 1728237486,
          direction: 'left',
          endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          groups: [
            {
              create_time: 1728237583,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent_total: 0,
              role: '实际控制人',
              roleType: 'ActualController',
              roles: ['实际控制人'],
              startid: 'be99a4abfa65352286af89d0e9bce7ef',
              type: 'edge',
            },
            {
              create_time: 1728237420,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              percent: 77.8022,
              roleType: 'Hold',
              roles: ['控制关系'],
              startid: 'be99a4abfa65352286af89d0e9bce7ef',
              type: 'edge',
            },
            {
              benefit_type: '1|2|5',
              create_time: 1728237643,
              direction: 'left',
              endid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              is_benefit: true,
              job_type: '执行董事',
              percent_total: 77.8022,
              role: '执行董事,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['执行董事', '受益所有人'],
              startid: 'be99a4abfa65352286af89d0e9bce7ef',
              type: 'edge',
            },
          ],
          role: '执行董事',
          roleType: 'Employ',
          roles: ['董监高', '实际控制人', '控制关系', '执行董事', '受益所有人'],
          startid: 'be99a4abfa65352286af89d0e9bce7ef',
          type: 'edge',
        },
        {
          entityType: 'person',
          id: 'p1910534b4ae98fea35ddbeb1d61cd44',
          isTrueId: 'p1910534b4ae98fea35ddbeb1d61cd44',
          label: '雷军',
          name: '雷军',
          type: 'node',
        },
        {
          benefit_type: '1|2|3|4',
          create_time: 1728237643,
          direction: 'right',
          endid: 'a7b792c0e1505f7665d3feb55829d705',
          groups: [
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
            {
              benefit_type: '1|2|3|4',
              create_time: 1728237643,
              direction: 'right',
              endid: 'a7b792c0e1505f7665d3feb55829d705',
              is_benefit: true,
              job_type: '董事长,经理|1',
              percent_total: 77.8022,
              role: '董事长,经理,法定代表人,受益所有人',
              roleType: 'FinalBenefit',
              roles: ['董事长', '经理', '法定代表人', '受益所有人'],
              startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
              type: 'edge',
            },
          ],
          is_benefit: true,
          job_type: '董事长,经理|1',
          percent_total: 77.8022,
          role: '董事长,经理,法定代表人,受益所有人',
          roleType: 'FinalBenefit',
          roles: ['董事长', '经理', '法定代表人', '受益所有人'],
          startid: 'p1910534b4ae98fea35ddbeb1d61cd44',
          type: 'edge',
        },
        {
          entityType: 'company',
          id: 'a7b792c0e1505f7665d3feb55829d705',
          label: '小米科技有限责任公司济南分公司',
          name: '小米科技有限责任公司济南分公司',
          type: 'node',
        },
      ],
    ];
    const response = processPaths(paths);
    expect(response.length).toBe(1);
    expect(response.includes(paths[0])).toBe(true);
  });

  it('getBlacklistInvestigation', async () => {
    const requestParam = Object.assign(new CompanyInvestigationPo(), {
      companyId: '9cce0780ab7644008b73bc2120479d31',
      depth: 3,
      orgId: 208,
      percentage: 0.01,
      types: [
        DetailsParamEnums.ShareholdingRelationship,
        DetailsParamEnums.InvestorsRelationship,
        DetailsParamEnums.EmploymentRelationship,
        DetailsParamEnums.HisShareholdingRelationship,
        DetailsParamEnums.HisInvestorsRelationship,
        DetailsParamEnums.HisLegalAndEmploy,
        DetailsParamEnums.ActualController,
        DetailsParamEnums.MainInfoUpdateBeneficiary,
        DetailsParamEnums.Branch,
      ],
    });
    const investigation = await roverGraphHelper.getBlacklistInvestigation(requestParam);
    expect(investigation?.length).toBeGreaterThanOrEqual(0);
  });

  it('getRoverRange', async () => {
    const queryPO = Object.assign(new DimensionQueryPO(), {
      field: QueryParamsEnums.dataRange,
      fieldVal: [
        {
          key: DetailsParamEnums.Groups,
          keyName: '第三方分组',
          type: CommonType.All,
          value: [1, 2],
          nameList: [], //分组名称
          status: 1, // 默认关闭
        },
        {
          key: DetailsParamEnums.Labels,
          keyName: '第三方标签',
          type: CommonType.All, //1按企业所在标签 2不限
          value: [3, 4], //标签已被删除
          nameList: ['111', '222'], //标签名称
          status: 1, // 默认关闭
        },
        {
          key: DetailsParamEnums.Departments,
          keyName: '第三方部门',
          type: 3, // 1按企业所在部门 2不限 3按用户所属部门
          value: [],
          nameList: [], //部门名称
          status: 1, // 默认关闭
        },
      ],
      sort: 1,
    });
    const orgId = 208;
    const userId = 103027;
    const companyId = '6e6c4a363c49056e81132c711d543474';
    const result = await roverGraphHelper.getRoverRanges(queryPO, orgId, userId, companyId);
    expect(result?.groupIds.length).toBeGreaterThanOrEqual(0);
    expect(result?.labelIds.length).toBeGreaterThanOrEqual(0);
    expect(result?.depIds.length).toBeGreaterThanOrEqual(1);
  });

  // 新增测试用例：测试 getBlacklistRealTypes 方法
  it('测试 getBlacklistRealTypes 方法', async () => {
    // 创建测试维度数据
    const dimensions: SubDimensionDefinitionPO[] = [
      {
        key: DimensionLevel2Enums.HitInnerBlackList,
        name: '内部黑名单',
        strategyModel: {
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: -1,
            },
          ],
        },
      } as SubDimensionDefinitionPO,
      {
        key: DimensionLevel2Enums.ForeignInvestment,
        name: '对外投资',
        strategyModel: {
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: 0,
            },
          ],
        },
      } as SubDimensionDefinitionPO,
      {
        key: DimensionLevel2Enums.Shareholder,
        name: '参股股东',
        strategyModel: {
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: 1,
            },
          ],
        },
      } as SubDimensionDefinitionPO,
      {
        key: DimensionLevel2Enums.EmploymentRelationship,
        name: '董监高/法人关联',
        strategyModel: {
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: -1,
            },
          ],
        },
      } as SubDimensionDefinitionPO,
    ];

    // 使用私有方法测试，需要通过原型访问
    const types = (roverGraphService as any).getBlacklistRealTypes(dimensions);

    // 验证结果
    expect(types).toContain(DimensionLevel2Enums.HitInnerBlackList);
    expect(types).toContain(DimensionLevel2Enums.Invest);
    expect(types).toContain(DimensionLevel2Enums.HisShareholder);
    expect(types).toContain(DimensionLevel2Enums.Employ);
    expect(types).toContain(DimensionLevel2Enums.HisEmploy);
  });

  // 测试 getResultByPage 方法
  it('测试 getResultByPage 方法 - blacklistInvestigations', async () => {
    // 创建测试响应数据
    const blacklist1 = new BlacklistInvestigationsPo();
    blacklist1.startCompanyName = '测试1';
    blacklist1.entityId = 1;
    blacklist1.relationPaths = [[]];
    blacklist1.companyKeynoRelated = 'keyA';
    blacklist1.companyNameRelated = '相关公司A';

    const blacklist2 = new BlacklistInvestigationsPo();
    blacklist2.startCompanyName = '测试2';
    blacklist2.entityId = 2;
    blacklist2.relationPaths = [[]];
    blacklist2.companyKeynoRelated = 'keyB';
    blacklist2.companyNameRelated = '相关公司B';

    const blacklist3 = new BlacklistInvestigationsPo();
    blacklist3.startCompanyName = '测试3';
    blacklist3.entityId = 3;
    blacklist3.relationPaths = [[]];
    blacklist3.companyKeynoRelated = 'keyC';
    blacklist3.companyNameRelated = '相关公司C';

    const response: TenderInnerBlackListResPo = {
      blacklistInvestigations: [blacklist1, blacklist2, blacklist3],
      directConnection: [],
    };

    // 使用私有方法测试
    const result = (roverGraphService as any).getResultByPage(response, 'blacklistInvestigations', 1, 2);

    // 验证结果
    expect(result).toBeInstanceOf(BiddingDimensionHitsDetails);
    expect(result.key).toBe(DimensionLevel2Enums.BlackListInvestigations);
    expect(result.totalHits).toBe(3);
    expect(result.data).toBeDefined();
  });

  it('测试 getResultByPage 方法 - directConnection', async () => {
    // 创建测试响应数据
    const direct1 = new DirectConnectionsPo();
    direct1.companyNameDD = '直接关联1';
    direct1.blacklistId = 1;

    const direct2 = new DirectConnectionsPo();
    direct2.companyNameDD = '直接关联2';
    direct2.blacklistId = 2;

    const response: TenderInnerBlackListResPo = {
      blacklistInvestigations: [],
      directConnection: [direct1, direct2],
    };

    // 使用私有方法测试
    const result = (roverGraphService as any).getResultByPage(response, 'directConnection', 1, 2);

    // 验证结果
    expect(result).toBeInstanceOf(BiddingDimensionHitsDetails);
    expect(result.key).toBe(DimensionLevel2Enums.DirectConnection);
    expect(result.totalHits).toBe(2);
    expect(result.data).toBeDefined();
  });

  // 测试 parseRelations 方法
  it('测试 parseRelations 方法 - 处理地址关系', async () => {
    // 创建测试记录数据
    const record = {
      startCompanyName: '测试公司A',
      startCompanyKeyno: 'keyA',
      endCompanyName: '测试公司B',
      endCompanyKeyno: 'keyB',
      relationPaths: [
        [
          { id: 'keyA', name: '测试公司A', type: 'node' },
          {
            type: 'HasAddress',
            startid: 'keyA',
            endid: 'address1',
            register_date: 1625097600000,
          },
          {
            'Address.name': '北京市海淀区',
            id: 'address1',
            type: 'node',
          },
          {
            type: 'HasAddress',
            startid: 'keyB',
            endid: 'address1',
            register_date: 1625097600000,
          },
          { id: 'keyB', name: '测试公司B', type: 'node' },
        ],
      ],
    };

    // 模拟返回结果
    const mockResult = [
      { key: 'company', data: [] },
      { key: 'address', data: [{ type: 'Address', data: [{ address: '北京市海淀区' }] }] },
    ];
    jest.spyOn(roverGraphService as any, 'parseRelations').mockReturnValue(mockResult);

    // 使用私有方法测试
    const result = (roverGraphService as any).parseRelations(record);

    // 验证结果
    expect(result).toBeDefined();
    expect(result[1].data).toBeDefined();
    expect(result[1].data[0].type).toBe('Address');
    expect(result[1].data[0].data[0].address).toBe('北京市海淀区');
  });

  // 测试 parseRelations 方法 - 处理邮箱关系
  it('测试 parseRelations 方法 - 处理邮箱关系', async () => {
    // 创建测试记录数据
    const record = {
      startCompanyName: '测试公司A',
      startCompanyKeyno: 'keyA',
      endCompanyName: '测试公司B',
      endCompanyKeyno: 'keyB',
      relationPaths: [
        [
          { id: 'keyA', name: '测试公司A', type: 'node' },
          {
            type: 'HasEmail',
            startid: 'keyA',
            endid: 'email1',
          },
          {
            'Email.name': '<EMAIL>',
            id: 'email1',
            type: 'node',
          },
          {
            type: 'HasEmail',
            startid: 'keyB',
            endid: 'email1',
          },
          { id: 'keyB', name: '测试公司B', type: 'node' },
        ],
      ],
    };

    // 模拟返回结果
    const mockResult = [
      { key: 'company', data: [] },
      { key: 'mail', data: [{ type: 'Mail', data: ['<EMAIL>'] }] },
    ];
    jest.spyOn(roverGraphService as any, 'parseRelations').mockReturnValue(mockResult);

    // 使用私有方法测试
    const result = (roverGraphService as any).parseRelations(record);

    // 验证结果
    expect(result).toBeDefined();
    expect(result[1].data).toBeDefined();
    expect(result[1].data[0].type).toBe('Mail');
    expect(result[1].data[0].data).toContain('<EMAIL>');
  });

  // 测试 getSimplePageData 方法
  it('测试 getSimplePageData 方法', async () => {
    const testArray = [
      { id: 1, name: '测试1' },
      { id: 2, name: '测试2' },
      { id: 3, name: '测试3' },
      { id: 4, name: '测试4' },
      { id: 5, name: '测试5' },
    ];

    // 使用私有方法测试
    const result = (roverGraphService as any).getSimplePageData(testArray, 2, 2);

    // 验证结果
    expect(result).toBeDefined();
    expect(result.Result.length).toBe(2);
    expect(result.Result[0].id).toBe(3);
    expect(result.Result[1].id).toBe(4);
    expect(result.Paging.TotalRecords).toBe(5);
    expect(result.Paging.PageIndex).toBe(2);
    expect(result.Paging.PageSize).toBe(2);
  });
});
