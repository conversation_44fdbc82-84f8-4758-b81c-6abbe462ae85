import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { CompanySearchService } from '../../company/company-search.service';
import * as moment from 'moment/moment';
import { DATE_FORMAT } from 'libs/constants/common';
import { DimensionQueryPO, getCompareResult, OperatorEnums, OperatorName } from 'libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { QueryParamsEnums } from 'libs/model/diligence/pojo/dimension/dimension.filter.params';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { HitDetailsBaseResponse } from 'libs/model/diligence/pojo/req&res/details/response';
import { AnalyzeService } from './analyze.base.service';
import { DimensionScorePO } from '../../../libs/model/diligence/pojo/dimension/DimensionScorePO';
import * as Bluebird from 'bluebird';
import { DimensionLevelClass } from '../../../libs/constants/dimension.constants';
import { processScorePO } from '../../../libs/utils/diligence/diligence.utils';
import { ConfigService } from '../../../libs/config/config.service';
import { HttpUtilsService } from '../../../libs/config/httputils.service';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { DimensionAnalyzeParamsPO } from '../DimensionAnalyzeParamsPO';
import { isOrganism } from '../../company/utils';
import { CompanyDetailService } from '../../company/company-detail.service';
import { getStartTimeByCycle } from 'libs/utils/utils';
import { pick } from 'lodash';
import { RoverGraphService } from './rover.graph.service';
import { RoverGraphHelper } from '../helper/rover.graph.helper';

/**
 * 企业详情数据源接口
 */
@Injectable()
export class CompanyService implements AnalyzeService<HitDetailsBaseQueryParams, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(CompanyService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly companySearchService: CompanySearchService,
    private readonly companyDetailService: CompanyDetailService,
    private readonly httpUtils: HttpUtilsService,
    private readonly roverGraphService: RoverGraphService,
    private readonly roverGraphHelper: RoverGraphHelper,
  ) {}

  async analyze(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[]): Promise<DimensionScorePO[]> {
    const [companyDetail, companyKzzDetail] = await Bluebird.all([
      this.companySearchService.companyDetailsQcc(companyId),
      this.companySearchService.companyDetailsKys(companyId),
    ]);
    // this.logger.info(`scanByCompanyEs():` + targetTypes.map((t) => t.key));
    return Bluebird.map(dimensionDefinitionPOs, async (d) => {
      /**
       * 处理命中描述信息需要的参数
       */
      const desData = {
        level: DimensionLevelClass[d.strategyModel.level || 0],
        cycle: d.strategyModel?.cycle > 0 ? d.strategyModel.cycle : 0,
        name: d.name,
        isHidden: '',
        isHiddenY: '',
      };
      let hitCount = 0;
      switch (d.key) {
        case DimensionLevel3Enums.BusinessAbnormal1: {
          // 经营状态非存续
          if (this.isBusinessAbnormal1(companyId, companyDetail?.ShortStatus)) {
            hitCount = 1;
          }
          break;
        }
        case DimensionLevel3Enums.BusinessAbnormal6: {
          // == ' 253392422400' ? '无固定期限' : company.TeamEnd | dateformat({default:'无固定期限',isS:true})
          if (companyDetail?.TeamEnd && moment.unix(companyDetail?.TeamEnd).isSameOrBefore(moment())) {
            hitCount = 1;
            Object.assign(desData, {
              start: moment.unix(companyDetail?.TermStart).format(DATE_FORMAT),
              end: moment.unix(companyDetail?.TeamEnd).format(DATE_FORMAT),
            });
          }
          break;
        }
        case DimensionLevel3Enums.BusinessAbnormal7: {
          // 无统一社会信用代码
          // ['撤销', '吊销', '注销', '注销中', '名称核准不通过', '清算', '除名'].includes(companyDetail.ShortStatus)
          if (!companyDetail?.CreditCode && !['注销'].includes(companyDetail?.ShortStatus)) {
            hitCount = 1;
          }
          break;
        }
        // case DimensionLevel3Enums.BusinessAbnormal2:
        // 被列为非正常户
        case DimensionLevel3Enums.BusinessAbnormal4: {
          hitCount =
            (
              await this.getDimensionDetail(d, {
                keyNo: companyId,
                pageIndex: 1,
                pageSize: 1000,
              })
            )?.Paging?.TotalRecords || 0;
          break;
        }
        case DimensionLevel3Enums.BusinessAbnormal8: {
          if (this.isBusinessAbnormal8(companyDetail?.TeamEnd)) {
            hitCount = 1;
            Object.assign(desData, {
              start: moment.unix(companyDetail?.TermStart).format(DATE_FORMAT),
              end: moment.unix(companyDetail?.TeamEnd).format(DATE_FORMAT),
            });
          }
          break;
        }
        case DimensionLevel3Enums.BusinessAbnormal9: {
          //经营期限已到期或临近到期
          const detailParam = d.strategyModel.detailsParams?.find((f) => f.field === QueryParamsEnums.approachingExpiry);
          if (
            this.isTeamEndTimeOut(companyDetail?.TeamEnd, detailParam?.fieldVal) ||
            (companyDetail?.TeamEnd && moment.unix(companyDetail?.TeamEnd).isSameOrBefore(moment()))
          ) {
            hitCount = 1;
            Object.assign(desData, {
              start: moment.unix(companyDetail?.TermStart).format(DATE_FORMAT),
              end: moment.unix(companyDetail?.TeamEnd).format(DATE_FORMAT),
            });
          }
          break;
        }
        case DimensionLevel2Enums.EstablishedTime: {
          const { isHit, baseMonthCount, operator } = this.isEstablishedTime(d?.strategyModel?.detailsParams, companyDetail?.StartDate);
          if (isHit) {
            hitCount = 1;
            Object.assign(desData, { amountMonth: baseMonthCount, operator: OperatorName[operator] });
          }
          break;
        }
        case DimensionLevel2Enums.LowCapital: {
          // 注册资本小于100万
          let registcapiamount = companyDetail?.['Registcapiamount'];
          if (!registcapiamount) {
            registcapiamount = companyDetail?.CommonList?.find((item) => item.Key === 19);
          }
          const { isHit, baseAmount, operator2 } = this.isLowCapital(d?.strategyModel?.detailsParams, registcapiamount);
          if (isHit) {
            hitCount = 1;
            Object.assign(desData, { amountW: parseInt(baseAmount), operator: OperatorName[operator2] });
          }
          break;
        }
        case DimensionLevel2Enums.FakeSOES: {
          hitCount =
            (
              await this.getDimensionDetail(d, {
                keyNo: companyId,
                pageIndex: 1,
                pageSize: 1000,
              })
            )?.Paging?.TotalRecords || 0;
          break;
        }
        case DimensionLevel2Enums.FraudList: {
          // isadd 用来表示qfktag的有效性，-1表示无效，0是更新，1是新增
          if (
            companyKzzDetail?.result?.isadd >= 0 &&
            companyKzzDetail?.result?.qfktag &&
            JSON.parse(companyKzzDetail.result.qfktag).tags?.find((t) => t.code == d.typeCode)
          ) {
            hitCount = 1;
          }
          break;
        }
        case DimensionLevel2Enums.NoCapital: {
          if (companyDetail?.EconKind === '事业单位' || companyDetail?.EconKind === '个体工商户') {
            return HitDetailsBaseResponse.ok();
          }

          //兼容旧模型，旧的规则沿用专业版，与202503-RA14536迭代升级规则不一致
          const paidInCapitalRatio = d?.strategyModel?.detailsParams?.find((q) => q.field === QueryParamsEnums.paidInCapitalRatio);
          if (paidInCapitalRatio) {
            const response = await this.getDimensionDetail(d, { keyNo: companyId });
            hitCount = response?.Paging?.TotalRecords || 0;
          } else {
            if (
              companyKzzDetail?.result?.isadd >= 0 &&
              companyKzzDetail?.result?.qfktag &&
              JSON.parse(companyKzzDetail.result.qfktag).tags?.find((t) => t.code == d.typeCode)
            ) {
              hitCount = 1;
            }
          }
          break;
        }
      }
      if (hitCount > 0) {
        return processScorePO(d, hitCount, desData);
      }
      return null;
    }).then((item) => item.filter((t) => t));
  }

  async getDimensionDetail(dimension: DimensionDefinitionPO, data: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const { keyNo, pageIndex: PageIndex, pageSize: PageSize } = data;
    const [companyDetail, companyKzzDetail] = await Bluebird.all([
      this.companySearchService.companyDetailsQcc(keyNo),
      this.companySearchService.companyDetailsKys(keyNo),
    ]);
    const typeCodeTag = companyKzzDetail?.result?.qfktag
      ? JSON.parse(companyKzzDetail.result.qfktag)?.tags?.find((t) => t.code == dimension.typeCode)
      : undefined;

    const dimensionDetails = HitDetailsBaseResponse.ok();
    switch (dimension?.key) {
      // 无统一社会信用代码
      case DimensionLevel3Enums.BusinessAbnormal7:
        if (!companyDetail?.CreditCode && !['注销'].includes(companyDetail?.ShortStatus)) {
          return Object.assign(dimensionDetails, {
            Result: [{ description: '该企业未登记统一社会信用代码' }],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      // 经营状态非存续
      case DimensionLevel3Enums.BusinessAbnormal1: {
        const shortStatus = companyDetail?.ShortStatus || companyKzzDetail.result.status;
        if (this.isBusinessAbnormal1(keyNo, shortStatus)) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '登记状态',
                value: companyDetail.ShortStatus,
                ShortStatus: companyDetail.ShortStatus,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      }
      // 被列为非正常户
      case DimensionLevel3Enums.BusinessAbnormal4: {
        const { strategyModel } = dimension;
        const { detailsParams, cycle } = strategyModel;
        const validData = detailsParams?.find((p) => p.field == QueryParamsEnums.isValid)?.fieldVal;
        const params: any = {
          ...data,
          isValid: Number(validData) === -1 ? [1, 0] : 1,
        };
        if (cycle !== -1) {
          const startTimestamp = getStartTimeByCycle(cycle || 3);
          params.rangeFilters = [
            {
              type: 'courtdate',
              dateList: [
                {
                  start: Math.ceil(startTimestamp / 1000),
                  end: Math.ceil(getStartTimeByCycle(0) / 1000),
                },
              ],
            },
          ];
        }
        try {
          const taxResponse = await this.companyDetailService.getTaxUnnormals(params);
          if (companyDetail?.CountInfo?.['TaxUnnormalCount'] && taxResponse?.Result?.length) {
            return Object.assign(dimensionDetails, {
              Result: taxResponse.Result.map((item) => {
                item.version = 'v2';
                return item;
              }),
              Paging: taxResponse.Paging,
            });
          }
        } catch (error) {
          this.logger.error(error);
          Object.assign(dimensionDetails, HitDetailsBaseResponse.failed('获取税务信息失败'));
        }
        break;
      }
      case DimensionLevel3Enums.BusinessAbnormal8: //临近经营期限
        if (this.isBusinessAbnormal8(companyDetail?.TeamEnd)) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '营业期限',
                value: moment.unix(companyDetail?.TermStart).format(DATE_FORMAT) + ' 至 ' + moment.unix(companyDetail.TeamEnd).format(DATE_FORMAT),
                TermStart: companyDetail?.TermStart,
                TeamEnd: companyDetail.TeamEnd,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      case DimensionLevel3Enums.BusinessAbnormal6: //经营期限已过有效期
        if (companyDetail?.TeamEnd && moment.unix(companyDetail?.TeamEnd).isSameOrBefore(moment())) {
          const start = companyDetail?.TermStart ? moment.unix(companyDetail?.TermStart).format(DATE_FORMAT) : '-';
          const end = companyDetail?.TeamEnd ? moment.unix(companyDetail.TeamEnd).format(DATE_FORMAT) : '-';
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '营业期限',
                value: start + ' 至 ' + end,
                TermStart: companyDetail?.TermStart,
                TeamEnd: companyDetail.TeamEnd,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      case DimensionLevel3Enums.BusinessAbnormal9: //经营期限已到期或临近到期
        const detailParam = dimension.strategyModel?.detailsParams?.find((f) => f.field === QueryParamsEnums.approachingExpiry);
        const start = companyDetail?.TermStart ? moment.unix(companyDetail?.TermStart).format(DATE_FORMAT) : '-';
        const end = companyDetail?.TeamEnd ? moment.unix(companyDetail.TeamEnd).format(DATE_FORMAT) : '-';
        if (this.isTeamEndTimeOut(companyDetail?.TeamEnd, detailParam?.fieldVal)) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '营业期限',
                value: start + ' 至 ' + end,
                TermStart: companyDetail?.TermStart,
                TeamEnd: companyDetail.TeamEnd,
                termStatus: '临近到期',
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        } else if (companyDetail?.TeamEnd && moment.unix(companyDetail?.TeamEnd).isSameOrBefore(moment())) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '营业期限',
                value: start + ' 至 ' + end,
                TermStart: companyDetail?.TermStart,
                TeamEnd: companyDetail.TeamEnd,
                termStatus: '已到期',
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      case DimensionLevel2Enums.EstablishedTime: {
        //新成立企业
        const { isHit } = this.isEstablishedTime(dimension?.strategyModel?.detailsParams, companyDetail?.StartDate);
        if (isHit) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label: '成立日期',
                value: moment.unix(companyDetail.StartDate).format(DATE_FORMAT),
                StartDate: companyDetail.StartDate,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      }
      case DimensionLevel2Enums.LowCapital: {
        let registcapiamount = companyDetail?.['Registcapiamount'];
        if (!registcapiamount) {
          registcapiamount = companyDetail?.CommonList?.find((item) => item.Key == 19);
        }
        const enterpriseTypeToLabelMap: Record<string, string> = {
          '001006': '出资额',
          '*********': '出资额',
          '*********': '出资额',
          '001015': '出资额',
          '001009': '资金数额',
          '001008': '成员出资总额',
          '001004': '开办资金',
          '001005': '注册资金',
        };

        const defaultLabel = '注册资本';
        let label = defaultLabel;
        if (companyDetail?.standardCode?.length) {
          companyDetail.standardCode.forEach((enterpriseType) => {
            if (enterpriseTypeToLabelMap[enterpriseType]) {
              label = enterpriseTypeToLabelMap[enterpriseType];
            }
          });
        }
        const { isHit } = this.isLowCapital(dimension?.strategyModel?.detailsParams, registcapiamount);
        if (isHit) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                label,
                value: companyDetail?.RegistCapi ?? null,
                RegistCapi: companyDetail?.RegistCapi ?? null,
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }

        break;
      }
      case DimensionLevel2Enums.FakeSOES: {
        const selfRisk = companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail?.result?.qfktag && typeCodeTag;

        //自身命中不再排查关联企业是否假冒国企
        if (selfRisk) {
          return Object.assign(dimensionDetails, {
            Result: [
              {
                Id: keyNo,
                description: `${companyKzzDetail.result.name}被列入假冒国企名单`,
                riskCompanyName: companyKzzDetail.result.name,
                riskCompanyId: keyNo,
                riskType: 'self',
                relations: [],
              },
            ],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }

        const params = {
          depth: 3,
          percentage: 100,
          relationTypes: ['Invest', 'Branch'],
          startCompanyId: keyNo,
        };

        const graphRelations = await this.roverGraphService.getCompanyDeepInvestBranch(params);
        const relations = this.roverGraphHelper.processInvestigationList(graphRelations);
        const relationCompanyIds = relations?.map((x) => x.endCompanyKeyno);
        const relationMap = new Map(relations?.map((r) => [r.endCompanyKeyno, r]) || []);

        let allRiskCompanies = [];
        if (relationCompanyIds?.length) {
          const allRelationDetails = await Bluebird.map(relationCompanyIds, async (id: string) => {
            return {
              detail: await this.companySearchService.companyDetailsKys(id),
              relation: relationMap.get(id),
            };
          });

          // 先过滤出所有风险企业
          allRiskCompanies = allRelationDetails
            .filter(
              ({ detail }) =>
                detail?.result?.isadd >= 0 && detail.result.qfktag && JSON.parse(detail.result.qfktag)?.tags?.find((t) => t.code == dimension.typeCode),
            )
            .map(({ detail, relation }) => ({
              id: detail?.result?.id,
              companyId: detail?.result?.id,
              companyName: detail?.result?.name,
              riskType: 'relation',
              relations: relation || [],
            }));
        }

        // 分页处理
        const startIndex = (PageIndex - 1) * PageSize;
        const endIndex = startIndex + PageSize;
        const pagedRiskCompanies = allRiskCompanies.slice(startIndex, endIndex);

        if (pagedRiskCompanies.length > 0) {
          return Object.assign(dimensionDetails, {
            Result: pagedRiskCompanies.map((risk) => ({
              Id: risk.id,
              description: `${risk.companyName}被列入假冒国企名单`,
              riskCompanyName: risk.companyName,
              riskCompanyId: risk.companyId,
              riskType: risk.riskType,
              relations: risk.relations,
            })),
            Paging: {
              PageSize,
              PageIndex,
              // 总记录数使用过滤后的全部风险企业数量
              TotalRecords: allRiskCompanies.length,
            },
          });
        }
        break;
      }
      // case DimensionLevel3Enums.BusinessAbnormal2: {
      //   if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail.result.qfktag && typeCodeTag) {
      //     const queryParam = {
      //       keyNo,
      //       pageIndex: PageIndex,
      //       pageSize: PageSize,
      //     };
      //     const dimensionQueryPo = dimension.strategyModel?.detailsParams?.find((p) => p.field == QueryParamsEnums.simpleCancellationStep);
      //     if (dimensionQueryPo) {
      //       const caseTypes = dimensionQueryPo.fieldVal.filter((item) => item.status);
      //       if (caseTypes?.length) {
      //         const caseType = caseTypes.map((k) => k.key).join(',');
      //         Object.assign(queryParam, { caseType });
      //       }
      //     }
      //     if (dimension.sourcePath) {
      //       const result = await this.httpUtils.getRequest(this.configService.proxyServer.dataService + dimension.sourcePath, queryParam);
      //       if (result?.Status == 200) {
      //         const { Result, Paging } = result;
      //         const newRes = Result.map((r) => {
      //           return {
      //             annoName: `${r.CompanyName}简易注销公告`,
      //             id: r.No,
      //             publishDate: r.PublicDate,
      //             resultContent: r.CancellationResultList?.[0]?.ResultContent,
      //           };
      //         });
      //         return Object.assign(dimensionDetails, {
      //           Result: newRes,
      //           Paging,
      //         });
      //       } else {
      //         return HitDetailsBaseResponse.failed('接口返回status != 200', DimensionSourceEnums.CompanyDetail);
      //       }
      //     }
      //   }
      //   break;
      // }
      case DimensionLevel2Enums.NoCapital: {
        // isadd 用来表示qfktag的有效性，-1表示无效，0是更新，1是新增
        const queryPo = dimension?.strategyModel?.detailsParams?.find((d) => d.field === QueryParamsEnums.paidInCapitalRatio);
        if (queryPo) {
          const { fieldVal, fieldOperator } = queryPo;
          //如果企业实缴资本为空，'-'，0等，则说明企业实缴资本异常，命中风险
          if (
            companyDetail?.RecCap &&
            !['-', 0, '0'].includes(companyDetail?.RecCap) &&
            companyDetail?.RegistCapi &&
            !['-', 0, '0'].includes(companyDetail?.RegistCapi)
          ) {
            const paidInCapitalRatio = this.calculatePaidInCapitalRatio(companyDetail.RecCap, companyDetail.RegistCapi);
            if (isNaN(paidInCapitalRatio) || !isFinite(paidInCapitalRatio)) {
              this.logger.warn(`异常实缴比例计算值 KeyNo:${keyNo} 计算结果:${paidInCapitalRatio}`);
              return dimensionDetails;
            }
            // 比较实缴资本比例
            if (getCompareResult(paidInCapitalRatio, fieldVal, fieldOperator)) {
              const result = await this.buildNoCapitalResult(companyDetail, paidInCapitalRatio);
              return Object.assign(dimensionDetails, pick(result, ['Result', 'Paging']));
            }
          } else {
            const result = await this.buildNoCapitalResult(companyDetail);
            return Object.assign(dimensionDetails, pick(result, ['Result', 'Paging']));
          }
        } else {
          if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail?.result?.qfktag && typeCodeTag) {
            //无实缴资本（实缴资本比例过低）
            return Object.assign(dimensionDetails, {
              Result: [
                [
                  { label: '注册资本', value: companyDetail.RegistCapi, RegistCapi: companyDetail.RegistCapi },
                  {
                    label: '实缴资本',
                    value: companyDetail.RecCap,
                    RecCap: companyDetail.RecCap,
                  },
                ],
              ],
              Paging: { TotalRecords: 1 },
            });
          }
        }

        break;
      }
      // 涉诈高风险名单
      case DimensionLevel2Enums.FraudList: {
        if (companyKzzDetail?.result?.isadd >= 0 && companyKzzDetail.result.qfktag && typeCodeTag) {
          return Object.assign(dimensionDetails, {
            Result: [{ description: '该企业因涉诈被列入高风险名单，请注意核实企业信息' }],
            Paging: { PageSize, PageIndex, TotalRecords: 1 },
          });
        }
        break;
      }
      default:
        break;
    }
    return dimensionDetails;
  }

  /**
   * 计算实缴资本比例
   */
  private calculatePaidInCapitalRatio(recCap: string, registCapi: string): number {
    const extractNumber = (str: string): number => {
      if (typeof str !== 'string' || str.trim() === '') return 0;
      const num = parseFloat(str.replace(/[^0-9.]/g, ''));
      if (str.includes('亿')) return num * *********;
      if (str.includes('万')) return num * 10000;
      return num;
    };

    const recCapNum = extractNumber(recCap);
    const registCapiNum = extractNumber(registCapi);
    return registCapiNum ? (recCapNum / registCapiNum) * 100 : 0;
  }

  /**
   * 构建实缴异常结果，若存在母公司持股大于50%，则返回母公司相关信息
   */
  private async buildNoCapitalResult(companyDetail: any, paidInCapitalRatio?: number) {
    const baseInfo = [
      { label: '注册资本', value: companyDetail.RegistCapi, RegistCapi: companyDetail.RegistCapi },
      { label: '实缴资本', value: companyDetail.RecCap, RecCap: companyDetail.RecCap },
      {
        label: '实缴比例',
        value: typeof paidInCapitalRatio === 'number' && !isNaN(paidInCapitalRatio) ? `${paidInCapitalRatio.toFixed(2)}%` : null,
        PaidInCapitalRatio: paidInCapitalRatio,
      },
    ];

    const partners = companyDetail?.Partners;
    const parentCompany = partners?.find((partner) => parseFloat(partner.StockPercentValue) > 50 && partner.StockTypeCode !== 1);

    if (parentCompany) {
      try {
        const parentCompanyDetail = await this.companySearchService.companyDetailsQcc(parentCompany.KeyNo);
        return {
          Result: [
            [
              ...baseInfo,
              { label: '母公司', value: parentCompany.StockName, ParentName: parentCompany.StockName, ParentKeyNo: parentCompany.KeyNo },
              { label: '母公司持股比例', value: parentCompany.StockPercent, ParentStockPercent: parentCompany.StockPercent },
              {
                label: '母公司注册资本',
                value: parentCompanyDetail.RegistCapi,
                ParentRegistCapi: parentCompanyDetail.RegistCapi,
              },
              { label: '母公司实缴资本', value: parentCompanyDetail.RecCap, ParentRecCap: parentCompanyDetail.RecCap },
            ],
          ],
          Paging: { TotalRecords: 1 },
        };
      } catch (error) {
        this.logger.error(`实缴资本异常-获取母公司详情失败 KeyNo:${parentCompany.KeyNo}`, error);
        return { Result: [baseInfo], Paging: { TotalRecords: 1 } };
      }
    }

    return { Result: [baseInfo], Paging: { TotalRecords: 1 } };
  }

  /**
   * 是否经营状态非存续
   */
  private isBusinessAbnormal1(keyNo: string, shortStatus: string) {
    if (isOrganism(keyNo)) {
      if (['撤销', '吊销', '注销', '注销中', '名称核准不通过', '清算', '除名'].includes(shortStatus)) {
        return true;
      }
    } else if (['吊销', '注销', '撤销', '停业', '歇业', '责令关闭', '清算', '除名'].includes(shortStatus)) {
      return true;
    }

    return false;
  }

  /**
   * 是否临近经营期限
   */
  private isBusinessAbnormal8(teamEnd) {
    if (teamEnd) {
      const baseDays = 90;
      //  企业经营到期时间 >今天 && <= 90天时命中
      if (moment.unix(teamEnd).isAfter(moment()) && moment.unix(teamEnd).isSameOrBefore(moment().add(baseDays, 'days'))) {
        return true;
      }
    }
    return false;
  }

  private isTeamEndTimeOut(teamEnd: number, approachingExpiry?: number) {
    if (teamEnd) {
      const expiryMonth = approachingExpiry || 3;
      //  企业经营到期时间 >今天 && <= expiryMonth时命中
      if (moment.unix(teamEnd).isAfter(moment()) && moment.unix(teamEnd).isSameOrBefore(moment().add(expiryMonth, 'months'))) {
        return true;
      }
    }
    return false;
  }

  /**
   * 是否新成立企业
   */
  private isEstablishedTime(params: DimensionQueryPO[], companyStartDate) {
    const detailsParams = params?.find((p) => p.field == QueryParamsEnums.duration);
    const baseMonthCount = detailsParams?.fieldVal || 24;
    const operator = detailsParams.fieldOperator || OperatorEnums.le;
    if (companyStartDate) {
      if (getCompareResult(moment().subtract(baseMonthCount, 'months').startOf('day').unix(), companyStartDate, operator)) {
        return { isHit: true, baseMonthCount, operator };
      }
    }
    return { isHit: false, baseMonthCount, operator };
  }

  /**
   * 是否注册资本小于100万
   */
  private isLowCapital(params: DimensionQueryPO[], registCapiAmount) {
    const detailsParams2 = params?.find((p) => p.field == QueryParamsEnums.registrationAmount);
    const baseAmount = detailsParams2?.fieldVal || 100;
    const operator2 = detailsParams2.fieldOperator || OperatorEnums.le;
    // 注册资本金额
    let registcapiamount = registCapiAmount?.['Value'] || 0;
    if (typeof registcapiamount === 'string') {
      registcapiamount = parseFloat(registcapiamount);
    }

    if (getCompareResult(registcapiamount, baseAmount, operator2)) {
      return { isHit: true, baseAmount, operator2, registcapiamount };
    }
    return { isHit: false, baseAmount, operator2, registcapiamount };
  }

  /**
   * 是否被列为非正常户 1废弃
   */
  private async isBusinessAbnormal4(data: HitDetailsBaseQueryParams) {
    let taxResponse: any = {};
    try {
      taxResponse = await this.companyDetailService.getTaxUnnormals(data);
    } catch (error) {
      this.logger.error(error);
    }
    // 新增的用v2判断
    return { taxResponse, version: 'v2' };
  }
}
