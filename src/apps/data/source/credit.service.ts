import { BadRequestException, Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { isNumber, pick, uniq } from 'lodash';
import { CreditBaseFilter } from 'libs/model/data/source/CreditRequestBaseParam';
import { DateRangeRelative } from 'libs/model/data/source/CreditSearchFilter';
import { DimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { EnterpriseLibService } from './enterprise.lib.service';
import { HitDetailsBaseResponse } from 'libs/model/diligence/pojo/req&res/details/response';
import { DimensionStrategyPO } from 'libs/model/diligence/pojo/dimension/DimensionStrategyPO';
import { QueryParamsEnums } from 'libs/model/diligence/pojo/dimension/dimension.filter.params';
import { OperatorEnums } from 'libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import * as Bluebird from 'bluebird';
import { AnalyzeService } from './analyze.base.service';
import { DimensionScorePO } from 'libs/model/diligence/pojo/dimension/DimensionScorePO';
import { HitDetailsCreditParam } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { DimensionAnalyzeParamsPO } from '../DimensionAnalyzeParamsPO';
import { getIsValidValue } from '../../../libs/utils/diligence/diligence.utils';

/**
 * 信用大数据数据源接口
 */
@Injectable()
export class CreditService implements AnalyzeService<HitDetailsCreditParam, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(CreditService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpUtils: HttpUtilsService,
    private readonly enterpriseLibService: EnterpriseLibService,
  ) {}

  async analyze(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], params: DimensionAnalyzeParamsPO): Promise<DimensionScorePO[]> {
    throw new BadRequestException('not supported');
  }

  // @Cacheable({ ttlSeconds: 300 })
  async getDimensionDetail(dimension: DimensionDefinitionPO, requestData: HitDetailsCreditParam): Promise<HitDetailsBaseResponse> {
    this.logger.info(`get hit details for dimension: ${dimension.key}`);
    if (!requestData?.keyNo) {
      throw new BadParamsException(RoverExceptions.Diligence.Detail.NeedKeyNo);
    }
    //查询参数使用优先级： 1. data 参数 2. dimension.strategyModel 如果有值，需要生成条件
    //根据key获取到category，拼接到data中
    let key = dimension.key;
    // 如果是 存在产品质量问题 必须传 subDimensionKey, 返回对应的子维度
    if (key === DimensionLevel2Enums.ProductQualityProblem) {
      if (!requestData?.subDimensionKey) {
        throw new BadParamsException(RoverExceptions.Diligence.Detail.NeedSubDimensionKey);
      }
      key = requestData.subDimensionKey;
    }

    const reqData = await this.requestDataConstructor(key, requestData, dimension.strategyModel);
    if (!reqData) {
      return HitDetailsBaseResponse.failed(RoverExceptions.BadParams.Common.error, DimensionSourceEnums.Credit, RoverExceptions.BadParams.Common.code);
    }
    const { keyNo } = requestData;
    try {
      const result = await this.httpUtils.postRequest(this.configService.dataServer.searchCredit, reqData);
      switch (dimension.key) {
        case DimensionLevel3Enums.MainMembersRestrictedOutbound:
        case DimensionLevel3Enums.MainMembersPersonCreditCurrent:
        case DimensionLevel2Enums.Bankruptcy: {
          await this.enterpriseLibService.processResponseData(result, dimension, keyNo, 'all');
          break;
        }
      }
      return Object.assign(new HitDetailsBaseResponse(), pick(result, ['Result', 'Paging', 'GroupItems']));
    } catch (error) {
      this.logger.error(`CreditService getDimensionDetail err: ${error}`);
      return HitDetailsBaseResponse.failed(error.response?.error || error.message, DimensionSourceEnums.Credit, error.response?.code);
    }
  }

  /**
   * 构造信用大数据请求参数
   * @param key
   * @param data
   * @param strategyModel
   */
  async requestDataConstructor(key: DimensionTypeEnums, data: Record<string, any>, strategyModel: DimensionStrategyPO) {
    const resultRequestData: Record<string, any> = {};
    //排序
    if (strategyModel?.sortField) {
      const sortField = strategyModel.sortField;
      resultRequestData['sortField'] = sortField.field;
      switch (sortField.order) {
        case 'DESC':
          resultRequestData['isSortAsc'] = false;
          break;
        case 'ASC':
          resultRequestData['isSortAsc'] = true;
          break;
        default:
          break;
      }
    }
    if (data?.field) {
      resultRequestData['sortField'] = data.field;
    }
    if (data?.order) {
      resultRequestData['isSortAsc'] = data.order === 'ASC';
    }
    const { pageSize, pageIndex } = data;
    const filterParam = new CreditBaseFilter();
    if (data.keyNo) {
      filterParam.keynos = [data.keyNo];
      // resultRequestData['searchKey'] = companyName;
    }
    //当前有效或历史
    // const validField = strategyModel.detailsParams?.find((q) => q.field === QueryParamsEnums.isValid);
    // if (validField?.fieldVal >= 0) {
    //   filterParam.isValid = validField.fieldVal + '';
    // }
    //统计周期
    if (strategyModel?.cycle > 0) {
      if (data.publishdate?.length > 0) {
        filterParam.publishdate = data.publishdate;
      } else {
        filterParam.publishdate = await this.getPublishDate(strategyModel?.cycle || 1);
      }
    }

    // const businessAbnormalType = strategyModel.detailsParams?.find((q) => q.field === QueryParamsEnums.businessAbnormalType);
    const associateObject = strategyModel.detailsParams?.find((q) => q.field === QueryParamsEnums.associateObject);
    switch (key) {
      case DimensionLevel3Enums.CancellationOfFiling: // 注销备案
        filterParam.creditType = [{ category: '24' }];
        filterParam.isValid = '1';
        break;
      case DimensionLevel3Enums.PersonCreditCurrent:
        filterParam.creditType = [{ category: '02' }];
        filterParam.isValid = '1';
        break;
      case DimensionLevel3Enums.PersonCreditHistory:
        filterParam.creditType = [{ category: '02' }];
        filterParam.isValid = '0';
        break;
      case DimensionLevel2Enums.Bankruptcy: {
        filterParam.creditType = [{ category: '01' }];
        const isValidField = strategyModel.detailsParams.find((f) => f.field === QueryParamsEnums.isValid);
        filterParam.isValid = getIsValidValue(isValidField.fieldVal);
        await this.enterpriseLibService.addRelatedKeyNos(filterParam.keynos, data.keyNo, associateObject, 'all');
        break;
      }
      case DimensionLevel2Enums.PersonExecution:
        filterParam.creditType = [{ category: '05' }];
        if (!filterParam.isValid) {
          filterParam.isValid = '0';
        }
        break;
      case DimensionLevel2Enums.ContractBreach:
        filterParam.creditType = [{ category: '38' }];
        break;
      case DimensionLevel2Enums.BondDefaults:
        filterParam.creditType = [{ category: '30' }];
        if (!filterParam.isValid) {
          filterParam.isValid = '0';
        }
        break;
      case DimensionLevel3Enums.ProductQualityProblem3: // 存在产品质量问题-假冒伪劣产品
        filterParam['cType'] = ['ZL06'];

        // 假冒伪劣产品用的是 currencedate
        if (filterParam.publishdate) {
          filterParam.currencedate = filterParam.publishdate;
          delete filterParam.publishdate;
        }
        break;
      case DimensionLevel3Enums.ProductQualityProblem4: // 存在产品质量问题-虚假宣传˝
        filterParam['cType'] = ['ZL07'];
        if (filterParam.publishdate) {
          filterParam.currencedate = filterParam.publishdate;
          delete filterParam.publishdate;
        }
        break;
      case DimensionLevel3Enums.ProductQualityProblem5: // 存在产品质量问题-其他质量问题
        filterParam['cType'] = ['ZL99'];
        break;
      case DimensionLevel3Enums.ProductQualityProblem6: // 存在产品质量问题-未准入境
        filterParam.creditType = [{ category: '22' }];
        break;
      case DimensionLevel3Enums.ProductQualityProblem7: // 存在产品质量问题-药品抽查【检验不合格】
        filterParam.creditType = [{ category: '33' }];
        break;
      case DimensionLevel3Enums.ProductQualityProblem9: // 存在产品质量问题-食品安全【检查不合格】
        filterParam.creditType = [{ category: '17' }];
        if (filterParam.publishdate) {
          filterParam.lianDate = filterParam.publishdate;
          delete filterParam.publishdate;
        }
        break;
      case DimensionLevel3Enums.MainMembersPersonCreditCurrent: {
        // 主要人员被列入失信被执行人（当前有效）
        filterParam.creditType = [{ category: '02' }];
        const isValidField = strategyModel.detailsParams.find((f) => f.field === QueryParamsEnums.isValid);
        filterParam.isValid = getIsValidValue(isValidField.fieldVal);
        delete resultRequestData['searchKey'];
        resultRequestData['isTable'] = true;
        filterParam.keynos = await this.enterpriseLibService.addRelatedKeyNosV2(data.keyNo, associateObject, 'all', key);
        if (filterParam.keynos?.length == 0) {
          return null;
        }
        break;
      }
      case DimensionLevel3Enums.MainMembersRestrictedOutbound: // 主要人员限制出境
        filterParam.creditType = [{ category: '04' }];
        filterParam.isValid = '1';
        delete resultRequestData['searchKey'];
        if (data.keyNo) {
          filterParam.keynos = await this.enterpriseLibService.addRelatedKeyNosV2(data.keyNo, associateObject, 'all', key);
          if (filterParam.keynos?.length == 0) {
            return null;
          }
        }
        break;
      case DimensionLevel3Enums.SubsidiaryPersonCreditCurrent:
        filterParam.creditType = [{ category: '02' }];
        filterParam.isValid = '0';
        if (data.keyNo) {
          const keyNos = await this.enterpriseLibService.getBranchKeyNos(data.keyNo);
          if (keyNos?.length > 0) {
            filterParam.keynos = keyNos;
          } else {
            return null;
          }
        }
        break;
      case DimensionLevel3Enums.SubsidiaryRestrictedConsumptionCurrent:
        filterParam.creditType = [{ category: '03' }];
        filterParam.isValid = '0';
        if (data.keyNo) {
          const keyNos = await this.enterpriseLibService.getBranchKeyNos(data.keyNo);
          if (keyNos?.length > 0) {
            filterParam.keynos = keyNos;
          } else {
            return null;
          }
        }
        break;
      case DimensionLevel3Enums.SubsidiaryRestrictedOutbound:
        filterParam.creditType = [{ category: '04' }];
        filterParam.isValid = '0';
        if (data.keyNo) {
          const keyNos = await this.enterpriseLibService.getBranchKeyNos(data.keyNo);
          if (keyNos?.length > 0) {
            filterParam.keynos = keyNos;
          } else {
            return null;
          }
        }
        break;
      default:
        break;
    }

    // TODO: 这段逻辑应该 放在 上面的Switch case 前面，防止case里面写的默认值被覆盖
    if (strategyModel?.detailsParams) {
      await Bluebird.map(strategyModel.detailsParams, async (queryPo) => {
        switch (queryPo.field) {
          // case QueryParamsEnums.cycle:
          //   break;
          case QueryParamsEnums.isValid:
            if (queryPo?.fieldVal >= 0) {
              filterParam.isValid = queryPo.fieldVal + '';
            } else {
              //不限的时候不传isValid参数
              delete filterParam.isValid;
            }
            break;
          case QueryParamsEnums.penaltiesAmount: //处罚金额
          case QueryParamsEnums.equityAmount: //股权数额
          case QueryParamsEnums.taxArrearsAmount: //欠税金额
          case QueryParamsEnums.registrationAmount: //注册金额
            if (data.amout) {
              filterParam.amount = data.amout;
            } else if (isNumber(queryPo?.fieldVal)) {
              //金额筛选条件 isNumber
              const fieldVal = Number(queryPo?.fieldVal);
              filterParam.amount = await this.getAmountConditions(queryPo.fieldOperator, fieldVal);
            }
            break;
          case QueryParamsEnums.penaltiesType: //处罚类型
            break;
          case QueryParamsEnums.executionTarget: //执行标的
            break;
          case QueryParamsEnums.capitalReduction: //资本降幅
            break;
          case QueryParamsEnums.changeThreshold: //变更阈值
            break;
          case QueryParamsEnums.duration: //成立时长
            break;
        }
      });
    }

    // switch (key) {
    //   case DimensionLevel2Enums.AdministrativePenalties:
    //     // 只取当前有效，历史数据不算
    //     filterParam.isValid = '1';
    //     break;
    //   default:
    //     break;
    // }
    resultRequestData['filter'] = filterParam;
    resultRequestData['pageSize'] = pageSize;
    resultRequestData['pageIndex'] = pageIndex;
    resultRequestData['isB'] = true;
    if (data.aggFields) {
      resultRequestData['aggFields'] = data.aggFields;
    }
    return resultRequestData;
  }

  private async getAmountConditions(fieldOperator: OperatorEnums, fieldVal: any) {
    switch (fieldOperator) {
      // case OperatorEnums.gt: //大于
      //   return [{ min: fieldVal }];
      case OperatorEnums.ge: //大于等于
        return [{ min: fieldVal }, { min: fieldVal, max: fieldVal }];
      // case OperatorEnums.eq: //等于
      //   return [{ min: fieldVal, max: fieldVal }];
      // case OperatorEnums.lt: //小于
      //   return [{ max: fieldVal }];
      case OperatorEnums.le: //小于等于
        return [{ max: fieldVal }, { min: fieldVal, max: fieldVal }];
      case OperatorEnums.contain: //包含
        break;
      case OperatorEnums.notContain: //不包含
        break;
      default:
        break;
    }
  }

  private async getPublishDate(num: number) {
    return [
      Object.assign(new DateRangeRelative(), {
        currently: true,
        flag: 1,
        number: num * 365,
        unit: 'day',
      }),
    ];
  }
}
