import { Test } from '@nestjs/testing';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { EntityManager, getManager } from 'typeorm';
import { BaseDimensions } from '../../../libs/constants/dimension.base.constants';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { IndicatorTypeEnums } from '../../../libs/model/settings/IndicatorTypeEnums';
import { AppTestModule } from '../../app/app.test.module';
import { CompanySearchModule } from '../../company/company-search.module';
import { DataModule } from '../data.module';
import { AnalyzeCreditService } from './analyze.credit.service';
import { CreditService } from './credit.service';
jest.setTimeout(300000);
describe('test analyze credit service', () => {
  let analyzeCreditService: AnalyzeCreditService;
  let creditService: CreditService;
  let entityManager: EntityManager;
  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppTestModule, DataModule, CompanySearchModule],
    }).compile();

    analyzeCreditService = module.get<AnalyzeCreditService>(AnalyzeCreditService);
    creditService = module.get<CreditService>(CreditService);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('DimensionLevel2Enums.OperationAbnormal test', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const company = '乐视网信息技术（北京）股份有限公司';
    const dimension = BaseDimensions[DimensionLevel2Enums.OperationAbnormal];
    const result = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName: company,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('被列入经营异常名录 BusinessAbnormal3', async () => {
    const companyId = '030f162fa99546ec9dad51e75ac14538';
    const companyName = '西安正太玉器珠宝有限公司';
    const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal3];
    const result = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(result?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('test analyze MainMembersPersonCreditCurrent', async () => {
    //6e1f1d7fc87edf5cb11967ac2c2dfbe8  四川同辉实业有限公司
    //02eca136b16504350746365fab632977  中安金控资产管理有限公司
    //3f71a9defd0dea42c8f24ff35f1586e1 北京华钜津桥联合商务咨询有限公司 公司和董监高同时都有限制高消费
    // 外部黑名单 安全生产领域失信生产经营单位 泉州市洛江区万安海盛特种气体经营部
    const companyId = '02eca136b16504350746365fab632977';
    const company = '中安金控资产管理有限公司';
    const dimension = BaseDimensions[DimensionLevel3Enums.MainMembersPersonCreditCurrent];
    const result = await analyzeCreditService.analyze(companyId, [dimension], {
      companyName: company,
      keyNo: companyId,
    });
    expect(result[0].key).toBe(DimensionLevel3Enums.MainMembersPersonCreditCurrent);
    expect(result[0].totalHits).toBeGreaterThan(0);
  });

  it('test credit analyze base test', async () => {
    //6e1f1d7fc87edf5cb11967ac2c2dfbe8  四川同辉实业有限公司
    //02eca136b16504350746365fab632977  中安金控资产管理有限公司
    const companyId = '02eca136b16504350746365fab632977';
    const company = '中安金控资产管理有限公司';
    const defines: DimensionDefinitionPO[] = Object.values(BaseDimensions);
    const creditDimensions = defines.filter((d) => d.source === DimensionSourceEnums.Credit);
    const result = await analyzeCreditService.analyze(companyId, creditDimensions, {
      companyName: company,
      keyNo: companyId,
    });
    expect(result.length).toBeGreaterThan(0);
  });

  it('OperationAbnormal test', async () => {
    const dimension: DimensionDefinitionPO = {
      key: DimensionLevel2Enums.OperationAbnormal,
      name: '历史被列入经营异常名录',
      strategyModel: {
        boost: 1.0,
        baseScore: 10,
        level: DimensionRiskLevelEnum.Medium,
        cycle: -1,
        sortField: { field: 'occurrencedate', order: 'DESC' },
        detailsParams: [
          {
            field: QueryParamsEnums.businessAbnormalType,
            fieldVal: [],
          },
        ],
      },
      isVirtualDimension: 0,
      source: DimensionSourceEnums.Credit,
      sourcePath: '/api/search/search-credit',
      status: 1,
      template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
      template2: '【#name#】',
      sort: 9,
      type: IndicatorTypeEnums.generalItems,
      description: '企业曾被市场监管部门列为经营异常名录',
    };
    const companyId = 'dec504c0df6c44e936d362f168a8cb6e';
    const company = '北京丹丹姐商贸有限公司';
    // const result = await analyzeCreditService.analyze(companyId, [dimension], { companyName: company });
    // expect(result?.[0].totalHits).toBeGreaterThanOrEqual(1);
    // dimension details
    // // console.log('------------------dimension detail ---------------------');
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    // // console.log(`details:${JSON.stringify(details)}`);
    expect(details.Result?.length).toBeGreaterThanOrEqual(1);
    expect(details.Result?.[0].name).toEqual(company);
  });

  it('FreezeEquity', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.FreezeEquity];
    const companyId = '39e3cbd13bcf3b526d1a5d204ace93ec';
    const company = '国美电器有限公司';
    const result = await analyzeCreditService.analyze(companyId, [dimension], {
      companyName: company,
      keyNo: companyId,
    });
    expect(result?.[0].totalHits).toBeGreaterThanOrEqual(1);
    // dimension details
    // // console.log('------------------dimension detail ---------------------');
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    // // console.log(`details:${JSON.stringify(details)}`);
    expect(details.Paging?.TotalRecords).toBeGreaterThanOrEqual(1);

    //查当前有效
    dimension.strategyModel.detailsParams.pop();
    dimension.strategyModel.detailsParams.push({
      field: QueryParamsEnums.isValid,
      fieldVal: '1',
    });
    const details2 = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    // // console.log(`details2:${JSON.stringify(details2)}`);
    expect(details2.Paging?.TotalRecords).toBeGreaterThanOrEqual(1);

    //查不限
    dimension.strategyModel.detailsParams.pop();
    dimension.strategyModel.detailsParams.push({
      field: QueryParamsEnums.isValid,
      fieldVal: '-1',
    });
    const details3 = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    // // console.log(`details3:${JSON.stringify(details3)}`);
    expect(details3.Paging?.TotalRecords).toBeGreaterThanOrEqual(details.Paging?.TotalRecords);
  });

  it('MainMembersRestrictedConsumptionCurrent', async () => {
    const dimension: DimensionDefinitionPO = BaseDimensions[DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent];
    const companyId = '2fc20223dd281981cdc486981d4ff1c2';
    const company = '上海隆琪机电设备有限公司';
    const result = await analyzeCreditService.analyze(companyId, [dimension], {
      companyName: company,
      keyNo: companyId,
    });
    expect(result?.[0].totalHits).toBeGreaterThanOrEqual(1);
    // dimension details
    // // console.log('------------------dimension detail ---------------------');
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    // // console.log(`details:${JSON.stringify(details)}`);
    expect(details.Paging?.TotalRecords).toBeGreaterThanOrEqual(1);

    //查当前有效
    dimension.strategyModel.detailsParams.pop();
    dimension.strategyModel.detailsParams.push({
      field: QueryParamsEnums.isValid,
      fieldVal: '1',
    });
    const details2 = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    // // console.log(`details2:${JSON.stringify(details2)}`);
    expect(details2.Paging?.TotalRecords).toBeGreaterThanOrEqual(1);

    //查不限
    dimension.strategyModel.detailsParams.pop();
    dimension.strategyModel.detailsParams.push({
      field: QueryParamsEnums.isValid,
      fieldVal: '-1',
    });
    const details3 = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    // // console.log(`details3:${JSON.stringify(details3)}`);
    expect(details3.Paging?.TotalRecords).toBeGreaterThanOrEqual(details.Paging?.TotalRecords);
  });

  it('MainMembersRestrictedConsumptionCurrent personkeynos empty', async () => {
    const dimension: DimensionDefinitionPO = BaseDimensions[DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent];
    const companyId = '4a6644aabd4d53ea27d6a027550a1ed6';
    const company = '北京沣昊科技有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details.Paging?.TotalRecords).toBeGreaterThanOrEqual(0);
  });

  it('getDimensionDetail DimensionLevel3Enums.MainMembersPersonCreditCurrent', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.MainMembersPersonCreditCurrent];
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: 'bc726c5785fa3e8fbfd9b81752755d58', //东阳市尚钧投资管理有限公司
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details.Paging?.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('ProductQualityProblem9', async () => {
    const dimension: DimensionDefinitionPO = BaseDimensions[DimensionLevel3Enums.ProductQualityProblem9];
    const companyId = '7a32505cb08af10632c993ece502f9da';
    const companyName = '安徽省小岗盼盼食品有限公司';
    // //使用信用大数据接口
    // const details = await creditService.getDimensionDetail(dimension, {
    //   keyNo: companyId,
    //   companyName,
    //   pageSize: 10,
    //   pageIndex: 1,
    // });
    //使用信用 ES
    delete dimension.strategyModel.cycle;
    const details2 = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details2.Paging.TotalRecords).toBeGreaterThanOrEqual(1);

    // const obj1 = details.Result[0];
    // const obj2 = details2.Result[0];
    // const keys1 = Object.keys(obj1);
    // const keys2 = Object.keys(obj2);
    // const allKeys = Array.from(new Set([...keys1, ...keys2]));
    // const diffKeys = allKeys.filter((key) => obj1[key] !== obj2[key]);
    // //列出差异字段
    // // // console.log(diffKeys);
  });

  it('DimensionLevel2Enums.AdministrativePenalties2', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.AdministrativePenalties2];
    const companyId = 'e95fbdbfcbf708615860a5df43ab10a3';
    const company = '浙江仙琚制药股份有限公司';
    // const result = await analyzeCreditService.analyze(companyId, [dimension], { companyName: company });
    // expect(result?.[0].totalHits).toBeGreaterThanOrEqual(1);
    // dimension details
    // // console.log('------------------dimension detail ---------------------');
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    // // console.log(`details:${JSON.stringify(details)}`);
    expect(details.Paging?.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('行政处罚（3年前涉及商业贿赂、垄断行为或政府采购活动违法行为)', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.AdministrativePenalties3];
    const companyId = '0d9f1df6df5a78c49b5b15f3adb20f94';
    const company = '重庆江都建材有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details.Paging?.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test detail for EnvironmentalPenalties', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.EnvironmentalPenalties];
    const companyId = 'a7d83441828a63d193b9643d533f1816';
    const company = '富利建设集团有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details.Paging?.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('环保处罚，设置处罚类型', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.EnvironmentalPenalties];
    const companyId = 'a7d83441828a63d193b9643d533f1816';
    const company = '富利建设集团有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details.Paging?.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('产品召回', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.ProductQualityProblem1];

    const companyId = '8f463e3e6c2d4a3b44e3f61641e331c8';
    const company = '贝克曼库尔特商贸（中国）有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    //产品召回默认近三年9条数据
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(9);
  });

  it('监管处罚', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.RegulateFinance];
    const companyId = '39e3cbd13bcf3b526d1a5d204ace93ec';
    const company = '国美电器有限公司';
    dimension.strategyModel.detailsParams.push({
      field: QueryParamsEnums.isValid,
      fieldVal: '1',
    });
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('税收违法', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.TaxationOffences];
    const companyId = 'f6cd38cc4196e58c98ad8c72396e384c';
    const companyName = '湛江市富晟石化有限公司';
    dimension.strategyModel.detailsParams = [
      {
        field: QueryParamsEnums.isValid,
        fieldVal: '1',
      },
    ];
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('有股权被冻结', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.FreezeEquity];
    const companyId = '594efa4b2b24b4b3672cd44278ae4627';
    const company = '浙江金牛文化产业有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('被列入限制高消费名单', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.RestrictedConsumptionCurrent];

    const companyId = '1090f61cde46ca4a896931146e4c98c4';
    const company = '恒大集团有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    expect(details.Result?.[0].PledgorInfo[0].Job).not.toBeUndefined();
  });

  // it('should getDetailFromEsForBid test', async () => {
  //   const dimension = Object.assign(new DimensionDefinitionPO(), {
  //     key: DimensionLevel2Enums.BidAdministrativePenalties,
  //     name: '涉围串标处罚',
  //     strategyModel: {
  //       boost: 1.0,
  //       baseScore: 5,
  //       level: DimensionRiskLevelEnum.Medium,
  //       cycle: 3,
  //       sortField: { field: 'punishdate', order: 'DESC' },
  //     },
  //     isVirtualDimension: 0,
  //     source: DimensionSourceEnums.Credit,
  //     sourcePath: '/api/search/search-credit',
  //     status: 1,
  //     template:
  //       '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
  //     template2: '#cycle#【#name#】 #count#条记录<span class="#isHidden#">，罚款总金额：#amountW#</span>',
  //     sort: 2,
  //     type: IndicatorTypeEnums.generalItems,
  //   });
  //   const requestData = Object.assign(
  //     {},
  //     {
  //       keyNos: ['a52582325f074a6d521047320a09d20f'],
  //       pageSize: 99,
  //       pageIndex: 1,
  //     },
  //   );
  //   const response = await supervisePunishService.getDimensionDetailForBid(dimension, requestData);
  //   // console.log(JSON.stringify(response));
  // });

  it('test DimensionLevel2Enums.Bankruptcy', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.Bankruptcy];
    const companyId = '44d026e7eb12ba1d49e2f090b1a12730';
    const company = '长沙新世界国际大饭店有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    const httpResponse = await creditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(httpResponse.Paging.TotalRecords).toEqual(details.Paging.TotalRecords);
    //查不限
    dimension.strategyModel.detailsParams.pop();
    dimension.strategyModel.detailsParams.push({
      field: QueryParamsEnums.isValid,
      fieldVal: '-1',
    });
    const details3 = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details3?.Paging.TotalRecords).toBeGreaterThanOrEqual(details.Paging.TotalRecords);
  });

  it('DimensionLevel3Enums.BusinessAbnormal3', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal3];
    const companyId = '0304353bb891e7f379bfc9efd28df1ae';
    const company = '广州市天河区沙东钟织服装店';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('DimensionLevel2Enums.OperationAbnormal', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.OperationAbnormal];
    const companyId = '37fc4eed1383edb39a18f5d2af91bb4e';
    const company = '海南澜博规划咨询有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toEqual(0);
  });

  it('DimensionLevel3Enums.RestrictedConsumptionHistory', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.RestrictedConsumptionHistory];
    const companyId = '1ab62d6a55b1428438ee1ee788e750c6';
    const company = '贵州红华物流有限公司';
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('存在产品质量问题-产品抽查不合格', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.ProductQualityProblem2];
    const companyId = 'b44f9b4e238a08c53788d52b0f464a83';
    const company = '湖北民泰药业有限责任公司';
    dimension.strategyModel.cycle = 1;
    dimension.strategyModel.detailsParams = [
      {
        field: QueryParamsEnums.isValid,
        fieldVal: '-1',
      },
    ];
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test detail for SpotCheck', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.SpotCheck];
    const companyId = '2d4e2e326a37a7757f001a6126f27a7f';
    const company = '广西建工集团建筑工程总承包有限公司';
    dimension.strategyModel.detailsParams.push({
      field: QueryParamsEnums.isValid,
      fieldVal: '1',
    });
    const details = await analyzeCreditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // it('DimensionLevel2Enums.BondDefaults', async () => {
  //   const dimension = BaseDimensions[DimensionLevel2Enums.BondDefaults];
  //   const companyId = '3ca1e9af5088420d08c4b0b01c863617';
  //   const details = await analyzeCreditService.analyze(companyId, [dimension], {
  //     keyNo: companyId,
  //     orgId: 208,
  //   });
  // });
});
