import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { BaseDimensions } from '../../../libs/constants/dimension.base.constants';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { AppTestModule } from '../../app/app.test.module';
import { DataModule } from '../data.module';
import { RiskChangeService } from './risk-change.service';

jest.setTimeout(60 * 1000);
describe('test risk change service', () => {
  let riskChangeService: RiskChangeService;
  let entityManager: EntityManager;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    riskChangeService = module.get<RiskChangeService>(RiskChangeService);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('近期变更大股东', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.MainInfoUpdateHolder];
    const param = new HitDetailsBaseQueryParams();
    param.pageSize = 10;
    param.pageIndex = 1;
    param.keyNo = 'aaa58c16dfbe0960995a6d430ba4dcbf';
    param.companyName = '常州宇开建筑劳务有限公司';
    const response = await riskChangeService.getDimensionDetail(dimension, param);
    expect(response.Result.length).toBeGreaterThanOrEqual(1);
  });

  it('近期变更实际控制人', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.MainInfoUpdatePerson];
    const param = new HitDetailsBaseQueryParams();
    param.pageSize = 10;
    param.pageIndex = 1;
    param.keyNo = '1c481abe582c4bbb50e1eb94fe3d4081';
    param.companyName = '安徽全鑫胜特种设备销售有限公司';
    const response = await riskChangeService.getDimensionDetail(dimension, param);
    expect(response.Result.length).toBeGreaterThanOrEqual(1);
  });

  it('近期变更受益所有人', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.MainInfoUpdateBeneficiary];
    const param = new HitDetailsBaseQueryParams();
    param.pageSize = 10;
    param.pageIndex = 1;
    param.keyNo = '1c481abe582c4bbb50e1eb94fe3d4081';
    param.companyName = '安徽全鑫胜特种设备销售有限公司';
    const response = await riskChangeService.getDimensionDetail(dimension, param);
    expect(response.Result.length).toBeGreaterThanOrEqual(1);
  });

  it('减资公告CapitalReduction', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.CapitalReduction];
    const param = new HitDetailsBaseQueryParams();
    param.pageSize = 10;
    param.pageIndex = 1;
    param.keyNo = 'e288fe59cc5709ef759c1745fc7ec5ac';
    param.companyName = '河南东鼎物流有限公司';
    const response = await riskChangeService.getDimensionDetail(dimension, param);
    expect(response.Result.length).toBeGreaterThanOrEqual(1);
  });

  it('近期变更注册资本', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.MainInfoUpdateRegisteredCapital];
    dimension.strategyModel.cycle = -1;
    const param = new HitDetailsBaseQueryParams();
    param.pageSize = 10;
    param.pageIndex = 1;
    param.keyNo = '9fab150cc5b9c6cc81eae626c7b771ad';
    param.companyName = '杭州五颜六色文化传媒有限公司';
    const response = await riskChangeService.getDimensionDetail(dimension, param);
    expect(response.Result.length).toBe(1);
  });

  it('test 公安通告 SecurityNotice', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.SecurityNotice];
    const param = Object.assign(new HitDetailsBaseQueryParams(), {
      pageSize: 10,
      pageIndex: 1,
      keyNo: 'a793f73d14fb20c1d3290335e3518091',
      companyName: '纵通（厦门）信息科技有限公司',
    });
    dimension.strategyModel.detailsParams.forEach((param) => {
      if (param.field == QueryParamsEnums.isValid) {
        param.fieldVal = '-1'; //查不限
      }
    });
    const response = await riskChangeService.getDimensionDetail(dimension, param);
    expect(response.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test 近期多起开庭公告 NoticeInTimePeriod', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.NoticeInTimePeriod];
    const param = Object.assign(new HitDetailsBaseQueryParams(), {
      pageSize: 10,
      pageIndex: 1,
      keyNo: '917720a16d08bdde171681983391aed0',
      companyName: '恒大地产集团有限公司',
    });
    dimension.strategyModel.detailsParams.forEach((param) => {
      if (param.field == QueryParamsEnums.isValid) {
        param.fieldVal = '-1'; //查不限
      }
    });
    const response = await riskChangeService.getDimensionDetail(dimension, param);
    expect(response.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test analyze', async () => {
    const dimensions = [];
    dimensions.push(BaseDimensions[DimensionLevel3Enums.NoticeInTimePeriod]);
    dimensions.push(BaseDimensions[DimensionLevel2Enums.SecurityNotice]);
    dimensions.push(BaseDimensions[DimensionLevel3Enums.CapitalReduction]);
    dimensions.push(BaseDimensions[DimensionLevel3Enums.MainInfoUpdateHolder]);
    const companyId = '917720a16d08bdde171681983391aed0';
    const response = await riskChangeService.analyze(companyId, dimensions);
    expect(response.length).toBeGreaterThanOrEqual(1);
  });
});
