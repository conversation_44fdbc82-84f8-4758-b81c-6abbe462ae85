import { MockBundleService, RoverBundleCounterType, RoverBundleLimitationType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { Test, TestingModule } from '@nestjs/testing';
import * as Bluebird from 'bluebird';
import { cloneDeep, isEqual, unionWith } from 'lodash';
import { EntityManager, getManager } from 'typeorm';
import { BaseDimensions } from '../../../libs/constants/dimension.base.constants';
import { getDefaultDimensionGroupDefinition } from '../../../libs/constants/dimension.constants';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { DimensionLevel1Enums } from '../../../libs/enums/diligence/DimensionLevel1Enums';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { ExcludeNodeTypeEnums } from '../../../libs/enums/diligence/ExcludeNodeTypeEnums';
import { TenderInnerBlackListResPo } from '../../../libs/model/bidding/model/TenderInnerBlackListResPo';
import { RoverUser } from '../../../libs/model/common';
import { GroupCompanyRelationsRequest } from '../../../libs/model/company/GroupCompanyRelationsRequest';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { ConditionValPO } from '../../../libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { RelatedPersonResponse } from '../../../libs/model/diligence/pojo/req&res/RelatedPersonResponse';
import { AppTestModule } from '../../app/app.test.module';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { DataModule } from '../data.module';
import { DimensionAnalyzeParamsPO } from '../DimensionAnalyzeParamsPO';
import { RoverGraphHelper } from '../helper/rover.graph.helper';
import { EnterpriseLibService } from './enterprise.lib.service';
import { RoverGraphService } from './rover.graph.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { RoverService } from './rover.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { InnerBlacklistEntity } from '../../../libs/entities/InnerBlacklistEntity';
import { CustomerEntity } from '../../../libs/entities/CustomerEntity';
import { SubDimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { BlacklistInvestigationsPo, DirectConnectionsPo } from '../../../libs/model/bidding/model/TenderInnerBlackListResPo';
import { CompanyDeepInvestBranchParamsPO, CompanyDeepRelationsParamsPO, PersonDeepRelationsParamsPO } from '../../data/DimensionAnalyzeParamsPO';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';

jest.setTimeout(30000 * 100);

describe('RoverGraphService 测试', () => {
  // 测试常量
  const groupsIds = [1254];
  const testCompanyIds = {
    qcc: 'f625a5b661058ba5082ca508f99ffe1b', // 企查查科技股份有限公司
    shangHaiQianMa: 'eec38b539ec6719d6b47407b408b5d5b', // 上海谦玛网络科技有限公司
    leShi: '9da51287f433eb974e5e4e764765582b', // 乐视控股（北京）有限公司
    shangHaiHangKong: '3566ee367ee90c80cf99d5cc71c6e259', // 上海航空国际旅游（集团）有限公司
    suZhouZhiBi: 'fc9ac2c207a8f102d0c1e6d372a501df', // 苏州知彼信息科技中心（有限合伙）
    xiaoMi: '9cce0780ab7644008b73bc2120479d31', // 小米科技有限责任公司
  };

  // 测试变量声明
  let enterpriseLibService: EnterpriseLibService;
  let graphService: RoverGraphService;
  let roverGraphHelper: RoverGraphHelper;
  let mockedBundleService: MockBundleService<RoverBundleCounterType, RoverBundleLimitationType>;
  let entityManager: EntityManager;
  // 测试用户
  const [testOrgId, testUserId] = generateUniqueTestIds('rover.graph.service.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  const defaultDimensionGroupDefinitionV1 = getDefaultDimensionGroupDefinition('v1');

  // 模拟依赖
  const mockConfigService = {
    roverGraphServer: {
      companyDeepInvestBranch: 'http://test-url/companyDeepInvestBranch',
      companyDeepRelations: 'http://test-url/companyDeepRelations',
      personRelations: 'http://test-url/personRelations',
      companyDeepRelatedParty: 'http://test-url/companyDeepRelatedParty',
      relations: 'http://test-url/relations',
    },
  };

  const mockRedisService = {
    getClient: jest.fn().mockReturnValue({}),
  };

  const mockHttpUtilsService = {
    postRequest: jest.fn().mockImplementation((url, data) => {
      return Promise.resolve({ success: true, data });
    }),
  };

  const mockRoverService = {};

  const mockRoverGraphHelper = {
    getCustomerInvestigation: jest.fn().mockResolvedValue([]),
    getBlacklistInvestigation: jest.fn().mockResolvedValue([]),
    getRoverRanges: jest.fn().mockResolvedValue({ groupIds: [], labelIds: [], depIds: [] }),
    getCustomerInvestigationCount: jest.fn().mockResolvedValue(0),
    getBlacklistInvestigationCount: jest.fn().mockResolvedValue(0),
    getCustomerInvestigationV2: jest.fn().mockResolvedValue([]),
    getBlacklistInvestigationV2: jest.fn().mockResolvedValue([]),
    searchCustomerWithAllConditions: jest.fn().mockResolvedValue([]),
    searchCustomerWithAnyCondition: jest.fn().mockResolvedValue([]),
    getBlacklistGroupLabelIds: jest.fn().mockResolvedValue({ groupIds: [], labelIds: [] }),
    searchBlackListWithAllConditions: jest.fn().mockResolvedValue([]),
    searchBlackListWithAnyCondition: jest.fn().mockResolvedValue([]),
    getTenderInnerBlacklist: jest.fn().mockResolvedValue({
      blacklistInvestigations: [],
      directConnection: [],
    }),
  };

  const mockInnerBlacklistRepo = {
    createQueryBuilder: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orWhere: jest.fn().mockReturnThis(),
      getRawMany: jest.fn().mockResolvedValue([]),
    }),
  };

  const mockCustomerRepo = {};

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
      providers: [
        { provide: ConfigService, useValue: mockConfigService },
        { provide: RedisService, useValue: mockRedisService },
        { provide: HttpUtilsService, useValue: mockHttpUtilsService },
        { provide: RoverService, useValue: mockRoverService },
        { provide: RoverGraphHelper, useValue: mockRoverGraphHelper },
        { provide: getRepositoryToken(InnerBlacklistEntity), useValue: mockInnerBlacklistRepo },
        { provide: getRepositoryToken(CustomerEntity), useValue: mockCustomerRepo },
      ],
    }).compile();

    enterpriseLibService = module.get<EnterpriseLibService>(EnterpriseLibService);
    graphService = module.get(RoverGraphService);
    roverGraphHelper = module.get(RoverGraphHelper);
    mockedBundleService = module.get(RoverBundleService);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  describe('单元测试-基础功能测试', () => {
    it('test graph', async () => {
      // Arrange
      const keyNo = testCompanyIds.shangHaiQianMa;

      // Act
      const v4 = await enterpriseLibService.getAllEmployeeList(keyNo);
      const v5 = await enterpriseLibService.getAllLegalPerson(keyNo);
      const personData = v4.concat(v5);
      const personList: RelatedPersonResponse[] = [];

      // Process data
      await Bluebird.map(personData, (p) => {
        const exist = personList.filter((pl) => pl.keyNo == p.keyNo);
        if (exist?.length) {
          // 处理 personData 中重复出现的数据， 合并 job和tag
          personList.concat(
            exist.map((tp) => {
              return Object.assign(tp, {
                job: unionWith(tp.job.split(','), p.job.split(','), isEqual).join(','),
                tags: unionWith(tp.tags, p.tags, isEqual),
              });
            }),
          );
        } else {
          personList.push({ ...p });
        }
      });

      // Assert
      expect(personList).toBeDefined();
    });

    it('should v3', async () => {
      // Arrange
      const keyNo = testCompanyIds.shangHaiQianMa;

      // Act
      const personData = await enterpriseLibService.getActualController(keyNo);

      // Assert
      expect(personData).toBeDefined();
    });
  });

  describe('集成测试-黑名单过滤测试', () => {
    it('filter blacklist', async () => {
      // Arrange
      const labelsIds = [];

      // Act
      const companyIds = await roverGraphHelper.searchBlackListWithAllConditions(208, [], []);
      const companyIds1 = await roverGraphHelper.searchBlackListWithAllConditions(208, groupsIds, labelsIds);
      const companyIds2 = await roverGraphHelper.searchBlackListWithAllConditions(208, [999], labelsIds);

      // Assert
      expect(companyIds.length).toBeGreaterThanOrEqual(companyIds1.length);
      expect(companyIds2.length).toBe(0);
    });

    it('filter customer', async () => {
      // Arrange
      const labelsIds = [];
      const depIds = [];

      // Act
      const companyIds = await roverGraphHelper.searchCustomerWithAllConditions(208, [3971], labelsIds, depIds);
      const companyIds2 = await roverGraphHelper.searchCustomerWithAllConditions(208, groupsIds, labelsIds, depIds);

      // Assert
      expect(companyIds.length).toBeGreaterThanOrEqual(0);
      expect(companyIds2.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('集成测试-命中黑名单测试', () => {
    it.skip('test hit blacklist', async () => {
      // Arrange
      const testCompanyId = testCompanyIds.qcc;
      const testDimension: DimensionDefinitionPO = BaseDimensions[DimensionLevel2Enums.BlacklistPartnerInvestigation];

      // 设置维度参数
      testDimension.strategyModel.detailsParams.forEach((d) => {
        if (d.field === QueryParamsEnums.dataRange) {
          d.fieldVal.forEach((f) => {
            if (f.key === DetailsParamEnums.Groups) {
              f.value = groupsIds;
            }
          });
        }
      });

      // Act
      const response = await graphService.getResultsV2(testUser.currentOrg, testUser.userId, testCompanyId, testDimension);

      // Assert
      expect(response.blacklist[DimensionLevel2Enums.BlacklistPartnerInvestigation]?.length).toBeGreaterThanOrEqual(0);
    });

    it('test blacklist detail', async () => {
      // Arrange
      const testCompanyId = testCompanyIds.qcc;
      const testDimension: DimensionDefinitionPO = BaseDimensions[DimensionLevel2Enums.BlacklistPartnerInvestigation];

      // Act
      const details = await graphService.getDimensionDetail(testDimension, {
        orgId: testUser.currentOrg,
        keyNo: testCompanyId,
        pageSize: 10,
        pageIndex: 1,
      });

      // Assert
      expect(details.Paging.TotalRecords).toBeGreaterThanOrEqual(0);
    });
  });

  describe('集成测试-命中客户测试', () => {
    it.skip('test hit customer', async () => {
      // Arrange
      const testCompanyId = testCompanyIds.leShi;
      const testDimension = BaseDimensions[DimensionLevel2Enums.CustomerPartnerInvestigation];

      // Act
      const response = await graphService.getResultsV2(testUser.currentOrg, testUser.userId, testCompanyId, testDimension);

      // Assert
      expect(response.partner[DimensionLevel2Enums.CustomerPartnerInvestigation]?.length).toBeGreaterThanOrEqual(0);
    });

    it.skip('test hit customer 相同受益人关系', async () => {
      // Arrange
      const testCompanyId = testCompanyIds.qcc;
      const testDimension = BaseDimensions[DimensionLevel2Enums.CustomerPartnerInvestigation];

      // Act
      const response = await graphService.getResultsV2(testUser.currentOrg, testUser.userId, testCompanyId, testDimension);

      // Assert
      expect(response.partner[DimensionLevel2Enums.CustomerPartnerInvestigation]?.length).toBeGreaterThanOrEqual(0);

      // 测试第三方中的关联公司 苏州知彼信息科技中心（有限合伙）
      const relatedCompanyId = testCompanyIds.suZhouZhiBi;
      expect(response.partner[DimensionLevel2Enums.CustomerPartnerInvestigation][0].companyKeynoRelated).toEqual(relatedCompanyId);
    });
  });

  describe('集成测试-分支机构测试', () => {
    it.skip('test blacklist branch', async () => {
      // Arrange
      const testCompanyId = testCompanyIds.shangHaiHangKong;
      const dimension = defaultDimensionGroupDefinitionV1[DimensionLevel1Enums.Risk_InnerBlacklist].items.find(
        (e) => e.key === DimensionLevel2Enums.BlacklistPartnerInvestigation,
      );

      // Act
      const response = await graphService.getResultsV2(testUser.currentOrg, testUser.userId, testCompanyId, dimension);

      // Assert
      expect(response.blacklist[DimensionLevel2Enums.BlacklistPartnerInvestigation]?.length).toBeGreaterThanOrEqual(0);
    });

    it.skip('test customer branch', async () => {
      // Arrange
      const user: RoverUser = getTestUser(1001652, 101347);
      const testCompanyId = testCompanyIds.suZhouZhiBi;
      const dimension = defaultDimensionGroupDefinitionV1[DimensionLevel1Enums.Risk_PartnerInvestigation].items.find(
        (e) => e.key === DimensionLevel2Enums.CustomerPartnerInvestigation,
      );

      // 设置深度参数
      dimension.strategyModel.detailsParams.forEach((d) => {
        if (d.field === QueryParamsEnums.depth) {
          d.fieldVal = 3;
        }
      });

      // Act
      const response = await graphService.getResultsV2(user.currentOrg, user.userId, testCompanyId, dimension);

      // Assert
      expect(response.partner[DimensionLevel2Enums.CustomerPartnerInvestigation]?.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('集成测试-公司关系测试', () => {
    it('test get companies relations', async () => {
      // Arrange
      const companyId1 = testCompanyIds.qcc;
      const companyName1 = '企查查科技股份有限公司';
      const companyId2 = testCompanyIds.suZhouZhiBi;
      const companyName2 = '苏州知彼信息科技中心（有限合伙）';

      const param = Object.assign(new GroupCompanyRelationsRequest(), {
        companyIds: [companyId1, companyId2],
        orgId: testUser.currentOrg,
        percentage: 0.01,
        depth: 2,
        types: ['Legal', 'Employ', 'Invest', 'HisLegal', 'HisEmploy', 'HisInvest', 'Branch', 'ActualController'],
        excludedTypes: Object.values(ExcludeNodeTypeEnums),
      });

      // Act
      const response = await graphService.getCompanyRelations(param.orgId, param);

      // Assert
      expect(response.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('集成测试-命中详情测试', () => {
    it('test detail HitInnerBlackList', async () => {
      // Arrange
      const companyId = testCompanyIds.qcc;
      const companyName = '企查查科技股份有限公司';
      const dimension = BaseDimensions[DimensionLevel2Enums.HitInnerBlackList];

      // Act
      const response = await graphService.getDimensionDetail(dimension, {
        orgId: 1001652,
        keyNo: companyId,
        pageSize: 10,
        pageIndex: 1,
      });

      // Assert - 不会命中，因为没有内部黑名单数据
      expect(response.Paging.TotalRecords).toBe(0);
    });

    it('test detail BlacklistPartnerInvestigation', async () => {
      // Arrange
      const companyId = testCompanyIds.qcc;
      const companyName = '企查查科技股份有限公司';
      const dimension = BaseDimensions[DimensionLevel2Enums.BlacklistPartnerInvestigation];

      // Act
      const response = await graphService.getDimensionDetail(dimension, {
        orgId: testUser.currentOrg,
        keyNo: companyId,
        pageSize: 10,
        pageIndex: 1,
      });

      // Assert - 不会命中，因为没有内部黑名单数据
      expect(response.Paging.TotalRecords).toBe(0);
    });
  });

  describe('集成测试-招标黑名单API测试', () => {
    it('test getTenderInnerBlacklist with real dependencies', async () => {
      // Arrange
      const companyId = testCompanyIds.qcc; // 企查查科技股份有限公司
      const orgId = testUser.currentOrg;
      const types = ['Invest', 'Employ', 'Legal']; // 常用的关系类型

      // 测试 depth 参数
      const detailsParams = [
        { field: QueryParamsEnums.depth, fieldVal: 2 },
        { field: QueryParamsEnums.excludedTypes, fieldVal: Object.values(ExcludeNodeTypeEnums) },
      ];

      // Act
      const response = await roverGraphHelper.getTenderInnerBlacklist(orgId, [companyId], types, detailsParams);

      // Assert
      expect(response).toBeDefined();
      expect(response).not.toBeNull();

      // 检查响应结构是否完整
      expect(response).toHaveProperty('directConnection');
      expect(response).toHaveProperty('blacklistInvestigations');
    });

    it('test getTenderInnerBlacklist when API returns null', async () => {
      // Arrange
      const companyId = testCompanyIds.qcc;
      const orgId = testUser.currentOrg;
      const types = ['Invest'];

      // Mock HTTP response to return null
      jest.spyOn(roverGraphHelper['httpUtilsService'], 'postRequest').mockResolvedValueOnce(null);

      // Mock searchInnerBlacklist to return a blacklist entity
      const mockBlacklist = {
        id: 12345,
        companyId: companyId,
        companyName: '企查查科技股份有限公司',
        duration: -1, // 永久
        joinDate: new Date(),
        reason: '测试原因',
      };

      jest.spyOn(roverGraphHelper, 'searchInnerBlacklist').mockResolvedValueOnce(mockBlacklist as any);

      // Act
      const response = await roverGraphHelper.getTenderInnerBlacklist(orgId, [companyId], types);

      // Assert
      expect(response).toBeDefined();
      expect(response).not.toBeNull();
      expect(response.directConnection).toHaveLength(1);
      expect(response.directConnection[0].blacklistId).toBe(mockBlacklist.id);
      expect(response.directConnection[0].companyKeynoDD).toBe(mockBlacklist.companyId);
      expect(response.directConnection[0].companyNameDD).toBe(mockBlacklist.companyName);
      expect(response.directConnection[0].duration).toBe(mockBlacklist.duration);
      expect(response.directConnection[0].joinDate).toBe(mockBlacklist.joinDate.getTime());
      expect(response.directConnection[0].reason).toBe(mockBlacklist.reason);

      // 还原模拟
      jest.restoreAllMocks();
    });

    it('test getTenderInnerBlacklist when API returns data but no direct connection', async () => {
      // Arrange
      const companyId = testCompanyIds.qcc;
      const orgId = testUser.currentOrg;
      const types = ['Invest'];

      // Mock HTTP response
      const mockApiResponse: TenderInnerBlackListResPo = {
        directConnection: [],
        blacklistInvestigations: [
          {
            companyKeynoRelated: companyId,
            companyNameRelated: '企查查科技股份有限公司',
            endCompanyKeyno: '',
            endCompanyName: '',
            entityId: 123,
            history: false,
            reason: '测试原因',
            relationPaths: [],
            relationTypes: ['Invest'],
            relations: [],
            startCompanyKeyno: companyId,
            startCompanyName: '企查查科技股份有限公司',
            steps: 1,
            version: 'V1',
          },
        ],
      };

      jest.spyOn(roverGraphHelper['httpUtilsService'], 'postRequest').mockResolvedValueOnce(mockApiResponse);

      // Mock searchInnerBlacklist to return a blacklist entity
      const mockBlacklist = {
        id: 12345,
        companyId: companyId,
        companyName: '企查查科技股份有限公司',
        duration: -1,
        joinDate: new Date(),
        reason: '测试原因',
      };

      jest.spyOn(roverGraphHelper, 'searchInnerBlacklist').mockResolvedValueOnce(mockBlacklist as any);

      // Act
      const response = await roverGraphHelper.getTenderInnerBlacklist(orgId, [companyId], types);

      // Assert
      expect(response).toBeDefined();
      expect(response.directConnection).toHaveLength(1);
      expect(response.directConnection[0].blacklistId).toBe(mockBlacklist.id);
      expect(response.blacklistInvestigations).toEqual(mockApiResponse.blacklistInvestigations);

      // 还原模拟
      jest.restoreAllMocks();
    });

    it('test getTenderInnerBlacklist when no blacklist found', async () => {
      // Arrange
      const companyId = testCompanyIds.qcc;
      const orgId = testUser.currentOrg;
      const types = ['Invest'];

      // Mock API response
      const mockApiResponse: TenderInnerBlackListResPo = {
        directConnection: [],
        blacklistInvestigations: [],
      };

      jest.spyOn(roverGraphHelper['httpUtilsService'], 'postRequest').mockResolvedValueOnce(mockApiResponse);

      // Mock searchInnerBlacklist to return null (no blacklist)
      jest.spyOn(roverGraphHelper, 'searchInnerBlacklist').mockResolvedValueOnce(null);

      // Act
      const response = await roverGraphHelper.getTenderInnerBlacklist(orgId, [companyId], types);

      // Assert
      expect(response).toBeDefined();
      expect(response.directConnection).toEqual([]);

      // 还原模拟
      jest.restoreAllMocks();
    });
  });

  describe('集成测试-结果V3测试', () => {
    it.skip('test getResultsV3 customer investigation', async () => {
      // Arrange
      const testCompanyId = testCompanyIds.qcc;
      const testDimension: DimensionDefinitionPO = cloneDeep(BaseDimensions[DimensionLevel2Enums.CustomerPartnerInvestigation]);

      // 设置深度参数
      testDimension.strategyModel.detailsParams.forEach((d) => {
        if (d.field === QueryParamsEnums.depth) {
          d.fieldVal = 3;
        }
      });

      // Act
      const response = await graphService.getResultsV3(testUser.currentOrg, testUser.userId, testCompanyId, testDimension);

      // Assert
      expect(response?.partner?.[DimensionLevel2Enums.CustomerPartnerInvestigation]?.length).toBeGreaterThanOrEqual(0);
      const sourceDataList = response.partner[DimensionLevel2Enums.CustomerPartnerInvestigation];
      expect(sourceDataList.length).toBeGreaterThanOrEqual(0);
    });

    it('test getResultsV2 blacklist depth = 3', async () => {
      // Arrange
      const testCompanyId = testCompanyIds.qcc;
      const dimension = BaseDimensions[DimensionLevel2Enums.BlacklistPartnerInvestigation];

      // 设置参数
      dimension.strategyModel.detailsParams.forEach((d) => {
        if (d.field === QueryParamsEnums.depth) {
          d.fieldVal = 3;
        } else if (d.field === QueryParamsEnums.types) {
          d.fieldVal.forEach((t) => {
            if (t.key === DetailsParamEnums.ShareholdingRatio) {
              t.value = 0.01;
            }
          });
        }
      });

      // Act
      const response = await graphService.getResultsV3(testUser.currentOrg, testUser.userId, testCompanyId, dimension);

      // Assert
      if (response.blacklist) {
        const sourceDataList = response.blacklist[DimensionLevel2Enums.BlacklistPartnerInvestigation];
        expect(sourceDataList.length).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('集成测试-分析功能测试', () => {
    it('test getResultsV3 analyze', async () => {
      // Arrange
      const testCompanyId = testCompanyIds.xiaoMi;
      const testDimension: DimensionDefinitionPO = BaseDimensions[DimensionLevel2Enums.CustomerPartnerInvestigation];
      const param = Object.assign(new DimensionAnalyzeParamsPO(), { orgId: testUser.currentOrg, userId: testUser.userId });

      // Act
      const response = await graphService.analyze(testCompanyId, [testDimension], param);

      // Assert
      expect(response).toBeDefined();
    });
  });

  describe('集成测试-可疑关系测试', () => {
    it('test getSuspectedRelation', async () => {
      // Arrange
      const dimension: DimensionDefinitionPO = BaseDimensions[DimensionLevel2Enums.CustomerSuspectedRelation];
      const param = Object.assign(new HitDetailsBaseQueryParams(), {
        keyNo: testCompanyIds.qcc,
        orgId: testUser.currentOrg,
        userId: testUser.userId,
        pageSize: 10,
        pageIndex: 1,
      });

      // Act
      const response = await graphService.getSuspectedRelationV2(dimension, param);

      // Assert
      expect(response).not.toBeNull();
    });
  });

  describe('单元测试-BundleService测试', () => {
    it('应能正确使用BundleCounter', async () => {
      // Arrange
      const customerCounter = await mockedBundleService.getBundleCounter(testUser, RoverBundleCounterType.ThirdPartyQuantity);

      // Act - 确保Bundle服务可用
      const increaseResult = await customerCounter.increase(1);

      // Assert
      expect(increaseResult).toBe(1);
    });
  });

  describe('单元测试-参数处理测试', () => {
    it('正确处理条件值参数', async () => {
      // Arrange
      const testDimension: DimensionDefinitionPO = cloneDeep(BaseDimensions[DimensionLevel2Enums.CustomerPartnerInvestigation]);
      const testTypes: ConditionValPO[] = [
        { key: DetailsParamEnums.Invest, value: null, status: 1 },
        { key: DetailsParamEnums.ShareholdingRatio, value: 0.05, status: 1 },
      ];

      // 设置参数
      testDimension.strategyModel.detailsParams.forEach((d) => {
        if (d.field === QueryParamsEnums.types) {
          d.fieldVal = testTypes;
        }
      });

      // 模拟getRangeCompanyIds方法
      jest.spyOn(graphService as any, 'getRangeCompanyIds').mockResolvedValue([testCompanyIds.suZhouZhiBi]);

      // 模拟roverGraphHelper.getCustomerInvestigation方法
      jest.spyOn(roverGraphHelper, 'getCustomerInvestigation').mockResolvedValue([
        {
          companyKeynoRelated: testCompanyIds.suZhouZhiBi,
          companyNameRelated: '苏州知彼信息科技中心（有限合伙）',
          startCompanyName: '企查查科技股份有限公司',
          startCompanyKeyno: testCompanyIds.qcc,
          relations: [],
          relationPaths: [],
          relationTypes: ['Invest'],
          version: 'V2',
        },
      ]);

      // Act
      const result = await graphService.getResultsV3(testUser.currentOrg, testUser.userId, testCompanyIds.qcc, testDimension);

      // Assert
      expect(result.partner).toBeDefined();
      expect(result.partner[DimensionLevel2Enums.CustomerPartnerInvestigation]).toBeDefined();
      expect(result.partner[DimensionLevel2Enums.CustomerPartnerInvestigation].length).toBeGreaterThan(0);

      // 还原模拟
      jest.restoreAllMocks();
    });
  });

  describe('单元测试-getResultsWithRangeCondition', () => {
    beforeEach(() => {
      // 重置所有 mock
      jest.spyOn(roverGraphHelper, 'getCustomerInvestigationV2').mockImplementation(() => Promise.resolve([]));
      jest.spyOn(roverGraphHelper, 'getBlacklistInvestigationV2').mockImplementation(() => Promise.resolve([]));
    });

    it('当维度类型无效时应返回空响应', async () => {
      // Arrange
      const dimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.CustomerPartnerInvestigation,
        strategyModel: {
          detailsParams: [
            {
              field: QueryParamsEnums.types,
              fieldVal: [], // 空的维度类型
            },
          ],
        },
      } as DimensionDefinitionPO;

      // Act
      const result = await graphService.getResultsWithRangeCondition(testUser.currentOrg, testUser.userId, testCompanyIds.qcc, dimension);

      // Assert
      expect(result.version).toBe('V2');
      expect(result.partner).toBeUndefined();
      expect(result.blacklist).toBeUndefined();
    });

    it('应正确处理客户伙伴调查维度', async () => {
      // Arrange
      const dimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.CustomerPartnerInvestigation,
        strategyModel: {
          detailsParams: [
            {
              field: QueryParamsEnums.types,
              fieldVal: [
                { key: DetailsParamEnums.Invest, value: null, status: 1 },
                { key: DetailsParamEnums.ShareholdingRatio, value: 0.05, status: 1 },
              ],
            },
            {
              field: QueryParamsEnums.depth,
              fieldVal: 2,
            },
            {
              field: QueryParamsEnums.dataRange,
              fieldVal: [{ key: DetailsParamEnums.Groups, value: groupsIds }],
            },
          ],
        },
      } as DimensionDefinitionPO;

      const mockResponse = [
        {
          companyKeynoRelated: testCompanyIds.suZhouZhiBi,
          companyNameRelated: '苏州知彼信息科技中心（有限合伙）',
          startCompanyName: '企查查科技股份有限公司',
          startCompanyKeyno: testCompanyIds.qcc,
          relations: [],
          relationPaths: [],
          relationTypes: ['Invest'],
          version: 'V2',
        },
      ];

      // Mock getRangeCondition
      jest.spyOn(graphService as any, 'getRangeCondition').mockResolvedValue({
        groupIds: groupsIds,
        labelIds: [],
        departmentIds: [],
      });

      jest.spyOn(roverGraphHelper, 'getCustomerInvestigationV2').mockResolvedValue(mockResponse);

      // Act
      const result = await graphService.getResultsWithRangeCondition(testUser.currentOrg, testUser.userId, testCompanyIds.qcc, dimension);

      // Assert
      expect(result.version).toBe('V2');
      expect(result.partner).toBeDefined();
      expect(result.partner[DimensionLevel2Enums.CustomerPartnerInvestigation]).toEqual(mockResponse);
      expect(roverGraphHelper.getCustomerInvestigationV2).toHaveBeenCalledWith(
        expect.objectContaining({
          orgId: testUser.currentOrg,
          companyId: testCompanyIds.qcc,
          depth: 2,
          percentage: 0.05,
          types: [DetailsParamEnums.Invest],
        }),
      );
    });

    it('应正确处理黑名单伙伴调查维度', async () => {
      // Arrange
      const dimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.BlacklistPartnerInvestigation,
        strategyModel: {
          detailsParams: [
            {
              field: QueryParamsEnums.types,
              fieldVal: [
                { key: DetailsParamEnums.Invest, value: null, status: 1 },
                { key: DetailsParamEnums.ShareholdingRatio, value: 0.05, status: 1 },
              ],
            },
            {
              field: QueryParamsEnums.depth,
              fieldVal: 2,
            },
            {
              field: QueryParamsEnums.dataRange,
              fieldVal: [{ key: DetailsParamEnums.Groups, value: groupsIds }],
            },
          ],
        },
      } as DimensionDefinitionPO;

      const mockResponse = [
        {
          companyKeynoRelated: testCompanyIds.suZhouZhiBi,
          companyNameRelated: '苏州知彼信息科技中心（有限合伙）',
          startCompanyName: '企查查科技股份有限公司',
          startCompanyKeyno: testCompanyIds.qcc,
          relations: [],
          relationPaths: [],
          relationTypes: ['Invest'],
          version: 'V2',
        },
      ];

      // Mock getRangeCondition
      jest.spyOn(graphService as any, 'getRangeCondition').mockResolvedValue({
        groupIds: groupsIds,
        labelIds: [],
        departmentIds: [],
      });

      jest.spyOn(roverGraphHelper, 'getBlacklistInvestigationV2').mockResolvedValue(mockResponse);

      // Act
      const result = await graphService.getResultsWithRangeCondition(testUser.currentOrg, testUser.userId, testCompanyIds.qcc, dimension);

      // Assert
      expect(result.version).toBe('V2');
      expect(result.blacklist).toBeDefined();
      expect(result.blacklist[DimensionLevel2Enums.BlacklistPartnerInvestigation]).toEqual(mockResponse);
      expect(roverGraphHelper.getBlacklistInvestigationV2).toHaveBeenCalledWith(
        expect.objectContaining({
          orgId: testUser.currentOrg,
          companyId: testCompanyIds.qcc,
          depth: 2,
          percentage: 0.05,
          types: [DetailsParamEnums.Invest],
        }),
      );
    });

    it('当没有范围条件时应返回空响应', async () => {
      // Arrange
      const dimension: DimensionDefinitionPO = {
        key: DimensionLevel2Enums.CustomerPartnerInvestigation,
        strategyModel: {
          detailsParams: [
            {
              field: QueryParamsEnums.types,
              fieldVal: [{ key: DetailsParamEnums.Invest, value: null, status: 1 }],
            },
          ],
        },
      } as DimensionDefinitionPO;

      // Mock getRangeCondition to return empty condition
      jest.spyOn(graphService as any, 'getRangeCondition').mockResolvedValue({
        groupIds: [],
        labelIds: [],
        departmentIds: [],
      });

      const customerInvestigationSpy = jest.spyOn(roverGraphHelper, 'getCustomerInvestigationV2');
      const blacklistInvestigationSpy = jest.spyOn(roverGraphHelper, 'getBlacklistInvestigationV2');

      // Act
      const result = await graphService.getResultsWithRangeCondition(testUser.currentOrg, testUser.userId, testCompanyIds.qcc, dimension);

      // Assert
      expect(result.version).toBe('V2');
      expect(result.partner).toBeUndefined();
      expect(result.blacklist).toBeUndefined();
      expect(customerInvestigationSpy).not.toHaveBeenCalled();
      expect(blacklistInvestigationSpy).not.toHaveBeenCalled();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });
  });

  describe('getBlacklistRealTypes', () => {
    it('应该正确转换维度到实际类型', () => {
      // 创建测试维度数据
      const dimensions: SubDimensionDefinitionPO[] = [
        {
          key: DimensionLevel2Enums.HitInnerBlackList,
          name: '内部黑名单',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        {
          key: DimensionLevel2Enums.ForeignInvestment,
          name: '对外投资',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: 0,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        {
          key: DimensionLevel2Enums.Shareholder,
          name: '参股股东',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: 1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        {
          key: DimensionLevel2Enums.EmploymentRelationship,
          name: '董监高/法人关联',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        {
          key: DimensionLevel2Enums.BlacklistSameSuspectedActualController,
          name: '疑似同一实际控制人',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        {
          key: DimensionLevel2Enums.BlacklistSameFinalBenefit,
          name: '同一受益所有人',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        {
          key: DimensionLevel2Enums.CompanyBranch,
          name: '分支机构',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        {
          key: DimensionLevel2Enums.Hold,
          name: '控制关系',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
      ];

      // 使用私有方法测试
      const types = (graphService as any).getBlacklistRealTypes(dimensions);

      // 验证结果
      expect(types).toContain(DimensionLevel2Enums.HitInnerBlackList);
      expect(types).toContain(DimensionLevel2Enums.Invest);
      expect(types).toContain(DimensionLevel2Enums.HisShareholder);
      expect(types).toContain(DimensionLevel2Enums.Employ);
      expect(types).toContain(DimensionLevel2Enums.HisEmploy);
      expect(types).toContain(DimensionLevel2Enums.ActualController);
      expect(types).toContain(DetailsParamEnums.FinalBenefit);
      expect(types).toContain(DimensionLevel2Enums.Branch);
      expect(types).toContain(DimensionLevel2Enums.Hold);
    });

    it('应该处理不同的 validValue 值', () => {
      // 测试 ForeignInvestment 维度的不同 validValue 值
      const dimensions: SubDimensionDefinitionPO[] = [
        // validValue = 0 应该返回 Invest
        {
          key: DimensionLevel2Enums.ForeignInvestment,
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: 0,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        // validValue = 1 应该返回 HisInvest
        {
          key: DimensionLevel2Enums.ForeignInvestment,
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: 1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        // validValue = -1 应该返回 Invest 和 HisInvest
        {
          key: DimensionLevel2Enums.ForeignInvestment,
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
      ];

      const types = (graphService as any).getBlacklistRealTypes(dimensions);

      // 验证结果
      expect(types).toContain(DimensionLevel2Enums.Invest);
      expect(types).toContain(DimensionLevel2Enums.HisInvest);
    });
  });

  describe('getResultByPage', () => {
    it('应该返回 blacklistInvestigations 分页数据', () => {
      // 创建测试响应数据
      const blacklist1 = new BlacklistInvestigationsPo();
      blacklist1.startCompanyName = '测试1';
      blacklist1.entityId = 1;
      blacklist1.relationPaths = [[]];
      blacklist1.companyKeynoRelated = 'key1';
      blacklist1.companyNameRelated = '相关公司1';

      const blacklist2 = new BlacklistInvestigationsPo();
      blacklist2.startCompanyName = '测试2';
      blacklist2.entityId = 2;
      blacklist2.relationPaths = [[]];
      blacklist2.companyKeynoRelated = 'key2';
      blacklist2.companyNameRelated = '相关公司2';

      const blacklist3 = new BlacklistInvestigationsPo();
      blacklist3.startCompanyName = '测试3';
      blacklist3.entityId = 3;
      blacklist3.relationPaths = [[]];
      blacklist3.companyKeynoRelated = 'key3';
      blacklist3.companyNameRelated = '相关公司3';

      const response: TenderInnerBlackListResPo = {
        blacklistInvestigations: [blacklist1, blacklist2, blacklist3],
        directConnection: [],
      };

      // 使用私有方法测试
      const result = (graphService as any).getResultByPage(response, 'blacklistInvestigations', 1, 2);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.key).toBe(DimensionLevel2Enums.BlackListInvestigations);
      expect(result.name).toBe('内部黑名单关联关系');
      expect(result.totalHits).toBe(3);
    });

    it('应该返回 directConnection 分页数据', () => {
      // 创建测试响应数据
      const direct1 = new DirectConnectionsPo();
      direct1.companyNameDD = '直接关联1';
      direct1.blacklistId = 1;

      const direct2 = new DirectConnectionsPo();
      direct2.companyNameDD = '直接关联2';
      direct2.blacklistId = 2;

      const response: TenderInnerBlackListResPo = {
        blacklistInvestigations: [],
        directConnection: [direct1, direct2],
      };

      // 使用私有方法测试
      const result = (graphService as any).getResultByPage(response, 'directConnection', 1, 2);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.key).toBe(DimensionLevel2Enums.DirectConnection);
      expect(result.name).toBe('被列入内部黑名单');
      expect(result.totalHits).toBe(2);
    });

    it('应该在数据为空时返回 null', () => {
      // 创建测试响应数据
      const response: TenderInnerBlackListResPo = {
        blacklistInvestigations: [],
        directConnection: [],
      };

      // 使用私有方法测试
      const result1 = (graphService as any).getResultByPage(response, 'blacklistInvestigations', 1, 2);
      const result2 = (graphService as any).getResultByPage(response, 'directConnection', 1, 2);

      // 验证结果
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });

    it('应该对未知类型返回 null', () => {
      // 创建测试响应数据
      const blacklist = new BlacklistInvestigationsPo();
      blacklist.entityId = 1;
      blacklist.relationPaths = [[]];

      const direct = new DirectConnectionsPo();
      direct.blacklistId = 2;

      const response: TenderInnerBlackListResPo = {
        blacklistInvestigations: [blacklist],
        directConnection: [direct],
      };

      // 使用私有方法测试
      const result = (graphService as any).getResultByPage(response, 'unknownType', 1, 2);

      // 验证结果
      expect(result).toBeNull();
    });
  });

  describe('parseRelations', () => {
    it('应该正确解析地址关系', () => {
      // 创建测试记录数据
      const record = {
        startCompanyName: '测试公司A',
        startCompanyKeyno: 'keyA',
        endCompanyName: '测试公司B',
        endCompanyKeyno: 'keyB',
        relationPaths: [
          [
            { id: 'keyA', name: '测试公司A', type: 'node' },
            {
              type: 'HasAddress',
              startid: 'keyA',
              endid: 'address1',
              register_date: 1625097600000,
            },
            {
              'Address.name': '北京市海淀区',
              id: 'address1',
              type: 'node',
            },
            {
              type: 'HasAddress',
              startid: 'keyB',
              endid: 'address1',
              register_date: 1625097600000,
            },
            { id: 'keyB', name: '测试公司B', type: 'node' },
          ],
        ],
      };

      // 使用私有方法测试
      const result = (graphService as any).parseRelations(record);

      // 验证结果
      expect(result).toBeDefined();
      expect(result[1].data).toBeDefined();
      expect(result[1].data[0].type).toBe('Address');
      expect(result[1].data[0].role).toBe('相同经营地址');
    });

    it('应该正确解析邮箱关系', () => {
      // 创建测试记录数据
      const record = {
        startCompanyName: '测试公司A',
        startCompanyKeyno: 'keyA',
        endCompanyName: '测试公司B',
        endCompanyKeyno: 'keyB',
        relationPaths: [
          [
            { id: 'keyA', name: '测试公司A', type: 'node' },
            {
              type: 'HasEmail',
              startid: 'keyA',
              endid: 'email1',
            },
            {
              'Email.name': '<EMAIL>',
              id: 'email1',
              type: 'node',
            },
            {
              type: 'HasEmail',
              startid: 'keyB',
              endid: 'email1',
            },
            { id: 'keyB', name: '测试公司B', type: 'node' },
          ],
        ],
      };

      // 使用私有方法测试
      const result = (graphService as any).parseRelations(record);

      // 验证结果
      expect(result).toBeDefined();
      expect(result[1].data).toBeDefined();
      expect(result[1].data[0].type).toBe('Mail');
      expect(result[1].data[0].role).toBe('相同邮箱');
      expect(result[1].data[0].data).toContain('<EMAIL>');
    });

    it('应该正确解析电话关系', () => {
      // 创建测试记录数据
      const record = {
        startCompanyName: '测试公司A',
        startCompanyKeyno: 'keyA',
        endCompanyName: '测试公司B',
        endCompanyKeyno: 'keyB',
        relationPaths: [
          [
            { id: 'keyA', name: '测试公司A', type: 'node' },
            {
              type: 'HasPhone',
              startid: 'keyA',
              endid: 'phone1',
              source: '工商',
            },
            {
              'Phone.name': '13800138000|13900139000',
              'Phone.source': '工商|官网',
              id: 'phone1',
              type: 'node',
            },
            {
              type: 'HasPhone',
              startid: 'keyB',
              endid: 'phone1',
              source: '官网',
            },
            { id: 'keyB', name: '测试公司B', type: 'node' },
          ],
        ],
      };

      // 使用私有方法测试
      const result = (graphService as any).parseRelations(record);

      // 验证结果
      expect(result).toBeDefined();
      expect(result[1].data).toBeDefined();
      expect(result[1].data[0].type).toBe('ContactNumber');
      expect(result[1].data[0].role).toBe('相同电话号码');
      expect(result[1].data[0].data.length).toBe(4); // 两个电话号码，每个公司各一个
    });

    it('应该正确解析担保关系', () => {
      // 创建测试记录数据
      const record = {
        startCompanyName: '测试公司A',
        startCompanyKeyno: 'keyA',
        endCompanyName: '测试公司B',
        endCompanyKeyno: 'keyB',
        relationPaths: [
          [
            { id: 'keyA', name: '测试公司A', type: 'node' },
            {
              type: 'Guarantee',
              startid: 'keyA',
              endid: 'keyB',
              direction: 'right',
            },
            { id: 'keyB', name: '测试公司B', type: 'node' },
          ],
        ],
      };

      // 使用私有方法测试
      const result = (graphService as any).parseRelations(record);

      // 验证结果
      expect(result).toBeDefined();
      expect(result[1].type).toBe('Guarantor');
      expect(result[1].role).toBe('相互担保关联');
      expect(result[1].showDetail).toBe(true);
    });

    it('应该正确解析股权出质关系', () => {
      // 创建测试记录数据
      const record = {
        startCompanyName: '测试公司A',
        startCompanyKeyno: 'keyA',
        endCompanyName: '测试公司B',
        endCompanyKeyno: 'keyB',
        relationPaths: [
          [
            { id: 'keyA', name: '测试公司A', type: 'node' },
            {
              type: 'Pledge',
              startid: 'keyA',
              endid: 'keyB',
              direction: 'right',
            },
            { id: 'keyB', name: '测试公司B', type: 'node' },
          ],
        ],
      };

      // 使用私有方法测试
      const result = (graphService as any).parseRelations(record);

      // 验证结果
      expect(result).toBeDefined();
      expect(result[1].type).toBe('EquityPledge');
      expect(result[1].role).toBe('股权出质关联');
      expect(result[1].showDetail).toBe(true);
    });
  });

  describe('getSimplePageData', () => {
    it('应该返回正确的分页数据', () => {
      const testArray = [
        { id: 1, name: '测试1' },
        { id: 2, name: '测试2' },
        { id: 3, name: '测试3' },
        { id: 4, name: '测试4' },
        { id: 5, name: '测试5' },
      ];

      // 使用私有方法测试
      const result = (graphService as any).getSimplePageData(testArray, 2, 2);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result.length).toBe(2);
      expect(result.Result[0].id).toBe(3);
      expect(result.Result[1].id).toBe(4);
      expect(result.Paging.TotalRecords).toBe(5);
      expect(result.Paging.PageIndex).toBe(2);
      expect(result.Paging.PageSize).toBe(2);
    });

    it('应该处理空数组', () => {
      const testArray = [];

      // 使用私有方法测试
      const result = (graphService as any).getSimplePageData(testArray, 1, 10);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result.length).toBe(0);
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该处理超出范围的页码', () => {
      const testArray = [
        { id: 1, name: '测试1' },
        { id: 2, name: '测试2' },
      ];

      // 使用私有方法测试
      const result = (graphService as any).getSimplePageData(testArray, 3, 1);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result.length).toBe(0);
      expect(result.Paging.TotalRecords).toBe(2);
      expect(result.Paging.PageIndex).toBe(3);
    });
  });

  // 测试公共方法
  describe('公共方法测试', () => {
    it('getCompanyDeepInvestBranch 应该调用 httpUtilsService.postRequest', async () => {
      const params: CompanyDeepInvestBranchParamsPO = {
        startCompanyId: 'test123',
        percentage: 0.1,
        depth: 2,
      };
      await graphService.getCompanyDeepInvestBranch(params);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.companyDeepInvestBranch, params);
    });

    it('getCompanyDeepRelations 应该调用 httpUtilsService.postRequest', async () => {
      const params: CompanyDeepRelationsParamsPO = {
        startCompanyId: 'test123',
        endCompanyIds: ['test456'],
        orgId: 123,
        percentage: 0.1,
        types: ['type1', 'type2'],
      };
      await graphService.getCompanyDeepRelations(params);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.companyDeepRelations, params);
    });

    it('getPersonDeepRelations 应该调用 httpUtilsService.postRequest', async () => {
      const params: PersonDeepRelationsParamsPO = {
        startId: 'p123',
        endCompanyIds: ['test456'],
        depth: 2,
        percentage: 0.1,
        types: ['type1', 'type2'],
      };
      await graphService.getPersonDeepRelations(params);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.personRelations, params);
    });

    it('getChildCompanyList 应该调用 httpUtilsService.postRequest 并传递正确参数', async () => {
      await graphService.getChildCompanyList('test123', 2, 0.1);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.companyDeepRelatedParty, {
        depth: 2,
        percentage: 0.1,
        relationType: 'MajorityInvestment',
        startCompanyId: 'test123',
      });
    });

    it('getParentCompanyList 应该调用 httpUtilsService.postRequest 并传递正确参数', async () => {
      await graphService.getParentCompanyList('test123', 2, 0.1);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.companyDeepRelatedParty, {
        depth: 2,
        percentage: 0.1,
        relationType: 'MotherCompanyMajorityShareholder',
        startCompanyId: 'test123',
      });
    });
  });
});
