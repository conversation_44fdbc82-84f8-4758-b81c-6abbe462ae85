import { Test } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { BaseDimensions } from '../../../libs/constants/dimension.base.constants';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { AppTestModule } from '../../app/app.test.module';
import { DataModule } from '../data.module';
import { CreditService } from './credit.service';

jest.setTimeout(300000);
describe('test http credit service(信用大数据接口)', () => {
  let creditService: CreditService;
  let entityManager: EntityManager;
  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    creditService = module.get<CreditService>(CreditService);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('test analyze BusinessAbnormal3', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const company = '乐视网信息技术（北京）股份有限公司';
    const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal3];
    const result = await creditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName: company,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test analyze DimensionLevel2Enums.OperationAbnormal', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const company = '乐视网信息技术（北京）股份有限公司';
    const dimension = BaseDimensions[DimensionLevel2Enums.OperationAbnormal];
    const result = await creditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName: company,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('破产重整', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.Bankruptcy];
    const companyId = '39e3cbd13bcf3b526d1a5d204ace93ec';
    const company = '国美电器有限公司';
    const httpResponse = await creditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(httpResponse.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    //查不限
    dimension.strategyModel.detailsParams.pop();
    dimension.strategyModel.detailsParams.push({
      field: QueryParamsEnums.isValid,
      fieldVal: '-1',
    });
    const details3 = await creditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(details3?.Paging.TotalRecords).toBeGreaterThanOrEqual(httpResponse.Paging.TotalRecords);
  });

  it('test analyze MainMembersPersonCreditCurrent', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const company = '乐视网信息技术（北京）股份有限公司';
    const dimension = BaseDimensions[DimensionLevel3Enums.MainMembersPersonCreditCurrent];
    const result = await creditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test analyze MainMembersRestrictedOutbound', async () => {
    const companyId = '7997e0f5948173d20a207fc934a9514e';
    const company = '上海大泉建筑安装有限公司';
    const dimension = BaseDimensions[DimensionLevel3Enums.MainMembersRestrictedOutbound];
    const result = await creditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    // console.log(JSON.stringify(result));
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test detail ProductQualityProblem6', async () => {
    const companyId = '6583579d4d007d7daab586c94d592e93';
    const company = '优合集团有限公司';
    const dimension = BaseDimensions[DimensionLevel3Enums.ProductQualityProblem6];
    const result = await creditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test detail ProductQualityProblem7', async () => {
    const companyId = '74d5755499819fde97110df41f154967';
    const company = '江西和硕药业有限公司';
    const dimension = BaseDimensions[DimensionLevel3Enums.ProductQualityProblem7];
    const result = await creditService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
});
