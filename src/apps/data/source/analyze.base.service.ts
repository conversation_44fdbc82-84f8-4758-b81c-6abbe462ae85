import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionScorePO } from '../../../libs/model/diligence/pojo/dimension/DimensionScorePO';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/pojo/req&res/details/response';
import { Client, RequestParams } from '@elastic/elasticsearch';
import { find } from 'lodash';
import { DimensionLevelClass } from '../../../libs/constants/dimension.constants';
import { AggBucketItemPO } from '../../../libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import { processScorePO } from '../../../libs/utils/diligence/diligence.utils';
import * as Bluebird from 'bluebird';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { DimensionAnalyzeParamsPO } from '../DimensionAnalyzeParamsPO';

export interface AnalyzeService<T extends HitDetailsBaseQueryParams, A extends DimensionAnalyzeParamsPO, R extends HitDetailsBaseResponse> {
  analyze(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], params?: A): Promise<DimensionScorePO[]>;

  getDimensionDetail(dimension: DimensionDefinitionPO, params: T, analyzeParams?: A): Promise<R>;
}

export abstract class AbstractEsAnalyzeService implements AnalyzeService<HitDetailsBaseQueryParams, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  protected readonly logger: Logger;
  protected bucketNamePrefix = 'sub_agg_';

  protected constructor(private readonly name: string, private readonly esClient: Client, private readonly indexName: string) {
    this.logger = QccLogger.getLogger(name);
  }

  async analyze(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], params?: DimensionAnalyzeParamsPO): Promise<DimensionScorePO[]> {
    try {
      if (!companyId || !dimensionDefinitionPOs?.length) {
        return [];
      }

      const query = await this.getQuery(companyId, dimensionDefinitionPOs, params);
      const aggs = await this.createAggs(companyId, dimensionDefinitionPOs, params);
      const queryBody = {
        query,
        aggs,
        size: 0,
      };
      const response = await this.searchEs(queryBody, companyId);
      // if (this.indexName == 'rover_credit_check_query') {
      //   this.logger.info(`search credit es companyId ${companyId} query: ${JSON.stringify(query)}`);
      //   this.logger.info(`search credit es companyId ${companyId} aggs: ${JSON.stringify(aggs)}`);
      //   this.logger.info(`search credit es companyId ${companyId} response: ${JSON.stringify(response)}`);
      // }
      const bucketData = this.processAggs(response?.body?.aggregations, dimensionDefinitionPOs);

      return this.processBucketData(bucketData, dimensionDefinitionPOs);
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }

  async getDimensionDetail(
    dimension: DimensionDefinitionPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo, pageIndex, pageSize } = params;
    try {
      const { total, data } = await this.getDetailFromEs(keyNo, dimension, params, analyzeParams);
      return Object.assign(new HitDetailsBaseResponse(), {
        Paging: {
          PageSize: pageSize,
          PageIndex: pageIndex,
          TotalRecords: total,
        },
        Result: data,
      });
    } catch (error) {
      this.logger.error('getDimensionDetail error!', error);
    }
    return new HitDetailsBaseResponse();
  }

  protected async getQuery(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], params?: DimensionAnalyzeParamsPO): Promise<object> {
    const query = {
      bool: {
        should: [],
      },
    };
    await Bluebird.map(dimensionDefinitionPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po, params);
      if (dimQuery) {
        query.bool.should.push(dimQuery);
      }
    });
    if (query.bool.should.length > 0) {
      query.bool['minimum_should_match'] = 1;
    }
    return query;
  }

  protected async createAggs(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], params?: DimensionAnalyzeParamsPO) {
    const aggs: any = {};
    await Bluebird?.map(dimensionDefinitionPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po, params);
      const aggsName = `${this.bucketNamePrefix}${po.key}`;
      if (dimQuery) {
        aggs[aggsName] = {
          filter: dimQuery,
          aggs: {},
        };
      }
    });
    return aggs;
  }

  protected processAggs(aggObj: any, dimensionDefinitionPOs?: DimensionDefinitionPO[]): AggBucketItemPO[] {
    const bucketData: AggBucketItemPO[] = [];

    Object.keys(aggObj).forEach((bucketName) => {
      // const bucket = aggObj[bucketName];
      const dimensionType = bucketName.replace(this.bucketNamePrefix, '') as DimensionTypeEnums;
      const bucket = aggObj[bucketName];

      const hitCount = bucket['doc_count'];
      if (hitCount > 0) {
        const res: AggBucketItemPO = {
          dimensionType,
          hitCount,
        };
        bucketData.push(res);
      }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: AggBucketItemPO[], dimensionDefinitionPOs: DimensionDefinitionPO[]): DimensionScorePO[] {
    return bucketData
      .map((item) => {
        const d: DimensionDefinitionPO = find(dimensionDefinitionPOs, { key: item.dimensionType });
        const desData = {
          level: DimensionLevelClass[d.strategyModel.level || 0],
          cycle: d.strategyModel?.cycle > 0 ? d.strategyModel.cycle : 0,
          name: d.name,
          isHidden: '',
          isHiddenY: '',
        };
        const { hitCount } = item as AggBucketItemPO;
        return processScorePO(d, hitCount, desData);
      })
      .filter((t) => t);
  }

  protected async getDetailFromEs(
    companyId: string,
    dimension: DimensionDefinitionPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ) {
    const { pageIndex, pageSize } = params;

    const query = await this.getDimensionQuery(companyId, dimension, params, analyzeParams);
    const sort = {};
    if (params?.field) {
      sort[params.field] = params?.order || 'DESC';
    } else if (dimension?.strategyModel?.sortField?.field) {
      sort[dimension?.strategyModel?.sortField.field] = dimension?.strategyModel?.sortField.order;
    }

    const response = await this.searchEs(
      {
        from: (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize,
        size: pageSize || 10,
        sort,
        query,
      },
      companyId,
    );
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }

  protected searchEs(body, preference: string) {
    const searchRequest: RequestParams.Search = {
      index: this.indexName,
      type: '_doc',
      body,
      preference,
    };
    return this.esClient.search(searchRequest);
  }

  protected abstract getDimensionQuery(
    companyId: string,
    dimension: DimensionDefinitionPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object>;
}
