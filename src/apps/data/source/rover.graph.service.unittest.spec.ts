import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { CustomerEntity } from '../../../libs/entities/CustomerEntity';
import { InnerBlacklistEntity } from '../../../libs/entities/InnerBlacklistEntity';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { BiddingDimensionHitsDetails } from '../../../libs/model/bidding/DiligenceBiddingResponse';
import { BlacklistInvestigationsPo, DirectConnectionsPo, TenderInnerBlackListResPo } from '../../../libs/model/bidding/model/TenderInnerBlackListResPo';
import { SubDimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { CompanyDeepInvestBranchParamsPO, CompanyDeepRelationsParamsPO, PersonDeepRelationsParamsPO } from '../DimensionAnalyzeParamsPO';
import { RoverGraphHelper } from '../helper/rover.graph.helper';
import { RoverGraphService } from './rover.graph.service';
import { RoverService } from './rover.service';

// 模拟 getTransV2Detail 函数
jest.mock('../../batch/message.handler/export/model/path-node/data-node-utils', () => ({
  getTransV2Detail: jest.fn().mockImplementation((data) => data),
  parseRelationsData: jest.fn().mockImplementation((path) => {
    return [
      {
        relations: [
          { id: 'node1' },
          {
            type: 'Address',
            role: '相同经营地址',
            data: [{ type: 'Address', role: '相同经营地址' }],
            showDetail: true,
          },
          { id: 'node2' },
        ],
      },
    ];
  }),
}));

const [testOrgId, testUserId] = generateUniqueTestIds('rover.graph.service.unittest.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
jest.setTimeout(30000 * 100);
describe('RoverGraphService Unit Tests', () => {
  let service: RoverGraphService;

  // 模拟依赖
  const mockConfigService = {
    roverGraphServer: {
      companyDeepInvestBranch: 'http://test-url/companyDeepInvestBranch',
      companyDeepRelations: 'http://test-url/companyDeepRelations',
      personRelations: 'http://test-url/personRelations',
      companyDeepRelatedParty: 'http://test-url/companyDeepRelatedParty',
      relations: 'http://test-url/relations',
    },
  };

  const mockRedisService = {
    getClient: jest.fn().mockReturnValue({}),
  };

  const mockHttpUtilsService = {
    postRequest: jest.fn().mockImplementation((url, data) => {
      return Promise.resolve({ success: true, data });
    }),
  };

  const mockRoverService = {
    getBlacklistSameSuspectedAcData: jest.fn().mockResolvedValue({}),
  };

  const mockRoverGraphHelper = {
    getCustomerInvestigation: jest.fn().mockResolvedValue([]),
    getBlacklistInvestigation: jest.fn().mockResolvedValue([]),
    getRoverRanges: jest.fn().mockResolvedValue({ groupIds: [], labelIds: [], depIds: [] }),
    getCustomerInvestigationCount: jest.fn().mockResolvedValue(0),
    getBlacklistInvestigationCount: jest.fn().mockResolvedValue(0),
    getCustomerInvestigationV2: jest.fn().mockResolvedValue([]),
    getBlacklistInvestigationV2: jest.fn().mockResolvedValue([]),
    searchCustomerWithAllConditions: jest.fn().mockResolvedValue([]),
    searchCustomerWithAnyCondition: jest.fn().mockResolvedValue([]),
    getBlacklistGroupLabelIds: jest.fn().mockResolvedValue({ groupIds: [], labelIds: [] }),
    searchBlackListWithAllConditions: jest.fn().mockResolvedValue([]),
    searchBlackListWithAnyCondition: jest.fn().mockResolvedValue([]),
    scanInnerBlacklistBatch: jest.fn().mockResolvedValue([]),
    scanDirectBatch: jest.fn().mockResolvedValue([]),
    scanInvestBatch: jest.fn().mockResolvedValue([]),
    scanBlacklistBranchBatch: jest.fn().mockResolvedValue([]),
    getTenderInnerBlacklist: jest.fn().mockResolvedValue({
      blacklistInvestigations: [],
      directConnection: [],
    }),
  };

  const mockInnerBlacklistRepo = {
    createQueryBuilder: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orWhere: jest.fn().mockReturnThis(),
      getRawMany: jest.fn().mockResolvedValue([]),
    }),
  };

  const mockCustomerRepo = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoverGraphService,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: RedisService, useValue: mockRedisService },
        { provide: HttpUtilsService, useValue: mockHttpUtilsService },
        { provide: RoverService, useValue: mockRoverService },
        { provide: RoverGraphHelper, useValue: mockRoverGraphHelper },
        { provide: getRepositoryToken(InnerBlacklistEntity), useValue: mockInnerBlacklistRepo },
        { provide: getRepositoryToken(CustomerEntity), useValue: mockCustomerRepo },
      ],
    }).compile();

    service = module.get<RoverGraphService>(RoverGraphService);

    // 模拟 getResultByPage 方法，避免依赖外部函数
    service['getResultByPage'] = jest.fn().mockImplementation((response, pageType, pageIndex, pageSize) => {
      if (pageType === 'blacklistInvestigations' && response.blacklistInvestigations.length > 0) {
        const result = new BiddingDimensionHitsDetails();
        result.key = DimensionLevel2Enums.BlackListInvestigations;
        result.name = '内部黑名单关联关系';
        result.totalHits = response.blacklistInvestigations.length;
        result.data = { Result: response.blacklistInvestigations };
        return result;
      } else if (pageType === 'directConnection' && response.directConnection.length > 0) {
        const result = new BiddingDimensionHitsDetails();
        result.key = DimensionLevel2Enums.DirectConnection;
        result.name = '被列入内部黑名单';
        result.totalHits = response.directConnection.length;
        result.data = { Result: response.directConnection };
        return result;
      }
      return null;
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBlacklistRealTypes', () => {
    it('应该正确转换维度到实际类型', () => {
      // 创建测试维度数据
      const dimensions: SubDimensionDefinitionPO[] = [
        {
          key: DimensionLevel2Enums.HitInnerBlackList,
          name: '内部黑名单',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        {
          key: DimensionLevel2Enums.ForeignInvestment,
          name: '对外投资',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: 0,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        {
          key: DimensionLevel2Enums.Shareholder,
          name: '参股股东',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: 1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
      ];

      // 使用私有方法测试
      const types = (service as any).getBlacklistRealTypes(dimensions);

      // 验证结果
      expect(types).toContain(DimensionLevel2Enums.HitInnerBlackList);
      expect(types).toContain(DimensionLevel2Enums.Invest);
      expect(types).toContain(DimensionLevel2Enums.HisShareholder);
    });

    it('应该处理不同的 validValue 值', () => {
      // 测试 ForeignInvestment 维度的不同 validValue 值
      const dimensions: SubDimensionDefinitionPO[] = [
        // validValue = 0 应该返回 Invest
        {
          key: DimensionLevel2Enums.ForeignInvestment,
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: 0,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
        // validValue = 1 应该返回 HisInvest
        {
          key: DimensionLevel2Enums.ForeignInvestment,
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: 1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
      ];

      const types = (service as any).getBlacklistRealTypes(dimensions);

      // 验证结果
      expect(types).toContain(DimensionLevel2Enums.Invest);
      expect(types).toContain(DimensionLevel2Enums.HisInvest);
    });
  });

  describe('getResultByPage', () => {
    it('应该返回 blacklistInvestigations 分页数据', () => {
      // 创建测试响应数据
      const blacklistInvestigation1 = new BlacklistInvestigationsPo();
      blacklistInvestigation1.companyNameRelated = '测试1';
      blacklistInvestigation1.companyKeynoRelated = 'key1';
      blacklistInvestigation1.relationPaths = []; // 添加空的 relationPaths 以避免错误

      const blacklistInvestigation2 = new BlacklistInvestigationsPo();
      blacklistInvestigation2.companyNameRelated = '测试2';
      blacklistInvestigation2.companyKeynoRelated = 'key2';
      blacklistInvestigation2.relationPaths = [];

      const blacklistInvestigation3 = new BlacklistInvestigationsPo();
      blacklistInvestigation3.companyNameRelated = '测试3';
      blacklistInvestigation3.companyKeynoRelated = 'key3';
      blacklistInvestigation3.relationPaths = [];

      const response: TenderInnerBlackListResPo = {
        blacklistInvestigations: [blacklistInvestigation1, blacklistInvestigation2, blacklistInvestigation3],
        directConnection: [],
      };

      // 使用私有方法测试
      const result = service['getResultByPage'](response, 'blacklistInvestigations', 1, 2);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.key).toBe(DimensionLevel2Enums.BlackListInvestigations);
      expect(result.name).toBe('内部黑名单关联关系');
      expect(result.totalHits).toBe(3);
    });

    it('应该返回 directConnection 分页数据', () => {
      // 创建测试响应数据
      const directConnection1 = new DirectConnectionsPo();
      directConnection1.companyNameDD = '直接关联1';
      directConnection1.companyKeynoDD = 'key1';
      directConnection1.blacklistId = 1;

      const directConnection2 = new DirectConnectionsPo();
      directConnection2.companyNameDD = '直接关联2';
      directConnection2.companyKeynoDD = 'key2';
      directConnection2.blacklistId = 2;

      const response: TenderInnerBlackListResPo = {
        blacklistInvestigations: [],
        directConnection: [directConnection1, directConnection2],
      };

      // 使用私有方法测试
      const result = service['getResultByPage'](response, 'directConnection', 1, 2);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.key).toBe(DimensionLevel2Enums.DirectConnection);
      expect(result.name).toBe('被列入内部黑名单');
      expect(result.totalHits).toBe(2);
    });

    it('应该在数据为空时返回 null', () => {
      // 创建测试响应数据
      const response: TenderInnerBlackListResPo = {
        blacklistInvestigations: [],
        directConnection: [],
      };

      // 使用私有方法测试
      const result1 = service['getResultByPage'](response, 'blacklistInvestigations', 1, 2);
      const result2 = service['getResultByPage'](response, 'directConnection', 1, 2);

      // 验证结果
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });
  });

  describe('parseRelations', () => {
    it('应该正确解析关系数据', () => {
      // 创建测试记录数据
      const record = {
        startCompanyName: '测试公司A',
        startCompanyKeyno: 'keyA',
        endCompanyName: '测试公司B',
        endCompanyKeyno: 'keyB',
        relationPaths: [
          [
            { id: 'keyA', name: '测试公司A', type: 'node' },
            {
              type: 'HasAddress',
              startid: 'keyA',
              endid: 'address1',
              register_date: 1625097600000,
            },
            {
              'Address.name': '北京市海淀区',
              id: 'address1',
              type: 'node',
            },
            {
              type: 'HasAddress',
              startid: 'keyB',
              endid: 'address1',
              register_date: 1625097600000,
            },
            { id: 'keyB', name: '测试公司B', type: 'node' },
          ],
        ],
      };

      // 模拟 parseRelations 方法的返回值
      (service as any).parseRelations = jest.fn().mockReturnValue([
        {
          relations: [
            { id: 'node1' },
            {
              type: 'Address',
              role: '相同经营地址',
              data: [{ type: 'Address', role: '相同经营地址' }],
              showDetail: true,
            },
            { id: 'node2' },
          ],
        },
      ]);

      // 使用私有方法测试
      const result = (service as any).parseRelations(record);

      // 验证结果
      expect(result).toBeDefined();
      expect(result[0].relations).toBeDefined();
      expect(result[0].relations.length).toBe(3);
      expect(result[0].relations[1].type).toBe('Address');
    });
  });

  describe('getSimplePageData', () => {
    it('应该返回正确的分页数据', () => {
      const testArray = [{ companyName: '测试1' }, { companyName: '测试2' }, { companyName: '测试3' }, { companyName: '测试4' }, { companyName: '测试5' }];

      // 使用私有方法测试
      const result = (service as any).getSimplePageData(testArray, 2, 2);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result.length).toBe(2);
      expect(result.Result[0].companyName).toBe('测试3');
      expect(result.Result[1].companyName).toBe('测试4');
      expect(result.Paging.TotalRecords).toBe(5);
      expect(result.Paging.PageIndex).toBe(2);
      expect(result.Paging.PageSize).toBe(2);
    });

    it('应该处理空数组', () => {
      const testArray = [];

      // 使用私有方法测试
      const result = (service as any).getSimplePageData(testArray, 1, 10);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result.length).toBe(0);
      expect(result.Paging.TotalRecords).toBe(0);
    });
  });

  // 测试公共方法
  describe('公共方法测试', () => {
    it('getCompanyDeepInvestBranch 应该调用 httpUtilsService.postRequest', async () => {
      const params: CompanyDeepInvestBranchParamsPO = {
        startCompanyId: 'test123',
        depth: 2,
        percentage: 0.1,
      };
      await service.getCompanyDeepInvestBranch(params);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.companyDeepInvestBranch, params);
    });

    it('getCompanyDeepRelations 应该调用 httpUtilsService.postRequest', async () => {
      const params: CompanyDeepRelationsParamsPO = {
        startCompanyId: 'test123',
        endCompanyIds: ['test456'],
        types: ['Invest'],
        orgId: 123,
        percentage: 0.1,
      };
      await service.getCompanyDeepRelations(params);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.companyDeepRelations, params);
    });

    it('getPersonDeepRelations 应该调用 httpUtilsService.postRequest', async () => {
      const params: PersonDeepRelationsParamsPO = {
        startId: 'p123',
        endCompanyIds: ['test456'],
        types: ['Employ'],
        depth: 2,
        percentage: 0.1,
      };
      await service.getPersonDeepRelations(params);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.personRelations, params);
    });

    it('getChildCompanyList 应该调用 httpUtilsService.postRequest 并传递正确参数', async () => {
      await service.getChildCompanyList('test123', 2, 0.1);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.companyDeepRelatedParty, {
        depth: 2,
        percentage: 0.1,
        relationType: 'MajorityInvestment',
        startCompanyId: 'test123',
      });
    });

    it('getParentCompanyList 应该调用 httpUtilsService.postRequest 并传递正确参数', async () => {
      await service.getParentCompanyList('test123', 2, 0.1);
      expect(mockHttpUtilsService.postRequest).toHaveBeenCalledWith(mockConfigService.roverGraphServer.companyDeepRelatedParty, {
        depth: 2,
        percentage: 0.1,
        relationType: 'MotherCompanyMajorityShareholder',
        startCompanyId: 'test123',
      });
    });
  });

  describe('scanForBiddingV2', () => {
    it('应该正确调用 getTenderInnerBlacklist 并处理结果', async () => {
      // 准备测试数据
      const orgId = 123;
      const companyIds = ['company1', 'company2'];
      const pageSize = 10;
      const pageIndex = 1;
      const dimensions: SubDimensionDefinitionPO[] = [
        {
          key: DimensionLevel2Enums.HitInnerBlackList,
          name: '内部黑名单',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
      ];
      const detailsParams = [];

      // 模拟 getTenderInnerBlacklist 的返回值
      const blacklistInvestigation = new BlacklistInvestigationsPo();
      blacklistInvestigation.companyNameRelated = '测试公司';
      blacklistInvestigation.companyKeynoRelated = 'testKey';
      blacklistInvestigation.relationPaths = [];

      const directConnection = new DirectConnectionsPo();
      directConnection.companyNameDD = '直接关联公司';
      directConnection.companyKeynoDD = 'directKey';
      directConnection.blacklistId = 1;

      mockRoverGraphHelper.getTenderInnerBlacklist.mockResolvedValue({
        blacklistInvestigations: [blacklistInvestigation],
        directConnection: [directConnection],
      });

      // 执行测试
      const result = await service.scanForBiddingV2(orgId, companyIds, pageSize, pageIndex, dimensions, detailsParams);

      // 验证结果
      expect(mockRoverGraphHelper.getTenderInnerBlacklist).toHaveBeenCalledWith(orgId, companyIds, expect.any(Array), detailsParams);
      expect(result).toHaveLength(2); // 应该返回两个结果项
      expect(result[0].key).toBe(DimensionLevel2Enums.BlackListInvestigations);
      expect(result[1].key).toBe(DimensionLevel2Enums.DirectConnection);
    });

    it('应该处理空结果', async () => {
      // 准备测试数据
      const orgId = 123;
      const companyIds = ['company1', 'company2'];
      const pageSize = 10;
      const pageIndex = 1;
      const dimensions: SubDimensionDefinitionPO[] = [
        {
          key: DimensionLevel2Enums.HitInnerBlackList,
          name: '内部黑名单',
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: -1,
              },
            ],
          },
        } as SubDimensionDefinitionPO,
      ];
      const detailsParams = [];

      // 模拟 getTenderInnerBlacklist 的返回值
      mockRoverGraphHelper.getTenderInnerBlacklist.mockResolvedValue({
        blacklistInvestigations: [],
        directConnection: [],
      });

      // 执行测试
      const result = await service.scanForBiddingV2(orgId, companyIds, pageSize, pageIndex, dimensions, detailsParams);

      // 验证结果
      expect(mockRoverGraphHelper.getTenderInnerBlacklist).toHaveBeenCalledWith(orgId, companyIds, expect.any(Array), detailsParams);
      expect(result).toHaveLength(0); // 应该返回空数组
    });
  });

  describe('parseTenderGraphBlacklistResponse', () => {
    it('应该正确解析黑名单响应数据', () => {
      // 创建测试数据
      const directConnection1 = new DirectConnectionsPo();
      directConnection1.companyNameDD = '直接关联1';
      directConnection1.companyKeynoDD = 'key1';
      directConnection1.blacklistId = 1;
      directConnection1.joinDate = Date.now() / 1000;

      const blacklistResponse = {
        directConnection: [directConnection1],
        personConnections: [],
        shareholderConnections: [],
        actualControllerConnections: [],
        branchConnections: [],
      };

      const dimensions: SubDimensionDefinitionPO[] = [
        {
          key: DimensionLevel2Enums.HitInnerBlackList,
          strategyModel: {
            detailsParams: [
              {
                field: QueryParamsEnums.isValid,
                fieldVal: '1',
              },
            ],
          },
        } as SubDimensionDefinitionPO,
      ];

      // 执行测试
      const result = (service as any).parseTenderGraphBlacklistResponse(blacklistResponse, dimensions);

      // 验证结果
      expect(result).toBeDefined();
      expect(result[DimensionLevel2Enums.HitInnerBlackList]).toBeDefined();
      expect(result[DimensionLevel2Enums.HitInnerBlackList].length).toBe(1);
    });
  });
});
