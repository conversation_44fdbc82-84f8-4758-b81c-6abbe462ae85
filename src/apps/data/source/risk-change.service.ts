/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { ConfigService } from 'libs/config/config.service';
import { Client } from '@elastic/elasticsearch';
import { AbstractEsAnalyzeService } from './analyze.base.service';
import { DimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { getStartTimeByCycle } from '../../../libs/utils/utils';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/pojo/req&res/details/response';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { pick } from 'lodash';
import { getPastExpirationDate } from '../../../libs/utils/date.utils';
import { DimensionAnalyzeParamsPO } from '../DimensionAnalyzeParamsPO';
import { DimensionScorePO } from '../../../libs/model/diligence/pojo/dimension/DimensionScorePO';
import { DimensionLevelClass } from '../../../libs/constants/dimension.constants';
import { processScorePO } from '../../../libs/utils/diligence/diligence.utils';
import { CompanyDetailService } from '../../company/company-detail.service';
import { ApiResponseStatusEnum } from '../../../libs/enums/ApiResponseStatusEnum';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import * as Bluebird from 'bluebird';
import { getCompareResult } from '../../../libs/model/diligence/pojo/dimension/DimensionQueryPO';

/**
 * 风险动态数据源
 */
@Injectable()
export class RiskChangeService extends AbstractEsAnalyzeService {
  constructor(private readonly configService: ConfigService, private readonly companyDetailService: CompanyDetailService) {
    super(
      'RiskChangeService',
      new Client({
        nodes: configService.esConfig.riskChangeList.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.riskChangeList.indexName,
    );
  }

  async getDimensionDetail(dimension: DimensionDefinitionPO, params: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    try {
      if (dimension.key === DimensionLevel3Enums.MainInfoUpdateRegisteredCapital) {
        return this.handleMainInfoUpdateRegisteredCapital(dimension, params);
      }

      const data = await super.getDimensionDetail(dimension, params);
      switch (dimension.key) {
        case DimensionLevel3Enums.NoticeInTimePeriod:
          return this.handleNoticeInTimePeriod(data);
        case DimensionLevel2Enums.SecurityNotice:
          return this.handleSecurityNotice(data);
        case DimensionLevel3Enums.CapitalReduction:
          return this.handleCapitalReduction(data);
        case DimensionLevel3Enums.MainInfoUpdateHolder:
        case DimensionLevel3Enums.MainInfoUpdatePerson:
        case DimensionLevel3Enums.MainInfoUpdateBeneficiary:
          return this.handleMainInfoUpdates(data);
        default:
          return this.handleDefaultCase(data);
      }
    } catch (error) {
      this.logger.error(`RiskChangeService getDimensionDetail err: ${error},dimension: ${dimension.key}`);
      return HitDetailsBaseResponse.failed(error.response?.error || error.message, DimensionSourceEnums.RiskChange, error.response?.code);
    }
  }

  // 注册资本变更特殊处理
  private async handleMainInfoUpdateRegisteredCapital(dimension: DimensionDefinitionPO, params: HitDetailsBaseQueryParams) {
    const fullData = await super.getDimensionDetail(dimension, { ...params, pageIndex: 1, pageSize: 1000 });

    const processedResults = fullData.Result.map((it) => {
      const changeExt = it.ChangeExtend ? JSON.parse(it.ChangeExtend) : {};
      if (changeExt?.T === 1) {
        // T=1表示减少
        // 提取数值（保留原始带单位的 beforeContent/afterContent）
        const beforeNum = parseFloat((changeExt?.A.match(/(\d+\.?\d*)/) || [])[0]) || 0;
        const afterNum = parseFloat((changeExt?.B.match(/(\d+\.?\d*)/) || [])[0]) || 0;
        return {
          beforeContent: changeExt?.A,
          afterContent: changeExt?.B,
          beforeNum: beforeNum,
          afterNum: afterNum,
          flag: changeExt?.T,
          changeDate: it?.ChangeDate,
        };
      }
      return null;
    })
      .filter(Boolean)
      .filter((item) => item.beforeNum > 0 && item.afterNum >= 0); // 过滤无效数值

    const queryPo = dimension?.strategyModel?.detailsParams?.find((d) => d.field === QueryParamsEnums.registeredCapitalChangeRatio);
    if (!queryPo) return this.createPagedResponse(processedResults, params);

    const matchedResults = processedResults.filter((item) => {
      if (item.beforeNum <= item.afterNum) return false; // 非减少或数值无效
      const reductionRatio = ((item.beforeNum - item.afterNum) / item.beforeNum) * 100;
      return getCompareResult(reductionRatio, queryPo.fieldVal, queryPo.fieldOperator);
    });

    return this.createPagedResponse(matchedResults, params);
  }

  // 开庭公告处理
  private async handleNoticeInTimePeriod(data: HitDetailsBaseResponse) {
    const caseReasonTypes = data.Result.filter((it) => it.ChangeExtend)
      .map((it) => JSON.parse(it.ChangeExtend)?.RC)
      .filter(Boolean);

    const caseReasonDescriptions = await this.companyDetailService.getCaseReasonDescription(caseReasonTypes);

    data.Result = data.Result.map((it) => {
      const changeExt = it.ChangeExtend ? JSON.parse(it.ChangeExtend) : {};
      return {
        caseNo: changeExt.C,
        caseReasonType: changeExt.RC,
        caseReasonDescription: caseReasonDescriptions[changeExt.RC],
        caseReason: changeExt.B,
        caseRoleGroup: changeExt.D,
        court: changeExt.E,
        courtDate: changeExt.A,
        isValid: it.IsValid,
        keyNo: it.KeyNo,
        riskId: it.Id,
        name: it.Name,
      };
    });
    return data;
  }

  // 安全通告处理
  private async handleSecurityNotice(data: HitDetailsBaseResponse) {
    data.Result = data.Result.map((it) => {
      const changeExt = it.ChangeExtend ? JSON.parse(it.ChangeExtend) : {};
      return {
        name: changeExt.A,
        reason: changeExt.H,
        publishUnit: changeExt.D,
        publishDate: changeExt.C,
        isValid: changeExt.IsValid,
        keyNo: it.KeyNo,
        riskId: it.Id,
        id: changeExt.J,
        updateDate: it.CreateDate,
      };
    });
    return data;
  }

  // 减资公告处理
  private async handleCapitalReduction(data: HitDetailsBaseResponse) {
    data.Result = data.Result.map((it) => {
      const changeExt = it.ChangeExtend ? JSON.parse(it.ChangeExtend) : {};
      return {
        Id: it.Id,
        ObjectId: it.ObjectId,
        DecideDate: changeExt.B,
        Content: changeExt.D,
        NoticeDate: changeExt.A,
        NoticePeriod: changeExt.C,
        NoticeTitle: changeExt.F,
        Name: it.Name,
        KeyNo: it.KeyNo,
      };
    }).sort((a, b) => new Date(b.NoticeDate).getTime() - new Date(a.NoticeDate).getTime());
    return data;
  }

  // 其他主要信息变更处理
  private async handleMainInfoUpdates(data: HitDetailsBaseResponse) {
    data.Result = data.Result.map((it) =>
      pick(it, [
        'GroupId',
        'Id',
        'KeyNo',
        'Name',
        'RiskLevel',
        'DataType',
        'Category',
        'BeforeContent',
        'AfterContent',
        'ChangeExtend',
        'ObjectId',
        'ChangeStatus',
        'ChangeDate',
        'CreateDate',
        'DetailCount',
        'DisplayList',
        'Extend1',
        'Extend4',
        'MaxLevel',
        'Extend3',
        'Extend2',
        'RKDetailCount',
        'IsImportant',
        'ImportantCount',
        'RelatedInfo',
      ]),
    );
    return data;
  }

  // 默认处理
  private async handleDefaultCase(data: HitDetailsBaseResponse) {
    data.Result = data.Result.map((it) => {
      return it.ChangeExtend ? JSON.parse(it.ChangeExtend) : {};
    }).filter(Boolean);
    return data;
  }

  // 公共分页方法
  private createPagedResponse(processedResults: any[], params: HitDetailsBaseQueryParams) {
    const pageSize = params?.pageSize || 10; // 默认分页大小
    const pageIndex = params?.pageIndex || 1;
    const totalRecords = processedResults.length;

    return {
      GroupItems: [],
      status: ApiResponseStatusEnum.OK,
      Result: processedResults.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: totalRecords,
      },
    };
  }

  async analyze(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], analyzeParams?: DimensionAnalyzeParamsPO): Promise<DimensionScorePO[]> {
    const result = [];
    const handledKeys = new Set();

    await Bluebird.map(
      dimensionDefinitionPOs,
      async (dimension) => {
        let hitCount = 0;
        const desData = {
          level: DimensionLevelClass[dimension.strategyModel.level || 0],
          name: dimension.name,
          isHidden: '',
          isHiddenY: '',
          recentTime: '',
        };

        switch (dimension.key) {
          case DimensionLevel3Enums.NoticeInTimePeriod: {
            handledKeys.add(dimension.key);
            const noticeRes = await this.getDimensionDetail(dimension, { keyNo: companyId, pageIndex: 1, pageSize: 5 });
            const limitCount = dimension.strategyModel.detailsParams.find((d) => d.field == QueryParamsEnums.limitCount);
            const recentTime = dimension.strategyModel.detailsParams.find((d) => d.field == QueryParamsEnums.recentTime);

            if (noticeRes?.Paging?.TotalRecords >= limitCount?.fieldVal) {
              hitCount = noticeRes.Paging.TotalRecords;
              // 1-近 7 天；2-近一个月；3-近 3 个月；
              switch (recentTime.fieldVal) {
                case 1:
                  desData.recentTime = '近7天';
                  break;
                case 2:
                  desData.recentTime = '近1个月';
                  break;
                case 3:
                  desData.recentTime = '近3个月';
              }
            }
            break;
          }
          case DimensionLevel3Enums.MainInfoUpdateRegisteredCapital: {
            handledKeys.add(dimension.key);
            //注册资本变更只命中减少的，需要特殊处理
            const response = await this.getDimensionDetail(dimension, { keyNo: companyId, pageIndex: 1, pageSize: 5 });
            hitCount = response.Paging.TotalRecords;
            break;
          }
        }

        if (hitCount) {
          result.push(processScorePO(dimension, hitCount, desData));
        }
      },
      { concurrency: 3 },
    );

    // 处理其他维度
    const otherDimensions = dimensionDefinitionPOs.filter((d) => !handledKeys.has(d.key));

    if (otherDimensions.length > 0) {
      const superResult = await super.analyze(companyId, otherDimensions, analyzeParams);
      result.push(...superResult);
    }

    return result.filter((it) => it);
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionDefinitionPO, params?: Record<string, any>) {
    const subBool = {
      filter: [],
    };
    const categoryMappings = {
      MainInfoUpdateHolder: 24,
      MainInfoUpdatePerson: 25,
      MainInfoUpdateBeneficiary: 114,
    };
    if (dimension.strategyModel?.cycle > 0) {
      const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
      if (dimension.key === DimensionLevel2Enums.SecurityNotice) {
        // 公安通告使用 CreateDate 字段，RA-15285
        subBool.filter.push({ range: { CreateDate: { gte: Math.ceil(timestamp / 1000) } } });
      } else {
        subBool.filter.push({ range: { ChangeDate: { gte: Math.ceil(timestamp / 1000) } } });
      }
    }
    const isValidParams = dimension.strategyModel?.detailsParams?.find((it) => it.field === QueryParamsEnums.isValid);
    if (isValidParams && Number(isValidParams.fieldVal) >= 0) {
      subBool.filter.push({ term: { IsValid: Number(isValidParams.fieldVal) } });
    }
    switch (dimension.key) {
      case DimensionLevel3Enums.NoticeInTimePeriod: {
        // 开庭公告
        subBool.filter.push({ term: { Category: 18 } });
        subBool.filter.push({ term: { KeyNo: companyId } });
        const recentTime = dimension.strategyModel.detailsParams.find((d) => d.field == QueryParamsEnums.recentTime);
        const dateType = recentTime?.fieldVal ? recentTime?.fieldVal : 2;
        const expirationDate = getPastExpirationDate(dateType);
        subBool.filter.push({ range: { CreateDate: { gte: expirationDate } } });
        break;
      }
      case DimensionLevel2Enums.SecurityNotice: {
        subBool.filter.push({ term: { Category: 109 } });
        subBool.filter.push({ term: { KeyNo: companyId } });
        // if (dimension.strategyModel?.cycle > 0) {
        //   const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
        //   subBool.filter.push({ range: { ChangeDate: { gte: Math.ceil(timestamp / 1000) } } });
        // }
        // const isValidParams = dimension.strategyModel?.detailsParams?.find((it) => it.field === QueryParamsEnums.isValid);
        // if (isValidParams && Number(isValidParams.fieldVal) >= 0) {
        //   subBool.filter.push({ term: { IsValid: Number(isValidParams.fieldVal) } });
        // }
        break;
      }
      case DimensionLevel3Enums.CapitalReduction: {
        subBool.filter.push({ term: { Category: 123 } });
        subBool.filter.push({ term: { IsValid: 1 } });
        subBool.filter.push({ term: { KeyNo: companyId } });
        break;
      }
      case DimensionLevel3Enums.MainInfoUpdateHolder:
      case DimensionLevel3Enums.MainInfoUpdatePerson:
      case DimensionLevel3Enums.MainInfoUpdateBeneficiary: {
        subBool.filter.push({ term: { IsValid: 1 } });
        subBool.filter.push({ terms: { IsRisk: [0, 1] } });
        subBool.filter.push({ range: { Es_Version: { lt: 43 } } });
        subBool.filter.push({ term: { IsRK: 1 } });
        subBool.filter.push({ term: { KeyNo: companyId } });
        subBool.filter.push({ term: { Category: categoryMappings[dimension.key] } });
        break;
      }
      case DimensionLevel3Enums.MainInfoUpdateRegisteredCapital: {
        subBool.filter.push({ term: { Category: 37 } });
        subBool.filter.push({ term: { IsValid: 1 } });
        subBool.filter.push({ term: { KeyNo: companyId } });
        break;
      }
      default: {
        this.logger.error('unreachable code');
      }
    }
    return { bool: subBool };
  }

  public async getAddressChangeInfo(keyno: string) {
    const query = {
      bool: {
        filter: [{ term: { Category: 40 } }, { term: { KeyNo: keyno } }],
      },
    };
    const response = await this.searchEs(
      {
        query,
        sort: [{ ChangeDate: { order: 'desc' } }],
      },
      keyno[0],
    );
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }
}
