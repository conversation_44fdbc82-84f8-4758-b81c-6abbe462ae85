import { BidCollusiveService } from './bid.collusive.service';
import { Test } from '@nestjs/testing';
import { AppTestModule } from '../../app/app.test.module';
import { HitDetailsBidBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request/HitDetailsBidBaseQueryParams';
import { DataModule } from '../data.module';

jest.setTimeout(60 * 1000);
describe('围串标关联', () => {
  let bidCollusiveService: BidCollusiveService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    bidCollusiveService = module.get(BidCollusiveService);
  });

  it('围串标关联查询', async () => {
    const detailParams: HitDetailsBidBaseQueryParams = {
      keyNos: ['44ecbce2cb832750f9c095da9beb9175', 'a3abe18aaffe3d4e800c0d43debf66cc'],
      keyNoAndNames: [
        { companyId: '44ecbce2cb832750f9c095da9beb9175', companyName: '河北禾盛化工有限公司' },
        { companyId: 'a3abe18aaffe3d4e800c0d43debf66cc', companyName: '河北培根生物科技有限公司' },
      ],
      pageIndex: 1,
      pageSize: 5,
    };

    const response = await bidCollusiveService.getBidCollusiveList(detailParams);
    expect(response.Result.length).toBe(2);
  });
});
