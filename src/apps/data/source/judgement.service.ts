/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { ConfigService } from 'libs/config/config.service';
import { Cacheable } from '@type-cacheable/core';
import { DimensionDefinitionPO, SubDimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/pojo/req&res/details/response';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { EnterpriseLibService } from './enterprise.lib.service';
import { Client } from '@elastic/elasticsearch';
import { AggBucketItemPO, JudgementAggBucketItemPO } from 'libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import * as Bluebird from 'bluebird';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { DocTypeMap, JudgeResultMap } from '../../../libs/constants/judgement';
import { getStartTimeByCycle, toRoundFixed, transferToNumber } from '../../../libs/utils/utils';
import { find, flatten, intersection, some } from 'lodash';
import { PersonData } from '../../../libs/model/data/source/PersonData';
import { AbstractEsAnalyzeService } from './analyze.base.service';
import { DimensionScorePO } from 'libs/model/diligence/pojo/dimension/DimensionScorePO';
import { DimensionLevelClass } from '../../../libs/constants/dimension.constants';
import { processScorePO } from '../../../libs/utils/diligence/diligence.utils';
import { HitDetailsBidBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request/HitDetailsBidBaseQueryParams';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from '../../../libs/exceptions/exceptionConstants';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { DimensionAnalyzeParamsPO } from '../DimensionAnalyzeParamsPO';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { PersonHelper } from '../helper/person.helper';
import { CompanyDetailService } from '../../company/company-detail.service';
import { ExcludeCompanyService } from 'apps/exclude_company/exclude-company.service';

/**
 * 裁判文书
 */
@Injectable()
export class JudgementService extends AbstractEsAnalyzeService {
  constructor(
    private readonly configService: ConfigService,
    private readonly entLibService: EnterpriseLibService,
    private readonly personHelper: PersonHelper,
    private readonly companyDetailService: CompanyDetailService,
    private readonly excludeCompanyService: ExcludeCompanyService,
  ) {
    super(
      JudgementService.name,
      new Client({
        nodes: configService.esConfig.judgement.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.judgement.indexName,
    );
  }

  /**
   * 获取风险维度的详情
   * @param dimension
   * @param params
   * @param analyzeParams
   * @returns
   */
  async getDimensionDetail(
    dimension: DimensionDefinitionPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNo, companyName } = params;
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    if (resp?.Result?.length) {
      resp.Result.forEach((d) => {
        d.casename = d?.casereason + ' ' + d?.trialround + DocTypeMap.get(d?.doctype);
        // 当事人优先用caserolegroupbyrolename，如果caserolegroupbyrolename为空，则用caserole
        if (d.caserolegroupbyrolename) {
          d.caserolegroupbyrolename = JSON.parse(d.caserolegroupbyrolename).map((role) => {
            if (role?.DetailList?.length > 0) {
              role.DetailList.forEach((roleDetail) => {
                if (roleDetail?.JR && roleDetail?.JudgeResult) {
                  roleDetail.JudgeResultDescription = JudgeResultMap.get(roleDetail?.JR);
                }
              });
            }
            return role;
          });
        }
        if (d.caserole) {
          d.caserole = JSON.parse(d.caserole);
        }
        d.involveTags = []; // 涉案标签（案外人、被提及）
        d.involveRole = []; // 涉案标签（案外人、被提及）
      });

      switch (dimension?.key) {
        case DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve:
        case DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory: {
          const ids = [keyNo];
          const keyNoMap = new Map<string, any>();
          keyNoMap.set(keyNo, { name: companyName, job: '企业主体' });
          const associateObjects = dimension.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.associateObject)?.fieldVal;
          const associateExcludes = dimension.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.associateExclude)?.fieldVal;
          const personData = await this.getRelatedCompanyAndPerson(keyNo, associateObjects, associateExcludes, dimension.key);
          personData.forEach((x) => {
            keyNoMap.set(x.keyNo, { name: x.name, job: x.job });
            ids.push(x.keyNo);
          });
          resp.Result.forEach((d) => {
            this.addTagAndRole(d, ids, keyNoMap);
          });
          break;
        }
        case DimensionLevel3Enums.SalesContractDispute:
        case DimensionLevel3Enums.MajorDispute: {
          //查询案由描述
          const caseReasonTypes = resp.Result.map((d) => d.casereasontype);
          const caseReasonDescriptions = await this.companyDetailService.getCaseReasonDescription(caseReasonTypes);
          resp.Result.forEach((d) => {
            d.caseReasonDescription = caseReasonDescriptions[d.casereasontype];
          });
          break;
        }
        default:
      }
    }
    return resp;
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionDefinitionPO): Promise<object> {
    const ids = [companyId];

    const subBool = {
      filter: [],
    };
    switch (dimension.key) {
      case DimensionLevel3Enums.SalesContractDispute: {
        subBool.filter.push(
          { term: { delflag: 0 } },
          { terms: { comprelkeywords: ids } },
          { terms: { casereasontype: ['B040109', 'B04010905', 'B040110', 'C02040109', 'C02040110'] } },
        );
        if (dimension.strategyModel?.cycle > 0) {
          const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
          subBool.filter.push({ range: { judgedate: { gte: Math.ceil(timestamp / 1000) } } });
        }
        const validParam = dimension.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.isValid);
        if (Number(validParam.fieldVal) >= 0) {
          subBool.filter.push({ term: { isvalid: Number(validParam.fieldVal) } });
        }
        return { bool: subBool };
      }
      case DimensionLevel3Enums.MajorDispute: {
        subBool['should'] = [];
        subBool['minimum_should_match'] = 1;
        subBool.filter.push({ term: { delflag: 0 } });
        const judgementRoleExclude = dimension.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.judgementRoleExclude);
        subBool['should'].push({ terms: { defendant: ids } });
        if (judgementRoleExclude?.fieldVal?.length) {
          if (!judgementRoleExclude.fieldVal.includes('prosecutor')) {
            subBool['should'].push({ terms: { prosecutor: ids } });
          }
          if (!judgementRoleExclude.fieldVal.includes('thirdpartyrole')) {
            subBool['should'].push({ terms: { thirdpartyrole: ids } });
          }
        } else {
          subBool['should'].push({ terms: { prosecutor: ids } });
          subBool['should'].push({ terms: { thirdpartyrole: ids } });
        }
        if (dimension.strategyModel?.cycle > 0) {
          const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
          subBool.filter.push({ range: { judgedate: { gte: Math.ceil(timestamp / 1000) } } });
        }
        const validParam = dimension.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.isValid);
        if (Number(validParam.fieldVal) >= 0) {
          subBool.filter.push({ term: { isvalid: Number(validParam.fieldVal) } });
        }
        const amountParam = dimension.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.amountInvolved);
        subBool.filter.push({ range: { amountinvolved: { gte: Number(amountParam.fieldVal) } } });
        return { bool: subBool };
      }
      case DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve:
      case DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory: {
        return await this.getCriminalInvolveQuery(companyId, dimension, ids, subBool);
      }
    }
    return { bool: subBool };
  }

  protected async createAggs(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[]) {
    const aggs: any = {};
    await Bluebird?.map(dimensionDefinitionPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po);
      const aggsName = `${this.bucketNamePrefix}${po.key}`;
      aggs[aggsName] = {
        filter: dimQuery,
        aggs: {},
      };
      switch (po.key) {
        case DimensionLevel3Enums.SalesContractDispute:
          // 买卖合同纠纷-统计公司作为被告的次数
          aggs[aggsName].aggs['agg_defendant'] = { filter: { terms: { defendant: [companyId] } } };
          break;
        case DimensionLevel3Enums.MajorDispute: {
          aggs[aggsName].aggs['amount'] = {
            sum: {
              field: 'amountinvolved',
            },
          };
          break;
        }
      }
    });
    return aggs;
  }

  protected processAggs(aggObj: any, dimensionDefinitionPOs: DimensionDefinitionPO[]): AggBucketItemPO[] {
    const bucketData: AggBucketItemPO[] = [];
    dimensionDefinitionPOs.forEach((po) => {
      const aggsName = `${this.bucketNamePrefix}${po.key}`;
      const bucket = aggObj[aggsName];
      const hitCount = bucket['doc_count'];
      if (hitCount > 0) {
        const res: JudgementAggBucketItemPO = {
          dimensionType: po.key,
          hitCount,
        };
        switch (po.key) {
          case DimensionLevel3Enums.SalesContractDispute: {
            const defendantBucket = bucket['agg_defendant'];
            const defendantCount = defendantBucket['doc_count'];
            const percentParam = po.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.percentAsDefendant);
            const percent = Number(percentParam.fieldVal);
            if ((defendantCount / hitCount) * 100 >= percent) {
              bucketData.push(res);
            }
            break;
          }
          case DimensionLevel3Enums.MajorDispute: {
            if (bucket.amount) {
              res.amount = transferToNumber(bucket.amount.value);
            }
            bucketData.push(res);
            break;
          }
          default:
            bucketData.push(res);
        }
      }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: JudgementAggBucketItemPO[], dimensionDefinitionPOs: DimensionDefinitionPO[]): DimensionScorePO[] {
    return bucketData
      .map((item) => {
        const d: DimensionDefinitionPO = find(dimensionDefinitionPOs, { key: item.dimensionType });
        const desData = {
          level: DimensionLevelClass[d.strategyModel.level || 0],
          cycle: d.strategyModel?.cycle > 0 ? d.strategyModel.cycle : 0,
          name: d.name,
          isHidden: '',
          isHiddenY: '',
        };
        switch (d.key) {
          case DimensionLevel3Enums.SalesContractDispute: {
            const percentParam = d.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.percentAsDefendant);
            Object.assign(desData, { percent: percentParam.fieldVal });
            break;
          }
          case DimensionLevel3Enums.MajorDispute: {
            if (item.amount) {
              Object.assign(desData, { amountW: toRoundFixed(item.amount / 10000, 2) });
            }
            break;
          }
        }
        const { hitCount } = item as AggBucketItemPO;
        return processScorePO(d, hitCount, desData);
      })
      .filter((t) => t);
  }

  /**
   * 获取关联企业以及人员
   * @param companyId
   * @param associateObjects 关联对象
   * @param associateExcludes 关联排除
   * @param dimensionKey 维度类型
   * @private
   */
  @Cacheable({ ttlSeconds: 100 })
  public async getRelatedCompanyAndPerson(companyId: string, associateObjects: any[], associateExcludes: any[], dimensionKey?: string): Promise<PersonData[]> {
    //投资机构Tags
    const investTags = ['私募基金', '投资', '基金', '资产管理', '私募', '资本', '城投', '证券', '保险', '信托', '银行', '财富关联'];
    const promiseArr = [this.entLibService.getEmployeeList(companyId)];
    const isLegal = find(associateObjects, { key: DetailsParamEnums.LegalRepresentative })?.status == 1;
    const isActualControl = find(associateObjects, { key: DetailsParamEnums.ActualController })?.status == 1;
    const isMajorShareholder = find(associateObjects, { key: DetailsParamEnums.MajorShareholder })?.status == 1;
    const isShareholder = find(associateObjects, { key: DetailsParamEnums.Shareholder })?.status == 1;
    const ignoreInvest = associateExcludes ? find(associateExcludes, { key: DetailsParamEnums.ShareHolderInvest })?.status == 1 : false;
    if (isLegal) {
      promiseArr.push(this.entLibService.getLegalPerson(companyId));
    }
    if (isActualControl) {
      promiseArr.push(this.personHelper.getFinalActualController(companyId));
    }
    if (isShareholder || isMajorShareholder) {
      promiseArr.push(this.entLibService.getPartnerList(companyId, 'all', ignoreInvest));
    }
    let personData: PersonData[] = flatten(await Bluebird.all(promiseArr));
    // 过滤排除的公司
    const filteredCompanyIds = await this.excludeCompanyService.filterExcludedCompanyIds(
      personData.map((p) => p?.keyNo),
      dimensionKey,
    );
    personData = personData.filter((p) => filteredCompanyIds.includes(p?.keyNo));
    if (isShareholder || isMajorShareholder) {
      //股东包含大股东，如果只包含大股东，则只显示大股东
      if (!isShareholder && isMajorShareholder) {
        personData = personData.filter((x) => x.tags?.includes('大股东'));
      }
    }
    const keyNosSet: Set<string> = new Set<string>();
    const uniqPersonData = [];
    personData
      .filter((x) => x?.keyNo)
      .forEach((x) => {
        if (!keyNosSet.has(x.keyNo)) {
          if (ignoreInvest && (some(x.tags, (t) => investTags.includes(t)) || investTags.some((tag) => x.name.includes(tag)))) {
            //如果关联派出设置了排除股东类型为投资机构，则排除投资机构，keyNo g开头的是机关单位，在本需求中也当投资机构处理
            return;
          }
          uniqPersonData.push(x);
          keyNosSet.add(x.keyNo);
        }
      });
    return uniqPersonData;
  }

  /**
   * 获取关联企业的bool查询
   * @param companyId
   * @param dimension
   * @param ids
   * @param subBool
   * @private
   */
  private async getCriminalInvolveQuery(companyId: string, dimension: DimensionDefinitionPO, ids: string[], subBool: any) {
    const cycle = 3;
    //需要特殊处理隐藏企查查的案件数据
    const hiddenCompanyId = 'f625a5b661058ba5082ca508f99ffe1b'; // 企查查的企业id
    const hiddenCaseIds = [
      '6429b14f7367fe496e20757cab0024160',
      'acb4d9bf9f0974cb2529cb3b4e28ccc90',
      'f57d1150a3048a4231eef8894f916fef0',
      '6237c63a13dcfdfa68d370eb6ab219ba0',
      'eac103253dd62dda0d945f0038f2e5900',
    ];
    const associateObjects = dimension.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.associateObject)?.fieldVal;
    const associateExcludes = dimension.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.associateExclude)?.fieldVal;
    const personData = await this.getRelatedCompanyAndPerson(companyId, associateObjects, associateExcludes, dimension.key);
    const keyNos = personData.map((x) => x.keyNo);
    ids.push(...keyNos);
    subBool.filter.push({ term: { delflag: 0 } }, { term: { casetype: 'xs' } }, { terms: { othercns: ids } }, { terms: { caselabel: ['TW00'] } });
    const timestamp = getStartTimeByCycle(cycle);
    let rangeOperator = 'gt';
    if (dimension.key === DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory) {
      rangeOperator = 'lte';
    }
    subBool.filter.push({ range: { judgedate: { [rangeOperator]: Math.ceil(timestamp / 1000) } } });
    const validParam = dimension.strategyModel.detailsParams.find((p) => p.field == QueryParamsEnums.isValid);
    if (Number(validParam.fieldVal) >= 0) {
      subBool.filter.push({ term: { isvalid: Number(validParam.fieldVal) } });
    }
    if (companyId == hiddenCompanyId) {
      subBool['must_not'] = [
        {
          terms: {
            id: hiddenCaseIds,
          },
        },
      ];
    }
    return { bool: subBool };
  }

  async getDimensionDetailForBid(dimension: SubDimensionDefinitionPO, requestData: HitDetailsBidBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const { keyNos, pageSize, pageIndex, keyNoAndNames } = requestData;
    this.logger.info(`get hit details for dimension: ${dimension.key}`);
    if (!requestData?.keyNos?.length) {
      throw new BadParamsException(RoverExceptions.Diligence.Detail.NeedKeyNo);
    }

    const { data } = await this.getDetailFromEsForBid(keyNos, dimension, requestData);
    const filterData = [];
    if (data) {
      data.forEach((d) => {
        // 【被告单位】不为空时，不排查【被提及】;【被告单位】为空时，排查【被提及】
        if (!d.defendant_c) {
          d.casename = d?.casereason + ' ' + d?.trialround + DocTypeMap.get(d?.doctype);
          // 当事人优先用caserolegroupbyrolename，如果caserolegroupbyrolename为空，则用caserole
          if (d.caserolegroupbyrolename) {
            d.caserolegroupbyrolename = JSON.parse(d.caserolegroupbyrolename).map((role) => {
              if (role?.DetailList?.length > 0) {
                role.DetailList.forEach((roleDetail) => {
                  if (roleDetail?.JR && roleDetail?.JudgeResult) {
                    roleDetail.JudgeResultDescription = JudgeResultMap.get(roleDetail?.JR);
                  }
                });
              }
              return role;
            });
          }
          if (d.caserole) {
            d.caserole = JSON.parse(d.caserole);
          }
          d.involveTags = []; // 涉案标签（案外人、被提及）
          d.involveRole = []; // 涉案标签（案外人、被提及）
          filterData.push(d);
        }
      });

      switch (dimension?.key) {
        case DimensionLevel2Enums.BidAdministrativeJudgement: {
          const ids = keyNos;
          const keyNoMap = new Map<string, any>();
          keyNoAndNames.forEach((x) => {
            keyNoMap.set(x.companyId, { name: x.companyName, job: '企业主体' });
          });

          filterData.forEach((d) => {
            this.addTagAndRole(d, ids, keyNoMap);
          });
          break;
        }
      }
      return Object.assign(HitDetailsBaseResponse.ok(), {
        Paging: {
          PageSize: pageSize,
          PageIndex: pageIndex,
          TotalRecords: filterData.length,
        },
        Result: filterData,
        GroupItems: [],
      });
    }
  }

  /**
   * 添加涉案标签和角色
   * @param d
   * @param ids
   * @param keyNoMap
   * @private
   */
  private async addTagAndRole(d: any, ids: string[], keyNoMap: Map<string, any>) {
    if (d.outsider && d.outsider?.length > 0) {
      const outsiders = intersection(d.outsider, ids);
      if (outsiders.length > 0) {
        d.involveTags.push('案外人');
        outsiders.forEach((id) => {
          const info = keyNoMap.get(id);
          d.involveRole.push({ Tag: '案外人', KeyNo: id, Name: info.name, Job: info.job });
        });
      }
    }
    if (d.involveTags?.length == 0) {
      if (d.othercns && d.othercns?.length > 0) {
        const othercns = intersection(d.othercns, ids);
        if (othercns.length > 0) {
          d.involveTags.push('被提及');
          othercns.forEach((id) => {
            const info = keyNoMap.get(id);
            d.involveRole.push({ Tag: '被提及', KeyNo: id, Name: info.name, Job: info.job });
          });
        }
      }
    }
  }

  private async getDetailFromEsForBid(companyIds: string[], dimension: DimensionDefinitionPO | SubDimensionDefinitionPO, params: Record<string, any>) {
    const { pageIndex, pageSize } = params;
    const query = await this.getDimensionQueryForBid(companyIds, dimension);
    const sort = {};
    if (dimension?.strategyModel?.sortField) {
      sort[dimension?.strategyModel?.sortField.field] = dimension?.strategyModel?.sortField.order;
    }

    const response = await this.searchEs(
      {
        from: (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize,
        size: pageSize || 10,
        sort,
        query,
      },
      companyIds[0],
    );
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }

  private async getDimensionQueryForBid(companyIds: string[], dimension: DimensionDefinitionPO | SubDimensionDefinitionPO): Promise<object> {
    const ids = companyIds;
    const subBool = {
      filter: [],
    };
    switch (dimension.key) {
      case DimensionLevel2Enums.BidAdministrativeJudgement: {
        subBool.filter.push({ term: { delflag: 0 } }, { terms: { casereasoncode: ['A030803'] } });
        // if (dimension.strategyModel?.cycle > 0) {
        //   const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
        //   subBool.filter.push({ range: { judgedate: { gte: Math.ceil(timestamp / 1000) } } });
        // }
        subBool['should'] = [];
        subBool['minimum_should_match'] = 1;
        subBool['should'].push({ terms: { defendant: ids } });
        subBool['should'].push({ terms: { othercns: ids } });
        return { bool: subBool };
      }
    }
    return { bool: subBool };
  }
}
