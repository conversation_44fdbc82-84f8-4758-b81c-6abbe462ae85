import { Injectable } from '@nestjs/common';
import { DimensionDefinitionPO, SubDimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { CreditAggBucketItemPO } from 'libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import { compact, difference, find, flatten, intersection, omit, uniq } from 'lodash';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { CreditEsMappingModel, creditSearchSourceMappings } from 'libs/constants/credit.analyze.constants';
import { getStartTimeByCycle } from 'libs/utils/utils';
import { getCreditMappingByDimension } from 'libs/utils/diligence/credit.utils';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { AbstractEsAnalyzeService } from './analyze.base.service';
import { DimensionScorePO } from '../../../libs/model/diligence/pojo/dimension/DimensionScorePO';
import { processScorePO } from '../../../libs/utils/diligence/diligence.utils';
import * as Bluebird from 'bluebird';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/pojo/req&res/details/response';
import { RoverExceptions } from '../../../libs/exceptions/exceptionConstants';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import { DimensionAnalyzeParamsPO } from '../DimensionAnalyzeParamsPO';
import { OvsSanctionsService } from './ovs.sanctions.service';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { DescriptionRegex } from '../../../libs/constants/common';

@Injectable()
export class OuterBlacklistService extends AbstractEsAnalyzeService {
  private expectedDimensionsTypes: DimensionTypeEnums[] = flatten(creditSearchSourceMappings.map((t) => t.dimensionType));

  constructor(private readonly configService: ConfigService, private readonly ovsSanctionsService: OvsSanctionsService) {
    super(
      OuterBlacklistService.name,
      new Client({
        nodes: configService.esConfig.blacklist.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.blacklist.indexName,
    );
  }

  getBidESCreditTypes() {
    return [
      // 涉采购黑名单
      DimensionLevel3Enums.GovernmentPurchaseIllegal,
      DimensionLevel3Enums.GovProcurementIllegal,
      DimensionLevel3Enums.ArmyProcurementIllegal,
      DimensionLevel3Enums.AIIBBlackList,
    ];
  }

  async analyze(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], params: DimensionAnalyzeParamsPO): Promise<DimensionScorePO[]> {
    try {
      const result: DimensionScorePO[] = [];
      if (!companyId || !dimensionDefinitionPOs?.length) {
        return [];
      }
      const diffTypes = difference(
        dimensionDefinitionPOs.map((m) => m.key),
        this.expectedDimensionsTypes,
      );
      if (diffTypes?.length) {
        this.logger.warn('passed dimension contains not expected ones, will remove unexpected keys');
        dimensionDefinitionPOs = dimensionDefinitionPOs.filter((po) => this.expectedDimensionsTypes.some((t) => t == po.key));
      }
      const dimension = find(dimensionDefinitionPOs, { key: DimensionLevel2Enums.HitOuterBlackList });
      const outerBlacklistDimension = Object.assign(new DimensionDefinitionPO(), omit(dimension, ['subDimensionList']), {
        subDimensionList: dimension.subDimensionList.filter((sub) => sub.key != DimensionLevel2Enums.ForeignExportControls && sub.status > 0),
      });
      const foreignBlacklist = Object.assign(new SubDimensionDefinitionPO(), omit(dimension, ['subDimensionList']), {
        subDimensionList: dimension.subDimensionList.filter((sub) => sub.key === DimensionLevel2Enums.ForeignExportControls && sub.status > 0),
      });
      if (outerBlacklistDimension?.subDimensionList?.length) {
        const response1 = await super.analyze(companyId, [outerBlacklistDimension], params);
        if (response1?.[0]) {
          result.push(...response1);
        }
      }
      if (result?.[0] && result[0].totalHits > 0) {
        const query = await super.getQuery(companyId, [outerBlacklistDimension], params);
        const size = Math.min(result[0].totalHits, 9999);
        const response = await this.searchEs({ size, query }, companyId);
        const data = response?.body?.hits?.hits?.map((d) => d._source);
        if (data.length > 0) {
          const typeList = uniq(data.map((x) => x.listtypecode.toString()));
          const levels = compact(
            outerBlacklistDimension.subDimensionList.map((x) => {
              if (x.status == 1 && intersection(typeList, x.child).length > 0) {
                return x.strategyModel.level;
              }
              return null;
            }),
          );
          if (levels.length > 0) {
            result[0].level = Math.max(...levels);
          }
        }
      }
      if (foreignBlacklist?.subDimensionList?.length) {
        //出口管制合规企业清单的level应该采用模型设置的，下面foreignBlacklistResult的level始终为2
        const foreignBlacklistResult: DimensionScorePO[] = await this.ovsSanctionsService.analyze(companyId, [foreignBlacklist], params);
        const level = foreignBlacklist?.subDimensionList?.[0]?.strategyModel?.level || DimensionRiskLevelEnum.High;
        if (result?.[0] && result[0].totalHits > 0 && foreignBlacklistResult?.[0]) {
          result[0].level = Math.max(result[0].level, level);
          result[0].totalHits += foreignBlacklistResult[0].totalHits;
          result[0].score += foreignBlacklistResult[0].score;
          const regex = DescriptionRegex;
          const match = regex.exec(result[0].description);
          if (match) {
            const M: number = result[0].totalHits;
            result[0].description = result[0].description.replace(regex, M.toString() + '条记录');
          }
        }
        if (!result?.[0] && foreignBlacklistResult?.[0]) {
          foreignBlacklistResult[0].level = level;
          Object.assign(result, foreignBlacklistResult);
        }
      }
      return result;
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }

  async getDimensionDetail(dimension: DimensionDefinitionPO, params: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const blacklistDimension = Object.assign(new DimensionDefinitionPO(), omit(dimension, ['subDimensionList']), {
      subDimensionList: dimension.subDimensionList.filter((sub) => sub.key != DimensionLevel2Enums.ForeignExportControls && sub.status > 0),
    });
    const tempParams = Object.assign(new HitDetailsBaseQueryParams(), params, {
      pageSize: 200,
      pageIndex: 1,
    });
    const blacklistResp: HitDetailsBaseResponse = await super.getDimensionDetail(blacklistDimension, tempParams);
    const hitDetailsBaseResponse = this.transFormEsData(blacklistResp, blacklistDimension);
    const foreignBlacklist = Object.assign(new SubDimensionDefinitionPO(), omit(dimension, ['subDimensionList']), {
      subDimensionList: dimension.subDimensionList.filter((sub) => sub.key === DimensionLevel2Enums.ForeignExportControls && sub.status > 0),
    });
    if (foreignBlacklist.subDimensionList.length) {
      const foreignBlacklistResp: HitDetailsBaseResponse = await this.ovsSanctionsService.getDimensionDetail(foreignBlacklist, tempParams);
      if (foreignBlacklistResp?.Result?.length) {
        hitDetailsBaseResponse.Result.push(...foreignBlacklistResp.Result);
      }
    }
    //内存分页
    const start = (params.pageIndex - 1) * params.pageSize;
    const end = start + params.pageSize;
    hitDetailsBaseResponse.Paging.PageIndex = params.pageIndex;
    hitDetailsBaseResponse.Paging.PageSize = params.pageSize;
    hitDetailsBaseResponse.Paging.TotalRecords = hitDetailsBaseResponse.Result.length;
    hitDetailsBaseResponse.Result = hitDetailsBaseResponse.Result.slice(start, end);
    return hitDetailsBaseResponse;
  }

  /**
   * 外部黑名单es数据转换
   * @param resp
   * @param dimension
   * @private
   */
  private transFormEsData(resp: HitDetailsBaseResponse, dimension: DimensionDefinitionPO | SubDimensionDefinitionPO) {
    if (resp?.Result?.length) {
      const blackItemMap = dimension?.subDimensionList?.length
        ? dimension.subDimensionList.reduce((acc, cur) => {
            cur.child?.forEach((child) => {
              acc[child] = cur;
            });
            return acc;
          }, {})
        : dimension.child?.reduce((acc, cur) => {
            acc[cur] = dimension;
            return acc;
          }, {});
      //优化下面的逻辑，减少循环次数
      resp.Result.forEach((source) => {
        const publishTypeStr = String(source.listtypecode);
        const blackItem = blackItemMap[publishTypeStr];
        if (blackItem) {
          Object.assign(source, {
            NameAndKeyNo: source?.nameandkeyno ? JSON.parse(source.nameandkeyno) : [],
            Name: source.compkeyno ? JSON.parse(source.compkeyno).Name : '',
            KeyNo: source.compkeyno ? JSON.parse(source.compkeyno).KeyNo : '',
            CaseReasonType: source.listtype, //命中黑名单类型
            CaseReason: source.decisionreason, //命中黑名单原因
            Court: source.decisionoffice, //列入机关
            Id: source.id,
            level: blackItem.strategyModel.level,
            Publishdate: source?.decisiondate, //在kys_blacklist_query 索引内，decisiondate字段表示列入黑名单的时间，与发布时间不同，需要单独处理
            dimensionKey: blackItem.key,
            dimensionName: blackItem.name,
          });
        }
      });
    }
    return resp;
  }

  protected async createAggs(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], params: Record<string, any>) {
    const aggs: any = {};
    await Bluebird?.map(dimensionDefinitionPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po, params);
      if (dimQuery) {
        const aggsName = `${this.bucketNamePrefix}${po.key}`;
        aggs[aggsName] = {
          filter: dimQuery,
          aggs: {},
        };
        const blacklistSettingItems = po.subDimensionList.filter((sub) => sub.status > 0);
        // 需要按照风险等级分别统计，维度的风险等级取最高的那个
        Object.keys(DimensionRiskLevelEnum)
          .filter((level) => isNaN(Number(level)))
          .forEach((level) => {
            const outerBlackPublishTypesLevel = flatten(
              blacklistSettingItems.filter((item) => item.strategyModel.level == DimensionRiskLevelEnum[level] && !!item.child).map((item) => item.child),
            );
            if (outerBlackPublishTypesLevel.length > 0) {
              aggs[aggsName].aggs[`agg_${level}`] = { filter: { terms: { publishtype: outerBlackPublishTypesLevel } } };
            }
          });
      }
    });
    return aggs;
  }

  protected processAggs(aggObj: any): CreditAggBucketItemPO[] {
    const bucketData: CreditAggBucketItemPO[] = [];
    Object.keys(aggObj).forEach((bucketName) => {
      const dimensionType = bucketName.replace(this.bucketNamePrefix, '') as DimensionTypeEnums;
      const bucket = aggObj[bucketName];
      const esMappingModel: CreditEsMappingModel = getCreditMappingByDimension(dimensionType);
      if (esMappingModel) {
        const hitCount = bucket['doc_count'];
        if (hitCount > 0) {
          const res: CreditAggBucketItemPO = {
            dimensionType,
            hitCount,
          };
          Object.keys(DimensionRiskLevelEnum)
            .filter((level) => isNaN(Number(level)))
            .sort((a, b) => DimensionRiskLevelEnum[a] - DimensionRiskLevelEnum[b])
            .forEach((level) => {
              if (bucket[`agg_${level}`] && bucket[`agg_${level}`]['doc_count'] > 0) {
                res.level = DimensionRiskLevelEnum[level];
              }
            });
          bucketData.push(res);
        }
      }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: CreditAggBucketItemPO[], dimensionDefinitionPOs: DimensionDefinitionPO[]): DimensionScorePO[] {
    return bucketData
      .map((item) => {
        const d: DimensionDefinitionPO = find(dimensionDefinitionPOs, { key: item.dimensionType });
        const desData = {
          level: item.level || d.strategyModel.level || 0,
          cycle: d.strategyModel?.cycle > 0 ? d.strategyModel.cycle : 0,
          name: d.name,
          isHidden: '',
          isHiddenY: '',
        };
        const { hitCount } = item;
        const dimensionScorePO = processScorePO(d, hitCount, desData);
        dimensionScorePO.level = desData.level;
        return dimensionScorePO;
      })
      .filter((t) => t);
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionDefinitionPO, params: Record<string, any>): Promise<object> {
    const { keyNo } = params;
    const subBool = {
      bool: {
        filter: [],
        must: [],
      },
    };
    const datastatus = '1';
    const blacklistSettingItems = dimension.subDimensionList.filter((sub) => sub.status > 0);
    const outerBlackPublishTypes: string[] = flatten(blacklistSettingItems.filter((it) => !!it.child).map((item) => item.child));
    subBool.bool.filter.push({
      terms: {
        listtypecode: outerBlackPublishTypes,
      },
    });
    subBool.bool.filter.push({
      terms: { authoritycode: [2, 3] },
    });
    subBool.bool.filter.push({
      term: {
        datastatus,
      },
    });
    if (keyNo) {
      subBool.bool.must.push({
        multi_match: { query: keyNo, type: 'phrase', fields: ['compname'] },
      });
    }
    return subBool;
  }

  async getDimensionDetailForBid(
    dimension: DimensionDefinitionPO | SubDimensionDefinitionPO,
    requestData: Record<string, any>,
  ): Promise<HitDetailsBaseResponse> {
    const { keyNos, pageSize, pageIndex } = requestData;
    this.logger.info(`get hit details for dimension: ${dimension.key}`);
    if (!requestData?.keyNos?.length) {
      throw new BadParamsException(RoverExceptions.Diligence.Detail.NeedKeyNo);
    }
    //查询参数使用优先级： 1. data 参数 2. dimension.strategyModel 如果有值，需要生成条件
    //根据key获取到category，拼接到data中

    if (this.getBidESCreditTypes().some((t) => t === dimension.key)) {
      const { total, data } = await this.getDetailFromEsForBid(keyNos, dimension, requestData);
      //适配旧数据结构
      if (data) {
        return this.transFormEsData(
          Object.assign(HitDetailsBaseResponse.ok(), {
            Paging: {
              PageSize: pageSize,
              PageIndex: pageIndex,
              TotalRecords: total,
            },
            Result: data,
            GroupItems: [],
          }),
          dimension,
        );
      }
    } else {
      return HitDetailsBaseResponse.failed(RoverExceptions.BadParams.Common.error, DimensionSourceEnums.Credit, RoverExceptions.BadParams.Common.code);
    }
  }

  private async getDetailFromEsForBid(companyIds: string[], dimension: DimensionDefinitionPO | SubDimensionDefinitionPO, params: Record<string, any>) {
    const { pageIndex, pageSize } = params;
    const query = await this.getDimensionQueryForBid(companyIds, dimension);
    const sort = {};
    if (dimension?.strategyModel?.sortField) {
      sort[dimension?.strategyModel?.sortField.field] = dimension?.strategyModel?.sortField.order;
    }
    const response = await this.searchEs(
      {
        from: (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize,
        size: pageSize || 10,
        sort,
        query,
      },
      companyIds[0],
    );
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }

  private async getDimensionQueryForBid(companyIds: string[], dimension: DimensionDefinitionPO | SubDimensionDefinitionPO): Promise<object> {
    const { strategyModel } = dimension;
    const subBool = {
      bool: {
        must: [],
        filter: [],
      },
    };
    const esMappingModel = getCreditMappingByDimension(dimension.key);
    if (esMappingModel) {
      const ids = companyIds;
      const outerBlackPublishType = dimension.child;
      if (outerBlackPublishType?.length) {
        subBool.bool.filter.push({
          terms: {
            listtypecode: [...outerBlackPublishType],
          },
        });
      }
      if (ids?.length) {
        subBool.bool.filter.push({
          terms: { ['compname.keyword']: ids },
        });
      }
      subBool.bool.filter.push({
        term: {
          datastatus: 1,
        },
      });
      const startTimestamp = getStartTimeByCycle(strategyModel?.cycle || 3);
      if (strategyModel?.cycle > 0) {
        subBool.bool.must.push({
          range: {
            decisiondate: {
              gte: Math.ceil(startTimestamp / 1000),
            },
          },
        });
      }
      return subBool;
    }
    return null;
  }

  async getBlacklistDetails(sourceIds: string[], keyNos: string[], pageSize: number, pageIndex: number, sortField: string, isSortAsc: boolean) {
    const query = {
      bool: {
        filter: [],
      },
    };
    query.bool.filter.push({
      terms: {
        id: sourceIds,
      },
    });
    const sort = {};
    if (sortField && ['decisiondate', 'removedate'].includes(sortField)) {
      sort[sortField] = isSortAsc ? 'asc' : 'desc';
    }
    const response = await this.searchEs(
      {
        from: (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize,
        size: pageSize || 10,
        query,
        sort,
      },
      keyNos[0],
    );
    const data = response?.body?.hits?.hits?.map((d) => d._source) || [];
    const total = response?.body?.hits?.total?.value || 0;
    if (data.length > 0) {
      data.forEach((source) => {
        Object.assign(source, {
          NameAndKeyNo: source?.nameandkeyno ? JSON.parse(source.nameandkeyno) : [],
          Name: source.compkeyno ? JSON.parse(source.compkeyno).Name : '',
          KeyNo: source.compkeyno ? JSON.parse(source.compkeyno).KeyNo : '',
          CaseReasonType: source.listtype, //命中黑名单类型
          CaseReason: source.decisionreason, //命中黑名单原因
          Court: source.decisionoffice, //列入机关
          Id: source.id,
          Publishdate: source?.decisiondate, //在kys_blacklist_query 索引内，decisiondate字段表示列入黑名单的时间，与发布时间不同，需要单独处理
        });
      });
    }

    return Object.assign(HitDetailsBaseResponse.ok(), {
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: total,
      },
      Result: data,
    });
  }
}
