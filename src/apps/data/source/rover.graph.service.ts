import { RedisService } from '@kezhaozhao/nestjs-redis';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as Bluebird from 'bluebird';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { DimensionLevelClass } from 'libs/constants/dimension.constants';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { BiddingDimensionHitsDetails } from 'libs/model/bidding/DiligenceBiddingResponse';
import { DimensionDefinitionPO, SubDimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionScorePO } from 'libs/model/diligence/pojo/dimension/DimensionScorePO';
import { BlacklistConnectionDDResponse } from 'libs/model/diligence/pojo/graph/BlacklistConnectionDDResponse';
import { PartnerConnectionDDResponse } from 'libs/model/diligence/pojo/graph/PartnerConnectionDDResponse';
import { HitDetailsBaseResponse } from 'libs/model/diligence/pojo/req&res/details/response';
import { dateTransform } from 'libs/utils/date.utils';
import { processScorePO } from 'libs/utils/diligence/diligence.utils';
import { compact, flatten, intersection, merge, pick, remove, uniq, uniqBy } from 'lodash';
import { Logger } from 'log4js';
import { Brackets, Repository } from 'typeorm';
import { RelationTypeConst } from '../../../libs/constants/model.constants';
import { CustomerEntity } from '../../../libs/entities/CustomerEntity';
import { InnerBlacklistEntity } from '../../../libs/entities/InnerBlacklistEntity';
import { StreamOperationEnum } from '../../../libs/enums/data/StreamOperationEnum';
import { StreamTableEnums } from '../../../libs/enums/data/StreamTableEnums';
import { ConditionOperatorEnums } from '../../../libs/enums/diligence/ConditionOperatorEnums';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { getArrByPage, TenderInnerBlackListResPo } from '../../../libs/model/bidding/model/TenderInnerBlackListResPo';
import { CompanyInvestigationPo } from '../../../libs/model/company/CompanyInvestigationPo';
import { GroupCompanyRelationsRequest } from '../../../libs/model/company/GroupCompanyRelationsRequest';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { ConditionValPO, DimensionQueryPO } from '../../../libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { ActualControllerConnectionPO } from '../../../libs/model/diligence/pojo/graph/ActualControllerConnectionPO';
import { RoverGraphAnalyzeResponsePO } from '../../../libs/model/diligence/pojo/graph/RoverGraphAnalyzeResponsePO';
import { RoverGraphParsedPO } from '../../../libs/model/diligence/pojo/graph/RoverGraphParsedPO';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { HitDetailsBidBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request/HitDetailsBidBaseQueryParams';
import { getTransV2Detail, parseRelationsData } from '../../batch/message.handler/export/model/path-node/data-node-utils';
import {
  CompanyDeepInvestBranchParamsPO,
  CompanyDeepRelationsParamsPO,
  DimensionAnalyzeParamsPO,
  PersonDeepRelationsParamsPO,
} from '../DimensionAnalyzeParamsPO';
import { RoverGraphHelper } from '../helper/rover.graph.helper';
import { AnalyzeService } from './analyze.base.service';
import { RoverService } from './rover.service';
import { DataRangeConditionPO } from 'libs/model/company/DataRangeConditionPO';

@Injectable()
export class RoverGraphService implements AnalyzeService<HitDetailsBaseQueryParams, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(RoverGraphService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly httpUtilsService: HttpUtilsService,
    private readonly roverService: RoverService,
    private readonly roverGraphHelper: RoverGraphHelper,
    @InjectRepository(InnerBlacklistEntity) private readonly innerBlacklistRepo: Repository<InnerBlacklistEntity>,
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
  ) {
    //useAdapter(redisService.getClient());
  }

  /**
   * 查询投资(控股为100的股东)以及分支机构的母公司
   * @param params
   * @returns
   */
  async getCompanyDeepInvestBranch(params: CompanyDeepInvestBranchParamsPO) {
    return await this.httpUtilsService.postRequest(this.configService.roverGraphServer.companyDeepInvestBranch, params);
  }

  /**
   * 查询一对多公司关系
   * @param params
   * @returns
   */
  async getCompanyDeepRelations(params: CompanyDeepRelationsParamsPO) {
    return await this.httpUtilsService.postRequest(this.configService.roverGraphServer.companyDeepRelations, params);
  }

  /**
   * 找出一个人和一组公司之间的关系
   * @param params
   * @returns
   */
  async getPersonDeepRelations(params: PersonDeepRelationsParamsPO) {
    return await this.httpUtilsService.postRequest(this.configService.roverGraphServer.personRelations, params);
  }

  /**
   * 查询子公司列表
   * @param companyId
   * @param depth
   * @param percentage
   * @returns
   */
  async getChildCompanyList(companyId: string, depth: number, percentage: number): Promise<any> {
    const params = {
      depth,
      percentage,
      relationType: 'MajorityInvestment',
      startCompanyId: companyId,
    };
    return await this.httpUtilsService.postRequest(this.configService.roverGraphServer.companyDeepRelatedParty, params);
  }

  async getParentCompanyList(companyId: string, depth: number, percentage: number): Promise<any> {
    const params = {
      depth,
      percentage,
      relationType: 'MotherCompanyMajorityShareholder',
      startCompanyId: companyId,
    };
    return await this.httpUtilsService.postRequest(this.configService.roverGraphServer.companyDeepRelatedParty, params);
  }

  async analyzePartnerAndBlackList(
    companyId: string,
    dimensionDefinitionPOs: DimensionDefinitionPO[],
    params: DimensionAnalyzeParamsPO,
  ): Promise<DimensionScorePO[]> {
    const { orgId, userId } = params;
    const promiseArray = [];
    dimensionDefinitionPOs.forEach((d) => {
      promiseArray.push(this.getResultsV2(orgId, userId, companyId, d));
    });
    const promiseRes = await Bluebird.all(promiseArray);
    const totalHit = compact(
      promiseRes?.map((p) => {
        if (p) {
          const { partner, blacklist } = p;
          return merge({}, partner, blacklist);
        }
      }),
    );
    return dimensionDefinitionPOs
      .map((d, index) => {
        const desData = {
          level: DimensionLevelClass[d.strategyModel.level || 0],
          cycle: d.strategyModel?.cycle > 0 ? d.strategyModel.cycle : 0,
          name: d.name,
          isHidden: '',
          isHiddenY: '',
        };
        if (totalHit[index][d.key]?.length && !totalHit[index]['version']) {
          return processScorePO(d, totalHit[index][d.key]?.length || 0, desData);
        }
        return null;
      })
      .filter((t) => t);
  }

  async analyzePartnerAndBlackListV2(
    companyId: string,
    dimensionDefinitionPOs: DimensionDefinitionPO[],
    params: DimensionAnalyzeParamsPO,
  ): Promise<DimensionScorePO[]> {
    if (!dimensionDefinitionPOs?.length) {
      return [];
    }
    const { orgId, userId } = params;
    const results = await Promise.all(
      dimensionDefinitionPOs.map(async (dimension) => {
        const dimensionTypes: ConditionValPO[] = dimension.strategyModel?.detailsParams?.find((e) => e.field === QueryParamsEnums.types)?.fieldVal || [];
        const usefulDimensionTypes: ConditionValPO[] = dimensionTypes.filter((d) => d.status > 0 && d.key !== DetailsParamEnums.ShareholdingRatio);
        if (!usefulDimensionTypes.length) {
          return null;
        }
        const types = usefulDimensionTypes.map((u) => u.key);
        const queryPO = dimension.strategyModel.detailsParams?.find((d) => d.field === QueryParamsEnums.dataRange);
        // 设置 depth默认值为 1
        const depth = dimension.strategyModel.detailsParams?.find((d) => d.field === QueryParamsEnums.depth)?.fieldVal || 1;
        const percentage = dimensionTypes.find((d) => d.key === DetailsParamEnums.ShareholdingRatio)?.value ?? 0.01;
        const investParam = new CompanyInvestigationPo(orgId, companyId, depth, percentage, types);
        const excludedTypes = dimension.strategyModel.detailsParams?.find((x) => x.field === QueryParamsEnums.excludedTypes)?.fieldVal;
        const rangeCompanyIds = await this.getRangeCompanyIds(dimension, orgId, userId, companyId, queryPO);

        if (!rangeCompanyIds.length) {
          return null;
        }
        let totalHit = 0;

        switch (dimension.key) {
          case DimensionLevel2Enums.CustomerPartnerInvestigation: {
            totalHit = await this.roverGraphHelper.getCustomerInvestigationCount({
              ...investParam,
              rangeCompanyIds,
              excludedTypes,
            });
            break;
          }
          case DimensionLevel2Enums.BlacklistPartnerInvestigation: {
            totalHit = await this.roverGraphHelper.getBlacklistInvestigationCount({
              ...investParam,
              rangeCompanyIds,
              excludedTypes,
            });
            break;
          }
        }
        if (!totalHit) {
          return null;
        }
        const desData = {
          level: DimensionLevelClass[dimension.strategyModel.level || 0],
          cycle: dimension.strategyModel?.cycle > 0 ? dimension.strategyModel.cycle : 0,
          name: dimension.name,
          isHidden: '',
          isHiddenY: '',
        };
        return processScorePO(dimension, totalHit, desData);
      }),
    );

    return results.filter(Boolean);
  }

  async analyze(
    companyId: string,
    dimensionDefinitionPOs: DimensionDefinitionPO[],
    params: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<DimensionScorePO[]> {
    const suspectDimensionArr = [];
    const promiseDimensionArr = [];
    dimensionDefinitionPOs.forEach((d) => {
      if (d.key === DimensionLevel2Enums.CustomerSuspectedRelation || d.key === DimensionLevel2Enums.BlacklistSuspectedRelation) {
        suspectDimensionArr.push(d);
      } else {
        promiseDimensionArr.push(d);
      }
    });
    const [suspectRes, promiseRes] = await Bluebird.all([
      this.analyzeSuspectedRelation(companyId, suspectDimensionArr, params),
      this.analyzePartnerAndBlackList(companyId, promiseDimensionArr, params),
    ]);
    return [...suspectRes, ...promiseRes];
  }

  async getDimensionDetail(dimension: DimensionDefinitionPO, params: Record<string, any>): Promise<HitDetailsBaseResponse> {
    const { orgId, userId, keyNo, pageIndex, pageSize } = params;
    const { partner, blacklist } = await this.getResultsV2(orgId, userId, keyNo, dimension);
    const totalHit = merge({}, partner, blacklist);
    if (totalHit) {
      return this.returnHitDetailsBaseResponse(totalHit[dimension.key], pageSize, pageIndex);
    }

    return new HitDetailsBaseResponse();
  }

  getPartnerTypes() {
    return [DimensionLevel2Enums.InvestorsRelationship, DimensionLevel2Enums.ShareholdingRelationship, DimensionLevel2Enums.ServeRelationship];
  }

  getBlacklistTypes() {
    return [
      DimensionLevel2Enums.HitInnerBlackList,
      DimensionLevel2Enums.Shareholder,
      DimensionLevel2Enums.ForeignInvestment,
      DimensionLevel2Enums.EmploymentRelationship,
    ];
  }

  async getDimensionDetailsByKeyNos(orgId: number, companyIds: string[], dimension: DimensionDefinitionPO, data: Record<string, any>) {
    const { pageIndex, pageSize } = data;
    const { blacklist } = await this.getResultsForBidding(orgId, companyIds, [dimension]);
    const totalHit = merge({}, blacklist);
    if (totalHit) {
      const responseData = totalHit[dimension.key];
      return this.returnHitDetailsBaseResponse(responseData, pageSize, pageIndex);
    }
    return new HitDetailsBaseResponse();
  }

  /**
   * 招标排查扫描内部黑名单风险
   * @param orgId
   * @param companyIds
   * @param pageSize
   * @param pageIndex
   * @param dimensions
   * @returns
   */
  async scanForBidding(
    orgId: number,
    companyIds: string[],
    pageSize: number,
    pageIndex: number,
    dimensions: SubDimensionDefinitionPO[],
  ): Promise<BiddingDimensionHitsDetails[]> {
    const { blacklist: totalHit } = await this.getResultsForBidding(orgId, companyIds, dimensions);
    const result: BiddingDimensionHitsDetails[] = [];
    for await (const d of dimensions) {
      const subDetail = Object.assign(new BiddingDimensionHitsDetails(), pick(d, ['key', 'name', 'status', 'sort']), {
        totalHits: 0,
      });
      if (totalHit[d.key]?.length) {
        //const totalHits = totalHit[d.key].length;
        const data = await this.returnHitDetailsBaseResponse(totalHit[d.key], pageSize, pageIndex);
        subDetail.totalHits = data?.Paging?.TotalRecords;
        subDetail.data = data;
      }
      result.push(subDetail);
    }
    return result;
  }

  async syncToNebula(orgId: number, ids: number[], table: StreamTableEnums, operation: StreamOperationEnum) {
    await this.roverGraphHelper.syncToNebula(orgId, ids, table, operation);
  }

  async getCompanyRelations(orgId: number, request: GroupCompanyRelationsRequest) {
    request.orgId = orgId;
    if (!request?.companyIds?.length && request.keyNoAndNames?.length) {
      request.companyIds = request.keyNoAndNames.map((e) => e.companyId);
    }
    try {
      return await this.httpUtilsService.postRequest(this.configService.roverGraphServer.relations, {
        ...request,
      });
    } catch (e) {
      this.logger.error('get company relations throw exception:' + e.message || e);
      this.logger.error(e);
    }
  }

  private async getResults(orgId: number, companyId: string, dimension: DimensionDefinitionPO): Promise<RoverGraphAnalyzeResponsePO> {
    const promiseArray = [];
    let isValid = '1';
    if (intersection([dimension.key], this.getPartnerTypes()).length) {
      isValid = dimension?.strategyModel?.detailsParams.find((e) => e.field === QueryParamsEnums.isValid)?.fieldVal;
      promiseArray.push(this.roverGraphHelper.scanPartner(orgId, companyId, isValid));
    } else {
      promiseArray.push(Promise.resolve(null));
    }
    if (intersection([dimension.key], this.getBlacklistTypes()).length) {
      isValid = dimension?.strategyModel?.detailsParams.find((e) => e.field === QueryParamsEnums.isValid)?.fieldVal;
      promiseArray.push(this.roverGraphHelper.scanInnerBlacklistV2(orgId, companyId, dimension));
    } else {
      promiseArray.push(Promise.resolve(null));
    }
    const [result1, result2] = await Bluebird.all(promiseArray);
    const partnerHit = result1 ? (result1 as PartnerConnectionDDResponse) : null;
    const blacklistHit = result2 ? (result2 as BlacklistConnectionDDResponse) : null;
    return {
      partner: partnerHit ? this.roverGraphHelper.parseGraphPartnerResponse(result1) : null,
      blacklist: blacklistHit ? this.roverGraphHelper.parseGraphBlacklistResponse(blacklistHit) : null,
    };
  }

  public async getResultsV2(orgId: number, userId: number, companyId: string, dimension: DimensionDefinitionPO): Promise<RoverGraphAnalyzeResponsePO> {
    if (!intersection([dimension.key], this.getPartnerTypes()).length && !intersection([dimension.key], this.getBlacklistTypes()).length) {
      const dimensionTypes: ConditionValPO[] = dimension.strategyModel?.detailsParams?.find((e) => e.field === QueryParamsEnums.types)?.fieldVal;
      const usefulDimensionTypes: ConditionValPO[] = dimensionTypes?.filter((d) => d.status > 0);
      const response = { partner: null, blacklist: null };
      if (usefulDimensionTypes?.length === 0) {
        return response;
      }
      //持股比例设置值,选中对外投资、参股股东、对外投资（历史）、参股股东（历史）其中任意一个时生效
      return this.getResultsWithRangeCondition(orgId, userId, companyId, dimension);
    } else {
      return this.getResults(orgId, companyId, dimension);
    }
  }

  /**
   * 支持穿透查询交叉重叠关联
   * @param orgId
   * @param userId
   * @param companyId
   * @param dimension
   */
  public async getResultsV3(orgId: number, userId: number, companyId: string, dimension: DimensionDefinitionPO): Promise<RoverGraphAnalyzeResponsePO> {
    const response = Object.assign(new RoverGraphAnalyzeResponsePO(), { version: 'V2' });
    const dimensionTypes: ConditionValPO[] = dimension.strategyModel?.detailsParams?.find((e) => e.field === QueryParamsEnums.types)?.fieldVal;

    const usefulDimensionTypes: ConditionValPO[] = dimensionTypes?.filter((d) => d.status > 0 && d.key !== DetailsParamEnums.ShareholdingRatio);

    if (!usefulDimensionTypes?.length) {
      return response;
    }
    const types = usefulDimensionTypes.map((u) => u.key);
    const queryPO = dimension.strategyModel.detailsParams?.find((d) => d.field === QueryParamsEnums.dataRange);
    //设置 depth默认值为 1
    const depth = dimension.strategyModel.detailsParams?.find((d) => d.field === QueryParamsEnums.depth)?.fieldVal || 1;
    const percentage = dimensionTypes?.find((d) => d.key === DetailsParamEnums.ShareholdingRatio)?.value ?? 0.01;
    const investParam = new CompanyInvestigationPo(orgId, companyId, depth, percentage, types);
    const excludedTypes = dimension.strategyModel.detailsParams.find((x) => x.field === QueryParamsEnums.excludedTypes)?.fieldVal;

    const rangeCompanyIds = await this.getRangeCompanyIds(dimension, orgId, userId, companyId, queryPO);
    if (rangeCompanyIds.length > 0) {
      if (dimension.key === DimensionLevel2Enums.CustomerPartnerInvestigation) {
        const dimensionList = await this.roverGraphHelper.getCustomerInvestigation({
          ...investParam,
          rangeCompanyIds,
          excludedTypes,
        });
        response.partner = Object.assign(new RoverGraphParsedPO(), { [DimensionLevel2Enums.CustomerPartnerInvestigation]: dimensionList });
      } else if (dimension.key === DimensionLevel2Enums.BlacklistPartnerInvestigation) {
        const dimensionList = await this.roverGraphHelper.getBlacklistInvestigation({
          ...investParam,
          rangeCompanyIds,
          excludedTypes,
        });
        response.blacklist = Object.assign(new RoverGraphParsedPO(), { [DimensionLevel2Enums.BlacklistPartnerInvestigation]: dimensionList });
      }
    }
    return response;
  }

  /**
   * 将范围条件直接传到图服务中进行查询
   * @param orgId
   * @param userId
   * @param companyId
   * @param dimension
   * @returns
   */
  public async getResultsWithRangeCondition(
    orgId: number,
    userId: number,
    companyId: string,
    dimension: DimensionDefinitionPO,
  ): Promise<RoverGraphAnalyzeResponsePO> {
    const response = Object.assign(new RoverGraphAnalyzeResponsePO(), { version: 'V2' });
    const dimensionTypes: ConditionValPO[] = dimension.strategyModel?.detailsParams?.find((e) => e.field === QueryParamsEnums.types)?.fieldVal;

    const usefulDimensionTypes: ConditionValPO[] = dimensionTypes?.filter((d) => d.status > 0 && d.key !== DetailsParamEnums.ShareholdingRatio);

    if (!usefulDimensionTypes?.length) {
      return response;
    }
    const types = usefulDimensionTypes.map((u) => u.key);
    const queryPO = dimension.strategyModel.detailsParams?.find((d) => d.field === QueryParamsEnums.dataRange);
    //设置 depth默认值为 1
    const depth = dimension.strategyModel.detailsParams?.find((d) => d.field === QueryParamsEnums.depth)?.fieldVal || 1;
    const percentage = dimensionTypes?.find((d) => d.key === DetailsParamEnums.ShareholdingRatio)?.value ?? 0.01;
    const investParam = new CompanyInvestigationPo(orgId, companyId, depth, percentage, types);
    const excludedTypes = dimension.strategyModel.detailsParams.find((x) => x.field === QueryParamsEnums.excludedTypes)?.fieldVal;

    const dataRangeCondition = await this.getRangeCondition(dimension, orgId, userId, companyId, queryPO);
    this.logger.info(`getResultsWithRangeCondition orgId:${orgId} companyId:${companyId}, dataRangeCondition:${JSON.stringify(dataRangeCondition)}`);
    if (dimension.key === DimensionLevel2Enums.CustomerPartnerInvestigation) {
      const dimensionList = await this.roverGraphHelper.getCustomerInvestigationV2({
        ...investParam,
        excludedTypes,
        dataRangeCondition,
      });
      response.partner = Object.assign(new RoverGraphParsedPO(), { [DimensionLevel2Enums.CustomerPartnerInvestigation]: dimensionList });
    } else if (dimension.key === DimensionLevel2Enums.BlacklistPartnerInvestigation) {
      const dimensionList = await this.roverGraphHelper.getBlacklistInvestigationV2({
        ...investParam,
        dataRangeCondition,
        excludedTypes,
      });
      response.blacklist = Object.assign(new RoverGraphParsedPO(), { [DimensionLevel2Enums.BlacklistPartnerInvestigation]: dimensionList });
    }
    return response;
  }

  async getRangeCompanyIds(dimension: DimensionDefinitionPO, orgId: number, userId: number, companyId: string, queryPO: any) {
    let companyIds = [];
    const conditionOperator = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.conditionOperator)?.value;
    switch (dimension.key) {
      case DimensionLevel2Enums.CustomerSuspectedRelation:
      case DimensionLevel2Enums.CustomerPartnerInvestigation: {
        const { groupIds, labelIds, depIds } = await this.roverGraphHelper.getRoverRanges(queryPO, orgId, userId, companyId);
        if (conditionOperator === ConditionOperatorEnums.And) {
          companyIds = await this.roverGraphHelper.searchCustomerWithAllConditions(orgId, groupIds, labelIds, depIds);
        } else {
          companyIds = await this.roverGraphHelper.searchCustomerWithAnyCondition(orgId, groupIds, labelIds, depIds);
        }
        break;
      }
      case DimensionLevel2Enums.BlacklistSuspectedRelation:
      case DimensionLevel2Enums.BlacklistPartnerInvestigation: {
        const groupIds0 = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Groups)?.value;
        const labelIds0 = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Labels)?.value;
        const { groupIds, labelIds } = await this.roverGraphHelper.getBlacklistGroupLabelIds(orgId, groupIds0, labelIds0);
        if (conditionOperator === ConditionOperatorEnums.And) {
          companyIds = await this.roverGraphHelper.searchBlackListWithAllConditions(orgId, groupIds, labelIds);
        } else {
          companyIds = await this.roverGraphHelper.searchBlackListWithAnyCondition(orgId, groupIds, labelIds);
        }
        break;
      }
      default:
        return [];
    }
    return companyIds.filter((x) => x !== companyId);
  }

  /**
   * 获取范围条件
   * @param dimension
   * @param orgId
   * @param userId
   * @param key
   * @param queryPO
   * @returns
   */
  private async getRangeCondition(dimension: DimensionDefinitionPO, orgId: number, userId: number, key: string, queryPO: any): Promise<DataRangeConditionPO> {
    const conditionOperator = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.conditionOperator)?.value;
    switch (dimension.key) {
      case DimensionLevel2Enums.CustomerSuspectedRelation:
      case DimensionLevel2Enums.CustomerPartnerInvestigation: {
        const { groupIds, labelIds, depIds } = await this.roverGraphHelper.getRoverRanges(queryPO, orgId, userId, key);
        return { groupIds, labelIds, departmentIds: depIds, conditionOperator };
      }
      case DimensionLevel2Enums.BlacklistSuspectedRelation:
      case DimensionLevel2Enums.BlacklistPartnerInvestigation: {
        const groupIds0 = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Groups)?.value;
        const labelIds0 = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Labels)?.value;
        const { groupIds, labelIds } = await this.roverGraphHelper.getBlacklistGroupLabelIds(orgId, groupIds0, labelIds0);
        return { groupIds, labelIds, departmentIds: [], conditionOperator };
      }
      default:
        return new DataRangeConditionPO();
    }
  }

  /**
   * 招标排查内部黑名单, 通过keynos 匹配内部黑名单
   * @param orgId
   * @param companyIds
   * @param dimensions
   * @returns
   */
  private async getResultsForBidding(orgId: number, companyIds: string[], dimensions: SubDimensionDefinitionPO[]): Promise<RoverGraphAnalyzeResponsePO> {
    const promiseArray = [];
    const promiseArray3 = [];
    let isValid = '1';
    dimensions.forEach((dimension) => {
      isValid = dimension?.strategyModel?.detailsParams?.find((e) => e.field === QueryParamsEnums.isValid).fieldVal || '1';
      switch (dimension.key) {
        case DimensionLevel2Enums.BlacklistSameSuspectedActualController: {
          promiseArray3.push(this.roverService.getBlacklistSameSuspectedAcData(orgId, companyIds, 100, 1));
          break;
        }
        case DimensionLevel2Enums.EmploymentRelationship: {
          promiseArray.push(this.roverGraphHelper.scanInnerBlacklistBatch(orgId, companyIds, isValid));
          break;
        }
        case DimensionLevel2Enums.HitInnerBlackList: {
          promiseArray.push(this.roverGraphHelper.scanDirectBatch(orgId, companyIds, isValid));
          break;
        }
        case DimensionLevel2Enums.Shareholder:
        case DimensionLevel2Enums.ForeignInvestment: {
          promiseArray.push(this.roverGraphHelper.scanInvestBatch(orgId, companyIds, isValid));
          break;
        }
        case DimensionLevel2Enums.CompanyBranch: {
          promiseArray.push(this.roverGraphHelper.scanBlacklistBranchBatch(orgId, companyIds));
          break;
        }
      }
    });
    const result: BlacklistConnectionDDResponse[] = await Bluebird.all(promiseArray);
    const result3: HitDetailsBaseResponse[] = await Bluebird.all(promiseArray3);
    if (!result && !compact(result3)) {
      return { blacklist: null };
    }
    const blacklistHit: BlacklistConnectionDDResponse = {
      directConnection: [],
      personConnections: [],
      shareholderConnections: [],
      actualControllerConnections: [],
      branchConnections: [],
    };
    const uniqPersonConnections = uniqBy(
      compact(flatten(result?.map((r) => r?.personConnections))),
      (x) => x.blacklistId + x.personKeyno + x.companyKeynoDD + x.companyKeynoRelated,
    );
    const uniqDirectConnection = uniqBy(
      compact(flatten(result?.map((r) => r?.directConnection))),
      (x) => x.blacklistId + x.companyKeynoDD + x.companyKeynoRelated,
    );
    const uniqShareholderConnections = uniqBy(
      compact(flatten(result?.map((r) => r?.shareholderConnections))),
      (x) => x.blacklistId + x.companyKeynoDD + x.companyKeynoRelated,
    );
    const uniqAcConnections = uniqBy(
      compact(flatten(result3?.map((r) => r?.Result as ActualControllerConnectionPO[]))),
      (x) => x?.keyNo + x?.companyId + x?.sourceCompanyId,
    );
    const uniqBranchConnections = uniqBy(
      compact(flatten(result?.map((r) => r?.branchConnections))),
      (x) => x.blacklistId + x.companyKeynoDD + x.companyKeynoRelated,
    );
    blacklistHit.personConnections.push(...uniqPersonConnections);
    blacklistHit.directConnection.push(...uniqDirectConnection);
    blacklistHit.shareholderConnections.push(...uniqShareholderConnections);
    blacklistHit.actualControllerConnections.push(...uniqAcConnections);
    blacklistHit.branchConnections.push(...uniqBranchConnections);
    return {
      blacklist: this.parseTenderGraphBlacklistResponse(blacklistHit, dimensions),
    };
  }

  private parseTenderGraphBlacklistResponse(response: BlacklistConnectionDDResponse, dimensions: SubDimensionDefinitionPO[]): RoverGraphParsedPO {
    const obj: RoverGraphParsedPO = new RoverGraphParsedPO();
    dimensions.forEach((dimension) => {
      const isValid = dimension?.strategyModel?.detailsParams?.find((e) => e?.field === QueryParamsEnums.isValid).fieldVal;
      switch (dimension.key) {
        case DimensionLevel2Enums.HitInnerBlackList: {
          if (response.directConnection) {
            obj[DimensionLevel2Enums.HitInnerBlackList] = uniq(response.directConnection).sort((a, b) => b.joinDate.valueOf() - a.joinDate.valueOf());
          }
          break;
        }
        case DimensionLevel2Enums.EmploymentRelationship: {
          if (response.personConnections) {
            obj[DimensionLevel2Enums.EmploymentRelationship] = uniq(response.personConnections).sort((a, b) => b.joinDate.valueOf() - a.joinDate.valueOf());
          }
          break;
        }
        case DimensionLevel2Enums.ForeignInvestment: {
          if (response.shareholderConnections) {
            const shareholderList = [];
            const nameSet = new Set<string>();
            response.shareholderConnections.forEach((s) => {
              if (s.direction > 0) {
                if (s.history) {
                  s.role = `历史${s?.role || '股东'}`;
                }
                const nameKey = s.companyNameDD + s.companyNameRelated;
                if (!nameSet.has(nameKey)) {
                  nameSet.add(nameKey);
                  shareholderList.push(s);
                }
              }
            });
            shareholderList.sort((a, b) => b.joinDate.valueOf() - a.joinDate.valueOf());
            if (isValid === '1') {
              obj[DimensionLevel2Enums.ForeignInvestment] = shareholderList?.filter((s) => !s.history);
            } else if (isValid === '0') {
              obj[DimensionLevel2Enums.ForeignInvestment] = shareholderList?.filter((s) => s.history);
            } else {
              obj[DimensionLevel2Enums.ForeignInvestment] = shareholderList;
            }
          }
          break;
        }
        case DimensionLevel2Enums.Shareholder: {
          if (response.shareholderConnections) {
            const shareholderList = [];
            const nameSet = new Set<string>();
            response.shareholderConnections.forEach((s) => {
              if (s.direction <= 0) {
                if (s.history) {
                  s.role = `历史${s?.role || '股东'}`;
                }
                const nameKey = s.companyNameDD + s.companyNameRelated;
                if (!nameSet.has(nameKey)) {
                  nameSet.add(nameKey);
                  shareholderList.push(s);
                }
              }
            });
            shareholderList.sort((a, b) => b.joinDate.valueOf() - a.joinDate.valueOf());
            if (isValid === '1') {
              obj[DimensionLevel2Enums.Shareholder] = shareholderList?.filter((s) => !s.history);
            } else if (isValid === '0') {
              obj[DimensionLevel2Enums.Shareholder] = shareholderList?.filter((s) => s.history);
            } else {
              obj[DimensionLevel2Enums.Shareholder] = shareholderList;
            }
          }
          break;
        }
        case DimensionLevel2Enums.BlacklistSameSuspectedActualController: {
          if (response.actualControllerConnections) {
            obj[DimensionLevel2Enums.BlacklistSameSuspectedActualController] = uniq(response.actualControllerConnections).sort((a, b) => b?.sort - a?.sort);
          }
          break;
        }
        case DimensionLevel2Enums.CompanyBranch: {
          if (response.branchConnections) {
            response.branchConnections.forEach((p) => {
              p.role = RelationTypeConst[DetailsParamEnums.Branch];
              p.relationType = RelationTypeConst[DetailsParamEnums.Branch];
              p.relationTypeKey = DetailsParamEnums.Branch;
            });
            obj[DimensionLevel2Enums.CompanyBranch] = uniq(response.branchConnections).sort((a, b) => b.joinDate.valueOf() - a.joinDate.valueOf());
          }
          break;
        }
      }
    });
    return obj;
  }

  private async returnHitDetailsBaseResponse(data: any[], pageSize: number, pageIndex: number): Promise<HitDetailsBaseResponse> {
    const start = pageSize * (pageIndex - 1);
    return Object.assign(new HitDetailsBaseResponse(), {
      Paging: {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: data?.length || 0,
      },
      Result: data?.slice(start, start + pageSize),
    });
  }

  /**
   * 查询疑似关系v2
   * @param dimension
   * @param requestData
   * 对公共部分进行重构，将不同维度的查询逻辑进行统一处理
   */
  async getSuspectedRelationV2(dimension: DimensionDefinitionPO, requestData: HitDetailsBaseQueryParams) {
    const { orgId, userId, keyNo, pageIndex, pageSize } = requestData;
    const types = dimension.strategyModel?.detailsParams?.find((x) => x.field === QueryParamsEnums.types);
    const excludedTypes = dimension.strategyModel?.detailsParams?.find((x) => x.field === QueryParamsEnums.excludedTypes)?.fieldVal;
    const typeList = types?.fieldVal?.filter((x) => x.status === 1).map((x) => x.key);
    if (typeList?.length > 0) {
      if (typeList.includes(DetailsParamEnums.EquityPledgeRelation)) {
        remove(typeList, (x) => x === DetailsParamEnums.EquityPledgeRelation);
        typeList.push(DetailsParamEnums.HasPledgee);
        typeList.push(DetailsParamEnums.Pledge);
      }
      let result = [];
      const queryPO = dimension.strategyModel?.detailsParams?.find((x) => x.field === QueryParamsEnums.dataRange);
      const rangeCompanyIds = await this.getRangeCompanyIds(dimension, orgId, userId, keyNo, queryPO);
      if (rangeCompanyIds.length > 0) {
        if (dimension.key === DimensionLevel2Enums.CustomerSuspectedRelation) {
          result = await this.roverGraphHelper.getCustomerSuspectedRelation(orgId, keyNo, typeList, excludedTypes);
        } else if (dimension.key === DimensionLevel2Enums.BlacklistSuspectedRelation) {
          result = await this.roverGraphHelper.getBlackListSuspectedRelation(orgId, keyNo, typeList, excludedTypes);
        }
        result = result.filter((x) => rangeCompanyIds.includes(x.startCompanyKeyno) || rangeCompanyIds.includes(x.endCompanyKeyno));
      }

      if (result.length > 0) {
        // 潜在利益，只有3层关系，所以需要对数据进行合并处理
        result = result.map((record) => {
          return {
            ...record,
            relationTypes: record.relationTypes.map((hitKey) => RelationTypeConst[hitKey]),
            // relations: calRalations(record)[0].relations,
            relations: this.parseRelations(record),
          };
        });
      }

      return Object.assign(new HitDetailsBaseResponse(), {
        Paging: {
          PageSize: pageSize,
          PageIndex: pageIndex,
          TotalRecords: result?.length || 0,
          dimensionKey: dimension.key,
        },
        Result: result?.slice((pageIndex - 1) * pageSize, pageIndex * pageSize) || [],
      });
    }
  }

  // /**
  //  * 查询疑似关系
  //  * @param dimension
  //  * @param requestData
  //  */
  // async getSuspectedRelation(dimension: DimensionDefinitionPO, requestData: HitDetailsBaseQueryParams) {
  //   const { orgId, userId, keyNo, pageIndex, pageSize } = requestData;
  //   const types = dimension.strategyModel?.detailsParams?.find((x) => x.field === QueryParamsEnums.types);
  //   const excludedTypes = dimension.strategyModel?.detailsParams?.find((x) => x.field === QueryParamsEnums.excludedTypes)?.fieldVal;
  //   const typeList = types?.fieldVal?.filter((x) => x.status === 1).map((x) => x.key);
  //   if (typeList?.length > 0) {
  //     if (typeList.includes(DetailsParamEnums.EquityPledgeRelation)) {
  //       remove(typeList, (x) => x === DetailsParamEnums.EquityPledgeRelation);
  //       typeList.push(DetailsParamEnums.HasPledgee);
  //       typeList.push(DetailsParamEnums.Pledge);
  //     }
  //     let result = [];
  //     if (dimension.key === DimensionLevel2Enums.BlacklistSuspectedRelation) {
  //       result = await this.roverGraphHelper.getBlackListSuspectedRelation(orgId, keyNo, typeList, excludedTypes);
  //     } else if (dimension.key === DimensionLevel2Enums.CustomerSuspectedRelation) {
  //       result = await this.roverGraphHelper.getCustomerSuspectedRelation(orgId, keyNo, typeList, excludedTypes);
  //     }
  //     // 过滤分组/标签选项
  //     if (result.length > 0) {
  //       return this.getHitDetailsResponseByRange(dimension, result, pageSize, pageIndex, orgId, keyNo, userId);
  //     }
  //   }
  //   return new HitDetailsBaseResponse();
  // }

  // async getHitDetailsResponseByRange(
  //   dimension: DimensionDefinitionPO,
  //   result: any[],
  //   pageSize: number,
  //   pageIndex: number,
  //   orgId: number,
  //   keyNo: string,
  //   userId?: number,
  // ): Promise<HitDetailsBaseResponse> {
  //   const queryPO = dimension.strategyModel?.detailsParams?.find((x) => x.field === QueryParamsEnums.dataRange);
  //   let rangeCompanyIds = [];
  //   if (dimension.key === DimensionLevel2Enums.BlacklistSuspectedRelation) {
  //     const groupIds = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Groups)?.value;
  //     const labelIds = queryPO?.fieldVal?.find((f) => f.key === DetailsParamEnums.Labels)?.value;
  //     if (groupIds?.length || labelIds?.length) {
  //       rangeCompanyIds = await this.searchCompanyIdsByLabelIds(orgId, groupIds, labelIds);
  //     }
  //   } else if (dimension.key === DimensionLevel2Enums.CustomerSuspectedRelation) {
  //     const { groupIds, labelIds, depIds } = await this.roverGraphHelper.getRoverRanges(queryPO, orgId, userId, keyNo);
  //     rangeCompanyIds = await this.roverGraphHelper.searchCustomer(orgId, groupIds, labelIds, depIds);
  //     rangeCompanyIds = rangeCompanyIds.filter((x) => x !== keyNo);
  //   }
  //   if (rangeCompanyIds.length > 0) {
  //     result = result.filter((x) => rangeCompanyIds.includes(x.startCompanyKeyno) || rangeCompanyIds.includes(x.endCompanyKeyno));
  //   }
  //
  //   if (result.length > 0) {
  //     // 潜在利益，只有3层关系，所以需要对数据进行合并处理
  //     result = result.map((record) => {
  //       return {
  //         ...record,
  //         relationTypes: record.relationTypes.map((hitKey) => RelationTypeConst[hitKey]),
  //         // relations: calRalations(record)[0].relations,
  //         relations: this.parseRelations(record),
  //       };
  //     });
  //   }
  //
  //   return Object.assign(new HitDetailsBaseResponse(), {
  //     Paging: {
  //       PageSize: pageSize,
  //       PageIndex: pageIndex,
  //       TotalRecords: result?.length || 0,
  //       dimensionKey: dimension.key,
  //     },
  //     Result: result?.slice((pageIndex - 1) * pageSize, pageIndex * pageSize) || [],
  //   });
  // }

  /**
   * 分析疑似关系命中情况
   * @param companyId
   * @param suspectDimensionArr
   * @param params
   * @private
   */
  private async analyzeSuspectedRelation(companyId: string, suspectDimensionArr: DimensionDefinitionPO[], params: DimensionAnalyzeParamsPO) {
    const scorePOArr = [];
    if (suspectDimensionArr.length === 0) {
      return scorePOArr;
    }
    const arr = [];
    const requestData = Object.assign(new HitDetailsBaseQueryParams(), {
      orgId: params.orgId,
      userId: params.userId,
      keyNo: companyId,
    });
    suspectDimensionArr.forEach((dimension) => arr.push(this.getSuspectedRelationV2(dimension, requestData)));
    const result = await Bluebird.all(arr);
    if (result.length > 0) {
      result.forEach((res) => {
        const totalHit = res.Paging.TotalRecords;
        if (totalHit > 0) {
          const dimension = suspectDimensionArr.find((d) => d.key === res.Paging.dimensionKey);
          const desData = {
            level: DimensionLevelClass[dimension.strategyModel.level || 0],
            cycle: dimension.strategyModel?.cycle > 0 ? dimension.strategyModel.cycle : 0,
            name: dimension.name,
            isHidden: '',
            isHiddenY: '',
          };
          const scorePO = processScorePO(dimension, totalHit, desData);
          scorePOArr.push(scorePO);
        }
      });
    }
    return scorePOArr;
  }

  private parseRelations(record: any) {
    const typeMap = {
      Guarantee: 'Guarantor',
      Pledge: 'EquityPledge',
      HasPledgee: 'EquityPledge',
      HasAddress: 'Address',
      HasEmail: 'Mail',
      HasPhone: 'ContactNumber',
    };
    // 将record的RalationPath打散重组
    const path = record.relationPaths.map((_relation) => {
      const nodeType = _relation[1].type; // 准入排查的类型，需要映射转译，方便打开弹窗
      const type = typeMap[nodeType] || nodeType; // 类型
      const role = RelationTypeConst[nodeType];
      let relations;
      // 股权出质关联和相互担保关联 不返回data
      if (['Guarantor', 'EquityPledge'].includes(type)) {
        relations = _relation.map((r, index) => {
          if (index % 2 === 0) {
            return r;
          }
          return {
            ...r,
            type,
            role,
            showDetail: true,
          };
        });
      } else {
        let data;
        if (type === 'Address') {
          const address = _relation[2]['Address.name'];
          data = [
            {
              type: 'Address',
              role,
              data: [
                {
                  address,
                  relationA: {
                    companyName: record.startCompanyName,
                    keyNo: record.startCompanyKeyno,
                    address,
                    startDate: dateTransform(_relation[1].register_date),
                  },
                  relationB: {
                    companyName: record.endCompanyName,
                    keyNo: record.endCompanyKeyno,
                    address,
                    startDate: dateTransform(_relation[3]?.register_date),
                  },
                },
              ],
            },
          ];
        } else if (type === 'Mail') {
          const name = uniq(_relation[2]['Email.name']?.split('|'));
          data = [
            {
              type: 'Mail',
              role,
              data: name,
            },
          ];
        } else {
          const phoneArr = uniq(_relation[2]['Phone.name']?.split('|'));
          const PhoneSource = _relation[2]['Phone.source']?.split('|');
          const phoneData = phoneArr?.reduce((arr: any[], phone, index) => {
            arr = [
              ...arr,
              {
                companyId: _relation[1].startid,
                t: phone + '',
                s: PhoneSource?.[index * 2] || _relation[1].source,
              },
              {
                companyId: _relation[3].endid,
                t: phone + '',
                s: PhoneSource?.[index * 2 + 1] || _relation[3].source,
              },
            ];
            return arr;
          }, []);
          data = [
            {
              type: 'ContactNumber',
              role,
              data: phoneData,
            },
          ];
        }
        relations = [
          _relation[0],
          {
            data,
            ..._relation[1],
            startid: record.startCompanyKeyno,
            endid: record.endCompanyKeyno,
            direction: 0,
            showDetail: true,
            type,
          },
          _relation[4],
        ];
      }
      return {
        role,
        type,
        relations,
        startCompanyKeyno: record.startCompanyKeyno,
        endCompanyKeyno: record.endCompanyKeyno,
      };
    });
    const finalPath = parseRelationsData(path);
    return finalPath[0].relations;
  }

  async searchCompanyIdsByLabelIds(orgId: number, groupIds: any, labelIds: any): Promise<number[]> {
    const query = this.innerBlacklistRepo.createQueryBuilder('blacklist').select('blacklist.companyId as companyId').leftJoin('blacklist.labels', 'labels');
    if (groupIds?.length) {
      if (groupIds?.includes(-1) && groupIds?.length === 1) {
        query.andWhere('blacklist.groupId is null');
      }
      if (!groupIds?.includes(-1)) {
        query.andWhere('blacklist.groupId in (:...groupIds)', { groupIds });
      }
      if (groupIds?.includes(-1) && groupIds?.length > 1) {
        const filterGroupIds = groupIds.filter((r) => r >= 0);
        query.andWhere(
          new Brackets((qb1) => {
            qb1.orWhere('blacklist.groupId is null');
            qb1.orWhere('blacklist.groupId in (:...filterGroupIds)', { filterGroupIds });
          }),
        );
      }
    }
    if (labelIds?.length) {
      query.andWhere('labels.labelId in (:...labelIds)', { labelIds });
    }
    const result = await query.getRawMany();
    return uniq(result.map((r) => r.companyId));
  }

  async scanForBiddingV2(
    orgId: number,
    companyIds: string[],
    pageSize: number,
    pageIndex: number,
    dimensions: SubDimensionDefinitionPO[],
    detailsParams: DimensionQueryPO[],
  ) {
    // 招标黑名单维度设置维度与实际类型不一致，需要转换
    const types = this.getBlacklistRealTypes(dimensions);
    const response = await this.getResultsForBiddingV2(orgId, companyIds, types, detailsParams);
    const blacklistInvestigationByPage = this.getResultByPage(response, 'blacklistInvestigations', pageIndex, pageSize);
    const directConnectionByPage = this.getResultByPage(response, 'directConnection', pageIndex, pageSize);
    return [blacklistInvestigationByPage, directConnectionByPage].filter((x) => x);
  }

  private async getResultsForBiddingV2(
    orgId: number,
    companyIds: string[],
    types: string[],
    detailsParams: DimensionQueryPO[],
  ): Promise<TenderInnerBlackListResPo> {
    return await this.roverGraphHelper.getTenderInnerBlacklist(orgId, companyIds, types, detailsParams);
  }

  private getBlacklistRealTypes(dimensions: SubDimensionDefinitionPO[]) {
    const types = [];
    dimensions.forEach((dimension) => {
      const validValue = dimension.strategyModel?.detailsParams?.find((x) => x.field === QueryParamsEnums.isValid)?.fieldVal;
      switch (dimension.key) {
        case DimensionLevel2Enums.HitInnerBlackList: {
          types.push(DimensionLevel2Enums.HitInnerBlackList);
          break;
        }
        case DimensionLevel2Enums.ForeignInvestment: {
          if (validValue === 0) {
            types.push(DimensionLevel2Enums.Invest);
          } else if (validValue === 1) {
            types.push(DimensionLevel2Enums.HisInvest);
          } else {
            types.push(DimensionLevel2Enums.Invest);
            types.push(DimensionLevel2Enums.HisInvest);
          }
          break;
        }
        case DimensionLevel2Enums.Shareholder: {
          if (validValue === 0) {
            types.push(DimensionLevel2Enums.Shareholder);
          } else if (validValue === 1) {
            types.push(DimensionLevel2Enums.HisShareholder);
          } else {
            types.push(DimensionLevel2Enums.Shareholder);
            types.push(DimensionLevel2Enums.HisShareholder);
          }
          break;
        }
        case DimensionLevel2Enums.EmploymentRelationship: {
          if (validValue === 0) {
            types.push(DimensionLevel2Enums.Employ);
          } else if (validValue === 1) {
            types.push(DimensionLevel2Enums.HisEmploy);
          } else {
            types.push(DimensionLevel2Enums.Employ);
            types.push(DimensionLevel2Enums.HisEmploy);
          }
          break;
        }
        case DimensionLevel2Enums.BlacklistSameSuspectedActualController: {
          types.push(DimensionLevel2Enums.ActualController);
          break;
        }
        case DimensionLevel2Enums.BlacklistSameFinalBenefit: {
          types.push(DetailsParamEnums.FinalBenefit);
          break;
        }
        case DimensionLevel2Enums.CompanyBranch: {
          types.push(DimensionLevel2Enums.Branch);
          break;
        }
        case DimensionLevel2Enums.Hold: {
          types.push(DimensionLevel2Enums.Hold);
          break;
        }
        default: {
          break;
        }
      }
    });
    return types;
  }

  private getResultByPage(response: TenderInnerBlackListResPo, pageType: string, pageIndex: number, pageSize: number) {
    switch (pageType) {
      case 'blacklistInvestigations': {
        const { blacklistInvestigations } = response;
        if (blacklistInvestigations.length === 0) {
          return null;
        }
        const hitsDetails = new BiddingDimensionHitsDetails();
        hitsDetails.key = DimensionLevel2Enums.BlackListInvestigations;
        hitsDetails.name = '内部黑名单关联关系';
        hitsDetails.totalHits = blacklistInvestigations.length;
        hitsDetails.allData = { Result: blacklistInvestigations };
        const data = this.getSimplePageData(blacklistInvestigations, pageIndex, pageSize);
        hitsDetails.data = getTransV2Detail(data);
        hitsDetails.status = 1;
        hitsDetails.sort = 2;
        return hitsDetails;
      }
      case 'directConnection': {
        const { directConnection } = response;
        if (directConnection.length === 0) {
          return null;
        }
        const hitsDetails = new BiddingDimensionHitsDetails();
        hitsDetails.key = DimensionLevel2Enums.DirectConnection;
        hitsDetails.name = '被列入内部黑名单';
        hitsDetails.totalHits = directConnection.length;
        hitsDetails.allData = { Result: directConnection };
        hitsDetails.data = this.getSimplePageData(directConnection, pageIndex, pageSize);
        hitsDetails.status = 1;
        hitsDetails.sort = 1;
        return hitsDetails;
      }
    }
    return null;
  }

  async getDirectConnection(postData: HitDetailsBidBaseQueryParams) {
    const { orgId, keyNos, pageIndex, pageSize } = postData;
    const types = [DimensionLevel2Enums.HitInnerBlackList];
    const response = await this.roverGraphHelper.getTenderInnerBlacklist(orgId, keyNos, types);
    const { directConnection } = response;
    return this.getSimplePageData(directConnection, pageIndex, pageSize);
  }

  async getBlackListInvestigations(postData: HitDetailsBidBaseQueryParams, dimensions: SubDimensionDefinitionPO[], detailsParams: DimensionQueryPO[]) {
    const { orgId, keyNos, pageIndex, pageSize } = postData;
    const types = this.getBlacklistRealTypes(dimensions);
    const response = await this.getResultsForBiddingV2(orgId, keyNos, types, detailsParams);
    const { blacklistInvestigations } = response;
    const data = this.getSimplePageData(blacklistInvestigations, pageIndex, pageSize);
    return getTransV2Detail(data);
  }

  private getSimplePageData(arr: any[], pageIndex: number, pageSize: number) {
    return getArrByPage(arr, pageIndex, pageSize);
  }
}
