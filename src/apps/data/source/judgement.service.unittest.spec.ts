import { Test } from '@nestjs/testing';
import { AppTestModule } from 'apps/app/app.test.module';
import { ExcludeCompanyService } from 'apps/exclude_company/exclude-company.service';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { PersonData } from '../../../libs/model/data/source/PersonData';
import { DataModule } from '../data.module';
import { PersonHelper } from '../helper/person.helper';
import { EnterpriseLibService } from './enterprise.lib.service';
import { JudgementService } from './judgement.service';

// Mock 依赖服务
jest.mock('./enterprise.lib.service');
jest.mock('../helper/person.helper');
jest.mock('../../company/company-detail.service');
jest.mock('apps/exclude_company/exclude-company.service');

const [testOrgId, testUserId] = generateUniqueTestIds('judgement.service.unittest.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(60 * 1000);
describe('JudgementService 单元测试', () => {
  let service: JudgementService;
  let entLibService: jest.Mocked<EnterpriseLibService>;
  let personHelper: jest.Mocked<PersonHelper>;
  let excludeCompanyService: jest.Mocked<ExcludeCompanyService>;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();

    service = module.get<JudgementService>(JudgementService);
    entLibService = module.get(EnterpriseLibService) as jest.Mocked<EnterpriseLibService>;
    personHelper = module.get(PersonHelper) as jest.Mocked<PersonHelper>;
    excludeCompanyService = module.get(ExcludeCompanyService) as jest.Mocked<ExcludeCompanyService>;

    // 重置所有 mock
    jest.clearAllMocks();
  });

  afterAll(async () => {});

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getRelatedCompanyAndPerson 方法测试', () => {
    const mockCompanyId = 'test_company_123';

    it('应该正确获取关联企业和人员 - 包含法定代表人', async () => {
      // Arrange - 准备测试数据
      const associateObjects = [
        {
          key: DetailsParamEnums.LegalRepresentative,
          status: 1,
        },
        {
          key: DetailsParamEnums.Employ,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockLegalPersonData: PersonData[] = [
        {
          name: '李四',
          keyNo: 'person456',
          job: '法定代表人',
          tags: ['法人'],
        },
      ];

      // Mock 依赖服务的方法
      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getLegalPerson').mockResolvedValue(mockLegalPersonData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act - 执行测试
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert - 验证结果
      expect(result).toHaveLength(2);
      expect(result.find((p) => p.keyNo === 'person123')).toBeDefined();
      expect(result.find((p) => p.keyNo === 'person456')).toBeDefined();
      expect(entLibService.getEmployeeList).toHaveBeenCalledWith(mockCompanyId);
      expect(entLibService.getLegalPerson).toHaveBeenCalledWith(mockCompanyId);
    });

    it('应该正确获取关联企业和人员 - 包含实际控制人', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.ActualController,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockActualControllerData: PersonData[] = [
        {
          name: '王五',
          keyNo: 'person789',
          job: '实际控制人',
          tags: ['控制人'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(personHelper, 'getFinalActualController').mockResolvedValue(mockActualControllerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(2);
      expect(result.find((p) => p.keyNo === 'person789')).toBeDefined();
      expect(personHelper.getFinalActualController).toHaveBeenCalledWith(mockCompanyId);
    });

    it('应该正确获取关联企业和人员 - 包含股东信息', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
      ];
      const associateExcludes = [];
      const ignoreInvest = false;

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '股东甲',
          keyNo: 'person999',
          job: '股东',
          tags: ['股东'],
          stockPercent: '20%',
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(2);
      expect(result.find((p) => p.keyNo === 'person999')).toBeDefined();
      expect(entLibService.getPartnerList).toHaveBeenCalledWith(mockCompanyId, 'all', ignoreInvest);
    });

    it('应该正确过滤大股东 - 只包含大股东时', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.MajorShareholder,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '大股东甲',
          keyNo: 'person888',
          job: '股东',
          tags: ['大股东'],
          stockPercent: '51%',
        },
        {
          name: '小股东乙',
          keyNo: 'person777',
          job: '股东',
          tags: ['股东'],
          stockPercent: '10%',
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(1); // 只有大股东（员工数据+经过过滤的股东数据）
      expect(result.find((p) => p.keyNo === 'person888')).toBeDefined(); // 大股东应该被包含
      expect(result.find((p) => p.keyNo === 'person777')).toBeUndefined(); // 小股东应该被过滤
    });

    it('应该正确排除投资机构', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
      ];
      const associateExcludes = [
        {
          key: DetailsParamEnums.ShareHolderInvest,
          status: 1,
        },
      ];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '普通股东',
          keyNo: 'person111',
          job: '股东',
          tags: ['股东'],
        },
        {
          name: '某某基金',
          keyNo: 'person222',
          job: '股东',
          tags: ['私募基金'],
        },
        {
          name: '某某信托',
          keyNo: 'person333',
          job: '股东',
          tags: ['信托'],
        },
        {
          name: '普通公司',
          keyNo: 'person444',
          job: '股东',
          tags: ['股东'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act
      const result = await service.getRelatedCompanyAndPerson(
        mockCompanyId,
        associateObjects,
        associateExcludes,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
      );

      // Assert
      expect(result).toHaveLength(3); // 员工 + 普通股东 + 普通公司
      expect(result.find((p) => p.keyNo === 'person111')).toBeDefined(); // 普通股东应该被包含
      expect(result.find((p) => p.keyNo === 'person222')).toBeUndefined(); // 投资基金应该被排除
      expect(result.find((p) => p.keyNo === 'person333')).toBeUndefined(); // 投资信托应该被排除
      expect(result.find((p) => p.keyNo === 'person444')).toBeDefined(); // 普通公司应该被包含
    });

    it('应该正确去重人员数据', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.LegalRepresentative,
          status: 1,
        },
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockLegalPersonData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123', // 同一个人
          job: '法定代表人',
          tags: ['法人'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123', // 同一个人
          job: '股东',
          tags: ['股东'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getLegalPerson').mockResolvedValue(mockLegalPersonData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(1); // 应该只有一个人（去重后）
      expect(result[0].keyNo).toBe('person123');
    });

    it('应该正确过滤排除的公司', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '股东甲',
          keyNo: 'excluded_person_456',
          job: '股东',
          tags: ['股东'],
        },
        {
          name: '股东乙',
          keyNo: 'person789',
          job: '股东',
          tags: ['股东'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['excluded_person_456']);

      // Act
      const result = await service.getRelatedCompanyAndPerson(
        mockCompanyId,
        associateObjects,
        associateExcludes,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
      );

      // Assert
      expect(result).toHaveLength(2); // 员工 + 未被排除的股东
      expect(result.find((p) => p.keyNo === 'person123')).toBeDefined();
      expect(result.find((p) => p.keyNo === 'person789')).toBeDefined();
      expect(result.find((p) => p.keyNo === 'excluded_person_456')).toBeUndefined(); // 被排除的股东不应该存在
    });

    it('应该正确处理空的 keyNo', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.LegalRepresentative,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockLegalPersonData: PersonData[] = [
        {
          name: '李四',
          keyNo: undefined, // 空的 keyNo
          job: '法定代表人',
          tags: ['法人'],
        },
        {
          name: '王五',
          keyNo: null as any, // null 的 keyNo
          job: '法定代表人',
          tags: ['法人'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getLegalPerson').mockResolvedValue(mockLegalPersonData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(1); // 只有有效 keyNo 的人员
      expect(result[0].keyNo).toBe('person123');
    });

    it('应该正确处理没有配置关联对象的情况', async () => {
      // Arrange
      const associateObjects = []; // 空的关联对象配置
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(1); // 只有员工数据
      expect(result[0].keyNo).toBe('person123');
      // 验证没有调用其他服务方法
      expect(entLibService.getLegalPerson).not.toHaveBeenCalled();
      expect(personHelper.getFinalActualController).not.toHaveBeenCalled();
      expect(entLibService.getPartnerList).not.toHaveBeenCalled();
    });

    it('应该正确处理股东和大股东同时配置的情况', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
        {
          key: DetailsParamEnums.MajorShareholder,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '大股东甲',
          keyNo: 'person888',
          job: '股东',
          tags: ['大股东'],
          stockPercent: '51%',
        },
        {
          name: '小股东乙',
          keyNo: 'person777',
          job: '股东',
          tags: ['股东'],
          stockPercent: '10%',
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(3); // 员工 + 所有股东（因为同时配置了股东和大股东）
      expect(result.find((p) => p.keyNo === 'person123')).toBeDefined();
      expect(result.find((p) => p.keyNo === 'person888')).toBeDefined();
      expect(result.find((p) => p.keyNo === 'person777')).toBeDefined();
    });

    it('应该正确处理投资机构过滤 - 通过tags过滤投资机构', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
      ];
      const associateExcludes = [
        {
          key: DetailsParamEnums.ShareHolderInvest,
          status: 1,
        },
      ];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '普通股东',
          keyNo: 'person111',
          job: '股东',
          tags: ['股东'],
        },
        {
          name: '某某基金',
          keyNo: 'person222',
          job: '股东',
          tags: ['私募基金'],
        },
        {
          name: '某某信托',
          keyNo: 'person333',
          job: '股东',
          tags: ['信托'],
        },
        {
          name: '普通公司',
          keyNo: 'person444',
          job: '股东',
          tags: ['股东'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue([]);

      // Act
      const result = await service.getRelatedCompanyAndPerson(
        mockCompanyId,
        associateObjects,
        associateExcludes,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
      );

      // Assert - 期望只有员工 + 两个普通股东（'某某基金'和'某某信托'被tags过滤掉）
      expect(result).toHaveLength(3); // 员工 + 普通股东 + 普通公司
      expect(result.find((p) => p.keyNo === 'person123')).toBeDefined(); // 员工应该被包含
      expect(result.find((p) => p.keyNo === 'person111')).toBeDefined(); // 普通股东应该被包含
      expect(result.find((p) => p.keyNo === 'person444')).toBeDefined(); // 普通公司应该被包含
      expect(result.find((p) => p.keyNo === 'person222')).toBeUndefined(); // 带'私募基金'标签的应该被排除
      expect(result.find((p) => p.keyNo === 'person333')).toBeUndefined(); // 带'信托'标签的应该被排除
    });
  });
});
