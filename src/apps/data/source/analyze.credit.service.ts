import { Injectable } from '@nestjs/common';
import { DimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { CreditAggBucketItemPO } from 'libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import { attempt, difference, find, flatten, isError, isNumber } from 'lodash';
import { QueryParamsEnums } from 'libs/model/diligence/pojo/dimension/dimension.filter.params';
import { OperatorEnums } from 'libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { EnterpriseLibService } from './enterprise.lib.service';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { BusinessAbnormalType, CreditEsMappingModel, creditSearchSourceMappings, PenaltiesType } from 'libs/constants/credit.analyze.constants';
import { getStartTimeByCycle, toRoundFixed, transferToNumber } from 'libs/utils/utils';
import { getCreditMappingByDimension } from 'libs/utils/diligence/credit.utils';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { AbstractEsAnalyzeService } from './analyze.base.service';
import { DimensionScorePO } from '../../../libs/model/diligence/pojo/dimension/DimensionScorePO';
import { processScorePO } from '../../../libs/utils/diligence/diligence.utils';
import * as Bluebird from 'bluebird';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/pojo/req&res/details/response';
import * as moment from 'moment';
import { DATE_FORMAT, HistoryValidNumbersArr, NoLimitValidNumbersArr } from '../../../libs/constants/common';
import { CompanySearchService } from '../../company/company-search.service';
import { HitDetailsCreditParam } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { DimensionAnalyzeParamsPO } from '../DimensionAnalyzeParamsPO';

@Injectable()
export class AnalyzeCreditService extends AbstractEsAnalyzeService {
  private expectedDimensionsTypes: DimensionTypeEnums[] = flatten(creditSearchSourceMappings.map((t) => t.dimensionType));

  constructor(
    private readonly configService: ConfigService,
    private readonly entLibService: EnterpriseLibService,
    private readonly companyService: CompanySearchService,
  ) {
    super(
      AnalyzeCreditService.name,
      new Client({
        nodes: configService.esConfig.credit.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.credit.indexName,
    );
  }

  getESCreditTypes() {
    return [
      DimensionLevel2Enums.AdministrativePenalties2,
      DimensionLevel2Enums.AdministrativePenalties3,
      DimensionLevel2Enums.SpotCheck,
      DimensionLevel3Enums.ProductQualityProblem1,
      DimensionLevel3Enums.ProductQualityProblem2,
      // DimensionLevel2Enums.AdministrativePenalties,
      DimensionLevel2Enums.EnvironmentalPenalties,
      DimensionLevel2Enums.TaxationOffences,
      DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent,
      DimensionLevel3Enums.RestrictedConsumptionCurrent,
      DimensionLevel3Enums.RestrictedConsumptionHistory,
      DimensionLevel3Enums.BusinessAbnormal5,
      DimensionLevel2Enums.OperationAbnormal,
      DimensionLevel2Enums.FreezeEquity,
      // DimensionLevel3Enums.ProductQualityProblem9,
      DimensionLevel2Enums.RegulateFinance,
      // DimensionLevel2Enums.Bankruptcy,
      DimensionLevel3Enums.BusinessAbnormal3,
    ];
  }

  getBidESCreditTypes() {
    return [
      // 涉围串标等投标处罚记录
      // DimensionLevel2Enums.BidAdministrativePenalties,
    ];
  }

  async analyze(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], params: DimensionAnalyzeParamsPO): Promise<DimensionScorePO[]> {
    try {
      if (!companyId || !dimensionDefinitionPOs?.length) {
        return [];
      }
      const diffTypes = difference(
        dimensionDefinitionPOs.map((m) => m.key),
        this.expectedDimensionsTypes,
      );
      if (diffTypes?.length) {
        this.logger.warn('passed dimension contains not expected ones, will remove unexpected keys');
        dimensionDefinitionPOs = dimensionDefinitionPOs.filter((po) => this.expectedDimensionsTypes.some((t) => t == po.key));
      }

      return super.analyze(companyId, dimensionDefinitionPOs, params);
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }

  async getDimensionDetail(dimension: DimensionDefinitionPO, params: HitDetailsCreditParam): Promise<HitDetailsBaseResponse> {
    const { keyNo } = params;
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params);
    if (resp?.Result?.length) {
      resp.Result.forEach((source) => {
        const Applicant = source.applicant || source.executionapplicant;
        const ApplicantInfo = Applicant ? JSON.parse(source?.nameandkeyno)?.filter((sn) => Applicant?.includes(sn?.Name)) : JSON.parse(source?.nameandkeyno);
        const NameAndKeyNo = source.nameandkeyno ? JSON.parse(source.nameandkeyno) : undefined;

        // // 食品安全检查不合格 特殊处理
        // if (dimension.key == DimensionLevel3Enums.ProductQualityProblem9) {
        //   NameAndKeyNo = [{ KeyNo: source.keyno, Name: source.name, Org: 0 }];
        //   if (!Applicant) {
        //     ApplicantInfo = [];
        //   }
        // }

        const Specs = source.specs ? (isError(attempt(JSON.parse, source.specs)) ? source.specs : attempt(JSON.parse, source.specs)) : undefined;

        Object.assign(source, {
          Id: source.id,
          KeyNo: source.keyno,
          Title: source.title,
          Name: source.name || source.pername,
          Type: source.type,
          RiskId: source.riskid,
          DataStatus: source.datastatus,
          HasImage: source.hasimage,
          StockInfo: source.stockinfo,
          Product: source.product,
          PunishGovCode: source.punishgovcode,
          OssId: source.ossid,
          Province: source.province,
          AreaCode: source.areacode,
          ProvinceDesc: source.provincedesc,
          TypeDesc: source.typedesc,
          IdNum: source.idnum,
          CreditCode: source.creditcode,
          CompOrg: source.comporg,
          OssKey: source.osskey,
          Applicant,
          Pledgor: source.pledgor,
          TagJsonArray: source.tagjsonarray ? JSON.parse(source.tagjsonarray) : undefined,
          NameAndKeyNo,
          CaseReasonType: source.casereasontype || source.removereason,
          PublishTypeDesc: source.publishtypedesc,
          CaseReason: source.casereason,
          PublishDate: source.publishdate,
          CurrenceDate: source.occurrencedate,
          OccurrenceDate: source.occurrencedate,
          Court: source.court,
          OrgNo: source.orgno,
          RemoveReason: source.removereason,
          OriginalName: source.originalname,
          PunishDate: source.punishdate,
          CourtCode: source.courtcode,
          CourtProvince: source.courtprovince,
          CaseNo: source.caseno,
          Amount: source.amount,
          ActionRemark: source.actionremark || source.removereason,
          FileUrl: source?.osskey?.length > 0 ? `https://qccdata.qichacha.com/CaseSupervisePunish/${source.osskey}` : '',
          ExecutionApplicant: source.executionapplicant,
          LianDate: source.liandate,
          PPledgor: source.p_pledgor,
          Specs,
          ApplicantInfo,
          AmountDesc: source.amountdesc,
          ExecuteStatus: source.executestatus,
          Address: source.address,
          docNo: source.caseno,
          punishReason: source.casereasonclean,
          punishResult: source.title,
          punishOffice: source.court,
          punishDate: moment(source?.punishdate * 1000).format(DATE_FORMAT),
          Amount2: source?.amount2,
        });
        if (source.NameAndKeyNo) {
          if (!(source.NameAndKeyNo instanceof Array)) {
            source.NameAndKeyNo = [source.NameAndKeyNo];
          }
        }
      });
      switch (dimension?.key) {
        // 主要人员限制高消费
        case DimensionLevel3Enums.RestrictedConsumptionHistory:
        case DimensionLevel3Enums.RestrictedConsumptionCurrent: {
          // 主要人员限制高消费 不返回企业主体标签
          const { personJobSet } = await this.entLibService.getCompanyExecutivesKeyNosV2(params.keyNo, 'person', true, true);
          resp?.Result?.forEach((x) => {
            // 限消令对象
            x['SubjectInfo'] = x.NameAndKeyNo.filter((a) => a.KeyNo == x.KeyNo);
            x?.SubjectInfo?.forEach((p) => {
              p['Job'] = personJobSet[p?.KeyNo];
            });
            // 限消令关联对象
            x['PledgorInfo'] = x.NameAndKeyNo.filter((a) => x?.p_pledgor_id?.includes(a.KeyNo) || x?.pledgor_id?.includes(a.KeyNo));
            x?.PledgorInfo?.forEach((p) => {
              p['Job'] = personJobSet[p?.KeyNo];
            });
            Object.assign(x, {
              FileUrl: x?.osskey?.length > 0 ? `https://qccdata.qichacha.com/CountXG/${x.osskey}` : '',
              // 申请人
              ApplicantInfo: x.Specs,
            });
          });
          break;
        }
        case DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent:
        case DimensionLevel3Enums.BusinessAbnormal3:
        case DimensionLevel2Enums.OperationAbnormal:
        case DimensionLevel2Enums.Bankruptcy:
        case DimensionLevel2Enums.RegulateFinance:
        case DimensionLevel2Enums.AdministrativePenalties2:
        case DimensionLevel2Enums.AdministrativePenalties3: {
          await this.entLibService.processResponseData(resp, dimension, keyNo, 'all');
          break;
        }
        default:
          break;
      }
    }
    return resp;
  }

  protected async createAggs(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[], params: HitDetailsCreditParam) {
    const aggs: any = {};
    await Bluebird?.map(dimensionDefinitionPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po, params);
      if (dimQuery) {
        const aggsName = `${this.bucketNamePrefix}${po.key}`;
        aggs[aggsName] = {
          filter: dimQuery,
          aggs: {
            amount: {
              sum: {
                field: 'amount',
              },
            },
            amount2: {
              sum: {
                field: 'amount2',
              },
            },
          },
        };
        switch (po.key) {
          case DimensionLevel2Enums.HitOuterBlackList: {
            const blacklistSettingItems = po.subDimensionList.filter((sub) => sub.status > 0);
            // 需要按照风险等级分别统计，维度的风险等级取最高的那个
            Object.keys(DimensionRiskLevelEnum)
              .filter((level) => isNaN(Number(level)))
              .forEach((level) => {
                const outerBlackPublishTypesLevel = flatten(
                  blacklistSettingItems
                    .filter((item) => item.strategyModel.level == DimensionRiskLevelEnum[level] && !!item.child)
                    .map((item) => item.child.map((s) => s - 15 * 100)),
                );
                if (outerBlackPublishTypesLevel.length > 0) {
                  aggs[aggsName].aggs[`agg_${level}`] = { filter: { terms: { publishtype: outerBlackPublishTypesLevel } } };
                }
              });
            break;
          }
        }
      }
    });
    return aggs;
  }

  protected processAggs(aggObj: any): CreditAggBucketItemPO[] {
    const bucketData: CreditAggBucketItemPO[] = [];
    Object.keys(aggObj).forEach((bucketName) => {
      // const bucket = aggObj[bucketName];
      const dimensionType = bucketName.replace(this.bucketNamePrefix, '') as DimensionTypeEnums;
      const bucket = aggObj[bucketName];
      const esMappingModel: CreditEsMappingModel = getCreditMappingByDimension(dimensionType);
      if (esMappingModel) {
        const hitCount = bucket['doc_count'];
        if (hitCount > 0) {
          const res: CreditAggBucketItemPO = {
            dimensionType,
            hitCount,
          };
          if (bucket.amount) {
            res.amount = transferToNumber(bucket.amount.value);
          }
          if (bucket.amount2) {
            res.amount2 = transferToNumber(bucket.amount2.value);
          }
          bucketData.push(res);
        }
      }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: CreditAggBucketItemPO[], dimensionDefinitionPOs: DimensionDefinitionPO[]): DimensionScorePO[] {
    return bucketData
      .map((item) => {
        const d: DimensionDefinitionPO = find(dimensionDefinitionPOs, { key: item.dimensionType });
        const desData = {
          level: item.level || d.strategyModel.level || 0,
          cycle: d.strategyModel?.cycle > 0 ? d.strategyModel.cycle : 0,
          name: d.name,
          isHidden: '',
          isHiddenY: '',
        };
        const { hitCount, amount, amount2 } = item as CreditAggBucketItemPO;

        switch (d.key) {
          case DimensionLevel2Enums.BondDefaults: {
            //  债券违约
            if (amount) {
              Object.assign(desData, { amountY: toRoundFixed(amount / 10000 / 10000, 2) });
            }
            if (amount2) {
              Object.assign(desData, { amount2Y: toRoundFixed(amount2 / 10000 / 10000, 2) });
              //维度未升级 cover
              Object.assign(desData, { amountW: amount2 / 10000 });
            }
            break;
          }
          case DimensionLevel2Enums.EnvironmentalPenalties: // '环保处罚',  countInfo.ENPCountV2
          case DimensionLevel3Enums.BusinessAbnormal3: // 被列入经营异常名录
          case DimensionLevel2Enums.Bankruptcy: // '破产重整', countInfo.BankruptcyCount
          case DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent: // 主要人员限制高消费
          case DimensionLevel3Enums.MainMembersRestrictedOutbound: // 主要人员限制出境
          case DimensionLevel2Enums.OperationAbnormal: // '多次被列入经营异常名录【当下未列入】'
          // case DimensionLevel2Enums.AdministrativePenalties: // '行政处罚',元
          case DimensionLevel3Enums.MainMembersPersonCreditCurrent: //主要人员被列入失信被执行人，元
          case DimensionLevel2Enums.PersonExecution:
          case DimensionLevel3Enums.PersonCreditCurrent:
          case DimensionLevel3Enums.PersonCreditHistory: // '被列入失信被执行人（历史）'
          case DimensionLevel2Enums.FreezeEquity: {
            // '股权冻结', 万元
            if (amount) {
              Object.assign(desData, { amountW: toRoundFixed(amount / 10000, 2) });
            }
            break;
          }
        }

        const dimensionScorePO = processScorePO(d, hitCount, desData);
        dimensionScorePO.level = desData.level;
        return dimensionScorePO;
      })
      .filter((t) => t);
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionDefinitionPO, params: HitDetailsCreditParam): Promise<object> {
    const { strategyModel } = dimension;
    const subBool = {
      bool: {
        must_not: [],
        must: [],
        should: [],
      },
    };
    const esMappingModel = getCreditMappingByDimension(dimension.key);
    if (esMappingModel) {
      subBool.bool.must.push({
        term: {
          type: esMappingModel.type,
        },
      });

      let useKeyNo = true;
      let datastatus = [0, 1];
      let ids = [companyId];
      const startTimestamp = getStartTimeByCycle(strategyModel?.cycle || 3);
      const associateObject = strategyModel.detailsParams?.find((q) => q.field === QueryParamsEnums.associateObject);
      switch (dimension.key) {
        case DimensionLevel3Enums.MainMembersRestrictedOutbound:
        case DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent:
        case DimensionLevel3Enums.MainMembersPersonCreditCurrent: {
          ids = await this.entLibService.addRelatedKeyNosV2(companyId, associateObject, 'all', dimension.key);
          if (!ids?.length) {
            return null;
          }
          datastatus = NoLimitValidNumbersArr;
          break;
        }
        case DimensionLevel2Enums.OperationAbnormal: {
          datastatus = [0, 10, 11, 13, 91, 93];
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                publishdate: {
                  gte: Math.ceil(startTimestamp / 1000),
                },
              },
            });
          }
          await this.entLibService.addRelatedKeyNos(ids, companyId, associateObject, 'all');
          break;
        }
        case DimensionLevel3Enums.RestrictedConsumptionCurrent: {
          datastatus = [1];
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                publishdate: {
                  gte: Math.ceil(startTimestamp / 1000),
                },
              },
            });
          }
          break;
        }
        case DimensionLevel3Enums.BusinessAbnormal3: {
          // 被列入经营异常名录 ,根据publishtype字段，还可以分为多个子类
          datastatus = [1];
          await this.entLibService.addRelatedKeyNos(ids, companyId, associateObject, 'all');
          break;
        }
        case DimensionLevel3Enums.CancellationOfFiling: // 注销备案
        case DimensionLevel3Enums.PersonCreditCurrent: {
          datastatus = [1];
          break;
        }
        case DimensionLevel3Enums.PersonCreditHistory: {
          datastatus = [0];
          break;
        }
        case DimensionLevel3Enums.RestrictedConsumptionHistory: {
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                publishdate: {
                  gte: Math.ceil(startTimestamp / 1000),
                },
              },
            });
          }
          datastatus = HistoryValidNumbersArr;
          break;
        }
        // 股权冻结
        case DimensionLevel2Enums.FreezeEquity: {
          datastatus = NoLimitValidNumbersArr;
          // 股权冻结查询 被执行和或者出质人 是当前企业的
          if (ids?.length) {
            subBool.bool.should.push({ terms: { keyno: ids } }, { terms: { pledgor_id: ids } });
            subBool.bool['minimum_should_match'] = 1;
          }
          useKeyNo = false;

          // 统计周期（年）
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                publishdate: {
                  gte: Math.ceil(startTimestamp / 1000),
                },
              },
            });
          }
          break;
        }
        case DimensionLevel2Enums.AdministrativePenalties3: {
          //该维度需求为只查三年前的数据
          const r = getStartTimeByCycle(3);
          subBool.bool.must.push({ range: { occurrencedate: { lte: Math.ceil(r / 1000) } } });
          subBool.bool.should.push(
            { match_phrase: { casereason: '商业贿赂' } },
            { match_phrase: { casereason: '垄断' } },
            { match_phrase: { casereason: '政府采购违法行为' } },
          );
          subBool.bool['minimum_should_match'] = 1;
          datastatus = NoLimitValidNumbersArr;
          await this.entLibService.addRelatedKeyNos(ids, companyId, associateObject, 'all');
          break;
        }
        case DimensionLevel2Enums.AdministrativePenalties2: {
          subBool.bool.should.push(
            { match_phrase: { casereason: '商业贿赂' } },
            { match_phrase: { casereason: '垄断' } },
            { match_phrase: { casereason: '政府采购违法行为' } },
          );
          subBool.bool['minimum_should_match'] = 1;
          datastatus = NoLimitValidNumbersArr;
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                punishdate: {
                  //处罚日期
                  // 目前发现 publishdate 和 occurrencedate 在es中数据一样
                  gte: Math.ceil(startTimestamp / 1000),
                },
              },
            });
          }
          await this.entLibService.addRelatedKeyNos(ids, companyId, associateObject, 'all');
          break;
        }
        // case DimensionLevel2Enums.AdministrativePenalties:
        case DimensionLevel2Enums.EnvironmentalPenalties:
          datastatus = NoLimitValidNumbersArr;
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                punishdate: {
                  //处罚日期
                  // 目前发现 publishdate 和 occurrencedate 在es中数据一样
                  gte: Math.ceil(startTimestamp / 1000),
                },
              },
            });
          }
          break;
        case DimensionLevel3Enums.ProductQualityProblem2:
        case DimensionLevel2Enums.SpotCheck: {
          const isValid = strategyModel?.detailsParams?.find((po) => po.field === QueryParamsEnums.isValid)?.fieldVal || -1;
          if (Number(isValid) >= 0) {
            // 不限，所有状态数据
            datastatus = [Number(isValid)];
          }
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                publishdate: { gte: Math.ceil(startTimestamp / 1000) },
              },
            });
          }
          break;
        }
        case DimensionLevel2Enums.TaxationOffences: {
          const isValid = strategyModel?.detailsParams?.find((po) => po.field === QueryParamsEnums.isValid)?.fieldVal || -1;
          // -1 1 1 93,
          if (Number(isValid) === 1) {
            // 不限，所有状态数据
            datastatus = [1];
          } else {
            datastatus = [1, 93];
          }
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                publishdate: { gte: Math.ceil(startTimestamp / 1000) },
              },
            });
          }
          break;
        }
        case DimensionLevel2Enums.RegulateFinance: {
          const isValid = strategyModel?.detailsParams?.find((po) => po.field === QueryParamsEnums.isValid)?.fieldVal || -1;
          if (Number(isValid) >= 0) {
            // 不限，所有状态数据
            datastatus = [Number(isValid)];
          }
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                punishdate: {
                  //处罚日期
                  // 目前发现 publishdate 和 occurrencedate 在es中数据一样
                  gte: Math.ceil(startTimestamp / 1000),
                },
              },
            });
          }
          const creditType = strategyModel?.detailsParams?.find((po) => po.field === QueryParamsEnums.creditType)?.fieldVal;
          if (Array.isArray(creditType) && creditType?.length) {
            subBool.bool.must.push({
              terms: {
                publishtype: creditType,
              },
            });
          }
          await this.entLibService.addRelatedKeyNos(ids, companyId, associateObject, 'all');
          break;
        }
        // case DimensionLevel3Enums.ProductQualityProblem9:
        //   if (strategyModel?.cycle > 0) {
        //     subBool.bool.must.push({
        //       range: {
        //         publishdate: { gte: Math.ceil(startTimestamp / 1000) },
        //       },
        //     });
        //   }
        //   subBool.bool.should.push({ match_phrase: { applicant: companyName } }, { terms: { keyno: ids } });
        //   subBool.bool['minimum_should_match'] = 1;
        //   useKeyNo = false;
        //   break;
        case DimensionLevel3Enums.ProductQualityProblem1: {
          if (strategyModel?.cycle > 0) {
            subBool.bool.must.push({
              range: {
                publishdate: { gte: Math.ceil(startTimestamp / 1000) },
              },
            });
          }
          break;
        }
        // case DimensionLevel2Enums.RestrictedOutbound:
        case DimensionLevel2Enums.PersonExecution:
        case DimensionLevel2Enums.ContractBreach:
        case DimensionLevel3Enums.ProductQualityProblem6:
        case DimensionLevel3Enums.ProductQualityProblem7:
        case DimensionLevel3Enums.ProductQualityProblem8: {
          break;
        }
        case DimensionLevel2Enums.Bankruptcy: {
          await this.entLibService.addRelatedKeyNos(ids, companyId, associateObject, 'all');
          datastatus = NoLimitValidNumbersArr;
          break;
        }
        case DimensionLevel2Enums.BondDefaults: {
          const isValid = strategyModel?.detailsParams?.find((po) => po.field === QueryParamsEnums.isValid)?.fieldVal || -1;
          let executestatus = [1, 2]; //当前有效，过滤掉已兑付数据
          if (Number(isValid) === -1) {
            // 不限，所有状态数据
            executestatus = [1, 2, 3];
          }
          subBool.bool.must.push({ terms: { executestatus } });
          break;
        }
      }
      if (strategyModel?.detailsParams) {
        strategyModel.detailsParams.forEach((queryPo) => {
          // const field: QueryParamsEnums = queryPo.field; // publishdate,currencedate,isValid,liandate,amount 等？
          switch (queryPo.field as QueryParamsEnums) {
            case QueryParamsEnums.isValid:
              if (Number(queryPo?.fieldVal) >= 0) {
                datastatus = [Number(queryPo.fieldVal)];
              }
              break;
            case QueryParamsEnums.penaltiesAmount: //处罚金额
            case QueryParamsEnums.equityAmount: //股权数额
            case QueryParamsEnums.taxArrearsAmount: //欠税金额
            case QueryParamsEnums.registrationAmount: //注册金额
              //金额筛选条件
              if (isNumber(queryPo?.fieldVal)) {
                const fieldVal = Number(queryPo?.fieldVal);
                const query: any = {};
                switch (queryPo.fieldOperator) {
                  case OperatorEnums.eq: {
                    Object.assign(query, { term: { amount: fieldVal } });
                    break;
                  }
                  case OperatorEnums.ge: {
                    subBool.bool.must.push({
                      range: {
                        amount: {
                          gte: fieldVal,
                        },
                      },
                    });
                    break;
                  }
                  case OperatorEnums.le: {
                    subBool.bool.must.push({
                      range: {
                        amount: {
                          lte: fieldVal,
                        },
                      },
                    });
                    break;
                  }
                  case OperatorEnums.gt: {
                    subBool.bool.must.push({
                      range: {
                        amount: {
                          gt: fieldVal,
                        },
                      },
                    });
                    break;
                  }
                  case OperatorEnums.lt: {
                    subBool.bool.must.push({
                      range: {
                        amount: {
                          lt: fieldVal,
                        },
                      },
                    });
                    break;
                  }
                }
              }
              break;

            case QueryParamsEnums.executionTarget: //执行标的
            case QueryParamsEnums.capitalReduction: //资本降幅
            case QueryParamsEnums.changeThreshold: //变更阈值
            case QueryParamsEnums.duration: //成立时长
              break;
            case QueryParamsEnums.penaltiesType: {
              //处罚类型
              if (queryPo?.fieldVal?.length > 0) {
                const publishtypeItems: string[] = queryPo?.fieldVal?.map((v) => PenaltiesType?.[v]?.esCode || '').filter((t) => t);
                if (publishtypeItems?.length > 0) {
                  subBool.bool.must.push({
                    terms: {
                      // 'A001', 'A002'
                      publishtype: publishtypeItems,
                    },
                  });
                }
              }
              break;
            }
            case QueryParamsEnums.businessAbnormalType: {
              //处罚类型
              if (queryPo?.fieldVal?.length > 0) {
                const publishtypeItems: string[] = queryPo?.fieldVal?.map((v) => BusinessAbnormalType?.[v]?.esCode || '').filter((t) => t);
                if (publishtypeItems?.length > 0)
                  subBool.bool.must.push({
                    terms: {
                      // 'A001', 'A002'
                      publishtype: publishtypeItems,
                    },
                  });
              }
              break;
            }
          }
        });
      }
      subBool.bool.must.push({
        terms: {
          datastatus,
        },
      });
      if (useKeyNo && ids?.length) {
        subBool.bool.must.push({
          terms: { keyno: ids },
        });
      }
      return subBool;
    }
    return null;
  }
}
