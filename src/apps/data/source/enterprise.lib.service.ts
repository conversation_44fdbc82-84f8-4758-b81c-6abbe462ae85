import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ConfigService } from 'libs/config/config.service';
import { Cacheable } from '@type-cacheable/core';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { cloneDeep, compact, find, flatten, flattenDeep, map, sum, unionWith, uniq, uniqBy } from 'lodash';
import * as Bluebird from 'bluebird';
import { DimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/pojo/req&res/details/response';
import { PersonData } from 'libs/model/data/source/PersonData';
import { CompanyExecutivesResult } from 'libs/model/data/source/CompanyExecutivesResult';
import * as moment from 'moment';
import { DATE_FORMAT } from 'libs/constants/common';
import { DimensionLevel2Enums } from 'libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { toRoundFixed } from 'libs/utils/utils';
import { ContractBreachDegree } from 'libs/constants/model.constants';
import { OperType } from '../../../libs/constants/credit.analyze.constants';
import { AnalyzeService } from './analyze.base.service';
import { DimensionScorePO } from 'libs/model/diligence/pojo/dimension/DimensionScorePO';
import { DimensionLevelClass } from '../../../libs/constants/dimension.constants';
import { processScorePO } from '../../../libs/utils/diligence/diligence.utils';
import { CompanySearchService } from 'apps/company/company-search.service';
import { isOrganism } from '../../company/utils';
import { CompanyDetailService } from '../../company/company-detail.service';
import { HitEnterpriseDimensionQueryParam } from '../../../libs/model/diligence/pojo/req&res/details/request/HitEnterpriseDimensionQueryParam';
import { DimensionAnalyzeParamsPO } from '../DimensionAnalyzeParamsPO';
import { EnterpriseLibDimensionService } from '../dimensions/enterprise.lib.dimension.service';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { ExcludeCompanyService } from 'apps/exclude_company/exclude-company.service';

/**
 * 企业库数据源接口( 企业库 + 风险动态 )
 */
@Injectable()
export class EnterpriseLibService implements AnalyzeService<HitEnterpriseDimensionQueryParam, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(EnterpriseLibService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly companySearchService: CompanySearchService,
    private readonly httpUtils: HttpUtilsService,
    private readonly companyService: CompanySearchService,
    private readonly companyDetailsService: CompanyDetailService,
    private readonly enterpriseLibDimensionService: EnterpriseLibDimensionService,
    private readonly excludeCompanyService: ExcludeCompanyService,
  ) {}

  async analyze(companyId: string, dimensionDefinitionPOs: DimensionDefinitionPO[]): Promise<DimensionScorePO[]> {
    const countInfo = await this.companyDetailsService.getCountInfo(companyId);
    const historyCountInfo = await this.companyDetailsService.getHistoryCountInfo(companyId);
    const countForB = await this.companyDetailsService.getCountForB(companyId);
    return Bluebird.map(dimensionDefinitionPOs, async (d) => {
      let hitCount = 0;
      /**
       * 处理命中描述信息需要的参数
       */
      const desData = {
        level: DimensionLevelClass[d.strategyModel.level || 0],
        cycle: d.strategyModel?.cycle > 0 ? d.strategyModel.cycle : 0,
        name: d.name,
        isHidden: '',
        isHiddenY: '',
      };
      // const validField = d?.strategyModel?.detailsParams?.find((q) => q.field === QueryParamsEnums.isValid)?.fieldVal;
      const needSearch = this.needDetailSearch(d.key, countInfo, historyCountInfo, countForB);
      if (needSearch) {
        switch (d.key) {
          case DimensionLevel3Enums.BusinessAbnormal5: {
            //疑似停产停业:
            if (d.key === DimensionLevel3Enums.BusinessAbnormal5) {
              const company = await this.companyService.companyDetailsQcc(companyId);
              if (['停业', '撤销', '注销', '吊销', '歇业', '责令关闭'].includes(company?.ShortStatus) || company?.EconKind === '个体工商户') {
                return HitDetailsBaseResponse.ok();
              }
            }
            const response = await this.getDimensionDetail(d, { keyNo: companyId });
            hitCount = response?.Paging?.TotalRecords || 0;
            break;
          }
          case DimensionLevel2Enums.TaxArrearsNotice: {
            //欠税公告:
            const taxArrearsNoticeRes = await this.getDimensionDetail(d, { keyNo: companyId });
            if (taxArrearsNoticeRes) {
              const bAmount1 = parseFloat(taxArrearsNoticeRes?.Paging?.['TaxBalanceAmount']);
              hitCount = taxArrearsNoticeRes?.Paging?.TotalRecords || 0;
              if (bAmount1) {
                Object.assign(desData, { amountW: toRoundFixed(bAmount1 / 10000, 2) });
              }
            }
            break;
          }
          case DimensionLevel2Enums.TaxCallNoticeV2: {
            //税务催缴
            const taxCallResult = await this.getDimensionDetail(d, { keyNo: companyId });
            if (taxCallResult) {
              const totalAmount = parseFloat(taxCallResult?.Paging?.['TotalAmount']);
              hitCount = taxCallResult?.Paging?.TotalRecords || 0;
              if (totalAmount) {
                Object.assign(desData, { amountW: toRoundFixed(totalAmount / 10000, 2) });
              }
            }
            break;
          }
          case DimensionLevel2Enums.GuaranteeRisk: // '担保风险': countInfo.GuarantorRiskCount
            const guaranteeRes = await this.getDimensionDetail(d, { keyNo: companyId });
            const riskAmount = sum(map(guaranteeRes?.GroupItems.filter((e) => e.key === 'amount')[0]?.items, 'value'));
            hitCount = guaranteeRes?.Paging?.TotalRecords || 0;
            if (riskAmount) {
              Object.assign(desData, { amountW: toRoundFixed(riskAmount / 10000, 2) });
            }
            break;
          case DimensionLevel3Enums.PersonCreditHistory: // '历史失信被执行人'
            const pccRes = await this.getDimensionDetail(d, {
              keyNo: companyId,
            });
            hitCount = pccRes?.Paging?.TotalRecords || 0;
            const executedAmount1 = pccRes?.GroupItems?.find((e) => e.key === 'amountsum')?.items[0]?.['sum'];
            //被执行金额
            if (executedAmount1) {
              Object.assign(desData, { amountW: toRoundFixed(executedAmount1 / 10000, 2) });
            }
            break;
          case DimensionLevel3Enums.PersonCreditCurrent: {
            // '被列入失信被执行人（当前有效）'
            const pccRes = await this.getDimensionDetail(d, {
              keyNo: companyId,
            });
            hitCount = pccRes?.Paging?.TotalRecords || 0;
            const executedAmount1 = pccRes?.GroupItems?.find((e) => e.key === 'amountsum')?.items[0]?.['sum'];
            //被执行金额
            if (executedAmount1) {
              Object.assign(desData, { amountW: toRoundFixed(executedAmount1 / 10000, 2) });
            }
            break;
          }
          case DimensionLevel2Enums.PersonExecution: {
            // '被执行人'
            const peRes = await this.getDimensionDetail(d, {
              keyNo: companyId,
            });
            hitCount = peRes?.Paging?.TotalRecords || 0;
            const peAmount = parseInt(peRes?.GroupItems?.find((e) => e.key === 'companynames')?.items[0]?.['sum']);
            if (peAmount) {
              Object.assign(desData, { amountW: toRoundFixed(peAmount / 10000, 2) });
            }
            break;
          }
          case DimensionLevel2Enums.ContractBreach: {
            // '合同违约',  countInfo.ContractBreachCount
            const result3 = await this.getDimensionDetail(d, { keyNo: companyId });
            hitCount = result3?.Paging?.TotalRecords || 0;
            // 涉及金额
            if (result3) {
              const bAmount1 = parseFloat(result3?.Result[0]?.TotalAmt);
              const revel = result3?.Result[0]?.Revel;
              const groupCount = parseFloat(result3?.Result[0]?.TotalNum);
              if (bAmount1) {
                Object.assign(desData, { amountW: toRoundFixed(bAmount1 / 10000, 2) });
              }
              if (revel) {
                Object.assign(desData, { degree: ContractBreachDegree?.[revel] });
              }
              if (groupCount) {
                hitCount = groupCount;
              }
            }
            break;
          }
          case DimensionLevel2Enums.BillDefaults: {
            //票据违约
            const peRes = await this.getDimensionDetail(d, {
              keyNo: companyId,
            });
            hitCount = peRes?.Paging?.TotalRecords || 0;
            break;
          }
          case DimensionLevel3Enums.EndExecutionCase: {
            //终本案件
            const endCaseRes = await this.getDimensionDetail(d, { keyNo: companyId });
            hitCount = endCaseRes?.Paging?.TotalRecords || 0;
            if (endCaseRes) {
              const bAmount1 = parseFloat(endCaseRes?.GroupItems?.find((e) => e.key === 'failureGroup')?.items[0]?.['sum']);
              if (bAmount1) {
                Object.assign(desData, { amountW: toRoundFixed(bAmount1 / 10000, 2) });
              }
            }
            break;
          }
          case DimensionLevel2Enums.LandMortgage: {
            // '土地抵押': countInfo.LMCount
            const landMortgageRes = await this.getDimensionDetail(d, { keyNo: companyId });
            if (landMortgageRes) {
              const totalAmount = parseFloat(landMortgageRes?.Paging?.['TotalAmount']);
              hitCount = landMortgageRes?.Paging?.TotalRecords || 0;
              if (totalAmount) {
                Object.assign(desData, { amountW: toRoundFixed(totalAmount, 2) });
              }
            }
            break;
          }
          case DimensionLevel3Enums.DebtOverdue: {
            //债务逾期
            const debtOverdueResult = await this.getDimensionDetail(d, { keyNo: companyId });
            if (debtOverdueResult) {
              const totalAmount = parseFloat(debtOverdueResult?.Paging?.['TotalAmount']);
              hitCount = debtOverdueResult?.Paging?.TotalRecords || 0;
              if (totalAmount) {
                Object.assign(desData, { amountW: toRoundFixed(totalAmount, 2) });
              }
            }
            break;
          }
          case DimensionLevel2Enums.CompanyCreditHistory: // '历史严重违法失信企业名录'
          case DimensionLevel2Enums.CompanyCredit: {
            // '被列入严重违法失信企业名录':
            hitCount = await this.commonGetDimensionDetail(d, companyId);
            break;
          }
          case DimensionLevel2Enums.JudicialAuction: {
            //司法拍卖
            hitCount = await this.commonGetDimensionDetail(d, companyId);
            break;
          }
          case DimensionLevel3Enums.ProductQualityProblem9: {
            // 食品安全检查不合格
            hitCount = await this.commonGetDimensionDetail(d, companyId);
            break;
          }
          case DimensionLevel2Enums.ChattelMortgage: // 动产抵押  MPledgeCount
          case DimensionLevel2Enums.TaxReminder: // 税务催报
          // case DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence: // 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为
          case DimensionLevel3Enums.MainInfoUpdateScope: // '经营范围变更',
          case DimensionLevel3Enums.MainInfoUpdateAddress: // '注册地址变更',
          case DimensionLevel3Enums.MainInfoUpdateName: // '企业名称变更',
          case DimensionLevel3Enums.MainInfoUpdateLegalPerson: // '法定代表人变更',
          case DimensionLevel3Enums.MainInfoUpdateHolder: // '大股东变更',
          case DimensionLevel3Enums.MainInfoUpdateBeneficiary: // '近期变更受益所有人',
          case DimensionLevel3Enums.MainInfoUpdateManager: // '董监高变更',
          case DimensionLevel3Enums.MainInfoUpdatePerson: // '实际控制人变更',
          case DimensionLevel3Enums.CancellationOfFiling: // 注销备案
          case DimensionLevel2Enums.NoCertification:
          case DimensionLevel2Enums.Certification:
          case DimensionLevel3Enums.Liquidation:
          case DimensionLevel2Enums.NoQualityCertification:
          case DimensionLevel3Enums.FinancialHealth:
          case DimensionLevel3Enums.EmployeeReduction:
          case DimensionLevel2Enums.StockPledge:
          case DimensionLevel2Enums.IPRPledge:
          case DimensionLevel2Enums.CompanyShell:
          case DimensionLevel2Enums.FakeRegister: //涉嫌冒名登记
          case DimensionLevel2Enums.ChattelSeizure: //动产查封
          case DimensionLevel3Enums.BusinessAbnormal2:
          case DimensionLevel2Enums.BondDefaults: {
            const result2 = await this.getDimensionDetail(d, { keyNo: companyId });
            hitCount = result2?.Paging?.TotalRecords || 0;
            break;
          }
        }
      }
      if (hitCount) return processScorePO(d, hitCount, desData);
    }).then((item) => item.filter((t) => t));
  }

  /**
   * 获取指定维度详情列表
   * @param dimension
   * @param data
   * @returns
   */
  async getDimensionDetail(dimension: DimensionDefinitionPO, data: HitEnterpriseDimensionQueryParam): Promise<HitDetailsBaseResponse> {
    switch (dimension.key) {
      case DimensionLevel2Enums.CompanyCredit:
      case DimensionLevel2Enums.CompanyCreditHistory: {
        const associateObject = dimension.strategyModel?.detailsParams?.find((q) => q.field === QueryParamsEnums.associateObject);
        data.relatedKeyNos = await this.addRelatedKeyNos([data.keyNo], data.keyNo, associateObject, 'other');
        if (data.relatedKeyNos.length > 200) {
          data.relatedKeyNos = data.relatedKeyNos.slice(0, 200);
        }
        data.originalKeyNo = data.keyNo;
      }
    }
    const response = await this.enterpriseLibDimensionService.getDimensionDetail(dimension, data);
    switch (dimension.key) {
      case DimensionLevel2Enums.CompanyCreditHistory:
      case DimensionLevel2Enums.CompanyCredit: {
        await this.processResponseData(response, dimension, data.originalKeyNo, 'other');
        break;
      }
      default:
    }
    return response;
  }

  public async addRelatedKeyNos(ids: string[], companyId: string, associateObject: any, sockType: string, dimensionKey?: string) {
    if (associateObject?.fieldVal?.length > 0) {
      const { keyNos } = await this.getRelatedCompanyAndPerson(companyId, associateObject.fieldVal, null, sockType);
      if (keyNos?.length > 0) {
        ids.push(...uniq(keyNos));
      }
    } else if (dimensionKey === DimensionLevel3Enums.MainMembersPersonCreditCurrent) {
      const { personNos } = await this.getCompanyExecutivesKeyNosV2(companyId, 'person', true, true);
      ids.push(...uniq(personNos));
    } else if (
      dimensionKey === DimensionLevel3Enums.MainMembersRestrictedOutbound ||
      dimensionKey === DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent ||
      dimensionKey === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence ||
      dimensionKey === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory
    ) {
      const { personNos } = await this.getCompanyExecutivesKeyNosV2(companyId, 'all');
      ids.push(...uniq(personNos));
    }
    if (dimensionKey === DimensionLevel3Enums.MainMembersPersonCreditCurrent && ids.length > 0) {
      ids = ids.filter((id) => id !== companyId);
    }
    return uniq(ids);
  }

  public async addRelatedKeyNosV2(companyId: string, associateObject: any, sockType: string, dimensionKey?: string) {
    const newIds = [];
    if (associateObject?.fieldVal?.length > 0) {
      const { keyNos } = await this.getRelatedCompanyAndPerson(companyId, associateObject.fieldVal, null, sockType, dimensionKey);
      if (keyNos?.length > 0) {
        newIds.push(...uniq(keyNos));
      }
    } else if (dimensionKey === DimensionLevel3Enums.MainMembersPersonCreditCurrent) {
      const { personNos } = await this.getCompanyExecutivesKeyNosV2(companyId, 'person', true, true, undefined, dimensionKey);
      newIds.push(...uniq(personNos));
    } else if (
      dimensionKey === DimensionLevel3Enums.MainMembersRestrictedOutbound ||
      dimensionKey === DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent ||
      dimensionKey === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence ||
      dimensionKey === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory
    ) {
      const { personNos } = await this.getCompanyExecutivesKeyNosV2(companyId, 'all', true, true, undefined, dimensionKey);
      newIds.push(...uniq(personNos));
    }
    return uniq(newIds);
  }

  /**
   * 获取关联企业以及人员
   * @param companyId
   * @param associateObjects 关联对象
   * @param associateExcludes 关联排除
   * @param sockType
   * @param dimensionKey
   * @private
   */
  // @Cacheable({ ttlSeconds: 100 })
  public async getRelatedCompanyAndPerson(companyId: string, associateObjects: any[], associateExcludes: any[], sockType = 'all', dimensionKey?: string) {
    const promiseArr = [];
    const isEmploy = find(associateObjects, { key: DetailsParamEnums.Employ })?.status == 1;
    const isLegal = find(associateObjects, { key: DetailsParamEnums.LegalRepresentative })?.status == 1;
    const isActualControl = find(associateObjects, { key: DetailsParamEnums.ActualController })?.status == 1;
    const isBenefit = find(associateObjects, { key: DetailsParamEnums.Benefit })?.status == 1;
    const isMajorShareholder = find(associateObjects, { key: DetailsParamEnums.MajorShareholder })?.status == 1;
    const isShareholder = find(associateObjects, { key: DetailsParamEnums.Shareholder })?.status == 1;
    const ignoreInvest = associateExcludes ? find(associateExcludes, { key: DetailsParamEnums.ShareHolderInvest })?.status == 1 : false;
    if (isEmploy) {
      // 董监高
      promiseArr.push(this.getEmployeeList(companyId));
    }
    if (isLegal) {
      // 法人代表
      promiseArr.push(this.getLegalPerson(companyId));
    }
    if (isActualControl) {
      // 实际控制人
      promiseArr.push(this.getFinalActualController(companyId));
    }
    if (isBenefit) {
      // 受益所有人
      promiseArr.push(this.getBenefitList(companyId, false));
    }
    if (isShareholder || isMajorShareholder) {
      promiseArr.push(this.getPartnerList(companyId, sockType, ignoreInvest));
    }
    //这里需要深拷贝对象数组，由于上面获取的promise包含缓存，下面构建list时候获取的仍然是原始对象的引用-personJobList.push(f)
    const personData: PersonData[] = flatten(await Bluebird.all(promiseArr));
    let newPersonData: PersonData[] = cloneDeep(personData);
    //过滤排除的公司
    const filteredCompanyIds = await this.excludeCompanyService.filterExcludedCompanyIds(
      newPersonData.map((p) => p?.keyNo),
      dimensionKey,
    );
    newPersonData = newPersonData.filter((p) => filteredCompanyIds.includes(p?.keyNo));
    if (isShareholder || isMajorShareholder) {
      //股东包含大股东，如果只包含大股东，则只显示大股东
      if (!isShareholder && isMajorShareholder) {
        newPersonData = newPersonData.filter((x) => x.tags?.includes('大股东'));
      }
    }
    const personJobList: PersonData[] = [];
    const personNameSet = new Set<string>();
    flattenDeep(compact(newPersonData)).forEach((f) => {
      const nameKeyNo = `${f?.name}${f?.keyNo}`;
      if (personNameSet.has(nameKeyNo)) {
        personJobList.forEach((pf) => {
          if (nameKeyNo === `${pf?.name}${pf?.keyNo}`) {
            pf.tags = uniq(flatten([pf?.tags, f?.tags]));
            pf.job = uniq(compact(pf?.job?.split(',')?.concat(f?.job?.split(','))))?.join(',');
          }
        });
      } else {
        personNameSet.add(nameKeyNo);
        personJobList.push(f);
      }
    });
    let personJobSet: Record<string, string> = {};

    personJobSet[companyId] = '企业主体';
    personJobList.map((e) => {
      if (e?.keyNo) {
        e.name = e.name.replace(/<em>/g, '').replace(/<\/em>/g, '');
        if (personJobSet[e.keyNo]) {
          personJobSet[e.keyNo] = uniq(compact(personJobSet[e.keyNo].split(',').concat(e.job.split(',')))).join(',');
        } else {
          personJobSet[e.keyNo] = e.job;
        }
      }
    });
    //主要人员核心关联方相关风险项，董监高类型过滤独董类型，主要人员不包含公司主体
    personJobSet = this.processPersonJobSet(dimensionKey, personJobSet);
    return { keyNos: Object.keys(personJobSet), personJobSet, personJobList };
  }

  private processPersonJobSet(dimensionKey: string, personJobSet: Record<string, string>) {
    if (
      dimensionKey === DimensionLevel3Enums.MainMembersPersonCreditCurrent ||
      dimensionKey === DimensionLevel3Enums.MainMembersRestrictedOutbound ||
      dimensionKey === DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent ||
      dimensionKey === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence ||
      dimensionKey === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory
    ) {
      const filteredEntries = Object.entries(personJobSet).filter(([key, jobStr]) => {
        const jobs = (jobStr || '').split(',').map((j) => j.trim());
        // 针对特定犯罪相关维度，仅过滤独立董事，不过滤企业主体
        if (
          dimensionKey === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence ||
          dimensionKey === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory
        ) {
          return !(jobs.length === 1 && jobs[0] === '独立董事');
        }
        return !((jobs.length === 1 && jobs[0] === '独立董事') || (jobs.length === 1 && jobs[0] === '企业主体'));
      });
      const newPersonJobSet: Record<string, string> = {};
      filteredEntries.forEach(([key, value]) => {
        newPersonJobSet[key] = value;
      });
      return newPersonJobSet;
    }
    return personJobSet;
  }

  public async processResponseData(resp: HitDetailsBaseResponse, dimension: DimensionDefinitionPO, keyNo: string, sockType: string) {
    let personJobSet;
    if (
      dimension.key === DimensionLevel3Enums.MainMembersPersonCreditCurrent ||
      dimension.key === DimensionLevel3Enums.MainMembersRestrictedOutbound ||
      dimension.key === DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent ||
      dimension.key === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence ||
      dimension.key === DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory
    ) {
      personJobSet = await this.getRelatedCompanyAndPersonJobSetV2(resp, dimension, keyNo, sockType);
    } else {
      personJobSet = await this.getRelatedCompanyAndPersonJobSet(resp, dimension, keyNo, sockType);
    }
    switch (dimension.key) {
      case DimensionLevel3Enums.MainMembersRestrictedOutbound: {
        resp?.Result?.forEach((x) => {
          x['mainKeyNo'] = keyNo; // 同一条数据在生成快照唯一键时会相同，导致关联关系会出现串的情况 RA-13401，这里设置主 KeyNo
          // 限制对象
          x?.SubjectInfo?.forEach((p) => {
            p['Job'] = personJobSet[p?.KeyNo];
            if (p['Job']) {
              p['description'] = `${p.Name}【${p.Job}】`;
            }
          });
        });
        break;
      }
      case DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent: {
        resp?.Result?.forEach((x) => {
          x['mainKeyNo'] = keyNo;
          // 限消令对象
          x['SubjectInfo'] = x.NameAndKeyNo.filter((a) => a.KeyNo == x.KeyNo);
          x?.SubjectInfo?.forEach((p) => {
            p['Job'] = personJobSet[p?.KeyNo];
            if (p.Job) {
              p['description'] = `${p.Name}【${p.Job}】`;
            }
          });
          // 限消令关联对象
          x['PledgorInfo'] = x.NameAndKeyNo.filter((a) => x?.p_pledgor_id?.includes(a.KeyNo) || x?.pledgor_id?.includes(a.KeyNo));
          x?.PledgorInfo?.forEach((p) => {
            p['Job'] = personJobSet[p?.KeyNo];
            if (p.Job) {
              p['description'] = `${p.Name}【${p.Job}】`;
            }
          });
          Object.assign(x, {
            FileUrl: x?.osskey?.length > 0 ? `https://qccdata.qichacha.com/CountXG/${x.osskey}` : '',
            // 申请人
            ApplicantInfo: x.Specs,
          });
        });
        break;
      }
      case DimensionLevel2Enums.Bankruptcy: {
        resp?.Result?.forEach((x) => {
          x['mainKeyNo'] = keyNo;
          if (!x.NameAndKeyNo) {
            x['NameAndKeyNo'] = [{ KeyNo: x.KeyNo, Name: x.CompanyName }];
          }
          x['SubjectInfo'] = x.NameAndKeyNo?.filter((a) => a.KeyNo == x.KeyNo);
          x?.SubjectInfo?.forEach((p) => {
            p['Job'] = personJobSet[p.KeyNo];
            if (p.Job) {
              p['description'] = `${p.Name}【${p.Job}】`;
            }
          });
          // 申请人明文处理
          const nameGroups = new Map<string, string[]>();
          for (const item of x.NameAndKeyNo) {
            if (item.ShowName && item.Name) {
              const group = nameGroups.get(item.ShowName) || [];
              group.push(item.Name);
              nameGroups.set(item.ShowName, group);
            }
          }
          x.ApplicantInfo?.forEach((item) => {
            const originalKey = item.Name;
            if (nameGroups.has(originalKey)) {
              const group = [...(nameGroups.get(originalKey) || [])]; // 数组副本
              if (group.length > 0) {
                // 取出第一个可用值
                item.Name = group.shift();
                // 更新映射表（使用原始Key）
                nameGroups.set(originalKey, group);
              }
            }
          });
        });
        break;
      }
      default:
        resp?.Result?.forEach((x) => {
          x['mainKeyNo'] = keyNo;
          if (!x.NameAndKeyNo) {
            x['NameAndKeyNo'] = [{ KeyNo: x.KeyNo, Name: x.CompanyName }];
          }
          x['SubjectInfo'] = x.NameAndKeyNo?.filter((a) => a.KeyNo == x.KeyNo || a.KeyNo == '');
          x?.SubjectInfo?.forEach((p) => {
            p['Job'] = personJobSet[p.KeyNo];
            if (p.Job) {
              p['description'] = `${p.Name}【${p.Job}】`;
            }
          });
        });
    }
  }

  public async getRelatedCompanyAndPersonJobSet(resp: HitDetailsBaseResponse, dimension: DimensionDefinitionPO, keyNo: string, sockType: string) {
    const associateObject = dimension.strategyModel.detailsParams?.find((q) => q.field === QueryParamsEnums.associateObject);
    let personJobSet = {};
    if (associateObject?.fieldVal?.length > 0) {
      const associateObjectArr = associateObject.fieldVal;
      const isMajorShareholder = find(associateObjectArr, { key: DetailsParamEnums.MajorShareholder })?.status == 1;
      const res = await this.getRelatedCompanyAndPerson(keyNo, associateObjectArr, null, sockType);
      personJobSet = res.personJobSet;
      res.personJobList?.forEach((x) => {
        if (x.tags?.includes('大股东') && isMajorShareholder) {
          // 大股东的 Job 也是股东，这里处理一下
          personJobSet[x.keyNo] = personJobSet[x.keyNo]?.replace('股东', '大股东');
        }
      });
    } else if (
      dimension.key === DimensionLevel3Enums.MainMembersPersonCreditCurrent ||
      dimension.key === DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent ||
      dimension.key === DimensionLevel3Enums.MainMembersRestrictedOutbound
    ) {
      const res = await this.getCompanyExecutivesKeyNosV2(keyNo);
      personJobSet = res.personJobSet;
    } else {
      const res = await this.getCompanyExecutivesKeyNosV2(keyNo, 'all');
      personJobSet = res.personJobSet;
    }
    return personJobSet;
  }

  public async getRelatedCompanyAndPersonJobSetV2(resp: HitDetailsBaseResponse, dimension: DimensionDefinitionPO, keyNo: string, sockType: string) {
    const associateObject = dimension.strategyModel.detailsParams?.find((q) => q.field === QueryParamsEnums.associateObject);
    let personJobSet = {};
    if (associateObject?.fieldVal?.length > 0) {
      const associateObjectArr = associateObject.fieldVal;
      const isMajorShareholder = find(associateObjectArr, { key: DetailsParamEnums.MajorShareholder })?.status == 1;
      const res = await this.getRelatedCompanyAndPerson(keyNo, associateObjectArr, null, sockType, dimension.key);
      personJobSet = res.personJobSet;
      res.personJobList?.forEach((x) => {
        if (x.tags?.includes('大股东') && isMajorShareholder) {
          // 大股东的 Job 也是股东，这里处理一下
          personJobSet[x.keyNo] = personJobSet[x.keyNo]?.replace('股东', '大股东');
        }
      });
    } else if (
      dimension.key === DimensionLevel3Enums.MainMembersPersonCreditCurrent ||
      dimension.key === DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent ||
      dimension.key === DimensionLevel3Enums.MainMembersRestrictedOutbound
    ) {
      const res = await this.getCompanyExecutivesKeyNosV2(keyNo, 'person', true, true, null, dimension.key);
      personJobSet = res.personJobSet;
    } else {
      const res = await this.getCompanyExecutivesKeyNosV2(keyNo, 'all', true, true, null, dimension.key);
      personJobSet = res.personJobSet;
    }
    return personJobSet;
  }

  /**
   * 获取公司高管keyNo:Job list
   * @param keyNo
   * @param stockType all-全部类型， person-自然人股东, other-非自然人股东
   * @param ignoreInvest — 过滤投资机构 ture=忽略投资机构
   * @param ignoreCompany 过滤公司主体
   * @param searchKey
   * @param dimensionKey
   */
  // @Cacheable({ ttlSeconds: 300 })
  async getCompanyExecutivesKeyNosV2(
    keyNo: string,
    stockType = 'person',
    ignoreInvest = true,
    ignoreCompany = false,
    searchKey?: string,
    dimensionKey?: string,
  ): Promise<CompanyExecutivesResult> {
    let personList: PersonData[][];
    if (isOrganism(keyNo)) {
      personList = await Bluebird.all([
        this.getLegalPerson(keyNo), // 法定代表人
        this.getOrganismPerson(keyNo), //社会组织主要人员
      ]);
    } else {
      personList = await Bluebird.all([
        this.getLegalPerson(keyNo), // 法定代表人
        this.getEmployeeList(keyNo), // 获取(当前)主要人员（最新公示+工商登记） 董监高
        this.getPartnerList(keyNo, stockType, ignoreInvest, 200, searchKey), // 获取股东信息  （最新公示+工商登记）
        this.getBenefitList(keyNo), // 受益所有人
      ]);
    }
    // 过滤排除的公司
    const flattenPersonList = flattenDeep(compact(personList));
    const filteredCompanyIds = await this.excludeCompanyService.filterExcludedCompanyIds(
      flattenPersonList.map((p) => p?.keyNo),
      dimensionKey,
    );
    const filteredPersonList = flattenPersonList.filter((p) => filteredCompanyIds.includes(p?.keyNo));
    const personJobList: PersonData[] = [];
    const personNameSet = new Set<string>();
    filteredPersonList.forEach((f) => {
      const nameKeyNo = `${f?.name}${f?.keyNo}`;
      if (personNameSet.has(nameKeyNo)) {
        personJobList.forEach((pf) => {
          if (nameKeyNo === `${pf?.name}${pf?.keyNo}`) {
            pf.tags = uniq(flatten([pf?.tags, f?.tags]));
            pf.job = uniq(compact(pf?.job?.split(',')?.concat(f?.job?.split(','))))?.join(',');
          }
        });
      } else {
        personNameSet.add(nameKeyNo);
        personJobList.push(f);
      }
    });
    let personJobSet: Record<string, string> = {};

    if (!ignoreCompany) {
      personJobSet[keyNo] = '企业主体';
    }
    personJobList.map((e) => {
      if (e?.keyNo) {
        e.name = e.name.replace(/<em>/g, '').replace(/<\/em>/g, '');
        if (personJobSet[e.keyNo]) {
          personJobSet[e.keyNo] = uniq(compact(personJobSet[e.keyNo].split(',').concat(e.job.split(',')))).join(',');
        } else {
          personJobSet[e.keyNo] = e.job;
        }
      }
    });
    //主要人员核心关联方相关风险项，董监高类型过滤独董类型，主要人员不包含公司主体
    personJobSet = this.processPersonJobSet(dimensionKey, personJobSet);
    return { personNos: Object.keys(personJobSet), personJobSet, personJobList };
  }

  /**
   * 获取子公司列表keyNo list
   * @param keyNo
   */
  async getBranchKeyNos(keyNo: string): Promise<string[]> {
    return await this.companyDetailsService.getBranchKeyNos(keyNo);
  }

  /**
   * 获取法定代表人
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getLegalPerson(keyNo: string): Promise<PersonData[]> {
    const result: PersonData[] = [];
    const companyDetail = await this.companySearchService.companyDetailsQcc(keyNo);

    if (companyDetail?.Oper?.Name) {
      result.push(
        Object.assign(new PersonData(), {
          name: companyDetail?.Oper?.Name,
          keyNo: companyDetail?.Oper?.KeyNo,
          job: OperType[companyDetail?.Oper?.OperType?.toString()] || OperType['1'],
        }),
      );
    }
    return result;
  }

  /**
   * 获取历史法定代表人
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getHisLegalPerson(keyNo: string): Promise<PersonData[]> {
    const personList: PersonData[] = [];
    const dataResult = await this.companyDetailsService.getCoyHistoryInfo(keyNo);
    // 工商变更记录分类型 对应不同的维度处理
    const OperList = dataResult?.Result?.OperList || [];
    if (OperList?.length) {
      OperList?.filter((e) => e?.Org === 2).forEach((item) => {
        personList.push(
          Object.assign(new PersonData(), {
            name: item?.OperName,
            job: '历史法定代表人',
            keyNo: item?.KeyNo,
            tags: item?.Tags || [],
            history: true,
          }),
        );
      });
    }
    return personList;
  }

  /**
   * 获取法定代表人（不限）
   * @param keyNo
   */
  public async getAllLegalPerson(keyNo: string): Promise<PersonData[]> {
    const [currentList, historyList] = await Bluebird.all([this.getLegalPerson(keyNo), this.getHisLegalPerson(keyNo)]);
    return unionWith(currentList, historyList);
  }

  /**
   * 获取 实际控制人（疑似）
   * @param keyNo
   * @returns
   */
  public async getActualController(keyNo: string) {
    try {
      const res = await this.companyDetailsService.getSuspectedActualControllerV5(keyNo);
      return res?.Result;
    } catch (e) {
      this.logger.error(`http Get QccSearch/GetSuspectedActualControllerV5 keyNo: ${keyNo}`);
      return null;
    }
  }

  /**
   * 分支机构
   * @param keyNo
   * @returns
   */
  public async getBranchList(keyNo: string) {
    return this.companyDetailsService.getBranchList(keyNo);
  }

  /**
   * 控制企业
   * @param keyNo
   * @returns
   */
  public async getHoldingCompany(keyNo: string) {
    return this.companyDetailsService.getHoldingCompany({ keyNo });
  }

  public async getChangeInfo(keyNo: string, pageSize = 10, pageIndex = 1) {
    const params = {
      keyNo,
      pageSize,
      pageIndex,
    };
    return this.companyDetailsService.getChangeInfoList(params);
  }

  /**
   * 获取社会组织主要人员，带分页
   * @param keyNo
   * @param pageSize
   * @param pageIndex
   */
  public async getOrganismPersonPaging(keyNo: string, pageSize = 10, pageIndex = 1) {
    const params = {
      keyNo,
      pageSize,
      pageIndex,
    };
    return this.companyDetailsService.getOrganismPersonList(params);
  }

  /**
   * 社会组织获取主要人员
   * @param keyNo
   */
  public async getOrganismPerson(keyNo: string): Promise<PersonData[]> {
    try {
      const param = {
        keyNo,
        pageSize: 200,
        pageIndex: 1,
      };
      const limited = 3000;
      let totalFetched = 0;
      const resultItems = [];
      do {
        const res1 = await this.companyDetailsService.getOrganismPersonList(param);

        // 检查res1是否为null，如果为null则退出循环
        if (!res1 || res1?.Status > 200) {
          break;
        }

        if (res1.Result?.length) {
          totalFetched += res1.Result.length;
          resultItems.push(...res1.Result);
        }
        if (!res1.Result?.length || res1.Result.length < param.pageSize || totalFetched >= limited) {
          break;
        }
        param.pageIndex++;
      } while (true);
      return resultItems.map((item) => {
        return Object.assign(new PersonData(), {
          name: item?.PersonName,
          job: item?.Position,
          keyNo: item?.PersonKey,
        });
      });
    } catch (e) {
      this.logger.error(`http Get QccSearch/List/OrganismPerson err:`, e);
      return null;
    }
  }

  /**
   * 获取(当前)主要人员 董监高
   * 先取最新公示数据，如果没有取工商登记数据
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getEmployeeList(keyNo: string): Promise<PersonData[]> {
    const result: PersonData[] = [];
    if (isOrganism(keyNo)) {
      return this.getOrganismPerson(keyNo);
    }
    try {
      await this.getEmployeeData(keyNo, 'IpoEmployees', result);
      if (result?.length) {
        return result;
      }
    } catch (e) {
      this.logger.error('getEmployeeList IpoEmployees error:', e);
    }
    try {
      await this.getEmployeeData(keyNo, 'Employees', result);
    } catch (e) {
      this.logger.error('getEmployeeList Employees error:', e);
    }
    return result;
  }

  /**
   * 获取历史主要人员
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getHisEmployeeList(keyNo: string): Promise<PersonData[]> {
    const personList: PersonData[] = [];
    const params = {
      keyNo,
      pageSize: 999,
    };
    let res = await this.companyDetailsService.getCoyHistoryEmployee2Info(params);
    if (res?.Paging?.TotalRecords > 999) {
      Object.assign(params, {
        pageSize: res?.Paging?.TotalRecords,
      });
      res = await this.companyDetailsService.getCoyHistoryEmployee2Info(params);
    }
    res?.Result?.forEach((item) => {
      personList.push(
        Object.assign(new PersonData(), {
          name: item?.EmployeeName,
          job:
            compact(item?.Job?.split(','))
              ?.map((j) => `历史${j}`)
              .join(',') || '历史高管',
          keyNo: item?.KeyNo,
          tags: item?.Tags || [],
          history: true,
        }),
      );
    });
    return personList;
  }

  /**
   * 获取主要人员（不限）
   * @param keyNo
   */
  public async getAllEmployeeList(keyNo: string): Promise<PersonData[]> {
    const [currentList, historyList] = await Bluebird.all([this.getEmployeeList(keyNo), this.getHisEmployeeList(keyNo)]);
    return unionWith(currentList, historyList);
  }

  /**
   * 获取股东信息 （最新公示+工商登记），
   * 先取最新公示数据，如果没有取工商登记数据
   * @param keyNo
   * @param stockType all-全部类型， person-自然人股东, other-非自然人股东
   * @param ignoreInvest — 过滤投资机构 ture=忽略投资机构
   * @param pageSize
   * @param searchKey
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getPartnerList(keyNo: string, stockType = 'person', ignoreInvest = false, pageSize = 200, searchKey?: string): Promise<PersonData[]> {
    const result: PersonData[] = [];
    ignoreInvest = false; // 投资机构过滤不精准，目前先不支持

    try {
      // type = IpoPartners 最新公示的股东
      await this.getPartnerData(keyNo, stockType, 'IpoPartners', result, ignoreInvest, pageSize, searchKey);

      // 如果最新公示有数据，直接返回
      if (result?.length) {
        return result;
      }
    } catch (error) {
      this.logger.error('getPartnerList IpoPartners error:', error);
    }

    try {
      // 如果最新公示没有数据，取工商登记的股东
      await this.getPartnerData(keyNo, stockType, 'Partners', result, ignoreInvest, pageSize, searchKey);
    } catch (error) {
      this.logger.error('getPartnerList Partners error:', error);
    }

    return result;
  }

  /**
   * 获取历史股东信息
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getHistoryPartnerList(keyNo: string): Promise<PersonData[]> {
    const param = {
      keyNo,
      pageSize: 200,
      pageIndex: 1,
    };
    const limited = 3000;
    let totalFetched = 0;
    const items = [];
    do {
      const res1 = await this.companyDetailsService.getCoyHistoryPartnerInfo(
        Object.assign(param, {
          pageIndex: param.pageIndex,
        }),
      );

      // 检查res1是否为null，如果为null则退出循环
      if (!res1 || res1?.Status > 200) {
        break;
      }

      if (res1.Result) {
        totalFetched += res1.Result.length;
        Array.prototype.push.apply(items, res1.Result);
      }

      if (!res1.Result || res1.Result.length < param.pageSize || totalFetched >= limited) {
        break;
      }
      param.pageIndex++;
    } while (true);
    return items
      .filter((e) => e?.Org === 2)
      .map((item) => {
        return Object.assign(new PersonData(), {
          name: item.PartnerName,
          job: '历史股东',
          keyNo: item.KeyNo,
          tags: item?.Tags || [],
          history: true,
          stockPercent: item.StockPercent ? Number(item.StockPercent.replace('%', '')) : undefined,
        });
      });
  }

  /**
   * 获取股东信息（不限）
   * @param keyNo
   */
  public async getAllPartnerList(keyNo: string): Promise<PersonData[]> {
    const [currentList, historyList] = await Bluebird.all([this.getPartnerList(keyNo), this.getHistoryPartnerList(keyNo)]);
    return unionWith(currentList, historyList);
  }

  /**
   * 获取动态数据（工商维度）
   * @param dimension
   * @param data
   * @param category
   */
  async getMainInfoUpdateHolder(dimension: DimensionDefinitionPO, data: Record<string, any>, category: string): Promise<any> {
    const sourcePath = dimension?.detailSourcePath ? dimension.detailSourcePath : dimension.sourcePath;

    const params = {
      keyNo: data.keyNo,
      pageIndex: data.pageIndex,
      pageSize: data.pageSize,
      category,
    };

    const cycle: number = dimension?.strategyModel?.cycle || 1;

    if (cycle > 0) {
      const dateStart = moment().subtract(cycle, 'year').format(DATE_FORMAT);
      const dateEnd = moment().format(DATE_FORMAT);

      Object.assign(params, { dateStart, dateEnd });
    }

    return await this.httpUtils.getRequest(this.configService.proxyServer.roverService + sourcePath, params);
  }

  /**
   * 获取最终受益人，受益自然人
   * @param keyNo
   * @param isBenefit 是否为最终受益人 true=最终受益人（受益所有人） false=受益自然人，默认为 true
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getBenefitList(keyNo: string, isBenefit = true): Promise<PersonData[]> {
    const result: PersonData[] = [];
    const benefit = '受益所有人';
    try {
      const params = {
        keyNo,
        isBenefit,
      };
      const res = await this.companyDetailsService.getBenefitDetail(params);
      res?.Result?.Names?.forEach((e) => {
        result.push(
          Object.assign(new PersonData(), {
            name: e.Name,
            keyNo: e.KeyNo,
            tag: [benefit],
            job: [...compact(map(e?.BenefitTypeInfo, 'Job')), benefit]?.join(','),
          }),
        );
      });
    } catch (error) {
      this.logger.error('getBenefitList error:', error);
    }
    return result;
  }

  /**
   * 查询主要人员
   * @param keyNo
   * @param type  type = IpoEmployees 最新公示的主要人员  type = Employees 工商登记的主要人员
   * @param personList
   */
  public async getEmployeeData(keyNo: string, type: string, personList: PersonData[]) {
    try {
      const res = await this.getEmployee(keyNo, type, 200);

      res?.Result?.forEach((item) => {
        personList.push(
          Object.assign(new PersonData(), {
            name: item.Name,
            job: item?.Job || '高管',
            keyNo: item.KeyNo,
            tags: item.Tags,
          }),
        );
      });
      return personList;
    } catch (error) {
      this.logger.error('getEmployeeData error:', error);
      return [];
    }
  }

  public async getEmployee(keyNo: string, type: string, pageSize = 10) {
    const params = {
      keyNo,
      nodeName: type,
      pageSize,
    };
    return await this.companyDetailsService.getEmployeeList(params);
  }

  /**
   * 查询公司股东信息
   * @param keyNo
   * @param stockType  all-全部类型， person-自然人股东, other-非自然人股东
   * @param type  type = IpoPartners 最新公示的股东  type = Partners 工商登记的股东
   * @param personList
   * @param ignoreInvest 过滤投资机构 ture=忽略投资机构
   * @param pageSize
   * @param searchKey
   */
  public async getPartnerData(
    keyNo: string,
    stockType: string,
    type: string,
    personList: PersonData[],
    ignoreInvest = false,
    pageSize = 200,
    searchKey?: string,
  ) {
    const partners = await this.getPartner(keyNo, type, pageSize, searchKey);

    partners?.Result?.forEach((item) => {
      const person = Object.assign(new PersonData(), {
        name: item.StockName,
        job: compact(['股东', item?.Job]).join(','),
        keyNo: item.KeyNo,
        tags: item.Tags,
        stockPercent: item.StockPercent,
        shouldCapi: item.ShouldCapi,
        shoudDate: item.ShoudDate,
      });
      switch (stockType) {
        case 'person':
          if (item.Org === 2) {
            personList.push(person);
          }
          break;
        case 'other':
          if (item.Org !== 2) {
            if (!ignoreInvest || !item?.Invest) {
              personList.push(person);
            }
          }
          break;
        default:
          if (!ignoreInvest || !item?.Invest) {
            personList.push(person);
          }
          break;
      }
    });
  }

  /**
   * TODO 当人员是股东列表的时候，可能会存在列表数量过多，进而导致上游尽调服务构造es查询条件时候超限(目前存在较多抽象方便的定义涉及到es query，修改代价较大)
   * 所以，暂定只取3000条
   *
   * 公司股东数量统计：
   * 股东最多的是12476，
   * 3000以上的有30 个，
   * 2000以上的92个，
   * 1000 有451个，
   * 500 有1720 ，
   * 200 以上的有8500个
   *
   * @param keyNo
   * @param type
   * @param pageSize
   * @param searchKey
   */
  public async getPartner(keyNo: string, type: string, pageSize = 500, searchKey?: string): Promise<{ Result: any[] }> {
    const param = {
      keyNo,
      type,
      pageSize,
      pageIndex: 1,
    };
    if (searchKey) {
      Object.assign(param, { searchKey });
    }
    const limited = 3000;
    let totalFetched = 0;
    const res: {
      Result: any[];
    } = {
      Result: [],
    };
    do {
      const res1 = await this.companyDetailsService.getPartnerWithGroup(param);

      // 检查res1是否为null，如果为null则退出循环
      if (!res1 || res1?.Status > 200) {
        break;
      }

      if (res1.Result) {
        totalFetched += res1.Result.length;
        Array.prototype.push.apply(res.Result, res1.Result);
      } else {
        break;
      }
      if (totalFetched >= limited || totalFetched >= res1.Paging?.TotalRecords || res1.Result.length < param.pageSize) {
        break;
      }
      param.pageIndex++;
    } while (true);

    return res;
  }

  /**
   * 获取最终实际控制人
   * 先从ActualControl取，ActualControl没值再从 FinalActualControl 里取，再没值从Names里取
   * @param keyNo
   */
  public async getFinalActualController(keyNo: string): Promise<PersonData[]> {
    const actualControllerResponse = await this.getActualController(keyNo);
    const sourceCompanyId = actualControllerResponse?.KeyNo;
    const sourceCompanyName = actualControllerResponse?.CompanyName;
    let finalActPersonList: PersonData[] = [];
    if (!actualControllerResponse) {
      return finalActPersonList;
    }
    const getFilteredPersonList = (personList: any[]) => {
      if (!personList.length) {
        return finalActPersonList;
      }
      const filteredPersonList = personList
        .filter((p) => p?.KeyNo?.length > 0)
        .map((e) => ({
          keyNo: e.KeyNo,
          name: e.Name,
          sourceCompanyId,
          sourceCompanyName,
          stockPercent: e.PercentTotal,
        }));
      return uniqBy(filteredPersonList, (x) => x.keyNo).filter((p) => p.keyNo.startsWith('p')) || [];
    };
    finalActPersonList = getFilteredPersonList(actualControllerResponse?.ActualControl?.PersonList || []);
    // 如果没有取到实际控制人或者 FinalActualControl有值，再从 FinalActualControl 里取
    if (actualControllerResponse.FinalActualControl) {
      finalActPersonList = getFilteredPersonList(actualControllerResponse.FinalActualControl?.PersonList || []);
    }
    if (actualControllerResponse?.Names?.length) {
      actualControllerResponse.Names.forEach((nameGroup) => {
        nameGroup?.Names.PersonList?.forEach((e) => {
          if (finalActPersonList?.length) {
            finalActPersonList.forEach((f) => {
              // if (f.keyNo === e.KeyNo) {
              //   f.tags = e?.Tags?.length ? e.Tags : ['实际控制人'];
              //   f.job = e.Job ? [...e.Job.split(','), '实际控制人'].join(',') : '实际控制人';
              // }
              f.tags = e?.Tags?.length ? e.Tags : ['实际控制人'];
              f.job = e.Job ? [...e.Job.split(','), '实际控制人'].join(',') : '实际控制人';
            });
          } else {
            finalActPersonList.push(
              Object.assign(new PersonData(), {
                name: e.Name,
                keyNo: e.KeyNo,
                tags: e?.Tags?.length ? e.Tags : ['实际控制人'],
                job: e.Job ? [...e.Job.split(','), '实际控制人'].join(',') : '实际控制人',
                stockPercent: e.PercentTotal,
                sourceCompanyId,
                sourceCompanyName,
              }),
            );
          }
        });
      });
    }
    return finalActPersonList;
  }

  private async commonGetDimensionDetail(d, companyId: string) {
    const result2 = await this.getDimensionDetail(d, { keyNo: companyId });
    return result2?.Paging?.TotalRecords || 0;
  }

  /**
   * 判断是否需要查详情
   * @param key
   * @param countInfo
   * @param historyCountInfo
   * @param countForB
   * @private
   */
  private needDetailSearch(key, countInfo: any, historyCountInfo: any, countForB: any) {
    // 部分维度只有三个结果都有值，且都等于 0，才不需要查询详情
    try {
      switch (key) {
        case DimensionLevel2Enums.ChattelMortgage: // 动产抵押
          if (countInfo['MPledgeCount'] === 0 && historyCountInfo['MpledgeCount'] === 0 && countForB['MpledgeBlockCount'] === 0) {
            return false;
          }
          break;
        case DimensionLevel3Enums.ProductQualityProblem9: // 食品安全
          if (!countInfo?.['FoodSafetyCount']) {
            return false;
          }
          break;
        case DimensionLevel2Enums.JudicialAuction: // 司法拍卖
          if (!countInfo?.['JudicialSaleCount']) {
            return false;
          }
          break;
        case DimensionLevel2Enums.BillDefaults: // 票据违约
          if (!countInfo?.['BillDefaultCount']) {
            return false;
          }
          break;
        case DimensionLevel2Enums.ContractBreach: // 合同违约
          if (countInfo['ContractBreachCount'] === 0) {
            return false;
          }
          break;
        case DimensionLevel3Enums.PersonCreditCurrent: // 失信被执行人
        case DimensionLevel3Enums.PersonCreditHistory: // 历史失信被执行人
          if (countInfo['ShiXinCount'] === 0 && historyCountInfo['SXCount'] && countForB['ShiXinCount']) {
            return false;
          }
          break;
        case DimensionLevel2Enums.GuaranteeRisk: // '担保风险'
          if (countInfo['GuaranteeRiskCount'] === 0) {
            return false;
          }
          break;
        case DimensionLevel2Enums.CompanyCreditHistory: // '历史严重违法失信企业名录'
        case DimensionLevel2Enums.CompanyCredit: // '严重违法失信企业名录'
          if (countInfo['SVCount'] === 0 && historyCountInfo['SVCount'] === 0 && countForB['SeriousViolationCount'] === 0) {
            return false;
          }
          break;
        case DimensionLevel2Enums.LandMortgage: // '土地抵押'
          if (countInfo['LMCount'] === 0 && historyCountInfo['LMCount'] === 0 && countForB['LandMortgageCount'] === 0) {
            return false;
          }
          break;
        case DimensionLevel2Enums.TaxArrearsNotice: // '欠税公告'
          if (countInfo['QSGGCount'] === 0 && historyCountInfo['HisOweTaxCount'] === 0 && countForB['OweTaxNoticeCount'] === 0) {
            return false;
          }
          break;
        case DimensionLevel2Enums.PersonExecution: // 被执行人
          if (countInfo['ZhiXingCount'] === 0 && historyCountInfo['ZXCount'] === 0 && countForB['ZhiXingCount'] === 0) {
            return false;
          }
          break;
        case DimensionLevel3Enums.EndExecutionCase: // 终本案件
          if (countInfo['EndExecutionCaseCount'] === 0 && historyCountInfo['EndExecutionCaseCount'] === 0 && countForB['EndExecutionCaseCount'] === 0) {
            return false;
          }
          break;
        default:
          return true;
      }
      return true;
    } catch (error) {
      // 取值异常，说明字段可能不存在，需要查询详情
      return true;
    }
  }
}
