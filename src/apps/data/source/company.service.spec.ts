import { Test } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { BaseDimensions } from '../../../libs/constants/dimension.base.constants';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { AppTestModule } from '../../app/app.test.module';
import { DataModule } from '../data.module';
import { CompanyService } from './company.service';
jest.setTimeout(60 * 1000);
describe('工商维度查询测试', () => {
  let companyService: CompanyService;
  let entityManager: EntityManager;
  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    companyService = module.get(CompanyService);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it.skip('无统一社会信用代码', async () => {
    // 未找到测试数据
    const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal7];
    const result = await companyService.getDimensionDetail(dimension, {
      keyNo: '8051867d0a3d25e91879beed7c7eafba',
      pageIndex: 1,
      pageSize: 10,
      companyName: '葫芦岛市泳乐琪制衣有限公司',
    });
    expect(result).toBeDefined();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('LowCapital analyze', async () => {
    const testCompanyId = 'saa4038da14a479a0bf176ff569bc5b7'; //滁州市金和保安服务有限公司
    const dimension = BaseDimensions[DimensionLevel2Enums.LowCapital];
    // //默认查全部
    //const result = await companyService.analyze(testCompanyId, [dimension]);

    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: testCompanyId,
      pageSize: 5,
      pageIndex: 1,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(0);
  });

  it('FakeSOES analyze', async () => {
    const testCompanyId = 'b99a568fb0749ea5d8f2ce58055ac0a4'; // 北京水润达国际贸易有限公司
    const dimension = BaseDimensions[DimensionLevel2Enums.FakeSOES];
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: testCompanyId,
      pageSize: 5,
      pageIndex: 1,
    });
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('NoCapital analyze', async () => {
    const testCompanyId = 'ge88ee6812608eb27c184400469dbb3c'; //互利消防技术有限公司
    const dimension = BaseDimensions[DimensionLevel2Enums.NoCapital];
    const result = await companyService.analyze(testCompanyId, [dimension]);
    expect(result[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: testCompanyId,
      pageSize: 5,
      pageIndex: 1,
    });
    expect(detail.Paging.TotalRecords).toEqual(1);
  });

  it('经营期限已到期或临近到期 DimensionLevel3Enums.BusinessAbnormal9', async () => {
    const testCompanyId = 'a8bd5ea3bb4e360378edce9cdf1ae4c1'; //张江汉世纪创业投资有限公司
    const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal9];
    // const detail = await companyService.getDimensionDetail(dimension, {
    //   keyNo: testCompanyId,
    //   pageSize: 5,
    //   pageIndex: 1,
    // });
    // expect(detail.Paging.TotalRecords).toEqual(1);
    // expect(detail.Result[0].termStatus).toEqual('临近到期');
    const detail2 = await companyService.getDimensionDetail(dimension, {
      keyNo: '5bb4c191fe07a77974a9b7ee5a1c76d5', //上海跳跳猫数字科技有限公司
      pageSize: 5,
      pageIndex: 1,
    });
    expect(detail2.Paging.TotalRecords).toEqual(1);
    expect(detail2.Result[0].termStatus).toEqual('已到期');
  });

  it.skip('临近经营期限 BusinessAbnormal8', async () => {
    // 未找到测试数据
    const testCompanyId = 'a37df92b828d3dc7706078d125efbf0a'; //南通卞秋莎国际贸易有限公司
    const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal8];
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: testCompanyId,
      pageSize: 5,
      pageIndex: 1,
    });
    expect(detail.Paging.TotalRecords).toEqual(1);
  });

  it('经营期限已过有效期 DimensionLevel3Enums.BusinessAbnormal6', async () => {
    //吉林省华建工程设备有限公司
    // 成都市川府机械有限责任公司
    // 沈阳顾嘉建筑机械租赁有限公司
    // 瑞丽彩云南集团瑞丽市滇野木屋制造有限公司
    const testCompanyId = '01bfdd398efeba75bcf791cd7e83177d'; //吉林省华建工程设备有限公司
    const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal6];
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: testCompanyId,
      pageSize: 5,
      pageIndex: 1,
    });
    expect(detail.Paging.TotalRecords).toEqual(1);

    const testCompanyId2 = 'g21e3a2acdad6fa627e6579ec20a7e51'; //中央人民广播电台驻新疆办事处
    const detail2 = await companyService.getDimensionDetail(dimension, {
      keyNo: testCompanyId2,
      pageSize: 5,
      pageIndex: 1,
    });
    expect(detail2.Paging.TotalRecords).toEqual(1);
  });

  it('新成立企业 DimensionLevel2Enums.EstablishedTime', async () => {
    const testCompanyId = 'e5f31183c5c9204623ac74a8445b434e'; //郯城上电新能源有限公司
    const dimension = BaseDimensions[DimensionLevel2Enums.EstablishedTime];
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: testCompanyId,
      pageSize: 5,
      pageIndex: 1,
    });
    expect(detail.Paging.TotalRecords).toEqual(1);
  });

  it('经营状态非存续 DimensionLevel3Enums.BusinessAbnormal1', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal1];
    // 存续：企查查 f625a5b661058ba5082ca508f99ffe1b
    let keyNo = 'f625a5b661058ba5082ca508f99ffe1b';
    const res6 = await companyService.getDimensionDetail(dimension, { keyNo, pageSize: 10, pageIndex: 1 });
    expect(res6.Paging.TotalRecords).toEqual(0);
    // 清算：广州市穗城房地产开发有限公司 92baadfb4287ffab43731f0c159b038f
    keyNo = '92baadfb4287ffab43731f0c159b038f';
    const res1 = await companyService.getDimensionDetail(dimension, { keyNo, pageSize: 10, pageIndex: 1 });
    expect(res1.Paging.TotalRecords).toEqual(1);
    // 吊销：天津汇通精锐商务信息咨询服务有限公司 0c28df4f8d31f258cb4e53cb453eaf6a
    keyNo = '0c28df4f8d31f258cb4e53cb453eaf6a';
    const res2 = await companyService.getDimensionDetail(dimension, { keyNo, pageSize: 10, pageIndex: 1 });
    expect(res2.Paging.TotalRecords).toEqual(1);
    // 注销：江苏华润集团公司 27b17c28755ee660755269dad5ac540d
    keyNo = '27b17c28755ee660755269dad5ac540d';
    const res3 = await companyService.getDimensionDetail(dimension, { keyNo, pageSize: 10, pageIndex: 1 });
    expect(res3.Paging.TotalRecords).toEqual(1);
    // 撤销：北方汇银控股集团有限公司 c4bdb3afdbf89675143831b661d7e55d
    keyNo = 'c4bdb3afdbf89675143831b661d7e55d';
    const res4 = await companyService.getDimensionDetail(dimension, { keyNo, pageSize: 10, pageIndex: 1 });
    expect(res4.Paging.TotalRecords).toEqual(1);
    // 停业：中卫安（北京）认证中心广东分中心 bff14c1bab25c555bc852b84f9b95ed1
    keyNo = 'bff14c1bab25c555bc852b84f9b95ed1';
    const res5 = await companyService.getDimensionDetail(dimension, { keyNo, pageSize: 10, pageIndex: 1 });
    expect(res5.Paging.TotalRecords).toEqual(1);
    //除名：深圳爱淘城网络科技股份有限公司 7b9ef20968f1679b81e19d8afdd39f3c
    keyNo = '7b9ef20968f1679b81e19d8afdd39f3c';
    const res7 = await companyService.getDimensionDetail(dimension, { keyNo, pageSize: 10, pageIndex: 1 });
    expect(res7.Paging.TotalRecords).toEqual(1);
    // 歇业：重庆隆福佳人生物科技集团有限公司 6b193c48003ec3b70df0f0f405ef55ea
    keyNo = '6b193c48003ec3b70df0f0f405ef55ea';
    const res8 = await companyService.getDimensionDetail(dimension, { keyNo, pageSize: 10, pageIndex: 1 });
    expect(res8.Paging.TotalRecords).toEqual(1);
    //责令关闭：深圳微金所金融信息服务有限公司 1e0578ffb3b9eabca88c863461692e59
    keyNo = '1e0578ffb3b9eabca88c863461692e59';
    const res9 = await companyService.getDimensionDetail(dimension, { keyNo, pageSize: 10, pageIndex: 1 });
    expect(res9.Paging.TotalRecords).toEqual(1);
  });

  it('被列为非正常户 BusinessAbnormal4', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal4];
    const keyNo = 'faffc5a8cca566ca0ed6e9c2910df942'; // 	广州永忻贸易有限公司
    dimension.strategyModel.cycle = -1;
    const res = await companyService.getDimensionDetail(dimension, {
      keyNo,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(res.Paging.TotalRecords).toBe(1);
  });
});
