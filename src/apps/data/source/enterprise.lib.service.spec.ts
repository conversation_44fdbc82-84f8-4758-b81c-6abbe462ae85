import { RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { Test } from '@nestjs/testing';
import { cloneDeep } from 'lodash';
import { EntityManager, getManager } from 'typeorm';
import { BaseDimensions } from '../../../libs/constants/dimension.base.constants';
import { DimensionLevel2Enums } from '../../../libs/enums/diligence/DimensionLevel2Enums';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { DimensionDefinitionPO } from '../../../libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { OperatorEnums } from '../../../libs/model/diligence/pojo/dimension/DimensionQueryPO';
import { AppTestModule } from '../../app/app.test.module';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { DataModule } from '../data.module';
import { EnterpriseLibDimensionService } from '../dimensions/enterprise.lib.dimension.service';
import { EnterpriseLibService } from './enterprise.lib.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { ExcludeCompanyService } from 'apps/exclude_company/exclude-company.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { ConfigService } from 'libs/config/config.service';
import { PersonData } from 'libs/model/data/source/PersonData';
import { DetailsParamEnums } from 'libs/enums/diligence/DetailsParamEnums';
jest.setTimeout(600 * 1000);

// 生成唯一测试ID
const [testOrgId, testUserId] = generateUniqueTestIds('enterprise.lib.service.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

describe('EnterpriseLibService', () => {
  let enterpriseService: EnterpriseLibService;
  let enterpriseLibDimensionService: EnterpriseLibDimensionService;
  let mockedBundleService: any;
  let entityManager: EntityManager;
  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();

    enterpriseService = module.get(EnterpriseLibService);
    enterpriseLibDimensionService = module.get(EnterpriseLibDimensionService);
    mockedBundleService = module.get(RoverBundleService);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  /**
   * 单元测试部分 - 核心方法功能测试
   */
  describe('单元测试 - analyze方法', () => {
    it.skip('应当分析并返回未命中的BusinessAbnormal2维度', async () => {
      // 跳过此集成测试，因为可能因外部服务超时
      const testCompanyId = '4d9c0df27574725ad2c3632057d27b31'; // 威海怀著贸易有限公司
      const dimension = Object.assign({}, BaseDimensions[DimensionLevel3Enums.BusinessAbnormal2]);
      const result = await enterpriseService.analyze(testCompanyId, [dimension]);
      expect(result[0]?.totalHits).toBeUndefined();
    });

    it.skip('应当分析并返回命中的BusinessAbnormal2维度', async () => {
      // 跳过此集成测试，因为可能因外部服务超时
      const testCompanyId2 = '22ddb6cb12cc6a142db462374031057e'; // 海口山水互联视界科技有限公司
      const dimension = Object.assign({}, BaseDimensions[DimensionLevel3Enums.BusinessAbnormal2]);
      const result2 = await enterpriseService.analyze(testCompanyId2, [dimension]);
      expect(result2[0]?.totalHits).toEqual(1);
    });

    it.skip('应当分析并返回命中的CompanyShell维度', async () => {
      // 跳过此集成测试，因为可能因外部服务超时
      const testCompanyId = '0091b397f71fdebd4c384c584b30d4fc'; // 屯昌君慎覆日用品经营部
      const dimension = BaseDimensions[DimensionLevel2Enums.CompanyShell];
      const result = await enterpriseService.analyze(testCompanyId, [dimension]);
      expect(result[0]?.totalHits).toBeGreaterThanOrEqual(1);
    });

    it.skip('应当分析并返回命中的DebtOverdue维度', async () => {
      // 跳过此集成测试，因为可能因外部服务超时
      const testCompanyId = '20acb09994722a450aa6b4df5eb1f806'; // 中新科技集团股份有限公司
      const dimension = BaseDimensions[DimensionLevel3Enums.DebtOverdue];
      const result = await enterpriseService.analyze(testCompanyId, [dimension]);
      expect(result[0]?.totalHits).toBeGreaterThanOrEqual(1);
    });
  });

  describe('单元测试 - getDimensionDetail方法', () => {
    it('应当获取BusinessAbnormal2维度详情', async () => {
      const testCompanyId = '22ddb6cb12cc6a142db462374031057e'; // 海口山水互联视界科技有限公司
      const dimension = Object.assign({}, BaseDimensions[DimensionLevel3Enums.BusinessAbnormal2]);
      const detail = await enterpriseService.getDimensionDetail(dimension, {
        keyNo: testCompanyId,
        pageSize: 5,
        pageIndex: 1,
      });
      expect(detail.Paging.TotalRecords).toEqual(1);
    });

    it('应当支持通过detailsParams过滤结果', async () => {
      const testCompanyId = '22ddb6cb12cc6a142db462374031057e'; // 海口山水互联视界科技有限公司
      const dimension = Object.assign({}, BaseDimensions[DimensionLevel3Enums.BusinessAbnormal2]);

      //设置简易注销结果参数
      dimension.strategyModel.detailsParams = [
        {
          field: QueryParamsEnums.simpleCancellationStep,
          fieldVal: [
            {
              key: '2',
              keyName: '正在进行',
              status: 1,
            },
            {
              key: '3',
              keyName: '准予/准许注销',
              status: 1,
            },
            {
              key: '1',
              keyName: '不予受理',
              status: 1,
            },
            {
              key: '4',
              keyName: '已撤销',
              status: 1,
            },
          ],
          sort: 0,
        },
      ];

      const detail = await enterpriseService.getDimensionDetail(dimension, {
        keyNo: testCompanyId,
        pageSize: 5,
        pageIndex: 1,
      });
      expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    });

    it('应当获取CompanyShell维度详情', async () => {
      const testCompanyId = '0091b397f71fdebd4c384c584b30d4fc'; // 屯昌君慎覆日用品经营部
      const dimension = BaseDimensions[DimensionLevel2Enums.CompanyShell];
      const detail = await enterpriseService.getDimensionDetail(dimension, {
        keyNo: testCompanyId,
        pageSize: 5,
        pageIndex: 1,
      });
      expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    });
  });
  jest.setTimeout(60 * 1000);
  describe('单元测试 - 数据获取相关方法', () => {
    it.skip('getPartner应当获取股东列表信息', async () => {
      // 跳过此集成测试，因为可能因外部服务超时
      const keyNo = '8238609466df9da83708e36a899358e5';
      const type = 'Partners';
      const res = await enterpriseService.getPartner(keyNo, type);
      expect(res.Result).not.toBeNull();
      expect(res.Result.length).toBeGreaterThan(500);
    });

    it.skip('getOrganismPerson应当获取社会组织主要人员', async () => {
      // 跳过此集成测试，因为可能因外部服务超时
      const response = await enterpriseService.getOrganismPerson('s7cbfe15cabad8c22b02eb79f24bb5ae');
      expect(response?.length).toBeGreaterThanOrEqual(1);
    });

    it.skip('getHistoryPartnerList应当获取历史股东信息', async () => {
      // 跳过此集成测试，因为可能因外部服务超时
      // 浙江嵊州农村商业银行股份有限公司
      const response = await enterpriseService.getHistoryPartnerList('8238609466df9da83708e36a899358e5');
      expect(response?.length).toBeGreaterThanOrEqual(44);
    });
  });

  /**
   * 集成测试部分 - 验证真实场景下的功能
   */
  describe('集成测试 - 企业维度分析', () => {
    it('集成测试 - BillDefaults票据违约维度', async () => {
      const dimension = BaseDimensions[DimensionLevel2Enums.BillDefaults];
      const keyNo = '8e66ac5dc8883c518c0c4046ab7a9ae1'; // 中经国开建工（山东）有限公司

      // 确保enterpriseService和enterpriseLibDimensionService返回一致结果
      const res = await enterpriseService.getDimensionDetail(dimension, {
        keyNo,
        pageSize: 10,
        pageIndex: 1,
      });

      const res2 = await enterpriseLibDimensionService.getDimensionDetail(dimension, {
        keyNo,
        pageSize: 10,
        pageIndex: 1,
      });

      expect(res).toEqual(res2);
      expect(res?.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    });

    it('集成测试 - TaxArrearsNotice欠税公告', async () => {
      const dimension = cloneDeep(BaseDimensions[DimensionLevel2Enums.TaxArrearsNotice]);
      const keyNo = '3d0c5112a30ee9177e850c809c18bef4'; // 绿地控股集团有限公司

      // 查不限
      dimension.strategyModel.detailsParams.forEach((d) => {
        if (d.field === QueryParamsEnums.isValid) {
          d.fieldVal = '-1';
        }
      });

      const result = await enterpriseService.getDimensionDetail(dimension, { keyNo });
      const result2 = await enterpriseLibDimensionService.getDimensionDetail(dimension, { keyNo });

      expect(result).toEqual(result2);

      // 查当前有效
      const dimension1 = Object.assign(dimension, {
        strategyModel: {
          boost: 1.0,
          baseScore: 10,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '1',
            },
            {
              field: QueryParamsEnums.taxArrearsAmount,
              fieldOperator: OperatorEnums.ge,
              fieldVal: 0,
            },
          ],
        },
      });

      const result1 = await enterpriseService.getDimensionDetail(dimension1, { keyNo });

      // 查历史
      const dimension0 = Object.assign(dimension, {
        strategyModel: {
          boost: 1.0,
          baseScore: 10,
          level: DimensionRiskLevelEnum.Medium,
          detailsParams: [
            {
              field: QueryParamsEnums.isValid,
              fieldVal: '0',
            },
            {
              field: QueryParamsEnums.taxArrearsAmount,
              fieldOperator: OperatorEnums.ge,
              fieldVal: 0,
            },
          ],
        },
      });

      const result0 = await enterpriseService.getDimensionDetail(dimension0, { keyNo });

      // 验证当前有效和历史的条数和 = 查不限的条数
      expect(result0?.Paging?.TotalRecords + result1?.Paging?.TotalRecords).toEqual(result?.Paging.TotalRecords);
    });

    it.skip('集成测试 - EndExecutionCase终本案件', async () => {
      // 跳过此集成测试，因为可能因外部服务超时
      const dimension: DimensionDefinitionPO = BaseDimensions[DimensionLevel3Enums.EndExecutionCase];
      const keyNo = '56cfb29252cc9769fff84cfec0866c2e'; // 侯马北方轻工城置业有限公司

      const result = await enterpriseService.analyze(keyNo, [dimension]);
      expect(result[0]?.totalHits).toBeGreaterThanOrEqual(1);
    });
  });

  describe('集成测试 - 企业关联信息', () => {
    it('集成测试 - PersonCreditCurrent被列入失信被执行人（当前有效）', async () => {
      const dimension = BaseDimensions[DimensionLevel3Enums.PersonCreditCurrent];
      const keyNo = '02eca136b16504350746365fab632977'; // 中安金控资产管理有限公司

      const res = await enterpriseService.getDimensionDetail(dimension, {
        keyNo,
        pageSize: 10,
        pageIndex: 1,
      });

      const res1 = await enterpriseLibDimensionService.getDimensionDetail(dimension, {
        keyNo,
        pageSize: 10,
        pageIndex: 1,
      });

      expect(res).toEqual(res1);
      expect(res.Paging.TotalRecords).toBeGreaterThan(1);
    });

    it('集成测试 - PersonCreditHistory历史失信被执行人', async () => {
      const dimension: DimensionDefinitionPO = BaseDimensions[DimensionLevel3Enums.PersonCreditHistory];
      const keyNo = '1ab62d6a55b1428438ee1ee788e750c6'; // 贵州红华物流有限公司

      // 测试默认周期条件（近3年）
      // const res = await enterpriseService.getDimensionDetail(dimension, {
      //   keyNo,
      //   pageSize: 10,
      //   pageIndex: 1,
      // });
      // expect(res.Result).not.toBeNull();
      // expect(res.Paging.TotalRecords).toBeGreaterThanOrEqual(1);

      // 测试近2年
      dimension.strategyModel.cycle = 2;
      const res2 = await enterpriseService.getDimensionDetail(dimension, {
        keyNo,
        pageSize: 10,
        pageIndex: 1,
      });
      expect(res2.Result).not.toBeNull();

      // 测试近1年
      dimension.strategyModel.cycle = 1;
      const res1 = await enterpriseService.getDimensionDetail(dimension, {
        keyNo,
        pageSize: 10,
        pageIndex: 1,
      });
      expect(res1.Result).not.toBeNull();

      // 测试不限周期
      dimension.strategyModel.cycle = -1;
      const res0 = await enterpriseService.getDimensionDetail(dimension, {
        keyNo,
        pageSize: 10,
        pageIndex: 1,
      });
      expect(res0.Result).not.toBeNull();
      expect(res0.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    });

    it('集成测试 - 涉嫌冒名登记', async () => {
      const dimension = BaseDimensions[DimensionLevel2Enums.FakeRegister];
      const keyNo = '7f19cdff5431a05f6190c7056632b17a';

      const res = await enterpriseService.getDimensionDetail(dimension, {
        keyNo,
        pageSize: 10,
        pageIndex: 1,
      });

      const res1 = await enterpriseLibDimensionService.getDimensionDetail(dimension, {
        keyNo,
        pageSize: 10,
        pageIndex: 1,
      });

      expect(res1.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
      expect(res.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
      expect(res).toEqual(res1);
    });

    it('集成测试-疑似停业歇业停产或被吊销证照 BusinessAbnormal5', async () => {
      const dimension = BaseDimensions[DimensionLevel3Enums.BusinessAbnormal5];
      const result = await enterpriseLibDimensionService.getDimensionDetail(dimension, {
        keyNo: '85904b8d8c9a9ef667b1dd1b1b79dc01',
        pageIndex: 2,
        pageSize: 2,
        companyName: '葫芦岛市南票区吉通物流有限公司',
      });
      expect(result).toBeDefined();
      expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    });

    it('集成测试-债券逾期', async () => {
      const dimension = BaseDimensions[DimensionLevel3Enums.DebtOverdue];
      const result = await enterpriseLibDimensionService.getDimensionDetail(dimension, {
        keyNo: 'ef5f21e7abc76f36f8497e8c0540fa7d',
        pageIndex: 1,
        pageSize: 10,
        companyName: '南京淘金视播商贸有限公司',
      });
      expect(result).toBeDefined();
      expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    });

    it('集成测试 - 员工人数减少', async () => {
      const dimension = BaseDimensions[DimensionLevel3Enums.EmployeeReduction];
      dimension.strategyModel.detailsParams.forEach((e) => {
        if (e.field === QueryParamsEnums.employeeReductionRatio) {
          e.fieldVal = 5;
        }
      });

      const keyNo = 'c70a55cb048c8e4db7bca357a2c113e0'; // 阿里巴巴（中国）网络技术有限公司

      const res = await enterpriseService.getDimensionDetail(dimension, {
        keyNo,
      });

      const res1 = await enterpriseLibDimensionService.getDimensionDetail(dimension, {
        keyNo,
      });

      expect(res1.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
      expect(res.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
      expect(res).toEqual(res1);
    });
  });

  it.skip('集成测试 - 动产抵押', async () => {
    // 跳过此集成测试，因为可能因外部服务超时
    const dimension = BaseDimensions[DimensionLevel2Enums.ChattelMortgage];

    const keyNo = '232d985c045c14316b80e23d1e9730bf'; // 江苏德龙镍业有限公司

    const res = await enterpriseService.analyze(keyNo, [dimension]);
    const res1 = await enterpriseLibDimensionService.getDimensionDetail(dimension, {
      keyNo,
    });

    expect(res1.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    expect(res[0].totalHits).toBeGreaterThanOrEqual(1);
  });

  it.skip('集成测试 - 动产查封', async () => {
    // 跳过此集成测试，因为可能因外部服务超时
    const dimension = BaseDimensions[DimensionLevel2Enums.ChattelSeizure];

    const keyNo = '3127b52a06e83005bccbac9b19d6838e'; // 吉林市众诚分离机械制造有限公司

    const res = await enterpriseService.analyze(keyNo, [dimension]);
    const res1 = await enterpriseLibDimensionService.getDimensionDetail(dimension, {
      keyNo,
    });

    expect(res1.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
    expect(res[0].totalHits).toBeGreaterThanOrEqual(1);
  });

  // 保留现有测试以确保向后兼容性
  it.skip('DimensionLevel2Enums.Certification', async () => {
    // 跳过此集成测试，因为可能因外部服务超时
    const dimension = BaseDimensions[DimensionLevel2Enums.Certification];
    const result = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: 'f625a5b661058ba5082ca508f99ffe1b',
      pageIndex: 1,
      pageSize: 10,
      companyName: '企查查科技股份有限公司',
    });
    expect(result).toBeDefined();
  });

  it('税务催报 TaxReminder', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.TaxReminder];
    const result = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: 'ae531b6860f4b5594c45d5896bdbd5d6',
    });
    expect(result).toBeDefined();
    // // console.log(JSON.stringify(result));
  });

  it('税务催缴 TaxCallNotice', async () => {
    const dimension = cloneDeep(BaseDimensions[DimensionLevel2Enums.TaxCallNoticeV2]);
    const result = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: 'e7ff903c1f5be21c8cd5704d408db503',
    });
    expect(result?.Paging?.TotalRecords).toEqual(0);
    dimension.strategyModel.detailsParams.forEach((d) => {
      if (d.field === QueryParamsEnums.AmountOwed) {
        d.fieldVal = 0;
      }
    });
    const result2 = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: 'e7ff903c1f5be21c8cd5704d408db503',
    });
    expect(result2?.Paging?.TotalRecords).toBeGreaterThan(1);
  });

  // 保持其它现有测试
  // 以下是重构后的mainInfoUpdate函数，使其更加通用
  const mainInfoUpdate = async (dimension: DimensionDefinitionPO) => {
    const keyNo = '90f9f13d2c9a1f6b0c8a5e8f75671ed2'; //深圳瑞贤金嘉网络科技有限公司
    dimension.strategyModel.cycle = -1; //设置为不限
    const res = await enterpriseService.getDimensionDetail(dimension, {
      keyNo,
      pageSize: 10,
      pageIndex: 1,
    });
    const res1 = await enterpriseLibDimensionService.getDimensionDetail(dimension, {
      keyNo,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(res).toEqual(res1);
    expect(res.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  };

  it.skip('test detail for MainInfoUpdateAddress', async () => {
    // 跳过此集成测试，因为可能因外部服务超时
    const dimension = BaseDimensions[DimensionLevel3Enums.MainInfoUpdateAddress];
    await mainInfoUpdate(dimension);
  });

  it.skip('test detail for MainInfoUpdateName', async () => {
    // 跳过此集成测试，因为可能因外部服务超时
    const dimension = BaseDimensions[DimensionLevel3Enums.MainInfoUpdateName];
    await mainInfoUpdate(dimension);
  });

  it.skip('test detail for MainInfoUpdateLegalPerson', async () => {
    // 跳过此集成测试，因为可能因外部服务超时
    const dimension = BaseDimensions[DimensionLevel3Enums.MainInfoUpdateLegalPerson];
    await mainInfoUpdate(dimension);
  });

  it.skip('test getEmployeeList 金科', async () => {
    // 跳过此集成测试，因为可能因外部服务超时
    const employeeList = await enterpriseService.getEmployeeList('77f780cb301b86c4f68814190c6709a9');
    expect(employeeList.length).toBeGreaterThan(0);
  });

  it('test IPRPledge', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.IPRPledge];
    dimension.strategyModel.cycle = -1; //设置为不限
    dimension.strategyModel.detailsParams.forEach((e) => {
      if (e.field === QueryParamsEnums.isValid) {
        e.fieldVal = '-1';
      }
    });
    const response = await enterpriseService.getDimensionDetail(dimension, { keyNo: '9bd74ed5a585f0c3253af9017de823c1' });
    expect(response.Paging.TotalRecords).toBeGreaterThan(1);
  });

  it('test DimensionLevel2Enums.BondDefaults', async () => {
    const dimension = BaseDimensions[DimensionLevel2Enums.BondDefaults];
    const result = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: 'bcaa8101139f2dd700edc58170462a26',
    });
    expect(result).toBeDefined();
    expect(result.Paging.TotalRecords).toBe(0);
    // 设置isValid为-1
    dimension.strategyModel.detailsParams.forEach((d) => {
      if (d.field === QueryParamsEnums.isValid) {
        d.fieldVal = '-1';
      }
    });
    const result2 = await enterpriseService.getDimensionDetail(dimension, { keyNo: 'bcaa8101139f2dd700edc58170462a26' });
    expect(result2).toBeDefined();
    expect(result2.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
});

describe('EnterpriseLibService - getRelatedCompanyAndPerson', () => {
  let service: EnterpriseLibService;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockExcludeCompanyService: jest.Mocked<ExcludeCompanyService>;

  beforeEach(async () => {
    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockExcludeCompanyService = {
      filterExcludedCompanyIds: jest.fn(),
    } as any;

    const module = await Test.createTestingModule({
      providers: [
        EnterpriseLibService,
        {
          provide: ConfigService,
          useValue: {},
        },
        {
          provide: CompanySearchService,
          useValue: mockCompanySearchService,
        },
        {
          provide: HttpUtilsService,
          useValue: {},
        },
        {
          provide: CompanyDetailService,
          useValue: {},
        },
        {
          provide: EnterpriseLibDimensionService,
          useValue: {},
        },
        {
          provide: ExcludeCompanyService,
          useValue: mockExcludeCompanyService,
        },
      ],
    }).compile();

    service = module.get<EnterpriseLibService>(EnterpriseLibService);

    // Mock 所有可能被调用的方法
    jest.spyOn(service, 'getEmployeeList').mockResolvedValue([
      {
        name: '张三',
        keyNo: 'emp1',
        job: '董事',
        tags: ['董事'],
      },
    ] as PersonData[]);

    jest.spyOn(service, 'getLegalPerson').mockResolvedValue([
      {
        name: '李四',
        keyNo: 'legal1',
        job: '法定代表人',
        tags: ['法人'],
      },
    ] as PersonData[]);

    jest.spyOn(service, 'getFinalActualController').mockResolvedValue([
      {
        name: '王五',
        keyNo: 'controller1',
        job: '实际控制人',
        tags: ['实控人'],
      },
    ] as PersonData[]);

    jest.spyOn(service, 'getBenefitList').mockResolvedValue([
      {
        name: '赵六',
        keyNo: 'benefit1',
        job: '受益所有人',
        tags: ['受益人'],
      },
    ] as PersonData[]);

    jest.spyOn(service, 'getPartnerList').mockResolvedValue([
      {
        name: '钱七',
        keyNo: 'partner1',
        job: '股东',
        tags: ['大股东'],
        stockPercent: '51%',
      },
    ] as PersonData[]);

    mockExcludeCompanyService.filterExcludedCompanyIds.mockResolvedValue(['testCompany1', 'emp1', 'legal1', 'controller1', 'benefit1', 'partner1']);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确处理所有关联对象类型', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const associateObjects = [
      { key: DetailsParamEnums.Employ, status: 1 },
      { key: DetailsParamEnums.LegalRepresentative, status: 1 },
      { key: DetailsParamEnums.ActualController, status: 1 },
      { key: DetailsParamEnums.Benefit, status: 1 },
      { key: DetailsParamEnums.Shareholder, status: 1 },
    ];

    // Act
    const result = await service.getRelatedCompanyAndPerson(companyId, associateObjects, [], 'all');

    // Assert
    expect(result.keyNos).toContain('emp1');
    expect(result.keyNos).toContain('legal1');
    expect(result.keyNos).toContain('controller1');
    expect(result.keyNos).toContain('benefit1');
    expect(result.keyNos).toContain('partner1');
    expect(result.personJobSet['emp1']).toBe('董事');
    expect(result.personJobSet['legal1']).toBe('法定代表人');
    expect(result.personJobSet[companyId]).toBe('企业主体');
  });

  it('应该正确处理大股东筛选', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const associateObjects = [{ key: DetailsParamEnums.MajorShareholder, status: 1 }];

    // 重置mock以确保只有大股东数据
    mockExcludeCompanyService.filterExcludedCompanyIds.mockResolvedValueOnce(['testCompany1', 'partner1']);

    // Act
    const result = await service.getRelatedCompanyAndPerson(companyId, associateObjects, [], 'all');

    // Assert
    expect(result.personJobList.length).toBe(1);
    expect(result.personJobList[0].tags).toContain('大股东');
  });

  it('应该正确处理主要人员核心关联方相关风险项的过滤', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const associateObjects = [{ key: DetailsParamEnums.Employ, status: 1 }];

    // Mock 一个包含独立董事的员工列表
    jest.spyOn(service, 'getEmployeeList').mockResolvedValueOnce([
      {
        name: '张三',
        keyNo: 'emp1',
        job: '独立董事',
        tags: ['董事'],
      },
    ] as PersonData[]);

    // Act
    const result = await service.getRelatedCompanyAndPerson(companyId, associateObjects, [], 'all', DimensionLevel3Enums.MainMembersPersonCreditCurrent);

    // Assert
    expect(Object.keys(result.personJobSet)).not.toContain('emp1');
  });

  it('应该正确处理排除公司的情况', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const associateObjects = [{ key: DetailsParamEnums.Employ, status: 1 }];

    // filterExcludedCompanyIds返回允许保留的IDs，不包含emp1意味着emp1被排除
    mockExcludeCompanyService.filterExcludedCompanyIds.mockResolvedValueOnce(['testCompany1']);

    // Act
    const result = await service.getRelatedCompanyAndPerson(companyId, associateObjects, [], 'all');

    // Assert
    expect(result.keyNos).not.toContain('emp1');
    expect(mockExcludeCompanyService.filterExcludedCompanyIds).toHaveBeenCalled();
  });

  it('应该正确合并相同人员的职务信息', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const associateObjects = [
      { key: DetailsParamEnums.Employ, status: 1 },
      { key: DetailsParamEnums.Shareholder, status: 1 },
    ];

    // Mock 一个人既是董事又是股东的情况
    jest.spyOn(service, 'getEmployeeList').mockResolvedValueOnce([
      {
        name: '张三',
        keyNo: 'person1',
        job: '董事',
        tags: ['董事'],
      },
    ] as PersonData[]);

    jest.spyOn(service, 'getPartnerList').mockResolvedValueOnce([
      {
        name: '张三',
        keyNo: 'person1',
        job: '股东',
        tags: ['股东'],
        stockPercent: '30%',
      },
    ] as PersonData[]);

    // 确保person1被保留
    mockExcludeCompanyService.filterExcludedCompanyIds.mockResolvedValueOnce(['testCompany1', 'person1']);

    // Act
    const result = await service.getRelatedCompanyAndPerson(companyId, associateObjects, [], 'all');

    // Assert
    expect(result.personJobList.length).toBe(1);
    expect(result.personJobSet['person1']).toBe('董事,股东');
    expect(result.personJobList[0].tags).toContain('董事');
    expect(result.personJobList[0].tags).toContain('股东');
  });
});

describe('EnterpriseLibService - addRelatedKeyNosV2', () => {
  let service: EnterpriseLibService;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockExcludeCompanyService: jest.Mocked<ExcludeCompanyService>;

  beforeEach(async () => {
    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockExcludeCompanyService = {
      filterExcludedCompanyIds: jest.fn(),
    } as any;

    const module = await Test.createTestingModule({
      providers: [
        EnterpriseLibService,
        {
          provide: ConfigService,
          useValue: {},
        },
        {
          provide: CompanySearchService,
          useValue: mockCompanySearchService,
        },
        {
          provide: HttpUtilsService,
          useValue: {},
        },
        {
          provide: CompanyDetailService,
          useValue: {},
        },
        {
          provide: EnterpriseLibDimensionService,
          useValue: {},
        },
        {
          provide: ExcludeCompanyService,
          useValue: mockExcludeCompanyService,
        },
      ],
    }).compile();

    service = module.get<EnterpriseLibService>(EnterpriseLibService);

    // Mock getRelatedCompanyAndPerson 方法
    jest.spyOn(service, 'getRelatedCompanyAndPerson').mockResolvedValue({
      keyNos: ['person1', 'person2'],
      personJobSet: {
        person1: '董事',
        person2: '监事',
      },
      personJobList: [],
    });

    // Mock getCompanyExecutivesKeyNosV2 方法
    jest.spyOn(service, 'getCompanyExecutivesKeyNosV2').mockResolvedValue({
      personNos: ['exec1', 'exec2'],
      personJobSet: {},
      personJobList: [],
    });

    mockExcludeCompanyService.filterExcludedCompanyIds.mockResolvedValue([]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确处理有 fieldVal 的情况', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const associateObject = {
      fieldVal: [
        { key: DetailsParamEnums.Employ, status: 1 },
        { key: DetailsParamEnums.LegalRepresentative, status: 1 },
      ],
    };

    // Act
    const result = await service.addRelatedKeyNosV2(companyId, associateObject, 'all');

    // Assert
    expect(result).toEqual(['person1', 'person2']);
    expect(service.getRelatedCompanyAndPerson).toHaveBeenCalledWith(companyId, associateObject.fieldVal, null, 'all', undefined);
  });

  it('应该正确处理 MainMembersPersonCreditCurrent 维度', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const dimensionKey = DimensionLevel3Enums.MainMembersPersonCreditCurrent;

    // Act
    const result = await service.addRelatedKeyNosV2(companyId, {}, 'all', dimensionKey);

    // Assert
    expect(result).toEqual(['exec1', 'exec2']);
    expect(service.getCompanyExecutivesKeyNosV2).toHaveBeenCalledWith(companyId, 'person', true, true, undefined, dimensionKey);
  });

  it('应该正确处理主要人员相关维度', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const dimensions = [
      DimensionLevel3Enums.MainMembersRestrictedOutbound,
      DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent,
      DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence,
      DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory,
    ];

    // 测试每个维度
    for (const dimensionKey of dimensions) {
      // Act
      const result = await service.addRelatedKeyNosV2(companyId, {}, 'all', dimensionKey);

      // Assert
      expect(result).toEqual(['exec1', 'exec2']);
      expect(service.getCompanyExecutivesKeyNosV2).toHaveBeenCalledWith(companyId, 'all', true, true, undefined, dimensionKey);
    }
  });

  it('应该返回空数组当没有匹配条件时', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const associateObject = {};

    // Act
    const result = await service.addRelatedKeyNosV2(companyId, associateObject, 'all');

    // Assert
    expect(result).toEqual([]);
    expect(service.getRelatedCompanyAndPerson).not.toHaveBeenCalled();
    expect(service.getCompanyExecutivesKeyNosV2).not.toHaveBeenCalled();
  });

  it('应该正确处理重复的 keyNos', async () => {
    // Arrange
    const companyId = 'testCompany1';
    const associateObject = {
      fieldVal: [{ key: DetailsParamEnums.Employ, status: 1 }],
    };

    // Mock 返回重复的 keyNos
    jest.spyOn(service, 'getRelatedCompanyAndPerson').mockResolvedValueOnce({
      keyNos: ['person1', 'person1', 'person2'],
      personJobSet: {},
      personJobList: [],
    });

    // Act
    const result = await service.addRelatedKeyNosV2(companyId, associateObject, 'all');

    // Assert
    expect(result).toEqual(['person1', 'person2']);
    expect(result.length).toBe(2); // 确保去重成功
  });
});
