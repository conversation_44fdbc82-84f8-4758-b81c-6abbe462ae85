/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { ConfigService } from 'libs/config/config.service';
import { DimensionDefinitionPO, SubDimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/pojo/req&res/details/response';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { EnterpriseLibService } from './enterprise.lib.service';
import { Client } from '@elastic/elasticsearch';
import { getStartTimeByCycle } from '../../../libs/utils/utils';
import { AbstractEsAnalyzeService } from './analyze.base.service';
import { CaseReasonCode, UnfairCompetitionCaseReasonCode } from '../../../libs/constants/case.constants';
import * as moment from 'moment';
import { HitDetailsCreditParam } from '../../../libs/model/diligence/pojo/req&res/details/request';
import { LawsuitResultNewMap } from '../../../libs/constants/common';
import { QueryParamsEnums } from '../../../libs/model/diligence/pojo/dimension/dimension.filter.params';
import { HitDetailsBidBaseQueryParams } from '../../../libs/model/diligence/pojo/req&res/details/request/HitDetailsBidBaseQueryParams';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from '../../../libs/exceptions/exceptionConstants';
import { intersection } from 'lodash';
import { CompanyDetailService } from '../../company/company-detail.service';

/**
 * 司法案件数据源接口
 */
@Injectable()
export class CaseService extends AbstractEsAnalyzeService {
  constructor(
    private readonly configService: ConfigService,
    private readonly entLibService: EnterpriseLibService,
    private readonly companyDetailService: CompanyDetailService,
  ) {
    super(
      CaseService.name,
      new Client({
        nodes: configService.esConfig.case.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.case.indexName,
    );
  }

  async getDimensionDetail(dimension: DimensionDefinitionPO, params: HitDetailsCreditParam): Promise<HitDetailsBaseResponse> {
    const { keyNo } = params;
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params);
    if (resp?.Result?.length) {
      switch (dimension?.key) {
        case DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence:
        case DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory: {
          const caseReasonTypes = resp.Result.map((d) => d.CaseReasonCode);
          const caseReasonDescriptions = await this.companyDetailService.getCaseReasonDescription(caseReasonTypes);
          const jobSet = await this.entLibService.getRelatedCompanyAndPersonJobSetV2(resp, dimension, keyNo, 'all');
          resp.Result.forEach((r) => {
            const AmtInfo = JSON.parse(r?.AmtInfo);
            const CaseRole = JSON.parse(r?.CaseRoleSearch);
            r['mainKeyNo'] = keyNo;
            r['RoleAmt'] = CaseRole?.map((c) => {
              c['AmtInfo'] = AmtInfo[c?.N];
              c['Job'] = jobSet[c?.N];
              if (c['Job']) {
                c['description'] = `${c.R}-${c.P}【${c.Job}】`;
              }
              return c;
            })?.filter(Boolean);
            r['caseReasonDescription'] = caseReasonDescriptions[r.CaseReasonCode];
          });
          break;
        }
        case DimensionLevel3Enums.LaborContractDispute:
        case DimensionLevel3Enums.UnfairCompetition: {
          const caseReasonTypes = resp.Result.map((d) => d.CaseReasonCode);
          const caseReasonDescriptions = await this.companyDetailService.getCaseReasonDescription(caseReasonTypes);
          resp.Result.forEach((r) => {
            r['Amt'] = r?.AmtInfo ? JSON.parse(r?.AmtInfo)?.[keyNo]?.Amt || '-' : '-';
            r['CaseRoleIdentity'] = r?.CaseRole
              ? JSON.parse(r?.CaseRole)
                  ?.find((f) => f.N === keyNo)
                  ?.RL?.map((rl) => `${rl.T}${rl.R}${rl.LR ? `[${LawsuitResultNewMap[rl.LR]}]` : ''}`)
                  ?.join('\n')
              : '-';
            r['LatestDateTrialRound'] = moment(r?.LastestDate * 1000).format('YYYY-MM-DD') + '\n' + r?.LatestTrialRound;
            r['caseReasonDescription'] = caseReasonDescriptions[r.CaseReasonCode];
          });
          break;
        }
        default:
          break;
      }
    }
    return resp;
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionDefinitionPO) {
    const ids = [companyId];
    const CaseReasonListObscure = ['资产保全', '交通肇事', '其他刑事'];
    const CaseReasonList = [
      '危险驾驶罪',
      '故意伤害罪',
      '故意杀人罪',
      '遗弃罪',
      '职务侵占',
      '诽谤罪',
      '盗窃罪',
      '过失致人死亡罪',
      '侵占罪',
      '强奸罪',
      '抢劫罪',
      '滥用职权罪',
      '侮辱罪',
      '聚众斗殴罪',
      '过失致人重伤罪',
      '诈骗罪',
      '妨害传染病防治罪',
      '聚众扰乱社会秩序罪',
      // '交通肇事',
      // '交通肇事罪',
      // '其他刑事',
      // '其他刑事罪',
    ];

    const needPersonKeyTypes: DimensionTypeEnums[] = [
      DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence,
      DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory,
    ];
    let personKeys: string[] = [];
    const associateObject = dimension.strategyModel?.detailsParams?.find((q) => q.field === QueryParamsEnums.associateObject);
    if (needPersonKeyTypes.includes(dimension.key)) {
      personKeys = await this.entLibService.addRelatedKeyNosV2(companyId, associateObject, 'all', dimension.key);
    }
    const subBool = {
      filter: [],
    };
    switch (dimension.key) {
      case DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory: {
        if (personKeys?.length > 0) {
          Array.prototype.push.apply(ids, personKeys);
        }
        // 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为（3年以上）
        // 默认条件 最新变更时间 小于 是3年
        const timestamp = getStartTimeByCycle(3);
        subBool.filter.push({ range: { LastestDate: { lte: Math.ceil(timestamp / 1000) } } });
        subBool.filter.push({ term: { IsValid: 1 } }, { term: { CaseTypeCode2: '2' } });
        subBool.filter.push({ terms: { 'DefendantNames.keyword': ids } });
        const obscure = CaseReasonListObscure.map((reason) => {
          return { match: { ['CaseReason.aliws']: { query: reason, fuzziness: 'AUTO' } } };
        });
        subBool['must_not'] = [
          {
            terms: { CaseReason: CaseReasonList },
          },
          ...obscure,
          // {
          //   bool: {
          //     should: CaseReasonListObscure.map((reason) => {
          //       return { match_phrase: { ['CaseReason.aliws']: { query: reason, slop: 1 } } };
          //     }),
          //     minimum_should_match: 1,
          //   },
          // },
        ];
        return { bool: subBool };
      }
      case DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence: {
        if (personKeys?.length > 0) {
          Array.prototype.push.apply(ids, personKeys);
        }
        if (dimension.strategyModel?.cycle > 0) {
          const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
          subBool.filter.push({ range: { LastestDate: { gt: Math.ceil(timestamp / 1000) } } });
        }
        // 最新变更时间 大于 是3年
        subBool.filter.push({ term: { IsValid: 1 } }, { term: { CaseTypeCode2: '2' } });
        subBool.filter.push({ terms: { 'DefendantNames.keyword': ids } });
        const obscure = CaseReasonListObscure.map((reason) => {
          return { match: { ['CaseReason.aliws']: { query: reason, fuzziness: 'AUTO' } } };
        });
        subBool['must_not'] = [
          {
            terms: { CaseReason: CaseReasonList },
          },
          ...obscure,
        ];
        return { bool: subBool };
      }
      case DimensionLevel3Enums.LaborContractDispute: {
        // 劳动合同纠纷
        if (dimension.strategyModel.cycle > 0) {
          const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
          // 最新变更时间 大于 是3年
          subBool.filter.push({ range: { LastestDate: { gt: Math.ceil(timestamp / 1000) } } });
        }
        let datastatus = [0, 1];
        const queryPo = dimension.strategyModel.detailsParams.find((p) => p.field === QueryParamsEnums.isValid);
        if (queryPo && Number(queryPo?.fieldVal) >= 0) {
          datastatus = [Number(queryPo.fieldVal)];
        }
        subBool.filter.push({ terms: { IsValid: datastatus } });
        subBool.filter.push({ terms: { 'DefendantNames.keyword': ids } }, { terms: { CaseReasonCode } });
        return { bool: subBool };
      }
      case DimensionLevel3Enums.UnfairCompetition: {
        if (dimension.strategyModel.cycle > 0) {
          const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
          // 最新变更时间 大于 是3年
          subBool.filter.push({ range: { LastestDate: { gt: Math.ceil(timestamp / 1000) } } });
        }
        let datastatus = [0, 1];
        const queryPo = dimension.strategyModel.detailsParams.find((p) => p.field === QueryParamsEnums.isValid);
        if (queryPo && Number(queryPo?.fieldVal) >= 0) {
          datastatus = [Number(queryPo.fieldVal)];
        }
        subBool.filter.push({ terms: { IsValid: datastatus } });
        subBool.filter.push({ terms: { 'DefendantNames.keyword': ids } }, { terms: { CaseReasonCode: UnfairCompetitionCaseReasonCode } });
        return { bool: subBool };
      }
    }
    return { bool: subBool };
  }

  async getDimensionDetailForBid(dimension: DimensionDefinitionPO, params: Record<string, any>): Promise<HitDetailsBaseResponse> {
    const { keyNos, pageIndex, pageSize } = params;
    try {
      if (dimension.source !== DimensionSourceEnums.Case) {
        return new HitDetailsBaseResponse();
      }

      switch (dimension?.key) {
        case DimensionLevel3Enums.BiddingCompanyRelationship2Case: {
          const { total, data } = await this.getDetailFromEsForBid(keyNos, dimension, params);
          if (data) {
            data.map((r) => {
              const AmtInfo = JSON.parse(r?.AmtInfo);
              const CaseRole = JSON.parse(r?.CaseRoleSearch);
              r['RoleAmt'] = CaseRole?.map((c) => {
                c['AmtInfo'] = AmtInfo[c?.N];
                return c;
              })?.filter(Boolean);
              return r;
            });
          }
          return Object.assign(new HitDetailsBaseResponse(), {
            Paging: {
              PageSize: pageSize,
              PageIndex: pageIndex,
              TotalRecords: total,
            },
            Result: data,
          });
        }
        default:
          break;
      }
    } catch (error) {
      this.logger.error('tender getDimensionDetail error!', error);
    }
    return new HitDetailsBaseResponse();
  }

  private async getDetailFromEsForBid(companyIds: string[], dimension: DimensionDefinitionPO, params: Record<string, any>) {
    const { pageIndex, pageSize } = params;
    const query = await this.getDimensionQueryForBid(companyIds, dimension);
    const sort = {};
    if (dimension?.strategyModel?.sortField) {
      sort[dimension?.strategyModel?.sortField.field] = dimension?.strategyModel?.sortField.order;
    }

    const response = await this.searchEs(
      {
        from: (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize,
        size: pageSize || 10,
        sort,
        query,
      },
      companyIds[0],
    );
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }

  private async getDimensionQueryForBid(companyIds: string[], dimension: DimensionDefinitionPO) {
    const ids = companyIds;
    const subBool = {
      filter: [],
      should: [],
    };
    switch (dimension.key) {
      case DimensionLevel3Enums.BiddingCompanyRelationship2Case: {
        subBool.filter.push({ term: { IsValid: 1 } });
        ids.forEach((id) => {
          subBool.filter.push({ term: { CompanyKeywords: id } });
        });
        return { bool: subBool };
      }
    }
    return { bool: subBool };
  }

  async getBidAdministrativeList(dimension: SubDimensionDefinitionPO, requestData: HitDetailsBidBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const { keyNos, keyNoAndNames } = requestData;
    this.logger.info(`get hit details for dimension: ${dimension.key}`);
    if (!requestData?.keyNos?.length) {
      throw new BadParamsException(RoverExceptions.Diligence.Detail.NeedKeyNo);
    }
    const { data } = await this.getBidAdministrativeListFromEs(keyNos, dimension);
    const result = [];
    if (data.length > 0) {
      const companyNames = keyNoAndNames.map((k) => k.companyName);
      for (const item of data) {
        const defendantNames = item.DefendantNames;
        if (item.CaseRole?.length > 0) {
          item.CaseRole = JSON.parse(item.CaseRole);
        }
        if (intersection(defendantNames, companyNames)?.length > 0 || intersection(defendantNames, keyNos)?.length > 0) {
          result.push(item);
        }
      }
    }
    return Object.assign(new HitDetailsBaseResponse(), {
      Paging: {
        PageSize: requestData.pageSize,
        PageIndex: requestData.pageIndex,
        TotalRecords: result.length || 0,
      },
      Result: result,
    });
  }

  private async getBidAdministrativeListFromEs(keyNos: string[], dimension: SubDimensionDefinitionPO) {
    const boolQuery = {
      bool: {
        should: [],
        minimum_should_match: 2,
        filter: [],
      },
    };
    boolQuery.bool.should.push({
      terms: {
        CompanyKeywords: keyNos,
      },
    });
    boolQuery.bool.should.push({
      terms: {
        CaseReasonCode: ['A030803', 'B050309'],
      },
    });
    boolQuery.bool.filter.push({
      terms: {
        IsValid: [1],
      },
    });
    if (dimension.strategyModel?.cycle > 0) {
      const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
      boolQuery.bool.filter.push({ range: { EarliestDate: { gte: Math.ceil(timestamp / 1000) } } });
    }
    const response = await this.searchEs(
      {
        query: boolQuery,
        size: 10000,
      },
      keyNos[0],
    );
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }
}
