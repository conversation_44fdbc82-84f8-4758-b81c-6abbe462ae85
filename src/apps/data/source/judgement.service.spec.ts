//对 judgement.service.ts 的测试用例
//注意：部分依赖外部ES服务的集成测试已被跳过，以避免超时问题
//如需运行完整的集成测试，请将相应的 it.skip 改为 it

import { Test } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { BaseDimensions } from '../../../libs/constants/dimension.base.constants';
import { DimensionLevel3Enums } from '../../../libs/enums/diligence/DimensionLevel3Enums';
import { AppTestModule } from '../../app/app.test.module';
import { DataModule } from '../data.module';
import { JudgementService } from './judgement.service';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { PersonData } from '../../../libs/model/data/source/PersonData';
import { EnterpriseLibService } from './enterprise.lib.service';
import { PersonHelper } from '../helper/person.helper';
import { ExcludeCompanyService } from 'apps/exclude_company/exclude-company.service';

const [testOrgId, testUserId] = generateUniqueTestIds('judgement.service.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(60 * 1000);
describe('JudgementService', () => {
  let service: JudgementService;
  let entityManager: EntityManager;
  let entLibService: EnterpriseLibService;
  let personHelper: PersonHelper;
  let excludeCompanyService: ExcludeCompanyService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();

    service = module.get<JudgementService>(JudgementService);
    entLibService = module.get<EnterpriseLibService>(EnterpriseLibService);
    personHelper = module.get<PersonHelper>(PersonHelper);
    excludeCompanyService = module.get<ExcludeCompanyService>(ExcludeCompanyService);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('test getDimensionDetail', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.SalesContractDispute];
    const keyNo = '2d4e2e326a37a7757f001a6126f27a7f'; // 	广西建工集团建筑工程总承包有限公司
    const res = await service.getDimensionDetail(dimension, {
      keyNo,
      pageSize: 10,
      pageIndex: 1,
      field: 'judgedate',
      order: 'DESC',
    });
    expect(res.Paging.TotalRecords).toBeGreaterThanOrEqual(0);
  });

  it.skip('test CompanyOrMainMembersCriminalInvolve', async () => {
    // 跳过此集成测试，因为可能因外部服务超时
    const dimension = BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve];
    const keyNo = 'aa15799d5eceefc46b199be5581480c3'; // 	潮安县庵埠长丰食品厂
    const res = await service.getDimensionDetail(dimension, {
      keyNo,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(res.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('test CompanyOrMainMembersCriminalInvolveHistory', async () => {
    // 跳过此集成测试，因为可能因外部服务超时
    const dimension = BaseDimensions[DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory];
    const keyNo = '55cef6814dfd9e340a8a35d7841f7b1c'; // 	中国储备粮管理集团有限公司
    const res = await service.getDimensionDetail(dimension, {
      keyNo,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(res.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test MajorDispute', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.MajorDispute];
    const keyNo = '2d4e2e326a37a7757f001a6126f27a7f'; //广西建工集团建筑工程总承包有限公司
    const res = await service.getDimensionDetail(dimension, {
      keyNo,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(res.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('test MajorDispute analyze', async () => {
    const dimension = BaseDimensions[DimensionLevel3Enums.MajorDispute];
    const keyNo = '2d4e2e326a37a7757f001a6126f27a7f'; //广西建工集团建筑工程总承包有限公司
    const analyzeRes = await service.analyze(keyNo, [dimension], {
      keyNo,
      companyName: '广西建工集团建筑工程总承包有限公司',
      orgId: 208,
    });
    expect(analyzeRes.length).toBeGreaterThanOrEqual(1);
  });

  describe('getRelatedCompanyAndPerson 方法测试', () => {
    const mockCompanyId = 'test_company_123';

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('应该正确获取关联企业和人员 - 包含法定代表人', async () => {
      // Arrange - 准备测试数据
      const associateObjects = [
        {
          key: DetailsParamEnums.LegalRepresentative,
          status: 1,
        },
        {
          key: DetailsParamEnums.Employ,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockLegalPersonData: PersonData[] = [
        {
          name: '李四',
          keyNo: 'person456',
          job: '法定代表人',
          tags: ['法人'],
        },
      ];

      // Mock 依赖服务的方法
      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getLegalPerson').mockResolvedValue(mockLegalPersonData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person123', 'person456']);

      // Act - 执行测试
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert - 验证结果
      expect(result).toHaveLength(2);
      expect(result.find((p) => p.keyNo === 'person123')).toBeDefined();
      expect(result.find((p) => p.keyNo === 'person456')).toBeDefined();
      expect(entLibService.getEmployeeList).toHaveBeenCalledWith(mockCompanyId);
      expect(entLibService.getLegalPerson).toHaveBeenCalledWith(mockCompanyId);
    });

    it('应该正确获取关联企业和人员 - 包含实际控制人', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.ActualController,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockActualControllerData: PersonData[] = [
        {
          name: '王五',
          keyNo: 'person789',
          job: '实际控制人',
          tags: ['控制人'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(personHelper, 'getFinalActualController').mockResolvedValue(mockActualControllerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person123', 'person789']);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(2);
      expect(result.find((p) => p.keyNo === 'person789')).toBeDefined();
      expect(personHelper.getFinalActualController).toHaveBeenCalledWith(mockCompanyId);
    });

    it('应该正确获取关联企业和人员 - 包含股东信息', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
      ];
      const associateExcludes = [];
      const ignoreInvest = false;

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '股东甲',
          keyNo: 'person999',
          job: '股东',
          tags: ['股东'],
          stockPercent: '20%',
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person123', 'person999']);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(2);
      expect(result.find((p) => p.keyNo === 'person999')).toBeDefined();
      expect(entLibService.getPartnerList).toHaveBeenCalledWith(mockCompanyId, 'all', ignoreInvest);
    });

    it('应该正确过滤大股东 - 只包含大股东时', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.MajorShareholder,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '大股东甲',
          keyNo: 'person888',
          job: '股东',
          tags: ['大股东'],
          stockPercent: '51%',
        },
        {
          name: '小股东乙',
          keyNo: 'person777',
          job: '股东',
          tags: ['股东'],
          stockPercent: '10%',
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person888', 'person777']);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(1); // 只有大股东（员工也会被过滤掉，因为只配置了大股东）
      expect(result.find((p) => p.keyNo === 'person888')).toBeDefined(); // 大股东应该被包含
      expect(result.find((p) => p.keyNo === 'person777')).toBeUndefined(); // 小股东应该被过滤
      expect(result.find((p) => p.keyNo === 'person123')).toBeUndefined(); // 员工也被过滤（因为不是大股东）
    });

    it('应该正确排除投资机构', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
      ];
      const associateExcludes = [
        {
          key: DetailsParamEnums.ShareHolderInvest,
          status: 1,
        },
      ];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '普通股东',
          keyNo: 'person111',
          job: '股东',
          tags: ['股东'],
        },
        {
          name: '投资基金',
          keyNo: 'person222',
          job: '股东',
          tags: ['私募基金'],
        },
        {
          name: '机关单位',
          keyNo: 'g123456',
          job: '股东',
          tags: ['股东'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person123', 'person111']); // 只返回员工和普通股东

      // Act
      const result = await service.getRelatedCompanyAndPerson(
        mockCompanyId,
        associateObjects,
        associateExcludes,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
      );

      // Assert
      expect(result).toHaveLength(2); // 员工 + 普通股东
      expect(result.find((p) => p.keyNo === 'person111')).toBeDefined(); // 普通股东应该被包含
      expect(result.find((p) => p.keyNo === 'person222')).toBeUndefined(); // 投资基金应该被排除
      expect(result.find((p) => p.keyNo === 'g123456')).toBeUndefined(); // 机关单位应该被排除
    });

    it('应该正确去重人员数据', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.LegalRepresentative,
          status: 1,
        },
        {
          key: DetailsParamEnums.Employ,
          status: 1,
        },
      ];
      const associateExcludes = [];
      const uniqueCompanyId = 'dedup_test_company_456'; // 使用唯一的companyId避免缓存

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      // 法定代表人数据中包含重复的person123
      const mockLegalPersonData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123', // 相同的keyNo，应该被去重
          job: '法定代表人',
          tags: ['法人'],
        },
      ];

      // 明确重置所有相关的spy
      jest.restoreAllMocks();
      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getLegalPerson').mockResolvedValue(mockLegalPersonData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person123']); // 只允许保留person123

      // Act - 使用唯一的companyId
      const result = await service.getRelatedCompanyAndPerson(uniqueCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(1); // 应该只有一个人（去重后）
      expect(result[0].keyNo).toBe('person123');
    });

    it('应该正确过滤排除的公司', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '股东甲',
          keyNo: 'excluded_person_456',
          job: '股东',
          tags: ['股东'],
        },
        {
          name: '股东乙',
          keyNo: 'person789',
          job: '股东',
          tags: ['股东'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person123', 'person789']);

      // Act
      const result = await service.getRelatedCompanyAndPerson(
        mockCompanyId,
        associateObjects,
        associateExcludes,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
      );

      // Assert
      expect(result).toHaveLength(2); // 员工 + 未被排除的股东
      expect(result.find((p) => p.keyNo === 'person123')).toBeDefined();
      expect(result.find((p) => p.keyNo === 'person789')).toBeDefined();
      expect(result.find((p) => p.keyNo === 'excluded_person_456')).toBeUndefined(); // 被排除的股东不应该存在
    });

    it('应该正确处理空的 keyNo', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.LegalRepresentative,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockLegalPersonData: PersonData[] = [
        {
          name: '李四',
          keyNo: undefined, // 空的 keyNo
          job: '法定代表人',
          tags: ['法人'],
        },
        {
          name: '王五',
          keyNo: null, // null 的 keyNo
          job: '法定代表人',
          tags: ['法人'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getLegalPerson').mockResolvedValue(mockLegalPersonData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person123']);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(1); // 只有有效 keyNo 的人员
      expect(result[0].keyNo).toBe('person123');
    });

    it('应该正确处理没有配置关联对象的情况', async () => {
      // Arrange
      const associateObjects = []; // 没有配置关联对象
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      // 重新设置所有必要的spy
      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getLegalPerson').mockResolvedValue([]);
      jest.spyOn(personHelper, 'getFinalActualController').mockResolvedValue([]);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue([]);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person123']);

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(1); // 只有员工数据
      expect(result[0].keyNo).toBe('person123');
      // 验证没有调用其他服务方法
      expect(entLibService.getLegalPerson).not.toHaveBeenCalled();
      expect(personHelper.getFinalActualController).not.toHaveBeenCalled();
      expect(entLibService.getPartnerList).not.toHaveBeenCalled();
    });

    it('应该正确处理股东和大股东同时配置的情况', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
        {
          key: DetailsParamEnums.MajorShareholder,
          status: 1,
        },
      ];
      const associateExcludes = [];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '大股东甲',
          keyNo: 'person888',
          job: '股东',
          tags: ['大股东'],
          stockPercent: '51%',
        },
        {
          name: '小股东乙',
          keyNo: 'person777',
          job: '股东',
          tags: ['股东'],
          stockPercent: '10%',
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person123', 'person888', 'person777']); // 包含员工和所有股东

      // Act
      const result = await service.getRelatedCompanyAndPerson(mockCompanyId, associateObjects, associateExcludes);

      // Assert
      expect(result).toHaveLength(3); // 员工 + 大股东 + 小股东
      expect(result.find((p) => p.keyNo === 'person888')).toBeDefined(); // 大股东应该被包含
      expect(result.find((p) => p.keyNo === 'person777')).toBeDefined(); // 小股东也应该被包含
    });

    it('应该正确排除投资机构名称中包含关键词的股东', async () => {
      // Arrange
      const associateObjects = [
        {
          key: DetailsParamEnums.Shareholder,
          status: 1,
        },
      ];
      const associateExcludes = [
        {
          key: DetailsParamEnums.ShareHolderInvest,
          status: 1,
        },
      ];

      const mockEmployeeData: PersonData[] = [
        {
          name: '张三',
          keyNo: 'person123',
          job: '董事',
          tags: ['董事'],
        },
      ];

      const mockPartnerData: PersonData[] = [
        {
          name: '普通股东',
          keyNo: 'person111',
          job: '股东',
          tags: ['股东'],
        },
        {
          name: '北京投资有限公司',
          keyNo: 'person222',
          job: '股东',
          tags: ['股东'],
        },
        {
          name: '上海资本管理公司',
          keyNo: 'person333',
          job: '股东',
          tags: ['股东'],
        },
        {
          name: '深圳基金管理有限公司',
          keyNo: 'person444',
          job: '股东',
          tags: ['股东'],
        },
      ];

      jest.spyOn(entLibService, 'getEmployeeList').mockResolvedValue(mockEmployeeData);
      jest.spyOn(entLibService, 'getPartnerList').mockResolvedValue(mockPartnerData);
      jest.spyOn(excludeCompanyService, 'filterExcludedCompanyIds').mockResolvedValue(['person111', 'person222', 'person333', 'person444']);

      // Act
      const result = await service.getRelatedCompanyAndPerson(
        mockCompanyId,
        associateObjects,
        associateExcludes,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
      );

      // Assert
      expect(result).toHaveLength(2); // 员工 + 普通股东
      expect(result.find((p) => p.keyNo === 'person111')).toBeDefined(); // 普通股东应该被包含
      expect(result.find((p) => p.keyNo === 'person222')).toBeUndefined(); // 包含"投资"的公司应该被排除
      expect(result.find((p) => p.keyNo === 'person333')).toBeUndefined(); // 包含"资本"的公司应该被排除
      expect(result.find((p) => p.keyNo === 'person444')).toBeUndefined(); // 包含"基金"的公司应该被排除
    });
  });
});
