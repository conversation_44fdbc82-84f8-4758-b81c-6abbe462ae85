import { Global, Module } from '@nestjs/common';
import { AnalyzeCreditService } from './source/analyze.credit.service';
import { CaseService } from './source/case.service';
import { CompanyService } from './source/company.service';
import { CreditService } from './source/credit.service';
import { EnterpriseLibService } from './source/enterprise.lib.service';
import { ProService } from './source/pro.service';
import { RoverGraphService } from './source/rover.graph.service';
import { RoverService } from './source/rover.service';
import { TenderService } from './source/tender.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DiligenceExcludesEntity } from '../../libs/entities/DiligenceExcludesEntity';
import { CompanySearchModule } from '../company/company-search.module';
import { InnerBlacklistEntity } from '../../libs/entities/InnerBlacklistEntity';
import { NegativeNewsService } from './source/negative.news.service';
import { JudgementService } from './source/judgement.service';
import { CustomerEntity } from '../../libs/entities/CustomerEntity';
import { TaxService } from './source/tax.service';
import { PledgeService } from './source/pledge.service';
import { RiskChangeService } from './source/risk-change.service';
import { OuterBlacklistService } from './source/outer.blacklist.service';
import { SupervisePunishService } from './source/supervise.punish.service';
import { PersonHelper } from './helper/person.helper';
import { PersonEntity } from '../../libs/entities/PersonEntity';
import { PersonOrgCompanyEntity } from '../../libs/entities/PersonOrgCompanyEntity';
import { BidCollusiveService } from './source/bid.collusive.service';
import { EnterpriseLibDimensionService } from './dimensions/enterprise.lib.dimension.service';
import { OvsSanctionsService } from './source/ovs.sanctions.service';
import { RoverGraphHelper } from './helper/rover.graph.helper';
import { MktScpecOverdueEntity } from '../../libs/entities/MktScpecOverdueEntity';
import { DepartmentEntity } from '../../libs/entities/DepartmentEntity';
import { GroupsEntity } from 'libs/entities/GroupsEntity';
import { LabelEntity } from 'libs/entities/LabelEntity';
import { ExcludeCompanyService } from 'apps/exclude_company/exclude-company.service';
import { ExcludeCompanyEntity } from 'libs/entities/ExcludeCompanyEntity';

@Module({
  providers: [
    AnalyzeCreditService,
    CaseService,
    CompanyService,
    CreditService,
    EnterpriseLibService,
    ProService,
    RoverGraphService,
    RoverService,
    TenderService,
    NegativeNewsService,
    JudgementService,
    TaxService,
    PledgeService,
    RiskChangeService,
    OuterBlacklistService,
    OvsSanctionsService,
    SupervisePunishService,
    PersonHelper,
    RoverGraphHelper,
    BidCollusiveService,
    EnterpriseLibDimensionService,
    ExcludeCompanyService,
  ],
  exports: [
    AnalyzeCreditService,
    CaseService,
    CompanyService,
    CreditService,
    EnterpriseLibService,
    ProService,
    RoverGraphService,
    RoverService,
    TenderService,
    NegativeNewsService,
    JudgementService,
    TaxService,
    PledgeService,
    RiskChangeService,
    OuterBlacklistService,
    OvsSanctionsService,
    SupervisePunishService,
    PersonHelper,
    RoverGraphHelper,
    BidCollusiveService,
    EnterpriseLibDimensionService,
  ],
  imports: [
    CompanySearchModule,
    TypeOrmModule.forFeature([
      DiligenceExcludesEntity,
      InnerBlacklistEntity,
      CustomerEntity,
      PersonEntity,
      PersonOrgCompanyEntity,
      MktScpecOverdueEntity,
      DepartmentEntity,
      GroupsEntity,
      LabelEntity,
      ExcludeCompanyEntity,
    ]),
  ],
})
@Global()
export class DataModule {}
