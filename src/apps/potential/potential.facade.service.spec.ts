import { Test, TestingModule } from '@nestjs/testing';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { EntityManager, getManager } from 'typeorm';
import { RoverUser } from '../../libs/model/common';
import { AppTestModule } from '../app/app.test.module';
import { getTestUser } from '../test_utils_module/test.user';
import { PotentialFacadeService } from './potential.facade.service';
import { PotentialModule } from './potential.module';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { some, sum } from 'lodash';

jest.setTimeout(30000000);
describe('potentialFacadeService test', () => {
  let potentialFacadeService: PotentialFacadeService;
  let companyDetailService: CompanyDetailService;
  let entityManager: EntityManager;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, PotentialModule],
    }).compile();
    potentialFacadeService = module.get<PotentialFacadeService>(PotentialFacadeService);
    entityManager = getManager();
    companyDetailService = module.get<CompanyDetailService>(CompanyDetailService);
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('should be defined', () => {
    expect(potentialFacadeService).toBeDefined();
  });

  it('scanRisk test ', async () => {
    const currentUser: RoverUser = getTestUser(1001652, 101348);

    const result = await potentialFacadeService.scanRisk(currentUser, {
      companyId: '06e19fa18c7869dca1bec8238709c9c0',
      companyName: '中国国家铁路集团有限公司',
    });
    expect(result).toBeDefined();
  });

  it.skip('debug-updateScanByPersonVerify test ', async () => {
    const currentUser: RoverUser = getTestUser(208, 5171);

    const params = {
      companyId: 'f625a5b661058ba5082ca508f99ffe1b',
      companyName: '企查查科技股份有限公司',
      personId: 83482,
      snapshotId: '',
      diligenceId: 59,
      key: DimensionLevel3Enums.SuspectedInterestConflict,
      keyNo: 'pb9ac28faba8285ca3d516ab6a91235f',
      status: 0,
      verifyType: 2,
      comment: '/',
    };
    const result = await potentialFacadeService.updateScanByPersonVerify(currentUser, params);
    expect(result).toBeDefined();
  });

  // const getCount = (children, exclude?) => {
  //   // count 剔除
  //   let count = 0;
  //   children.forEach((item) => {
  //     if (item.isHis) {
  //       return true;
  //     }
  //     if (item.isGroup) {
  //       return true;
  //     }
  //     if (exclude?.includes(item.pos)) {
  //       return true;
  //     }
  //     if (item.count && !item.detailCount) {
  //       count += parseInt(item.count);
  //     } else if (!item.disable && item.count !== 0) {
  //       count++;
  //     }
  //   });
  //   return count;
  // };

  // const getCompanyCountInfo = async (companyId: string) => {
  //   const response = await companyDetailService.getCompanyDetail(companyId);
  //   const company = response.Result;
  //   const countInfo = company.CountInfo;
  //   // 必须要有企业资质才展示全部资质（包含备案地和安许证）
  //   const consQualificationCount = countInfo.JZQYZZCount || 0;
  //   // 必须要是企业资质、人员或四库业绩
  //   let shouldShowBm =
  //     sum([
  //       countInfo.JZQYZZCount, // 企业资质
  //       countInfo.JZZCRYCount, // 建筑注册人员
  //       countInfo.JZXMCount, // 四库业绩
  //     ]) > 0 && some(company?.Tags, (t) => t.Type === 408);

  //   // 建筑行业排除以下类型企业 109 学校 110 医院 506 保险机构，金融机构 908 机关单位 15 医院等级
  //   if (Array.isArray(company.Tags) && company.Tags.some((t) => [109, 110, 506, 908, 15].includes(t.Type))) {
  //     shouldShowBm = false;
  //   }

  //   /** 药品生产资质 */
  //   const drugpdrlistCount = (countInfo.MedicineProductionValidCount || 0) + (countInfo.GMPValidCount || 0);
  //   /** 药品经营资质 */
  //   const drugrunlistCount = (countInfo.MedicineManagementValidCount || 0) + (countInfo.GSPValidCount || 0);
  //   /** 器械生产资质 */
  //   const dceqpdrlistCount = (countInfo.MedicineDeviceProductionValidCount || 0) + (countInfo.DCEQ003Count || 0);
  //   /** 器械经营资质 */
  //   const dceqrunlistCount = (countInfo.MedicineDeviceManagementValidCount || 0) + (countInfo.DCEQ001Count || 0);
  //   /** 药品注册备案 */
  //   const drugreglistCount = (countInfo.DrugPDRCount || 0) + (countInfo.DCC99725Count || 0) + (countInfo.DrugCME || 0);
  //   /** 中药保护品种 */
  //   const drugcpvlistCount = countInfo.ChiMedicineProtectedValidCount || 0;
  //   /** 出口销售证明 */
  //   const drugsalelistCount = countInfo.ExportMedicineValidCount || 0;
  //   /** 药品抽检 */
  //   const drugchecklistCount = countInfo.DrugSCCount || 0;
  //   /** 境外药品备案 */
  //   const drugrecordlistCount = countInfo.DrugPORCount || 0;
  //   /** 国产医疗器械 */
  //   const dceqinlistCount = (countInfo.DomesticMedicineDeviceRegisterValidCount || 0) + (countInfo.DCEQ006Count || 0);
  //   /** 进口医疗器械 */
  //   const dceqoutlistCount = (countInfo.ImportMedicineDeviceValidCount || 0) + (countInfo.DCEQ007Count || 0);
  //   /** 一次性器械 */
  //   const dceqoncelistCount = countInfo.DisposableMedicineDeviceValidCount || 0;

  //   /** 除「药品抽检」外，其他维度有数据则展示医药信息板块  只有【药品抽检】有数据  整个医药信息也是不展示 注：医药信息的维度展示逻辑 和 count展示是2套独立的逻辑 */
  //   const ygCount =
  //     drugpdrlistCount +
  //     drugrunlistCount +
  //     dceqpdrlistCount +
  //     dceqrunlistCount +
  //     drugreglistCount +
  //     drugcpvlistCount +
  //     drugsalelistCount +
  //     drugrecordlistCount +
  //     dceqinlistCount +
  //     dceqoutlistCount +
  //     dceqoncelistCount;

  //   const kyqCount =
  //     (countInfo.MineralRegistCKCount && countInfo.MineralRegistCKValidCount) || (countInfo.MineralRegistTKCount && countInfo.MineralRegistTKValidCount);
  //   // 当农药生产资质维度有数据且没有全部过期才展示农药信息
  //   const nyxxCount = countInfo.PesticidesCertificateValidCount || countInfo.PesticidesPermissionValidCount || countInfo.PesticidesRegistrationValidCount;

  //   const children = [];
  //   const exclude = [];
  //   if (shouldShowBm) {
  //     const bmChildren = [];
  //     bmChildren.push(
  //       { name: '资质资格', pos: 'bmqualification', count: consQualificationCount, disable: !consQualificationCount },
  //       { name: '注册人员', pos: 'bmperson', count: countInfo.ZCRYCount, disable: !countInfo.ZCRYCount },
  //       { name: '中标业绩', pos: 'bmwtb', count: countInfo.JZZBYJV2Count, disable: !countInfo.JZZBYJV2Count },
  //       { name: '四库业绩', pos: 'bmproject', count: countInfo.JZXMCount, disable: !countInfo.JZXMCount },
  //     );
  //     children.push({ name: '建筑信息', pos: 'bmgroup', count: getCount(bmChildren), isGroup: true }, ...bmChildren);
  //   }
  //   if (ygCount) {
  //     const ygChildren = [];
  //     ygChildren.push(
  //       {
  //         name: '药品生产资质',
  //         pos: 'drugpdrlist',
  //         count: (countInfo.DCC99734Count || 0) + (countInfo.DCC99723Count || 0),
  //         disable: !drugpdrlistCount && !((countInfo.DCC99734Count || 0) + (countInfo.DCC99723Count || 0)),
  //       },
  //       {
  //         name: '药品经营资质',
  //         pos: 'drugrunlist',
  //         count: (countInfo.DCC99741Count || 0) + (countInfo.DCC99724Count || 0),
  //         disable: !drugrunlistCount && !((countInfo.DCC99741Count || 0) + (countInfo.DCC99724Count || 0)),
  //       },
  //       {
  //         name: '器械生产资质',
  //         pos: 'dceqpdrlist',
  //         count: (countInfo.DCEQ002Count || 0) + (countInfo.DCEQ003Count || 0),
  //         disable: !dceqpdrlistCount && !((countInfo.DCEQ002Count || 0) + (countInfo.DCEQ003Count || 0)),
  //       },
  //       {
  //         name: '器械经营资质',
  //         pos: 'dceqrunlist',
  //         count: (countInfo.DCEQ004Count || 0) + (countInfo.DCEQ001Count || 0),
  //         disable: !dceqrunlistCount && !((countInfo.DCEQ004Count || 0) + (countInfo.DCEQ001Count || 0)),
  //       },
  //       {
  //         name: '药品注册备案',
  //         pos: 'drugreglist',
  //         count: (countInfo.DrugPDRCount || 0) + (countInfo.DCC99725Count || 0) + (countInfo.DrugCME || 0),
  //         disable: !drugreglistCount && !((countInfo.DrugPDRCount || 0) + (countInfo.DCC99725Count || 0) + (countInfo.DrugCME || 0)),
  //       },
  //       { name: '中药保护品种', pos: 'drugcpvlist', count: countInfo.DrugCPVCount, disable: !drugcpvlistCount && !countInfo.DrugCPVCount },
  //       { name: '出口销售证明', pos: 'drugsalelist', count: countInfo.DrugESCCount, disable: !drugsalelistCount && !countInfo.DrugESCCount },
  //       { name: '药品抽检', pos: 'drugchecklist', count: countInfo.DrugSCCount, disable: !drugchecklistCount && !countInfo.DrugSCCount },
  //       { name: '境外药品备案', pos: 'drugrecordlist', count: countInfo.DrugPORCount, disable: !drugrecordlistCount && !countInfo.DrugPORCount },
  //       {
  //         name: '国产医疗器械',
  //         pos: 'dceqinlist',
  //         count: (countInfo.DCEQ006Count || 0) + (countInfo.DCC99726Count || 0),
  //         disable: !dceqinlistCount && !((countInfo.DCEQ006Count || 0) + (countInfo.DCC99726Count || 0)),
  //       },
  //       {
  //         name: '进口医疗器械',
  //         pos: 'dceqoutlist',
  //         count: (countInfo.DCC99727Count || 0) + (countInfo.DCEQ007Count || 0),
  //         disable: !dceqoutlistCount && !((countInfo.DCC99727Count || 0) + (countInfo.DCEQ007Count || 0)),
  //       },
  //       { name: '一次性器械', pos: 'dceqoncelist', count: countInfo.DrugDDRICount, disable: !dceqoncelistCount && !countInfo.DrugDDRICount },
  //     );
  //     children.push({ name: '医药信息', pos: 'yggroup', count: getCount(ygChildren), isGroup: true }, ...ygChildren);
  //   }

  //   // 矿业权信息
  //   if (kyqCount) {
  //     const kyqChildren = [];
  //     const registerInfoCount = (countInfo.MineralRegistTKCount || 0) + (countInfo.MineralRegistCKCount || 0);
  //     const acceptableCount = (countInfo.MineralAcceptanceCKCount || 0) + (countInfo.MineralAcceptanceTKCount || 0) + (countInfo.MineralAcceptanceZDCount || 0);
  //     const mortgageFilingCount = countInfo.MineralMortgageCount;
  //     const mineralFreezeCount = countInfo.MineralFreezeCount;
  //     kyqChildren.push(
  //       { name: '登记信息', pos: 'minieralregisterinfolist', count: registerInfoCount, disable: !registerInfoCount },
  //       { name: '受理公开', pos: 'minieralacceptablelist', count: acceptableCount, disable: !acceptableCount },
  //       { name: '抵押备案', pos: 'miningmortgagefilinglist', count: mortgageFilingCount, disable: !mortgageFilingCount },
  //       { name: '冻结信息', pos: 'prospecingrightsfreezelist', count: mineralFreezeCount, disable: !mineralFreezeCount },
  //     );
  //     children.push({ name: '采矿信息', pos: 'ckygroup', count: getCount(kyqChildren), isGroup: true }, ...kyqChildren);
  //   }

  //   // 农药信息
  //   if (nyxxCount) {
  //     const nyxxChildren = [];
  //     const pesticideQualificationCount = countInfo.PesticidesCertificateCount;
  //     const pesticideApprovalCount = countInfo.PesticidesPermissionCount;
  //     const pesticideRegistrationCount = countInfo.PesticidesRegistrationCount;
  //     nyxxChildren.push(
  //       { name: '农药生产资质', pos: 'pesticidequalification', count: pesticideQualificationCount, disable: !pesticideQualificationCount },
  //       { name: '农药生产批准', pos: 'pesticideapproval', count: pesticideApprovalCount, disable: !pesticideApprovalCount },
  //       { name: '农药登记', pos: 'pesticideregistration', count: pesticideRegistrationCount, disable: !pesticideRegistrationCount },
  //     );
  //     children.push({ name: '农药信息', pos: 'nyxxgroup', count: getCount(nyxxChildren), isGroup: true }, ...nyxxChildren);
  //   }

  //   return { companyId, companyName: company.Name, industryCount: getCount(children, exclude) };
  // };

  // /**
  //  * 脚本方法，与潜在利益无关，仅用于测试
  //  */
  // it.skip('debug-getPotentialPersonList test ', async () => {
  //   // const result = await getCompanyCountInfo('36981090a4ec7d29b578b7559b8d7b51');
  //   // console.log(result);
  //   const coampnyIds = [
  //     '97b50ed3922a49a2cb993f685e07e65e',
  //     '7e613ffe157af151aae102ca7bc3a79e',
  //     '8028abef2312949f0719d4ae56d52679',
  //     '980fb4785dd84bf6f1aa42631e9f8eff',
  //     '54c293738b855120d808f49b836b53ca',
  //     '6820aa54a4aab975fa7e60285129e208',
  //     'dda22006621e34427c6db9a01b7b98dd',
  //     '8b5facca68efe35537c27f66e495006f',
  //     '065e6518636b8558389e774b9241f929',
  //     'f87f9f5ce0091e5e64e6588d367886fe',
  //     '5cfc0891e13fc3dca4d2f59672148521',
  //     '9d680d2c490d1d4fd8fc1b61b60ce7bb',
  //     '9d680d2c490d1d4fd8fc1b61b60ce7bb',
  //     '7eeec76baf65df4a850e72a21f8d4e04',
  //     'dac195dc9156b396c7257da26d0a2697',
  //     '280b1dd9192221655e3428eee21b35bb',
  //     '915cd3b7ad70b1e262abe013a4e7ff94',
  //     '8c8803c0ea225295dfb5bceefe7e9dc9',
  //     '9a0029adcf51f7da8a15f4a4a40d9fa8',
  //     'ea8974f77beeefef2986a584e0733143',
  //     'fa078a468930c63c92f7909b5a1c5788',
  //     '668fe187309ebdf5bf162fddda404c79',
  //     'db7e37d94fec9585b83de7492c809fe6',
  //     'ffee5ae63f005a1d87f9c46a23b926e0',
  //     'a51c750caf2bea2fd3980dada13ae7cd',
  //     '3571d9ef6e0dfda2f92ab6a1efa7a289',
  //     '1dd7324e85002a94ed860a7d33ad8d28',
  //     '222f40e6c78dca1a70f76ab808ff7c34',
  //     '9455caa6b52cadddd8fcced982182d34',
  //     '62ba5f69c5dfda0da62558603371f422',
  //     'a26ed770bed7dbdeca4c4205962eb9d6',
  //     '02d68fdfc282fa75bcac3e3b30be5b4b',
  //     '02d68fdfc282fa75bcac3e3b30be5b4b',
  //     '93025fe41b54afd84c867c070a261b01',
  //     '93025fe41b54afd84c867c070a261b01',
  //     'f7432b8bb9e0ab7c7f495c68491cc6ef',
  //     'f7432b8bb9e0ab7c7f495c68491cc6ef',
  //     '9a33e2e519cbb4c5dbec5c5f4602c31f',
  //     'ef2df8ddb644ab441537e87780510b8e',
  //     'c1127ec9eaf4faa3f31d17bf7c90adf8',
  //     '7b37342403a80412f1c5091fea956b90',
  //     'e9babda8bde2e18275988b7fb5e6edb0',
  //     'b87a56d53f4b2896cdf670c22c6abd3e',
  //     '81c6c0b40fffe4df346f6580a01ac78a',
  //     'a86046c3f6a1a67a078b3f2d5dcf78fa',
  //     '0413171331ddce7320593ef2d3c0bad9',
  //     '413ddb604211e8ea3a38ccc93d77382a',
  //     'aafb85c76d8bac7e58821063d3ca2f5c',
  //     'cb7fa6e26e86dbed29aa720acb71f655',
  //     '9ba92ce1f7e64f6c9e22478dfcc2a079',
  //     'c41ba69e2bee4f1563ba1c994eba3dad',
  //     '12e9400c03cf8eb3e4eefd1c2e17c50c',
  //     '7bb7f10fbffbdb6af869af34e8697ecc',
  //     '98b05d47792b12cb5a061f2e814a3c90',
  //     'c271c7a4d2cfa129909e1e4226ac5b72',
  //     'c271c7a4d2cfa129909e1e4226ac5b72',
  //     '9189cafbadef80ea61068e0f6dbdb0ba',
  //     '9189cafbadef80ea61068e0f6dbdb0ba',
  //     '00c01a226348b7cfcb8b7ab348499f83',
  //     '4adb0baaae91386c5760ebe768c58c15',
  //     'b60ac46f282fdef808661702d06dae2d',
  //     '5585abf789b82378adbfaa503693241b',
  //     '5585abf789b82378adbfaa503693241b',
  //     '4241d66ae70779fab053b050a81d8f64',
  //     '92806dfcaea0a650d73ba963533dcaea',
  //     '6f019463d22370ee4e62df9b926f66d5',
  //     '9c335975e47901a048ee626bc2a761bc',
  //     'da19bd14512fd8008baf4e3796b99790',
  //     'a7b810895217d697ba3bb3dd10f72820',
  //     'bed905aefcdbc1263b93b4ec059b9228',
  //     '278ad8bea19f4da9e86ea78ea13e2259',
  //     '6d1d4ab45f22a1312d0b5ee8dce209f4',
  //     '8a4333b20b9c1b957f431978976cff91',
  //     'ca36742e95d7d8f4fdffd3dd4442bf9b',
  //     'b6709eef5d9aec7b14f261bcdc831a8f',
  //     '57c75901d38ddf813528fa283b563991',
  //     '39f9e16a0ec03c7591cb2b721d103d32',
  //     '7a21be1638ee08ac47dd993c3c2d551a',
  //     '34ee34a38415c7219a914810cd5e1c98',
  //     'd896af12a2e8227fb0b576111356f84a',
  //     '2f7b47fa3d3bd83b86d6e2f2599fd9f5',
  //     '2f7b47fa3d3bd83b86d6e2f2599fd9f5',
  //     '80f2ee38cd18bec5acf0f6411cee5cf1',
  //     'c8a86f0021dc34f72acb5bb6816340dd',
  //     '4abe0f43f835cd779a47acf884b60296',
  //     '99a9ea50a97f9844bdd4b328371bd723',
  //     '90b0684b9646b5b5a4f3b4a3a6825acb',
  //     '6ded42da127253c3b94dde9f5ba244d5',
  //     'addff54270dad9af2c51274d60d707e5',
  //     'e823f3bb77d03b4d0e1a70ad57868c38',
  //     '361afbb25161edd35b6442f0ba7b86a0',
  //     '8bc8a90b407e8c129f8b8cd53e1586d1',
  //     '285d5fd186444d043082eadb57adb2e9',
  //     '7cc46163d5524e1560998f41b3932789',
  //     '7cc46163d5524e1560998f41b3932789',
  //     '9553aa03a5dca4efcca96c0b35b759fd',
  //     '2fc81c75607751032bc46108ccecf795',
  //     '01e730922e415a0f15fd53504a4e7ac7',
  //     '1235a6d214f8c70f22e5ed0a7ecddd65',
  //     '36981090a4ec7d29b578b7559b8d7b51',
  //   ];
  //   // const coampnyIds = ['7a21be1638ee08ac47dd993c3c2d551a'];
  //   const resultList = await Promise.all(
  //     coampnyIds.map(
  //       async (companyId) => {
  //         return await getCompanyCountInfo(companyId);
  //       },
  //       { concurrency: 5 },
  //     ),
  //   );
  //   console.log(JSON.stringify(resultList));
  // });
});
