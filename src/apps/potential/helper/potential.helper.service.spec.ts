import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { PotentialHelperService } from './potential.helper.service';
import { PotentialModule } from '../potential.module';
import { AppTestModule } from 'apps/app/app.test.module';
import { getTestUser } from 'apps/test_utils_module/test.user';
import { RoverUser } from 'libs/model/common';
import { SettingTypeEnums } from 'libs/model/settings/SettingTypeEnums';
import { SettingsService } from 'apps/settings/settings.service';

jest.setTimeout(30000000);
describe('PotentialHelperService debug test', () => {
  let potentialHelperService: PotentialHelperService;
  let entityManager: EntityManager;
  let settingService: SettingsService;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, PotentialModule],
    }).compile();
    potentialHelperService = module.get<PotentialHelperService>(PotentialHelperService);
    entityManager = getManager();
    settingService = module.get<SettingsService>(SettingsService);
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('should be defined', () => {
    expect(potentialHelperService).toBeDefined();
  });

  it.skip('scanRisk test ', async () => {
    const currentUser: RoverUser = getTestUser(1001652, 101347);
    // const reportChatRequest = {
    //   businessType: 0,
    //   businessId: 691104,
    // };
    const potentialSetting = await settingService.getOrgSettings(1001652, SettingTypeEnums.potential);
    const result = await potentialHelperService.doPotentialRiskScanV2(
      currentUser.currentOrg,
      currentUser.userId,
      '06e19fa18c7869dca1bec8238709c9c0',
      '中国国家铁路集团有限公司',
      potentialSetting,
    );

    // (currentUser, {
    //   companyId: 'fc6443e4774f789f81b4f5a6e95e46dd',
    //   companyName: '苏州众汇聚合信息科技有限公司',
    // });
    expect(result).toBeDefined();
  });
});
