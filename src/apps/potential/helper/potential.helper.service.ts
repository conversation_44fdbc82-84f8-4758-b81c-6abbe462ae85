import { BadRequestException, Injectable } from '@nestjs/common';
import { RoverGraphService } from '../../data/source/rover.graph.service';
import { RoverService } from '../../data/source/rover.service';
import { PotentialDimensionGroupEnums, PotentialDimensionKeys } from '../../../libs/constants/potential.dimension.constants';
import { flattenDeep, pick, uniq, uniqBy } from 'lodash';
import * as Bluebird from 'bluebird';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { PotentialPersonHelperService } from './potential.person.helper.service';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { md5 } from '@kezhaozhao/qcc-utils/dist/qichacha/qichacha.util';
import { AccReportPO } from 'libs/model/potential/po/AccReportPO';
import { CompanySearchEsService } from 'apps/company/company-search-es.service';
import { TenderService } from 'apps/data/source/tender.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { PersonEntity } from 'libs/entities/PersonEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PersonOrgCompanyEntity } from 'libs/entities/PersonOrgCompanyEntity';
import { RoverGraphHelper } from 'apps/data/helper/rover.graph.helper';

@Injectable()
export class PotentialHelperService {
  constructor(
    readonly roverGraphHelper: RoverGraphHelper,
    readonly roverGraphService: RoverGraphService,
    readonly roverService: RoverService,
    readonly potentialPersonHelperService: PotentialPersonHelperService,
    readonly companyDetailService: CompanyDetailService,
    readonly companySearchEsService: CompanySearchEsService,
    readonly tenderService: TenderService,
    readonly companySearchService: CompanySearchService,
    @InjectRepository(PersonOrgCompanyEntity) readonly personOrgCompanyRepo: Repository<PersonOrgCompanyEntity>,
  ) {}

  private readonly logger: Logger = QccLogger.getLogger(PotentialHelperService.name);

  private readonly token = 'eeee2212-3c39-42b7-9e86-3f186030cb2d';

  private readonly riskAllTypes = ['Legal', 'Employ', 'Invest', 'HisLegal', 'HisEmploy', 'HisInvest', 'ActualController', 'FinalBenefit'];

  private readonly currencyCount = 3;

  async doPotentialRiskScanV2(orgId: number, userId: number, companyId: string, companyName: string, dimensionSettings: any) {
    const time1 = Date.now();

    const groupDefinitions = dimensionSettings.content;
    // 将模型打平为一个个独立的风险维度
    const allDefs: any[] = this.getAllDimension(groupDefinitions);
    if (!allDefs?.length) {
      throw new BadRequestException(RoverExceptions.Setting.Empty);
    }
    const timeCost = {};

    try {
      const [companyMainPartnerHits, companyHistoryMainPartner, parentCompanyHits, childCompanyHits, personRelatedCompanyHits] = await Bluebird.all([
        this.processCompanyMainPartner(orgId, companyId, companyName, allDefs)
          .then((r) => {
            timeCost['companyMainPartner'] = Date.now() - time1;
            return r;
          })
          .catch((error) => {
            this.logger.error(`处理公司主要人员失败: ${companyName}`, { error, companyId });
            timeCost['companyMainPartner'] = Date.now() - time1;
            return null;
          }),
        this.processCompanyHistoryMainPartner(orgId, companyId, companyName, allDefs)
          .then((r) => {
            timeCost['companyHistoryMainPartner'] = Date.now() - time1;
            return r;
          })
          .catch((error) => {
            this.logger.error(`处理公司历史主要人员失败: ${companyName}`, { error, companyId });
            timeCost['companyHistoryMainPartner'] = Date.now() - time1;
            return null;
          }),
        this.processParentCompany(orgId, companyId, allDefs)
          .then((r) => {
            timeCost['parentCompany'] = Date.now() - time1;
            return r;
          })
          .catch((error) => {
            this.logger.error(`处理母公司失败: ${companyName}`, { error, companyId });
            timeCost['parentCompany'] = Date.now() - time1;
            return null;
          }),
        this.processChildCompany(orgId, companyId, allDefs)
          .then((r) => {
            timeCost['childCompany'] = Date.now() - time1;
            return r;
          })
          .catch((error) => {
            this.logger.error(`处理子公司失败: ${companyName}`, { error, companyId });
            timeCost['childCompany'] = Date.now() - time1;
            return null;
          }),
        this.processPersonRelatedCompany(orgId, companyId, allDefs)
          .then((r) => {
            timeCost['personRelatedCompany'] = Date.now() - time1;
            return r;
          })
          .catch((error) => {
            this.logger.error(`处理人员关联公司失败: ${companyName}`, { error, companyId });
            timeCost['personRelatedCompany'] = Date.now() - time1;
            return null;
          }),
        // RA-15799 上下游企业数量不可控，下掉该维度
        // this.processBusinessRelatedCompany(orgId, companyId, allDefs).then((r) => {
        //   timeCost['businessRelatedCompany'] = Date.now() - time1;
        //   return r;
        // }),
        // RA-15663 processCompanyMainPartner 与 processSameNameOrContact 方法共同调用了getCompanyRelatedPersonList方法，
        // 在返回时会出现 companyPerson 被覆盖的情况,因此放到外面执行；造成覆盖的原因暂时未排查出来，后续再优化
        // this.processSameNameOrContact(orgId, companyId, companyName, allDefs).then((r) => {
        //   timeCost['sameNameOrContact'] = Date.now() - time1;
        //   return r;
        // }),
      ]);

      const sameNameOrContactHits = await this.processSameNameOrContact(orgId, companyId, companyName, allDefs)
        .then((r) => {
          timeCost['sameNameOrContact'] = Date.now() - time1;
          return r;
        })
        .catch((error) => {
          this.logger.error(`处理同名联系人失败: ${companyName}`, { error, companyId });
          timeCost['sameNameOrContact'] = Date.now() - time1;
          return null;
        });

      timeCost['total'] = Date.now() - time1;
      const allDimensionHits = {
        companyMainPartnerHits,
        companyHistoryMainPartner,
        parentCompanyHits,
        childCompanyHits,
        personRelatedCompanyHits,
        sameNameOrContactHits,
      };
      const notHitListIds = companyMainPartnerHits?.notHitList?.map((x) => x.id) || [];
      notHitListIds.push(...(companyHistoryMainPartner?.notHitList?.map((x) => x.id) || []));

      const dimensionHits = [];
      const interKeys = [];
      const groupResults = {};
      Object.keys(allDimensionHits).forEach((key) => {
        const hitDetail = allDimensionHits[key];
        if (!hitDetail || !hitDetail.hitList?.length) {
          return;
        }
        // 排除已确认不是
        const dataList = hitDetail.hitList?.filter((x) => !notHitListIds.includes(x.id));
        hitDetail.hitList = dataList;
        if (dataList?.length > 0) {
          dimensionHits.push(hitDetail.key);
          const groupResultArr = groupResults[hitDetail.groupKey];
          if (groupResultArr) {
            dataList.forEach((x) => {
              // 多维度命中时，取 sort 值小的
              const index = groupResultArr.findIndex((y) => y.id == x.id);
              if (index != -1 && groupResultArr[index].companyPerson.sort > x.companyPerson.sort) {
                groupResultArr[index] = x;
              } else if (index == -1) {
                groupResultArr.push(x);
              }
            });
            // groupResults[hitDetail.groupKey] = [...uniqBy(groupResultArr.concat(hitDetail.hitList), 'id')];
          } else {
            groupResults[hitDetail.groupKey] = [...uniqBy(hitDetail.hitList, 'id')];
          }
          interKeys.push(hitDetail.key);
        }
      });

      // RA-15736 两个同名员工，命中疑似同名，核实其中一个是本人，则另一个应直接不命中
      const staffWorkingOutsideForeignInvestmentHits = groupResults['StaffWorkingOutsideForeignInvestment'];
      const suspectedInterestConflictHits = groupResults['SuspectedInterestConflict'];
      if (staffWorkingOutsideForeignInvestmentHits?.length > 0 && suspectedInterestConflictHits?.length > 0) {
        const nameList = staffWorkingOutsideForeignInvestmentHits.map((hit) => hit.person?.name) || [];
        const hitArr = suspectedInterestConflictHits.filter((x) => !nameList.includes(x.person?.name));
        groupResults['SuspectedInterestConflict'] = hitArr;
      }
      // 补充展示所需信息
      await this.processHitPersonInfo(groupResults, companyId);
      // 处理风险结果
      const { result, description } = this.processDiligenceResult(groupResults, interKeys, allDefs);
      return {
        description,
        result,
        details: { timeCost, hitDetails: allDimensionHits, dimensionHits, groupResults },
      };
    } catch (error) {
      this.logger.error(`潜在利益冲突排查失败: ${companyName}`, { error, companyId, orgId, userId });
      timeCost['total'] = Date.now() - time1;
      // 返回基本结果，避免完全失败
      return {
        description: '排查过程中遇到异常，请稍后重试',
        result: 0,
        details: { timeCost, hitDetails: {}, dimensionHits: [], groupResults: {} },
      };
    }
  }

  processDiligenceResult(groupResults: any, interKeys: any[], allDefs: any[]) {
    let result = 0;
    if (groupResults['StaffWorkingOutsideForeignInvestment']?.length > 0) {
      result = 2; //潜在利益冲突
    } else if (groupResults['SuspectedInterestConflict']?.length > 0) {
      result = 1; // 疑似潜在利益冲突
    } else {
      result = 0; // 未命中
    }

    let description = '';
    switch (result) {
      case 0: {
        description = '本次排查未发现被排查企业与内部人员存在潜在利益冲突风险信息';
        break;
      }
      case 1: {
        description = `被排查企业存在【${interKeys
          .map((x) => allDefs.find((t) => x == t.key).name)
          .join(
            '】、【',
          )}】等潜在利益冲突风险，利益冲突（Conflict of Interest）可能引发腐败、舞弊或决策不公等问题，建议采取措施有效降低利益冲突风险，维护企业合法权益`;
        break;
      }
      case 2: {
        description = `被排查企业存在【${interKeys
          .map((x) => allDefs.find((t) => x == t.key).name)
          .join(
            '】、【',
          )}】等潜在利益冲突风险，利益冲突（Conflict of Interest）可能引发腐败、舞弊或决策不公等问题，建议采取措施有效降低利益冲突风险，维护企业合法权益`;
      }
    }
    return { result, description };
  }

  /**
   * 补充展示所需信息
   */
  async processHitPersonInfo(groupResults: any, companyId: string) {
    // 处理每个分组的数据
    await Bluebird.map(
      Object.entries(groupResults),
      async ([key, groupResult]: [string, any[]]) => {
        if (!groupResult?.length) return;
        const companyPersons = groupResult.map((hitItem) => hitItem.companyPerson).filter(Boolean);
        try {
          await Bluebird.all([
            this.processBenefitInfo(companyPersons),
            this.processRelationInfo(companyPersons, companyId),
            this.processCompanyInfo(companyPersons),
          ]);
        } catch (error) {
          this.logger.error(`处理组 ${key} 的信息时出错`, { error, key, companyPersonsCount: companyPersons.length });
          // 不阻断其他组的处理
        }
      },
      { concurrency: 2 }, // 减少分组处理的并发数，从3降到2
    );
    return groupResults;
  }

  async processBenefitInfo(companyPersons: any[]) {
    if (!companyPersons?.length) return;
    try {
      // 批量查询最终受益股份
      const batchParams = companyPersons.map((person) => ({
        queryKey: person.companyName,
        keyNo: person.keyNo,
        pageSize: 10,
        pageIndex: 1,
        isSortAsc: false,
      }));
      const benefitResponses = await Bluebird.map(
        batchParams,
        async (params) => {
          try {
            return await this.companyDetailService.getBenefitList(params);
          } catch (error) {
            this.logger.warn(`获取受益信息失败: ${params.keyNo}`, { error, params });
            return null; // 返回null，避免中断整个批次
          }
        },
        { concurrency: 2 }, // 减少并发数，从3降到2
      );
      // 处理查询结果
      benefitResponses.forEach((response, index) => {
        if (!response) return; // 跳过失败的请求

        const companyPerson = companyPersons[index];
        if (response.Status === 200 && response.Result?.length > 0) {
          const benefitItem = response.Result.find(
            (x) => x.BenefiterKeyNo === companyPerson.keyNo && x.Name.replace(/<em>|<\/em>/g, '') === companyPerson.companyName,
          );
          if (benefitItem) {
            companyPerson['benefitStockPercent'] = benefitItem.StockPercent;
          }
        }
      });
    } catch (error) {
      this.logger.error('批量获取受益信息失败', { error, companyPersons });
    }
  }

  async processRelationInfo(companyPersons: any[], companyId: string) {
    if (!companyPersons?.length) return;
    try {
      // 批量查询关联路径
      const relationParams = companyPersons.map((person) => ({
        depth: 5,
        endCompanyIds: uniq([person.companyId, companyId]),
        percentage: null,
        startId: person.keyNo,
        types: this.riskAllTypes,
      }));
      const relationResponses = await Bluebird.map(
        relationParams,
        async (params) => {
          try {
            return await this.roverGraphService.getPersonDeepRelations(params);
          } catch (error) {
            this.logger.warn(`获取关系链失败: ${params.startId}`, { error, params });
            return null; // 返回null，避免中断整个批次
          }
        },
        { concurrency: 2 }, // 减少并发数，从3降到2
      );
      relationResponses.forEach((response, index) => {
        if (!response) return; // 跳过失败的请求

        const companyPerson = companyPersons[index];
        if (response?.length > 0) {
          companyPerson['relationPathList'] = this.roverGraphHelper.processInvestigationList(response);
        }
      });
    } catch (error) {
      this.logger.error('批量获取关系链失败', { error, companyPersons });
    }
  }

  async processCompanyInfo(companyPersons: any[]) {
    if (!companyPersons?.length) return;
    try {
      const companyIds = companyPersons.map((x) => x.companyId);
      const companyInfos = await Bluebird.map(
        companyIds,
        async (companyId) => {
          try {
            return await this.companySearchService.companyDetailsQcc(companyId);
          } catch (error) {
            this.logger.warn(`获取公司信息失败: ${companyId}`, { error, companyId });
            return null; // 返回null，避免中断整个批次
          }
        },
        { concurrency: 1 }, // 大幅减少并发数，从3降到1，避免过多的同时调用
      );
      // 处理查询结果
      companyInfos.forEach((response, index) => {
        if (!response) return; // 跳过失败的请求

        const companyPerson = companyPersons[index];
        // 公司登记信息
        companyPerson['shortStatus'] = response.ShortStatus;
      });
    } catch (error) {
      this.logger.error('批量获取公司信息失败', { error, companyPersons });
    }
  }
  /**
   * 公司主要人员排查
   * @param orgId
   * @param companyId
   * @param allDefs
   * @returns
   */
  async processCompanyMainPartner(orgId: number, companyId: string, companyName: string, allDefs: any[]) {
    const targetDefPos = allDefs.filter((d) => d.key === PotentialDimensionKeys.CompanyMainPartner);
    if (!targetDefPos?.length) {
      return null;
    }
    const { strategyModel, groupKey, key, sort } = targetDefPos[0];
    const personList = await this.getOrgPersonList(targetDefPos[0], orgId, 1);
    if (!personList?.length) {
      return null;
    }
    const detailsParam = strategyModel.detailsParams?.find((x) => x.field === 'types');
    const riskTypes = detailsParam?.fieldVal?.filter((x) => x.status == 1)?.map((x) => x.key);
    if (!riskTypes?.length) {
      return null;
    }
    const percentage = detailsParam?.percentage;
    const comapnyRelatedList = await this.potentialPersonHelperService.getCompanyRelatedPersonList(companyId, companyName, riskTypes, percentage);
    // 判断是否命中
    const hitList = [];
    const sameNamePerson = [];
    const notHitList = [];

    // 使用Bluebird优化嵌套forEach，保证原有业务逻辑
    await Bluebird.map(
      comapnyRelatedList,
      async (companyPerson) => {
        companyPerson['sort'] = sort;
        const person = personList.find((y) => y.keyNo && y.keyNo == companyPerson.keyNo);
        if (person) {
          // 命中，添加命中详情并继续下一个元素
          this.addHitDetail(hitList, person, companyPerson, key, false);
          return;
        }
        const personHitList = personList.filter((y) => y.name && y.name == companyPerson.name && y.cardId);
        if (personHitList.length > 0) {
          // 使用Bluebird处理内层forEach，但设置并发为1以保持原有的串行逻辑
          await Bluebird.map(
            personHitList,
            async (person2) => {
              const hitPerson = hitList.find((x) => x.id == person2.id);
              if (!hitPerson) {
                sameNamePerson.push({ person: person2, companyPerson });
              }
            },
            { concurrency: 1 }, // 串行处理以保持原有逻辑
          );
        }
      },
      { concurrency: 2 }, // 外层适度并发
    );

    // 处理未命中，但名字相同且存在身份证的人员
    if (sameNamePerson?.length) {
      const sameNamePersonList = sameNamePerson.map((x) => x.person);
      // 这里第三方接口不返回历史信息，所以历史关系不会被命中
      const { companyIds, accReportList } = await this.getRelatedCompanyList(sameNamePersonList);

      // 使用Bluebird并发处理，但限制并发数
      await Bluebird.map(
        sameNamePerson,
        async (x) => {
          const { person, companyPerson } = x;
          const companyId = companyIds.find((y) => y == companyPerson.companyId);
          if (companyId) {
            // 命中
            this.addHitDetail(hitList, person, companyPerson, key, true);
            return;
          }
          const accReportArr = accReportList.filter((x) => x.personId == person.id);
          if (accReportArr.length) {
            let hitFound = false;
            // 内层循环保持for...of以支持break
            for (const accReport of accReportArr) {
              if (hitFound) break; // 如果已经命中，跳出内层循环

              const companyIdByAcc = accReport['companyId'];
              const personName = person.name;
              try {
                // 先查询主要人员
                let personKeyNo = await this.getPersonKeyNoByCompanyIdAndName(companyIdByAcc, personName);
                if (!personKeyNo) {
                  // 如果没有查到，再查历史主要人员
                  personKeyNo = await this.getPersonKeyNoByCompanyIdAndNameHistory(companyIdByAcc, personName);
                }
                if (personKeyNo && companyPerson.keyNo == personKeyNo) {
                  // 命中
                  this.addHitDetail(hitList, person, companyPerson, key, true);
                  hitFound = true;
                  break;
                }
              } catch (error) {
                this.logger.warn(`历史人员keyNo查询失败: ${companyIdByAcc}, ${personName}`, error);
                // 继续处理下一个，不中断整个流程
              }
            }
          }
        },
        { concurrency: 1 }, // 串行处理以保持原有逻辑和避免过多并发请求
      );
    }
    // 这里不包含当前数据，不能直接判定未命中
    return { groupKey, key, hitList, notHitList };
  }

  /**
   * 公司历史主要人员排查，第三方接口不返回历史数据，这里需要单独处理
   * @param orgId
   * @param companyId
   * @param allDefs
   * @returns
   */
  async processCompanyHistoryMainPartner(orgId: number, companyId: string, companyName: string, allDefs: any[]) {
    const targetDefPos = allDefs.filter((d) => d.key === PotentialDimensionKeys.CompanyHistoryMainPartner);
    if (!targetDefPos?.length) {
      return null;
    }
    const { strategyModel, groupKey, key, sort } = targetDefPos[0];
    const personList = await this.getOrgPersonList(targetDefPos[0], orgId, 1);
    if (!personList?.length) {
      return null;
    }
    const detailsParam = strategyModel.detailsParams?.find((x) => x.field === 'types');
    const riskTypes = detailsParam?.fieldVal?.filter((x) => x.status == 1)?.map((x) => x.key);
    if (!riskTypes?.length) {
      return null;
    }
    const percentage = detailsParam?.percentage;
    const companyRelatedList = await this.potentialPersonHelperService.getCompanyRelatedPersonList(companyId, companyName, riskTypes, percentage);
    // 判断是否命中
    const hitList = [];
    const sameNamePerson = [];
    const notHitList = [];

    // 使用Bluebird优化嵌套forEach，保证原有业务逻辑
    await Bluebird.map(
      companyRelatedList,
      async (companyPerson) => {
        companyPerson['sort'] = sort;
        const person = personList.find((y) => y.keyNo && y.keyNo == companyPerson.keyNo);
        if (person) {
          // 命中，添加命中详情并继续下一个元素
          this.addHitDetail(hitList, person, companyPerson, key, false);
          return;
        }
        const personHitList = personList.filter((y) => y.name && y.name == companyPerson.name && y.cardId);
        if (personHitList.length > 0) {
          // 使用Bluebird处理内层forEach，但设置并发为1以保持原有的串行逻辑
          await Bluebird.map(
            personHitList,
            async (person2) => {
              const hitPerson = hitList.find((x) => x.id == person2.id);
              if (!hitPerson) {
                sameNamePerson.push({ person: person2, companyPerson });
              }
            },
            { concurrency: 1 }, // 串行处理以保持原有逻辑
          );
        }
      },
      { concurrency: 2 }, // 外层适度并发
    );

    // 处理未命中，但名字相同且存在身份证的人员
    if (sameNamePerson?.length) {
      const sameNamePersonList = sameNamePerson.map((x) => x.person);
      const { accReportList } = await this.getRelatedCompanyList(sameNamePersonList);

      // 使用Bluebird并发处理，但限制并发数
      await Bluebird.map(
        sameNamePerson,
        async (x) => {
          const { person, companyPerson } = x;
          const accReportArr = accReportList.filter((x) => x.personId == person.id);
          if (accReportArr.length) {
            let hitFound = false;
            // 内层循环保持for...of以支持break
            for (const accReport of accReportArr) {
              if (hitFound) break; // 如果已经命中，跳出内层循环

              const companyIdByAcc = accReport['companyId'];
              const personName = person.name;
              try {
                // 先查询主要人员
                let personKeyNo = await this.getPersonKeyNoByCompanyIdAndName(companyIdByAcc, personName);
                if (!personKeyNo) {
                  // 如果没有查到，再查历史主要人员
                  personKeyNo = await this.getPersonKeyNoByCompanyIdAndNameHistory(companyIdByAcc, personName);
                }
                if (personKeyNo && companyPerson.keyNo == personKeyNo) {
                  // 命中
                  this.addHitDetail(hitList, person, companyPerson, key, true);
                  hitFound = true;
                  break;
                }
              } catch (error) {
                this.logger.warn(`历史人员keyNo查询失败: ${companyIdByAcc}, ${personName}`, error);
                // 继续处理下一个，不中断整个流程
              }
            }
          }
        },
        { concurrency: 1 }, // 串行处理以保持原有逻辑和避免过多并发请求
      );
    }
    return { groupKey, key, hitList, notHitList };
  }

  async getPersonKeyNoByCompanyIdAndNameHistory(companyIdByAcc: string, personName: string) {
    let personKeyNo = null;
    if (!companyIdByAcc || !personName) {
      return personKeyNo;
    }
    try {
      const params = { keyNo: companyIdByAcc, queryKey: personName };
      const response = await this.companyDetailService.getCoyHistoryEmployee2Info(params);
      if (response.Status == 200 && response.Result?.length > 0) {
        const personInfo = response.Result.filter((x) => x.EmployeeName?.replace('<em>', '')?.replace('</em>', '') == personName)?.[0];
        personKeyNo = personInfo?.KeyNo;
      }
    } catch (error) {
      this.logger.warn(`查询历史人员KeyNo失败: ${companyIdByAcc}, ${personName}`, { error });
    }
    return personKeyNo;
  }

  async getPersonKeyNoByCompanyIdAndName(companyIdByAcc: string, personName: string) {
    let personKeyNo = null;
    if (!companyIdByAcc || !personName) {
      return personKeyNo;
    }
    try {
      const params = { keyNo: companyIdByAcc, nodeName: 'Employees', queryKey: personName };
      const response = await this.companyDetailService.getEmployeeList(params);
      if (response.Status == 200 && response.Result?.length > 0) {
        const personInfo = response.Result.filter((x) => x.EmployeeName?.replace('<em>', '')?.replace('</em>', '') == personName)?.[0];
        personKeyNo = personInfo?.KeyNo;
      }
    } catch (error) {
      this.logger.warn(`查询人员KeyNo失败: ${companyIdByAcc}, ${personName}`, { error });
    }
    return personKeyNo;
  }

  async getOrgPersonList(dimensionDef: any, orgId: number, identityInfoFilterType = 1) {
    const { personType, personGroups } = dimensionDef;
    return await this.potentialPersonHelperService.getOrgPersonList(personType, personGroups, orgId, identityInfoFilterType);
  }

  /**
   * 母公司排查
   * @param orgId
   * @param companyId
   * @param allDefs
   * @returns
   */
  async processParentCompany(orgId: number, companyId: string, allDefs: any[]) {
    const targetDefPos = allDefs.filter((d) => d.key === PotentialDimensionKeys.ParentCompany);
    if (!targetDefPos?.length) {
      return {};
    }
    const dimensionDef = targetDefPos[0];
    return await this.doProcessParentChildCompany(orgId, companyId, dimensionDef);
  }

  /**
   * 子公司排查
   * @param orgId
   * @param companyId
   * @param allDefs
   * @returns
   */
  async processChildCompany(orgId: number, companyId: string, allDefs: any[]) {
    const targetDefPos = allDefs.filter((d) => d.key === PotentialDimensionKeys.ChildCompany);
    if (!targetDefPos?.length) {
      return {};
    }
    const dimensionDef = targetDefPos[0];
    return await this.doProcessParentChildCompany(orgId, companyId, dimensionDef);
  }

  /**
   * 执行母公司或子公司排查
   * @param orgId
   * @param companyId
   * @param dimensionDef
   * @returns
   */
  async doProcessParentChildCompany(orgId: number, companyId: string, dimensionDef: any) {
    const { strategyModel, groupKey, key, sort } = dimensionDef;
    const personList = await this.getOrgPersonList(dimensionDef, orgId, 1);
    if (!personList?.length) {
      return {};
    }
    const percentage = strategyModel.detailsParams?.find((x) => x.field == 'percentage')?.fieldVal;
    const depth = strategyModel.detailsParams?.find((x) => x.field == 'depth')?.fieldVal;
    let companyList;
    if (dimensionDef.key === PotentialDimensionKeys.ChildCompany) {
      // 获取子公司列表
      companyList = await this.roverGraphService.getChildCompanyList(companyId, depth, percentage);
    } else {
      // 获取母公司列表
      companyList = await this.roverGraphService.getParentCompanyList(companyId, depth, percentage);
    }
    const hitList = [];
    if (companyList?.length > 0) {
      const relatedCompanyIdAndNameMap = companyList.map((x) => ({
        companyId: x.companyId,
        companyName: x.companyName,
      }));
      const riskTypes = this.riskAllTypes;

      const relatedCompanyResultList = await Bluebird.map(relatedCompanyIdAndNameMap, async (x) => {
        return await this.potentialPersonHelperService.getCompanyRelatedPersonList(x.companyId, x.companyName, riskTypes, null);
      });
      const sameNamePersonList = [];
      relatedCompanyResultList.forEach((relatedList) => {
        relatedList.forEach((companyPerson) => {
          companyPerson['sort'] = sort;
          const person = personList.find((y) => y.keyNo && y.keyNo == companyPerson.keyNo);
          if (person) {
            // 命中
            this.addHitDetail(hitList, person, companyPerson, key, false);
            return;
          }
          const person2 = personList.find((y) => y.name && y.name == companyPerson.name && y.cardId);
          if (person2) {
            sameNamePersonList.push(person2);
          }
        });
      });
      // 处理未命中，但名字相同且存在身份证的人员
      if (sameNamePersonList?.length) {
        const { companyIds, accReportList } = await this.getRelatedCompanyList(sameNamePersonList);
        sameNamePersonList.forEach((x) => {
          relatedCompanyResultList.forEach((companyPersonResult) => {
            companyPersonResult.forEach((companyPerson) => {
              if (companyIds.includes(companyPerson.companyId) && companyPerson.name == x.name) {
                // 命中
                this.addHitDetail(hitList, x, companyPerson, key, true);
              }
            });
          });
        });
      }
    }
    return { groupKey, key, hitList };
  }

  addHitDetail(hitList: any[], person: PersonEntity, companyPerson: any, key: string, cardIdCheck: boolean) {
    const hitPerson = hitList.find((x) => x.id == person.id);
    if (hitPerson) {
      Object.assign(hitPerson.person, pick(person, ['isSameName', 'isSameContact', 'samePhone', 'sameEmail', 'relation', 'supplierCustomer']));
      if (companyPerson) {
        if (companyPerson.job && typeof companyPerson.job === 'string') {
          if (companyPerson.job.includes(',')) {
            companyPerson.job = companyPerson.job?.split(',') || [];
          } else {
            companyPerson.job = [companyPerson.job];
          }
        }
        // 优先取存在当前关系的公司
        const isCurrentRelation =
          companyPerson.hitKey.includes('Legal') ||
          companyPerson.hitKey.includes('Employ') ||
          companyPerson.hitKey.includes('Invest') ||
          companyPerson.hitKey.includes('ActualController') ||
          companyPerson.hitKey.includes('FinalBenefit');
        const hitCompanyPerson = hitPerson.companyPerson;
        if (isCurrentRelation || !hitCompanyPerson) {
          hitPerson['companyPerson'] = companyPerson;
        }
      }
    } else {
      if (person.cardId) {
        // 加密处理
        person.cardId = person.cardId.replace(/^(.{6})(.*)(.{4})$/, '$1********$3');
      }
      const hitDetail = Object.assign({ hitType: key, cardIdCheck, person, id: person.id });
      if (companyPerson) {
        if (companyPerson.job && typeof companyPerson.job === 'string') {
          if (companyPerson.job.includes(',')) {
            companyPerson.job = companyPerson.job?.split(',') || [];
          } else {
            companyPerson.job = [companyPerson.job];
          }
        }
        Object.assign(hitDetail, { companyPerson });
      }
      hitList.push(hitDetail);
    }
  }

  /**
   * 员工关联公司排查
   * @param orgId
   * @param companyId
   * @param allDefs
   * @returns
   */
  async processPersonRelatedCompany(orgId: number, companyId: string, allDefs: any[]) {
    const targetDefPos = allDefs.filter((d) => d.key === PotentialDimensionKeys.PersonRelatedCompany);
    if (!targetDefPos?.length) {
      return null;
    }
    const { personType, personGroups, groupKey, key, strategyModel, sort } = targetDefPos[0];
    const detailsParam = strategyModel.detailsParams?.find((x) => x.field === 'types');
    const types = detailsParam?.fieldVal?.filter((x) => x.status == 1).map((x) => x.key);
    const percentage = detailsParam?.percentage;
    if (!types?.length) {
      return null;
    }
    const personList = await this.getOrgPersonList(targetDefPos[0], orgId, 1);
    if (!personList?.length) {
      this.logger.info(
        'processPersonRelatedCompany getOrgPersonList is empty, orgId: %s, personType: %s, personGroups: %s',
        orgId,
        JSON.stringify(personType),
        JSON.stringify(personGroups),
      );
      return null;
    }

    const { companyIds, accReportList, dbHasCompanyIdPersonList } = await this.getRelatedCompanyList(personList);

    if (!companyIds?.length) {
      this.logger.info(
        'processPersonRelatedCompany companyIds is empty, orgId: %s, personType: %s, personGroups: %s',
        orgId,
        JSON.stringify(personType),
        JSON.stringify(personGroups),
      );
      return null;
    }
    const params = {
      endCompanyIds: companyIds,
      orgId,
      percentage,
      startCompanyId: companyId,
      types,
      depth: 3,
    };
    // 通过图数据库查询是否存在关联关系
    const relations = await this.roverGraphService.getCompanyDeepRelations(params);
    // 取出存在关联的公司
    const relationCompanyIdAndNames = relations?.map((x) => {
      let relationCompanyId;
      let relationCompanyName;
      if (x.endCompanyKeyno == companyId) {
        relationCompanyId = x.startCompanyKeyno;
        relationCompanyName = x.startCompanyName;
      } else {
        relationCompanyId = x.endCompanyKeyno;
        relationCompanyName = x.endCompanyName;
      }
      return { companyId: relationCompanyId, companyName: relationCompanyName };
    });

    // 取出存在关联的人员
    const hitList = [];
    if (relationCompanyIdAndNames?.length > 0) {
      const relatedCompanyResultList = await Bluebird.map(relationCompanyIdAndNames, async (companyIdAndNames) => {
        const relatedComapnyPersonList = await this.potentialPersonHelperService.getCompanyRelatedPersonList(
          companyIdAndNames.companyId,
          companyIdAndNames.companyName,
          types,
          null,
        );
        return { companyId: companyIdAndNames.companyId, relatedComapnyPersonAList: relatedComapnyPersonList };
      });
      relationCompanyIdAndNames?.forEach((x) => {
        let person;
        const accReport = accReportList.find((y) => y.companyId && y.companyId == x.companyId);
        if (accReport) {
          person = personList.find((person) => person.id == accReport.personId);
        } else {
          person = dbHasCompanyIdPersonList.find((person) => person.companyId == x.companyId);
        }
        const relation = relations.find((y) => y.endCompanyKeyno == x.companyId);

        const companyPerson = relatedCompanyResultList
          .filter((y) => y.companyId == x.companyId)?.[0]
          ?.relatedComapnyPersonAList?.find((y) => y.name == person.name);
        if (companyPerson) {
          companyPerson['sort'] = sort;
          const hitDetail = {
            hitType: key,
            cardIdCheck: true,
            id: person.id,
            person,
            relation,
            companyPerson,
          };
          hitList.push(hitDetail);
        }
      });
    }

    return { groupKey, key, hitList };
  }

  /**
   * 获取相关人员关联的公司列表
   * @param personList 人员列表
   */
  async getRelatedCompanyList(personList: PersonEntity[]) {
    const dbHasCompanyIdPersonList = [];
    const accReportList = [];
    const accComapnyName = [];
    const accCreditCode = [];
    const companyIds = [];
    // 并行处理每个人员的关联公司信息
    await Promise.all(
      personList.map(async (person) => {
        const cardId = person.cardId;
        // 如果人员已有公司ID，直接加入列表
        if (person.companyId) {
          dbHasCompanyIdPersonList.push(person);
          companyIds.push(person.companyId);
        }
        // 如果有身份证信息，获取accReport
        if (cardId) {
          const idCardMd5 = md5(cardId).toUpperCase();
          const uuid = idCardMd5;
          try {
            // 调用接口获取accReport
            const accReport = await this.companyDetailService.getPersonAccReport(person.name, idCardMd5, this.token, uuid);
            if (accReport?.data?.length) {
              // 处理accReport数据
              accReport.data.forEach((item) => {
                const accReportPO = item as AccReportPO;
                if (accReportPO?.companyName) {
                  if (accReportPO.creditNo) {
                    accCreditCode.push(accReportPO.creditNo);
                  } else {
                    accComapnyName.push(accReportPO.companyName);
                  }
                  accReportPO['personId'] = person.id;
                  accReportPO['personName'] = person.name;
                  accReportPO['uuid'] = uuid;
                  accReportList.push(accReportPO);
                }
              });
            }
          } catch (error) {
            // 记录错误日志
            this.logger.error(`获取${person.name}的accReport失败,idCardMd5 : %s,error: %s`, idCardMd5, error);
          }
        }
      }),
    );
    // 如果有accReport数据，获取对应的公司keyNo
    if (accReportList?.length) {
      const creditCodeList = uniq(accCreditCode);
      const companyNameList = uniq(accComapnyName);
      const companyList = await this.companySearchEsService.getCompanyKeyNo(creditCodeList, companyNameList, 20);
      accReportList.forEach((x) => {
        const company = companyList.find((y) => {
          if (x.creditNo?.length > 0 && y.creditcode == x.creditNo) {
            return true;
          }
          if (x.companyName?.length > 0 && y.name == x.companyName) {
            return true;
          }
        });
        if (company) {
          x['companyId'] = company.id;
          x['companyName'] = company.name;
        }
      });
      companyIds.push(...(accReportList.map((x) => x.companyId).filter((x) => x) || []));
    }
    return { companyIds, accReportList, dbHasCompanyIdPersonList };
  }

  /**
   * 相同姓名、联系方式排查
   * @param orgId
   * @param companyId
   * @param allDefs
   * @returns
   */
  async processSameNameOrContact(orgId: number, companyId: string, companyName: string, allDefs: any[]) {
    const targetDefPos = allDefs.filter((d) => d.key === PotentialDimensionKeys.SameNameOrContact);
    if (!targetDefPos?.length) {
      return [];
    }
    const { personType, personGroups, groupKey, key, strategyModel } = targetDefPos[0];
    const personList = await this.getOrgPersonList(targetDefPos[0], orgId, 2);
    if (!personList?.length) {
      this.logger.info(
        'processSameNameOrContact getOrgPersonList is empty, orgId: %s, personType: %s, personGroups: %s',
        orgId,
        JSON.stringify(personType),
        JSON.stringify(personGroups),
      );
      return null;
    }
    const detailsParam = strategyModel.detailsParams?.find((x) => x.field === 'types');
    const types = detailsParam?.fieldVal?.filter((x) => x.status == 1)?.map((x) => x.key);
    if (!types?.length) {
      return null;
    }
    const percentage = detailsParam?.percentage;
    const comapnyRelatedPersonResult = await this.potentialPersonHelperService.getCompanyRelatedPersonList(companyId, companyName, types, percentage);
    // 判断是否命中同名
    const hitList = [];
    // 补充核实非本人结果校验
    const personIds = personList.map((x) => x.id);
    const verifyPersonList = await this.personOrgCompanyRepo
      .createQueryBuilder('poc')
      .where('poc.personId in (:...personIds)', { personIds })
      .andWhere('poc.orgId = :orgId', { orgId })
      .andWhere('poc.status = 0 and association = 0')
      .getMany();

    comapnyRelatedPersonResult.forEach((companyPerson) => {
      const personHitList = personList.filter((y) => y.name && y.name == companyPerson.name);
      if (personHitList?.length) {
        personHitList.forEach((person) => {
          if (verifyPersonList?.length > 0) {
            const verifyPerson = verifyPersonList.find((x) => x.personId == person.id);
            if (companyPerson.keyNo && verifyPerson?.keyNo == companyPerson.keyNo) {
              // 已核实非本人
              return;
            }
          }
          person['isSameName'] = true;
          this.addHitDetail(hitList, person, companyPerson, key, false);
        });
      }
    });
    //相同联系方式
    const { tellist, emaillist } = await this.companySearchService.getContact(companyId);
    if (!tellist?.length && !emaillist?.length) {
      return { groupKey, key, hitList };
    }
    // 处理相同联系方式的人员
    personList.forEach((person) => {
      let samePhone = '';
      let sameEmail = '';
      if (person.phone || person.email) {
        const sameTel = tellist?.find((t) => person.phone?.includes(t.t));
        if (sameTel) {
          samePhone = sameTel.t;
        }
        const sameEmailItem = emaillist?.find((e) => person.email?.includes(e.e));
        if (sameEmailItem) {
          sameEmail = sameEmailItem.e;
        }
        if (samePhone || sameEmail) {
          person['isSameContact'] = true;
          person['samePhone'] = samePhone;
          person['sameEmail'] = sameEmail;
          this.addHitDetail(hitList, person, null, key, false);
        }
      }
    });
    return { groupKey, key, hitList };
  }

  /**
   * 将模型打平为一个个独立的风险维度
   * @param dimensionContent
   * @returns
   */
  getAllDimension(dimensionContent: any): any[] {
    return flattenDeep(
      Object.keys(dimensionContent)?.map((groupKey) => {
        if (!dimensionContent[groupKey]?.status) {
          return null;
        }
        const group = dimensionContent[groupKey];
        const { items, personType, personGroups, activeDimensionType } = group;
        return items?.map((item) => {
          if (item.status != 1 || (item.dimensionType && !activeDimensionType?.includes(item.dimensionType))) {
            return null;
          }
          if (activeDimensionType && item.dimensionType && !activeDimensionType.includes(item.dimensionType)) {
            return null;
          }
          item.groupKey = groupKey;
          item.personType = personType;
          item.personGroups = personGroups;
          item.activeDimensionType = activeDimensionType;
          return item;
        });
      }),
    ).filter((x) => x);
  }

  getPaidCount(dimensionSettings: any) {
    const groupDefinitions = dimensionSettings.content;
    // 将模型打平为一个个独立的风险维度
    const allDefs: any[] = this.getAllDimension(groupDefinitions);
    const targetDefPos = allDefs.filter((d) => d.groupKey === PotentialDimensionGroupEnums.StaffWorkingOutsideForeignInvestment)?.[0];
    if (!targetDefPos) {
      return 1;
    }
    const { activeDimensionType } = targetDefPos;
    if (activeDimensionType.length > 1) {
      return 3;
    }
    return 1;
  }
}
