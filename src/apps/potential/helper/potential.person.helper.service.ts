import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cacheable } from '@type-cacheable/core';
import { EnterpriseLibService } from 'apps/data/source/enterprise.lib.service';
import { PersonEntity } from 'libs/entities/PersonEntity';
import { Repository } from 'typeorm';
import * as Bluebird from 'bluebird';
import { PersonData } from 'libs/model/data/source/PersonData';
import { cloneDeep, flatMap, uniq } from 'lodash';
import { ExcludeCompanyService } from 'apps/exclude_company/exclude-company.service';

@Injectable()
export class PotentialPersonHelperService {
  constructor(
    @InjectRepository(PersonEntity) readonly personRepo: Repository<PersonEntity>,
    private readonly enterpriseLibService: EnterpriseLibService,
    private readonly excludeCompanyService: ExcludeCompanyService,
  ) {}

  /**
   * 获取公司相关人员列表
   * @param companyId
   * @param types
   * @param percentage
   */
  // @Cacheable({ ttlSeconds: 5 })
  async getCompanyRelatedPersonList(companyId: string, companyName: string, types: string[], percentage?: number): Promise<PersonData[]> {
    if (!types?.length || !companyId) {
      return null;
    }
    // 单独处理股东信息
    // 这里需要注意命中股东可能不满足股比设置的情况：
    // 一般来说，不满足股比的应该剔除，但是如果存在其他类型的命中情况，页面需要展示持股比例
    // 所以这里需要先获取所有的股东列表，然后再进行过滤
    let investMap = {};
    let investHitList = [];
    let investFlag = true;
    if (!types.includes('Invest')) {
      types.push('Invest');
      percentage = percentage || null;
      investFlag = false; //标记是否需要获取股东列表
    }
    const investList = await this.getInvestList(types, companyId, percentage);
    investMap = investList.reduce((acc, item) => {
      const key = `${item.name}-${item.keyNo}`;
      acc[key] = item.stockPercent;
      return acc;
    }, {});
    if (investFlag) {
      // 实际需要查询股东列表，过滤不满足股比的情况
      investHitList = investList.filter((x) => x['InvestHit']);
    }
    let allhit: PersonData[] = flatMap(
      await Bluebird.all([
        this.getLegalList(types, companyId),
        this.getEmployeeList(types, companyId),
        this.getHisLegalList(types, companyId),
        this.getHisEmployeeList(types, companyId),
        this.getHisInvestList(types, companyId, percentage),
        this.getActualControllerList(types, companyId),
        this.getFinalBenefitList(types, companyId),
      ]),
      (arr) => arr,
    );
    allhit.push(...investHitList);
    // 对 allhit 进行过滤
    const filteredCompanies = await this.excludeCompanyService.filterExcludedCompanyIds(allhit.map((x) => x.keyNo));
    allhit = allhit.filter((x) => filteredCompanies.includes(x.keyNo));
    const groupBy = allhit.reduce((acc, item) => {
      const key = `${item.name}-${item.keyNo}`;
      const stockPercent = investMap[key];
      if (stockPercent) {
        item['stockPercent'] = stockPercent;
      }
      if (!acc[key]) {
        acc[key] = item;
      } else {
        if (item.job?.length > 0) {
          acc[key].job = `${acc[key].job},${item.job}`;
        }
        if (!acc[key].tags) {
          acc[key].tags = [];
        }
        if (item.tags?.length > 0) {
          acc[key].tags.push(...item.tags);
        }
        acc[key].hitKey.push(...item.hitKey);
        acc[key].stockPercent = acc[key].stockPercent || item['stockPercent'];
      }
      return acc;
    }, {});
    const resultList = Object.values(groupBy);
    resultList.forEach((x) => {
      x['companyId'] = companyId;
      x['companyName'] = companyName;
      x['hitKey'] = [...new Set(x['hitKey'])];
      x['tags'] = [...new Set(x['tags'])];
      if (typeof x['job'] === 'string') {
        if (x['job'].includes(',')) {
          x['job'] = uniq(x['job'].split(',')).filter((y) => y);
        } else {
          x['job'] = [x['job']];
        }
      }
    });
    return resultList as PersonData[];
  }

  /**
   * 获取公司最终受益人列表
   * @param types
   * @param companyId
   */
  async getFinalBenefitList(types: string[], companyId: string): Promise<PersonData[]> {
    if (!types || !types.includes('FinalBenefit')) {
      return [];
    }
    const benefitList = await this.enterpriseLibService.getBenefitList(companyId);
    if (benefitList?.length > 0) {
      benefitList.forEach((x) => {
        x['hitKey'] = ['FinalBenefit'];
      });
    }
    return benefitList;
  }

  /**
   * 获取公司实际控制人列表
   * @param types
   * @param companyId
   */
  async getActualControllerList(types: string[], companyId: string): Promise<PersonData[]> {
    if (!types || !types.includes('ActualController')) {
      return [];
    }
    const response = await this.enterpriseLibService.getActualController(companyId);
    if (response) {
      return response.Names?.flatMap(
        (nameGroup) =>
          nameGroup.Names.PersonList?.map((person) =>
            Object.assign(new PersonData(), {
              name: person.Name,
              job: '实际控制人',
              keyNo: person.KeyNo,
              tags: person.Tags || [],
              history: false,
              ControlPercent: person.PercentTotal,
              hitKey: ['ActualController'],
            }),
          ) || [],
      );
    }
    return [];
  }

  /**
   * 获取公司历史股东列表
   * @param types
   * @param companyId
   * @param percentage
   */
  async getHisInvestList(types: string[], companyId: string, percentage?: number): Promise<PersonData[]> {
    if (!types || !types.includes('HisInvest')) {
      return [];
    }
    const historyPartnerList = await this.enterpriseLibService.getHistoryPartnerList(companyId);
    if (historyPartnerList?.length > 0) {
      const newHistoryPartnerList = cloneDeep(historyPartnerList);
      newHistoryPartnerList.forEach((x) => {
        const stockPercent = x.stockPercent;
        x['hitKey'] = ['HisInvest'];
        if (percentage) {
          x['InvestHit'] = Number(stockPercent) >= percentage;
        } else {
          x['InvestHit'] = true;
        }
        if (stockPercent) {
          x['stockPercent'] = stockPercent + '%';
        }
      });
      return newHistoryPartnerList.filter((x) => x['InvestHit']);
    }
    return [];
  }

  /**
   * 获取公司历史董监高列表
   * @param types
   * @param companyId
   */
  async getHisEmployeeList(types: string[], companyId: string): Promise<PersonData[]> {
    if (!types || !types.includes('HisEmploy')) {
      return [];
    }
    const hisEmployeeList = await this.enterpriseLibService.getHisEmployeeList(companyId);
    if (hisEmployeeList?.length > 0) {
      hisEmployeeList.forEach((x) => {
        x['hitKey'] = ['HisEmploy'];
      });
    }
    return hisEmployeeList;
  }

  /**
   * 获取公司历史法人列表
   * @param types
   * @param companyId
   */
  async getHisLegalList(types: string[], companyId: string): Promise<PersonData[]> {
    if (!types || !types.includes('HisLegal')) {
      return [];
    }
    const hisLegalList = await this.enterpriseLibService.getHisLegalPerson(companyId);
    if (hisLegalList?.length > 0) {
      hisLegalList.forEach((x) => {
        x['hitKey'] = ['HisLegal'];
      });
    }
    return hisLegalList;
  }

  /**
   * 获取公司股东列表
   * @param types
   * @param companyId
   * @param percentage
   */
  async getInvestList(types: string[], companyId: string, percentage?: number): Promise<PersonData[]> {
    if (!types || !types.includes('Invest')) {
      return [];
    }

    const investList = await this.enterpriseLibService.getPartnerList(companyId);
    if (investList?.length > 0) {
      const newInvestList = cloneDeep(investList);
      newInvestList.map((x) => {
        x['hitKey'] = ['Invest'];
        if (percentage) {
          const stockPercent = Number(x.stockPercent?.replace('%', ''));
          x['InvestHit'] = stockPercent >= percentage;
          // console.log(`stockPercent:${stockPercent} >= percentage:${percentage},result:${x['InvestHit']}`);
        } else {
          x['InvestHit'] = true;
        }
      });
      return newInvestList;
    }
    return [];
  }

  /**
   * 获取公司当前董监高列表
   * @param types
   * @param companyId
   */
  async getEmployeeList(types: string[], companyId: string): Promise<PersonData[]> {
    if (!types || !types.includes('Employ')) {
      return [];
    }
    const employeeList = await this.enterpriseLibService.getEmployeeList(companyId);
    if (employeeList?.length > 0) {
      employeeList.forEach((x) => {
        x['hitKey'] = ['Employ'];
      });
    }
    return employeeList;
  }

  /**
   * 获取公司法人列表
   * @param types
   * @param companyId
   */
  async getLegalList(types: string[], companyId: string): Promise<PersonData[]> {
    if (!types || !types.includes('Legal')) {
      return [];
    }
    const legalList = await this.enterpriseLibService.getLegalPerson(companyId);
    if (legalList?.length > 0) {
      legalList.forEach((x) => {
        x['hitKey'] = ['Legal'];
      });
    }
    return legalList;
  }

  /**
   * 获取需要排查的公司人员列表
   * @param personType
   * @param personGroups
   * @param orgId
   * @param identityInfoFilterType 身份信息筛选类型 1:需要身份信息 2:不需要身份信息 3:全部
   */
  @Cacheable({ ttlSeconds: 5 }) //核实人员后，缓存时间过长会导致数据更新不及时
  async getOrgPersonList(personType: number[], personGroups: any, orgId: number, identityInfoFilterType: number) {
    if (!personType) {
      // 没有选择人员类型，则不进行排查
      return null;
    }
    const queryBuilder = this.personRepo.createQueryBuilder('person');
    queryBuilder.leftJoinAndSelect('person.group', 'group');
    queryBuilder.andWhere('person.orgId = :orgId', { orgId });
    queryBuilder.andWhere('person.active = 1');

    if (identityInfoFilterType === 1) {
      // 需要身份信息相关 Code
      queryBuilder.andWhere('(person.cardId is not null or person.keyNo is not null or person.companyId is not null )');
    } else if (identityInfoFilterType === 2) {
      // 不需要身份信息相关 Code
      queryBuilder.andWhere('(person.cardId is null and person.keyNo is null )');
    }
    if (personType.length === 1) {
      if (personType.includes(1)) {
        // 只查员工
        queryBuilder.andWhere('person.relationPersonId = -1');
      } else {
        // 只查员工亲属
        queryBuilder.andWhere('person.relationPersonId != -1');
      }
    }
    if (personGroups?.value?.length) {
      queryBuilder.andWhere('person.groupId in (:...groupIds)', { groupIds: personGroups.value });
    }
    const personList = await queryBuilder.getMany();
    if (personList?.length > 0) {
      // 补充关联人员信息
      const relatedPersonIds = personList.map((x) => x.relationPersonId)?.filter((x) => x != -1);
      const relatedPersonList = await this.personRepo.findByIds(relatedPersonIds);
      personList.forEach((person) => {
        if (person.relationPersonId != -1) {
          const relationPerson = relatedPersonList?.filter((x) => x.id == person.relationPersonId)?.[0];
          if (relationPerson) {
            person['relationPersonName'] = relationPerson.name;
            person['relationPersonKeyNo'] = relationPerson.keyNo;
          }
        }
      });
    }
    return personList;
  }
}
