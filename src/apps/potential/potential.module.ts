import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SettingsModule } from '../settings/settings.module';
import { DataModule } from '../data/data.module';
import { PotentialController } from './potential.controller';
import { PotentialFacadeService } from './potential.facade.service';
import { PotentialHelperService } from './helper/potential.helper.service';
import { PotentialDiligenceEntity } from '../../libs/entities/PotentialDiligenceEntity';
import { CompanySearchModule } from '../company/company-search.module';
import { PersonModule } from '../person/person.module';
import { PersonEntity } from 'libs/entities/PersonEntity';
import { PotentialPersonHelperService } from './helper/potential.person.helper.service';
import { PersonOrgCompanyEntity } from 'libs/entities/PersonOrgCompanyEntity';
import { PackageUsageEntity } from '../../libs/entities/PackageUsageEntity';
import { ExcludeCompanyService } from 'apps/exclude_company/exclude-company.service';
import { ExcludeCompanyModule } from 'apps/exclude_company/exclude-company.module';
import { ExcludeCompanyEntity } from 'libs/entities/ExcludeCompanyEntity';

@Module({
  controllers: [PotentialController],
  providers: [PotentialFacadeService, PotentialHelperService, PotentialPersonHelperService, ExcludeCompanyService],
  exports: [PotentialFacadeService, PotentialHelperService, PotentialPersonHelperService],
  imports: [
    TypeOrmModule.forFeature([PotentialDiligenceEntity, PersonEntity, PersonOrgCompanyEntity, PackageUsageEntity, ExcludeCompanyEntity]),
    SettingsModule,
    DataModule,
    CompanySearchModule,
    PersonModule,
  ],
})
export class PotentialModule {}
