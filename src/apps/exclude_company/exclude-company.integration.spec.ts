import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { ExcludeCompanyService } from './exclude-company.service';
import { ExcludeCompanyEntity } from '../../libs/entities/ExcludeCompanyEntity';
import { generateUniqueTestIds } from '../test_utils_module/test.user';

describe('ExcludeCompany 模块集成测试', () => {
  let module: TestingModule;
  let service: ExcludeCompanyService;
  let repository: Repository<ExcludeCompanyEntity>;
  let testOrgId: number;
  let testUserId: number;

  beforeAll(async () => {
    [testOrgId, testUserId] = generateUniqueTestIds('exclude-company.integration.spec.ts');

    // 创建测试模块，使用内存数据库
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [ExcludeCompanyEntity],
          synchronize: true,
          logging: false,
        }),
        TypeOrmModule.forFeature([ExcludeCompanyEntity]),
      ],
      providers: [
        ExcludeCompanyService,
        {
          provide: RedisService,
          useValue: {
            getClient: jest.fn().mockReturnValue({
              get: jest.fn(),
              set: jest.fn(),
              del: jest.fn(),
            }),
          },
        },
      ],
    }).compile();

    service = module.get<ExcludeCompanyService>(ExcludeCompanyService);
    repository = module.get<Repository<ExcludeCompanyEntity>>('ExcludeCompanyEntityRepository');
  });

  afterAll(async () => {
    await module.close();
  });

  beforeEach(async () => {
    // 清理测试数据
    await repository.clear();
  });

  describe('数据库操作测试', () => {
    it('应该正确处理重复添加的情况', async () => {
      // Arrange
      const testCompanyId = `duplicate_company_${testOrgId}`;
      const testCompanyName = '重复公司';

      // Act - 第一次添加
      const firstAdd = await service.addExcludeCompany(testCompanyId, testCompanyName);

      // Act - 第二次添加相同公司
      const secondAdd = await service.addExcludeCompany(testCompanyId, testCompanyName);

      // Assert - 验证返回相同的实体
      expect(firstAdd.id).toBe(secondAdd.id);
      expect(firstAdd.companyId).toBe(secondAdd.companyId);

      // 验证数据库中只有一条记录
      const allCompanies = await repository.find();
      const duplicateCompanies = allCompanies.filter((c) => c.companyId === testCompanyId);
      expect(duplicateCompanies).toHaveLength(1);
    });

    it('应该正确处理删除不存在公司的情况', async () => {
      // Arrange
      const nonExistentCompanyId = `non_existent_${testOrgId}`;

      // Act
      const deleteResult = await service.removeExcludeCompany(nonExistentCompanyId);

      // Assert
      expect(deleteResult).toBe(false);
    });
  });
});
