import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { ExcludeCompanyService } from './exclude-company.service';
import { ExcludeCompanyEntity } from '../../libs/entities/ExcludeCompanyEntity';

describe('ExcludeCompanyService 单元测试', () => {
  let service: ExcludeCompanyService;
  let mockRepository: jest.Mocked<Repository<ExcludeCompanyEntity>>;
  let mockRedisService: jest.Mocked<RedisService>;

  const mockExcludeCompanies: ExcludeCompanyEntity[] = [
    {
      id: 1,
      companyId: 'company1',
      companyName: '测试公司1',
      createDate: new Date(),
      updateDate: new Date(),
    },
    {
      id: 2,
      companyId: 'company2',
      companyName: '测试公司2',
      createDate: new Date(),
      updateDate: new Date(),
    },
  ];

  beforeEach(async () => {
    const mockRepositoryMethods = {
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };

    const mockRedisServiceMethods = {
      getClient: jest.fn().mockReturnValue({}),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExcludeCompanyService,
        {
          provide: getRepositoryToken(ExcludeCompanyEntity),
          useValue: mockRepositoryMethods,
        },
        {
          provide: RedisService,
          useValue: mockRedisServiceMethods,
        },
      ],
    }).compile();

    service = module.get<ExcludeCompanyService>(ExcludeCompanyService);
    mockRepository = module.get(getRepositoryToken(ExcludeCompanyEntity));
    mockRedisService = module.get(RedisService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getExcludeCompanyList', () => {
    it('应该成功返回排除公司列表', async () => {
      // Arrange
      mockRepository.find.mockResolvedValue(mockExcludeCompanies);

      // Act
      const result = await service.getExcludeCompanyList();

      // Assert
      expect(result).toEqual(mockExcludeCompanies);
      expect(mockRepository.find).toHaveBeenCalledWith({
        order: {
          createDate: 'DESC',
        },
      });
    });

    it('当数据库查询失败时应该抛出错误', async () => {
      // Arrange
      const mockError = new Error('数据库连接失败');
      mockRepository.find.mockRejectedValue(mockError);

      // Act & Assert
      await expect(service.getExcludeCompanyList()).rejects.toThrow(mockError);
    });
  });

  describe('getExcludeCompanyIds', () => {
    it('应该返回排除公司ID集合', async () => {
      // Arrange
      jest.spyOn(service, 'getExcludeCompanyList').mockResolvedValue(mockExcludeCompanies);

      // Act
      const result = await service.getExcludeCompanyIds();

      // Assert
      expect(result).toBeInstanceOf(Set);
      expect(result.has('company1')).toBe(true);
      expect(result.has('company2')).toBe(true);
      expect(result.size).toBe(2);
    });
  });

  describe('isCompanyExcluded', () => {
    it('当公司在排除列表中时应该返回true', async () => {
      // Arrange
      const mockExcludeIds = new Set(['company1', 'company2']);
      jest.spyOn(service, 'getExcludeCompanyIds').mockResolvedValue(mockExcludeIds);

      // Act
      const result = await service.isCompanyExcluded('company1');

      // Assert
      expect(result).toBe(true);
    });

    it('当公司不在排除列表中时应该返回false', async () => {
      // Arrange
      const mockExcludeIds = new Set(['company1', 'company2']);
      jest.spyOn(service, 'getExcludeCompanyIds').mockResolvedValue(mockExcludeIds);

      // Act
      const result = await service.isCompanyExcluded('company3');

      // Assert
      expect(result).toBe(false);
    });

    it('当查询失败时应该返回false', async () => {
      // Arrange
      jest.spyOn(service, 'getExcludeCompanyIds').mockRejectedValue(new Error('查询失败'));

      // Act
      const result = await service.isCompanyExcluded('company1');

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('addExcludeCompany', () => {
    it('应该成功添加新的排除公司', async () => {
      // Arrange
      const companyId = 'newCompany';
      const companyName = '新公司';
      const mockCreatedEntity = { id: 3, companyId, companyName, createDate: new Date(), updateDate: new Date() };

      mockRepository.findOne.mockResolvedValue(null);
      mockRepository.create.mockReturnValue(mockCreatedEntity);
      mockRepository.save.mockResolvedValue(mockCreatedEntity);

      // Act
      const result = await service.addExcludeCompany(companyId, companyName);

      // Assert
      expect(result).toEqual(mockCreatedEntity);
      expect(mockRepository.findOne).toHaveBeenCalledWith({ where: { companyId } });
      expect(mockRepository.create).toHaveBeenCalledWith({ companyId, companyName });
      expect(mockRepository.save).toHaveBeenCalledWith(mockCreatedEntity);
    });

    it('当公司已存在时应该返回现有记录', async () => {
      // Arrange
      const companyId = 'existingCompany';
      const companyName = '现有公司';
      const existingEntity = mockExcludeCompanies[0];

      mockRepository.findOne.mockResolvedValue(existingEntity);

      // Act
      const result = await service.addExcludeCompany(companyId, companyName);

      // Assert
      expect(result).toEqual(existingEntity);
      expect(mockRepository.create).not.toHaveBeenCalled();
      expect(mockRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('removeExcludeCompany', () => {
    it('应该成功删除排除公司', async () => {
      // Arrange
      const companyId = 'company1';
      mockRepository.delete.mockResolvedValue({ affected: 1, raw: {} });

      // Act
      const result = await service.removeExcludeCompany(companyId);

      // Assert
      expect(result).toBe(true);
      expect(mockRepository.delete).toHaveBeenCalledWith({ companyId });
    });

    it('当公司不存在时应该返回false', async () => {
      // Arrange
      const companyId = 'nonExistentCompany';
      mockRepository.delete.mockResolvedValue({ affected: 0, raw: {} });

      // Act
      const result = await service.removeExcludeCompany(companyId);

      // Assert
      expect(result).toBe(false);
    });
  });
});
