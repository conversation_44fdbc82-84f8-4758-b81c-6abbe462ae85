import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExcludeCompanyEntity } from '../../libs/entities/ExcludeCompanyEntity';
import { ExcludeCompanyService } from './exclude-company.service';

@Module({
  providers: [ExcludeCompanyService],
  imports: [TypeOrmModule.forFeature([ExcludeCompanyEntity])],
  exports: [ExcludeCompanyService],
})
export class ExcludeCompanyModule {}
