# 准入排查过滤关联主体公司列表

## 1.涉及到的维度

```typescript
DimensionLevel3Enums.MainMembersPersonCreditCurrent:主要人员及核心关联方被列入失信被执行人
DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent:主要人员及核心关联方被列入限制高消费
DimensionLevel3Enums.MainMembersRestrictedOutbound:主要人员及核心关联方被列入限制出境
DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence:公司及主要人员、核心关联方涉刑事犯罪
DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory:公司及主要人员、核心关联方涉刑事犯罪（3年以上及其他）
DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve:近3年涉贪污受贿裁判相关提及方
DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory:涉贪污受贿裁判相关提及方（3年以上及其他）
```

**如上涉及到的维度，当相关方、关联方主体在 exclude_company 表中，则过滤掉这部分企业用剩余的关联方再做排查**

## 当关联主体在 exclude_company 表中

### exclude_company 表结构：

```mysql
create table exclude_company
(
    id           int auto_increment
        primary key,
    company_id   varchar(45)                        not null,
    company_name varchar(500)                       not null,
    create_date  datetime default CURRENT_TIMESTAMP null,
    update_date  datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
    constraint exclude_company_company_id_uindex
        unique (company_id)
)
    comment '第三方排查关联主体默认排查公司名单';
```

## 2.新建 exclude_company 模块

### 2.1 支持查询 exclude_company 列表，并将列表数据长期缓存到 Redis 中

### 2.2 添加一个方法，可支持后续对不在 exclude_company 列表中的公司，根据公司的 standardCode,label 来判断关联方是否保留
