import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { Cacheable, useIoRedisAdapter } from 'type-cacheable';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { ExcludeCompanyEntity } from '../../libs/entities/ExcludeCompanyEntity';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { ConfigService } from 'libs/config/config.service';
import { DimensionLevel3Enums } from 'libs/constants/dimension.base.constants';

/**
 * 第三方排查关联主体默认排查公司名单服务
 */
@Injectable()
export class ExcludeCompanyService {
  private readonly logger: Logger = QccLogger.getLogger(ExcludeCompanyService.name);

  constructor(
    @InjectRepository(ExcludeCompanyEntity)
    private readonly excludeCompanyRepo: Repository<ExcludeCompanyEntity>,
    private readonly redisService: RedisService,
    private readonly httpUtilsService: HttpUtilsService,
    private readonly configService: ConfigService,
  ) {
    //@ts-ignore
    useIoRedisAdapter(this.redisService.getClient());
  }

  /**
   * 获取排除公司列表（带缓存）
   * @returns 排除公司列表
   */
  @Cacheable({ ttlSeconds: 86400 }) // 缓存24小时
  public async getExcludeCompanyList(): Promise<ExcludeCompanyEntity[]> {
    try {
      return await this.excludeCompanyRepo.find({
        order: {
          createDate: 'DESC',
        },
      });
    } catch (error) {
      this.logger.error('获取排除公司列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取排除公司ID集合（带缓存）
   * @returns 排除公司ID集合
   */
  @Cacheable({ ttlSeconds: 86400 }) // 缓存24小时
  public async getExcludeCompanyIds(): Promise<Set<string>> {
    try {
      const excludeCompanies = await this.getExcludeCompanyList();
      return new Set(excludeCompanies.map((company) => company.companyId));
    } catch (error) {
      this.logger.error('获取排除公司ID集合失败:', error);
      throw error;
    }
  }

  /**
   * 检查公司是否在排除列表中
   * @param companyId 公司ID
   * @returns 是否在排除列表中
   */
  public async isCompanyExcluded(companyId: string): Promise<boolean> {
    try {
      const excludeIds = await this.getExcludeCompanyIds();
      return excludeIds.has(companyId);
    } catch (error) {
      this.logger.error(`检查公司是否排除失败, companyId: ${companyId}`, error);
      return false;
    }
  }

  /**
   * 根据公司ID列表过滤排除的公司
   * @param companyIds 公司ID列表
   * @param dimensionKey 维度类型
   * @returns 过滤后的公司ID列表
   */
  public async filterExcludedCompanyIds(companyIds: string[], dimensionKey?: string): Promise<string[]> {
    //只针对如下维度做排除过滤
    // - `DimensionLevel3Enums.MainMembersPersonCreditCurrent`: 主要人员及核心关联方被列入失信被执行人
    // - `DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent`: 主要人员及核心关联方被列入限制高消费
    // - `DimensionLevel3Enums.MainMembersRestrictedOutbound`: 主要人员及核心关联方被列入限制出境
    // - `DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence`: 公司及主要人员、核心关联方涉刑事犯罪
    // - `DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory`: 公司及主要人员、核心关联方涉刑事犯罪（3 年以上及其他）
    // - `DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve`: 近 3 年涉贪污受贿裁判相关提及方
    // - `DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory`: 涉贪污受贿裁判相关提及方（3 年以上及其他）
    if (
      !dimensionKey ||
      ![
        DimensionLevel3Enums.MainMembersPersonCreditCurrent,
        DimensionLevel3Enums.MainMembersRestrictedConsumptionCurrent,
        DimensionLevel3Enums.MainMembersRestrictedOutbound,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalOffence,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalOffenceHistory,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolve,
        DimensionLevel3Enums.CompanyOrMainMembersCriminalInvolveHistory,
      ].includes(dimensionKey as DimensionLevel3Enums)
    ) {
      return companyIds;
    }
    try {
      if (!companyIds || companyIds.length === 0) {
        return companyIds;
      }
      this.logger.info(`过滤排除公司ID列表: ${companyIds}`);
      const resultCompanyIds: string[] = await this.httpUtilsService.postRequest(this.configService.roverGraphServer.filterCompany, { companyIds });
      return resultCompanyIds;
    } catch (error) {
      this.logger.error('过滤排除公司ID列表失败:', error);
      // 发生错误时返回原列表，避免影响业务流程
      return companyIds;
    }
  }

  /**
   * 添加排除公司
   * @param companyId 公司ID
   * @param companyName 公司名称
   * @returns 创建的排除公司实体
   */
  public async addExcludeCompany(companyId: string, companyName: string): Promise<ExcludeCompanyEntity> {
    try {
      // 检查是否已存在
      const existing = await this.excludeCompanyRepo.findOne({
        where: { companyId },
      });

      if (existing) {
        this.logger.warn(`排除公司已存在: ${companyId}`);
        return existing;
      }

      const excludeCompany = this.excludeCompanyRepo.create({
        companyId,
        companyName,
      });

      const result = await this.excludeCompanyRepo.save(excludeCompany);
      this.logger.info(`添加排除公司成功: ${companyId} - ${companyName}`);

      return result;
    } catch (error) {
      this.logger.error(`添加排除公司失败: ${companyId} - ${companyName}`, error);
      throw error;
    }
  }

  /**
   * 删除排除公司
   * @param companyId 公司ID
   * @returns 是否删除成功
   */
  public async removeExcludeCompany(companyId: string): Promise<boolean> {
    try {
      const result = await this.excludeCompanyRepo.delete({ companyId });
      const success = result.affected > 0;

      if (success) {
        this.logger.info(`删除排除公司成功: ${companyId}`);
      } else {
        this.logger.warn(`排除公司不存在: ${companyId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`删除排除公司失败: ${companyId}`, error);
      throw error;
    }
  }
}
