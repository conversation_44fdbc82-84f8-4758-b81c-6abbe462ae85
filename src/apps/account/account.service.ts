/* eslint-disable @typescript-eslint/naming-convention */
import { BadRequestException, Injectable } from '@nestjs/common';
import { ChangeCurrentOrg, GetOrgsModel, UpdatePasswordRequest, UpdateSearchModel } from 'libs//model/account/request';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { CACHE_KEY_PASSWORD_VERIFICATION_CODE, CACHE_KEY_PASSWORD_VERIFICATION_CODE_LOCK, RANDOM_MAX, RANDOM_MIN } from 'libs//model/account/constant';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { QccService } from './qcc.service';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { NewTrialApplyForRover } from 'libs//model/account/NewTrialApplyForRover';
import { pick } from 'lodash';
import { TrailService } from './trial.service';
import { SmsService } from '@kezhaozhao/qcc-utils';

@Injectable()
export class AccountService {
  private readonly logger: Logger = QccLogger.getLogger(AccountService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly qccService: QccService,
    private readonly httpUtils: HttpUtilsService,
    private readonly trailService: TrailService,
    private readonly smsService: SmsService,
  ) {
    //useAdapter(redisService.getClient());
  }

  /**
   * 根据用户签名获取有效的 sessionId
   * @param phone
   * @param email
   * @param accessKey
   * @param timestamp
   * @param sign
   */
  async loginByAccessKey(phone: string, email: string, accessKey: string, timestamp: number, sign: string, ip, userAgent) {
    return this.httpUtils.postRequest(this.configService.enterpriseServer.loginRedirect, {
      phone,
      email,
      accessKey,
      timestamp,
      sign,
      ip,
      userAgent,
    });
  }

  /**
   * 通过token获取sessionId
   * @param data
   * @returns
   */
  async loginByToken(accessToken: string) {
    return this.httpUtils.postRequest(this.configService.enterpriseServer.loginByToken, { accessToken });
  }

  /**
   * 获取用户企业列表
   * @param data
   * @returns
   */
  async getUserOrgList(data: GetOrgsModel) {
    return this.httpUtils.postRequest(this.configService.enterpriseServer.getUserOrgList, data);
  }

  async changeCurrentOrg(data: ChangeCurrentOrg) {
    return this.httpUtils.postRequest(this.configService.enterpriseServer.changeCurrentOrg, data);
  }

  /**
   * 获取发版日志列表
   * @param data
   * @returns
   */
  async searchReleaseLog(data: UpdateSearchModel) {
    return this.httpUtils.postRequest(this.configService.enterpriseServer.searchUpdateLog, data);
  }

  /**
   * 发送修改密码验证码
   * @param phone
   */
  public async sendUpdatePasswordVerifyCode(phone: string) {
    // 验证码键
    const verificationCodeKey = `${CACHE_KEY_PASSWORD_VERIFICATION_CODE}:${phone}`;
    // 验证码锁键: (1分钟内只能发一次请求)
    const verificationLockKey = `${CACHE_KEY_PASSWORD_VERIFICATION_CODE_LOCK}:${phone}`;

    // 如果有锁, 抛出异常: 60秒内只允许发一次验证码
    const redis = this.redisService.getClient();
    const verificationLock = await redis.get(verificationLockKey);
    if (verificationLock == '1') {
      throw new BadRequestException(RoverExceptions.Common.SMS.Duplicated);
    }

    // 每次都发送新的短信验证码
    const verificationCode = String(Math.floor(Math.random() * (RANDOM_MAX - RANDOM_MIN + 1) + RANDOM_MIN));
    // 设置新 key 和有效时间
    await redis.set(verificationCodeKey, verificationCode, 'EX', 60 * 10); // 3分钟有效

    try {
      this.logger.log(`send verificationCode ${verificationCode} to ${phone}`);
      const { body } = await this.smsService.singleSend(phone, 'SMS_260950018', `#code#=${verificationCode}`, '企查查');

      // this.logger.debug(data);
      // if (data.code !== 0) {
      //   throw new Error(data.msg);
      // }
      // 设置重发锁
      await redis.set(verificationLockKey, '1', 'EX', 60);

      return {
        code: body.code,
        phone: phone,
        sid: body.bizId,
        requestId: body.requestId,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(RoverExceptions.Common.SMS.Error);
    }
  }

  /**
   * 修改密码
   * @param phone
   * @param postBody
   */
  public async updatePassword(phone: string, postBody: UpdatePasswordRequest) {
    const { verifyCode, newPassword, confirmPassword } = postBody;

    const redis = this.redisService.getClient();
    const verificationCodeKey = `${CACHE_KEY_PASSWORD_VERIFICATION_CODE}:${phone}`;
    const verificationCode = await redis.get(verificationCodeKey);

    // 验证码不正确
    if (verificationCode !== verifyCode) {
      throw new BadRequestException(RoverExceptions.UserRelated.User.VerificationCodeError);
    }

    // 密码不一致
    if (newPassword !== confirmPassword) {
      throw new BadRequestException(RoverExceptions.UserRelated.User.PasswordNotMatch);
    }
    await this.qccService.changePassword(phone, newPassword);
    // const strength = passwordStrength(newPassword);
    // // 修改当前密码强度
    // await this.userRepository.update(userId, {
    //   passwordStrength: strength.id
    // });
    return { affected: 1 };
  }

  /**
   * 调用企查查接口查询对应名称
   * @param searchKey
   * @param includeFields
   * @param pageSize
   * @param pageIndex
   * @returns
   */
  public async searchName(searchKey: string, includeFields = ['KeyNo', 'Name', 'OperName'], pageSize = 10, pageIndex = 1) {
    try {
      const { Paging, Result } = await this.httpUtils.postRequest(this.configService.dataServer.searchAdvance, {
        searchKey,
        searchIndex: 'name',
        pageSize,
        pageIndex,
      });
      const newResult = Result?.map((company) => {
        return pick(company, includeFields);
      });
      return { Paging, Result: newResult };
    } catch (e) {
      this.logger.error(`http GET ${this.configService.dataServer.searchAdvance} err:`, e);
      return '';
    }
  }

  /**
   * 检查手机验证码是否正确
   * @param phone
   * @param code
   * @param prefix
   */
  public async codeVerification(phone: string, code: string, prefix = 'qccRover:trial') {
    const redis = await this.redisService.getClient();
    const verificationCodeKey = `${prefix}:verification_code:${phone}`;
    const verificationCode = await redis.get(verificationCodeKey);
    if (verificationCode !== code) {
      throw new BadRequestException(RoverExceptions.UserRelated.User.VerificationCodeError);
    }
  }

  public async trailApply(postData: NewTrialApplyForRover) {
    // 未登录用户
    if (!postData.code) {
      throw new BadRequestException(RoverExceptions.UserRelated.User.VerificationCodeError);
    } else {
      await this.codeVerification(postData.phone, postData.code);
    }
    // TODO 验证手机号是否已开通rover
    await this.trailService.applyRover(postData);

    return {
      status: '200',
    };
  }

  public async trailApplyTest(postData: NewTrialApplyForRover) {
    // 未登录用户
    // if (!postData.code) {
    //   throw new BadRequestException(RoverExceptions.UserRelated.User.VerificationCodeError);
    // } else {
    //   await this.codeVerification(postData.phone, postData.code);
    // }
    // TODO 验证手机号是否已开通rover
    await this.trailService.applyRover(postData);

    return {
      status: '200',
    };
  }

  /**
   * 发送手机验证码
   * @param phone
   * @param prefix
   * @param type
   */
  public async sendVerificationCodeForTrail(phone: string, prefix = 'qccRover:trial') {
    // 验证码键
    const verificationCodeKey = `${prefix}:verification_code:${phone}`;
    // 验证码锁键: (1分钟内只能发一次请求)
    const verificationLockKey = `${prefix}:verification_code_lock:${phone}`;

    const redis = await this.redisService.getClient();
    // 先从 redis 中取出:
    const verificationLock = await redis.get(verificationLockKey);

    // NOTE: 有锁抛错
    const isLocked = verificationLock === '1';
    if (isLocked) {
      // NOTE:  如果有锁, 抛出异常: 60秒内只允许发一次验证码
      throw new BadRequestException(RoverExceptions.Common.SMS.Duplicated);
    }

    // 每次都发送新的短信验证码
    const verificationCode = String(Math.floor(Math.random() * (RANDOM_MAX - RANDOM_MIN + 1) + RANDOM_MIN));
    // 设置新 key 和有效时间
    await redis.set(verificationCodeKey, verificationCode, 'EX', 60 * 10);

    // NOTE: 将随机验证码通过短信的形式发给用户
    try {
      const qccCodeTplId = 'SMS_257672883';
      const data = this.httpUtils.postRequest(this.configService.enterpriseServer.sendQccCode, {
        phone,
        tplId: qccCodeTplId,
        tplValue: `#code#=${verificationCode}`,
      });

      this.logger.info(`send verificationCode ${verificationCode} to ${phone}`);
      this.logger.debug(data);
      // if (data.code !== 0) {
      //   throw new Error(data.msg);
      // }
      // 设置重发锁
      await redis.set(verificationLockKey, '1', 'EX', 60);

      return data;
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(RoverExceptions.Common.SMS.Error);
    }
  }
}
