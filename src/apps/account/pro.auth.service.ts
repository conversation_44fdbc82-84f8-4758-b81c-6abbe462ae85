import { Injectable } from '@nestjs/common';
import { <PERSON>gger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ConfigService } from 'libs/config/config.service';
import * as crypto from 'crypto';
import * as buffer from 'buffer';
import { RoverUser } from 'libs/model/common';

@Injectable()
export class ProAuthService {
  private readonly logger: Logger = QccLogger.getLogger(ProAuthService.name);
  private key: Buffer = buffer.Buffer.from('jIMSui6A6zsTu2m00qHGEQ==', 'base64'); //AES加密密钥
  private iv: string = this.configService.server.qccPro.secretKey.slice(0, 16).toLowerCase(); //AES加密偏移向量

  constructor(private readonly configService: ConfigService) {}

  //加密
  async encrypt(plaintext) {
    const cipher = crypto.createCipheriv('aes-128-cbc', this.key, this.iv);
    cipher.setAutoPadding(true);
    let ciph = cipher.update(plaintext, 'utf8', 'base64');
    ciph += cipher.final('base64');
    return ciph;
  }

  //解密
  async decrypt(encryptText) {
    const decipher = crypto.createDecipheriv('aes-128-cbc', this.key, this.iv);
    decipher.setAutoPadding(true);
    let txt = decipher.update(encryptText, 'base64', 'utf8');
    txt += decipher.final('utf8');
    return txt;
  }

  /**
   * 获取Token
   * @param getTokenBody
   */
  public async getProToken(currentUser: RoverUser) {
    const { currentOrg } = currentUser;
    //各个字段的传值请参考说明文档，timespan单位秒

    const loginName = `${this.configService.server.qccPro}_${this.configService.nodeEnv}_${currentOrg}`;

    const text = {
      loginName,
      name: '',
      timespan: Math.ceil(new Date().getTime() / 1000),
      role: 'staff',
      email: '',
    };
    const entryContent = JSON.stringify(text);
    const encryptText = await this.encrypt(entryContent);
    return { timespan: text.timespan, key: this.configService.server.qccPro.key, token: encryptText };
  }
}
