import { Modu<PERSON> } from '@nestjs/common';
import { AccountController } from './account.controller';
import { QccService } from './qcc.service';
import { AccountService } from './account.service';
import { AfsVerifyService } from './afs-verify.service';
import { ProAuthService } from '../account/pro.auth.service';
import { TrailService } from './trial.service';
import { UserEntity } from 'libs/entities/UserEntity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  controllers: [AccountController],
  providers: [QccService, AccountService, AfsVerifyService, ProAuthService, TrailService],
  imports: [TypeOrmModule.forFeature([UserEntity])],
})
export class AccountModule {}
