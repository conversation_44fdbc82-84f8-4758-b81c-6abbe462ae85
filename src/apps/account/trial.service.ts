import { BadRequestException, ForbiddenException, HttpException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { find, isEmpty, pick } from 'lodash';
import { ConfigService } from 'libs/config/config.service';
import { NewTrialApplyForRover } from 'libs/model/account/NewTrialApplyForRover';
import { LEADS_OBJECT_FIELDS } from 'libs/constants/trial.constants';
import { InjectRepository } from '@nestjs/typeorm';
import { UserEntity } from 'libs/entities/UserEntity';
import { Repository } from 'typeorm';
import { Product } from '@kezhaozhao/saas-bundle-service';
import * as Bluebird from 'bluebird';

@Injectable()
export class TrailService {
  private readonly logger: Logger = QccLogger.getLogger(TrailService.name);
  private leadsObjectFields: any;
  private leadsObjectId: number;

  constructor(
    private readonly configService: ConfigService,
    @InjectRepository(UserEntity) private readonly userRepository: Repository<UserEntity>,
    private readonly httpService: HttpService,
  ) {}

  /**
   * 申请试用
   * @param postData
   */
  public async applyRover(postData: NewTrialApplyForRover) {
    try {
      const user = await this.userRepository.findOne({ phone: postData.phone, active: 1 });
      if (user) {
        const userBundle = await this.httpService.axiosRef
          .post(`${this.configService.kzzServer.bundleService}/search/user`, {
            userId: user.userId,
            orgId: user.orgId,
            productCode: Product.Rover,
            all: 1,
          })
          .then((resp) => resp.data);
        if (!isEmpty(userBundle)) {
          // 如果有 rover套餐，可能已过期
          throw new BadRequestException(RoverExceptions.UserRelated.Trial.BundleExist);
        }
      }
      const exist = await this.searchLeads(postData.phone);
      if (exist) {
        throw new BadRequestException(RoverExceptions.UserRelated.Trial.ApplyDuplicated);
      }
      await this.addLeads(postData);
      return {
        status: '200',
      };
    } catch (err) {
      if (err && err.response) {
        if (err instanceof HttpException) {
          throw err;
        } else if (err.response.data) {
          this.logger.error(`rover trial apply failed: ${JSON.stringify(err.response.data)}`);
        } else {
          this.logger.error(`rover trial apply failed: ${JSON.stringify(err.response)}`);
        }
      } else {
        this.logger.error(`rover trial apply failed: ${err.message}`);
      }
      throw new InternalServerErrorException({ message: err.response?.data?.message[0], ...RoverExceptions.UserRelated.Trial.ApplyFailed });
    }
  }

  private getParams() {
    let userId, orgId, sourceId;
    let tagIds: number[] = [];
    if (['local', 'dev'].includes(this.configService.nodeEnv)) {
      //开发环境忽略
      throw new ForbiddenException(RoverExceptions.UserRelated.Trial.DevFailed);
    } else if (['test'].includes(this.configService.nodeEnv)) {
      //测试环境
      orgId = 10280;
      userId = 6862; // 负责人: 6862 张文琴
      sourceId = 2498; // 线索来源： 2493 平台申请 / 2498 线上自助
      tagIds = [961]; // 线索分组: 第三方风险排查 961
    } else if (['release', 'prod'].includes(this.configService.nodeEnv)) {
      //线上
      orgId = 87496;
      userId = 239729; // 负责人 91349 汪允  / 225979 王传奇   91331 郭媛  /   225979  孙冰洁  / 239729  系统内置用户
      sourceId = 158775; // 线索来源:  158770 平台申请 / 158775 线上自助
      tagIds = [1102]; // 线索分组  Rover
    }
    return { orgId, userId, sourceId, tagIds };
  }

  /**
   * 申请试用推送至CRM
   * @param postData
   * @private
   */
  private async addLeads(postData: NewTrialApplyForRover) {
    const { userId, orgId, sourceId, tagIds } = this.getParams();
    // 目前只有一种 来源渠道   第三方风险WEB-申请试用,  第三方风险H5-申请试用
    // postData.channel = '第三方风险WEB-申请试用';

    const leads: any = {
      category: 1, // 个人线索
      refCompanyId: postData?.refCompanyId, // 关联工商id
      name: `${postData.companyName}-${postData.name}-${postData.phone}`, // 线索名称
      status: 16, // 线索状态：16-待处理；18-跟进中；19-无效
      phoneName: postData.name, // 联系人姓名
      phone: postData.phone, // 联系电话
      province: '', // 所属地区-省
      city: '', // 所属地区-市
      district: '', // 所属地区-区
      note: postData.remarks || '', // 备注
      organizationId: orgId,
      leaderIds: [userId],
      collaboratorIds: [],
      tags: Array.isArray(postData.tagIds) ? postData.tagIds : tagIds,
      source: sourceId,
    };

    const objectId = await this.getLeadsObjectId(userId); // 174
    const objectFields = await this.getLeadsObjectFields(objectId, userId);
    const objectValues = {};
    // 处理自定义字段
    if (postData?.channel) {
      // 来源渠道
      objectValues[objectFields[LEADS_OBJECT_FIELDS.CHANNEL]] = [postData.channel];
    }
    if (postData?.tracking) {
      objectValues[objectFields[LEADS_OBJECT_FIELDS.TRACKING]] = postData.tracking;
    }
    if (!isEmpty(objectValues)) {
      leads.customObjectValues = [
        {
          objectId: objectId,
          values: objectValues,
        },
      ];
    }
    const resp = await this.httpService.axiosRef
      .request({
        method: 'POST',
        url: this.configService.kzzCrmServer.leads,
        headers: {
          uid: userId,
        },
        data: leads,
      })
      .then((resp) => resp.data);
    let leadsId;
    if (resp.code == 200 && resp.data) {
      leadsId = resp.data.customerId;
      this.logger.info(`leads=${leadsId} is assigned to user=${userId}, phone=${postData.phone} success`);
    } else {
      this.logger.error(`leads=${postData.companyName} is assigned to user=${userId}, phone=${postData.phone} failed: ${JSON.stringify(resp)}`);
    }
    return leadsId;
  }

  /**
   * 根据手机号查找是否已提交试用申请
   * @param phone
   */
  private async searchLeads(phone: string) {
    const { userId, sourceId, tagIds } = this.getParams();

    const data = {
      sourceIds: [sourceId],
      tagIds,
      fulltext: phone,
      pageSize: 1,
      pageIndex: 1,
    };
    const leadsResp = await this.httpService.axiosRef
      .request({
        method: 'POST',
        url: this.configService.kzzCrmServer.leadsSearch,
        headers: {
          uid: userId,
        },
        data,
      })
      .then((resp) => resp.data)
      .catch((err) => {
        this.logger.info('leadsSearch error ');
        this.logger.error(err);
      });

    if (leadsResp?.data?.length > 0) {
      return leadsResp.data[0];
    }
    return null;
  }

  /**
   * 线索搜索
   * @param companyName
   * @private
   */
  private async searchLeadsByCompany(companyName: string) {
    const { userId } = this.getParams();

    const data = {
      fulltext: companyName,
      pageSize: 1,
      pageIndex: 1,
    };
    const leadsResp = await this.httpService.axiosRef
      .request({
        method: 'POST',
        url: this.configService.kzzCrmServer.leadsSearch,
        headers: {
          uid: userId,
        },
        data,
      })
      .then((resp) => resp.data)
      .catch((err) => {
        this.logger.info('leadsSearch error ');
        this.logger.error(err);
      });

    if (leadsResp?.data?.length > 0) {
      return leadsResp.data[0];
    }
    return null;
  }

  /**
   * 获取客户经理信息
   * @param companyName
   */
  public async getOrgCustomerInfo(companyName: string) {
    const [res1, res2] = await Bluebird.all([this.searchCustomer(companyName), this.searchLeadsByCompany(companyName)]);
    if (res1) {
      return pick(
        res1.userCustomers.find((f) => f.roleId == 4 && (f.groupId === 4 || f.groupId === 1002)),
        ['name', 'phone', 'email'],
      );
    } else {
      if (res2) {
        return pick(
          res2.userCustomers.find((f) => f.roleId == 4 && (f.groupId === 4 || f.groupId === 1002)),
          ['name', 'phone', 'email'],
        );
      }
      return {};
    }
  }

  /**
   * CRM 客户搜索
   * @param companyName
   * @private
   */
  private async searchCustomer(companyName: string) {
    const { userId } = this.getParams();

    const data = {
      dataScope: 1,
      fulltext: companyName,
      pageSize: 1,
      pageIndex: 1,
    };
    const customerResp = await this.httpService.axiosRef
      .request({
        method: 'POST',
        url: this.configService.kzzCrmServer.customerSearch,
        headers: {
          uid: userId,
          'Content-Type': 'application/json',
        },
        data,
      })
      .then((resp) => resp.data)
      .catch((err) => {
        this.logger.info('customerSearch error ');
        this.logger.error(err);
      });

    if (customerResp?.data?.length > 0) {
      return customerResp.data[0];
    }
    return null;
  }

  /**
   * 获取线索对象id
   * @param userId
   */
  private async getLeadsObjectId(userId: number) {
    if (!this.leadsObjectId) {
      const resp = await this.httpService.axiosRef
        .request({
          method: 'POST',
          url: `${this.configService.kzzCrmServer.dynamicObjects}/search`,
          headers: {
            uid: userId + '',
          },
          data: {
            pageIndex: 1,
            pageSize: 100,
            type: 1,
          },
        })
        .then((resp) => resp.data);
      if (resp.code == 200 && resp.data) {
        const obj = find(resp.data, { objectName: '线索' });
        if (obj) {
          this.leadsObjectId = obj.objectId;
        }
      }
      if (!this.leadsObjectId) {
        throw new InternalServerErrorException(RoverExceptions.UserRelated.Trial.LeadsDynamicObjectNotFound);
      }
    }
    return this.leadsObjectId;
  }

  /**
   * 获取线索的自定义字段
   * @param objectId
   * @param userId
   */
  private async getLeadsObjectFields(objectId: number, userId: number) {
    if (!this.leadsObjectFields) {
      const fields = {};
      const resp = await this.httpService.axiosRef
        .request({
          method: 'GET',
          url: `${this.configService.kzzCrmServer.dynamicObjects}/${objectId}/fields`,
          headers: {
            uid: userId + '',
          },
        })
        .then((resp) => resp.data);

      if (resp.code == 200 && resp.data) {
        for (const field of resp.data) {
          if (field.fieldKey === 'CFLD202109240002') {
            // CFLD202109240002  使用方向,测试与线上fieldKey一致
            fields[LEADS_OBJECT_FIELDS.USAGE] = field.fieldKey;
          } else if (field.fieldKey === 'CFLD202207270002') {
            // CFLD202207270002 来源渠道
            fields[LEADS_OBJECT_FIELDS.CHANNEL] = field.fieldKey;
          } else if (field.fieldKey === 'CFLD202109240007') {
            // CFLD202109240007 需求类型
            fields[LEADS_OBJECT_FIELDS.REQUIREMENTS] = field.fieldKey;
          } else if (field.fieldKey === 'CFLD202109240009') {
            // CFLD202109240009 关注点
            fields[LEADS_OBJECT_FIELDS.FOCUS_ON] = field.fieldKey;
          } else if (field.fieldKey === 'CFLD202210090002') {
            //CFLD202210090002 关键词
            fields[LEADS_OBJECT_FIELDS.TRACKING] = field.fieldKey;
          }
        }
      }
      if (
        !fields[LEADS_OBJECT_FIELDS.USAGE] ||
        !fields[LEADS_OBJECT_FIELDS.CHANNEL] ||
        !fields[LEADS_OBJECT_FIELDS.REQUIREMENTS] ||
        !fields[LEADS_OBJECT_FIELDS.FOCUS_ON] ||
        !fields[LEADS_OBJECT_FIELDS.TRACKING]
      ) {
        throw new InternalServerErrorException(RoverExceptions.UserRelated.Trial.LeadsDynamicObjectFieldNotFound);
      }
      this.leadsObjectFields = fields;
    }
    return this.leadsObjectFields;
  }

  /**
   * 申请试用专属客户
   * @param user
   * @param employeeId
   */
  public async serviceStaff(employeeId: string) {
    let result = null;
    try {
      result = await this.httpService.axiosRef
        .post(`${this.configService.server.saasService}/extra/getBusinessInfoByEmployeeNo`, {
          employeeNo: employeeId,
        })
        .then((resp) => resp.data);
      return result;
    } catch (err) {
      result = {
        name: '周青',
        phone: '***********',
        email: '<EMAIL>',
      };
    }
    return result;
  }
}
