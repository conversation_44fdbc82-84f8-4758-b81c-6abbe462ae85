import { BadRequestException, HttpException, Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import * as crypto from 'crypto';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from 'libs/config/config.service';

@Injectable()
export class QccService {
  private readonly logger: Logger = QccLogger.getLogger(QccService.name);

  constructor(private readonly httpService: HttpService, private readonly configService: ConfigService) {}

  /**
   * 修改用户密码
   * @param phone
   * @param newPassword
   */
  public async changePassword(phone: string, newPassword: string) {
    try {
      let hashPassword = this.md5(`DHX_password:${newPassword}`);
      hashPassword = this.md5(`DHX_password:${hashPassword}`);
      const result = await this.httpService.axiosRef
        .post(`${this.configService.server.saasService}/user/changePassword`, {
          phone,
          newPassword: hashPassword,
          newPasswordVerify: hashPassword,
        })
        .then((resp) => resp.data);
      if (result.status != 200) {
        this.logger.error(`change password by phone failed: ${JSON.stringify(result)}`);
        throw new BadRequestException(RoverExceptions.UserRelated.User.ChangePasswordFailed);
      }
    } catch (err) {
      if (err instanceof HttpException) {
        throw err;
      } else {
        if (err.response && err.response.data) {
          this.logger.error(`change password by phone failed: ${JSON.stringify(err.response.data)}`);
        } else {
          this.logger.error(`change password by phone failed: ${err.message}`);
        }
        throw new BadRequestException(RoverExceptions.UserRelated.User.ChangePasswordFailed);
      }
    }
  }

  private md5(content, secret = '') {
    const MD5 = crypto.createHash('md5');
    MD5.update(content + secret);
    return MD5.digest('hex');
  }
}
