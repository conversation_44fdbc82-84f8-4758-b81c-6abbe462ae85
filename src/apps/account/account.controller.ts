import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Header,
  InternalServerErrorException,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Request,
  Res,
  UseFilters,
  UseGuards,
} from '@nestjs/common';
import { ApiCookieAuth, ApiOkResponse, ApiOperation, ApiProperty, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  ChangeCurrentOrg,
  ResetPasswordRequest,
  ResetPasswordVerificationCodePayload,
  UpdatePasswordRequest,
  UpdateSearchModel,
  VerificationCodePayload,
  VerificationCodePayloadForTrail,
  VerificationCodeRequest,
} from 'libs//model/account/request';
import { AffectedResponse, RoverUser } from 'libs/model/common';
import { RoverSessionGuard } from 'libs/guards/RoverSession.guard';
import { AuthService, SessionGuard } from '@kezhaozhao/saas-auth';
import { Response } from 'express';
import { GuardExceptionFilter } from 'libs/filters/guard-exception.filter';
import { AccountService } from './account.service';
import { BadParamsException, LogUtils } from '@kezhaozhao/qcc-utils';
import { AfsVerifyService } from './afs-verify.service';
import { Product } from '@kezhaozhao/saas-bundle-service';
import { ProAuthService } from './pro.auth.service';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { NewTrialApplyForRover } from 'libs//model/account/NewTrialApplyForRover';
import { ResultType } from 'libs/model/account/constant';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { VerifyCodeResp } from '../../libs/model/account/response';
import { TrailService } from './trial.service';

@Controller('account')
@ApiTags('Account')
@ApiCookieAuth()
export class AccountController {
  private readonly logger: Logger = QccLogger.getLogger(AccountController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly accountService: AccountService,
    private readonly afsVerifyService: AfsVerifyService,
    private readonly proAuthService: ProAuthService,
    private readonly trailService: TrailService,
  ) {}

  @Post('sendUpdatePasswordVerifyCode')
  @UseGuards(RoverSessionGuard)
  @ApiOperation({ summary: '发送修改密码验证码' })
  @ApiOkResponse({ type: VerifyCodeResp })
  public async sendUpdatePasswordVerifyCode(@Request() req, @Body() body: VerificationCodePayload) {
    const { phone } = req.user;
    const { sessionId, sig, token } = body;
    const ip = LogUtils.ip(req);
    // 人机验签
    await this.afsVerifyService.afsVerification(sessionId, sig, token, ip);
    return this.accountService.sendUpdatePasswordVerifyCode(phone);
  }

  @Post('updatePassword')
  @UseGuards(RoverSessionGuard)
  @ApiOperation({ summary: '修改密码' })
  @ApiOkResponse({ type: AffectedResponse })
  public async updatePassword(@Request() req, @Body() postBody: UpdatePasswordRequest) {
    return this.accountService.updatePassword(req.user.phone, postBody);
  }

  @Post('sendRestPasswordVerifyCode')
  @ApiOperation({ summary: '发送重置密码验证码' })
  @ApiOkResponse({ type: VerifyCodeResp })
  public async sendRestPasswordVerifyCode(@Request() req, @Body() body: ResetPasswordVerificationCodePayload) {
    const { phone, sessionId, sig, token } = body;
    const ip = LogUtils.ip(req);
    // 人机验签
    await this.afsVerifyService.afsVerification(sessionId, sig, token, ip);
    return this.accountService.sendUpdatePasswordVerifyCode(phone);
  }

  @Post('resetPassword')
  @ApiOperation({ summary: '重置密码' })
  @ApiOkResponse({ type: AffectedResponse })
  public async resetPassword(@Body() postBody: ResetPasswordRequest) {
    return this.accountService.updatePassword(postBody.phone, postBody);
  }

  @Get('loginRedirect')
  @ApiOperation({ description: '页面通过Token(accessKey + timestamp + sign)登录并跳转' })
  public async loginRedirect(
    @Request() req,
    @Query('phone') phone: string,
    @Query('email') email: string,
    @Query('accessKey') accessKey: string,
    @Query('timestamp') timestamp: string,
    @Query('sign') sign: string,
    @Query('redirectUrl') redirectUrl: string,
    @Query('fallbackUrl') fallbackUrl: string,
    @Res() response: Response,
  ) {
    this.logger.info(
      `loginRedirect accessKey=${accessKey}, phone=${phone}, email=${email}, timestamp=${timestamp}, sign=${sign}, redirectUrl=${redirectUrl}, fallbackUrl=${fallbackUrl}`,
    );
    //phone 和 email 必须有一个
    if ((!phone && !email) || !accessKey || !timestamp || !sign || !redirectUrl) {
      throw new BadRequestException({ error: '缺少必要参数' });
    }
    try {
      const ip = LogUtils.ip(req);
      const userAgent = req.headers['user-agent'];
      const result = await this.accountService.loginByAccessKey(phone, email, accessKey, Number(timestamp), sign, ip, userAgent);
      this.logger.info(`loginRedirect accessKey=${accessKey} loginByAccessKey result: ${JSON.stringify(result)}`);
      return this.processLoginAction(response, result, fallbackUrl, redirectUrl);
    } catch (error) {
      this.logger.error(`loginRedirect accessKey=${accessKey} error: ${error.message}`);
      this.logger.error(error);
      if (fallbackUrl) {
        response.redirect(fallbackUrl + '?errorMessage=' + encodeURIComponent(error.message));
      } else {
        throw new BadRequestException(RoverExceptions.UserRelated.Auth.InvalidAccessToken);
      }
    }
  }

  @Get('loginByToken')
  @ApiOperation({ description: '页面通过Token登录' })
  public async loginByToken(
    @Query('accessToken') accessToken: string,
    @Query('redirectUrl') redirectUrl: string,
    @Query('fallbackUrl') fallbackUrl: string,
    @Res() response: Response,
  ) {
    this.logger.info(`loginByToken: accessToken=${accessToken}`);
    try {
      const result = await this.accountService.loginByToken(accessToken);
      return this.processLoginAction(response, result, fallbackUrl, redirectUrl);
    } catch (error) {
      this.logger.error(`loginByToken error: ${error.message}`);
      this.logger.error(error);
      if (fallbackUrl) {
        response.redirect(fallbackUrl + '?errorMessage=' + encodeURIComponent(error.message));
      } else {
        throw new BadRequestException(RoverExceptions.UserRelated.Auth.InvalidAccessToken);
      }
    }
  }

  private processLoginAction(response: Response, result: CustomCallbackResponse, fallbackUrl, redirectUrl) {
    if (result.resultType == ResultType.Session) {
      response.cookie('KZZSESSID', result.data, {
        domain: '.qcc.com',
        httpOnly: true,
        path: '/',
        sameSite: 'none',
        secure: true,
        maxAge: 1000 * 60 * 60 * 24 * 7,
      });
      this.logger.info(`set KZZSESSID = ${result.data}, success!`);
      // 跳转页面是将kzzsessid传给前端, 前端可以在嵌入页面请求后端接口时把kzzsessid加入到header中
      redirectUrl += redirectUrl.indexOf('?') > -1 ? '&' : '?';
      redirectUrl += 'kzzsessid=' + result.data;
      response.redirect(redirectUrl);
    } else if (result.resultType == ResultType.Error) {
      if (fallbackUrl) {
        response.redirect(fallbackUrl + '?errorMessage=' + encodeURIComponent(result.data?.error));
      } else {
        throw result.data?.error;
      }
    }
  }

  @Get('logout')
  @UseGuards(SessionGuard)
  @UseFilters(GuardExceptionFilter)
  @Header('Cache-Control', 'no-store')
  @ApiOperation({ summary: '用户登出' })
  @ApiResponse({ status: 302, description: '跳转到首页' })
  public async logout(@Request() req, @Res() res: Response) {
    try {
      await this.authService.logout(req);
      res.redirect('/');
    } catch (e) {
      throw new InternalServerErrorException();
    }
  }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @Post('changeCurrentOrg')
  @ApiOperation({ summary: '切换用户所在组织' })
  async updateProfile(@Request() req, @Body() postData: ChangeCurrentOrg) {
    const currentUser: RoverUser = req.user;
    if (postData.currentOrg && postData.currentOrg != currentUser.currentOrg) {
      postData.product = Product.Rover;
      postData.loginUserId = currentUser.loginUserId;
      await this.accountService.changeCurrentOrg(postData);
      this.authService.clearExchangeUserCache(req.user); // 清除exchangeUser缓存
      req.user.currentOrg = postData.currentOrg;
    }
    return req.user;
  }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @Get('org/list')
  @ApiOperation({ summary: '获取用户的组织列表' })
  async getOrgList(@Request() req) {
    const currentUser: RoverUser = req.user;
    return this.accountService.getUserOrgList({
      loginUserId: currentUser.loginUserId,
      guid: currentUser.guid,
    });
  }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @Post('updateLog/search')
  @ApiOperation({ summary: '获取产品的发版日志' })
  async searchReleaseLog(@Body() data: UpdateSearchModel) {
    // const currentUser: RoverUser = req.user;
    data.serviceCode = Product.Rover;
    data.status = 2;
    return this.accountService.searchReleaseLog(data);
  }

  @Get('searchName')
  @ApiQuery({ name: 'searchKey', required: true })
  @ApiOperation({ summary: '公司名称补全', description: '企业名称模糊搜索，无需登录的' })
  public async searchName(@Query('searchKey') searchKey: string) {
    const chineseRegex = /[\u4E00-\u9FFF\u3400-\u4DBF\u{20000}-\u{2A6DF}\u{2A700}-\u{2B734}\u{2B740}-\u{2B81D}\u{2B820}-\u{2CEAF}\u{2CEB0}-\u{2EBEF}]/u;
    if (!chineseRegex.test(searchKey)) {
      throw new BadParamsException(RoverExceptions.BadParams.Common);
    }
    return this.accountService.searchName(searchKey, ['KeyNo', 'Name', 'OperName', 'ImageUrl'], 5, 1);
  }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @Post('getProToken')
  @ApiOperation({ summary: '获取用户专业版详情页token' })
  async getProToke(@Request() req) {
    const currentUser: RoverUser = req.user;
    return this.proAuthService.getProToken(currentUser);
  }

  @Post('trial/apply')
  @ApiOperation({ summary: '申请使用' })
  @ApiOkResponse({ type: AffectedResponse })
  public async trailApply(@Body() postData: NewTrialApplyForRover) {
    return this.accountService.trailApply(postData);
  }

  @Get('customerInfo')
  @ApiOperation({ summary: '获取客户商机负责人信息' })
  @ApiOkResponse({ type: AffectedResponse })
  public async getCompanyCustomerInfo(@Query('companyName') companyName: string) {
    return this.trailService.getOrgCustomerInfo(companyName);
  }

  // @Post('trial/apply/test')
  // @ApiOperation({ summary: '申请使用' })
  // @ApiOkResponse({ type: AffectedResponse })
  // public async trailApplyT(@Body() postData: NewTrialApplyForRover) {
  //   return this.accountService.trailApplyTest(postData);
  // }

  @Post('trial/verificationCode')
  @ApiOperation({ summary: '获取手机验证码' })
  @ApiOkResponse({ type: AffectedResponse })
  public async trialVerificationCode(@Body() body: VerificationCodePayloadForTrail, @Request() req) {
    this.logger.debug(`get verification code: ${body.phone}`);
    const ip = LogUtils.ip(req);
    const { phone, sessionId, sig, token } = body;
    // 人机验签
    await this.afsVerifyService.afsVerification(sessionId, sig, token, ip);
    return this.accountService.sendVerificationCodeForTrail(phone);
  }

  @Post('trial/verificationCodeV2')
  @ApiOperation({ summary: '获取手机验证码(无人机验证)' })
  @ApiOkResponse({ type: AffectedResponse })
  public async trialVerificationCodeNoAfs(@Body() body: VerificationCodeRequest, @Request() req) {
    this.logger.debug(`get verification code: ${body.phone}`);
    // const ip = LogUtils.ip(req);
    const { phone } = body;
    // 人机验签
    // await this.afsVerifyService.afsVerification(sessionId, sig, token, ip);
    return this.accountService.sendVerificationCodeForTrail(phone);
  }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @Get('changeOrgRedirect/:orgId')
  @ApiOperation({ summary: '切换用户所在组织并跳转' })
  async changeOrgRedirect(@Request() req, @Res() res: Response, @Param('orgId', ParseIntPipe) orgId: number, @Query('redirect') redirect: string) {
    const currentUser: RoverUser = req.user;
    this.logger.info(`changeOrgRedirect orgId ${orgId} currentOrg ${currentUser.currentOrg}`);
    if (orgId != currentUser.currentOrg) {
      const postData: ChangeCurrentOrg = {
        product: Product.Rover,
        loginUserId: currentUser.loginUserId,
        currentOrg: orgId,
      };
      const result = await this.accountService.changeCurrentOrg(postData);
      req.user.currentOrg = postData.currentOrg;
      this.logger.info(`changeOrgRedirect result ${JSON.stringify(result)}`);
    }

    redirect = redirect && redirect.startsWith('/') ? redirect : '/supplier';
    setTimeout(() => res.redirect(redirect), 500);
  }

  @ApiCookieAuth()
  @UseGuards(RoverSessionGuard)
  @Get('changeOrgRedirect2/:orgId')
  @ApiOperation({ summary: '切换用户所在组织并跳转2' })
  async changeOrgRedirect2(@Request() req, @Res() res: Response, @Param('orgId', ParseIntPipe) orgId: number, @Query('redirect') redirect: string) {
    const currentUser: RoverUser = req.user;
    this.logger.info(`changeOrgRedirect2 orgId ${orgId} currentOrg ${currentUser.currentOrg}`);
    if (orgId != currentUser.currentOrg) {
      const postData: ChangeCurrentOrg = {
        product: Product.Rover,
        loginUserId: currentUser.loginUserId,
        currentOrg: orgId,
      };
      const result = await this.accountService.changeCurrentOrg(postData);
      req.user.currentOrg = postData.currentOrg;
      this.logger.info(`changeOrgRedirect2 result ${JSON.stringify(result)}`);
    }

    redirect = redirect && redirect.startsWith('/') ? redirect : '/supplier';
    res.redirect(redirect);
  }
}

export class CustomCallbackResponse {
  @ApiProperty({ description: 'data' })
  data: any;
  @ApiProperty({ description: '结果类型' })
  resultType: number;
  @ApiProperty({ description: '结果类型描述' })
  msg: string;
  @ApiProperty({ description: 'openId' })
  openId: string;
  @ApiProperty({ description: '组织Id' })
  orgId: string;
  @ApiProperty({ description: 'guid' })
  guid: string;
  @ApiProperty({ description: '成功回跳地址' })
  redirectUrl: string;
  @ApiProperty({ description: '失败回跳地址' })
  fallbackUrl: string;
}
