import { Test, TestingModule } from '@nestjs/testing';
import { AccountController } from './account.controller';
import { INestApplication } from '@nestjs/common';
import { AccountService } from './account.service';
import { QccService } from './qcc.service';
import { AfsVerifyService } from './afs-verify.service';
import { ProAuthService } from './pro.auth.service';
import { TrailService } from './trial.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from '../../libs/entities/UserEntity';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { AppTestModule } from '../app/app.test.module';

jest.setTimeout(60 * 1000);
describe('集成测试-AccountController', () => {
  let app: INestApplication;
  let controller: AccountController;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, TypeOrmModule.forFeature([UserEntity])],
      providers: [QccService, AccountService, AfsVerifyService, ProAuthService, TrailService],
      controllers: [AccountController],
    }).compile();
    app = module.createNestApplication();
    await app.init();
    controller = module.get<AccountController>(AccountController);
  });

  it('测试公司名称补全接口', async () => {
    expect(controller).toBeDefined();
    const response: any = await controller.searchName('企查查');
    expect(response).not.toBeUndefined();
    expect(response.Result[0].KeyNo).toEqual('f625a5b661058ba5082ca508f99ffe1b');
    //不包含中文，只传统一信用代码
    await expect(controller.searchName('91320594088140947F')).rejects.toThrow(BadParamsException);
  });
});
