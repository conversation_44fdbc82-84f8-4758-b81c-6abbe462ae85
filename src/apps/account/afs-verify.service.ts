import { BadRequestException, Injectable } from '@nestjs/common';
import { nanoid } from 'nanoid';
import * as crypto from 'crypto';
import { get } from 'lodash';
import { ConfigService } from 'libs/config/config.service';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { HttpService } from '@nestjs/axios';

@Injectable()
export class AfsVerifyService {
  private readonly logger: Logger = QccLogger.getLogger(AfsVerifyService.name);

  constructor(private readonly configService: ConfigService, private readonly httpService: HttpService) {}

  /**
   * get user session by sessionId API
   */
  public async afsVerification(sessionId: string, sig: string, token: string, ip: string) {
    const { accessKey, accessKeySecret, appKey, scene, baseUrl } = this.configService.server.aliyun;
    const method = 'GET';
    const payload = {
      Format: 'JSON',
      Version: '2018-01-12',
      SignatureMethod: 'HMAC-SHA1',
      Timestamp: new Date().toISOString(),
      // Signature
      SignatureVersion: '1.0',
      SignatureNonce: nanoid(),
      Action: 'AuthenticateSig',
      AccessKeyId: accessKey,
      AppKey: appKey,
      RemoteIp: ip,
      Scene: scene,
      // Post
      Token: token,
      SessionId: sessionId,
      Sig: sig,
    };
    const normalized = this.afsNormalize(payload);
    const cannibalised = this.afsCanonicalize(normalized);
    const stringToSign = `${method}&${this.afsEncode('/')}&${this.afsEncode(cannibalised)}`;
    const key = `${accessKeySecret}&`;
    const signature = crypto.createHmac('sha1', key).update(stringToSign).digest('base64');
    normalized.push(['Signature', this.afsEncode(signature)]);
    const api = `${baseUrl}/?${this.afsCanonicalize(normalized)}`;
    try {
      const resData = await this.httpService.axiosRef.get(api);
      if (get(resData, 'data.Code') !== 100) {
        throw new BadRequestException(RoverExceptions.UserRelated.Auth.ManMachineError);
      }
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(RoverExceptions.UserRelated.Auth.ManMachineError);
    }
  }

  private afsEncode(value) {
    return encodeURIComponent(value).replace(/!/g, '%21').replace(/'/g, '%27').replace(/\(/g, '%28').replace(/\)/g, '%29').replace(/\*/g, '%2A');
  }

  private afsNormalize(params) {
    const keys = Object.keys(params).sort((a, b) => a.localeCompare(b));
    return keys.map((key) => [this.afsEncode(key), this.afsEncode(params[key])]);
  }

  private afsCanonicalize(normalized) {
    const fields = normalized.map(([key, value]) => `${key}=${value}`);
    return fields.join('&');
  }
}
