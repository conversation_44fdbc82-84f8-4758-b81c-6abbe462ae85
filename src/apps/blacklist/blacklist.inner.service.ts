import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { AffectedResponse, AggsResponse, RoverUser } from 'libs/model/common';
import { CreateInnerBlacklistModel } from 'libs/model/blacklist/CreateInnerBlacklistModel';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { InnerBlacklistEntity } from 'libs/entities/InnerBlacklistEntity';
import { Brackets, EntityManager, In, LessThanOrEqual, MoreThanOrEqual, Repository } from 'typeorm';
import { SearchInnerBlacklistModel } from 'libs/model/blacklist/SearchInnerBlacklistModel';
import { SearchInnerBlacklistResponse } from 'libs/model/blacklist/SearchInnerBlacklistResponse';
import { UpdateInnerBlacklistModel } from 'libs/model/blacklist/UpdateInnerBlacklistModel';
import { BadParamsException, CommonExceptions } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { BatchStatusEnums } from 'libs/enums/batch/BatchStatusEnums';
import { DurationEnums } from 'libs//enums/blacklist/DurationEnums';
import * as moment from 'moment';
import { RoverBundleCounterType, RoverBundleService } from '@kezhaozhao/saas-bundle-service';
import { EntityDuplicatedException } from '../../libs/exceptions/EntityDuplicatedException';
import { DiligenceHistoryCacheHelper } from 'apps/basic/diligence.history.cache.helper';
import { compact, difference, flatten, groupBy, isEmpty, omit, uniq, uniqBy } from 'lodash';
import { CompanySearchService } from '../company/company-search.service';
import { InnerBlacklistLabelEntity } from '../../libs/entities/InnerBlacklistLabelEntity';
import * as Bluebird from 'bluebird';
import { OperatorTypeEnums } from '../../libs/enums/oplog/OperatorTypeEnums';
import { OpLogService } from '../oplog/oplog.service';
import { GroupChangeRequest } from '../../libs/model/blacklist/GroupChangeRequest';
import { LabelModifyRequest } from '../../libs/model/blacklist/LabelModifyRequest';
import { GroupsEntity } from '../../libs/entities/GroupsEntity';
import { LabelEntity } from '../../libs/entities/LabelEntity';
import { LabelService } from '../element/label.service';
import { MatchCustomerModel } from '../../libs/model/customer/MatchCustomerModel';
import { SecurityService } from '../../libs/config/security.service';
import { PermissionByEnum } from '../../libs/enums/PermissionScopeEnum';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchCreateInnerBlacklist } from '../../libs/model/blacklist/BatchCreateInnerBlacklist';
import { CustomerEntity } from 'libs/entities/CustomerEntity';
import { CustomerService } from '../customer/customer.service';
import { CompanyBase, RemoveCustomerResponse } from '../../libs/model/blacklist/RemoveCustomerResponse';
import { QueryBuilderHelper } from '../../libs/common/sql.helper';
import { StreamTableEnums } from '../../libs/enums/data/StreamTableEnums';
import { StreamOperationEnum } from '../../libs/enums/data/StreamOperationEnum';
import { RoverGraphService } from '../data/source/rover.graph.service';
import { DepartmentService } from '../element/department.service';
import { DepartmentTypeEnum } from '../../libs/enums/department/DepartmentTypeEnum';
import { InnerBlacklistDepartmentEntity } from '../../libs/entities/InnerBlacklistDepartmentEntity';
import { DepartmentEntity } from '../../libs/entities/DepartmentEntity';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';
import { InnerBlackAggsRequest } from '../../libs/model/blacklist/InnerBlackAggsRequest';
import { UserEntity } from '../../libs/entities/UserEntity';
import { GroupType } from '../../libs/model/element/CreateGroupModel';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';

@Injectable()
export class BlacklistInnerService {
  private readonly logger: Logger = QccLogger.getLogger(BlacklistInnerService.name);

  constructor(
    @InjectRepository(InnerBlacklistEntity) private readonly innerBlacklistRepo: Repository<InnerBlacklistEntity>,
    @InjectRepository(InnerBlacklistLabelEntity) private readonly innerBlacklistLabelRepo: Repository<InnerBlacklistLabelEntity>,
    @InjectRepository(GroupsEntity) private readonly groupRepo: Repository<GroupsEntity>,
    @InjectRepository(LabelEntity) private readonly labelRepo: Repository<LabelEntity>,
    @InjectRepository(CustomerEntity) private readonly customerRepo: Repository<CustomerEntity>,
    @InjectRepository(InnerBlacklistDepartmentEntity) private readonly innerBlacklistDepartmentRepo: Repository<InnerBlacklistDepartmentEntity>,
    @InjectRepository(DepartmentEntity) private readonly departmentRepo: Repository<DepartmentEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly bundleService: RoverBundleService,
    private readonly diligenceHistoryHelper: DiligenceHistoryCacheHelper,
    private readonly companySearchService: CompanySearchService,
    private readonly customerService: CustomerService,
    private readonly labelService: LabelService,
    private readonly oplogService: OpLogService,
    private readonly securityService: SecurityService,
    private readonly graphService: RoverGraphService,
    private readonly departmentService: DepartmentService,
  ) {}

  async checkCountLimit(currentUser: RoverUser, num: number) {
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.InnerBlacklistQuantity);
    // await bundleCounter.clear();
    await bundleCounter.check(num);
  }

  /**
   * 添加黑名单
   * 设置了 duration 字段 且 duration 大于等于 0，则会根据 duration 计算出 expiredDate 字段
   * - 如果 duration 为 -1，则表示永久加入黑名单
   * - 如果 duration 为 -2，则表示expireDate 字段为用户自定义设置且必填
   * @param currentUser
   * @param postData
   * @param isBatch
   */
  async add(currentUser: RoverUser, postData: CreateInnerBlacklistModel, isBatch = false) {
    const { currentOrg: orgId, userId, departments } = currentUser;
    const dbInfo = await this.innerBlacklistRepo.findOne({ where: { orgId: orgId, companyId: postData.companyId } });
    if (dbInfo) {
      throw new EntityDuplicatedException(RoverExceptions.Customer.DuplicatedCompany, [dbInfo.id]);
    }
    const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.InnerBlacklistQuantity);
    // await bundleCounter.clear();
    await bundleCounter.increase(1);
    //计算过期时间
    if (!postData?.joinDate) {
      //默认joinDate是创建时间
      postData.joinDate = moment().toDate();
    }
    if (postData?.duration >= 0) {
      postData.expiredDate = await this.calculateExpiredDate(postData?.duration, postData?.joinDate);
    } else if (postData?.duration === -2) {
      if (!postData?.expiredDate) {
        throw new BadParamsException({ message: `expiredDate 字段为必填!`, ...CommonExceptions.Common.Request.NotFound });
      }
    }
    const companyEntity = await this.companySearchService.createCompanyInfo(postData.companyId, postData.companyName);
    if (!companyEntity) {
      await bundleCounter.decrease(1);
      throw new BadParamsException({ message: '公司不存在!', ...CommonExceptions.Common.Request.NotFound });
    }
    // 使用 entityManager 查询
    const diligence = await this.entityManager.findOne(DiligenceHistoryEntity, {
      where: { orgId, companyId: postData.companyId },
      order: { updateDate: 'DESC' },
    });
    let blackList: InnerBlacklistEntity;
    try {
      if (postData?.departmentNames?.length) {
        postData.department = postData.departmentNames.join(',');
      }
      blackList = await this.innerBlacklistRepo.save(
        Object.assign(
          new InnerBlacklistEntity(),
          omit(postData, ['econkind', 'province', 'city', 'district', 'industry1', 'industry2', 'industry3', 'industry4', 'departmentNames']),
          {
            orgId,
            depId: departments?.[0],
            createBy: userId,
          },
          {
            result: diligence?.result,
            diligenceId: diligence?.id,
            diligenceDate: diligence?.updateDate,
          },
        ),
      );
    } catch (error) {
      await bundleCounter.decrease(1);
      throw error;
    }

    if (postData?.labelIds?.length) {
      // 保存客户标签
      await this.innerBlacklistLabelRepo.save(
        postData.labelIds.map((labelId) =>
          Object.assign(new InnerBlacklistLabelEntity(), {
            innerBlacklistId: blackList.id,
            labelId,
          }),
        ),
      );
    }
    const departmentEntities = await this.departmentService.saveDepartments(orgId, postData.departmentNames, DepartmentTypeEnum.InnerBlacklist);
    if (departmentEntities?.length) {
      //保存内部黑名单部门关联
      await this.innerBlacklistDepartmentRepo.save(
        departmentEntities.map((department) =>
          Object.assign(new InnerBlacklistDepartmentEntity(), {
            innerBlacklistId: blackList.id,
            departmentId: department.departmentId,
          }),
        ),
      );
    }
    await Bluebird.all([
      this.diligenceHistoryHelper.makeHistoryForUpdate(orgId),
      this.oplogService.add(isBatch ? OperatorTypeEnums.BatchCreate : OperatorTypeEnums.Create, InnerBlacklistEntity, currentUser, [blackList.id], [blackList]),
    ]);
    this.logger.info(`org:${orgId}，user:${userId} 保存 内部 黑名单成功，id:${blackList.id}`);
    return blackList;
  }

  async updateFromExcel(currentUser: RoverUser, createData: CreateInnerBlacklistModel) {
    const { currentOrg: orgId } = currentUser;
    const dbInfo = await this.innerBlacklistRepo.findOne({ where: { orgId: orgId, companyId: createData.companyId } });
    const { by, userIds } = this.securityService.checkScope(currentUser, 2042);
    if (dbInfo && (by == PermissionByEnum.ORG || userIds?.includes(dbInfo.createBy))) {
      const partialData = Object.assign(new UpdateInnerBlacklistModel(), createData);
      await this.update(dbInfo.id, partialData, currentUser);
      return Object.assign(dbInfo, createData);
    }
  }

  async update(id: number, partialData: UpdateInnerBlacklistModel, currentUser: RoverUser) {
    const { currentOrg: orgId } = currentUser;
    //查询当前黑名单是否存在
    const blacklistEntities = await this.innerBlacklistRepo.find({
      where: {
        orgId: orgId,
        companyId: partialData.companyId,
      },
    });
    if (!blacklistEntities || blacklistEntities?.length == 0) {
      throw new BadRequestException(RoverExceptions.BadParams.NotFound);
    }
    if (blacklistEntities.length > 1) {
      throw new EntityDuplicatedException(
        RoverExceptions.BadParams.InnerBlackListMany,
        blacklistEntities.map((b) => b.id),
      );
    }
    const dbBlacklist = blacklistEntities.find((b) => b.id == id);
    if (!dbBlacklist) {
      throw new BadRequestException(RoverExceptions.BadParams.NotFound);
    }
    const companyEntity = await this.companySearchService.createCompanyInfo(partialData.companyId, partialData.companyName);
    if (!companyEntity) {
      throw new BadParamsException({ message: `公司:${partialData.companyId}不存在!`, ...CommonExceptions.Common.Request.NotFound });
    }
    const { labelIds, departmentNames } = partialData;
    // 准备更新数据
    if (!partialData?.joinDate) {
      //默认joinDate是创建时间
      partialData.joinDate = dbBlacklist.createDate;
    }
    if (
      (partialData?.duration !== dbBlacklist?.duration || moment(partialData?.joinDate).toDate() !== moment(dbBlacklist?.joinDate).toDate()) &&
      partialData?.duration >= 0
    ) {
      partialData.expiredDate = await this.calculateExpiredDate(partialData.duration, partialData?.joinDate);
    } else if (partialData?.duration === -2) {
      if (!partialData?.expiredDate) {
        throw new BadParamsException({ message: `expiredDate 字段为必填!`, ...CommonExceptions.Common.Request.NotFound });
      }
    }
    if (departmentNames?.length) {
      partialData.department = departmentNames.join(',');
    }
    // 标签处理
    let preLabel = '-';
    let differentLabel;
    const innerBlacklistLabels = await this.entityManager.find(InnerBlacklistLabelEntity, { innerBlacklistId: id });
    if (innerBlacklistLabels?.length > 0) {
      const oldLabelIds = innerBlacklistLabels.map((t) => t.labelId);
      const oldLabels = await this.labelRepo.findByIds(oldLabelIds);
      preLabel = oldLabels?.map((t) => t.name)?.join(',');
      differentLabel = difference(oldLabelIds, labelIds);
    } else if (labelIds?.length > 0) {
      differentLabel = true;
    }

    // 部门处理
    let differentDepartment;
    let departmentEntities;
    const blacklistDepartments = await this.entityManager.find(InnerBlacklistDepartmentEntity, { innerBlacklistId: id });
    if (blacklistDepartments?.length > 0) {
      const oldDepartmentIds = blacklistDepartments.map((t) => t.departmentId);
      const oldDepartments = await this.entityManager.findByIds(DepartmentEntity, oldDepartmentIds);
      const oldDepartmentNames = oldDepartments.map((t) => t.name);
      differentDepartment = difference(oldDepartmentNames, departmentNames);
    } else if (departmentNames?.length > 0) {
      differentDepartment = true;
    }

    if (differentDepartment) {
      // 存在差异，需要变更，预先插入数据库
      departmentEntities = await this.departmentService.saveDepartments(orgId, departmentNames, DepartmentTypeEnum.InnerBlacklist);
    }
    // 事务处理数据
    const updateResult = await this.entityManager.transaction(async (manager) => {
      const result = await manager.update(
        InnerBlacklistEntity,
        { id, orgId },
        omit(partialData, ['econkind', 'province', 'city', 'district', 'industry1', 'industry2', 'industry3', 'industry4', 'labelIds', 'departmentNames']),
      );
      if (differentLabel) {
        // 标签有变化，先删后增
        await manager.delete(InnerBlacklistLabelEntity, { innerBlacklistId: id });
        if (labelIds?.length) {
          await manager.save(
            labelIds.map((i) =>
              Object.assign(new InnerBlacklistLabelEntity(), {
                labelId: i,
                innerBlacklistId: id,
              }),
            ),
          );
        }
      }
      if (differentDepartment) {
        // 部门有变化，先删后增
        await manager.delete(InnerBlacklistDepartmentEntity, { innerBlacklistId: id });
        if (departmentEntities?.length) {
          await manager.save(
            departmentEntities.map((i) =>
              Object.assign(new InnerBlacklistDepartmentEntity(), {
                departmentId: i.departmentId,
                innerBlacklistId: id,
              }),
            ),
          );
        }
      }

      //更新时间通过ON UPDATE CURRENT_TIMESTAMP更新，label更新没有更新主表字段，强制更新一下
      await manager.update(InnerBlacklistEntity, { id }, { updateDate: new Date() });
      return result;
    });
    const currentBlacklist = await this.entityManager.findOne(InnerBlacklistEntity, id);
    await Bluebird.all([
      this.oplogService.add(OperatorTypeEnums.Edit, InnerBlacklistEntity, currentUser, [id], [dbBlacklist], [currentBlacklist], preLabel),
      this.diligenceHistoryHelper.makeHistoryForUpdate(orgId),
    ]);
    return updateResult;
  }

  async count(currentUser: RoverUser) {
    return this.innerBlacklistRepo.count({ orgId: currentUser.currentOrg, status: BatchStatusEnums.Done });
  }

  private async getQb(currentUser: RoverUser, postData: SearchInnerBlacklistModel) {
    const {
      operators,
      groupIds,
      labelIds,
      companyIds,
      registcapiAmount,
      startDateCode,
      statusCode,
      department,
      searchKey,
      companyNames,
      companyNos,
      ids,
      departmentIds,
      depIds,
      result,
      expiredDate,
    } = postData;
    const { currentOrg: orgId } = currentUser;
    const { by, userIds } = this.securityService.checkScope(currentUser, 2041);
    const qb = this.innerBlacklistRepo
      .createQueryBuilder('blacklist')
      .leftJoinAndSelect('blacklist.creator', 'creator')
      .leftJoinAndSelect('blacklist.labels', 'labels')
      .leftJoinAndSelect('blacklist.company', 'company')
      .leftJoinAndSelect('blacklist.departments', 'departments')
      .where('blacklist.orgId = :orgId', { orgId })
      .andWhere('blacklist.status = :status', { status: BatchStatusEnums.Done });
    if (by == PermissionByEnum.USER) {
      qb.andWhere('blacklist.createBy in (:...userIds)', { userIds });
    }

    if (labelIds?.length) {
      if (labelIds.includes(-1)) {
        if (labelIds.length === 1) {
          qb.andWhere('labels.labelId is null');
        } else {
          const labelResult = labelIds.filter((r) => r >= 0);
          qb.andWhere(
            new Brackets((qb1) => {
              qb1.orWhere('labels.labelId is null');
              qb1.orWhere('labels.labelId in (:...labelIds)', { labelIds: labelResult });
            }),
          );
        }
      } else {
        // qb.andWhere('label.labelId in (:...labelIds)', { labelIds });
        qb.andWhere('labels.labelId in (:...labelIds)', { labelIds });
      }
    }

    if (departmentIds?.length) {
      if (departmentIds.includes(-1)) {
        if (departmentIds.length === 1) {
          qb.andWhere('departments.departmentId is null');
        } else {
          const depIdResult = departmentIds.filter((r) => r >= 0);
          qb.andWhere(
            new Brackets((qb1) => {
              qb1.orWhere('departments.departmentId is null');
              qb1.orWhere('departments.departmentId in (:...depIdResult)', { depIdResult });
            }),
          );
        }
      } else {
        //qb.andWhere('departments.departmentId in (:...departmentIds)', { departmentIds });
        qb.andWhere('departments.departmentId in (:...departmentIds)', { departmentIds });
      }
    }

    if (companyIds?.length) {
      qb.andWhere('blacklist.companyId in (:...companyIds)', { companyIds });
    }

    //部门
    if (!isEmpty(department)) {
      qb.andWhere('blacklist.department = :department', { department });
    }

    if (ids?.length) {
      qb.andWhere('blacklist.id in (:...ids)', { ids: postData.ids });
    }

    if (depIds?.length) {
      qb.andWhere('blacklist.depId in (:...depIds)', { depIds });
    }

    if (operators?.length) {
      qb.andWhere('blacklist.createBy in (:...operators)', { operators });
    }

    if (groupIds?.length) {
      if (groupIds?.includes(-1) && groupIds?.length === 1) {
        qb.andWhere('blacklist.groupId is null');
      }
      if (!groupIds?.includes(-1)) {
        qb.andWhere('blacklist.groupId in (:...groupIds)', { groupIds });
      }
      if (groupIds?.includes(-1) && groupIds?.length > 1) {
        const filterGroupIds = groupIds.filter((r) => r >= 0);
        qb.andWhere(
          new Brackets((qb1) => {
            qb1.orWhere('blacklist.groupId is null');
            qb1.orWhere('blacklist.groupId in (:...filterGroupIds)', { filterGroupIds });
          }),
        );
      }
    }

    if (result && result.length > 0) {
      qb.andWhere('blacklist.result in (:...result)', { result });
    }
    // 列入时间
    QueryBuilderHelper.applyDateRangeQuery(qb, postData?.joinDate, 'joinDate');
    //创建时间
    QueryBuilderHelper.applyDateRangeQuery(qb, postData?.createDate, 'createDate');
    //更新时间
    QueryBuilderHelper.applyDateRangeQuery(qb, postData?.updateDate, 'updateDate');
    //过期时间
    QueryBuilderHelper.applyDateRangeQuery(qb, expiredDate, 'expiredDate');

    // 公司名称
    if (searchKey) {
      qb.andWhere(
        new Brackets((qb1) => {
          qb1.orWhere('company.name like :name', { name: `%${searchKey}%` });
          qb1.orWhere('company.creditcode = :creditcode', { creditcode: searchKey });
        }),
      );
    }

    // 公司名称
    if (companyNames?.length) {
      qb.andWhere('company.name in (:...companyNames)', { companyNames });
    }
    if (companyNos?.length) {
      qb.andWhere('company.companyId in (:...companyNos)', { companyNos });
    }

    // 企业类型
    if (postData?.econKindCode?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          const econKindMap = {};
          postData.econKindCode.forEach((econKind) => {
            econKindMap[econKind] = econKind;
          });
          postData.econKindCode.forEach((key) => {
            if (key) {
              qb1.orWhere(`find_in_set(:${key}, company.econkind)`, { [key]: econKindMap[key] });
            } else {
              qb1.orWhere('company.econkind is null');
            }
          });
        }),
      );
    }

    // 企业性质
    if (postData?.econType?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          postData.econType.forEach((key) => {
            qb1.orWhere(`find_in_set(:${key}, company.econType)`, { [key]: key });
          });
        }),
      );
    }

    // 司库类型
    if (postData?.treasuryType?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          postData.treasuryType.forEach((key) => {
            qb1.orWhere(`find_in_set(:${key}, company.treasuryType)`, { [key]: key });
          });
        }),
      );
    }

    // 机构类型
    if (postData?.enterpriseType?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          postData.enterpriseType.forEach((key) => {
            qb1.orWhere(`find_in_set(:${key}, company.enterpriseType)`, { [key]: key });
          });
        }),
      );
    }

    // 国民行业
    if (postData?.industry?.length) {
      const params = {};
      const industrySql = [];
      for (let i = 0; i < postData.industry?.length; i++) {
        const industry = postData.industry[i];
        if (industry.i4) {
          const key = 'i4' + i;
          params[key] = industry.i4;
          industrySql.push(`company.industry4 = :${key}`);
        } else if (industry.i3) {
          const key = 'i3' + i;
          params[key] = industry.i3;
          industrySql.push(`company.industry3 = :${key}`);
        } else if (industry.i2) {
          const key = 'i2' + i;
          params[key] = industry.i2;
          industrySql.push(`company.industry2 = :${key}`);
        } else if (industry.i1) {
          const key = 'i1' + i;
          params[key] = industry.i1;
          industrySql.push(`company.industry1 = :${key}`);
        }
      }
      qb.andWhere(`(${industrySql.join(' OR ')})`, params);
    }

    // 行政地区
    if (postData?.region?.length) {
      const params = {};
      const regionSql = [];
      for (let i = 0; i < postData.region?.length; i++) {
        const region = postData.region[i];
        if (region.dt) {
          const key = 'dt' + i;
          params[key] = region.dt;
          regionSql.push(`company.district = :${key}`);
        } else if (region.ct) {
          const key = 'ct' + i;
          params[key] = region.ct;
          regionSql.push(`company.city = :${key}`);
        } else if (region.pr) {
          const key = 'pr' + i;
          params[key] = region.pr;
          regionSql.push(`company.province = :${key}`);
        }
      }
      qb.andWhere(`(${regionSql.join(' OR ')})`, params);
    }

    //过期状态
    if (postData?.expiredStatus === 1) {
      //未过期
      qb.andWhere(
        new Brackets((qb1) => {
          qb1.orWhere({ expiredDate: MoreThanOrEqual(moment().toDate()) });
          qb1.orWhere({ duration: DurationEnums.ForEver });
        }),
      );
    }
    if (postData?.expiredStatus === 2) {
      //已过期
      qb.andWhere({ expiredDate: LessThanOrEqual(moment().toDate()) });
    }

    //注册资本
    if (registcapiAmount?.length) {
      qb.andWhere(
        new Brackets((qb1) => {
          registcapiAmount.forEach((r, index) => {
            const hasMin = r.min !== undefined && r.min !== null;
            const hasMax = r.max !== undefined && r.max !== null;
            if (hasMin && hasMax) {
              qb1.orWhere(
                new Brackets((qb2) => {
                  qb2
                    .where('company.registcapiAmount >= :min' + index, { ['min' + index]: r.min })
                    .andWhere('company.registcapiAmount < :max' + index, { ['max' + index]: r.max });
                }),
              );
            } else if (hasMin) {
              qb1.orWhere('company.registcapiAmount >= :min' + index, { ['min' + index]: r.min });
            } else if (hasMax) {
              qb1.orWhere('company.registcapiAmount < :max' + index, { ['max' + index]: r.max });
            }
          });
        }),
      );
    }

    QueryBuilderHelper.applyDateRangeQuery(qb, startDateCode, 'company.startDateCode');

    //注册状态
    if (statusCode?.length) {
      qb.andWhere('company.statusCode in (:...statusCode)', { statusCode });
    }
    return qb;
  }

  async search(currentUser: RoverUser, postData: SearchInnerBlacklistModel, notUsePage = false): Promise<SearchInnerBlacklistResponse> {
    const { sortField, isSortAsc, selectAll } = postData;

    const qb = await this.getQb(currentUser, postData);

    let sortFieldUse = sortField;
    if (sortFieldUse == 'registcapi') {
      sortFieldUse = 'registcapiAmount';
    }

    if (sortField) {
      if (sortFieldUse == 'registcapiAmount' || sortFieldUse == 'startDateCode') {
        qb.addOrderBy(`company.${sortFieldUse}`, isSortAsc ? 'ASC' : 'DESC');
      } else {
        qb.addOrderBy(`blacklist.${sortFieldUse}`, isSortAsc ? 'ASC' : 'DESC');
      }
    } else {
      qb.orderBy('blacklist.createDate', 'DESC');
    }
    const blacklistIds = [];
    const labels = [];
    const departments = [];
    if (selectAll) {
      const allRaw = await qb.getMany();
      Array.prototype.push.apply(blacklistIds, uniq(allRaw.map((a) => a.id)));
      Array.prototype.push.apply(
        labels,
        uniqBy(flatten(allRaw.map((a) => a.labels)), (r: LabelEntity) => r.labelId),
      );
      Array.prototype.push.apply(
        departments,
        uniqBy(flatten(allRaw.map((a) => a.departments)), (r: DepartmentEntity) => r.departmentId),
      );
    }
    qb.select([
      'blacklist.id',
      'blacklist.orgId',
      'blacklist.comment',
      'blacklist.reason',
      'blacklist.duration',
      'blacklist.department',
      'blacklist.joinDate',
      'blacklist.createDate',
      'blacklist.updateDate',
      'blacklist.batchId',
      'blacklist.status',
      'blacklist.expiredDate',
      'blacklist.groupId',
      'blacklist.result',
      'blacklist.diligenceId',
      'blacklist.diligenceDate',
      'company',
      'labels',
      'departments',
      'creator.userId',
      'creator.name',
    ]);

    const { pageSize, pageIndex } = postData;
    if (!notUsePage) {
      qb.skip(pageSize * (pageIndex - 1)).take(pageSize);
    }
    const [data, total] = await qb.getManyAndCount();
    const resData = data.map((d) => {
      if (d.company) {
        return Object.assign(new InnerBlacklistEntity(), omit(d, ['company']), {
          companyId: d.company.companyId,
          companyName: d.company.name,
          econkind: d.company.econkind,
          province: d.company.province,
          city: d.company.city,
          district: d.company.district,
          industry1: d.company.industry1,
          industry2: d.company.industry2,
          industry3: d.company.industry3,
          industry4: d.company.industry4,
          registcapi: d.company.registcapi,
          registcapiAmount: d.company.registcapiAmount,
          startDateCode: d.company.startDateCode,
          statusCode: d.company.statusCode,
          econType: d.company.econType,
          treasuryType: d.company.treasuryType,
          enterpriseType: d.company.enterpriseType,
          creditcode: d.company.creditcode,
        });
      } else {
        return d;
      }
    });
    return {
      pageSize,
      pageIndex,
      data: resData,
      total,
      blacklistIds,
      labels,
      departments,
    };
  }

  private async aggsCompanyFields(currentUser: RoverUser, field: string) {
    const { by, userIds } = this.securityService.checkScope(currentUser, 2031);
    const { currentOrg: orgId } = currentUser;
    let sql =
      ' select distinct company.' +
      field +
      ' FROM `inner_blacklist` `inner_blacklist` LEFT JOIN `company` `company` ON `company`.`company_id`=`inner_blacklist`.`company_id` WHERE `inner_blacklist`.`org_id` = ' +
      orgId +
      ' AND `inner_blacklist`.`status` = 2';
    if (by == PermissionByEnum.USER) {
      sql += 'AND `inner_blacklist`.`create_by` in (' + userIds.join(',') + ')';
    }
    const result = await this.innerBlacklistRepo.query(sql);
    return result.map((r) => r[field]);
  }

  async remove(currentUser: RoverUser, postData: SearchInnerBlacklistModel) {
    const { currentOrg: orgId } = currentUser;
    let operatorType = OperatorTypeEnums.Remove;
    const qb = await this.getQb(currentUser, postData);
    const preBlacklists = await qb.getMany();
    if (!preBlacklists?.length) {
      return null;
    }
    const deleteIds = preBlacklists.map((m) => m.id);
    if (deleteIds.length > 1) {
      operatorType = OperatorTypeEnums.BatchRemove;
    }
    //套餐额度要还到具体dep上，如果部门没套餐则还给组织
    const depIdsGroup = groupBy(preBlacklists, (g) => g?.depId);
    await Bluebird.map(Object.keys(depIdsGroup), async (depId) => {
      const bundleCounter = await this.bundleService.getBundleCounter(currentUser, RoverBundleCounterType.InnerBlacklistQuantity, depId);
      await bundleCounter.decrease(depIdsGroup[depId].length);
    });
    //先做同步再删除，否则图数据不会删除
    await this.graphService.syncToNebula(currentUser.currentOrg, deleteIds, StreamTableEnums.InnerBlacklist, StreamOperationEnum.DELETE);
    const deleteResult = await this.innerBlacklistRepo.delete({ orgId, id: In(deleteIds) });
    await Bluebird.all([
      this.diligenceHistoryHelper.makeHistoryForUpdate(orgId),
      this.oplogService.add(operatorType, InnerBlacklistEntity, currentUser, deleteIds, preBlacklists),
      this.innerBlacklistLabelRepo.delete({ innerBlacklistId: In(deleteIds) }),
      this.innerBlacklistDepartmentRepo.delete({ innerBlacklistId: In(deleteIds) }),
    ]);
    return deleteResult;
  }

  /**
   * 计算过期时间
   * @param duration
   * @param joinDate
   */
  private async calculateExpiredDate(duration: number, joinDate: Date): Promise<Date> {
    let expiredDate: Date = null; //空表示永久
    switch (duration) {
      case DurationEnums.ForEver:
        break;
      case DurationEnums.ThreeMonth:
        expiredDate = moment(joinDate).add(3, 'M').toDate();
        break;
      case DurationEnums.SixMonth:
        expiredDate = moment(joinDate).add(6, 'M').toDate();
        break;
      case DurationEnums.AYear:
        expiredDate = moment(joinDate).add(1, 'y').toDate();
        break;
      case DurationEnums.TwoYears:
        expiredDate = moment(joinDate).add(2, 'y').toDate();
        break;
      case DurationEnums.ThreeYears:
        expiredDate = moment(joinDate).add(3, 'y').toDate();
        break;
      case DurationEnums.FiveYears:
        expiredDate = moment(joinDate).add(5, 'y').toDate();
        break;
    }
    return expiredDate;
  }

  async aggregation(currentUser: RoverUser) {
    const { currentOrg: orgId } = currentUser;
    try {
      const { by, userIds } = this.securityService.checkScope(currentUser, 2041);
      return this.departmentService.getDepartmentList(orgId, DepartmentTypeEnum.InnerBlacklist, by, userIds);
    } catch (error) {
      if (error instanceof ForbiddenException) {
        return [];
      }
      throw error;
    }
  }

  public async changeBatchBlacklist(currentUser: RoverUser, postData: GroupChangeRequest): Promise<AffectedResponse> {
    const affectedResponse: AffectedResponse = new AffectedResponse();
    const { groupId } = postData;
    affectedResponse.affected = 0;
    const preBlacklists = await this.innerBlacklistRepo.findByIds(postData.ids);
    //1.判断分组是否存在
    //2.如果 groupId为-1，则表示移动分组到默认分组
    const updateData = {
      groupId,
      orgId: currentUser?.currentOrg,
      depId: currentUser?.departments?.[0],
    };
    if (groupId === -1) {
      updateData.groupId = null;
    } else {
      const resultGroup = await this.groupRepo.findOne({ groupId: postData.groupId });
      if (!resultGroup) {
        throw new BadRequestException(RoverExceptions.GroupRelated.Group.NotFound);
      }
    }
    //3.批量更新customer分组信息
    const updateResult = await this.innerBlacklistRepo
      .createQueryBuilder()
      .update(InnerBlacklistEntity)
      .set(updateData)
      .where('id in (:ids)', { ids: postData.ids })
      .execute();
    const currentBlacklists = await this.innerBlacklistRepo.findByIds(postData.ids);
    await Bluebird.all([
      this.oplogService.add(OperatorTypeEnums.Edit, InnerBlacklistEntity, currentUser, postData.ids, preBlacklists, currentBlacklists),
      this.diligenceHistoryHelper.makeHistoryForUpdate(currentUser.currentOrg),
    ]);
    affectedResponse.affected = updateResult?.affected;
    return affectedResponse;
  }

  /**
   * 批量追加标签
   * @param currentUser
   * @param postData
   */
  public async customerBatchModify(currentUser: RoverUser, postData: LabelModifyRequest): Promise<AffectedResponse> {
    const affectedResponse: AffectedResponse = new AffectedResponse();
    affectedResponse.affected = 0;
    if (postData?.labelIds?.length === 0) {
      //全部删除所有标签
      await this.innerBlacklistLabelRepo.delete({ innerBlacklistId: In(postData.ids) });
      affectedResponse.affected = postData?.ids?.length || 0;
      return affectedResponse;
    }
    //1.判断标签是否存在
    const total = await this.labelRepo.count({ labelId: In(postData.labelIds) });
    if (total === 0 || total < postData?.labelIds?.length) {
      throw new BadRequestException(RoverExceptions.GroupRelated.Label.NotFound);
    }
    //2.批量更新
    const entities = await this.innerBlacklistRepo.find({
      id: In(postData.ids),
      orgId: currentUser?.currentOrg,
    });
    const labelKV = await this.labelService.getLabelKV(currentUser?.currentOrg, 3);
    if (entities) {
      await Bluebird.map(entities, async (entity) => {
        const entityLabels = await this.innerBlacklistLabelRepo.find({ innerBlacklistId: entity.id });
        const dbLabelIds = entityLabels.map((t) => t.labelId);
        if (difference(postData.labelIds, dbLabelIds)) {
          const preLabel = labelKV[entity.id] || '-';
          // 有不同的labelId,先移除标签
          await this.innerBlacklistLabelRepo.delete({ innerBlacklistId: entity.id });
          await this.innerBlacklistLabelRepo.save(
            postData.labelIds.map((i) =>
              Object.assign(new InnerBlacklistLabelEntity(), {
                labelId: i,
                innerBlacklistId: entity.id,
              }),
            ),
          );
          await this.oplogService.add(OperatorTypeEnums.Edit, InnerBlacklistEntity, currentUser, postData.ids, [entity], [], preLabel);
        }
      });
      affectedResponse.affected = entities.length;
    }

    return affectedResponse;
  }

  public async match(currentUser: RoverUser, req: MatchCustomerModel) {
    const { companyIds } = req;
    const qb = this.innerBlacklistRepo
      .createQueryBuilder('blacklist')
      .select(['blacklist'])
      .andWhere('blacklist.orgId = :orgId', { orgId: currentUser.currentOrg })
      .andWhere('blacklist.status = :status', { status: BatchStatusEnums.Done })
      .andWhere('blacklist.companyId in (:...companyIds)', { companyIds });
    // .skip(pageSize * (pageIndex - 1))
    // .take(pageSize);
    const [data] = await qb.getManyAndCount();
    return data;
  }

  public async checkCompany(orgId, companyId: string) {
    const entity = await this.innerBlacklistRepo.findOne({ orgId, status: BatchStatusEnums.Done, companyId });
    if (!entity) {
      return {};
    }
    return entity;
  }

  /**
   * 删除指定第三方列表的公司添加到内部黑名单中
   * @param currentUser
   * @param postData
   */
  async removeCustomer2Blacklist(currentUser: RoverUser, postData: BatchCreateInnerBlacklist): Promise<RemoveCustomerResponse> {
    const response = new RemoveCustomerResponse();
    const { comment, duration, reason, department, joinDate, expiredDate, groupId, labelIds, departmentNames } = postData;
    const customers = await this.customerRepo.find({
      customerId: In(postData.customerIds),
      orgId: currentUser.currentOrg,
      status: BatchStatusEnums.Done,
    });
    if (!customers?.length) {
      throw new BadParamsException({ message: '公司不存在!', ...CommonExceptions.Common.Request.NotFound });
    }
    const blacklists = await this.innerBlacklistRepo.find({
      companyId: In(customers.map((c) => c.companyId)),
      orgId: currentUser.currentOrg,
      status: BatchStatusEnums.Done,
    });
    const blacklistCompanyIds = blacklists?.map((b) => b.companyId);
    //取customer中未添加到黑名单的公司添加
    let effectCustomers: CustomerEntity[];
    let sameCompanyList: CompanyBase[];
    if (blacklistCompanyIds?.length) {
      effectCustomers = customers.filter((c) => !blacklistCompanyIds?.includes(c.companyId));
      sameCompanyList = customers
        .filter((c) => blacklistCompanyIds?.includes(c.companyId))
        ?.map((e) =>
          Object.assign(new CompanyBase(), {
            companyId: e.companyId,
            companyName: e.name,
            customerId: e.customerId,
          }),
        );
    } else {
      effectCustomers = customers;
    }
    if (!effectCustomers?.length) {
      if (sameCompanyList?.length) {
        //所选公司都已存在黑名单列表中
        await this.customerService.remove(currentUser, Object.assign(new SearchInnerBlacklistModel(), { ids: sameCompanyList?.map((e) => e.customerId) }));
        return Object.assign(response, {
          sameCompanyList: sameCompanyList?.map((e) =>
            Object.assign(new CompanyBase(), {
              companyId: e.companyId,
              companyName: e.companyName,
            }),
          ),
        });
      }
      throw new BadParamsException({ message: '公司不存在!', ...CommonExceptions.Common.Request.NotFound });
    }

    //待添加的blacklist，添加innerBlacklist
    const batchBlacklist: CreateInnerBlacklistModel[] = [];
    effectCustomers?.forEach((e) => {
      const createBlacklistModel: CreateInnerBlacklistModel = {
        companyId: e.companyId,
        companyName: e.name,
        comment,
        duration,
        reason,
        department,
        joinDate,
        expiredDate,
        groupId,
        labelIds,
        departmentNames,
      };
      batchBlacklist.push(createBlacklistModel);
    });

    await Bluebird.map(batchBlacklist, (bs) => this.add(currentUser, bs), { concurrency: 5 });
    //批量移动成功
    const blacklistResponse = await this.innerBlacklistRepo.find({
      companyId: In(batchBlacklist?.map((b) => b.companyId)),
      orgId: currentUser.currentOrg,
      status: BatchStatusEnums.Done,
    });
    const effectCustomerIds = effectCustomers?.map((e) => e.customerId);
    const sameCustomerIds = sameCompanyList?.map((e) => e.customerId);
    //黑名单添加成功，删除customer
    const removeArr = [];
    if (effectCustomerIds?.length) {
      removeArr.push(this.customerService.remove(currentUser, Object.assign(new SearchInnerBlacklistModel(), { ids: effectCustomerIds })));
    }
    if (sameCustomerIds?.length) {
      removeArr.push(this.customerService.remove(currentUser, Object.assign(new SearchInnerBlacklistModel(), { ids: sameCustomerIds })));
    }
    await Bluebird.all(removeArr);
    return Object.assign(response, {
      sameCompanyList,
      effectedRows: blacklistResponse,
    });
  }

  public async aggsForSearch(user, postData: InnerBlackAggsRequest): Promise<AggsResponse[]> {
    let qb: SelectQueryBuilder<InnerBlacklistEntity>;
    try {
      qb = await this.getQb(user, postData.query);
      if (!qb) {
        return [];
      }
    } catch (e) {
      throw e;
    }
    const aggsField = postData.aggsField;
    if (!aggsField) {
      return [];
    }
    switch (aggsField) {
      case 'riskLevel':
        qb.select('blacklist.result', 'fieldValue').addSelect('COUNT(distinct(blacklist.id))', 'count').groupBy('blacklist.result');
        break;
      case 'group':
        qb.select('blacklist.groupId', 'fieldValue').addSelect('COUNT(distinct(blacklist.id))', 'count').groupBy('blacklist.groupId');
        break;
      case 'operator':
        qb.select('creator.userId', 'fieldValue').addSelect('COUNT(distinct(blacklist.id))', 'count').groupBy('creator.userId');
        break;
      case 'department':
        qb.select('departments.departmentId', 'fieldValue').addSelect('COUNT(distinct(blacklist.id))', 'count').groupBy('departments.departmentId');
        break;
      case 'label':
        // @ts-ignore
        qb.select('labels.labelId', 'fieldValue').addSelect('COUNT(distinct(blacklist.id))', 'count').groupBy('labels.labelId');
        break;
      case 'econType': {
        // qb.addGroupBy('company.econkind');
        // @ts-ignore
        // qb.select('company.econkind', 'fieldValue').addSelect('COUNT(customer.customerId)', 'count').groupBy('company.econkind');
        qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`company`.`econ_type`, ',', numbers.n), ',', -1)", 'fieldValue')
          .addSelect('COUNT(distinct(blacklist.id))', 'count')
          .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
          .groupBy('fieldValue');
        break;
      }
      case 'treasuryType': {
        // @ts-ignore
        qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`company`.`treasury_type`, ',', numbers.n), ',', -1)", 'fieldValue')
          .addSelect('COUNT(distinct(blacklist.id))', 'count')
          .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
          .groupBy('fieldValue');
        break;
      }
      case 'enterpriseType': {
        // qb.addGroupBy('company.econkind');
        // @ts-ignore
        // qb.select('company.econkind', 'fieldValue').addSelect('COUNT(customer.customerId)', 'count').groupBy('company.econkind');
        qb.select("SUBSTRING_INDEX(SUBSTRING_INDEX(`company`.`enterprise_type`, ',', numbers.n), ',', -1)", 'fieldValue')
          .addSelect('COUNT(distinct(blacklist.id))', 'count')
          .leftJoin('(SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5)', 'numbers', '1=1')
          .groupBy('fieldValue');
        break;
      }
      case 'statusCode': {
        // qb.addGroupBy('company.econkind');
        // @ts-ignore
        qb.select('company.statusCode', 'fieldValue').addSelect('COUNT(distinct(blacklist.id))', 'count').groupBy('company.statusCode');
        break;
      }
    }
    const rows = await qb.getRawMany();
    switch (aggsField) {
      case 'department': {
        const departments = await this.departmentRepo.findByIds(rows.map((r) => r.fieldValue));
        return compact(
          rows.map((r) => {
            if (!r.fieldValue) {
              r.fieldValue = -1;
              r.fieldLabel = '未分部门';
            } else {
              const department = departments.find((d) => d.departmentId == r.fieldValue);
              r.fieldLabel = department?.name || '';
            }
            r.count = Number(r.count);
            return r;
          }),
        );
      }
      case 'label': {
        const labels = await this.labelRepo.findByIds(rows.map((r) => r.fieldValue));
        return rows.map((r) => {
          if (!r.fieldValue) {
            r.fieldValue = -1;
            r.fieldLabel = '无标签';
          } else {
            const label = labels.find((d) => d.labelId == r.fieldValue);
            r.fieldLabel = label?.name || '';
          }
          r.count = Number(r.count);
          return r;
        });
      }
      case 'operator': {
        const operators = await this.userRepo.findByIds(rows.map((r) => r.fieldValue));
        return compact(
          rows.map((r) => {
            if (!r.fieldValue) return;
            const operator = operators.find((d) => d.userId == r.fieldValue);
            r.fieldLabel = operator?.name || '';
            r.count = Number(r.count);
            return r;
          }),
        );
      }
      case 'group': {
        const groups = await this.groupRepo.find({
          where: {
            orgId: user.currentOrg,
            groupType: GroupType.InnerBlacklistGroup,
          },
          order: { order: 'DESC' },
        });
        const result = [];
        const noGroupRow = rows.find((r) => r.fieldValue == null);
        result.push({
          fieldLabel: '未分组',
          fieldValue: '-1',
          count: Number(noGroupRow?.count) || 0,
        });
        groups.forEach((g) => {
          const row = rows.find((r) => g.groupId == r.fieldValue);
          result.push({
            fieldLabel: g.name,
            fieldValue: g.groupId + '',
            count: Number(row?.count) || 0,
          });
        });
        return result;
      }
      default:
    }
    return rows.map((r) =>
      Object.assign(new AggsResponse(), {
        count: Number(r.count),
        ...r,
      }),
    );
  }

  async updateCompanyDiligenceInfo(orgId: number, companyIds?: string[]) {
    this.logger.log(`开始更新客户的调查信息，orgId=${orgId}, companyIds=${companyIds}`);
    let count = 0;
    let potentialLoopTimes = 0;
    const pageSize = 300;
    do {
      const qb = this.innerBlacklistRepo
        .createQueryBuilder('company')
        .where('company.orgId = :orgId', { orgId })
        .andWhere('company.status = :status', { status: BatchStatusEnums.Done })
        .andWhere('( company.result  is null or company.result = -1) ');
      if (companyIds?.length) {
        qb.andWhere('company.companyId in (:...companyIds)', { companyIds });
      }
      const items = await qb.take(pageSize).getMany();
      count += items.length;
      if (items.length) {
        const sql = `SELECT * FROM (SELECT company_id, org_id, id as diligenceId, result, create_date, update_date, ROW_NUMBER() OVER (PARTITION BY company_id ORDER BY id DESC) AS row_num FROM due_diligence WHERE org_id = ${orgId} and company_id IN (${items
          .map((i) => `'${i.companyId}'`)
          .join(',')}) ORDER BY id DESC) a WHERE a.row_num = 1;`;
        const rows = await this.innerBlacklistRepo.query(sql);
        if (!rows.length) {
          potentialLoopTimes++;
        } else {
          await Bluebird.map(
            rows,
            async (row) => {
              const item = items.find((i) => i.companyId === row.company_id);
              if (item) {
                return this.innerBlacklistRepo.update(item.id, {
                  result: row.result,
                  diligenceId: row.diligenceId,
                  diligenceDate: row.update_date,
                });
              }
            },
            { concurrency: 10 },
          );
        }
        // 因为 latest_diligence_result 可能会被查询过程中修改，所以需要防止死循环
        // 如果连续两次的客户列表都没有找到任何尽调记录
        if (!items.length || items.length < pageSize || potentialLoopTimes > 1) {
          break;
        }
      } else {
        break;
      }
    } while (true);

    return count;
  }
}
