import { HttpService } from '@nestjs/axios';
import { Test, TestingModule } from '@nestjs/testing';
import { BatchService } from 'apps/batch/service/batch.service';
import { BatchBaseHelper } from 'apps/batch/service/helper/batch.base.helper';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { BatchTypeEnums } from 'libs/enums/batch/BatchTypeEnums';
import { EntityManager, getManager } from 'typeorm';
import { OpenapiBatchDiligenceRequest } from '../../libs/model/openapi/diligence/OpenapiBatchDiligenceRequest';
import { AppTestModule } from '../app/app.test.module';
import { CompanySearchService } from '../company/company-search.service';
import { AuthRequest } from './model';
import { OpenApiController } from './openapi.controller';
import { OpenApiModule } from './openapi.module';
import { OpenApiService } from './openapi.service';

jest.setTimeout(30000);
// jest.useFakeTimers();

describe.skip('模拟openapi接口调用', () => {
  let apiService: OpenApiService;
  let httpService: HttpService;
  let entityManager: EntityManager;
  // eslint-disable-next-line @typescript-eslint/no-inferrable-types
  const env: string = 'test';

  const accessKey = env == 'prod' ? 'Vg0hmS5qCsPFtBIL' : 'QGokJCMgoOAUhx8TOf';
  const accessSecret = env == 'prod' ? '9ANAm9V0cv9aNwiEJSZ' : 'QpVdIvKd6p5LLf3S';
  const domain = env == 'prod' ? 'https://kys.qcc.com' : 'http://kys.test.greatld.com';
  const phone = env == 'prod' ? '18626272086' : '18626272086';

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, OpenApiModule],
    }).compile();
    apiService = module.get<OpenApiService>(OpenApiService);
    httpService = module.get<HttpService>(HttpService);
    entityManager = getManager();
  });

  afterAll(async () => {
    await entityManager.connection.close();
  });

  afterEach(async () => {
    // // console.log('afterEach...');
  });

  it('页面嵌入-招标排查排查详情页', async () => {
    const params = Object.assign(new AuthRequest(), {
      phone,
      accessKey,
      accessSecret,
      timestamp: Date.now(),
    });

    // const url1 = domain + '/rover/openapi/auth/jwt';

    const sign = await apiService.getSign(params);
    // params.sign = sign

    const url = `${domain}/rover/account/loginRedirect?phone=${params.phone}&accessKey=${params.accessKey}&timestamp=${params.timestamp}&sign=${sign}&redirectUrl=${domain}/external/supplier/bidding-investigation/detail?id=230385`;

    // console.log(url);
    expect(url);

    const sign2 = encodeURIComponent(sign);
    console.log(
      `${domain}/rover/account/loginRedirect?phone=${params.phone}&accessKey=${params.accessKey}&timestamp=${params.timestamp}&sign=${sign2}&redirectUrl=${domain}/external/supplier/bidding-investigation/detail?id=230385`,
    );
  });

  it('招标排查 bidding/scanRisk', async () => {
    // test 环境

    const params = Object.assign(new AuthRequest(), {
      phone,
      accessKey,
      accessSecret,
      timestamp: Date.now(),
    });

    const url1 = domain + '/rover/openapi/auth/jwt';

    const sign = await apiService.getSign(params);
    params.sign = sign;
    const jwt = await httpService.axiosRef
      .request({
        method: 'POST',
        url: url1,
        data: params,
      })
      .then((res) => {
        return res?.data;
      })
      .catch((err) => {
        return err;
      });
    // const jwt = await apiService.getJwt(params);
    // console.log('jwt:', jwt);

    try {
      const url2 = domain + '/rover/openapi/bidding/scanRisk';
      const result = await httpService.axiosRef
        .request({
          method: 'POST',
          url: url2,
          headers: {
            authorization: jwt,
          },
          data: {
            companyKeys: ['桐庐富尔克医疗器械有限公司', '杭州康乘医疗器械有限公司', '杭州富阳天狼医疗器械有限公司'],
            projectName: '苏康 串激电机',
            projectNo: '苏康 串激电机',
          },
        })
        .then((res) => {
          return res?.data;
        });
      // // console.log(result);
    } catch (error) {
      // // console.log(error);
      expect(error).toBeUndefined();
    }
  });

  it('准入排查 diligence/scanRisk', async () => {
    // test 环境

    const params = Object.assign(new AuthRequest(), {
      phone,
      accessKey,
      accessSecret,
      timestamp: Date.now(),
    });

    const url1 = domain + '/rover/openapi/auth/jwt';

    const sign = await apiService.getSign(params);
    params.sign = sign;
    const jwt = await httpService.axiosRef
      .request({
        method: 'POST',
        url: url1,
        data: params,
      })
      .then((res) => {
        return res?.data;
      })
      .catch((err) => {
        return err;
      });
    // const jwt = await apiService.getJwt(params);
    // console.log('jwt:', jwt);

    try {
      const url2 = domain + '/rover/openapi/diligence/scanRisk';
      const result = await httpService.axiosRef
        .request({
          method: 'POST',
          url: url2,
          headers: {
            authorization: jwt,
          },
          data: {
            companyKey: '乐视网信息技术（北京）股份有限公司',
          },
        })
        .then((res) => {
          return res?.data;
        });
      // // console.log(result);
    } catch (error) {
      // // console.log(error);
      expect(error).toBeUndefined();
    }
  });

  it('添加内部黑名单 blacklist/inner', async () => {
    // test 环境
    const params = Object.assign(new AuthRequest(), {
      phone,
      accessKey,
      accessSecret,
      timestamp: Date.now(),
    });

    const url1 = domain + '/rover/openapi/auth/jwt';

    const sign = await apiService.getSign(params);
    params.sign = sign;
    const jwt = await httpService.axiosRef
      .request({
        method: 'POST',
        url: url1,
        data: params,
      })
      .then((res) => {
        return res?.data;
      })
      .catch((err) => {
        return err;
      });
    // const jwt = await apiService.getJwt(params);
    // console.log('jwt:', jwt);

    try {
      const url2 = domain + '/rover/openapi/blacklist/inner';
      const result = await httpService.axiosRef
        .request({
          method: 'POST',
          url: url2,
          headers: {
            authorization: jwt,
          },
          data: {
            companyKey: '乐视网信息技术（北京）股份有限公司',
          },
        })
        .then((res) => {
          return res?.data;
        });
      // // console.log(result);
    } catch (error) {
      // // console.log(error);
      expect(error).toBeDefined();
    }
  });

  it('移出内部黑名单 blacklist/inner/remove', async () => {
    // test 环境
    const params = Object.assign(new AuthRequest(), {
      phone,
      accessKey,
      accessSecret,
      timestamp: Date.now(),
    });

    const url1 = domain + '/rover/openapi/auth/jwt';

    const sign = await apiService.getSign(params);
    params.sign = sign;
    const jwt = await httpService.axiosRef
      .request({
        method: 'POST',
        url: url1,
        data: params,
      })
      .then((res) => {
        return res?.data;
      })
      .catch((err) => {
        return err;
      });
    // const jwt = await apiService.getJwt(params);
    // console.log('jwt:', jwt);

    try {
      const url2 = domain + '/rover/openapi/blacklist/inner/remove';
      const result = await httpService.axiosRef
        .request({
          method: 'POST',
          url: url2,
          headers: {
            authorization: jwt,
          },
          data: {
            ids: [46924],
          },
        })
        .then((res) => {
          return res?.data;
        });
      // // console.log(result);
    } catch (error) {
      // // console.log(error);
      // expect(error).toBeUndefined();
    }
  });
});

describe('批量尽调扫描测试', () => {
  let batchBaseHelperService: BatchBaseHelper;
  let batchService: BatchService;
  let controller: OpenApiController;
  let companySearchService: CompanySearchService;
  let module: TestingModule;
  // 测试用户
  const [testOrgId, testUserId] = generateUniqueTestIds('openapi.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, OpenApiModule],
    }).compile();

    batchBaseHelperService = module.get<BatchBaseHelper>(BatchBaseHelper);
    batchService = module.get<BatchService>(BatchService);
    controller = module.get<OpenApiController>(OpenApiController);
    companySearchService = module.get<CompanySearchService>(CompanySearchService);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  afterAll(async () => {
    await module.close();
  });

  it('应成功创建批量尽调任务', async () => {
    // Arrange
    const postData: OpenapiBatchDiligenceRequest = {
      companyKeys: ['企查查科技股份有限公司', '万得信息技术股份有限公司', '乐视'],
      skipError: 0,
    };

    const mockBatchResult = {
      batchId: 'test-batch-id',
      batchInfo: 'test batch info',
      orgId: testOrgId,
      createDate: new Date(),
      endDate: new Date(),
      status: 1,
      creator: testUserId,
    };

    jest.spyOn(batchBaseHelperService, 'checkBatchCount').mockResolvedValue(undefined);
    jest.spyOn(controller as any, 'toBatchDiligenceRequest').mockResolvedValue({
      data: [{ companyName: '企查查科技股份有限公司' }, { companyName: '万得信息技术股份有限公司' }],
      settingId: 1,
    });
    jest.spyOn(batchService, 'createBatchDiligenceTask').mockResolvedValue(mockBatchResult);

    // Act
    const result = await controller.scanBatchDiligenceResult({ user: testUser }, postData);

    // Assert
    expect(result).toBeDefined();
    expect(result.batchId).toBe(mockBatchResult.batchId);
    expect(result.recordCount).toBe(2);
    expect(result.allCount).toBe(3);
    expect(batchBaseHelperService.checkBatchCount).toHaveBeenCalledWith(testUser, BatchBusinessTypeEnums.Diligence_ID);
    expect(batchService.createBatchDiligenceTask).toHaveBeenCalledWith(
      testUser,
      expect.any(Array),
      BatchBusinessTypeEnums.Diligence_ID,
      BatchTypeEnums.Import,
      { settingId: 1 },
    );
  });

  it('当用户有执行中任务时应抛出异常', async () => {
    // Arrange
    const postData: OpenapiBatchDiligenceRequest = {
      companyKeys: ['企查查科技股份有限公司'],
      settingId: 1,
    };

    jest.spyOn(batchBaseHelperService, 'checkBatchCount').mockRejectedValue(new Error('已有执行中的任务'));

    // Act & Assert
    await expect(controller.scanBatchDiligenceResult({ user: testUser }, postData)).rejects.toThrow('已有执行中的任务');
  });

  it('应正确处理公司名称的全角转换', async () => {
    // Arrange
    const postData: OpenapiBatchDiligenceRequest = {
      companyKeys: ['企查查科技股份有限公司', '杭州富阳天狼医疗器械有限公司'],
      settingId: 1,
    };

    const mockBatchResult = {
      batchId: 'test-batch-id',
      batchInfo: 'test batch info',
      orgId: testOrgId,
      createDate: new Date(),
      endDate: new Date(),
      status: 1,
      creator: testUserId,
    };

    jest.spyOn(batchBaseHelperService, 'checkBatchCount').mockResolvedValue(undefined);
    const toBatchDiligenceRequestSpy = jest.spyOn(controller as any, 'toBatchDiligenceRequest').mockResolvedValue({
      data: [{ companyName: '企查查科技股份有限公司' }, { companyName: '杭州富阳天狼医疗器械有限公司' }],
      settingId: 1,
    });
    jest.spyOn(batchService, 'createBatchDiligenceTask').mockResolvedValue(mockBatchResult);

    // Act
    await controller.scanBatchDiligenceResult({ user: testUser }, postData);

    // Assert
    expect(toBatchDiligenceRequestSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        companyKeys: expect.arrayContaining([expect.any(String), expect.any(String)]),
      }),
    );
  });

  it('当skipError=0且存在不匹配的企业时应抛出异常', async () => {
    // Arrange
    const postData: OpenapiBatchDiligenceRequest = {
      companyKeys: ['不存在的公司', '企查查科技股份有限公司'],
      skipError: 0,
    };

    jest.spyOn(batchBaseHelperService, 'checkBatchCount').mockResolvedValue(undefined);
    jest.spyOn(companySearchService, 'matchCompanyInfo').mockResolvedValue({
      matchedCompanyInfos: [{ id: '123', name: '企查查科技股份有限公司', t_type: '0', isHide: false, b2bproduct: [], b2bproductcategory: [] }],
      unmatchedNames: ['不存在的公司'],
      unsupported: [],
      matchedNames: ['企查查科技股份有限公司'],
    });

    // Act & Assert
    expect(controller.scanBatchDiligenceResult({ user: testUser }, postData)).rejects.toThrow('不匹配的企业名称或统一社会信用代码');
  });

  it('当skipError=0且存在不支持的企业时应抛出异常', async () => {
    // Arrange
    const postData: OpenapiBatchDiligenceRequest = {
      companyKeys: ['企查查科技股份有限公司', '不支持的企业'],
      skipError: 0,
    };

    jest.spyOn(batchBaseHelperService, 'checkBatchCount').mockResolvedValue(undefined);
    jest.spyOn(companySearchService, 'matchCompanyInfo').mockResolvedValue({
      matchedCompanyInfos: [{ id: '123', name: '企查查科技股份有限公司', t_type: '0', isHide: false, b2bproduct: [], b2bproductcategory: [] }],
      unmatchedNames: [],
      unsupported: ['不支持的企业'],
      matchedNames: ['企查查科技股份有限公司'],
    });

    // Act & Assert
    expect(controller.scanBatchDiligenceResult({ user: testUser }, postData)).rejects.toThrow('不支持的企业');
  });

  it('当skipError=1时应忽略不匹配和不支持的企业', async () => {
    // Arrange
    const postData: OpenapiBatchDiligenceRequest = {
      companyKeys: ['企查查科技股份有限公司', '不存在的公司', '不支持的企业'],
      skipError: 1,
    };

    const mockBatchResult = {
      batchId: 'test-batch-id',
      batchInfo: 'test batch info',
      orgId: testOrgId,
      createDate: new Date(),
      endDate: new Date(),
      status: 1,
      creator: testUserId,
    };

    jest.spyOn(batchBaseHelperService, 'checkBatchCount').mockResolvedValue(undefined);
    jest.spyOn(companySearchService, 'matchCompanyInfo').mockResolvedValue({
      matchedCompanyInfos: [{ id: '123', name: '企查查科技股份有限公司', t_type: '0', isHide: false, b2bproduct: [], b2bproductcategory: [] }],
      unmatchedNames: ['不存在的公司'],
      unsupported: ['不支持的企业'],
      matchedNames: ['企查查科技股份有限公司'],
    });
    jest.spyOn(batchService, 'createBatchDiligenceTask').mockResolvedValue(mockBatchResult);

    // Act
    const result = await controller.scanBatchDiligenceResult({ user: testUser }, postData);

    // Assert
    expect(result).toBeDefined();
    expect(result.batchId).toBe(mockBatchResult.batchId);
    expect(result.recordCount).toBe(1); // 只有一个匹配的公司
    expect(result.allCount).toBe(3); // 原始请求中的所有公司数量
  });
});
