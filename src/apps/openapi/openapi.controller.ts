import { BadRequestException, Body, Controller, Get, Param, ParseIntPipe, Post, Put, Query, Request, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { OpenApiService } from './openapi.service';
import { OpenApiJwtGuard } from 'libs/guards/openapi.jwt.guard';
import { AuthRequest, ModelSummaryDTO } from './model';
import { AggsResponse, IdsParams, RoverUser } from 'libs/model/common';
import { OpenapiBiddingRequest } from '../../libs/model/openapi/bidding/OpenapiBiddingRequest';
import { DiligenceBiddingRequest } from '../../libs/model/bidding/DiligenceBiddingRequest';
import { JointBiddingAnalysisModel } from '../../libs/model/tender/JointBiddingAnalysisModel';
import { CompanySearchService } from '../company/company-search.service';
import { chunk, isEmpty, omit, pick } from 'lodash';
import { DimensionLevel2Enums } from '../../libs/enums/diligence/DimensionLevel2Enums';
import { OpenapiDiligenceRequest } from '../../libs/model/openapi/diligence/OpenapiDiligenceRequest';
import { EvaluationService } from '../diligence/evaluation/evaluation.service';
import { GetDiligenceResultParams } from '../../libs/model/diligence/pojo/req&res/GetDiligenceResultParams';
import { CreateCustomerModel } from '../../libs/model/customer/CreateCustomerModel';
import { StreamTableEnums } from '../../libs/enums/data/StreamTableEnums';
import { StreamOperationEnum } from '../../libs/enums/data/StreamOperationEnum';
import { OpenapiCreateCustomerRequest } from '../../libs/model/openapi/customer/OpenapiCreateCustomerRequest';
import { CustomerService } from '../customer/customer.service';
import { RoverGraphService } from '../data/source/rover.graph.service';
import { SecurityService } from '../../libs/config/security.service';
import { InjectRepository } from '@nestjs/typeorm';
import { GroupsEntity } from '../../libs/entities/GroupsEntity';
import { In, Repository } from 'typeorm';
import { LabelEntity } from '../../libs/entities/LabelEntity';
import { CustomerEntity } from '../../libs/entities/CustomerEntity';
import { CreateInnerBlacklistModel } from '../../libs/model/blacklist/CreateInnerBlacklistModel';
import { OpenapiCreateBlacklistRequest } from '../../libs/model/openapi/blacklist/OpenapiCreateBlacklistRequest';
import { BlacklistInnerService } from '../blacklist/blacklist.inner.service';
import { InnerBlacklistEntity } from '../../libs/entities/InnerBlacklistEntity';
import { OpenapiCreatePersonRequest } from '../../libs/model/openapi/person/OpenapiCreatePersonRequest';
import { CreatePersonModel, Relative } from '../../libs/model/person/CreatePersonModel';
import { PersonService } from '../person/person.service';
import * as Bluebird from 'bluebird';
import { PersonEntity } from '../../libs/entities/PersonEntity';
import { OpenapiBiddingRequestV2 } from '../../libs/model/openapi/bidding/OpenapiBiddingRequestV2';
import { RoverExceptions } from '../../libs/exceptions/exceptionConstants';
import { SettingsService } from '../settings/settings.service';
import { SearchOrgSettingParam } from '../../libs/model/settings/SearchOrgSettingParam';
import { SettingTypeEnums } from '../../libs/model/settings/SettingTypeEnums';
import { toFullWidth } from '../../libs/utils/string.utils';
import { SearchCustomerModel } from '../../libs/model/customer/SearchCustomerModel';
import { SearchInnerBlacklistModel } from '../../libs/model/blacklist/SearchInnerBlacklistModel';
import { SearchPersonModel } from '../../libs/model/person/SearchPersonModel';
import { MonitorGroupCompanyEntity } from '../../libs/entities/MonitorGroupCompanyEntity';
import { AddCompanyRequest } from '../../libs/model/monitor/request/AddCompanyRequest';
import { MonitorCompanyService } from '../monitor/monitor.company.service';
import { SearchCompanyRequest } from '../../libs/model/monitor/request/SearchCompanyRequest';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { OpenapiCreateMonitorRequest } from '../../libs/model/openapi/monitor/OpenapiCreateMonitorRequest';
import { BatchService } from '../batch/service/batch.service';
import { allowType } from '../../libs/constants/common';
import { BiddingDiligenceService } from '../bidding/service/bidding.diligence.service';
import { BiddingCommonService } from '../bidding/service/bidding.common.service';
import { OpenapiBatchDiligenceRequest, OpenapiBatchDiligenceResultRequest } from '../../libs/model/openapi/diligence/OpenapiBatchDiligenceRequest';
import { BatchDiligenceRequest } from '../../libs/model/batch/po/parse/ParsedRecordBase';
import { BatchBusinessTypeEnums } from '../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchTypeEnums } from '../../libs/enums/batch/BatchTypeEnums';
import { DiligenceAnalyzeService } from '../diligence/analyze/diligence.analyze.service';
import { MonitorGroupService } from '../monitor/monitor.group.service';
import { InnerBlackAggsRequest } from '../../libs/model/blacklist/InnerBlackAggsRequest';
import { Throttle } from '@nestjs/throttler';
import { OpenApiUserThrottlerGuard } from 'libs/guards/OpenApiUserThrottlerGuard';
import { GetHitDetailsParamBase } from 'libs/model/diligence/pojo/req&res/details/GetHitDetailsParam';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/pojo/req&res/details/request';
import { DimensionDetailService } from 'apps/diligence/details/dimension.detail.service';
import { AnalyzedCompanySearchedRequest } from 'apps/diligence/analyze/po/AnalyzedCompanySearchedRequest';
import { BatchBaseHelper } from '../batch/service/helper/batch.base.helper';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { ExportConditionRequest } from 'apps/batch/message.handler/export/model/ExportRecordPO';
import { BatchEntity } from 'libs/entities/BatchEntity';

@Controller('openapi')
@ApiTags('openapi')
export class OpenApiController {
  private readonly logger: Logger = QccLogger.getLogger(OpenApiController.name);

  constructor(
    private readonly openApiService: OpenApiService,
    private readonly biddingDiligenceService: BiddingDiligenceService,
    private readonly biddingCommonService: BiddingCommonService,
    private readonly companySearchService: CompanySearchService,
    private readonly evaluationService: EvaluationService,
    private readonly customerService: CustomerService,
    private readonly graphService: RoverGraphService,
    private readonly securityService: SecurityService,
    private readonly innerBlacklistService: BlacklistInnerService,
    private readonly personService: PersonService,
    private readonly settingService: SettingsService,
    private readonly monitorCompanyService: MonitorCompanyService,
    private readonly monitorGroupService: MonitorGroupService,
    private readonly batchService: BatchService,
    private readonly diligenceAnalyzeService: DiligenceAnalyzeService,
    private readonly dimensionDetailService: DimensionDetailService,
    private readonly batchBaseHelperService: BatchBaseHelper,
    @InjectRepository(GroupsEntity) private readonly groupRepo: Repository<GroupsEntity>,
    @InjectRepository(MonitorGroupEntity) private readonly monitorGroupRepo: Repository<MonitorGroupEntity>,
    @InjectRepository(LabelEntity) private readonly labelRepo: Repository<LabelEntity>,
  ) {}

  @Post('auth/jwt')
  async getJwt(@Body() request: AuthRequest) {
    return this.openApiService.getJwt(request);
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Throttle(50, 60)
  @Post('diligence/pdf/:diligenceId')
  @ApiOperation({ summary: '准入排查报告导出pdf任务' })
  async diligenceExportPdf(@Request() req, @Param('diligenceId', ParseIntPipe) diligenceId: number) {
    const currentUser: RoverUser = req.user;
    const condition: ExportConditionRequest = { diligenceId };
    const result = await this.batchService.createExportBatchEntity(currentUser, BatchBusinessTypeEnums.Diligence_Report_Export, condition);
    return {
      batchId: result.batchId,
      creatorId: result.creatorId,
      fileName: result.fileName,
    };
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('diligence/getPdf/:batchId')
  @ApiOperation({ summary: '查询生成的 pdf 链接' })
  async getDiligencePdf(@Request() req, @Param('batchId', ParseIntPipe) batchId: number) {
    const currentUser: RoverUser = req.user;
    const batch = await this.batchService.getBatchEntity(currentUser, batchId, true);
    this.checkBatchStatus(batch);
    if (batch.businessType != BatchBusinessTypeEnums.Diligence_Report_Export) {
      throw new BadRequestException(RoverExceptions.BadParams.NotFound);
    }
    return {
      batchId: batch.batchId,
      detailFile: batch.detailFile,
      previewUrl: batch.previewUrl,
      fileName: batch.fileName,
    };
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('diligence/detail')
  @ApiOperation({ summary: '获取详情: 获取准入排查维度详情' })
  async getDiligenceDetails(
    @Request() req,
    @Query('diligenceId') diligenceId: number,
    @Query('pageIndex') pageIndex: number,
    @Query('detailType') detailType: string,
  ) {
    let key: DimensionLevel2Enums;
    switch (detailType) {
      case 'CustomerPartnerInvestigation':
        key = DimensionLevel2Enums.CustomerPartnerInvestigation;
        break;
      case 'CustomerSuspectedRelation':
        key = DimensionLevel2Enums.CustomerSuspectedRelation;
        break;
      default:
        throw new BadRequestException(RoverExceptions.Diligence.Common.UnknownType);
    }
    const currentUser: RoverUser = req.user;
    const diligence = await this.evaluationService.getDiligenceById(currentUser, diligenceId);
    // 取消快照检查，没有快照就走查询逻辑
    // const status = diligence?.snapshotDetails?.status;
    // if (status != SnapshotStatus.SUCCESS) {
    //   throw new BadRequestException(RoverExceptions.Diligence.Snapshot.NotGenerated);
    // }
    const params = Object.assign(new HitDetailsBaseQueryParams(), {
      source: 'Rover',
      keyNo: diligence.companyId,
      companyName: diligence.name,
      snapshotId: diligence.snapshotId,
      pageIndex: pageIndex || 1,
      pageSize: 50,
      userId: currentUser.userId,
    });

    const hitDetailParam: GetHitDetailsParamBase = {
      key,
      orgId: currentUser.currentOrg,
    };
    const result = await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, params);
    // 补充企业分组和标签信息
    const relatedCompanyIds = result?.Result?.map((x) => x.companyKeynoRelated) || [];
    if (!relatedCompanyIds?.length) {
      return {
        Paging: {
          PageIndex: params.pageIndex,
          PageSize: 50,
          TotalRecords: 0,
        },
        resultData: [],
      };
    }
    const relatedCompanies = await this.customerService.getCustomersWithGroupAndLabel(hitDetailParam.orgId, relatedCompanyIds);
    const resultData = result.Result.map((record) => {
      const relatedCompany = relatedCompanies.find((x) => x.companyId === record['companyKeynoRelated']);
      if (!relatedCompany) {
        return pick(record, ['companyKeynoRelated', 'companyNameRelated', 'startCompanyKeyno', 'startCompanyName']);
      }
      return Object.assign(
        {},
        pick(record, ['companyKeynoRelated', 'companyNameRelated', 'startCompanyKeyno', 'startCompanyName']),
        pick(relatedCompany, ['groupName', 'labelNames']),
      );
    });
    return Object.assign({}, pick(result, ['Paging']), { resultData });
  }

  // region 招标排查
  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Throttle(50, 60) //  每分钟50次
  @Post('bidding/scanRisk')
  @ApiOperation({ summary: '招标排查-执行排查（最多支持20家企业）' })
  async scanBiddingDimensionResult(@Request() req, @Body() postData: OpenapiBiddingRequest) {
    const currentUser: RoverUser = req.user;
    if (postData.companyKeys.length > 20) {
      return {
        code: 400,
        message: '此接口仅支持20家以内企业排查，请联系商务经理！',
      };
    }
    postData.companyKeys = postData.companyKeys.map(toFullWidth);
    const biddingRequest = await this.toDiligenceBiddingRequest(postData, currentUser);
    const diligenceResult = await this.biddingDiligenceService.scanRisk(currentUser, biddingRequest);
    const dimensionHitsDetails = diligenceResult.details.dimensionHitsDetails.map((x) => pick(x, ['key', 'name', 'level']));
    const dimensionOverviews = diligenceResult.details.dimensionOverviews;
    return Object.assign(
      pick(diligenceResult, ['id', 'tenderNo', 'projectNo', 'projectName', 'orgId', 'operator', 'createDate', 'result', 'description', 'companyList']),
      {
        dimensionHitsDetails,
        dimensionOverviews,
      },
    );
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Throttle(30, 60)
  @Post('bidding/asyncScanRisk')
  @ApiOperation({ summary: '招标排查-异步执行排查（最多支持100家企业）' })
  async scanBiddingAsync(@Request() req, @Body() postData: OpenapiBiddingRequestV2) {
    const currentUser: RoverUser = req.user;
    postData.companyKeys = postData.companyKeys.map(toFullWidth);
    const biddingRequest = await this.toDiligenceBiddingRequest(postData, currentUser);
    const diligenceResult = await this.biddingDiligenceService.scanRiskAsync(currentUser, biddingRequest);
    return pick(diligenceResult, ['id', 'tenderNo', 'projectNo', 'projectName', 'orgId', 'operator', 'createDate']);
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('bidding/history/result')
  @ApiOperation({ summary: '招标排查-获取排查结果' })
  async getBiddingHistoryDimensionResult(@Request() req, @Query('tenderNo') tenderNo: string) {
    const currentUser: RoverUser = req.user;
    const diligenceResult = await this.biddingCommonService.getByTenderNo(currentUser, tenderNo);
    if (!diligenceResult) {
      throw new BadRequestException(RoverExceptions.Openapi.Bidding.InvalidTenderNo);
    }
    if (diligenceResult.status !== 1) {
      return pick(diligenceResult, ['id', 'tenderNo', 'projectNo', 'projectName', 'orgId', 'operator', 'createDate', 'status']);
    } else {
      const dimensionHitsDetails = diligenceResult.details.dimensionHitsDetails.map((x) => pick(x, ['key', 'name', 'level']));
      return Object.assign(
        pick(diligenceResult, ['id', 'tenderNo', 'projectNo', 'projectName', 'orgId', 'operator', 'createDate', 'result', 'description', 'status']),
        { dimensionHitsDetails },
      );
    }
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('bidding/history/BiddingCompanyRelation')
  @ApiOperation({ summary: '招标排查-获取深度关系排查详情' })
  async getBiddingHistoryBiddingCompanyRelation(@Request() req, @Query('tenderNo') tenderNo: string) {
    const currentUser: RoverUser = req.user;
    const diligenceResult = await this.biddingCommonService.getByTenderNo(currentUser, tenderNo);
    if (!diligenceResult) {
      throw new BadRequestException(RoverExceptions.Openapi.Bidding.InvalidTenderNo);
    }
    if (diligenceResult.status !== 1) {
      return pick(diligenceResult, ['id', 'tenderNo', 'projectNo', 'projectName', 'orgId', 'operator', 'createDate', 'status']);
    } else {
      const detail = diligenceResult.details.dimensionHitsDetails.find((x) => x.key == DimensionLevel2Enums.BiddingCompanyRelation);
      return Object.assign(pick(diligenceResult, ['id', 'tenderNo', 'projectNo', 'projectName', 'orgId', 'operator', 'createDate', 'status']), {
        detail,
      });
    }
  }

  // endregion

  // region 批量准入排查
  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Throttle(10, 60)
  @Post('diligence/scanRiskBatch')
  @ApiOperation({ summary: '批量准入排查' })
  async scanBatchDiligenceResult(@Request() req, @Body() postData: OpenapiBatchDiligenceRequest) {
    const currentUser: RoverUser = req.user;
    // 校验用户是否已有执行中任务
    await this.batchBaseHelperService.checkBatchCount(currentUser, BatchBusinessTypeEnums.Diligence_ID);
    postData.companyKeys = postData.companyKeys.map(toFullWidth);
    const allCount = postData.companyKeys.length;
    const batchDiligenceRequest = await this.toBatchDiligenceRequest(postData);
    const recordCount = batchDiligenceRequest.data.length;
    const result = await this.batchService.createBatchDiligenceTask(
      currentUser,
      batchDiligenceRequest.data,
      BatchBusinessTypeEnums.Diligence_ID,
      BatchTypeEnums.Import,
      { settingId: batchDiligenceRequest.settingId },
    );
    return Object.assign(pick(result, ['batchId', 'batchInfo', 'orgId', 'createDate', 'endDate', 'status', 'creator']), {
      recordCount,
      allCount,
    });
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('diligence/scanRiskBatch/result')
  @ApiOperation({ summary: '批量准入排查任务状态' })
  async getBatchDiligenceResult(@Request() req, @Query('batchId') batchId: number) {
    const currentUser: RoverUser = req.user;
    const result = await this.batchService.getBatchEntityWithCreator(currentUser, batchId);
    return Object.assign(
      pick(result, ['batchId', 'batchInfo', 'orgId', 'createDate', 'endDate', 'status', 'creator', 'recordCount', 'paidCount', 'errorCount', 'successCount']),
      {
        creator: pick(result.creator, ['userId', 'name', 'phone']),
      },
    );
  }

  // 批量准入排查结果详情接口
  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('diligence/scanRiskBatch/detail')
  @ApiOperation({ summary: '批量准入排查结果' })
  async getBatchDiligenceDetail(@Request() req, @Body() postData: OpenapiBatchDiligenceResultRequest) {
    const { batchId, pageIndex } = postData;
    const currentUser: RoverUser = req.user;
    // 校验batch的状态
    const batchStatus = await this.batchService.getBatchEntity(currentUser, batchId, false);
    this.checkBatchStatus(batchStatus);

    const request: AnalyzedCompanySearchedRequest = {
      pageIndex: pageIndex || 1,
      batchIdCurrent: batchId,
      pageSize: 50,
      batchIdPrevious: -1,
      onlyChangedCompany: 0,
      diligenceInfo: 0,
      aggsInfo: 0,
    };
    let result = {
      data: [],
      total: 0,
      pageIndex: pageIndex,
      pageSize: 50,
    };
    result = await this.diligenceAnalyzeService.getBatchItemsPageForOpenApi(request, currentUser.currentOrg);
    if (result?.data?.length > 0) {
      const data = result?.data?.map((x) => {
        return { batchId, companyId: x.companyId, companyName: x.companyName, diligenceId: x.diligenceIdCurrent };
      });
      return Object.assign(pick(result, ['pageIndex', 'pageSize', 'total']), { data });
    }
    return result;
  }

  checkBatchStatus(batchStatus: BatchEntity) {
    if (!batchStatus) {
      // 如果batch不存在
      throw new BadRequestException(RoverExceptions.BadParams.NotFound);
    }
    if ([0, 1].includes(batchStatus.status)) {
      // 如果batch 处理未完成
      throw new BadRequestException(RoverExceptions.Batch.OnlyOneBatchProcess);
    }
    if (batchStatus.status === 3) {
      // 任务处理失败
      throw new BadRequestException(RoverExceptions.Batch.Failed);
    }
  }

  // region 准入排查
  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Throttle(60, 60)
  @Post('diligence/scanRisk')
  @ApiOperation({ summary: '准入排查-执行排查' })
  async scanRisk(@Request() req, @Body() postData: OpenapiDiligenceRequest) {
    const currentUser: RoverUser = req.user;
    postData.companyKey = toFullWidth(postData.companyKey);
    const diligenceRequest = await this.toDiligenceRequest(postData);
    const result = await this.evaluationService.getRiskListV2(currentUser, diligenceRequest);
    if (postData.settingId) {
      const settings = await this.settingService.getSettingById(postData.settingId);
      if (!settings) {
        throw new BadRequestException(RoverExceptions.Setting.InvalidId);
      }
      if (settings.orgId != currentUser.currentOrg) {
        throw new BadRequestException(RoverExceptions.Setting.InvalidId);
      }
    }

    const originalHits = result.details.originalHits.map((x) => pick(x, ['key', 'name', 'level', 'totalHits']));
    const creditRateResult = pick(result.details.creditRateResult, ['Score', 'ScoreDescInfo']);
    return Object.assign(pick(result, ['id', 'name', 'companyId', 'operator', 'orgId', 'result', 'createDate']), {
      originalHits,
      creditRateResult,
    });
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('risk-model-options')
  @ApiQuery({ name: 'type', enum: SettingTypeEnums })
  @ApiOperation({ summary: '获取风险模型列表' })
  async getRiskModelList(@Request() req, @Query('type') type: SettingTypeEnums): Promise<ModelSummaryDTO[]> {
    const currentUser: RoverUser = req.user;
    const param: SearchOrgSettingParam = {
      deleted: 0,
    };
    const settings = await this.settingService.listOrgSettings(currentUser.currentOrg, type, param);
    return settings.map((it) => ({
      id: it.id,
      name: it.name,
    }));
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('monitor-groups')
  @ApiOperation({ summary: '获取监控分组列表' })
  async getMonitorGroupList(@Request() req): Promise<ModelSummaryDTO[]> {
    const currentUser: RoverUser = req.user;
    const groups = await this.monitorGroupService.searchGroup(currentUser, {});
    return groups.map((it) => ({
      id: it.id,
      name: it.name,
    }));
  }

  // endregion

  // region 第三方列表
  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('customer')
  @ApiOperation({ summary: '第三方列表-添加企业' })
  async createCustomer(@Request() req, @Body() data: OpenapiCreateCustomerRequest) {
    const currentUser: RoverUser = req.user;
    data.companyKey = toFullWidth(data.companyKey);
    const createCustomerModel = await this.toCreateCustomerModel(data, currentUser);

    const result = await this.customerService.create(currentUser, createCustomerModel);
    await this.graphService.syncToNebula(currentUser.currentOrg, [result.customerId], StreamTableEnums.Customer, StreamOperationEnum.INSERT);
    return result;
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Put('customer')
  @ApiOperation({ summary: '第三方列表-更新企业' })
  async updateCustomer(@Request() req, @Query('customerId', ParseIntPipe) customerId: number, @Body() data: OpenapiCreateCustomerRequest) {
    const currentUser: RoverUser = req.user;
    data.companyKey = toFullWidth(data.companyKey);
    await this.securityService.allowWithinOrg(CustomerEntity, currentUser, [customerId], 'customerId');
    const createCustomerModel = await this.toCreateCustomerModel(data, currentUser);
    const result = await this.customerService.update(customerId, createCustomerModel, currentUser);
    await this.graphService.syncToNebula(currentUser.currentOrg, [customerId], StreamTableEnums.Customer, StreamOperationEnum.UPDATE);
    return result;
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('customer/remove')
  @ApiOperation({ summary: '第三方列表-删除企业' })
  async deleteCustomer(@Request() req, @Body() postData: IdsParams) {
    const currentUser: RoverUser = req.user;
    await this.securityService.allowWithinOrg(CustomerEntity, currentUser, postData.ids, 'customerId');
    const result = await this.customerService.remove(currentUser, Object.assign(new SearchCustomerModel(), postData));
    await this.graphService.syncToNebula(currentUser.currentOrg, postData.ids, StreamTableEnums.Customer, StreamOperationEnum.DELETE);
    return result;
  }

  // endregion

  // region 内部黑名单
  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('blacklist/inner')
  @ApiOperation({ summary: '内部黑名单-添加企业' })
  async createInnerBlacklist(@Request() req, @Body() data: OpenapiCreateBlacklistRequest) {
    const currentUser: RoverUser = req.user;
    data.companyKey = toFullWidth(data.companyKey);
    const createModel = await this.toCreateInnerBlacklistModel(data);
    const result = await this.innerBlacklistService.add(currentUser, createModel);
    await this.graphService.syncToNebula(currentUser.currentOrg, [result.id], StreamTableEnums.InnerBlacklist, StreamOperationEnum.INSERT);
    return result;
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Put('blacklist/inner')
  @ApiOperation({ summary: '内部黑名单-更新企业' })
  async updateInnerBlacklist(@Request() req, @Query('id', ParseIntPipe) id: number, @Body() data: OpenapiCreateBlacklistRequest) {
    const currentUser: RoverUser = req.user;
    data.companyKey = toFullWidth(data.companyKey);
    await this.securityService.allowWithinOrg(InnerBlacklistEntity, currentUser, [id]);
    const createModel = await this.toCreateInnerBlacklistModel(data);
    const result = await this.innerBlacklistService.update(id, createModel, currentUser);
    await this.graphService.syncToNebula(currentUser.currentOrg, [id], StreamTableEnums.InnerBlacklist, StreamOperationEnum.UPDATE);
    return result;
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('blacklist/inner/remove')
  @ApiOperation({ summary: '内部黑名单-删除企业' })
  async deleteInnerBlacklist(@Request() req, @Body() postData: IdsParams) {
    const currentUser: RoverUser = req.user;
    await this.securityService.allowWithinOrg(InnerBlacklistEntity, currentUser, postData.ids);
    const result = await this.innerBlacklistService.remove(currentUser, Object.assign(new SearchInnerBlacklistModel(), postData));
    await this.graphService.syncToNebula(currentUser.currentOrg, postData.ids, StreamTableEnums.InnerBlacklist, StreamOperationEnum.DELETE);
    return result;
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('blacklist-groups')
  @ApiOperation({ summary: '获取内部黑名单分组列表' })
  @ApiOkResponse({ type: AggsResponse })
  async getBlacklistGroups(@Request() req) {
    return this.searchBlacklistAggs(req.user, 'group');
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('blacklist-labels')
  @ApiOperation({ summary: '获取内部黑名单标签列表' })
  @ApiOkResponse({ type: AggsResponse })
  async getBlacklistLabels(@Request() req) {
    return this.searchBlacklistAggs(req.user, 'label');
  }

  private async searchBlacklistAggs(user: RoverUser, aggsField: string) {
    const postParam = Object.assign(new InnerBlackAggsRequest(), { aggsField });
    const aggsResponse = await this.innerBlacklistService.aggsForSearch(user, postParam);
    return aggsResponse?.map((x) => {
      return {
        name: x.fieldLabel,
        id: Number(x.fieldValue),
      };
    });
  }

  // endregion

  // region 人员管理
  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('person')
  @ApiOperation({ summary: '人员管理-添加人员' })
  async createPerson(@Request() req, @Body() data: OpenapiCreatePersonRequest) {
    const currentUser: RoverUser = req.user;
    data.companyKey = toFullWidth(data.companyKey);
    const createModel = await this.toCreatePersonModel(data, currentUser);
    createModel.relationPersonId = -1;
    return this.personService.create(currentUser, createModel);
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Put('person')
  @ApiOperation({ summary: '人员管理-更新人员' })
  async updatePerson(@Request() req, @Query('id', ParseIntPipe) id: number, @Body() data: OpenapiCreatePersonRequest) {
    const currentUser: RoverUser = req.user;
    data.companyKey = toFullWidth(data.companyKey);
    await this.securityService.allowWithinOrg(PersonEntity, req.user, [id]);
    const createModel = await this.toCreatePersonModel(data, currentUser);
    createModel.relationPersonId = -1;
    return this.personService.update(currentUser, id, createModel);
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('person/remove')
  @ApiOperation({ summary: '人员管理-删除人员' })
  async deletePerson(@Request() req, @Body() postData: IdsParams) {
    const currentUser: RoverUser = req.user;
    await this.securityService.allowWithinOrg(PersonEntity, req.user, postData.ids);
    return this.personService.remove(currentUser, Object.assign(new SearchPersonModel(), postData));
  }

  //监控列表
  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('monitor')
  @ApiOperation({ summary: '监控管理-添加监控企业' })
  @ApiOkResponse({ type: MonitorGroupCompanyEntity, isArray: true })
  public async createMonitor(@Request() req, @Body() param: OpenapiCreateMonitorRequest): Promise<MonitorGroupCompanyEntity[]> {
    const currentUser: RoverUser = req.user;
    const monitorParam = await this.toCreateMonitorRequest(param, currentUser);
    //创建监控企业
    return this.monitorCompanyService.create(currentUser, [monitorParam]);
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Post('monitor/remove')
  @ApiOperation({ summary: '监控管理-删除监控企业' })
  public async deleteMonitor(@Request() req, @Body() postData: IdsParams) {
    const currentUser: RoverUser = req.user;
    this.securityService.checkScope(currentUser, 2101);
    return this.monitorCompanyService.remove(currentUser, Object.assign(new SearchCompanyRequest(), postData));
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('tender/detailFile')
  @ApiOperation({ summary: '获取招标报告详情' })
  async getTenderPDFByBatchId(@Request() req, @Query('id') id: number) {
    return this.getBatchEntityForOpenApi(req.user, id);
  }

  @UseGuards(OpenApiJwtGuard, OpenApiUserThrottlerGuard)
  @Get('diligence/detailFile')
  @ApiOperation({ summary: '获取尽调报告详情' })
  async getDiligencePDFByBatchId(@Request() req, @Query('id') id: number) {
    return this.getBatchEntityForOpenApi(req.user, id);
  }

  // endregion

  private async toDiligenceRequest(request: OpenapiDiligenceRequest): Promise<GetDiligenceResultParams> {
    const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfo([request.companyKey]);
    if (unmatchedNames.length) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.Diligence.UnMatchedCompany, { message: [`不匹配的企业名称或统一社会信用代码：${unmatchedNames.join('，')}`] }),
      );
    }
    if (unsupported.length) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.Diligence.UnSupportedCompany, { message: [`不支持的企业：${unsupported.join('，')}`] }),
      );
    }
    return Object.assign(new GetDiligenceResultParams(), {
      companyId: matchedCompanyInfos[0].id,
      companyName: matchedCompanyInfos[0].name,
      settingId: request.settingId,
    });
  }

  private async transformToDiligenceRequest(request: OpenapiBatchDiligenceRequest): Promise<BatchDiligenceRequest> {
    const { settingId, skipError, companyKeys } = request;
    const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfo(companyKeys);
    if (skipError != 1) {
      if (unmatchedNames.length) {
        throw new BadRequestException(
          Object.assign(RoverExceptions.Openapi.Bidding.UnMatchedCompany, { message: [`不匹配的企业名称或统一社会信用代码：${unmatchedNames.join('，')}`] }),
        );
      }
      if (unsupported.length) {
        throw new BadRequestException(
          Object.assign(RoverExceptions.Openapi.Bidding.UnSupportedCompany, { message: [`不支持的企业：${unsupported.join('，')}`] }),
        );
      }
    }
    const batchDiligenceReq = Object.assign(new BatchDiligenceRequest(), { settingId });

    batchDiligenceReq.data = matchedCompanyInfos.map((x) =>
      Object.assign(new JointBiddingAnalysisModel(), {
        companyId: x.id,
        companyName: x.name,
      }),
    );
    return batchDiligenceReq;
  }

  private async toBatchDiligenceRequest(request: OpenapiBatchDiligenceRequest): Promise<BatchDiligenceRequest> {
    const { settingId, skipError, companyKeys } = request;
    const batchDiligenceReq = Object.assign(new BatchDiligenceRequest(), { data: [], settingId });
    if (!companyKeys?.length) {
      return batchDiligenceReq;
    }

    const chunks = chunk(companyKeys, 100);
    const results = await Promise.all(
      chunks.map(async (chunk) => {
        const res = await this.transformToDiligenceRequest(
          Object.assign(new OpenapiBatchDiligenceRequest(), {
            settingId,
            skipError,
            companyKeys: chunk,
          }),
        );
        return res.data || [];
      }),
    );
    batchDiligenceReq.data = results.reduce((acc, curr) => acc.concat(curr), []);
    return batchDiligenceReq;
  }

  private async toDiligenceBiddingRequest(request: OpenapiBiddingRequest, currentUser: RoverUser): Promise<DiligenceBiddingRequest> {
    const { projectNo, projectName, ignoreUnMatchCompany } = request;
    const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfo(request.companyKeys);
    if (unmatchedNames.length && !ignoreUnMatchCompany) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.Bidding.UnMatchedCompany, { message: [`不匹配的企业名称或统一社会信用代码：${unmatchedNames.join('，')}`] }),
      );
    }
    if (unsupported.length && !ignoreUnMatchCompany) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.Bidding.UnSupportedCompany, { message: [`不支持的企业：${unsupported.join('，')}`] }),
      );
    }
    return Object.assign(
      new DiligenceBiddingRequest(
        matchedCompanyInfos.map((x) =>
          Object.assign(new JointBiddingAnalysisModel(), {
            companyId: x.id,
            companyName: x.name,
          }),
        ),
      ),
      { projectNo, projectName, orgId: currentUser.currentOrg },
    );
  }

  private async toCreateCustomerModel(request: OpenapiCreateCustomerRequest, currentUser: RoverUser): Promise<CreateCustomerModel> {
    const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfo(
      [request.companyKey],
      ['id', 'name', 'creditcode', 'regno'],
      allowType,
      true,
    );
    if (unmatchedNames.length) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.Customer.UnMatchedCompany, { message: [`不匹配的企业名称或统一社会信用代码：${unmatchedNames.join('，')}`] }),
      );
    }
    if (unsupported.length) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.Customer.UnSupportedCompany, { message: [`不支持的企业：${unsupported.join('，')}`] }),
      );
    }
    const matchCompany = matchedCompanyInfos[0];
    const createCustomerModel: CreateCustomerModel = Object.assign(new CreateCustomerModel(), omit(request, ['companyKey', 'groupName', 'labelNames']), {
      companyId: matchCompany.id,
      name: matchCompany.name,
      companyName: matchCompany.name,
    });
    if (request.groupName) {
      const group = await this.groupRepo.findOne({
        groupType: 1,
        orgId: currentUser.currentOrg,
        name: request.groupName,
      });
      if (group) {
        createCustomerModel.groupId = group.groupId;
      }
    }
    if (request.labelNames) {
      const labels = await this.labelRepo.find({ orgId: currentUser.currentOrg, name: In(request.labelNames) });
      if (labels?.length) {
        createCustomerModel.labelIds = labels.map((x) => x.labelId);
      }
    }
    if (!isEmpty(request?.customerDepartment)) {
      createCustomerModel.departmentNames = request.customerDepartment
        .replace(/[\r\n\t]/g, '')
        .replace(/，/g, ',')
        .trim()
        .split(',');
    }
    return createCustomerModel;
  }

  private async toCreateInnerBlacklistModel(request: OpenapiCreateBlacklistRequest): Promise<CreateInnerBlacklistModel> {
    const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfo(
      [request.companyKey],
      ['id', 'name', 'creditcode', 'regno'],
      allowType,
      true,
    );
    if (unmatchedNames.length) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.InnerBlacklist.UnMatchedCompany, {
          message: [`不匹配的企业名称或统一社会信用代码：${unmatchedNames.join('，')}`],
        }),
      );
    }
    if (unsupported.length) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.InnerBlacklist.UnSupportedCompany, { message: [`不支持的企业：${unsupported.join('，')}`] }),
      );
    }
    const matchCompany = matchedCompanyInfos[0];
    const createInnerBlacklistModel: CreateInnerBlacklistModel = Object.assign(new CreateInnerBlacklistModel(), omit(request, ['companyKey']), {
      companyId: matchCompany.id,
      companyName: matchCompany.name,
    });
    if (!isEmpty(request?.department)) {
      createInnerBlacklistModel.departmentNames = request.department
        .replace(/[\r\n\t]/g, '')
        .replace(/，/g, ',')
        .trim()
        .split(',');
    }
    return createInnerBlacklistModel;
  }

  private async toCreatePersonModel(request: OpenapiCreatePersonRequest, currentUser: RoverUser): Promise<CreatePersonModel> {
    const createModel: CreatePersonModel = Object.assign(new CreatePersonModel(), omit(request, ['companyKey', 'groupName', 'relatives']));
    if (request.groupName) {
      const group = await this.groupRepo.findOne({
        groupType: 2,
        orgId: currentUser.currentOrg,
        name: request.groupName,
      });
      if (group) {
        createModel.groupId = group.groupId;
      }
    }
    if (request.companyKey) {
      const { matchedCompanyInfos } = await this.companySearchService.matchCompanyInfo([request.companyKey]);
      if (matchedCompanyInfos?.length) {
        const matchCompany = matchedCompanyInfos[0];
        const matchCompanyPersons = await this.personService.matchCompanyPerson(request.name, matchCompany.id);
        if (matchCompanyPersons?.length) {
          createModel.companyId = matchCompany.id;
          createModel.companyName = matchCompany.name;
          createModel.keyNo = matchCompanyPersons[0].keyNo;
        }
      }
    }
    if (request.relatives) {
      createModel.relatives = await Bluebird.map(request.relatives, async (x) => {
        const r = Object.assign(new Relative(), omit(x, ['companyKey']));
        if (!r.relationship) {
          r.relationship = '其他';
        }
        if (x.companyKey) {
          const { matchedCompanyInfos } = await this.companySearchService.matchCompanyInfo([x.companyKey]);
          if (matchedCompanyInfos?.length) {
            const matchCompany = matchedCompanyInfos[0];
            const matchCompanyPersons = await this.personService.matchCompanyPerson(x.name, matchCompany.id);
            if (matchCompanyPersons?.length) {
              r.companyId = matchCompany.id;
              r.companyName = matchCompany.name;
              r.keyNo = matchCompanyPersons[0].keyNo;
            }
          }
        }
        return r;
      });
    }
    return createModel;
  }

  private async toCreateMonitorRequest(request: OpenapiCreateMonitorRequest, currentUser: RoverUser): Promise<AddCompanyRequest> {
    const { matchedCompanyInfos, unmatchedNames, unsupported } = await this.companySearchService.matchCompanyInfo([request.companyKey]);
    if (unmatchedNames.length) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.Diligence.UnMatchedCompany, { message: [`不匹配的企业名称或统一社会信用代码：${unmatchedNames.join('，')}`] }),
      );
    }
    if (unsupported.length) {
      throw new BadRequestException(
        Object.assign(RoverExceptions.Openapi.Diligence.UnSupportedCompany, { message: [`不支持的企业：${unsupported.join('，')}`] }),
      );
    }
    if (matchedCompanyInfos?.length) {
      let defaultGroup;
      if (request.groupId) {
        defaultGroup = await this.monitorGroupRepo.findOne({
          orgId: currentUser.currentOrg,
          id: request.groupId,
        });
        if (!defaultGroup) {
          throw new BadRequestException(Object.assign(RoverExceptions.Openapi.Monitor.UselessGroup, { message: [`无效的分组：${request.groupId}`] }));
        }
      } else {
        //获取默认分组
        defaultGroup = await this.monitorGroupRepo.findOne({
          orgId: currentUser.currentOrg,
          name: '默认分组',
        });
      }
      if (!defaultGroup) {
        //如果默认分组不存在，创建一个默认分组
        const group = new MonitorGroupEntity(); //创建分组
        group.name = '默认分组';
        group.orgId = currentUser.currentOrg;
        defaultGroup = await this.monitorGroupRepo.save(group);
      }
      const matchCompany = matchedCompanyInfos[0];
      return Object.assign(new AddCompanyRequest(), {
        companyId: matchCompany.id,
        companyName: matchCompany.name,
        groupId: defaultGroup.id,
      });
    }
    throw new BadRequestException(Object.assign(RoverExceptions.Openapi.Monitor.UselessCompany, { message: [`无效的企业：${unsupported.join('，')}`] }));
  }

  private async getBatchEntityForOpenApi(currentUser: RoverUser, id: number) {
    const batch = await this.batchService.getBatchEntity(currentUser, id);
    return batch ? pick(batch, ['batchId', 'fileName', 'detailFile']) : null;
  }
}
