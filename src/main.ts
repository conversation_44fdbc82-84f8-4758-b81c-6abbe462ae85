import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as compression from 'compression';
import { ValidationPipe } from '@nestjs/common';
import * as Sentry from '@sentry/node';
// import * as Tracing from '@sentry/tracing';
import * as cookieParser from 'cookie-parser';
import { RewriteFrames } from '@sentry/integrations';
import {
  BlacklistInterceptor,
  GlobalExceptionFilter,
  handClassValidatorError,
  NewrelicInterceptor,
  QccSaasService,
  RavenInterceptor,
  SentryUtils,
} from '@kezhaozhao/qcc-utils';
import agent from '@kezhaozhao/qcc-logger';
import helmet from 'helmet';
import { AppModule } from 'apps/app/app.module';
import { API_BASE } from 'libs/constants/common';
import { ConfigService } from 'libs/config/config.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { useAdapter } from '@type-cacheable/ioredis-adapter';
import * as path from 'path';
import * as express from 'express';
import { RedisIoAdapter } from './apps/socket/common/redis.io.adapter';

if (!process.env.PROJECT_NAME) {
  process.env.PROJECT_NAME = 'qcc-rover-service';
}

if (!process.env.SERVICE_NAME) {
  process.env.SERVICE_NAME = 'qcc-rover-service';
}

async function bootstrap() {
  const dsn = process.env.SENTRY_DSN;
  const projectName = process.env.PROJECT_NAME || 'qcc-rover-service';
  const app = await NestFactory.create(AppModule);
  app.enableCors();
  app.enableShutdownHooks();
  const apiBase = API_BASE;
  app.setGlobalPrefix(apiBase);
  app.use(compression());
  app.use(cookieParser()); // 开启 cookie-file.parser (用于 udesk 在线客服识别用户)

  // const httpService = context.get<HttpService>('HttpService');
  const configService: ConfigService = app.get(ConfigService);
  const redisService: RedisService = app.get(RedisService);
  const qccSaasService: QccSaasService = app.get(QccSaasService);
  useAdapter(redisService.getClient());
  // const blackListConfig = configService.server.blackListService;
  // app.use(cookieParser(configService.jwt.secret));
  // app.useGlobalInterceptors(new AccessCheckInterceptor(`${blackListConfig.baseUrl}/${blackListConfig.check}`, httpService, configService.accessCheck));
  app.useGlobalInterceptors(new BlacklistInterceptor(qccSaasService, configService.accessCheck));
  app.use(Sentry.Handlers.requestHandler());
  // app.use(Sentry.Handlers.tracingHandler());
  app.useGlobalInterceptors(
    new RavenInterceptor({
      requestTimeout: 180000,
    }),
  );

  // const httpAdapter = app.getHttpAdapter();
  // const instance = httpAdapter.getInstance();
  // @ts-ignore
  Sentry.init({
    debug: false,
    serverName: projectName,
    enabled: !!dsn,
    release: process.env.SENTRY_RELEASE || `${projectName}@${process.env.PIPELINE_ID || 'default-pipe-id'}`,
    environment: process.env.SENTRY_ENVIRONMENT || 'default',
    dsn,
    integrations: [
      new RewriteFrames({
        //@ts-ignore
        root: global.__rootdir__,
      }),
      // enable HTTP calls tracing
      // new Sentry.Integrations.Http({ tracing: true })
      // enable Express.js middleware tracing
      // new Tracing.Integrations.Express({ app: instance })
    ],
    // tracesSampler: SentryUtils.tracesSampler,
    beforeSend: SentryUtils.beforeSend,
    beforeBreadcrumb: SentryUtils.beforeBreadcrumb,
  });

  app.useGlobalInterceptors(new NewrelicInterceptor());
  // global exceptions
  app.useGlobalFilters(new GlobalExceptionFilter());

  // transform
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      exceptionFactory: handClassValidatorError,
    }),
  );

  if (configService.stage !== 'prod') {
    // Swagger configuration
    const options = new DocumentBuilder().setTitle('Qcc Rover Service').setDescription('Qcc Rover Service').setVersion('1.0').addBearerAuth().build();
    const document = SwaggerModule.createDocument(app, options);
    SwaggerModule.setup(`${apiBase}/swagger`, app, document);
    app.use(apiBase, express.static(path.join(__dirname, '..', 'static'))); // 只在非生产环境下使用
  } else {
    app.use(
      helmet({
        xssFilter: true,
      }),
    );
  }

  const PORT = process.env.PORT || 7001;
  agent.start({
    // serviceName: projectName
    usePulsarClient: false,
    serviceName: `${process.env.STAGE || 'localhost'}::${projectName}`,
    disablePlugins: 'amqplib',
    traceIgnorePath:
      '/**/health/*,/**/**/health/*,/**/ping,/*/*/ping,/qcc/kzz/authSync,/rover/message.handler/search,/rover/account/searchName,trace/monitorSentimentDynamic,trace/monitorRiskDynamic',
  });

  const redisIoAdapter = new RedisIoAdapter(app);
  await redisIoAdapter.connectToRedis(configService.redis);

  app.useWebSocketAdapter(redisIoAdapter);

  await app.listen(PORT);
}

bootstrap();
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace NodeJS {
    interface Global {
      __rootdir__: string;
    }
  }
}
// @ts-ignore
global.__rootdir__ = __dirname || process.cwd();
// This allows TypeScript to detect our global value
declare global {
  let __rootdir__: string;
}

process.on('SIGHUP', () => {
  // 确保IDE工具 rerun或者stop时候，程序能自己退出，否则存在内存泄漏风险
  process.exit(1);
});
