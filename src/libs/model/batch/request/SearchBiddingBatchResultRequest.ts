import { PaginationQueryParams } from 'libs/model/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { DimensionTypeEnums } from '../../../enums/diligence/DimensionTypeEnums';
import { CompanyRelationType } from '../../../enums/tender/DiligenceHistoryEnum';
import { values } from 'lodash';

export class SearchBiddingBatchResultRequest extends PaginationQueryParams {
  @ApiProperty({ description: 'batchId', type: String, required: true })
  @IsNotEmpty()
  @Type(() => Number)
  @IsOptional()
  batchId: number;

  @ApiPropertyOptional({ description: '关键词' })
  searchKey?: string;

  @ApiPropertyOptional({ description: '状态 -1-稍后核定 0-通过 1-审慎核实 2-不通过' })
  status?: number[];

  @ApiPropertyOptional({ description: '全部维度' })
  dimensionLevelAll?: DimensionTypeEnums;

  @ApiPropertyOptional({ description: '查询维度' })
  dimensionLevel1?: DimensionTypeEnums;

  @ApiPropertyOptional({ description: '查询维度' })
  dimensionLevel2?: DimensionTypeEnums;

  @ApiPropertyOptional({ description: '疑似关系类型', enum: values(CompanyRelationType) })
  @IsArray()
  @IsOptional()
  // @IsIn(values(CompanyRelationType))
  relationType?: CompanyRelationType[];

  diligenceIds?: number[];
}
