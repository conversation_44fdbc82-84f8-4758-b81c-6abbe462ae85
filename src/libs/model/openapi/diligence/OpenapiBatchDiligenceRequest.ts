import { ApiProperty } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayNotEmpty, IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationParams } from '../../common';

export class OpenapiBatchDiligenceRequest {
  @ApiProperty({ required: true, description: '公司名称/统一社会信用代码，精确匹配' })
  @IsArray()
  @ArrayNotEmpty()
  @ArrayMaxSize(500)
  companyKeys: string[];

  @ApiProperty({ required: false, description: '风险模型ID' })
  @IsOptional()
  settingId?: number;

  @ApiProperty({ required: false, description: '跳过异常数据：0-不跳过，1-跳过，默认 1' })
  @IsOptional()
  skipError?: number = 1;
}

export class OpenapiBatchDiligenceResultRequest extends PaginationParams {
  @ApiProperty({ description: 'batchId', type: Number, required: true })
  @IsNotEmpty()
  @Type(() => Number)
  @IsOptional()
  batchId: number;
}
