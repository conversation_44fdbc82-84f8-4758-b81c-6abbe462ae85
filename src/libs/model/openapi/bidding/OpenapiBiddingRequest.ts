import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, ArrayNotEmpty, IsArray, IsNotEmpty } from 'class-validator';

export class OpenapiBiddingRequest {
  @ApiProperty({ required: true, description: '项目编号' })
  @IsNotEmpty()
  projectNo: string;

  @ApiProperty({ required: true, description: '项目名称' })
  @IsNotEmpty()
  projectName: string;

  @ApiProperty({ required: true, description: '公司名称/统一社会信用代码，精确匹配' })
  @ArrayMinSize(2)
  // @ArrayMaxSize(20) 由业务中校验
  @IsArray()
  @ArrayNotEmpty()
  companyKeys: string[];

  @ApiProperty({ required: false, description: '是否忽略不匹配的公司，默认false' })
  ignoreUnMatchCompany?: boolean;
}
