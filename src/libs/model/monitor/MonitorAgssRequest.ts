import { SearchCompanyRequest } from './request/SearchCompanyRequest';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsIn, ValidateNested } from 'class-validator';

export class MonitorAgssRequest {
  @ApiProperty({ type: SearchCompanyRequest })
  @Type(() => SearchCompanyRequest)
  @ValidateNested()
  query: SearchCompanyRequest;

  @ApiProperty({
    description: '要聚合的字段',
    enum: ['level', 'operator', 'econType', 'enterpriseType', 'statusCode', 'group'],
  })
  @IsIn(['result', 'operator', 'econType', 'treasuryType', 'enterpriseType', 'statusCode', 'group'])
  aggsField: 'result' | 'operator' | 'econType' | 'treasuryType' | 'enterpriseType' | 'statusCode' | 'group';
}
