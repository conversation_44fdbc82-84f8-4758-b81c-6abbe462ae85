import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationParams } from 'libs/model/common';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsArray, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { DateRangeRelative, Region } from '@kezhaozhao/qcc-model';
import { Industry } from '@kezhaozhao/company-search-api';
import { FilterModel } from '../../customer/FilterModel';
import { EconType, EnterpriseType, TreasuryType } from '../../../constants/common';

export class SearchCompanyRequest extends PaginationParams {
  @ApiProperty({ description: '分组id', required: false })
  @IsOptional()
  @IsArray()
  groupIds?: number[];

  @ApiProperty({ description: '公司名称关键字', type: String })
  @IsOptional()
  searchKey?: string;

  @ApiPropertyOptional({
    description:
      '公司类型,EconKindCode 10（有限责任公司），20（股份有限公司），30（国企），40（外企），50（独资企业），60（合伙制企业），70（个体工商户）,80-联营企业,90-集体所有制,100-有限合伙,110-普通合伙',
    type: String,
    isArray: true,
    // enum: Object.keys(ConditionParams.econKindCode)
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  econKindCode?: string[];

  @ApiPropertyOptional({
    description: 'region 行政区域对象数组 [{ pr: 省份代码 province, ac: 市/区县代码  areacode }]',
    type: Region,
    isArray: true,
  })
  @Type(() => Region)
  @IsOptional()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  region?: Region[];

  @ApiPropertyOptional({
    description: '行业分类',
    isArray: true,
    type: Industry,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => Industry)
  industry?: Industry[];

  @ApiPropertyOptional({
    description: '创建时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  createDate?: DateRangeRelative[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '负责人ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  ownerIds?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '负责人depIds' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  depIds?: number[];

  @ApiProperty({ description: '注册资本', type: FilterModel, isArray: true })
  @Type(() => FilterModel)
  @IsOptional()
  @IsArray()
  registcapiAmount?: FilterModel[];

  @ApiPropertyOptional({
    description: '成立时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  startDateCode?: DateRangeRelative[];

  @ApiPropertyOptional({ type: String, description: '注册状态code', isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  statusCode?: string[];

  @ApiProperty({ description: '风险的等级', enum: [0, 1, 2] })
  @IsOptional()
  result?: number[];

  @ApiProperty({ description: '监控ids' })
  @IsOptional()
  ids?: number[];

  @ApiProperty({ description: '创建者ID' })
  @IsOptional()
  creatorIds?: number[];

  @ApiProperty({ required: false, description: '排序字段，默认为空' })
  @IsOptional()
  sortField?: string;

  @ApiProperty({ required: false, default: false, description: '是否升序（默认false，即默认倒序）' })
  isSortAsc?: boolean = false;

  @ApiPropertyOptional({ type: String, isArray: true, description: 'companyIds' })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  companyIds?: string[];

  @ApiProperty({ description: '操作人用户id' })
  operators?: number[];

  @ApiPropertyOptional({
    description: '企业性质',
    type: String,
    isArray: true,
    enum: Object.keys(EconType),
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  econType?: string[];

  @ApiPropertyOptional({
    description: '司库类型',
    type: String,
    isArray: true,
    enum: Object.keys(TreasuryType),
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  treasuryType?: string[];

  @ApiPropertyOptional({
    description: '机构类型',
    type: String,
    isArray: true,
    enum: Object.keys(EnterpriseType),
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  enterpriseType?: string[];
}
