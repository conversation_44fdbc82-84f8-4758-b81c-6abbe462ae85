import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayMinSize, ArrayNotEmpty, IsArray, IsNotEmpty, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationQueryParams } from 'libs/model/common';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { Industry, Region } from '@kezhaozhao/company-search-api';
import { FilterModel } from '../customer/FilterModel';
import { EconType, EnterpriseType, TreasuryType } from '../../constants/common';

export class SearchInnerBlacklistModel extends PaginationQueryParams {
  @ApiProperty({ description: '公司名称关键字', type: String, required: true })
  @IsNotEmpty()
  @Type(() => String)
  @IsOptional()
  searchKey: string;

  @ApiPropertyOptional({
    description:
      '公司类型,EconKindCode 10（有限责任公司）， 20（股份有限公司）， 30（国企）， 40（外企）， ' +
      '50（独资企业）， 60（合伙制企业）， 70（个体工商户）, 80-联营企业, 90-集体所有制, 100-有限合伙, ' +
      '110-普通合伙',
    type: String,
    isArray: true,
    // enum: Object.keys(ConditionParams.econKindCode)
  })
  // @IsIn(Object.keys(ConditionParams.econKindCode), { each: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  econKindCode?: string[];

  @ApiPropertyOptional({
    description: '企业性质',
    type: String,
    isArray: true,
    enum: Object.keys(EconType),
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  econType?: string[];

  @ApiPropertyOptional({
    description: '司库类型',
    type: String,
    isArray: true,
    enum: Object.keys(TreasuryType),
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  treasuryType?: string[];

  @ApiPropertyOptional({
    description: '机构类型',
    type: String,
    isArray: true,
    enum: Object.keys(EnterpriseType),
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  enterpriseType?: string[];

  @ApiPropertyOptional({
    description: 'region 行政区域对象数组 [{ pr: 省份代码 province, ac: 市/区县代码  areacode }]',
    type: Region,
    isArray: true,
  })
  @Type(() => Region)
  @IsOptional()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  region?: Region[];

  @ApiPropertyOptional({
    description: '行业分类',
    isArray: true,
    type: Industry,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => Industry)
  industry?: Industry[];

  @ApiPropertyOptional({
    description: '列入时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  joinDate?: DateRangeRelative[];

  @ApiPropertyOptional({
    description: '创建时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  createDate?: DateRangeRelative[];

  @ApiPropertyOptional({
    description: '更新时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  updateDate?: DateRangeRelative[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '黑名单ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  ids?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '黑名单depIds' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  depIds?: number[];

  @ApiProperty({ required: false, description: '排序字段，默认为空' })
  @IsOptional()
  sortField?: string;

  @ApiProperty({ required: false, default: false, description: '是否升序（默认false，即默认倒序）' })
  isSortAsc?: boolean = false;

  @ApiProperty({ description: '操作人用户id' })
  operators?: number[];

  @ApiPropertyOptional({ description: '过期状态，1：未过期，2：已过期' })
  expiredStatus: number;

  @ApiPropertyOptional({ type: Number, isArray: true, description: '分组ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @ArrayMaxSize(20, {})
  @ArrayMinSize(1, {})
  groupIds?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '标签ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @ArrayMaxSize(20, {})
  @ArrayMinSize(1, {})
  labelIds?: number[];

  @ApiPropertyOptional({ type: String, isArray: true, description: 'companyIds' })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  companyIds?: string[];

  @ApiProperty({ required: false, description: '全部选中状态' })
  @IsOptional()
  selectAll = false;

  @ApiProperty({ description: '注册资本', type: FilterModel, isArray: true })
  @Type(() => FilterModel)
  @IsOptional()
  @IsArray()
  registcapiAmount: FilterModel[];

  @ApiPropertyOptional({
    description: '成立时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  startDateCode?: DateRangeRelative[];

  @ApiPropertyOptional({ type: String, description: '注册状态code', isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  statusCode?: string[];

  @ApiProperty({ type: String, description: '黑名单来源部门', required: false })
  @IsOptional()
  department?: string;

  @ApiProperty({ type: String, isArray: true, description: 'companyNames', maxItems: 1000 })
  @IsOptional()
  @IsArray()
  @Type(() => String)
  @ArrayMinSize(1)
  @ArrayMaxSize(1000)
  companyNames?: string[];

  @ApiProperty({ type: String, isArray: true, description: 'companyNos', maxItems: 1000 })
  @IsOptional()
  @IsArray()
  @Type(() => String)
  @ArrayMinSize(1)
  @ArrayMaxSize(1000)
  companyNos?: string[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '所属部门id' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @ArrayMaxSize(20, {})
  @ArrayMinSize(1, {})
  departmentIds?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '风险等级 -1:查未排查' })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  result?: number[];

  @ApiPropertyOptional({
    description: '到期时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  expiredDate?: DateRangeRelative[];
}
