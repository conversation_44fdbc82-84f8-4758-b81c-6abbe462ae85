import { IsIn, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SearchInnerBlacklistModel } from './SearchInnerBlacklistModel';

export class InnerBlackAggsRequest {
  @ApiProperty({ type: SearchInnerBlacklistModel })
  @ValidateNested()
  query: SearchInnerBlacklistModel;

  @ApiProperty({
    description: '要聚合的字段',
    enum: ['label', 'department', 'econType', 'treasuryType', 'enterpriseType', 'statusCode', 'operator', 'group', 'riskLevel'],
  })
  @IsIn(['label', 'department', 'econType', 'treasuryType', 'enterpriseType', 'statusCode', 'operator', 'group', 'riskLevel'])
  aggsField: string;

  constructor() {
    this.query = new SearchInnerBlacklistModel();
  }
}
