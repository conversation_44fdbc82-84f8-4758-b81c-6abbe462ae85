import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber } from 'class-validator';

export class DataRangeConditionPO {
  @IsArray()
  @ApiProperty({ description: '分组id列表', example: [1, 2, 3] })
  groupIds: number[];
  @IsArray()
  @ApiProperty({ description: '标签id列表', example: [1, 2, 3] })
  labelIds: number[];
  @IsArray()
  @ApiProperty({ description: '部门id列表', example: [1, 2, 3] })
  departmentIds: number[];
  @IsNumber()
  @ApiProperty({ description: '条件运算符,1:与,2:或', example: 1 })
  conditionOperator: number;
}
