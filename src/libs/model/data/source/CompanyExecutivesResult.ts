import { ApiProperty } from '@nestjs/swagger';
import { PersonData } from './PersonData';

/**
 * 公司高管信息查询结果
 */
export class CompanyExecutivesResult {
  @ApiProperty({
    description: '人员keyNo列表',
    type: [String],
    example: ['person123', 'person456', 'company789'],
  })
  personNos: string[];

  @ApiProperty({
    description: '人员keyNo与职务的映射关系',
    type: 'object',
    additionalProperties: { type: 'string' },
    example: {
      person123: '法定代表人,董事长',
      person456: '董事,股东',
      company789: '企业主体',
    },
  })
  personJobSet: Record<string, string>;

  @ApiProperty({
    description: '人员详细信息列表',
    type: [PersonData],
  })
  personJobList: PersonData[];
}
