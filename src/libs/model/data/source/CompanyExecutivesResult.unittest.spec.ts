import { CompanyExecutivesResult } from './CompanyExecutivesResult';
import { PersonData } from './PersonData';

describe('CompanyExecutivesResult 类型测试', () => {
  it('应该正确创建 CompanyExecutivesResult 实例', () => {
    // Arrange
    const mockPersonData: PersonData = {
      name: '张三',
      keyNo: 'person123',
      job: '法定代表人',
      tags: ['法人'],
    };

    const expectedResult: CompanyExecutivesResult = {
      personNos: ['person123', 'person456'],
      personJobSet: {
        person123: '法定代表人,董事长',
        person456: '董事,股东',
      },
      personJobList: [mockPersonData],
    };

    // Act & Assert
    expect(expectedResult.personNos).toEqual(['person123', 'person456']);
    expect(expectedResult.personJobSet).toEqual({
      person123: '法定代表人,董事长',
      person456: '董事,股东',
    });
    expect(expectedResult.personJobList).toEqual([mockPersonData]);
  });

  it('应该支持空的结果', () => {
    // Arrange
    const emptyResult: CompanyExecutivesResult = {
      personNos: [],
      personJobSet: {},
      personJobList: [],
    };

    // Act & Assert
    expect(emptyResult.personNos).toHaveLength(0);
    expect(Object.keys(emptyResult.personJobSet)).toHaveLength(0);
    expect(emptyResult.personJobList).toHaveLength(0);
  });

  it('应该正确处理复杂的职务映射', () => {
    // Arrange
    const complexResult: CompanyExecutivesResult = {
      personNos: ['company789', 'person123'],
      personJobSet: {
        company789: '企业主体',
        person123: '法定代表人,董事长,股东',
      },
      personJobList: [
        {
          name: '测试公司',
          keyNo: 'company789',
          job: '企业主体',
        },
        {
          name: '张三',
          keyNo: 'person123',
          job: '法定代表人,董事长,股东',
          tags: ['法人', '董事', '股东'],
          stockPercent: '51%',
        },
      ],
    };

    // Act & Assert
    expect(complexResult.personNos).toContain('company789');
    expect(complexResult.personNos).toContain('person123');
    expect(complexResult.personJobSet['company789']).toBe('企业主体');
    expect(complexResult.personJobSet['person123']).toBe('法定代表人,董事长,股东');
    expect(complexResult.personJobList).toHaveLength(2);
  });
});
