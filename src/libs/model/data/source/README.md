# 数据源模型 (Data Source Models)

## 概述

该目录包含了数据源相关的模型定义，主要用于企业库服务和人员数据处理。

## 模型列表

### PersonData

人员数据模型，包含人员的基本信息和职务信息。

**主要字段：**

- `name`: 姓名
- `keyNo`: 人员 keyNo
- `job`: 人员类型/职务
- `tags`: 标签
- `stockPercent`: 持股比例
- `shouldCapi`: 认缴出资（万元）
- `history`: 是否历史数据

### CompanyExecutivesResult

公司高管信息查询结果模型，用于 `getCompanyExecutivesKeyNosV2` 方法的返回值。

**字段说明：**

```typescript
export class CompanyExecutivesResult {
  // 人员keyNo列表
  personNos: string[];

  // 人员keyNo与职务的映射关系
  personJobSet: Record<string, string>;

  // 人员详细信息列表
  personJobList: PersonData[];
}
```

**使用示例：**

```typescript
import { CompanyExecutivesResult } from 'libs/model/data/source/CompanyExecutivesResult';

// 在服务中使用
async getCompanyExecutives(keyNo: string): Promise<CompanyExecutivesResult> {
  const result = await this.enterpriseLibService.getCompanyExecutivesKeyNosV2(keyNo);

  console.log('人员keyNo列表:', result.personNos);
  console.log('职务映射:', result.personJobSet);
  console.log('详细信息:', result.personJobList);

  return result;
}
```

**返回值示例：**

```json
{
  "personNos": ["person123", "person456", "company789"],
  "personJobSet": {
    "person123": "法定代表人,董事长",
    "person456": "董事,股东",
    "company789": "企业主体"
  },
  "personJobList": [
    {
      "name": "张三",
      "keyNo": "person123",
      "job": "法定代表人,董事长",
      "tags": ["法人", "董事"]
    },
    {
      "name": "李四",
      "keyNo": "person456",
      "job": "董事,股东",
      "tags": ["董事", "股东"],
      "stockPercent": "30%"
    }
  ]
}
```

## 类型安全

所有模型都使用 TypeScript 严格类型定义，并包含：

1. **API 文档注解**: 使用 `@ApiProperty` 装饰器提供 Swagger 文档
2. **验证规则**: 使用 class-validator 进行数据验证
3. **示例数据**: 提供清晰的使用示例

## 测试

每个模型都包含对应的单元测试文件：

- `PersonData` - 基础人员数据模型测试
- `CompanyExecutivesResult.unittest.spec.ts` - 公司高管结果模型测试

运行测试：

```bash
npm test -- src/libs/model/data/source/
```

## 注意事项

1. **向后兼容**: 新增字段时保持向后兼容性
2. **类型约束**: 严格遵循 TypeScript 类型定义
3. **文档更新**: 修改模型时同步更新文档和示例
