import { ApiProperty } from '@nestjs/swagger';

export class PersonData {
  @ApiProperty({ description: '姓名' })
  name: string;

  @ApiProperty({ description: '人员keyNo' })
  keyNo?: string;

  @ApiProperty({ description: '人员类型/职务' })
  job?: string;

  @ApiProperty({ description: '标签' })
  tags?: string[];

  @ApiProperty({ description: '持股比例' })
  stockPercent?: string;

  @ApiProperty({ description: '认缴出资（万元）' })
  shouldCapi?: string;

  @ApiProperty({ description: '是否历史数据' })
  history?: boolean = false;

  @ApiProperty({ description: '认缴出资日' })
  shoudDate?: string;

  @ApiProperty({ description: '招标排查中潜在利益冲突命中维度类型' })
  dimension?: string;

  @ApiProperty({ description: '是否同名' })
  isSameName?: boolean;

  @ApiProperty({ description: '是否相同联系方式' })
  isSameContact?: boolean;

  @ApiProperty({ description: '关联企业 companyId' })
  sourceCompanyId?: string;

  @ApiProperty({ description: '关联企业 companyName' })
  sourceCompanyName?: string;

  hitKey?: any[];
}

export class InvestmentData {
  @ApiProperty({ description: '被投资公司名称' })
  name: string;

  @ApiProperty({ description: '被投资公司keyNo' })
  keyNo?: string;

  @ApiProperty({ description: '被投资公司状态' })
  companyStatus?: string;

  @ApiProperty({ description: '认缴出资额' })
  investCapi?: string;

  @ApiProperty({ description: '持股比例' })
  stockPercent?: string;
}
