import { PaginationQueryParams } from '@kezhaozhao/qcc-model';
import { Product } from '@kezhaozhao/saas-bundle-service';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsIn, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, MaxLength, MinLength } from 'class-validator';

export class UpdateNameRequest {
  @ApiProperty({ description: '用户姓名' })
  @IsString()
  @MaxLength(45)
  name: string;
}

export class VerificationCodePayload {
  @ApiProperty({ type: String, required: false, description: '滑块参数: sessionId' })
  @IsNotEmpty()
  sessionId: string;

  @ApiProperty({ type: String, required: false, description: '滑块参数: sig' })
  @IsNotEmpty()
  sig: string;

  @ApiProperty({ type: String, required: false, description: '滑块参数: token' })
  @IsNotEmpty()
  token: string;
}

export class ResetPasswordVerificationCodePayload extends VerificationCodePayload {
  @ApiProperty()
  @IsNotEmpty()
  @Matches(/^1[3456789]\d{9}$/, { message: '手机号码格式不正确' })
  phone: string;
}

export class VerificationCodePayloadForTrail {
  @ApiProperty()
  @IsNotEmpty()
  @Matches(/^1[3456789]\d{9}$/, { message: '手机号码格式不正确' })
  phone: string;

  @ApiProperty({ type: String, required: false, description: '滑块参数: sessionId' })
  @IsNotEmpty()
  sessionId: string;

  @ApiProperty({ type: String, required: false, description: '滑块参数: sig' })
  @IsNotEmpty()
  sig: string;

  @ApiProperty({ type: String, required: false, description: '滑块参数: token' })
  @IsNotEmpty()
  token: string;
}

export class VerificationCodeRequest {
  @ApiProperty()
  @IsNotEmpty()
  @Matches(/^1[3456789]\d{9}$/, { message: '手机号码格式不正确' })
  phone: string;
}

export class UpdatePasswordRequest {
  @ApiProperty({ description: '验证码' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(10)
  verifyCode: string;

  @ApiProperty({ description: '新密码' })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @MaxLength(18)
  newPassword: string;

  @ApiProperty({ description: '确认密码' })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @MaxLength(18)
  confirmPassword: string;
}

export class ResetPasswordRequest extends UpdatePasswordRequest {
  @ApiProperty()
  @IsNotEmpty()
  @Matches(/^1[3456789]\d{9}$/, { message: '手机号码格式不正确' })
  phone: string;
}

export class ChangeCurrentOrg {
  @ApiProperty({ description: '用户的loginUserId', required: false })
  loginUserId?: number;

  @ApiProperty({ description: '当前组织ID', required: true })
  @IsOptional()
  @IsNumber()
  currentOrg: number;

  @ApiProperty({ description: '产品', required: false })
  @IsOptional()
  product?: string;
}

export class GetOrgsModel {
  @ApiProperty({ description: '用户的loginUserId' })
  loginUserId: number;

  @ApiProperty({ description: '用户的guid' })
  guid: string;
}

export class UpdateSearchModel extends PaginationQueryParams {
  @ApiProperty({ description: '对应产品code', type: String })
  @IsNotEmpty()
  @IsIn([Product.Crm, Product.Rover, Product.Rover])
  serviceCode = Product.Rover;

  @ApiProperty({ description: '更新日志名称', type: String, required: false })
  @IsOptional()
  @MaxLength(50)
  keyName: string;

  @ApiPropertyOptional({ description: '是否返回全部:0-分页,1-全部' })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  returnAll = 0;

  @ApiPropertyOptional({ description: '当前状态:1-待发布;2-已发布' })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  status = 2;

  @ApiProperty({ description: '日志更新类型:1-PC;2-ios;3-Android', type: Number })
  @IsArray()
  @Type(() => Number)
  logType: number[];

  @ApiPropertyOptional({ description: '当前页码' })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  pageIndex: number;

  @ApiPropertyOptional({ description: '每页条数' })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  pageSize: number;
}
