import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationQueryParams } from 'libs/model/common';
import { Type } from 'class-transformer';
import { ArrayMaxSize, ArrayMinSize, ArrayNotEmpty, IsArray, IsNotEmpty, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { DateRangeRelative, Region } from '@kezhaozhao/qcc-model';

export class SearchPersonModel extends PaginationQueryParams {
  @ApiProperty({ description: '姓名关键字', type: String, required: false })
  @IsNotEmpty()
  @Type(() => String)
  @IsOptional()
  searchKey?: string;

  @ApiPropertyOptional({
    description: 'region 行政区域对象数组 [{ pr: 省份代码 province, ac: 市/区县代码  areacode }]',
    type: Region,
    isArray: true,
  })
  @Type(() => Region)
  @IsOptional()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  region?: Region[];

  @ApiPropertyOptional({
    description: '创建时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  createDate?: DateRangeRelative[];

  @ApiPropertyOptional({
    description: '更新时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  updateDate?: DateRangeRelative[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '负责人ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  ownerIds?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '分组ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @ArrayMaxSize(20, {})
  @ArrayMinSize(1, {})
  groupIds?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '人员ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  ids?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '人员depIds' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  depIds?: number[];

  @ApiProperty({ required: false, description: '排序字段，默认为空' })
  @IsOptional()
  sortField?: string;

  @ApiProperty({ required: false, default: false, description: '是否升序（默认false，即默认倒序）' })
  isSortAsc?: boolean = false;

  @ApiProperty({ description: '操作人用户id' })
  operators?: number[];

  @ApiPropertyOptional({ type: String, isArray: true, description: 'personNos' })
  @IsOptional()
  @IsArray()
  @Type(() => String)
  @ArrayMinSize(1)
  @ArrayMaxSize(1000)
  personNos?: string[];

  @ApiProperty({ required: false, description: '全部选中状态' })
  @IsOptional()
  selectAll = false;
}
