export enum QueryParamsEnums {
  mailMatchingMode = 'mailMatchingMode', //邮箱匹配模式
  addressMatchingMode = 'addressMatchingMode', //地址匹配模式

  reasonType = 'reasonType', //经营异常原因类型
  amount = 'amount', //金额

  blackListType = 'blackListType', //黑名单类型
  /**
   * 股权数额
   */
  equityAmount = 'equityAmount', //
  /**
   * 处罚类型
   */
  penaltiesType = 'penaltiesType', //
  /**
   * 处罚事由类型
   */
  punishReasonType = 'punishReasonType', //
  /**
   * 处罚金额
   */
  penaltiesAmount = 'penaltiesAmount', //
  /**
   * 匹配条件 or and
   */
  conditionOperator = 'conditionOperator',
  /**
   * 欠税金额
   */
  taxArrearsAmount = 'taxArrearsAmount',
  /**
   * 1:当前有效，0:历史, -1:不限
   */
  isValid = 'isValid', //
  /**
   * 执行标的
   */
  executionTarget = 'executionTarget', //
  /**
   * 被执行总金额
   */
  executionSum = 'executionSum', //
  /**
   * 注册金额
   */
  registrationAmount = 'registrationAmount', //
  /**
   * 资本降幅
   */
  capitalReduction = 'capitalReduction', //
  /**
   * 变更阈值
   */
  changeThreshold = 'changeThreshold', //
  // operator = 'operator', //运算符
  /**
   * 统计周期
   */
  cycle = 'cycle',
  /**
   * 成立时长 月
   */
  duration = 'duration', //
  /**
   * 涉案总金额
   */
  amountInvolved = 'amountInvolved', //
  /**
   * 作为被告方占比，如50
   */
  percentAsDefendant = 'percentAsDefendant', //
  /**
   * 经营异常类型
   */
  businessAbnormalType = 'businessAbnormalType',
  /**
   * 裁判文书身份类型（排除） prosecutor-原告 thirdpartyrole-第三人
   */
  judgementRoleExclude = 'judgementRoleExclude',
  types = 'types',
  depth = 'depth',
  percentage = 'percentage',
  excludedTypes = 'excludedTypes', //节点排除类型
  // excludedNodes = 'excludedNodes', //疑似关系节点排除类型
  /**
   * 筛选数据范围（黑名单，交叉重叠）
   */
  dataRange = 'dataRange',
  /**
   * 新闻主体类型
   */
  topics = 'topics',
  /**
   * 担保金额
   */
  guaranteeAmount = 'guaranteeAmount',
  /**
   * 出质比例/总股本
   */
  equityPledgeRatio = 'equityPledgeRatio',
  /**
   * 土地抵押金额
   */
  landMortgageAmount = 'landMortgageAmount',
  /**
   * 动产抵押 被担保主债权数额
   */
  chattelMortgageMainAmount = 'chattelMortgageMainAmount',

  /**
   * 未执行总金额
   */
  failure = 'failure',

  /**
   * 案件总金额
   */
  caseAmount = 'caseAmount',

  /**
   * 担保风险金额
   */
  guaranteedprincipal = 'guaranteedprincipal',

  /**
   * 资质证书
   */
  certification = 'certification',
  /**
   * 临近到期时间
   */
  nearExpirationType = 'nearExpirationType',
  /**
   * 近期时间
   */
  recentTime = 'recentTime',
  /**
   * 设置的数量值
   */
  limitCount = 'limitCount',

  /**
   * 资产负债率
   */
  assetLiabilityRatio = 'assetLiabilityRatio',
  // 营业执照
  businessLicense = 'businessLicense',
  // 纳税资质
  taxpayerList = 'taxpayerList',

  /**
   * 关联对象
   */
  associateObject = 'associateObject',
  /**
   * 关联对象排除
   */
  associateExclude = 'associateExclude',

  /**
   * 人员分组
   */
  personGroups = 'personGroups',

  /**
   * 股权质押状态
   */
  pledgeStatus = 'pledgeStatus',
  /**
   * 欠缴金额
   */
  AmountOwed = 'AmountOwed',

  /**信用大数据金融监管类型*/
  creditType = 'creditType',

  /**
   * 临近到期
   */
  approachingExpiry = 'approachingExpiry',

  /**
   * 经营状态
   */
  businessStatus = 'businessStatus',
  /**
   * 简易注销阶段
   */
  simpleCancellationStep = 'simpleCancellationStep',

  /**
   * 票据承兑风险状态
   */
  billAcceptanceRiskStatus = 'billAcceptanceRiskStatus',

  /**
   * 实缴资本比例
   */
  paidInCapitalRatio = 'paidInCapitalRatio',

  /**
   * 员工人数减少比例
   */
  employeeReductionRatio = 'employeeReductionRatio',

  /**
   * 注册资本变更比例
   */
  registeredCapitalChangeRatio = 'registeredCapitalChangeRatio',

  /**
   * 对外投资变更类型
   */
  foreignInvestmentChangeType = 'foreignInvestmentChangeType',
}
