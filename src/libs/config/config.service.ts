/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import * as path from 'path';
import { HttpModuleOptions } from '@nestjs/axios';
import { PulsarMQSettings } from '@kezhaozhao/message-queue/dist/components/PulsarMQSettings';
import { AuthenticationToken } from 'pulsar-client';
import { KafkaConfig } from 'kafkajs';
import * as dotenvx from '@dotenvx/dotenvx';

@Injectable()
export class ConfigService {
  server: any;
  kzzServer: any;
  enterpriseServer: any;
  redis: any;
  typeorm: any;
  riskDB: any;
  jwt: any;
  accessCheck: {
    includes: string[];
  };
  proxyServer: any;
  dataServer: any;
  domain: string;
  nodeEnv: string;
  stage: string;
  axiosConfig: HttpModuleOptions;
  searchDomain: string;
  pulsarMQ: PulsarMQSettings;
  esConfig: any;
  roverGraphServer: any;
  kzzEnterpriseService: any;
  kzzCrmServer: any;
  udesk: {
    host: string;
    title: string;
    code: string;
    userKey: string;
    pluginId: string;
    groupId: string;
  };
  kafkaClientConfig: KafkaConfig;
  kafkaTopic: any;

  constructor() {
    this.nodeEnv = process.env.NODE_ENV || 'local';
    if (this.nodeEnv == 'local' || process.env.JEST_WORKER_ID) {
      // 开发本地环境通过.env加载默认配置
      dotenvx.config();
    }
    this.domain = process.env.DOMAIN || 'rover.dev.greatld.com';
    this.searchDomain = 'http://search.test.greatld.com';
    this.stage = process.env.STAGE || 'test';
    this.proxyServer = {
      // AI数据地址
      dataService: process.env.QCCDATASERVICE || 'http://nodejs-qcc-backend-data.sit.office.qichacha.com',
      roverService: process.env.QCCROVERSERVICE || 'http://rover-rover-backend.sit.office.qichacha.com',
      riskService: process.env.QCCRISKSERVICE || 'http://qcc-risk-app-risk-api.sit.office.qichacha.com',
      userService: process.env.QCCUSERSERVICE || 'http://qcc-user-app-user-api.sit.office.qichacha.com',
    };
    this.dataServer = {
      // GET 公司详情接口
      companyDetail: `${this.proxyServer.dataService}/api/ECILocal/GetDetail`,
      companyInfo: `${this.proxyServer.dataService}/api/QccSearch/List/KeyNosByNames`,
      searchAdvance: `${this.proxyServer.dataService}/api/ECILocal/SearchMultiSelection`,
      // 获取公司各维度数据当前count
      getCountInfo: `${this.proxyServer.dataService}/api/ECILocal/getCountInfo`,
      // 获取公司各维度数据历史count
      getHistoryCountInfo: `${this.proxyServer.dataService}/api/History/GetCoyHistoryCountInfo`,
      // 信用评价列表 （主体信用评级 + 债券评级）
      // getCreditRating: `${this.proxyServer.roverService}/api/dimension/enterprise-qualification/get-credit-rating-view-list`,
      // 纳税信用等级
      // getTaxCredit: `${this.proxyServer.roverService}/api/dimension/enterprise-qualification/get-tax-credit-list`,
      // 海关评级 进出口信用详情
      // getImportExport: `${this.proxyServer.roverService}/api/dimension/enterprise-qualification/get-import-export-detail`,
      // 查询信用大数据 20250207 信用大数据接口切换 api/search/search-credit-> api/QccSearch/CreditSearch/Credit
      searchCredit: `${this.proxyServer.dataService}/api/QccSearch/CreditSearch/Credit`,
      // 任意文本解析公司名列表
      getCoyList: `${this.proxyServer.dataService}/api/ECILocal/GetCoyListWithFreeText`,
      // 根据keynos获取logo
      getLogoByKeyNos: `${this.proxyServer.dataService}/api/ECILocal/GetShortNameByKeyNos`,
      // 企查分详情
      getCreditRate: `${this.proxyServer.dataService}/api/ECILocal/GetCreditRate`,
      // 企查分趋势
      getCreditRateTrend: `${this.proxyServer.dataService}/api/ECILocal/GetCreditRateTrend`,
      // 获取B端count数据
      getCountForB: `${this.proxyServer.dataService}/api/ECILocal/GetCountForB`,
    };
    this.server = {
      // 阿里云人机验证配置
      aliyun: {
        baseUrl: process.env.ALIYUN_AFS_SERVICE,
        accessKey: process.env.ALIYUN_AFS_ACCESS_KEY,
        accessKeySecret: process.env.ALIYUN_AFS_ACCESS_KEY_SECRET,
        appKey: process.env.ALIYUN_AFS_APP_KEY,
        scene: process.env.ALIYUN_AFS_SENCE,
      },
      oss: {
        region: 'oss-cn-hangzhou',
        accessKeyId: process.env.OSS_ACCESS_KEY_ID,
        endpoint: ['prod', 'release'].includes(this.nodeEnv) ? 'https://oss-cn-hangzhou-internal.aliyuncs.com/' : undefined,
        internal: ['prod', 'release'].includes(this.nodeEnv),
        accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
        bucket: 'kezhaozhao-data',
        urlExpires: 24 * 3600,
        secure: true,
        timeout: 120 * 1000, // 120s
        prefix: 'rover',
      },
      mailerService: {
        host: process.env.MAIL_SERVICE_HOST,
        port: process.env.MAIL_SERVICE_PORT,
        secure: true,
        auth: {
          user: process.env.MAIL_SERVICE_USER,
          pass: process.env.MAIL_SERVICE_PASS,
        },
      },
      smsService: {
        msgService: process.env.MSGSERVICE || 'http://nodejs-qcc-msg-server.sit.office.qichacha.com',
      },
      // 调用专业版接口配置
      qccPro: {
        loginName: process.env.QCC_PRO_LOGIN_NAME,
        baseUrl: process.env.QCC_PRO_BASE_URL,
        key: process.env.QCC_PRO_SERVICE_KEY,
        secretKey: process.env.QCC_PRO_SERVICE_SECRET_KEY,
        slbUrl: process.env.QCC_PRO_SLB_URL || 'http://**************/',
      },
      tender: {
        baseUrl: process.env.TENDER_SERVICE,
        token: process.env.TENDER_SERVICE_TOKEN,
      },
      comDomainService: process.env.COMDOMAIN || 'http://**************:8800/index/',
      extDomainService: process.env.EXTDOMAIN || 'http://**************:9999/index/',
      ssoService: process.env.SSOSERVICE || 'http://**************:8800/index/',
      appService: process.env.APPSERVICE || 'http://**************:9900/app/',
      wxQccDomainService: process.env.WXQCCDOMAIM || 'http://wxapi.qichacha.com/owx/api/wechat/',
      wxAdminService: process.env.WXADMINSERVICE || 'http://wxapi.qichacha.com/owx/v1/admin',
      saasService: process.env.SAASSERVICE || 'http://qcc-user-app-user-api.sit.office.qichacha.com',
      bossService: process.env.BOSSSERVICE || 'http://boss.greatld.com:81',
    };
    this.kzzServer = {
      ddPlatformService: process.env.DD_PLATFORM_SERVICE || 'http://api.test.greatld.com',
      enterpriseService: process.env.ENTERPRISE_SERVICE_V2 || 'http://e.test.greatld.com/qcc/e',
      bundleService: process.env.SAAS_BUNDLE_SERVICE || 'http://api.test.greatld.com/api/bundle',
      authService: process.env.AUTH_SERVICE || 'http://api.test.greatld.com/qcc/auth',
      roverGraphService: process.env.ROVER_GRAPH_SERVICE || 'http://api.test.greatld.com/graph/rover',
      pdfService: process.env.PDF_SERVICE || 'http://api.test.greatld.com/qcc/pdf',
      crmService: process.env.CRM_SERVICE || 'http://z.test.greatld.com/kzz/crm',
      companySearchApi: process.env.COMPANY_SEARCH_API || this.searchDomain,
    };
    this.enterpriseServer = {
      // 获取用户
      getUserDepartment: `${this.kzzServer.enterpriseService}/internal/integration/users/department/search`,
      // 获取部门列表
      getDepartments: `${this.kzzServer.enterpriseService}/internal/integration/departments/search`,
      // 获取组织列表
      getUserOrgList: `${this.kzzServer.enterpriseService}/internal/integration/org/list`,
      // 切换组织
      changeCurrentOrg: `${this.kzzServer.enterpriseService}/internal/integration/users/changeCurrentOrg`,
      // 发送企查查验证码
      sendQccCode: `${this.kzzServer.enterpriseService}/internal/sendMsg`,
      // 获取发版日志列表
      searchUpdateLog: `${this.kzzServer.enterpriseService}/internal/updateLog/search`,
      // 通过token获取QCCSESSID
      loginByToken: `${this.kzzServer.enterpriseService}/internal/auth/loginByToken`,
      loginRedirect: `${this.kzzServer.enterpriseService}/internal/auth/loginRedirect`,
    };
    this.roverGraphServer = {
      // 初始化组织
      initOrg: `${this.kzzServer.roverGraphService}/init`,
      partner: `${this.kzzServer.roverGraphService}/partner`,
      blacklist: `${this.kzzServer.roverGraphService}/blacklist`,
      blacklistBatch: `${this.kzzServer.roverGraphService}/blacklist/batch`,
      invest: `${this.kzzServer.roverGraphService}/invest`,
      direct: `${this.kzzServer.roverGraphService}/direct`,
      syncManually: `${this.kzzServer.roverGraphService}/sync/manually`,
      relations: `${this.kzzServer.roverGraphService}/company/relations`,
      customerFinalBenefit: `${this.kzzServer.roverGraphService}/finalBenefit/customer`,
      blacklistFinalBenefit: `${this.kzzServer.roverGraphService}/finalBenefit/blacklist`,
      partnerBranch: `${this.kzzServer.roverGraphService}/branch/partner`,
      blacklistBranch: `${this.kzzServer.roverGraphService}/branch/blacklist`,
      blacklistBranchBatch: `${this.kzzServer.roverGraphService}/branch/blacklist/batch`,
      customerInvestigation: `${this.kzzServer.roverGraphService}/company/customer/investigation`,
      customerInvestigationV2: `${this.kzzServer.roverGraphService}/company/customer/investigationV2`,
      customerInvestigationCount: `${this.kzzServer.roverGraphService}/company/customer/investigation/count`,
      blacklistInvestigation: `${this.kzzServer.roverGraphService}/company/blacklist/investigation`,
      blacklistInvestigationV2: `${this.kzzServer.roverGraphService}/company/blacklist/investigationV2`,
      blacklistInvestigationCount: `${this.kzzServer.roverGraphService}/company/blacklist/investigation/count`,
      customerPotentiallyInvestigation: `${this.kzzServer.roverGraphService}/customer/potentially/interested`,
      blacklistPotentiallyInvestigation: `${this.kzzServer.roverGraphService}/blacklist/potentially/interested`,
      tenderInnerBlacklist: `${this.kzzServer.roverGraphService}/company/tender/inner/blacklist`,
      companyDeepRelations: `${this.kzzServer.roverGraphService}/deep/relations`,
      companyDeepRelatedParty: `${this.kzzServer.roverGraphService}/deep/relatedParty`,
      companyDeepInvestBranch: `${this.kzzServer.roverGraphService}/deep/investBranch`,
      personRelations: `${this.kzzServer.roverGraphService}/deep/personRelations`,
      filterCompany: `${this.kzzServer.roverGraphService}/relatedParty/filterCompany`,
    };
    this.kzzCrmServer = {
      leads: `${this.kzzServer.crmService}/leads`,
      leadsSearch: `${this.kzzServer.crmService}/v2/leads/search`,
      customerSearch: `${this.kzzServer.crmService}/v2/customer/search`,
      dynamicObjects: `${this.kzzServer.crmService}/dynamic/objects`,
    };
    // const configLogger = QccLogger.getLogger(ConfigService.name);
    this.redis = {
      host: process.env.REDIS_HOST,
      port: process.env.REDIS_PORT,
      db: process.env.REDIS_DB,
      password: process.env.REDIS_PASSWD,
      keyPrefix: 'rover:',
      enableReadyCheck: true,
      // retryStrategy: (times) => {
      //   return Math.min(times * 200, 5000) || 500;
      // },
      onClientReady: (client) => {
        // client.on('close', () => {
        //   configLogger.error('redis connection is closed');
        // });
        client.on('reconnecting', async (error) => {
          // const message.handler = `redis is reconnecting with delay: ${error}`;
          // configLogger.error(message.handler);
        });

        client.on('connect', () => {
          // configLogger.info('redis client is connected');
        });
        client.on('error', (err) => {
          // configLogger.error(`redis error: ${err}`);
        });
      },
      reconnectOnError: function (err) {
        // configLogger.error('redis reconnectOnError', err);
        // const targetError = 'READONLY';
        // if (err.message.handler.includes(targetError)) {
        //   // Only reconnect when the error contains "READONLY"
        //   return true; // or `return 1;`
        // }
        // return 2;
      },
    };
    this.typeorm = {
      type: 'mysql',
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      username: process.env.DB_USER,
      password: process.env.DB_PASSWD,
      database: process.env.DATABASE,
      charset: 'utf8mb4',
      synchronize: false,
      logging: false,
      multipleStatements: true,
      entities: [path.join(__dirname, '../entities/**/*.{js,ts}')],
      useConnectionPooling: true,
      pool: {
        max: 2000, // 最大连接数
        min: 0, // 最小连接数
        acquireTimeout: 30000, // 获取连接的超时时间（毫秒）
        idleTimeout: 30000, // 连接空闲超时时间（毫秒）
        connectionTimeout: 60000, // 连接超时时间（毫秒）
      },
      // cache: {
      //   duration: 30000, // 默认缓存30s(当明确开启缓存的时候)
      //   type: 'ioredis',
      //   options: this.redis,
      // },
    };
    this.riskDB = {
      name: 'riskdb',
      type: 'mysql',
      host: process.env.RISK_DB_HOST,
      port: process.env.RISK_DB_PORT,
      username: process.env.RISK_DB_USER,
      password: process.env.RISK_DB_PASSWD,
      database: process.env.RISK_DATABASE,
      charset: 'utf8mb4',
      synchronize: false,
      logging: false,
      entities: [path.join(__dirname, '../riskentities/**/*.{js,ts}')],
      // cache: {
      //   duration: 600000,
      //   type: 'ioredis',
      //   options: this.redis,
      // },
    };
    this.jwt = {
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '604800s' },
    }; // expire after 1 week
    this.accessCheck = {
      // check: process.env.ACCESS_CHECK === 'true',
      // match: [],
      // logIgnore: [],
      includes: ['**/(diligence|batch|monitor|settings|customer|company|person|bidding|blacklist)/**', '**/account/searchName'],
    };
    this.axiosConfig = {
      timeout: 180000, // 5s
      headers: {
        'x-kzz-request-from-server': 'qcc-rover-service',
        'x-request-from-app-name': 'rover',
        'x-request-from-head-app-name': 'qcc-rover-service',
      },
    };
    this.esConfig = {
      bundleUsage: {
        nodesQuery: process.env.KZZ_ES_NODES_READ,
        nodesWrite: process.env.KZZ_ES_NODES_WRITE,
        indexName: process.env.BUNDLE_ES_INDEX || 'saas_bundle_usage_daily-test',
        indexType: '_doc',
      },
      credit: {
        nodes: process.env.ES_CREDIT_READ,
        indexName: 'rover_credit_check_query',
        indexType: '_doc',
      },
      finance: {
        nodes: process.env.ES_CREDIT_READ,
        indexName: 'qfk_finc_corp_stat_query',
        indexType: '_doc',
      },
      blacklist: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'kys_blacklist_query',
        indexType: '_doc',
      },
      case: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'case_search_query',
        indexType: '_doc',
      },
      supervisePunish: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'kys_risk_supervise_punish_administrative_merge_query',
        indexType: '_doc',
      },
      bidCollusive: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'kys_risk_collusive_comps_query',
        indexType: '_doc',
      },
      negativeNews: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: process.env.ES_INDEX_NEGATIVENEWS || 'merchants_negative_news_query', // merchants_negative_news_query
        indexType: '_doc',
      },
      judgement: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'mongo_judgements_query',
        indexType: '_doc',
      },
      snapshot: {
        nodesQuery: process.env.ES_NODES_SNAPSHOT_READ,
        nodesWrite: process.env.ES_NODES_SNAPSHOT_WRITE,
        indexName: process.env.ES_INDEX_SNAPSHOT || 'kys_snapshot_test',
        indexType: '_doc',
      },
      diligenceAnalyze: {
        nodesQuery: process.env.ES_NODES_SNAPSHOT_READ,
        nodesWrite: process.env.ES_NODES_SNAPSHOT_WRITE,
        indexName: process.env.ES_INDEX_ANALYZE || 'kys_diligence_analyze_test',
        indexType: '_doc',
      },
      kysCompany: {
        nodes: process.env.ES_K8S_KZZ_READ,
        indexName: 'kys_company_query',
        indexType: '_doc',
      },
      tax: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'list_search_query',
        indexType: '_doc',
      },
      riskChangeList: {
        nodes: process.env.ES_RISK_CHANGE_READ,
        indexName: 'risk_changelist_query',
        indexType: '_doc',
      },
      pledge: {
        nodes: process.env.ES_K8S_KZZ_READ,
        indexName: 'companyrisk_pledge_new_query', // 股权出质
        indexType: '_doc',
      },
      ovsSanctions: {
        nodes: process.env.ES_K8S_CRM_READ,
        indexName: 'kys_comp_ovs_sanctions_query',
        indexType: '_doc',
      },
    };
    this.udesk = {
      host: process.env.UDESK_HOST,
      title: process.env.UDESK_TITLE,
      code: process.env.UDESK_CODE,
      userKey: process.env.UDESK_USER_KEY,
      pluginId: process.env.UDESK_PLUGIN_ID,
      groupId: process.env.UDESK_GROUP_ID,
    };
    this.pulsarMQ = {
      tenant: 'kezz',
      namespace: process.env.PULSAR_NS,
      clientConfig: {
        messageListenerThreads: 10,
        serviceUrl: process.env.PULSAR_URL,
        authentication: new AuthenticationToken({ token: process.env.PULSAR_TOKEN }),
      },
    };
    this.kafkaClientConfig = {
      clientId: `${process.env.STAGE || 'test'}_data_quality`,
      ssl: false,
      sasl: {
        mechanism: 'scram-sha-256', // scram-sha-256 or scram-sha-512
        username: process.env.KAFKA_SASL_USER,
        password: process.env.KAFKA_SASL_PWD,
      },
      brokers: process.env.KAFKA_BROKERS_GROUP ? process.env.KAFKA_BROKERS_GROUP.split(',') : [],
    };
    this.kafkaTopic = {
      // 公司监控动态推送队列
      // group_kezz_kys_company_moniter_test
      // group_kezz_kys_company_moniter_dev
      monitorCompanyRisk: {
        isOpen: true,
        name: 'kezz_kys_company_moniter',
        consumerConfig: {
          groupId: process.env.GORUP_KYS_COMPANY_MONITOR || 'group_kezz_kys_company_moniter_test',
        },
        producerConfig: {},
      },
      monitorCompanySentiment: {
        //TODO 需要根据实际名称修改
        isOpen: true,
        name: 'kezz_kys_company_moniter_sentiment',
        consumerConfig: {
          groupId: process.env.GORUP_KYS_COMPANY_MONITOR_S || 'group_kezz_company_moniter_sentiment_dev',
        },
        producerConfig: {},
      },
      canalStream: {
        isOpen: true,
        name: process.env.DB_STREAM_TOPIC || 'kezz_rover_stream_test',
        consumerConfig: {
          groupId: process.env.DB_STREAM_GROUP || 'group_kezz_rover_node_test',
        },
        producerConfig: {},
      },
    };
  }

  getOssObject(folderName: string, filename?: string, ossStage?: string) {
    if (filename) {
      return `rover/${ossStage || this.nodeEnv}/${folderName}/${filename}`;
    }
    return `rover/${ossStage || this.nodeEnv}/${folderName}`;
  }
}
