export class ExceptionBaseInfo {
  code: number;
  error: string;
}

export const RoverExceptions = {
  // 10xxxx
  BadParams: {
    Common: {
      code: 100001,
      error: '参数错误',
    },
    OrgUserDuplicated: {
      code: 100002,
      error: '该用户已经是企业成员，请勿重复加入',
    },
    DepartmentNotFound: {
      code: 100003,
      error: '部门不存在',
    },
    RoleNotFound: {
      code: 100004,
      error: '角色不存在',
    },
    UnknownRoleScope: {
      code: 100005,
      error: '未知的roleScope',
    },
    NotFound: {
      code: 100006,
      error: '记录不存在',
    },
    InnerBlackListDuplicated: {
      code: 100007,
      error: '企业在内部黑名单已存在，请勿重复添加',
    },
    Company: {
      SearchKeyNotFound: {
        code: 101000,
        error: '查询关键词不能为空',
      },
    },
    Phone: {
      code: 100008,
      error: '手机号码格式不正确',
    },
    Date: {
      code: 100009,
      error: '开始时间必须早于结束时间',
    },
    InnerBlackListMany: {
      code: 100010,
      error: '存在多条重复黑名单数据',
    },
  },
  // 20xxxx
  UserRelated: {
    User: {
      OrphanUser: {
        code: 200002,
        error: '用户不属于任何组织，请联系管理员添加',
      },
      ApplicationForbidden: {
        code: 200003,
        error: '您当前暂未开通第三方风险排查产品权限，请联系商务经理开通',
      },
      GetUserBundleInfoFailed: {
        code: 200009,
        error: '获取用户套餐信息失败',
      },
      GetUserOrgInfoFailed: {
        code: 200010,
        error: '获取用户组织信息失败',
      },
      ApplicationExpired: {
        code: 200011,
        error: '您当前第三方风险排查产品权限已到期，请联系商务经理',
      },
      VerificationCodeError: {
        code: 200020,
        error: '验证码不正确',
      },
      PasswordNotMatch: {
        code: 200021,
        error: '两次密码不一致',
      },
      ChangePasswordFailed: {
        code: 200022,
        error: '修改密码失败',
      },
      RoleForbidden: {
        code: 200009,
        error: '您暂无该功能权限，请联系管理员',
      },
    },
    Auth: {
      ManMachineError: {
        code: 200502,
        error: '人机验证失败, 请重新拖动滑块',
      },
      OrgNotCreated: {
        code: 200509,
        error: '您尚未开通产品套餐，请提交试用申请或联系客服开通',
      },
      ApplicationNotFound: {
        code: 200008,
        error: '您当前所在组织尚未开通产品套餐，请提交试用申请或联系客服开通',
      },
      OrgNotMatch: {
        code: 200511,
        error: '当前组织已切换，请重新加载页面',
      },
      InvalidJWT: {
        code: 200512,
        error: '无效的token',
      },
      JWTError: {
        code: 200513,
        error: '权限校验失败',
      },
      JWTExpired: {
        code: 200513,
        error: 'token已过期',
      },
      RequestExpire: {
        code: 200520,
        error: 'timestamp已过期',
      },
      InvalidTimestamp: {
        code: 200521,
        error: '无效的请求',
      },
      InvalidSign: {
        code: 200522,
        error: '无效的签名',
      },
      InvalidAccessKey: {
        code: 200523,
        error: '无效的accessKey',
      },
      ForbiddenAccess: {
        code: 200524,
        error: '无权限访问或该资源不存在',
      },
      InvalidAccessToken: {
        code: 200525,
        error: '无效的AccessToken',
      },
    },
    // 2003xx
    Role: {
      NotFound: {
        code: 200300,
        error: '角色不存在',
      }, //
      UnknownRoleScope: {
        code: 200301,
        error: '未知的 roleScope',
      },
      Duplicated: {
        code: 200302,
        error: '该角色名称已存在',
      },
      SystemRole: {
        code: 200303,
        error: '系统角色不可删除',
      },
      UserExists: {
        code: 200304,
        error: '该角色下有用户，请修改用户的角色后再删除',
      },
    },
    Trial: {
      TrialFailed: {
        code: 200409,
        error: '申请试用失败，请联系客服',
      },
      ApplyFailed: {
        code: 200410,
        error: '服务器异常，请稍后再试，或联系客服。',
      },
      ApplyDuplicated: {
        code: 200411,
        error: '申请记录已存在，请勿重复提交。',
      },
      DevFailed: {
        code: 200412,
        error: '开发环境暂不开放。',
      },
      LeadsDynamicObjectNotFound: {
        code: 200413,
        error: '无法找到线索对象',
      },
      ServiceStaffFailed: {
        code: 200414,
        error: '商务信息不存在',
      },
      LeadsDynamicObjectFieldNotFound: {
        code: 200415,
        error: '无法找到线索自定义字段',
      },
      BundleExist: {
        code: 200416,
        error: '当前手机号已开通, 现在登录。',
      },
    },
  },
  // 30xxxx
  GroupRelated: {
    // 3000xx
    Group: {
      NotFound: {
        code: 300001,
        error: '分组不存在',
      },
      NotDelete: {
        code: 300002,
        error: '无法删除，至少要保留一个分组',
      },
      RecycleFull: {
        code: 300003,
        error: '回收站已满，无法删除',
      },
      TypeError: {
        code: 300004,
        error: '分组类型错误',
      },
      AddError: {
        code: 300005,
        error: '添加分组失败',
      },
      EidtError: {
        code: 300006,
        error: '编辑分组失败',
      },
      DuplicatedError: {
        code: 300007,
        error: '新增分组名重复',
      },
      DeleteError: {
        code: 300008,
        error: '移除分组失败',
      },
      NotMonitor: {
        code: 300009,
        error: '监控未开启',
      },
      NoPermission: {
        code: 300010,
        error: '无查看权限',
      },
      BatchLimited: {
        code: 300011,
        error: '批量添加超限',
      },
      Limited: {
        code: 300012,
        error: '分组数量超限',
      },
      CanNotEdited: {
        code: 300013,
        error: '该分组不可编辑',
      },
    },
    // 3001xx
    Item: {
      BatchLimited: {
        code: 300101,
        error: '批量添加超限',
      },
      AddError: {
        code: 300102,
        error: '添加自选失败',
      },
      DeleteError: {
        code: 300103,
        error: '删除自选失败',
      },
      NotFound: {
        code: 300104,
        error: '自选不存在',
      },
    },
    // 3002xx
    Risk: {
      Group: {
        CreateError: {
          code: 300201,
          error: '添加分组信息失败',
        },
        UpdateError: {
          code: 300202,
          error: '更新分组信息失败',
        },
        RemoveError: {
          code: 300203,
          error: '移除分组信息失败',
        },
        RecoverError: {
          code: 300204,
          error: '恢复分组信息失败',
        },
      },
      Monitor: {
        CreateError: {
          code: 300205,
          error: '添加监控信息失败',
        },
        RemoveError: {
          code: 300206,
          error: '移除监控信息失败',
        },
        SearchListError: {
          code: 300207,
          error: '查询监控信息失败',
        },
      },
    },
    Label: {
      NotFound: {
        code: 400001,
        error: '标签不存在',
      },
      AddError: {
        code: 400002,
        error: '添加标签失败',
      },
      EidtError: {
        code: 400003,
        error: '编辑标签失败',
      },
      DuplicatedError: {
        code: 400004,
        error: '新增标签名重复',
      },
      DeleteError: {
        code: 400005,
        error: '移除标签失败',
      },
      BatchLimited: {
        code: 400006,
        error: '批量添加超限',
      },
      Limited: {
        code: 400007,
        error: '标签数量超限',
      },
    },
    Person: {
      NotFound: {
        code: 500001,
        error: '人员不存在',
      },
      AddError: {
        code: 500002,
        error: '添加人员失败',
      },
      EidtError: {
        code: 500003,
        error: '编辑人员失败',
      },
      DuplicatedPersonNoError: {
        code: 500004,
        error: '该员工编号已存在，请勿重复创建',
      },
      DuplicatedNameError: {
        code: 500005,
        error: '新增姓名重复',
      },
      DeleteError: {
        code: 500006,
        error: '移除人员失败',
      },
      BatchLimited: {
        code: 500007,
        error: '批量添加超限',
      },
      DuplicatedPhoneError: {
        code: 500008,
        error: '手机号码重复',
      },
      PersonDuplicateError: {
        code: 500009,
        error: '该企业已关联到其他同名人员，请核实后操作',
      },
      DuplicatedCardIdError: {
        code: 500010,
        error: '您输入的证件号码已存在，请核实后再添加',
      },
      CardIdError: {
        code: 500011,
        error: '您输入的证件号码不符合规则，请核实后再添加',
      },
      CardIdDuplicateError: {
        code: 500012,
        error: '您输入的多个证件号码存在重复，请核实后再添加',
      },
    },
  },
  // 4000XX
  Import: {
    FileError: {
      code: 400001,
      error: '文件上传失败！该功能仅支持Excel（xls、xlsx）格式文件！',
    },
    TemplateError: {
      code: 400002,
      error: '文件上传失败！上传文件不符合模板要求，请下载最新模板后重新上传！',
    },
    DataNotFount: {
      code: 400003,
      error: '您导入的文件中无有效数据，请检查后重新上传！',
    },
    Limited: {
      code: 400004,
      error: '单次导入最大数量不超过#limit#条!',
    },
    FileNameError: {
      code: 400006,
      error: '您导入的文件名称长度过长，请控制在100个字符以内',
    },
    FileSizeError: {
      code: 400007,
      error: '文件上传失败！上传文件大小不能超过2M，请检查后重新上传！',
    },
  },
  // 5000XX
  Diligence: {
    Detail: {
      NeedKeyNo: {
        code: 500001,
        error: '缺少keyNo',
      },
      NeedSubDimensionKey: {
        code: 500002,
        error: '缺少subDimensionKey',
      },
      GetDetailsFailed: {
        code: 500003,
        error: '获取维度详情失败',
      },
      NeedDimensionKey: {
        code: 500004,
        error: '缺失维度key',
      },
      VerifyPersonFail: {
        code: 500005,
        error: '人员核实失败',
      },
      NeedCompanyId: {
        code: 500006,
        error: '缺少companyId',
      },
      PersonNotFound: {
        code: 500007,
        error: '人员不存在，请核实',
      },
      NeedPersonKeyNo: {
        code: 500008,
        error: '缺少人员keyNo',
      },
      ApplyDuplicated: {
        code: 500009,
        error: '排查请求已发起，请勿重复提交。',
      },
      Failed: {
        code: 500010,
        error: '排查异常，请稍后重试。',
      },
      CustomerOverRange: {
        code: 500011,
        error: '被排查企业关联企业数超限，请调整交叉重叠企业关联范围后排查',
      },
      BlackListOverRange: {
        code: 500012,
        error: '被排查企业关联企业数超限，请调整内部黑名单企业关联范围后排查',
      },
    },
    Analyze: {
      NoResult: {
        code: 500101,
        error: '第三方列表企业还未执行过全量风险排查巡检',
      },
    },
    Snapshot: {
      NotFound: {
        code: 500201,
        error: '快照不存在或者无权限访问',
      },
      //未生成
      NotGenerated: {
        code: 500202,
        error: '数据处理中，请稍后再试',
      },
    },
    Setting: {
      Empty: {
        code: 5000401,
        error: '您尚未设置启用风险排查事项，请前往设置中心进行设置',
      },
    },
    Common: {
      NotFound: {
        code: 500501,
        error: '找不到持续排查记录或者没有访问权限',
      },
      Nonsupport: {
        code: 500502,
        error: '暂不支持香港公司、台湾企业、海外企业、美股企业、机关单位',
      },
      UnknownType: {
        code: 500503,
        error: '未知的维度类型',
      },
    },
    // 已废弃
    ContinuousDiligence: {
      DataIncomplete: {
        code: 500601,
        error: '持续监控-数据不完整',
      },
      MissedEndDiligenceData: {
        code: 500602,
        error: '持续监控-截止时间当天的数据不完整',
      },
    },
    // 招标排查
    Tender: {
      OnlyOneProcess: {
        code: 500701,
        error: '已有任务进行中，请稍后',
      },
      OutOfCompanyCount: {
        code: 500702,
        error: '单次招标排查企业数量不超过#limit#家',
      },
    },
    // 招标排查
    Specific: {
      OnlyOneProcess: {
        code: 500701,
        error: '已有任务进行中，请稍后',
      },
      OutOfCompanyCount: {
        code: 500702,
        error: '单次特定关系排查企业数量不超过#limit#家',
      },
    },
  },
  Batch: {
    CreateFailed: {
      code: 600001,
      error: '创建批量任务失败',
    },
    OnlyOneBatchProcess: {
      code: 600002,
      error: '任务进行中，请稍后',
    },
    DataNotFount: {
      code: 600003,
      error: '无有效导出数据！',
    },
    OutOfTimes: {
      code: 600004,
      error: '已达到每天最执行次数上限！',
    },
    ThirdPartyEmpty: {
      code: 600005,
      error: '风险巡检是针对巡检方案设置的企业范围进行排查巡检，请检查对应企业范围下是否存在有效企业',
    },
    ExportBatchLimited: {
      code: 600006,
      error: '单次导出最大数量不超过#limit#条！',
    },
    RetryFailed: {
      code: 600007,
      error: '任务重试失败',
    },
    NotNeedRetry: {
      code: 600008,
      error: '无需要重试的内容',
    },
    DiscontinueFailed: {
      code: 600009,
      error: '任务中止失败',
    },
    UnSupportedDiscontinue: {
      code: 600010,
      error: '任务不支持中止操作',
    },
    OutOfLimit: {
      code: 600011,
      error: '当前已达到任务执行数量上限，请稍后再试！',
    },
    Failed: {
      code: 600012,
      error: '任务处理异常，请联系客服！',
    },
  },
  Customer: {
    DuplicatedError: {
      code: 700001,
      error: '企业名称已存在',
    },
    DuplicatedCompany: {
      code: 700002,
      error: '该企业已存在，请勿重复提交',
    },
    Analyze: {
      NoResult: {
        code: 500101,
        error: '第三方列表企业还未执行过全量风险排查巡检',
      },
    },
    NotValidCompany: {
      code: 700003,
      error: '该企业不存在，是否已经添加到合作列表？',
    },
  },
  // 8000xx
  Common: {
    CallbackFailed: {
      code: 800002,
      error: '回调失败',
    },
    RequestFailed: {
      code: 800003,
      error: '服务端接口请求失败',
    },
    SMS: {
      Error: {
        code: 900200,
        error: '短信发送失败',
      },
      Duplicated: {
        code: 900201,
        error: '60秒内只允许发送一次验证码',
      },
    },
    Request: {
      AccessDenied: {
        code: 900100,
        error: '访问被拒绝',
      },
      ReachLimit: {
        code: 900101,
        error: '请求数达到最大限制',
      },
      Duplicated: {
        code: 900102,
        error: '记录已存在，请勿重复提交',
      },
      NotFound: {
        code: 900103,
        error: '记录不存在',
      },
      BatchLimited: {
        code: 900104,
        error: '批量操作数量超过上限',
      },
      CreateReachLimit: {
        code: 900105,
        error: '创建数量已达上限！',
      },
    },
  },
  // 9000xx
  Bundle: {
    PersonReachLimit: {
      code: 900001,
      error: '您可管理的人员数量已达到上限！',
    },
    InnerBlacklistReachLimit: {
      code: 900002,
      error: '您可管理的内部黑名单数量已达到上限！',
    },
    CustomerReachLimit: {
      code: 900003,
      error: '您可管理的第三方数量已达到上限！',
    },
    DailyDiligencReachLimit: {
      code: 900004,
      error: '您可每日排查此时已达到上限！',
    },
    CounterOperationNotFound: {
      code: 900005,
      error: '计数器操作失败！ CounterOperation 不存在！',
    },
  },
  Monitor: {
    // 10000xx
    Group: {
      NotFound: {
        code: 1000001,
        error: '分组不存在',
      },
      NotDelete: {
        code: 1000002,
        error: '无法删除，至少要保留一个分组',
      },
      RecycleFull: {
        code: 1000003,
        error: '回收站已满，无法删除',
      },
      TypeError: {
        code: 1000004,
        error: '分组类型错误',
      },
      AddError: {
        code: 1000005,
        error: '添加分组失败',
      },
      EditError: {
        code: 1000006,
        error: '编辑分组失败',
      },
      DuplicatedError: {
        code: 1000007,
        error: '新增分组名重复',
      },
      DeleteError: {
        code: 1000008,
        error: '移除分组失败',
      },
      NotMonitor: {
        code: 1000009,
        error: '监控未开启',
      },
      NoPermission: {
        code: 1000010,
        error: '无查看权限',
      },
      BatchLimited: {
        code: 1000011,
        error: '批量添加超限',
      },
      Limited: {
        code: 1000012,
        error: '分组数量超限',
      },
      CanNotEdited: {
        code: 1000013,
        error: '该分组不可编辑',
      },
      CanNotDeleteWithCompany: {
        code: 1000014,
        error: '该分组下存在持续排查企业，不能删除分组！',
      },
    },
    // 9001xx
    Company: {
      Duplicated: {
        code: 900102,
        error: '记录已存在，请勿重复提交',
      },
      GroupNotExists: {
        code: 900102,
        error: '分组不存在',
      },
      GroupCompanyLimit: {
        code: 900102,
        error: '该分组下的公司数量已达到上限',
      },
      AccessDenied: {
        code: 900100,
        error: '访问被拒绝',
      },
      ReachLimit: {
        code: 900101,
        error: '请求数达到最大限制',
      },
      NotFound: {
        code: 900103,
        error: '记录不存在',
      },
      BatchLimited: {
        code: 900104,
        error: '批量操作数量超过上限',
      },
      CreateReachLimit: {
        code: 900105,
        error: '创建数量已达到最大限制',
      },
      InvalidRelatedCompanies: {
        code: 900106,
        error: '无效的关联方',
      },
    },
    // 9003xx
    Dynamic: {
      MonitorDynamicTypeNotFound: {
        code: 900301,
        error: '监控动态类型不存在',
      },
      NotNeedHandle: {
        code: 900302,
        error: '监控动态无需处理',
      },
    },
  },
  TenderAlert: {
    CreateReachLimit: {
      code: 1100001,
      error: '创建数量已达到最大限制',
    },
    NameDuplicated: {
      code: 1100002,
      error: '方案名称重复，请核实后重新提交',
    },
    AlertCompanyExceedLimit: {
      code: 1100003,
      error: '预警监控企业过多，最多支持500家',
    },
    SubscriptionTenderIsZero: {
      code: 1100004,
      error: '暂未找到相关数据，换个新的关键词试试吧',
    },
    SubscriptionTenderExceedLimit: {
      code: 1100005,
      error: '关键词设置太过宽泛，请输入更精准的关键词试试吧',
    },
    TenderDetailExportExceedLimit: {
      code: 1100006,
      error: '您的标讯详情导出额度已用完',
    },
  },
  Setting: {
    CreateReachLimit: {
      code: 1200001,
      error: '创建数量已达到最大限制',
    },
    NameDuplicated: {
      code: 1200002,
      error: '模型名称重复',
    },
    AtLeastOne: {
      code: 1200003,
      error: '必须存在一个模型',
    },
    CanNotDelete: {
      code: 1200004,
      error: '模型被使用，无法删除',
    },
    InvalidId: {
      code: 1200005,
      error: '模型ID不合法',
    },
    Empty: {
      code: 1200006,
      error: '当前模型无有效设置项，请前往设置中心进行设置',
    },
  },
  Openapi: {
    Bidding: {
      UnMatchedCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
      InvalidTenderNo: {
        code: 1003,
        error: '排查编号不存在相应的排查记录',
      },
      //排查公司数量不足
      InsufficientCompany: {
        code: 1004,
        error: '排查公司数量必须大于等于2家',
      },
    },
    Diligence: {
      UnMatchedCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
    },
    Customer: {
      UnMatchedCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
    },
    InnerBlacklist: {
      UnMatchedCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
    },
    Monitor: {
      UselessCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
      InvalidCompany: {
        code: 1003,
        error: '企业不存在',
      },
      UselessGroup: {
        code: 1004,
        error: '分组不存在',
      },
    },
  },
};
