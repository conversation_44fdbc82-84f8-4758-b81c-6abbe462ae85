import { IndicatorTypeEnums } from 'libs/model/settings/IndicatorTypeEnums';
import { DimensionDefinitionPO } from 'libs/model/diligence/pojo/dimension/DimensionDefinitionPO';
import { DimensionRiskLevelEnum } from 'libs/enums/diligence/DimensionRiskLevelEnum';
import { DimensionLevel3Enums } from 'libs/enums/diligence/DimensionLevel3Enums';
import { DimensionLevel2Enums } from '../enums/diligence/DimensionLevel2Enums';

export const DefaultOuterBlacklistItems: DimensionDefinitionPO[] = [
  {
    key: DimensionLevel3Enums.GovernmentPurchaseIllegal,
    name: '政府采购严重违法失信行为记录名单',
    description:
      '依据《关于报送政府采购严重违法失信行为信息记录的通知》（财办库[2014]526号）发布公示的存在政府采购领域违法失信行为的信用主体名单。数据来源：中国政府采购网、信用中国等',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['25'],
    type: IndicatorTypeEnums.generalItems,
    sort: 1,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.SafetyProductionEnterprise,
    name: '安全生产领域失信生产经营单位',
    description: '应急管理部门认定的在安全生产领域存在失信行为的生产经营单位及其法定代表人、主要负责人（含实际控制人）。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['2'],
    type: IndicatorTypeEnums.generalItems,
    sort: 2,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.CustomsList,
    name: '海关失信企业名单',
    description: '由海关部门依据《中华人民共和国海关企业信用管理暂行办法》认定的失信企业名单。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['7'],
    type: IndicatorTypeEnums.generalItems,
    sort: 3,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.IntellectualPropertyIllegal,
    name: '知识产权（专利）领域严重失信联合戒对象名单',
    description:
      '知识产权局依法依规认定存在重复专利侵权行为、不依法执行行为、专利代理严重违法行为、专利代理师资格证书挂靠行为、非正常申请专利行为、提供虚假文件行为的信用主体名单。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['43'],
    type: IndicatorTypeEnums.generalItems,
    sort: 4,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.EnvironmentalProtection,
    name: '环保失信黑名单',
    description: '由生态环境部依法认定并予以公示的失信企业名录。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['13', '65'],
    type: IndicatorTypeEnums.generalItems,
    sort: 5,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.LaborGuarantee,
    name: '劳动保障违法',
    description: '人社部门认定的存在重大违法事项的企业名录。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['6'],
    type: IndicatorTypeEnums.generalItems,
    sort: 6,
    template: '',
    template2: '',
  },
  // RA-14334 数据停用
  {
    key: DimensionLevel3Enums.ListedCompanyIllegal,
    name: '违法失信上市公司',
    description: '由中国证监会及其派出机构认定予以行政处罚、市场禁入的上市公司及相关机构和人员等责任主体。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['18'],
    type: IndicatorTypeEnums.generalItems,
    sort: 7,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.GovProcurementIllegal,
    name: '国央企采购黑名单',
    description: '各个国企/央企发布公示的存在采购领域失信行为的信用主体名单。数据来源：各个国企/央企官网等',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 0,
    child: ['74', '77', '78'],
    type: IndicatorTypeEnums.generalItems,
    sort: 8,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.FgwBlackList,
    name: '发改委黑名单',
    description: '发展改革委员会认定的严重失信行为的市场主体',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['57'],
    type: IndicatorTypeEnums.generalItems,
    sort: 9,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel2Enums.ForeignExportControls,
    name: '出口管制合规风险企业清单',
    description:
      '被国外相关部门（机构）列入出口管制合规风险企业清单，包含美国出口管制企业清单、亚投行禁止名单、联合国综合制裁名单、英国制裁名单、加拿大自治制裁综合清单、澳大利亚综合制裁清单、 世界银行不合格公司和个人名单、欧洲投资银行排除名单、日本经济产业省贸易管制最终用户名单、英国财政部金融制裁目标综合清单等',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '12', '13', '14', '15', '17', '18', '19', '22', '23', '24', '25'],
    type: IndicatorTypeEnums.generalItems,
    sort: 10,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel2Enums.WorldBankBlacklist,
    name: '世界银行“黑名单”',
    description: '世界银行(World Bank)在其网站公布的制裁名单中涉及的实体企业等',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 0,
    child: [],
    type: IndicatorTypeEnums.generalItems,
    sort: 11,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel2Enums.SupervisionOfKeyIndustry,
    name: '重点行业领域监管黑名单',
    description:
      '包括出入境检验检疫信用管理严重失信企业名单、电力行业严重违法失信主体、拖欠农民工工资黑名单、统计领域严重失信企业及其有关人员、运输物流行业严重失信黑名单、电子商务领域黑名单、建筑工程领域黑名单、医疗医领域黑名单、价格失信黑名单、石油天然气行业严重违法失信主体、招投标活动失信行为企业、建筑行业不良行为、严重违法超限超载运输当事人名单等',
    strategyModel: { level: DimensionRiskLevelEnum.Medium },
    status: 1,
    child: ['40', '41', '68', '79', '39', '4', '38', '5', '54', '3', '24', '28', '33', '34', '55', '56', '69', '98', '11', '1'],
    // child: ['40', '41', '68', '79', '39', '4', '38', '14', '5', '54', '3', '24', '28', '33', '34', '55', '56', '69', '98', '11'],
    type: IndicatorTypeEnums.generalItems,
    sort: 12,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.MedicalMedicineIllegal,
    name: '医疗医药领域黑名单',
    description: '医药、医药卫生行业企业被主管部门纳入黑名单',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['40', '41', '68', '79', '39'],
    type: IndicatorTypeEnums.generalItems,
    sort: 13,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.InspectionAuthority,
    name: '出入境检验检疫信用管理严重失信企业名单',
    description: '国家质量监督检验检疫总局依法认定的因严重违法违规行为多次受到处罚的企业名单。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['4'],
    type: IndicatorTypeEnums.generalItems,
    sort: 14,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.PriceBreakPromise,
    name: '价格失信黑名单',
    description: '经营者违反价格法律、法规和规章，发生情节严重的价格违法行为被主管单位列入黑名单',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['38'],
    type: IndicatorTypeEnums.generalItems,
    sort: 15,
    template: '',
    template2: '',
    isHidden: true,
  },
  // RA-14334 数据停用
  // {
  //   key: DimensionLevel3Enums.QualityCredit,
  //   name: '严重质量失信黑名单',
  //   description: '质监部门依法依规认定为D级的企业，且情节特别严重、造成恶劣社会影响的企业纳入严重质量失信“黑名单”。数据来源：信用中国',
  //   strategyModel: { level: DimensionRiskLevelEnum.High },
  //   status: 1,
  //   child: ['14'],
  //   type: IndicatorTypeEnums.generalItems,
  //   sort: 16,
  //   template: '',
  //   template2: '',
  //   isHidden: true,
  // },
  {
    key: DimensionLevel3Enums.MigrantWorkers,
    name: '拖欠农民工工资黑名单',
    description: '人社部门认定的违反国家工资支付法律法规规章规定，存在拖欠工资情形的用人单位及其法定代表人 、其他责任人。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['5'],
    type: IndicatorTypeEnums.generalItems,
    sort: 17,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.ElectricityIndustryIllegal,
    name: '电力行业严重违法失信主体',
    description: '经政府主管部门认定存在严重违法失信行为并纳入电力行业“黑名单”的市场主体。数据来源：中国电力企业联合会',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['54'],
    type: IndicatorTypeEnums.generalItems,
    sort: 18,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.Statistics,
    name: '统计领域严重失信企业及其有关人员',
    description: '统计部门依法认定并通过国家统计局网站公示。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['3'],
    type: IndicatorTypeEnums.generalItems,
    sort: 19,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.EcBlacklist,
    name: '电子商务领域黑名单',
    description: '由国家发展改革委员会及各相关部门在电子商务及分享经济领域认定的炒信行为主体。数据来源：全国信用信息共享平台（对外公示主要为信用中国）',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['24'],
    type: IndicatorTypeEnums.generalItems,
    sort: 20,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.LogisticsIndustryIllegal,
    name: '运输物流行业严重失信黑名单',
    description: '依法依规对涉及性质恶劣、情节严重、社会危害较大的违法失信行为的运输物流行业市场主体列入“黑名单”。数据来源：信用中国',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['28'],
    type: IndicatorTypeEnums.generalItems,
    sort: 21,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.ConstructionEngineeringBlacklist,
    name: '建筑工程领域黑名单',
    description: '建筑工程领域(行业)特定严重失信行为的市场主体',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 1,
    child: ['33', '34'],
    type: IndicatorTypeEnums.generalItems,
    sort: 22,
    template: '',
    template2: '',
    isHidden: true,
  },
  {
    key: DimensionLevel3Enums.ArmyProcurementIllegal,
    name: '军队采购失信名单',
    description: '根据军队供应商管理相关规定被列入采购失信名单的企业；数据来源：军队采购网',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 0,
    child: ['75', '76'],
    type: IndicatorTypeEnums.generalItems,
    sort: 23,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.PrivateEnterpriseCooperationBlacklist,
    name: '名企不合作企业清单',
    description: '大型企业对外公开发布的永不合作主体清单',
    strategyModel: { level: DimensionRiskLevelEnum.Medium },
    status: 1,
    child: ['103'],
    type: IndicatorTypeEnums.keyItems,
    sort: 24,
    template: '',
    template2: '',
  },
  {
    key: DimensionLevel3Enums.AIIBBlackList,
    name: '亚投行禁止名单',
    description:
      '亚投行自行调查公布的禁止名单，以及基于非洲开发银行集团、亚洲开发银行、欧洲复兴开发银行、美洲开发银行和世界银行集团(简称多边开发银行)之间相互强制执行对方禁止行动的协议中涉及的实体企业。数据来源：亚洲开发银行等银行官网',
    strategyModel: { level: DimensionRiskLevelEnum.High },
    status: 0,
    child: [],
    type: IndicatorTypeEnums.generalItems,
    sort: 25,
    template: '',
    template2: '',
    isHidden: true,
  },
];

export const CompOvsSanctionsFieldNameMapping = {
  nationcode: '国家代码',
  sanctionscode: '制裁代码',
  sanctionstypecode: '制裁种类代码',
  sanctionstype: '制裁种类',
  name: '名称',
  sanctionsregulations: '制裁条例/名单',
  sanctionsmeasure: '制裁措施/项目',
  sanctionsreason: '制裁原因',
  designateddate: '指定日期',
  remark: '备注',
  identifications: '认证信息',
  alias: '别名信息',
  address: '地址信息',
  keynolist: '企业匹配信息',
  federalregisternotice: '联邦公报通知',
  expirationdate: '截止日期',
  startdate: '开始日期',
  lastupdatedate: '最后更新日期',
  sourceurl: '来源',
};

export const ForeignExportControlsCodeTranslation = {
  '1': {
    name: '美国财政部办公室OFAC制裁名单（U.S. Department of the Treasury - Office of Foreign Assets Control (OFAC) Sanctions List）',
    court: '美国财政部办公室OFAC',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '2': {
    name: '英国外交、联邦与发展办公室制裁局FCDO制裁名单（UK - Foreign, Commonwealth & Development Office (FCDO) Sanctions List）',
    court: '英国外交、联邦与发展办公室制裁局FCDO',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'sanctionsreason',
      'designateddate',
      'lastupdatedate',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '3': {
    name: '美国国土安全部UFLPA实体清单（U.S. Department of Homeland Security - Uyghur Forced Labor Prevention Act (UFLPA) Entity List）',
    court: '美国国土安全部UFLPA',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'sanctionsreason', 'designateddate', 'sourceurl'],
  },
  '4': {
    name: '美国国防部CMCC中国军事公司名单（U.S. Department of Defense - Chinese Military Company List）',
    court: '美国国防部CMCC',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'designateddate', 'keynolist', 'sourceurl'],
  },
  '5': {
    name: '美国国务院国防贸易管制局禁止名单（U.S. Department of State - Directorate of Defense Trade Controls Debarment List）',
    court: '美国国务院国防贸易管制局',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'designateddate', 'federalregisternotice', 'sourceurl'],
  },
  '6': {
    name: '美国BIS实体清单（U.S. Department of Commerce - Bureau of Industry and Security (BIS) Entity List）',
    court: '美国商业部工业安全局BIS',
    fields: [
      'nationcode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'sanctionsreason',
      'alias',
      'address',
      'keynolist',
      'federalregisternotice',
      'sourceurl',
    ],
  },
  '7': {
    name: '美国BIS被拒绝人员名单（U.S. Department of Commerce - Bureau of Industry and Security (BIS) Denied Persons List ）',
    court: '美国商业部工业安全局BIS',
    fields: [
      'nationcode',
      'sanctionstypecode',
      'name',
      'sanctionsregulations',
      'designateddate',
      'address',
      'expirationdate',
      'federalregisternotice',
      'sourceurl',
    ],
  },
  '8': {
    name: '美国BIS未经验证的列表（U.S. Department of Commerce - Bureau of Industry and Security (BIS) Unverified List ）',
    court: '美国商业部工业安全局BIS',
    fields: ['nationcode', 'sanctionstypecode', 'name', 'sanctionsregulations', 'address', 'keynolist', 'federalregisternotice', 'sourceurl'],
  },
  '9': {
    name: '美国BIS军事最终用户列表（U.S. Department of Commerce - Bureau of Industry and Security (BIS) Military End User List）',
    court: '美国商业部工业安全局BIS',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'address', 'keynolist', 'federalregisternotice', 'sourceurl'],
  },
  '12': {
    name: '亚投行禁止名单（Asian Infrastructure Investment Bank (AIIB) Debarment List）',
    court: '',
    fields: [
      'nationcode',
      'sanctionstypecode',
      'name',
      'sanctionsregulations',
      'sanctionsreason',
      'designateddate',
      'address',
      'expirationdate',
      'startdate',
      'sourceurl',
    ],
  },
  '13': {
    name: '加拿大自治综合制裁名单（CA - Consolidated Canadian Autonomous Sanctions List）',
    court: '',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'address', 'sourceurl'],
  },
  '14': {
    name: '联合国安理会综合清单（United Nations Security Council Consolidated List）',
    court: '',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'designateddate',
      'lastupdatedate',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '15': {
    name: '联合国制裁委员会制裁名单（United Nations Sanctions Committee Sanctions List）',
    court: '',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'designateddate',
      'lastupdatedate',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '17': {
    name: '澳大利亚综合制裁清单（AU - Dfat Consolidated Sanctions List）',
    court: '',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '18': {
    name: '世界银行不合格公司和个人名单（World Bank Blacklist）',
    court: '世界银行',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '19': {
    name: '美国国务院防扩散制裁（US - Department Of State Nonproliferation Sanctions List）',
    court: '美国国务院',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
    ],
  },
  '22': {
    name: '欧洲投资银行排除名单（European Investment Bank exclusion list）',
    court: '欧洲投资银行',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
      'expirationdate',
    ],
  },
  '23': {
    name: '日本经济产业省贸易管制最终用户名单（List of trade control end users of the Ministry of Economy, Trade and Industry of Japan）',
    court: '日本经济产业省',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
      'expirationdate',
    ],
  },
  '24': {
    name: '英国财政部金融制裁目标综合清单（UK Treasury Consolidated list of financial sanctions targets）',
    court: '英国财政部',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
      'expirationdate',
    ],
  },
  '25': {
    name: '美国国务院外国恐怖组织',
    court: '美国国务院',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
      'sourceurl',
      'expirationdate',
    ],
  },
};
