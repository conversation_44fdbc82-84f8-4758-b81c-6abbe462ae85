import { StreamTableEnums } from '../enums/data/StreamTableEnums';
import { RoverBundleCounterType } from '@kezhaozhao/saas-bundle-service/dist_client/client/config/bundle/rover.bundle';
import { DimensionRiskLevelEnum } from '../enums/diligence/DimensionRiskLevelEnum';
import { DimensionLevel3Enums } from '../enums/diligence/DimensionLevel3Enums';

export const TimeInterval3Hour = 1000 * 60 * 60 * 3;
export const Time_Minutes_10 = 1000 * 60 * 10;
export const API_BASE = '/rover';

export enum RoleScope {
  Personal = 1,
  Department = 2,
  SubDepartment = 3,
  Global = 4,
}

export const ModuleTypes = {
  Company: { name: 'Company', code: 0 },
  Credit: { name: 'Credit', code: 1 },
};

export class ModuleType {
  name: string;
  code: number;
}

/**
 * 支持的公司类型
 * 3:香港企业，4:机关单位，5：台湾企业
 */
export const allowType = ['0', '1', '11', '12', '3', '4', '5', '15'];

/**
 *不支持的企业类型
 */
export const ForbiddenStandardCode = ['001003', '001013', '001014'];

export enum ExportSource {
  Item = 1,
}

export enum HotWordCat {
  hotWord = 1032,
  bossHotWord = 146527,
  risknHotWord = 146528,
  deadbeatHotWord = 146517,
  investReportHotWord = 146559,
}

export enum ServiceCode {
  SAAS_CZB = 'SAAS_CZB',
  SAAS_JZCC = 'SAAS_JZCC',
  SAAS_KZZ = 'SAAS_KZZ',
  KZZ_CRM = 'KZZ_CRM',
  KZZ_ENT = 'KZZ_ENT',
  SAAS_ROVER = 'SAAS_ROVER',
  OUT_BOUND = 'OUT_BOUND',
}

export enum RelatedType {
  /** 名称匹配 */
  name = 'name',
  /** 股权关联 */
  investment = 'investment',
  /** 投资关联 */
  shareholding = 'shareholding',
  /** 任职关联 */
  employment = 'employment',
  /** 疑似关联 */
  suspected = 'suspected',
}

/**
 * 蔡司专用
 */
export const ZEISSDimensions = [DimensionLevel3Enums.PunishedEmployeesWorkingOutside, DimensionLevel3Enums.PunishedEmployeesForeignInvestment];
export const detailUrl = ['/rover/v1/api/company/detail/get-detail'];

export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

export const DATE_FORMAT = 'YYYY-MM-DD';

const prefix = '';
export const QueueNames = {
  BatchJob: 'rover-batch-job-queue' + prefix,
  BatchJobDD: 'rover-batch-job-queue-dd' + prefix,
  BatchJobTender: 'rover-batch-job-queue-tender' + prefix,
  BatchMonitor: 'rover-batch-job-monitor-queue' + prefix,
  BatchExportJob: 'rover-batch-export-job-queue' + prefix,
  // BatchExportMonitor: 'rover-batch-export-job-monitor-queue' + prefix,
  DiligenceAnalyze: 'rover-diligence-analyze' + prefix,
  DiligenceSnapshot: 'rover-diligence-snapshot' + prefix,
  BatchDiligenceSnapshot: 'rover-diligence-snapshot-batch' + prefix,
  ContinuousDiligence: 'rover-diligence-continuous' + prefix,
  RoverDataSyncQUeue: 'ent-data-sync-rover',
  TenderDiligenceQueue: 'rover-tender-diligence' + prefix,
  BatchSpecificQueue: 'rover-batch-specific' + prefix,
  BatchPotentialQueue: 'rover-batch-potential' + prefix,
};

/**
 * 简易注销结果枚举设置
 */
export const SimpleCancellationTypes = [
  {
    key: '2',
    keyName: '正在进行',
    status: 1,
  },
  {
    key: '3',
    keyName: '准予/准许注销',
    status: 1,
  },
  {
    key: '1',
    keyName: '不予受理',
    status: 0,
  },
  {
    key: '4',
    keyName: '已撤销',
    status: 0,
  },
];

/**
 * 分组场景
 */
export enum GroupScene {
  /**
   * 0-通用分组
   */
  Normal = 0,
  /**
   * 1-曾被处罚的现任员工或前员工分组（卡尔蔡司专用，不可删除）
   */
  ZEISS = 1,
}

export enum CounterOperation {
  Increase = 'increase',
  Decrease = 'decrease',
  Check = 'check',
}

export const OperInfoMapping = {
  Org: {
    // 公司
    Company: 0,
    // 社会组织
    Org: 1,
    // 主要人员
    Employee: 2,
    // 香港公司
    HKCompany: 3,
    // 政府机构和学校
    Government: 4,
    // 台湾公司
    TWCompany: 5,
    // 私募基金产品
    PefundProduct: 6,
    // 医院
    Hospital: 7,
    // 海外公司
    Oversea: 8,
    // 海外公司
    Oversea2: 9,
    // 基金会
    Fund: 10,
    // 事业单位
    Institution: 11,
    // 律师事务所
    LawOffice: 12,
    // 投资机构
    Invest: 13,
    // 美股
    UsStock: 14,
    // 无法判断
    CompanyWithoutKeyNo: -1,
    // 没有Id的人名
    PersonWithoutCerNo: -2,
    // 其他
    Other: -3,
  },
  OperType: {
    1: '法定代表人',
    2: '执行事务合伙人',
    3: '负责人',
    4: '经营者',
    5: '投资人',
    6: '董事长',
    7: '理事长',
    8: '代表人',
  },
};

export const ResourceTableConst = {
  [StreamTableEnums.Customer]: 'customer',
  [StreamTableEnums.Person]: 'person',
  [StreamTableEnums.InnerBlacklist]: 'inner_blacklist',
  [StreamTableEnums.DueDiligence]: 'due_diligence',
  [StreamTableEnums.DueDiligenceTender]: 'due_diligence_tender_history',
  [StreamTableEnums.Batch]: 'batch',
  [StreamTableEnums.AnalyzeRisk]: 'batch', //风险巡检记录也是在 batch 表中
};

export const PermissionScope = {
  [StreamTableEnums.Customer]: 2031,
  [StreamTableEnums.Person]: 2061,
  [StreamTableEnums.InnerBlacklist]: 2041,
  [StreamTableEnums.DueDiligence]: 2021,
  [StreamTableEnums.DueDiligenceTender]: 2110,
  [StreamTableEnums.AnalyzeRisk]: 2014,
};

/**
 * 负面新闻 topics
 */
export const TopicTypes = [
  { key: 'all', keyName: '不限', status: 1 },
  {
    key: '40001',
    keyName: '停工停产',
    status: 0,
  },
  {
    key: '40002',
    keyName: '生产事故',
    status: 0,
  },
  {
    key: '40004',
    keyName: '偷税漏税',
    status: 0,
  },
  {
    key: '40017',
    keyName: '资产查封/扣押/冻结',
    status: 0,
  },
  // {
  //   key: '40030',
  //   keyName: '重大经济损失',
  //   status: 0,
  // },
  {
    key: '40008',
    keyName: '破产清算',
    status: 0,
  },
  {
    key: '40011',
    keyName: '垄断信息',
    status: 0,
  },
  {
    key: '40015',
    keyName: '经营失联(异常)',
    status: 0,
  },
  {
    key: '40023',
    keyName: '非法集资',
    status: 0,
  },
  {
    key: '40014',
    keyName: '资金挪用/占用',
    status: 0,
  },
  {
    key: '20001',
    keyName: '财务造假',
    status: 0,
  },
  {
    key: '20002',
    keyName: '审计意见',
    status: 0,
  },
  {
    key: '20003',
    keyName: '担保预警',
    status: 0,
  },
  {
    key: '20004',
    keyName: '资金风险',
    status: 0,
  },
  {
    key: '20005',
    keyName: '计提坏账准备',
    status: 0,
  },
  {
    key: '20006',
    keyName: '财报延期披露',
    status: 0,
  },
  {
    key: '30001',
    keyName: '高层被查',
    status: 0,
  },
  {
    key: '30002',
    keyName: '高管违法',
    status: 0,
  },
  {
    key: '30003',
    keyName: '高管失联/无法履职/丑闻',
    status: 0,
  },
  {
    key: '30004',
    keyName: '贪污受贿',
    status: 0,
  },
  {
    key: '10002',
    keyName: '兑付/偿付不确定',
    status: 0,
  },
  {
    key: '10003',
    keyName: '债券/债务违约',
    status: 0,
  },
  // {
  //   key: '10004',
  //   keyName: '中债隐含评级',
  //   status: 0,
  // },
  {
    key: '70004',
    keyName: '坍塌事故',
    status: 0,
  },
  {
    key: '80001',
    keyName: '违法违规',
    status: 0,
  },
  {
    key: '80002',
    keyName: '立案调查',
    status: 0,
  },
  {
    key: '80006',
    keyName: '暴雷事件',
    status: 0,
  },
  // {
  //   key: '80007',
  //   keyName: '中毒事故',
  //   status: 0,
  // },
  {
    key: '12016',
    keyName: '税务注销登记',
    status: 0,
  },
  {
    key: '40003',
    keyName: '拖欠货款',
    status: 0,
  },
  {
    key: '40005',
    keyName: '资产出售',
    status: 0,
  },
  {
    key: '40006',
    keyName: '诉讼纠纷',
    status: 0,
  },
  {
    key: '40016',
    keyName: '减资/分立/合并',
    status: 0,
  },
  {
    key: '40012',
    keyName: '侵权抄袭',
    status: 0,
  },
  {
    key: '40013',
    keyName: '环保问题',
    status: 0,
  },
  {
    key: '40007',
    keyName: '股权冻结',
    status: 0,
  },
  {
    key: '40021',
    keyName: '业绩亏损',
    status: 0,
  },
  // {
  //   key: '40022',
  //   keyName: '丧失经销商资质',
  //   status: 0,
  // },
  {
    key: '40010',
    keyName: '业绩下降',
    status: 0,
  },
  // {
  //   key: '40028',
  //   keyName: '关联方不利变化',
  //   status: 0,
  // },
  // {
  //   key: '40029',
  //   keyName: '关联方人事变动',
  //   status: 0,
  // },
  {
    key: '40025',
    keyName: '体制改革',
    status: 0,
  },
  // {
  //   key: '40026',
  //   keyName: '竞争力份额下降',
  //   status: 0,
  // },
  // {
  //   key: '40024',
  //   keyName: '股东利益斗争',
  //   status: 0,
  // },
  {
    key: '40020',
    keyName: '维权',
    status: 0,
  },
  {
    key: '40018',
    keyName: '合同纠纷',
    status: 0,
  },
  {
    key: '40009',
    keyName: '合作终止',
    status: 0,
  },
  {
    key: '40019',
    keyName: '客户投诉',
    status: 0,
  },
  {
    key: '50001',
    keyName: '监管关注',
    status: 0,
  },
  {
    key: '50002',
    keyName: '监管谈话',
    status: 0,
  },
  {
    key: '50003',
    keyName: '监管警示',
    status: 0,
  },
  {
    key: '50004',
    keyName: '公开谴责',
    status: 0,
  },
  {
    key: '50005',
    keyName: '通报批评',
    status: 0,
  },
  {
    key: '50006',
    keyName: '市场禁入',
    status: 0,
  },
  {
    key: '30005',
    keyName: '裁员相关',
    status: 0,
  },
  {
    key: '30006',
    keyName: '拖欠薪资',
    status: 0,
  },
  {
    key: '30007',
    keyName: '员工罢工',
    status: 0,
  },
  // {
  //   key: '30008',
  //   keyName: '自杀猝死',
  //   status: 0,
  // },
  {
    key: '30009',
    keyName: '欠缴社保',
    status: 0,
  },
  {
    key: '30010',
    keyName: '商业机密被泄露',
    status: 0,
  },
  {
    key: '30011',
    keyName: '实控人变更',
    status: 0,
  },
  {
    key: '10001',
    keyName: '承诺失信',
    status: 0,
  },
  {
    key: '10005',
    keyName: '信用评级下调',
    status: 0,
  },
  {
    key: '10006',
    keyName: '评级展望负面',
    status: 0,
  },
  {
    key: '10007',
    keyName: '列入评级观察',
    status: 0,
  },
  {
    key: '10008',
    keyName: '推迟评级',
    status: 0,
  },
  {
    key: '10009',
    keyName: '责令改正',
    status: 0,
  },
  {
    key: '10010',
    keyName: '信披问题',
    status: 0,
  },
  {
    key: '60001',
    keyName: '产品召回',
    status: 0,
  },
  {
    key: '60002',
    keyName: '产品问题',
    status: 0,
  },
  {
    key: '60003',
    keyName: '虚假宣传',
    status: 0,
  },
  {
    key: '70001',
    keyName: '项目通报',
    status: 0,
  },
  {
    key: '70002',
    keyName: '终止项目',
    status: 0,
  },
  {
    key: '70003',
    keyName: '无证施工',
    status: 0,
  },
  {
    key: '80003',
    keyName: '市/估值下降',
    status: 0,
  },
  {
    key: '80004',
    keyName: '推迟/取消发行',
    status: 0,
  },
  // {
  //   key: '80005',
  //   keyName: '爆仓',
  //   status: 0,
  // },
  // {
  //   key: '40027',
  //   keyName: '环保信用行为排名',
  //   status: 0,
  // },
  {
    key: '12000',
    keyName: '经营相关',
    status: 0,
  },
  {
    key: '13000',
    keyName: '市场相关',
    status: 0,
  },
  {
    key: '11000',
    keyName: '管理相关',
    status: 0,
  },
  {
    key: '14000',
    keyName: '其他相关',
    status: 0,
  },
  {
    key: '40026',
    keyName: '空壳公司',
    status: 0,
  },
  {
    key: '40027',
    keyName: '黑名单',
    status: 0,
  },
  {
    key: '40031',
    keyName: '供应商风险',
    status: 0,
  },
  {
    key: '40032',
    keyName: '破产重整',
    status: 0,
  },
  {
    key: '40033',
    keyName: '破产解散',
    status: 0,
  },
  {
    key: '40037',
    keyName: '停业/清盘',
    status: 0,
  },
  {
    key: '40040',
    keyName: '行贿受贿',
    status: 0,
  },
  {
    key: '40042',
    keyName: '商业机密/数据泄露',
    status: 0,
  },
  {
    key: '40049',
    keyName: '非法用工',
    status: 0,
  },
  {
    key: '10012',
    keyName: '资不抵债',
    status: 0,
  },
  {
    key: '10016',
    keyName: '债务/票据逾期',
    status: 0,
  },
  {
    key: '80008',
    keyName: '假冒国企',
    status: 0,
  },
  {
    key: '40024',
    keyName: '司法拍卖',
    status: 0,
  },
  {
    key: '40028',
    keyName: '关联企业风险',
    status: 0,
  },
  {
    key: '40029',
    keyName: '供应链问题',
    status: 0,
  },
  {
    key: '40030',
    keyName: '市场销售不佳',
    status: 0,
  },
  {
    key: '40034',
    keyName: '市场份额下滑',
    status: 0,
  },
  {
    key: '40035',
    keyName: '产品价格下降',
    status: 0,
  },
  {
    key: '40036',
    keyName: '行业竞争加剧',
    status: 0,
  },
  {
    key: '40038',
    keyName: '市场传闻',
    status: 0,
  },
  {
    key: '40039',
    keyName: '社会责任缺失',
    status: 0,
  },
  {
    key: '40041',
    keyName: '信誉危机',
    status: 0,
  },
  {
    key: '40043',
    keyName: '资质问题',
    status: 0,
  },
  {
    key: '40044',
    keyName: '审批不通过',
    status: 0,
  },
  {
    key: '40045',
    keyName: '走私',
    status: 0,
  },
  {
    key: '40046',
    keyName: '订单减少',
    status: 0,
  },
  {
    key: '40047',
    keyName: '食品安全问题',
    status: 0,
  },
  {
    key: '40048',
    keyName: '债权人会议',
    status: 0,
  },
  {
    key: '40050',
    keyName: '违规收集用户数据',
    status: 0,
  },
  {
    key: '13001',
    keyName: '减持',
    status: 0,
  },
  {
    key: '13009',
    keyName: '停牌',
    status: 0,
  },
  {
    key: '50007',
    keyName: '纪律处分',
    status: 0,
  },
  {
    key: '50008',
    keyName: '监管叫停',
    status: 0,
  },
  {
    key: '50009',
    keyName: '行政问询',
    status: 0,
  },
  {
    key: '50010',
    keyName: '内幕交易',
    status: 0,
  },
  {
    key: '50011',
    keyName: '反洗钱',
    status: 0,
  },
  {
    key: '50012',
    keyName: '金融诈骗',
    status: 0,
  },
  {
    key: '50013',
    keyName: '中介机构连带责任',
    status: 0,
  },
  {
    key: '20007',
    keyName: '虚假报表',
    status: 0,
  },
  {
    key: '20008',
    keyName: '虚开发票',
    status: 0,
  },
  {
    key: '20009',
    keyName: '不良资产增加',
    status: 0,
  },
  {
    key: '20010',
    keyName: '流动性风险',
    status: 0,
  },
  {
    key: '20011',
    keyName: '现金流问题',
    status: 0,
  },
  {
    key: '20012',
    keyName: '虚构交易',
    status: 0,
  },
  {
    key: '20013',
    keyName: '资产减值',
    status: 0,
  },
  {
    key: '30012',
    keyName: '法定代表人变更',
    status: 0,
  },
  {
    key: '30013',
    keyName: '控制权之争',
    status: 0,
  },
  {
    key: '30014',
    keyName: '股东变动',
    status: 0,
  },
  {
    key: '10011',
    keyName: '终止评级',
    status: 0,
  },
  {
    key: '10013',
    keyName: '合同违约',
    status: 0,
  },
  {
    key: '10014',
    keyName: '撤销评级',
    status: 0,
  },
  {
    key: '10015',
    keyName: '非标风险',
    status: 0,
  },
  {
    key: '10017',
    keyName: '债务展期',
    status: 0,
  },
  {
    key: '10018',
    keyName: '行政处罚',
    status: 0,
  },
  {
    key: '60004',
    keyName: '产品研发终止',
    status: 0,
  },
  {
    key: '60005',
    keyName: '产品下架',
    status: 0,
  },
  {
    key: '60006',
    keyName: '制假售假',
    status: 0,
  },
  {
    key: '60007',
    keyName: '软件违规',
    status: 0,
  },
  {
    key: '80009',
    keyName: '自然灾害',
    status: 0,
  },
  {
    key: '80010',
    keyName: '公共卫生事件',
    status: 0,
  },
  {
    key: '80011',
    keyName: '交通中断',
    status: 0,
  },
  {
    key: '80012',
    keyName: '汇率波动',
    status: 0,
  },
  {
    key: '80013',
    keyName: '地缘政治冲突',
    status: 0,
  },
  {
    key: '80014',
    keyName: '国际贸易风险',
    status: 0,
  },
  {
    key: '80015',
    keyName: '制裁',
    status: 0,
  },
];

// eslint-disable-next-line @typescript-eslint/naming-convention
export const RoverBundleCounterTypeName = {
  [RoverBundleCounterType.DiligenceCompanyQuantity]: '风险排查额度',
  [RoverBundleCounterType.DiligenceHistoryQuantity]: '风险排查按次收费额度',
  [RoverBundleCounterType.DiligenceReportQuantity]: '报告下载额度',
  [RoverBundleCounterType.BatchInspectionQuantity]: '批量巡检额度',
  [RoverBundleCounterType.ThirdPartyQuantity]: '第三方列表额度',
  [RoverBundleCounterType.InnerBlacklistQuantity]: '内部黑名单额度',
  [RoverBundleCounterType.PersonQuantity]: '人员列表额度',
  [RoverBundleCounterType.MonitorCompanyQuantity]: '合作监控额度',
  [RoverBundleCounterType.DiligenceTenderQuantity]: '招标排查额度',
};

/**
 * 套餐额度提醒权益，仅提醒以下套餐权益
 */
// eslint-disable-next-line @typescript-eslint/naming-convention
export const RoverBundleReminderType: string[] = [
  RoverBundleCounterType.DiligenceCompanyQuantity,
  RoverBundleCounterType.MonitorCompanyQuantity,
  RoverBundleCounterType.DiligenceTenderQuantity,
];

export const levelKv = {
  [DimensionRiskLevelEnum.High]: '警示风险',
  [DimensionRiskLevelEnum.Medium]: '关注风险',
  [DimensionRiskLevelEnum.Alert]: '提示风险',
};

export const resultKv = {
  [DimensionRiskLevelEnum.High]: '高风险',
  [DimensionRiskLevelEnum.Medium]: '中风险',
  [DimensionRiskLevelEnum.Alert]: '低风险',
};

/**
 * 诉讼结果映射
 */
export const LawsuitResultNewMap = {
  '1': '支持',
  '2': '撤诉',
  '3': '诉讼中止',
  '4': '发回重审',
  '5': '达成调解',
  '6': '驳回',
  '7': '同意人身保护令',
  '8': '同意管辖权异议',
  '9': '适用普通程序',
  '10': '适用简易程序',
  '11': '撤回申请',
  '12': '不支持',
  '13': '部分支持',
  '14': '撤回上诉',
  '15': '驳回上诉',
  '16': '撤销原判',
  '17': '追加被执行人',
  '18': '财产保全',
  '19': '解除财产保全',
  '20': '对方撤诉',
  '21': '不支持',
  '22': '对方被驳回',
  '23': '对方撤回申请',
  '24': '不承担责任',
  '25': '执行完毕',
  '26': '执行中止',
  '27': '执行法院变更',
  '28': '终结本次执行',
  '29': '申请人被驳回',
  '30': '原告撤诉',
  '31': '驳回管辖权异议',
};

/**
 * 机构类型
 */
export const EnterpriseType = {
  '0': '其他',
  '001001001': '有限责任公司',
  '001001002': '股份有限公司',
  '001006': '个人独资企业',
  '001007001': '普通合伙',
  '001007002': '有限合伙',
  '001002004': '股份合作企业',
  '001009': '个体工商户',
  '001008': '农民专业合作社（联合社）',
  '001003': '机关单位',
  '001004': '事业单位',
  '001005': '社会组织',
  '001011': '律师事务所',
  '001016': '学校',
  '001015': '医疗机构',
};

/**
 * 企业性质
 */
export const EconType = {
  '0': '其他',
  '002001001': '国有企业',
  '002001001001': '央企',
  '002001001002': '央企子公司',
  '002001001003': '省管国企',
  '002001001004': '市管国企',
  '002001001005': '国有全资企业',
  '002001001006': '国有独资企业',
  '002001001007': '国有控股企业',
  '002001001008': '国有实际控制企业',
  '001002002': '集体所有制',
  '001002003': '联营企业',
  '002006': '民营企业',
  '002002': '港澳台投资企业',
  '002003': '外商投资企业',
};

/**
 * 司库企业类型映射
 */
export const TreasuryType = {
  // 中央企业（合并央企+央企子公司）
  K01: {
    code: 'K01',
    name: '中央企业',
    sourceCodes: ['002001001009', '002001001010'],
  },
  // 地方国有企业（合并多个地方国企类型）
  K02: {
    code: 'K02',
    name: '地方国有企业',
    sourceCodes: [
      '002001001011', // 省管国企
      '002001001012', // 市管国企
      '002001001013', // 国有全资企业
      '002001001014', // 国有独资企业
      '002001001015', // 国有控股企业
      '002001001016', // 国有实际控制企业
    ],
  },
  K03: {
    code: 'K03',
    name: '中央部委',
    sourceCodes: [], //数据来源于comp_busi_list_info表
  },
  // 地方政府
  K04: {
    code: 'K04',
    name: '地方政府',
    sourceCodes: ['001003'], // 机关单位
  },
  // 民营企业
  K05: {
    code: 'K05',
    name: '民营企业',
    sourceCodes: ['002006'],
  },
  // 事业单位
  K07: {
    code: 'K07',
    name: '事业单位',
    sourceCodes: ['001004'],
  },
  // 个体工商户
  K08: {
    code: 'K08',
    name: '个体工商户',
    sourceCodes: ['001009'],
  },
  // 其他类型（兜底分类）
  K06: {
    code: 'K06',
    name: '其他',
    sourceCodes: [],
  },
};

/**
 * B端和C端数据隔离 isValid枚举值0:历史 1:非历史 10:网站临时屏蔽数据 11:网信办屏蔽 12:人行屏蔽 13:客户屏蔽 14:法院屏蔽 91:网信办屏蔽历史 92:人行屏蔽历史 93:客户屏蔽历史 94:法院屏蔽历史
 */
export const IsValidNumbers = '10,11,13,91,93';

/**
 * 历史
 */
export const HistoryValidNumbers = '0' + ',' + IsValidNumbers;

/**
 * 不限
 */
export const NoLimitValidNumbers = '0,1' + ',' + IsValidNumbers;

export const NoLimitValidNumbersArr = NoLimitValidNumbers.split(',').map(Number);

export const HistoryValidNumbersArr = HistoryValidNumbers.split(',').map(Number);

/**
 * 用于维度命中描述的正则匹配规则（出现维度变更需要修改时使用）
 */
export const DescriptionRegex = /\b(\d+)\s*条记录/;
