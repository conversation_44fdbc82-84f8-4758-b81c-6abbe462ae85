//------------------ 加密数据 -------------------
import { DateRangeRelative } from '@kezhaozhao/search-utils';
import * as crypto from 'crypto';
import * as moment from 'moment';
import { RequestUtils } from '@kezhaozhao/qcc-utils';
import { PaginationParams, PaginationResponse } from '../model/common';

const algorithm = 'aes-128-cbc';
const aesAlgorithm = 'aes-128-ecb';

/**
 * @description 加密数据
 * @description params: data--要加密的数据，必须是utf8格式的字符串；callback--处理结果集的回调函数
 * @param data {string}
 */
export const encrypt = async (data: string): Promise<string> => {
  const password = crypto.randomBytes(16).toString('hex'); // password是用于生产密钥的密码
  const salt = crypto.randomBytes(16).toString('hex'); // 生成盐值
  const iv = crypto.randomBytes(8).toString('hex'); // 初始化向量

  return new Promise((resolve, reject) => {
    crypto.scrypt(password, salt, 16, function (err, derivedKey) {
      if (err) {
        reject(err);
      } else {
        const cipher = crypto.createCipheriv(algorithm, derivedKey, iv); // 创建 cipher 实例

        // 加密数据
        let cipherText = cipher.update(data, 'utf8', 'hex');
        cipherText += cipher.final('hex');
        cipherText += password + salt + iv;

        resolve(cipherText);
      }
    });
  });
};

/**
 * @description 解密通过 encrypt(); 加密的数据
 * @description param: cipherText--通过 encrypt() 加密的数据; callback--处理结果集的回调函数。
 * @param cipherText {string}
 */
export const decrypt = async (cipherText: string): Promise<string> => {
  const iv = cipherText.slice(-16); // 获取初始化向量
  const salt = cipherText.slice(-48, -16); // 获取盐值
  const password = cipherText.slice(-80, -48); // 获取密钥密码
  const data = cipherText.slice(0, -80); //获取密文

  return new Promise((resolve, reject) => {
    crypto.scrypt(password, salt, 16, function (err, derivedKey) {
      if (err) {
        reject(err);
      } else {
        const decipher = crypto.createDecipheriv(algorithm, derivedKey, iv); // 创建 decipher 实例

        // 解密数据
        let txt = decipher.update(data, 'hex', 'utf8');
        txt += decipher.final('utf8');
        resolve(txt);
      }
    });
  });
};

export const toRoundFixed = (num: number, fractionDigits = 2): string => {
  // 为了解决 142.605 保留两位四舍五入会变成 142.6的问题 ，强制加上 0.00001
  return Number((num + 0.00001).toFixed(fractionDigits)).toString();
};

/**
 * 四舍五入
 * @param num
 * @param fractionDigits
 * @returns
 */
export const toRoundFixedOld = (num: number, fractionDigits = 2): string => {
  let result = num.toString();

  const arr = num.toString().split('.');
  const integer = arr[0]; // 整数部分
  let decimal = arr[1]; // 小数部分
  if (!decimal) {
    //小数点处理
    result += '.';
    for (let i = 0; i < fractionDigits; i += 1) {
      result += '0';
    }
    return result;
  }
  const length = decimal?.length || 0;

  // 如果小于指定的位数，补上
  if (length < fractionDigits) {
    for (let i = 0; i < fractionDigits - length; i += 1) {
      result += '0';
    }
    return result;
  }

  // 如果已经符合要求位数，直接返回
  if (decimal?.length == fractionDigits) {
    return result;
  }

  // 大于指定位数四舍五入处理

  // 小数部分保留 指定位数
  decimal = decimal.substring(0, fractionDigits) + '.' + decimal.substring(fractionDigits, fractionDigits + 1);
  // 四舍五入处理
  decimal = Math.round(parseFloat(decimal)).toString();
  // 整数和小数部分拼接
  result = integer + '.' + decimal;
  return result;
};

/**
 * 科学计数法转数字
 * @param inputNumber
 */
export const transferToNumber = (inputNumber) => {
  if (isNaN(inputNumber)) {
    return inputNumber;
  }
  inputNumber = '' + inputNumber;
  inputNumber = parseFloat(inputNumber);
  if (isNaN(inputNumber)) {
    return inputNumber;
  }
  const eformat = inputNumber.toExponential(); // 转换为标准的科学计数法形式（字符串）
  const tmpArray = eformat.match(/\d(?:\.(\d*))?e([+-]\d+)/); // 分离出小数值和指数值
  return inputNumber.toFixed(Math.max(0, (tmpArray[1] || '').length - tmpArray[2]));
};

/**
 * formatMoney
 * @param inputNumber
 */
export const formatMoney = (inputNumber) => {
  if (!inputNumber) {
    return '-';
  }
  let transferNumber = transferToNumber(inputNumber);
  transferNumber = parseFloat(transferNumber);
  const res = transferNumber.toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
  if (isNaN(inputNumber)) {
    return inputNumber;
  }
  return res;
};

export const getDateRange = (num: number): DateRangeRelative[] => {
  return [
    Object.assign(new DateRangeRelative(), {
      currently: true,
      flag: 1,
      number: num * 365,
      unit: 'day',
    }),
  ];
};

/**
 *
 * @param cycle 年数，必须大于等于0
 * @return 当前时间减去cycle年数后的时间（当天的开始），毫秒级时间戳
 */
export const getStartTimeByCycle = (cycle: number): number => {
  const now = moment().startOf('day');
  if (cycle <= 0) {
    return now.toDate().getTime();
  }

  return moment(RequestUtils.ProcessDRR(getDateRange(cycle)[0]).start, 'YYYY-MM-DD')
    .toDate()
    .getTime();
};

// 封装日期格式化函数
export const formatDate = (dateValue: any, formatStr: string): string => {
  if (dateValue === 0 || dateValue === null || dateValue === undefined) {
    return '-';
  }
  const timestamp = Number(dateValue);
  if (isNaN(timestamp) || timestamp < 0) {
    return '-';
  }

  // 可处理10位和13位时间戳
  const adjustedTimestamp = timestamp > 1e12 ? timestamp : timestamp * 1000;
  return moment(adjustedTimestamp).format(formatStr);
};

export const toDateRangeRelative = (start: Date, end: Date): DateRangeRelative => {
  return Object.assign(new DateRangeRelative(), {
    currently: true,
    flag: 5,
    min: start.toISOString(),
    max: end.toISOString(),
    number: 1,
    unit: 'day',
  });
};

export const fetchAll = async <R1 extends PaginationParams, R2 extends PaginationResponse, R3>(
  searchFunction: (param1: R1, ...argArray: any[]) => Promise<R2>,
  allResults: R3[],
  condition: R1,
  ...argArray: any[]
): Promise<R3[]> => {
  const pageSize = condition.pageSize;
  let pageIndex = condition.pageIndex;
  while (true) {
    const response = await searchFunction({ ...condition, pageIndex, pageSize }, ...argArray);
    if (!response?.data || !response?.total) {
      return allResults;
    }
    allResults.push(...(response.data as R3[]));
    if (response.total <= pageIndex * pageSize) {
      break;
    }
    pageIndex += 1;
  }
  return allResults;
};

export const getAll = async <R1 extends Record<string, any>, R2 extends Record<string, any>, R3>(
  param: R1,
  searchFunction: (param1: R1) => Promise<R2>,
  dataField: string,
  allResults: R3[],
): Promise<R3[]> => {
  const pageSize = param.pageSize;
  let pageIndex = param.pageIndex;
  while (true) {
    const response = await searchFunction(param);
    if (!response || !response[dataField] || !response.Paging?.TotalRecords) {
      return allResults;
    }
    if (response[dataField] && (response[dataField] as R3[])) {
      allResults.push(...(response[dataField] as R3[]));
    }
    if (response.Paging.TotalRecords <= pageIndex * pageSize) {
      break;
    }
    pageIndex += 1;
  }
  return allResults;
};

/**
 * 生成RSA密钥对
 * @returns publicKey privateKey
 */
export const generateRSAKeyPair = () => {
  const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048, // 密钥长度
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem',
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem',
    },
  });
  return { publicKey, privateKey };
};

/**
 * 使用公钥加密信息
 * @param originalMsg
 * @param publicKey
 * @returns
 */
export const publicEncrypt = (publicKey: string, originalMsg: string) => {
  // 使用公钥加密信息
  const buffer = Buffer.from(originalMsg);
  const encrypted = crypto.publicEncrypt(publicKey, buffer);
  // console.log('Encrypted message:', encrypted.toString('base64'));
  // 返回 base64格式的加密字符串
  return encrypted.toString('base64');
};
/**
 * 使用私钥解密信息
 * @param privateKey  私钥
 * @param encryptedBase64String base64格式的加密字符串
 */
export const privateDecrypt = (privateKey: string, encryptedBase64String: string) => {
  const encryptedBuffer = Buffer.from(encryptedBase64String, 'base64');
  const decrypted = crypto.privateDecrypt(privateKey, encryptedBuffer);
  const decryptedMessage = decrypted.toString('utf8');
  // console.log('Decrypted message:', decryptedMessage);
  return decryptedMessage;
};

/**
 * 返回哈希值的二进制格式转为十六进制字符串
 * @param data
 * @returns
 */
export const getHash = (data: string) => {
  // 返回哈希值的二进制格式转为十六进制字符串
  return crypto.createHash('sha256').update(data).digest('hex');
};

export const generateRandomString = (minLength: number, maxLength: number): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;
  const randomLength = Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength;

  for (let i = 0; i < randomLength; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
};

export const monitrPushEncrypt = (originalMsg: string, secretKey: string) => {
  const cipher = crypto.createCipher(aesAlgorithm, secretKey);
  let crypted = cipher.update(originalMsg, 'utf8', 'hex');
  crypted += cipher.final('hex');
  return crypted;
};

export const monitrPushDecrypt = (encryptedString: string, secretKey: string) => {
  const decipher = crypto.createDecipher('aes-128-ecb', secretKey);
  let dec = decipher.update(encryptedString, 'hex', 'utf8');
  dec += decipher.final('utf8');
  return dec;
};

export const stringToSeed = (str: string): number => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash |= 0; // Convert to 32bit integer
  }
  return hash;
};
