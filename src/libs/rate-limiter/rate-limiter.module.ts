import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RateLimitConfigEntity } from 'libs/entities/RateLimitConfigEntity';
import { RateLimitExceededHistoryEntity } from 'libs/entities/RateLimitExceededHistoryEntity';
import { RateLimiterService } from './rate-limiter.service';

@Module({
  imports: [TypeOrmModule.forFeature([RateLimitConfigEntity, RateLimitExceededHistoryEntity])],
  providers: [RateLimiterService],
  exports: [RateLimiterService],
})
export class RateLimiterModule {}
