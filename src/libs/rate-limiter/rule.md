# 令牌桶算法实现方案

## 基本原理

令牌桶算法是一种用于控制系统资源访问速率的算法，其核心思想是系统以恒定速率生成令牌并放入令牌桶中，请求需要从桶中获取令牌才能被处理。这种算法既能限制平均处理速率，又允许一定程度的突发流量。

### 算法特点

1. **平滑性**：令牌以固定速率生成，保证长期平均速率稳定
2. **突发容忍**：通过设置桶容量，允许短时间内处理突发请求
3. **简单高效**：实现简单，运行时开销小
4. **可预测性**：系统行为可预测，便于容量规划

## 分布式实现方案

在我们的系统中，需要实现一个基于 Redis 的分布式令牌桶算法，以支持多实例部署环境下的一致性限流。

### 设计目标

1. **分布式一致性**：多个服务实例共享同一个限流状态
2. **高性能**：最小化 Redis 操作次数，降低延迟
3. **精确控制**：支持不同粒度的速率限制
4. **动态调整**：支持运行时调整限流参数
5. **可监控性**：提供限流指标的收集和监控

### 核心算法实现

#### 1. Redis 脚本实现

使用 Redis 的 Lua 脚本实现原子操作，避免分布式环境下的竞态条件：

```lua
-- KEYS[1]: 令牌桶的Redis键
-- ARGV[1]: 当前时间戳(毫秒)
-- ARGV[2]: 令牌生成速率(每秒生成的令牌数)
-- ARGV[3]: 桶容量
-- ARGV[4]: 请求需要的令牌数
-- ARGV[5]: 上次更新时间戳(毫秒)
-- 返回值: [是否获取成功(0/1), 当前令牌数, 下次可获取令牌的时间戳]

local bucket_key = KEYS[1]
local now = tonumber(ARGV[1])
local rate = tonumber(ARGV[2])
local capacity = tonumber(ARGV[3])
local requested = tonumber(ARGV[4])
local last_refresh = tonumber(ARGV[5])

-- 计算从上次更新到现在应该生成的令牌数
local elapsed_seconds = (now - last_refresh) / 1000
local new_tokens = math.min(capacity, elapsed_seconds * rate)

-- 获取当前令牌数并添加新生成的令牌
local current_tokens = tonumber(redis.call('GET', bucket_key) or "0")
current_tokens = math.min(capacity, current_tokens + new_tokens)

-- 尝试获取令牌
local success = 0
local next_available_time = 0

if current_tokens >= requested then
    -- 令牌足够，获取成功
    current_tokens = current_tokens - requested
    success = 1
else
    -- 令牌不足，计算下次可用时间
    local tokens_needed = requested - current_tokens
    next_available_time = now + (tokens_needed / rate) * 1000
end

-- 更新令牌桶状态
redis.call('SET', bucket_key, current_tokens)
redis.call('PEXPIRE', bucket_key, 3600000) -- 设置1小时过期时间

return {success, current_tokens, next_available_time}
```

#### 2. TypeScript 实现类

```typescript
export class TokenBucket {
  private readonly redisClient: Redis;
  private readonly scriptSha: string;
  private readonly options: TokenBucketOptions;

  constructor(options: TokenBucketOptions, redisClient: Redis) {
    this.options = options;
    this.redisClient = redisClient;
    // 加载脚本并获取SHA1
    this.loadScript();
  }

  private async loadScript(): Promise<void> {
    // 加载Redis Lua脚本
    this.scriptSha = await this.redisClient.script('load', TOKEN_BUCKET_SCRIPT);
  }

  /**
   * 尝试获取令牌
   * @param key 限流键标识
   * @param tokensRequired 需要的令牌数
   * @returns 是否获取成功
   */
  async tryAcquire(key: string, tokensRequired: number = 1): Promise<boolean> {
    const now = Date.now();
    const bucketKey = `token_bucket:${this.options.limiterType}:${key}`;

    // 获取上次刷新时间
    const lastRefreshKey = `${bucketKey}:last_refresh`;
    const lastRefresh = parseInt((await this.redisClient.get(lastRefreshKey)) || now.toString(), 10);

    // 执行令牌桶算法
    const result = await this.redisClient.evalsha(
      this.scriptSha,
      1,
      bucketKey,
      now,
      this.options.tokensPerSecond,
      this.options.bucketCapacity,
      tokensRequired,
      lastRefresh,
    );

    // 更新上次刷新时间
    await this.redisClient.set(lastRefreshKey, now.toString());
    await this.redisClient.expire(lastRefreshKey, 3600);

    // 记录指标
    this.recordMetrics(key, result[0] === 1, tokensRequired, result[1]);

    return result[0] === 1;
  }

  /**
   * 记录限流指标
   */
  private recordMetrics(key: string, success: boolean, requested: number, remaining: number): void {
    // 实现指标记录逻辑
    // 可以记录到Prometheus或其他监控系统
  }
}
```

### 多级时间窗口实现

为了同时支持短、中、长期的速率限制，我们可以实现多级时间窗口：

```typescript
export class MultiLevelRateLimiter {
  private readonly buckets: TokenBucket[];

  constructor(options: MultiLevelRateLimiterOptions, redisClient: Redis) {
    // 创建多个不同时间窗口的令牌桶
    this.buckets = options.levels.map(
      (level) =>
        new TokenBucket(
          {
            tokensPerSecond: level.tokensPerSecond,
            bucketCapacity: level.bucketCapacity,
            limiterType: options.limiterType,
          },
          redisClient,
        ),
    );
  }

  /**
   * 尝试获取令牌，所有级别都必须通过
   */
  async tryAcquire(key: string, tokensRequired: number = 1): Promise<boolean> {
    // 检查所有级别的限流器
    for (const bucket of this.buckets) {
      const success = await bucket.tryAcquire(key, tokensRequired);
      if (!success) {
        return false;
      }
    }
    return true;
  }
}
```

## 动态令牌消耗计算

根据任务复杂度动态计算所需令牌数：

```typescript
export function calculateRequiredTokens(jobParams: JobParameters): number {
  const { entityCount } = jobParams;

  if (entityCount <= 2000) return 1;
  if (entityCount <= 4000) return 2;
  if (entityCount <= 6000) return 3;
  if (entityCount <= 8000) return 4;
  return 5; // 最大消耗5个令牌
}
```

## 令牌借用机制

实现优先级队列之间的令牌借用：

```typescript
export class BorrowingTokenBucket {
  private readonly primaryBucket: TokenBucket;
  private readonly secondaryBuckets: TokenBucket[];
  private readonly borrowingLimits: Map<string, number>;

  constructor(options: BorrowingTokenBucketOptions, redisClient: Redis) {
    this.primaryBucket = new TokenBucket(options.primary, redisClient);
    this.secondaryBuckets = options.secondary.map((opt) => new TokenBucket(opt, redisClient));
    this.borrowingLimits = new Map(options.borrowingLimits);
  }

  async tryAcquire(key: string, tokensRequired: number = 1): Promise<boolean> {
    // 首先尝试从主令牌桶获取
    const primarySuccess = await this.primaryBucket.tryAcquire(key, tokensRequired);
    if (primarySuccess) {
      return true;
    }

    // 如果主令牌桶不足，尝试从次要令牌桶借用
    for (let i = 0; i < this.secondaryBuckets.length; i++) {
      const bucketKey = `secondary_${i}`;
      const borrowLimit = this.borrowingLimits.get(bucketKey) || 0;

      // 检查是否已达到借用上限
      const borrowedKey = `borrowed:${key}:${bucketKey}`;
      const borrowed = parseInt((await this.redisClient.get(borrowedKey)) || '0', 10);

      if (borrowed + tokensRequired <= borrowLimit) {
        const success = await this.secondaryBuckets[i].tryAcquire(key, tokensRequired);
        if (success) {
          // 更新借用计数
          await this.redisClient.incrby(borrowedKey, tokensRequired);
          await this.redisClient.expire(borrowedKey, 3600);
          return true;
        }
      }
    }

    return false;
  }
}
```

## 自适应调整机制

根据系统负载和历史数据自动调整令牌生成速率：

```typescript
export class AdaptiveTokenBucket extends TokenBucket {
  private readonly metricsWindow: number = 60 * 1000; // 1分钟
  private readonly metrics: Array<{ timestamp: number; success: boolean }> = [];

  async tryAcquire(key: string, tokensRequired: number = 1): Promise<boolean> {
    // 先调用父类方法
    const result = await super.tryAcquire(key, tokensRequired);

    // 记录结果
    this.metrics.push({ timestamp: Date.now(), success: result });

    // 清理过期指标
    this.cleanupMetrics();

    // 定期调整速率
    await this.adjustRate();

    return result;
  }

  private cleanupMetrics(): void {
    const now = Date.now();
    const cutoff = now - this.metricsWindow;
    while (this.metrics.length > 0 && this.metrics[0].timestamp < cutoff) {
      this.metrics.shift();
    }
  }

  private async adjustRate(): Promise<void> {
    // 每10秒调整一次
    if (Date.now() % 10000 > 100) return;

    // 计算拒绝率
    const total = this.metrics.length;
    if (total < 10) return; // 样本太少，不调整

    const rejected = this.metrics.filter((m) => !m.success).length;
    const rejectRate = rejected / total;

    // 根据拒绝率调整速率
    let newRate = this.options.tokensPerSecond;
    if (rejectRate > 0.2) {
      // 拒绝率过高，增加速率
      newRate = Math.min(this.options.maxTokensPerSecond, newRate * 1.1);
    } else if (rejectRate < 0.05) {
      // 拒绝率过低，可以适当降低速率
      newRate = Math.max(this.options.minTokensPerSecond, newRate * 0.95);
    }

    // 更新速率
    if (newRate !== this.options.tokensPerSecond) {
      this.options.tokensPerSecond = newRate;
      // 记录速率变化
      console.log(`Adjusted rate for ${this.options.limiterType} to ${newRate}`);
    }
  }
}
```

## 监控与告警

实现监控指标收集和告警机制：

```typescript
export class RateLimiterMetrics {
  private readonly redisClient: Redis;

  constructor(redisClient: Redis) {
    this.redisClient = redisClient;
  }

  async recordLimitEvent(options: { orgId: number; limiterType: string; success: boolean; tokensRequested: number; tokensRemaining: number }): Promise<void> {
    const { orgId, limiterType, success, tokensRequested, tokensRemaining } = options;

    // 记录限流事件
    const now = Date.now();
    const minute = Math.floor(now / 60000);

    // 按分钟记录成功/失败计数
    const key = `metrics:${limiterType}:${orgId}:${minute}`;
    const field = success ? 'success' : 'rejected';

    await this.redisClient.hincrby(key, field, 1);
    await this.redisClient.hincrby(key, 'tokens_requested', tokensRequested);

    if (!success) {
      // 记录拒绝事件，用于告警
      const rejectKey = `rejected:${limiterType}:${minute}`;
      await this.redisClient.incr(rejectKey);
      await this.redisClient.expire(rejectKey, 3600);

      // 检查是否需要告警
      const rejectCount = parseInt((await this.redisClient.get(rejectKey)) || '0', 10);
      if (rejectCount > 10) {
        // 触发告警
        this.triggerAlert(limiterType, orgId, rejectCount);
      }
    }

    // 设置过期时间
    await this.redisClient.expire(key, 86400); // 保留24小时
  }

  private triggerAlert(limiterType: string, orgId: number, rejectCount: number): void {
    // 实现告警逻辑
    console.warn(`Rate limit alert: ${limiterType} for org ${orgId} rejected ${rejectCount} requests in the last minute`);
    // 可以发送邮件、短信或调用告警API
  }
}
```

## 总结

本文档提供了一个完整的令牌桶算法实现方案，包括：

1. 基于 Redis 的分布式令牌桶实现
2. 多级时间窗口限流
3. 动态令牌消耗计算
4. 令牌借用机制
5. 自适应速率调整
6. 监控与告警

这些功能共同构成了一个强大而灵活的限流系统，可以有效控制系统资源的使用，保障系统稳定运行。
