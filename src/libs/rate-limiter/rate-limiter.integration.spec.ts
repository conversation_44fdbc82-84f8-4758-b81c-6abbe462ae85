import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from 'apps/app/app.test.module';
import { getManager } from 'typeorm';
import { generateUniqueTestIds } from '../../apps/test_utils_module/test.user';
import { RateLimitConfigEntity } from '../entities/RateLimitConfigEntity';
import { RateLimitExceededHistoryEntity } from '../entities/RateLimitExceededHistoryEntity';
import { RateLimiterTypeEnums } from './model/RateLimiterTypeEnums';
import { RateLimiterModule } from './rate-limiter.module';
import { RateLimiterService } from './rate-limiter.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import Redis from 'ioredis';

describe('RateLimiter Integration Tests', () => {
  let service: RateLimiterService;
  let moduleRef: TestingModule;
  let redisClient: Redis;
  // 生成测试组织ID基础值
  const testIds = generateUniqueTestIds('rate-limiter.integration.spec.ts');
  const baseOrgId = testIds[0];

  // 为每个测试用例生成唯一的组织ID
  const testOrgIds = {
    basic: baseOrgId,
    dynamic: baseOrgId + 100,
    default: baseOrgId + 200,
    customTokens: baseOrgId + 300,
    tokenParam: baseOrgId + 400,
    multiOrg1: baseOrgId + 500,
    multiOrg2: baseOrgId + 501,
    concurrent1: baseOrgId + 600,
    concurrent2: baseOrgId + 601,
    concurrent3: baseOrgId + 602,
    edgeCase: baseOrgId + 700,
    multiDynamic: baseOrgId + 800,
    fillRatio30: baseOrgId + 900,
    fillRatio70: baseOrgId + 901,
    fillRatio0: baseOrgId + 902,
    fillRatioCustom: baseOrgId + 903,
    cacheTest1: baseOrgId + 1000,
    cacheTest2: baseOrgId + 1001,
  };

  beforeAll(async () => {
    moduleRef = await Test.createTestingModule({
      imports: [AppTestModule, RateLimiterModule],
    }).compile();

    service = moduleRef.get<RateLimiterService>(RateLimiterService);
    const redisService = moduleRef.get<RedisService>(RedisService);
    redisClient = redisService.getClient();
  });

  afterAll(async () => {
    await moduleRef.close();
  });

  beforeEach(async () => {
    // 清理测试数据
    const entityManager = getManager();
    const allOrgIds = Object.values(testOrgIds);

    // 使用IN查询批量删除所有测试组织的数据
    if (allOrgIds.length > 0) {
      const placeholders = allOrgIds.map(() => '?').join(',');
      await entityManager.query(`DELETE FROM rate_limit_config WHERE org_id IN (${placeholders})`, allOrgIds);
      await entityManager.query(`DELETE FROM rate_limit_exceeded_history WHERE org_id IN (${placeholders})`, allOrgIds);
    }

    // 清理Redis缓存 - 更全面的清理
    for (const orgId of allOrgIds) {
      // 清理令牌桶相关的键
      const tokenBucketKeys = await redisClient.keys(`token_bucket:*:${orgId}*`);
      if (tokenBucketKeys.length > 0) {
        await redisClient.del(...tokenBucketKeys);
      }

      // 清理其他可能的限流相关键
      const rateLimitKeys = await redisClient.keys(`rate_limit:*:${orgId}:*`);
      if (rateLimitKeys.length > 0) {
        await redisClient.del(...rateLimitKeys);
      }
    }

    // 清理服务的限流器缓存
    (service as any).limiterCache.clear();
  });

  it('should create custom config and use it for rate limiting', async () => {
    // 创建自定义配置
    const entityManager = getManager();
    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.basic,
      orgName: 'Test Org',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      globalLimit: 5,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    // 获取限流器
    const limiter = await service.getLimiter(testOrgIds.basic, RateLimiterTypeEnums.Diligence_Queue_L0);

    // 执行5次请求，应该都成功
    for (let i = 0; i < 5; i++) {
      const result = await limiter.check(testOrgIds.basic, entityManager);
      expect(result).toBe(true);
    }

    // 第6次请求应该失败
    const result = await limiter.check(testOrgIds.basic, entityManager);
    expect(result).toBe(false);

    // 验证超限记录
    const records = await entityManager.find(RateLimitExceededHistoryEntity, {
      where: { orgId: testOrgIds.basic },
    });
    expect(records.length).toBe(1);
    expect(records[0].currentLimit).toBe(5);
    expect(records[0].currentUsage).toBe(1); // 第6次请求消耗1个token
  });

  it('should respect dynamic limits based on time of day', async () => {
    // 创建带有动态限制的配置
    const entityManager = getManager();
    const currentHour = new Date().getHours();
    const nextHour = (currentHour + 1) % 24;

    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.dynamic,
      orgName: 'Test Org',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L1,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      dynamicLimits: [{ hourStart: currentHour, hourEnd: nextHour, limit: 3 }],
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    // 获取限流器
    const limiter = await service.getLimiter(testOrgIds.dynamic, RateLimiterTypeEnums.Diligence_Queue_L1);

    // 执行3次请求，应该都成功
    for (let i = 0; i < 3; i++) {
      const result = await limiter.check(testOrgIds.dynamic, entityManager);
      expect(result).toBe(true);
    }

    // 第4次请求应该失败
    const result = await limiter.check(testOrgIds.dynamic, entityManager);
    expect(result).toBe(false);
  });

  it('should use default config when no custom config exists', async () => {
    // 不创建自定义配置，使用默认配置
    const entityManager = getManager();

    // 获取限流器（使用默认配置）
    const limiter = await service.getLimiter(testOrgIds.default, RateLimiterTypeEnums.Diligence_Queue_L0);
    const globalLimit = limiter.getOptions().globalLimit;
    // 默认配置的 globalLimit 是 20，实际测试显示初始令牌数是 20
    // 使用并发请求来避免令牌重新生成的影响
    const requests = Array(globalLimit + 1)
      .fill(0)
      .map(() => limiter.check(testOrgIds.default, entityManager));
    const results = await Promise.all(requests);

    // 计算成功和失败的数量
    const successCount = results.filter((r) => r === true).length;
    const failureCount = results.filter((r) => r === false).length;

    // 应该有20个成功，1个失败
    expect(successCount).toBe(globalLimit);
    expect(failureCount).toBe(1);
  });

  it('should handle custom tokens per request', async () => {
    // 创建自定义配置，设置每次请求消耗2个token
    const entityManager = getManager();
    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.customTokens,
      orgName: 'Test Org',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      globalLimit: 10,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1, // 注意：这里使用1而不是2，然后在check时显式传入2
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    // 获取限流器
    const limiter = await service.getLimiter(testOrgIds.customTokens, RateLimiterTypeEnums.Diligence_Queue_L0);

    // 执行5次请求，每次显式消耗2个token，总共消耗10个token，应该都成功
    for (let i = 0; i < 5; i++) {
      const result = await limiter.check(testOrgIds.customTokens, entityManager, 2);
      expect(result).toBe(true);
    }

    // 第6次请求应该失败，因为已经用完了10个token
    const result = await limiter.check(testOrgIds.customTokens, entityManager, 2);
    expect(result).toBe(false);
  });

  it('should handle custom tokens parameter in check method', async () => {
    // 创建自定义配置
    const entityManager = getManager();
    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.tokenParam,
      orgName: 'Test Org',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      globalLimit: 10,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    // 获取限流器
    const limiter = await service.getLimiter(testOrgIds.tokenParam, RateLimiterTypeEnums.Diligence_Queue_L0);

    // 消耗8个token
    const result1 = await limiter.check(testOrgIds.tokenParam, entityManager, 8);
    expect(result1).toBe(true);

    // 消耗2个token，应该成功
    const result2 = await limiter.check(testOrgIds.tokenParam, entityManager, 2);
    expect(result2).toBe(true);

    // 再消耗1个token，应该失败
    const result3 = await limiter.check(testOrgIds.tokenParam, entityManager, 1);
    expect(result3).toBe(false);
  });

  it('should handle multiple organizations independently', async () => {
    // 为两个不同的组织创建相同的限流配置
    const entityManager = getManager();
    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.multiOrg1,
      orgName: 'Test Org 1',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      globalLimit: 5,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.multiOrg2,
      orgName: 'Test Org 2',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      globalLimit: 5,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    // 获取两个组织的限流器
    const limiter1 = await service.getLimiter(testOrgIds.multiOrg1, RateLimiterTypeEnums.Diligence_Queue_L0);
    const limiter2 = await service.getLimiter(testOrgIds.multiOrg2, RateLimiterTypeEnums.Diligence_Queue_L0);

    // 组织1消耗5个token
    for (let i = 0; i < 5; i++) {
      const result = await limiter1.check(testOrgIds.multiOrg1, entityManager);
      expect(result).toBe(true);
    }

    // 组织1的第6次请求应该失败
    const result1 = await limiter1.check(testOrgIds.multiOrg1, entityManager);
    expect(result1).toBe(false);

    // 组织2应该不受组织1的影响，可以正常请求5次
    for (let i = 0; i < 5; i++) {
      const result = await limiter2.check(testOrgIds.multiOrg2, entityManager);
      expect(result).toBe(true);
    }

    // 组织2的第6次请求应该失败
    const result2 = await limiter2.check(testOrgIds.multiOrg2, entityManager);
    expect(result2).toBe(false);
  });

  it('should handle concurrent requests from multiple organizations correctly', async () => {
    // 为三个不同的组织创建限流配置
    const entityManager = getManager();
    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.concurrent1,
      orgName: 'Test Org 1',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      globalLimit: 5,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.concurrent2,
      orgName: 'Test Org 2',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      globalLimit: 3,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.concurrent3,
      orgName: 'Test Org 3',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      globalLimit: 4,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    // 获取三个组织的限流器
    const limiter1 = await service.getLimiter(testOrgIds.concurrent1, RateLimiterTypeEnums.Diligence_Queue_L0);
    const limiter2 = await service.getLimiter(testOrgIds.concurrent2, RateLimiterTypeEnums.Diligence_Queue_L0);
    const limiter3 = await service.getLimiter(testOrgIds.concurrent3, RateLimiterTypeEnums.Diligence_Queue_L0);

    // 并发请求测试
    const org1Requests = Array(6)
      .fill(0)
      .map(() => limiter1.check(testOrgIds.concurrent1, entityManager));
    const org2Requests = Array(4)
      .fill(0)
      .map(() => limiter2.check(testOrgIds.concurrent2, entityManager));
    const org3Requests = Array(5)
      .fill(0)
      .map(() => limiter3.check(testOrgIds.concurrent3, entityManager));

    const [org1Results, org2Results, org3Results] = await Promise.all([Promise.all(org1Requests), Promise.all(org2Requests), Promise.all(org3Requests)]);

    // 验证结果
    // 组织1: 限制5，发起6个请求，应该有5个成功，1个失败
    expect(org1Results.filter((r) => r === true).length).toBe(5);
    expect(org1Results.filter((r) => r === false).length).toBe(1);

    // 组织2: 限制3，发起4个请求，应该有3个成功，1个失败
    expect(org2Results.filter((r) => r === true).length).toBe(3);
    expect(org2Results.filter((r) => r === false).length).toBe(1);

    // 组织3: 限制4，发起5个请求，应该有4个成功，1个失败
    expect(org3Results.filter((r) => r === true).length).toBe(4);
    expect(org3Results.filter((r) => r === false).length).toBe(1);

    // 验证超限记录
    const records = await entityManager.find(RateLimitExceededHistoryEntity, {
      where: [{ orgId: testOrgIds.concurrent1 }, { orgId: testOrgIds.concurrent2 }, { orgId: testOrgIds.concurrent3 }],
    });

    expect(records.length).toBe(3); // 每个组织都有一条超限记录

    // 验证每个组织的超限记录
    const org1Record = records.find((r) => r.orgId === testOrgIds.concurrent1);
    const org2Record = records.find((r) => r.orgId === testOrgIds.concurrent2);
    const org3Record = records.find((r) => r.orgId === testOrgIds.concurrent3);

    expect(org1Record).toBeDefined();
    expect(org1Record.currentLimit).toBe(5);
    expect(org1Record.currentUsage).toBe(1); // 每次失败请求消耗1个token

    expect(org2Record).toBeDefined();
    expect(org2Record.currentLimit).toBe(3);
    expect(org2Record.currentUsage).toBe(1); // 每次失败请求消耗1个token

    expect(org3Record).toBeDefined();
    expect(org3Record.currentLimit).toBe(4);
    expect(org3Record.currentUsage).toBe(1); // 每次失败请求消耗1个token
  });

  it('should handle edge case when no dynamic limit matches current hour', async () => {
    // 创建动态限制配置，但不包含当前小时
    const entityManager = getManager();
    const currentHour = new Date().getHours();
    const nonMatchingHourStart = (currentHour + 1) % 24;
    const nonMatchingHourEnd = (currentHour + 2) % 24;

    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.edgeCase,
      orgName: 'Test Org',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L1,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      globalLimit: 5, // 当动态限制不匹配时，应该使用全局限制
      dynamicLimits: [{ hourStart: nonMatchingHourStart, hourEnd: nonMatchingHourEnd, limit: 10 }],
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    // 获取限流器
    const limiter = await service.getLimiter(testOrgIds.edgeCase, RateLimiterTypeEnums.Diligence_Queue_L1);

    // 执行5次请求，应该都成功（使用全局限制）
    for (let i = 0; i < 5; i++) {
      const result = await limiter.check(testOrgIds.edgeCase, entityManager);
      expect(result).toBe(true);
    }

    // 第6次请求应该失败
    const result = await limiter.check(testOrgIds.edgeCase, entityManager);
    expect(result).toBe(false);
  });

  it('should handle multiple dynamic limits and choose the correct one', async () => {
    // 创建带有多个动态限制的配置
    const entityManager = getManager();
    const currentHour = new Date().getHours();
    const prevHour = (currentHour - 1 + 24) % 24;
    const nextHour = (currentHour + 1) % 24;

    await entityManager.save(RateLimitConfigEntity, {
      orgId: testOrgIds.multiDynamic,
      orgName: 'Test Org',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L2,
      ttlSeconds: 60,
      defaultTokenPerRequest: 1,
      dynamicLimits: [
        { hourStart: prevHour, hourEnd: currentHour, limit: 10 }, // 不匹配当前小时
        { hourStart: currentHour, hourEnd: nextHour, limit: 3 }, // 匹配当前小时
        { hourStart: nextHour, hourEnd: (nextHour + 1) % 24, limit: 20 }, // 不匹配当前小时
      ],
      initialFillRatio: 1.0, // 初始化时填满令牌桶
      warningThreshold: 5,
    });

    // 获取限流器
    const limiter = await service.getLimiter(testOrgIds.multiDynamic, RateLimiterTypeEnums.Diligence_Queue_L2);

    // 执行3次请求，应该都成功
    for (let i = 0; i < 3; i++) {
      const result = await limiter.check(testOrgIds.multiDynamic, entityManager);
      expect(result).toBe(true);
    }

    // 第4次请求应该失败
    const result = await limiter.check(testOrgIds.multiDynamic, entityManager);
    expect(result).toBe(false);
  });

  // 新增测试：不同 initialFillRatio 的场景
  describe('Different initialFillRatio scenarios', () => {
    it('should handle initialFillRatio 0.3 (30% initial tokens)', async () => {
      const entityManager = getManager();
      await entityManager.save(RateLimitConfigEntity, {
        orgId: testOrgIds.fillRatio30,
        orgName: 'Test Org',
        limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
        globalLimit: 10,
        ttlSeconds: 60,
        defaultTokenPerRequest: 1,
        initialFillRatio: 0.3, // 初始化时只有30%的令牌 (3个)
        warningThreshold: 5,
      });

      const limiter = await service.getLimiter(testOrgIds.fillRatio30, RateLimiterTypeEnums.Diligence_Queue_L0);
      const currentTokens = await limiter.getCurrentTokens();
      // 并发请求来避免令牌重新生成
      const requests = Array(5)
        .fill(0)
        .map(() => limiter.check(testOrgIds.fillRatio30, entityManager));
      const results = await Promise.all(requests);

      const successCount = results.filter((r) => r === true).length;
      const failureCount = results.filter((r) => r === false).length;

      // 应该有3个成功（30% of 10），2个失败
      expect(successCount).toBe(3);
      expect(failureCount).toBe(2);
    });

    it('should handle initialFillRatio 0.7 (70% initial tokens)', async () => {
      const entityManager = getManager();
      await entityManager.save(RateLimitConfigEntity, {
        orgId: testOrgIds.fillRatio70,
        orgName: 'Test Org',
        limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
        globalLimit: 10,
        ttlSeconds: 60,
        defaultTokenPerRequest: 1,
        initialFillRatio: 0.7, // 初始化时有70%的令牌 (7个)
        warningThreshold: 5,
      });

      const limiter = await service.getLimiter(testOrgIds.fillRatio70, RateLimiterTypeEnums.Diligence_Queue_L0);

      // 并发请求来避免令牌重新生成
      const requests = Array(9)
        .fill(0)
        .map(() => limiter.check(testOrgIds.fillRatio70, entityManager));
      const results = await Promise.all(requests);

      const successCount = results.filter((r) => r === true).length;
      const failureCount = results.filter((r) => r === false).length;

      // 应该有7个成功（70% of 10），2个失败
      expect(successCount).toBe(7);
      expect(failureCount).toBe(2);
    });

    it('should handle initialFillRatio 0.0 (empty bucket)', async () => {
      const entityManager = getManager();
      await entityManager.save(RateLimitConfigEntity, {
        orgId: testOrgIds.fillRatio0,
        orgName: 'Test Org',
        limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
        globalLimit: 10,
        ttlSeconds: 60,
        defaultTokenPerRequest: 1,
        initialFillRatio: 0.0, // 初始化时没有令牌
        warningThreshold: 5,
      });

      const limiter = await service.getLimiter(testOrgIds.fillRatio0, RateLimiterTypeEnums.Diligence_Queue_L0);

      // 第一次请求应该失败，因为没有初始令牌
      const result = await limiter.check(testOrgIds.fillRatio0, entityManager);
      expect(result).toBe(false);

      // 验证超限记录
      const records = await entityManager.find(RateLimitExceededHistoryEntity, {
        where: { orgId: testOrgIds.fillRatio0 },
      });
      expect(records.length).toBe(1);
    });

    it('should handle different initialFillRatio with custom tokens per request', async () => {
      const entityManager = getManager();
      await entityManager.save(RateLimitConfigEntity, {
        orgId: testOrgIds.fillRatioCustom,
        orgName: 'Test Org',
        limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
        globalLimit: 20,
        ttlSeconds: 60,
        defaultTokenPerRequest: 1,
        initialFillRatio: 0.6, // 初始化时有60%的令牌 (12个)
        warningThreshold: 5,
      });

      const limiter = await service.getLimiter(testOrgIds.fillRatioCustom, RateLimiterTypeEnums.Diligence_Queue_L0);

      // 消耗5个令牌，应该成功
      const result1 = await limiter.check(testOrgIds.fillRatioCustom, entityManager, 5);
      expect(result1).toBe(true);

      // 再消耗7个令牌，应该成功（总共12个）
      const result2 = await limiter.check(testOrgIds.fillRatioCustom, entityManager, 7);
      expect(result2).toBe(true);

      // 再消耗1个令牌，应该失败（已经用完12个）
      const result3 = await limiter.check(testOrgIds.fillRatioCustom, entityManager, 1);
      expect(result3).toBe(false);
    });
  });

  // 新增测试：缓存管理功能
  describe('Cache Management', () => {
    it('should clear specific limiter cache', async () => {
      const entityManager = getManager();

      // 创建配置
      await entityManager.save(RateLimitConfigEntity, {
        orgId: testOrgIds.cacheTest1,
        orgName: 'Test Org',
        limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
        globalLimit: 10,
        ttlSeconds: 60,
        defaultTokenPerRequest: 1,
        initialFillRatio: 1.0,
        warningThreshold: 5,
      });

      // 获取限流器（会被缓存）
      await service.getLimiter(testOrgIds.cacheTest1, RateLimiterTypeEnums.Diligence_Queue_L0);
      await service.getLimiter(testOrgIds.cacheTest1, RateLimiterTypeEnums.Diligence_Queue_L1);

      // 验证缓存统计
      const statsBefore = service.getCacheStats();
      expect(statsBefore.size).toBeGreaterThanOrEqual(2);

      // 清理特定限流器缓存
      service.clearLimiterCache(testOrgIds.cacheTest1, RateLimiterTypeEnums.Diligence_Queue_L0);

      // 验证缓存统计
      const statsAfter = service.getCacheStats();
      expect(statsAfter.size).toBe(statsBefore.size - 1);
      expect(statsAfter.keys).not.toContain(`${testOrgIds.cacheTest1}:${RateLimiterTypeEnums.Diligence_Queue_L0}`);
    });

    it('should clear all limiters for an organization', async () => {
      const entityManager = getManager();

      // 创建配置
      await entityManager.save(RateLimitConfigEntity, {
        orgId: testOrgIds.cacheTest2,
        orgName: 'Test Org',
        limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
        globalLimit: 10,
        ttlSeconds: 60,
        defaultTokenPerRequest: 1,
        initialFillRatio: 1.0,
        warningThreshold: 5,
      });

      // 获取多个限流器
      await service.getLimiter(testOrgIds.cacheTest2, RateLimiterTypeEnums.Diligence_Queue_L0);
      await service.getLimiter(testOrgIds.cacheTest2, RateLimiterTypeEnums.Diligence_Queue_L1);
      await service.getLimiter(testOrgIds.cacheTest2, RateLimiterTypeEnums.Diligence_Queue_L2);

      const statsBefore = service.getCacheStats();
      const orgKeysBefore = statsBefore.keys.filter((key) => key.startsWith(`${testOrgIds.cacheTest2}:`));

      // 清理该组织的所有限流器
      service.clearLimiterCache(testOrgIds.cacheTest2);

      const statsAfter = service.getCacheStats();
      const orgKeysAfter = statsAfter.keys.filter((key) => key.startsWith(`${testOrgIds.cacheTest2}:`));

      expect(orgKeysAfter.length).toBe(0);
      expect(statsAfter.size).toBe(statsBefore.size - orgKeysBefore.length);
    });
  });
});
