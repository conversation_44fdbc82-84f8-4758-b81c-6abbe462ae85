import { RedisService } from '@kezhaozhao/nestjs-redis';
import { RateLimiter } from './model/RateLimiter';
import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { RateLimiterTypeEnums } from './model/RateLimiterTypeEnums';
import Redis from 'ioredis';
import { In, MoreThanOrEqual, Repository } from 'typeorm';
import { RateLimitConfigEntity } from 'libs/entities/RateLimitConfigEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { RateLimitExceededHistoryEntity } from 'libs/entities/RateLimitExceededHistoryEntity';

/**
 * 监控数据接口
 */
export interface RateLimiterMonitoringData {
  orgId: number;
  limiters: {
    limiterType: RateLimiterTypeEnums;
    currentTokens: number;
    limit: number;
    ttlSeconds: number;
    tokenRate: number;
    exceededCount: number;
  }[];
}

@Injectable()
export class RateLimiterService implements OnModuleDestroy {
  private readonly redisClient: Redis;
  private readonly defaultLimiterConfigs: RateLimitConfigEntity[] = [
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      defaultTokenPerRequest: 1,
      globalLimit: 100,
      dynamicLimits: [],
      ttlSeconds: 300,
      burstCapacity: 30,
      maxTokensPerRequest: 10,
      warningThreshold: 5,
      initialFillRatio: 0.5,
      description: '优先级最高的队列，一般可以用作 OpenApi 提交的异步的尽调',
    },
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L1,
      defaultTokenPerRequest: 1,
      globalLimit: 10,
      ttlSeconds: 300,
      burstCapacity: 20,
      maxTokensPerRequest: 5,
      warningThreshold: 3,
      initialFillRatio: 0.5,
      dynamicLimits: [
        {
          hourStart: 0,
          hourEnd: 8,
          limit: 150,
        },
        {
          hourStart: 8,
          hourEnd: 18,
          limit: 50,
        },
        {
          hourStart: 18,
          hourEnd: 24,
          limit: 150,
        },
      ],
      description: '优先级第二的队列，可以用作风险年检',
    },
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L2,
      defaultTokenPerRequest: 1,
      globalLimit: 25,
      ttlSeconds: 300,
      burstCapacity: 10,
      maxTokensPerRequest: 3,
      warningThreshold: 2,
      initialFillRatio: 0.5,
      dynamicLimits: [
        {
          hourStart: 0,
          hourEnd: 8,
          limit: 40,
        },
        {
          hourStart: 8,
          hourEnd: 18,
          limit: 25,
        },
        {
          hourStart: 18,
          hourEnd: 24,
          limit: 40,
        },
      ],
      description: '优先级第三，普通批量排查，主要是区分闲时和忙时设置动态的并发',
    },
  ];

  private readonly limiterCache: Map<string, RateLimiter> = new Map();

  constructor(
    private readonly redisService: RedisService,
    @InjectRepository(RateLimitConfigEntity) private readonly rateLimitConfigRepository: Repository<RateLimitConfigEntity>,
    @InjectRepository(RateLimitExceededHistoryEntity) private readonly rateLimitExceededHistoryRepository: Repository<RateLimitExceededHistoryEntity>,
  ) {
    this.redisClient = this.redisService.getClient();
  }

  onModuleDestroy() {
    // 清理缓存
    this.limiterCache.clear();
  }

  /**
   * 清理指定组织的限流器缓存
   * @param orgId 组织ID
   * @param limiterType 限流器类型（可选，不指定则清理该组织的所有限流器）
   */
  clearLimiterCache(orgId: number, limiterType?: RateLimiterTypeEnums) {
    if (limiterType) {
      const key = `${orgId}:${limiterType}`;
      this.limiterCache.delete(key);
    } else {
      // 清理该组织的所有限流器
      const keysToDelete = Array.from(this.limiterCache.keys()).filter((key) => key.startsWith(`${orgId}:`));
      keysToDelete.forEach((key) => this.limiterCache.delete(key));
    }
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存大小和键列表
   */
  getCacheStats() {
    return {
      size: this.limiterCache.size,
      keys: Array.from(this.limiterCache.keys()),
    };
  }

  async getLimitConfig(orgId: number, limiterType: RateLimiterTypeEnums): Promise<RateLimitConfigEntity> {
    const configs: RateLimitConfigEntity[] = await this.rateLimitConfigRepository.find({
      where: {
        orgId: In([orgId, -1]),
        limiterType,
      },
    });
    let config = configs.find((c) => c.orgId === orgId);
    if (!config) {
      config = configs.find((c) => c.orgId === -1);
    }
    if (!config) {
      config = this.defaultLimiterConfigs.find((c) => c.limiterType === limiterType);
    }
    return config;
  }

  async getLimiter(orgId: number, limiterType: RateLimiterTypeEnums): Promise<RateLimiter> {
    const key = `${orgId}:${limiterType}`;

    // 检查缓存
    if (this.limiterCache.has(key)) {
      return this.limiterCache.get(key);
    }

    // 创建新实例
    const config: RateLimitConfigEntity = await this.getLimitConfig(orgId, limiterType);
    const limiter = new RateLimiter(
      {
        orgId,
        limiterType,
        globalLimit: config.globalLimit,
        dynamicLimits: config.dynamicLimits,
        tokensPerRequest: config.defaultTokenPerRequest,
        ttlSeconds: config.ttlSeconds || 300,
        // burstCapacity: config.burstCapacity,
        maxTokensPerRequest: config.maxTokensPerRequest,
        warningThreshold: config.warningThreshold,
        initialFillRatio: config.initialFillRatio,
      },
      this.redisClient,
    );

    // 缓存实例
    this.limiterCache.set(key, limiter);
    return limiter;
  }

  /**
   * 获取监控数据
   * @param orgId 组织ID
   * @param limiterType 限流器类型（可选）
   * @returns 监控数据
   */
  async getMonitoringData(orgId: number, limiterType?: RateLimiterTypeEnums): Promise<RateLimiterMonitoringData> {
    const result: RateLimiterMonitoringData = {
      orgId,
      limiters: [],
    };

    // 确定要监控的限流器类型
    const limiterTypes = limiterType ? [limiterType] : Object.values(RateLimiterTypeEnums);

    for (const type of limiterTypes) {
      const limiter = await this.getLimiter(orgId, type);
      const config = await this.getLimitConfig(orgId, type);

      // 获取当前令牌数
      const { currentTokens, limit } = await limiter.getCurrentTokens();

      result.limiters.push({
        limiterType: type,
        currentTokens,
        limit,
        ttlSeconds: config.ttlSeconds,
        tokenRate: limit / config.ttlSeconds,
        exceededCount: await this.getExceededCount(orgId, type),
      });
    }

    return result;
  }

  /**
   * 获取超限次数
   * @param orgId 组织ID
   * @param limiterType 限流器类型
   * @returns 超限次数
   */
  private async getExceededCount(orgId: number, limiterType: RateLimiterTypeEnums): Promise<number> {
    // 统计最近24小时的超限记录
    const count = await this.rateLimitExceededHistoryRepository.count({
      where: {
        orgId,
        limiterType,
        createDate: MoreThanOrEqual(new Date(Date.now() - 24 * 300 * 300 * 1000)),
      },
    });

    return count;
  }
}
