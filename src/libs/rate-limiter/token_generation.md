## 令牌桶生成需求与设计方案

### 现有需求

1. 支持一个基础的量，比如每分钟 10 个。
2. 大部分时候是用来限制消息队列 consumer 的速度，现在每个消息会有 5 个 job(大部分情况都是 5 个),每个 job 大部分情况是消耗 1 个 token，但是会根据客户的三方列表排查范围来动态计算，比如你要排查范围 2000 家，那就是消耗 1 个，4000 家那就是消耗 2 个，10000 家那就是消耗 5 个，以此类推，但是大部分情况下都是 1，并且很少会超过 5。
3. 原来的想法是基于分钟基本设置，比如 10 次/分钟，但是可能会导致一个消息也消费不了，所以可能需要考虑设置周期为 5 分钟，但是又要稍微兼顾一下平均速率限制，而不是一次性消费 10\*5 这种尖刺特别高的并发。
4. 另外有一个初步想法是针对不同优先级的队列，设置不同的令牌桶，比如 L0 的优先级最高，L1 的优先级其次。并且他们可以指定时间段，不同时间段确保处理速率不太一样，可以查看 RateLimiterService.defaultLimiterConfigs 有个初始化的配置。

### 优化设计方案

#### 1. 令牌桶时间周期设计

- **平滑令牌生成机制**：将令牌生成从每分钟固定量调整为更平滑的方式

  - 设置基础单位为分钟，但在内部实现上采用令牌桶算法，按照固定速率生成令牌
  - 例如：10 个/分钟 → 每 6 秒生成 1 个令牌，避免突发消费
  - 支持配置"突发容量"，允许短时间内消费一定量的令牌，但长期维持在平均速率
  - 初始化时填充令牌桶至当前时间段限制的一半，确保服务启动后立即可以处理请求

- **基于现有 ttlSeconds 的优化**：
  - 保留现有的 ttlSeconds 参数（默认 60 秒）作为基础时间单位
  - 增加令牌生成速率计算：每秒生成 currentLimit / ttlSeconds 个令牌
  - 利用 ttlSeconds 划分时间窗口，自动处理时间段变化

#### 2. 动态令牌消耗计算

- **基于任务复杂度的令牌消耗**：
  - 支持手动指定每次请求消耗的 token 数量
  - 设置令牌消耗上限（如 5 个），防止极端情况
  - 对于超出正常消耗量的请求（如需要消耗 10 个 token），可以允许执行但发出警告，提示用户调整请求粒度

#### 3. 优先级队列与时间段差异化

- **多令牌桶设计**：

  - 为不同优先级队列（L0、L1）分别设置独立的令牌桶
  - 高优先级队列（L0）拥有更高的令牌生成速率和突发容量
  - 各个令牌桶之间独立工作，每个令牌桶针对 orgId 独立存在，不同组织间不共享令牌

- **时间段差异化**：
  - 继续使用现有的 RateLimiterDynamicLimit 实现不同时间段的限流配置
  - 工作时间（8:00-18:00）：限制较严格，确保核心业务稳定
  - 非工作时间（18:00-24:00 和 6:00-8:00）：提高令牌生成速率，加速处理积压任务
  - 深夜时段（0:00-6:00）：最高处理速率，处理大量非紧急任务

#### 4. 监控与统计

- **实时监控**：

  - 记录每个队列的令牌使用情况、拒绝率、队列积压情况
  - 提供接口获取监控数据，方便运维人员了解系统状态

- **统计报告**：
  - 记录每个组织的令牌使用情况
  - 提供按时间段、按优先级的使用统计

### 实现方案

#### 1. 扩展 RateLimiterOptions 类

```typescript
export class RateLimiterOptions {
  // 现有属性
  dynamicLimits?: RateLimiterDynamicLimit[];
  globalLimit?: number;
  tokensPerRequest?: number;
  ttlSeconds: number;
  orgId: number;
  limiterType: RateLimiterTypeEnums;

  // 新增属性
  burstCapacity?: number; // 突发容量，允许短时间内消费的最大令牌数
  maxTokensPerRequest?: number; // 单次请求最大令牌消耗量
  warningThreshold?: number; // 警告阈值，当令牌消耗超过此值时发出警告
  initialFillRatio?: number; // 初始填充比例，默认为0.5（一半容量）
}
```

#### 2. 实现真正的令牌桶算法

```typescript
export class RateLimiter {
  private readonly redisClient: Redis;
  private readonly options: RateLimiterOptions;
  private readonly logger: Logger;

  constructor(options: RateLimiterOptions, redisClient: Redis) {
    // 验证配置
    if (!options.dynamicLimits?.length && !options.globalLimit) {
      throw new Error('dynamicLimits or globalLimit is required');
    }

    this.options = options;
    this.options.tokensPerRequest = this.options.tokensPerRequest ?? 1;
    this.options.ttlSeconds = Math.min(this.options.ttlSeconds ?? 60, 60);
    this.options.initialFillRatio = this.options.initialFillRatio ?? 0.5;
    this.redisClient = redisClient;
  }

  // 获取当前时间段的限制
  private async getCurrentLimit(): Promise<number> {
    const currentHour = new Date().getHours();
    const limiter = this.options.dynamicLimits?.find((limit) => {
      return currentHour >= limit.hourStart && currentHour < limit.hourEnd;
    });

    if (!limiter && !this.options.globalLimit) {
      throw new Error(
        `RateLimiterOptions, either dynamicLimits.limit or globalLimit is required, orgId=${this.options.orgId},rateLimiterType=${this.options.limiterType}, currentHour=${currentHour}`,
      );
    }

    return limiter?.limit || this.options.globalLimit;
  }

  // 检查并消费令牌
  async check(orgId: number, entityManager: EntityManager, tokens?: number): Promise<boolean> {
    const tokensToConsume = tokens || this.options.tokensPerRequest || 1;
    const currentTime = Date.now();

    // 检查令牌消耗是否超过警告阈值
    if (tokensToConsume > (this.options.warningThreshold || 5)) {
      this.logger.warn(`高令牌消耗警告: orgId=${orgId}, type=${this.options.limiterType}, tokens=${tokensToConsume}`);
    }

    // 获取当前时间段的限制
    const limit = await this.getCurrentLimit();

    // 计算令牌生成速率（每毫秒）
    const tokenRate = limit / (this.options.ttlSeconds * 1000);

    // 获取令牌桶信息
    const bucketKey = `token_bucket:${this.options.limiterType}:${orgId}`;
    const lastUpdateKey = `${bucketKey}:last_update`;

    // 使用Lua脚本保证原子操作
    const script = `
      local bucket_key = KEYS[1]
      local last_update_key = KEYS[2]
      local current_time = tonumber(ARGV[1])
      local tokens_to_consume = tonumber(ARGV[2])
      local token_rate = tonumber(ARGV[3])
      local limit = tonumber(ARGV[4])
      local initial_fill_ratio = tonumber(ARGV[5])
      
      -- 获取当前令牌数和上次更新时间
      local current_tokens = tonumber(redis.call('get', bucket_key) or "0")
      local last_update = tonumber(redis.call('get', last_update_key) or "0")
      
      -- 如果是首次访问，初始化令牌桶
      if last_update == 0 then
        -- 初始填充一定比例的令牌
        current_tokens = math.floor(limit * initial_fill_ratio)
        last_update = current_time
        redis.call('set', last_update_key, last_update)
        redis.call('set', bucket_key, current_tokens)
      else
        -- 计算自上次更新后应该生成的令牌数
        local elapsed_time = current_time - last_update
        local tokens_to_add = math.floor(elapsed_time * token_rate)
        
        if tokens_to_add > 0 then
          -- 更新令牌数，但不超过最大限制
          current_tokens = math.min(current_tokens + tokens_to_add, limit)
          redis.call('set', bucket_key, current_tokens)
          redis.call('set', last_update_key, current_time)
        end
      end
      
      -- 检查是否有足够的令牌
      if current_tokens >= tokens_to_consume then
        -- 消费令牌
        redis.call('decrby', bucket_key, tokens_to_consume)
        return 1  -- 成功
      else
        return 0  -- 失败，令牌不足
      end
    `;

    // 执行脚本
    const result = await this.redisClient.eval(
      script,
      2,
      bucketKey,
      lastUpdateKey,
      currentTime.toString(),
      tokensToConsume.toString(),
      tokenRate.toString(),
      limit.toString(),
      this.options.initialFillRatio.toString(),
    );

    const success = result === 1;

    if (!success) {
      // 记录超限
      this.logger.warn(`orgId=${orgId}, rateLimitType=${this.options.limiterType} 超出限制，请求令牌: ${tokensToConsume}，可用令牌不足`);
      try {
        await entityManager.save(RateLimitExceededHistoryEntity, {
          orgId,
          limiterType: this.options.limiterType,
          period: Math.floor(currentTime / 1000),
          currentLimit: limit,
          currentUsage: tokensToConsume,
        });
      } catch (error) {
        this.logger.error(`orgId=${orgId}, rateLimitType=${this.options.limiterType} 记录超限记录失败，错误信息：${error}`);
        captureException(new RateLimiterException(`orgId=${orgId}, rateLimitType=${this.options.limiterType} 记录超限记录失败，错误信息：${error}`));
      }
    }

    return success;
  }

  // 获取当前令牌数
  async getCurrentTokens(orgId: number): Promise<{ currentTokens: number; limit: number }> {
    const bucketKey = `token_bucket:${this.options.limiterType}:${orgId}`;
    const lastUpdateKey = `${bucketKey}:last_update`;
    const currentTime = Date.now();

    // 获取当前时间段的限制
    const limit = await this.getCurrentLimit();

    // 计算令牌生成速率（每毫秒）
    const tokenRate = limit / (this.options.ttlSeconds * 1000);

    // 使用Lua脚本获取当前令牌数
    const script = `
      local bucket_key = KEYS[1]
      local last_update_key = KEYS[2]
      local current_time = tonumber(ARGV[1])
      local token_rate = tonumber(ARGV[2])
      local limit = tonumber(ARGV[3])
      local initial_fill_ratio = tonumber(ARGV[4])
      
      -- 获取当前令牌数和上次更新时间
      local current_tokens = tonumber(redis.call('get', bucket_key) or "0")
      local last_update = tonumber(redis.call('get', last_update_key) or "0")
      
      -- 如果是首次访问，初始化令牌桶
      if last_update == 0 then
        -- 初始填充一定比例的令牌
        current_tokens = math.floor(limit * initial_fill_ratio)
        last_update = current_time
        redis.call('set', last_update_key, last_update)
        redis.call('set', bucket_key, current_tokens)
      else
        -- 计算自上次更新后应该生成的令牌数
        local elapsed_time = current_time - last_update
        local tokens_to_add = math.floor(elapsed_time * token_rate)
        
        if tokens_to_add > 0 then
          -- 更新令牌数，但不超过最大限制
          current_tokens = math.min(current_tokens + tokens_to_add, limit)
          redis.call('set', bucket_key, current_tokens)
          redis.call('set', last_update_key, current_time)
        end
      end
      
      return current_tokens
    `;

    const currentTokens = await this.redisClient.eval(
      script,
      2,
      bucketKey,
      lastUpdateKey,
      currentTime.toString(),
      tokenRate.toString(),
      limit.toString(),
      this.options.initialFillRatio.toString(),
    );

    return {
      currentTokens: parseInt(currentTokens.toString(), 10),
      limit,
    };
  }
}
```

#### 3. 改进 RateLimiterService

```typescript
@Injectable()
export class RateLimiterService {
  private readonly redisClient: Redis;
  private readonly defaultLimiterConfigs: RateLimitConfigEntity[] = [
    // 保留现有配置并扩展
    {
      orgId: -1,
      id: -1,
      orgName: 'default',
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      defaultTokenPerRequest: 1,
      globalLimit: 20,
      dynamicLimits: [],
      ttlSeconds: 60,
      burstCapacity: 30, // 新增：突发容量
      maxTokensPerRequest: 10, // 新增：单次最大消耗
      warningThreshold: 5, // 新增：警告阈值
      initialFillRatio: 0.5, // 新增：初始填充比例
      description: '优先级最高的队列，一般可以用作 OpenApi 提交的异步的尽调',
    },
    // 其他配置保持不变...
  ];

  private readonly limiterCache: Map<string, RateLimiter> = new Map();

  constructor(
    private readonly redisService: RedisService,
    @InjectRepository(RateLimitConfigEntity) private readonly rateLimitConfigRepository: Repository<RateLimitConfigEntity>,
  ) {
    this.redisClient = this.redisService.getClient();
  }

  // 获取限流器实例
  async getLimiter(orgId: number, limiterType: RateLimiterTypeEnums): Promise<RateLimiter> {
    const key = `${orgId}:${limiterType}`;

    // 检查缓存
    if (this.limiterCache.has(key)) {
      return this.limiterCache.get(key);
    }

    // 创建新实例
    const config: RateLimitConfigEntity = await this.getLimitConfig(orgId, limiterType);
    const limiter = new RateLimiter(
      {
        ...config,
        ttlSeconds: config.ttlSeconds || 60,
      },
      this.redisClient,
    );

    // 缓存实例
    this.limiterCache.set(key, limiter);
    return limiter;
  }

  // 获取监控数据
  async getMonitoringData(orgId: number, limiterType?: RateLimiterTypeEnums): Promise<RateLimiterMonitoringData> {
    const result: RateLimiterMonitoringData = {
      orgId,
      limiters: [],
    };

    // 确定要监控的限流器类型
    const limiterTypes = limiterType ? [limiterType] : Object.values(RateLimiterTypeEnums);

    for (const type of limiterTypes) {
      const limiter = await this.getLimiter(orgId, type);
      const config = await this.getLimitConfig(orgId, type);

      // 获取当前令牌数
      const { currentTokens, limit } = await limiter.getCurrentTokens(orgId);

      result.limiters.push({
        limiterType: type,
        currentTokens,
        limit,
        ttlSeconds: config.ttlSeconds,
        tokenRate: limit / config.ttlSeconds,
        exceededCount: await this.getExceededCount(orgId, type),
      });
    }

    return result;
  }

  // 获取超限次数
  private async getExceededCount(orgId: number, limiterType: RateLimiterTypeEnums): Promise<number> {
    // 统计最近24小时的超限记录
    const count = await this.rateLimitConfigRepository.manager.count(RateLimitExceededHistoryEntity, {
      where: {
        orgId,
        limiterType,
        createDate: MoreThanOrEqual(new Date(Date.now() - 24 * 60 * 60 * 1000)),
      },
    });

    return count;
  }
}

// 监控数据接口
export interface RateLimiterMonitoringData {
  orgId: number;
  limiters: {
    limiterType: RateLimiterTypeEnums;
    currentTokens: number;
    limit: number;
    ttlSeconds: number;
    tokenRate: number;
    exceededCount: number;
  }[];
}
```

#### 4. 扩展数据库实体

```typescript
@Entity('rate_limit_config')
export class RateLimitConfigEntity {
  // 现有字段保持不变
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', name: 'org_id' })
  orgId: number;

  // 新增字段
  @Column({
    type: 'int',
    name: 'burst_capacity',
    nullable: true,
  })
  @ApiProperty({ description: '突发容量，允许短时间内消费的最大令牌数' })
  burstCapacity?: number;

  @Column({
    type: 'int',
    name: 'max_tokens_per_request',
    nullable: true,
  })
  @ApiProperty({ description: '单次请求最大令牌消耗量' })
  maxTokensPerRequest?: number;

  @Column({
    type: 'int',
    name: 'warning_threshold',
    nullable: true,
  })
  @ApiProperty({ description: '警告阈值，当令牌消耗超过此值时发出警告' })
  warningThreshold?: number;

  @Column({
    type: 'float',
    name: 'initial_fill_ratio',
    nullable: true,
    default: 0.5,
  })
  @ApiProperty({ description: '初始填充比例，默认为0.5（一半容量）' })
  initialFillRatio?: number;
}
```

### 实施计划

1. **阶段一：基础功能实现**

   - 扩展 RateLimiterOptions 和 RateLimitConfigEntity
   - 实现真正的令牌桶算法，支持平滑令牌生成
   - 添加初始令牌填充机制
   - 单元测试确保功能正常

2. **阶段二：监控功能**

   - 添加监控数据收集功能
   - 提供监控数据接口
   - 集成测试验证功能

3. **阶段三：性能优化**

   - 优化 Redis 操作，使用 Lua 脚本减少网络往返
   - 添加缓存机制减少数据库查询
   - 压力测试验证系统稳定性

### 注意事项

1. **兼容性考虑**

   - 由于系统尚未投入生产，可以进行较大改动
   - 保持 RateLimiterTypeEnums 不变，确保现有代码调用方式不变
   - 保持命名一致性，统一使用 RateLimit 或 TokenBucket 术语

2. **性能优化**

   - 使用 Redis 的 Lua 脚本进行原子操作，减少网络往返
   - 缓存 RateLimiter 实例，减少重复创建
   - 适当缓存配置信息，减少数据库查询

3. **可靠性保障**
   - 添加完善的错误处理和日志记录
   - 考虑 Redis 故障时的降级策略
   - 定期清理过期的监控数据，避免数据膨胀
