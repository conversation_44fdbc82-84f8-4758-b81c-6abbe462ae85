import Redis from 'ioredis';
import { RateLimiterOptions } from './RateLimiterOptions';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { EntityManager } from 'typeorm';
import { RateLimitExceededHistoryEntity } from 'libs/entities/RateLimitExceededHistoryEntity';
import { captureException } from '@sentry/node';
import { RateLimiterException } from './RateLimiterException';

export class RateLimiter {
  private readonly redisClient: Redis;
  private readonly options: RateLimiterOptions;
  private readonly logger: Logger = QccLogger.getLogger('RateLimiter.ts');

  constructor(options: RateLimiterOptions, redisClient: Redis) {
    if (!options.dynamicLimits?.length && !options.globalLimit) {
      throw new Error('dynamicLimits or globalLimit is required');
    }
    if (options.dynamicLimits?.length) {
      options.dynamicLimits.forEach((d) => {
        const { hourStart, hourEnd, limit } = d;
        if (!limit) {
          throw new Error('RateLimiterOptions.dynamicLimits.limit is required');
        }
        if (hourStart < 0 || hourEnd < 0 || hourEnd <= hourStart) {
          throw new Error('RateLimiterOptions.dynamicLimits.hourStart and hourEnd must not be positive and hourEnd must be greater than hourStart');
        }
        // hourStart, hourEnd 必须是0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24这种小时数，正则判断
        const hourReg = /^(0|1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|22|23|24)$/;
        if (!hourReg.test(hourStart.toString()) || !hourReg.test(hourEnd.toString())) {
          throw new Error('RateLimiterOptions.dynamicLimits.hourStart and hourEnd must be 0-24');
        }
      });
    }
    this.options = options;
    this.options.tokensPerRequest = this.options.tokensPerRequest ?? 1;
    this.options.ttlSeconds = Math.min(this.options.ttlSeconds ?? 60, 60);
    this.options.initialFillRatio = this.options.initialFillRatio ?? 0.5;
    this.options.warningThreshold = this.options.warningThreshold ?? 5;
    this.redisClient = redisClient;
  }

  /**
   * 获取当前小时的限制次数
   * 优先使用dynamicLimits，如果dynamicLimits不存在，则使用globalLimit
   * @returns
   */
  private async getLimit(): Promise<number> {
    const currentHour = new Date().getHours();
    const limiter = this.options.dynamicLimits?.find((limit) => {
      return currentHour >= limit.hourStart && currentHour < limit.hourEnd;
    });
    if (!limiter && !this.options.globalLimit) {
      throw new Error(
        `RateLimiterOptions, either dynamicLimits.limit or globalLimit is required, orgId=${this.options.orgId},rateLimiterType=${this.options.limiterType}, currentHour=${currentHour}`,
      );
    }
    return limiter?.limit || this.options.globalLimit;
  }

  /**
   * 检查并消费令牌
   * @param orgId 组织ID
   * @param entityManager 实体管理器
   * @param tokens 要消费的令牌数量
   * @returns 是否允许消费
   */
  async check(orgId: number, entityManager: EntityManager, tokens?: number): Promise<boolean> {
    const tokensToConsume = tokens || this.options.tokensPerRequest || 1;
    const currentTime = Date.now();

    // 检查令牌消耗是否超过警告阈值
    if (tokensToConsume > this.options.warningThreshold) {
      this.logger.warn(`高令牌消耗警告: orgId=${orgId}, type=${this.options.limiterType}, tokens=${tokensToConsume}`);
    }

    // 获取当前时间段的限制
    const limit = await this.getLimit();

    // 计算令牌生成速率（每毫秒）
    const tokenRate = limit / (this.options.ttlSeconds * 1000);

    // 获取令牌桶信息
    const bucketKey = `token_bucket:${this.options.limiterType}:${orgId}`;
    const lastUpdateKey = `${bucketKey}:last_update`;

    // 使用Lua脚本保证原子操作
    const script = `
      local bucket_key = KEYS[1]
      local last_update_key = KEYS[2]
      local current_time = tonumber(ARGV[1])
      local tokens_to_consume = tonumber(ARGV[2])
      local token_rate = tonumber(ARGV[3])
      local limit = tonumber(ARGV[4])
      local initial_fill_ratio = tonumber(ARGV[5])
      
      -- 获取当前令牌数和上次更新时间
      local current_tokens = tonumber(redis.call('get', bucket_key) or "0")
      local last_update = tonumber(redis.call('get', last_update_key) or "0")
      
      -- 如果是首次访问，初始化令牌桶
      if last_update == 0 then
        -- 初始填充一定比例的令牌
        current_tokens = math.floor(limit * initial_fill_ratio)
        last_update = current_time
        redis.call('set', last_update_key, last_update)
        redis.call('set', bucket_key, current_tokens)
      else
        -- 计算自上次更新后应该生成的令牌数
        local elapsed_time = current_time - last_update
        local tokens_to_add = math.floor(elapsed_time * token_rate)
        
        if tokens_to_add > 0 then
          -- 更新令牌数，但不超过最大限制
          current_tokens = math.min(current_tokens + tokens_to_add, limit)
          redis.call('set', bucket_key, current_tokens)
          redis.call('set', last_update_key, current_time)
        end
      end
      
      -- 检查是否有足够的令牌
      if current_tokens >= tokens_to_consume then
        -- 消费令牌
        redis.call('decrby', bucket_key, tokens_to_consume)
        return 1  -- 成功
      else
        return 0  -- 失败，令牌不足
      end
    `;

    // 执行脚本
    const result = await this.redisClient.eval(
      script,
      2,
      bucketKey,
      lastUpdateKey,
      currentTime.toString(),
      tokensToConsume.toString(),
      tokenRate.toString(),
      limit.toString(),
      this.options.initialFillRatio.toString(),
    );

    const success = result === 1;

    if (!success) {
      // 记录超限
      this.logger.warn(`orgId=${orgId}, rateLimitType=${this.options.limiterType} 超出限制，请求令牌: ${tokensToConsume}，可用令牌不足`);
      try {
        await entityManager.save(RateLimitExceededHistoryEntity, {
          orgId,
          limiterType: this.options.limiterType,
          period: Math.floor(currentTime / 1000),
          currentLimit: limit,
          currentUsage: tokensToConsume,
        });
      } catch (error) {
        this.logger.error(`orgId=${orgId}, rateLimitType=${this.options.limiterType} 记录超限记录失败，错误信息：${error}`);
        captureException(new RateLimiterException(`orgId=${orgId}, rateLimitType=${this.options.limiterType} 记录超限记录失败，错误信息：${error}`));
      }
    }

    return success;
  }

  /**
   * 获取当前令牌数
   * @param orgId 组织ID
   * @returns 当前令牌数和限制
   */
  async getCurrentTokens(orgId: number): Promise<{ currentTokens: number; limit: number }> {
    const bucketKey = `token_bucket:${this.options.limiterType}:${orgId}`;
    const lastUpdateKey = `${bucketKey}:last_update`;
    const currentTime = Date.now();

    // 获取当前时间段的限制
    const limit = await this.getLimit();

    // 计算令牌生成速率（每毫秒）
    const tokenRate = limit / (this.options.ttlSeconds * 1000);

    // 使用Lua脚本获取当前令牌数
    const script = `
      local bucket_key = KEYS[1]
      local last_update_key = KEYS[2]
      local current_time = tonumber(ARGV[1])
      local token_rate = tonumber(ARGV[2])
      local limit = tonumber(ARGV[3])
      local initial_fill_ratio = tonumber(ARGV[4])
      
      -- 获取当前令牌数和上次更新时间
      local current_tokens = tonumber(redis.call('get', bucket_key) or "0")
      local last_update = tonumber(redis.call('get', last_update_key) or "0")
      
      -- 如果是首次访问，初始化令牌桶
      if last_update == 0 then
        -- 初始填充一定比例的令牌
        current_tokens = math.floor(limit * initial_fill_ratio)
        last_update = current_time
        redis.call('set', last_update_key, last_update)
        redis.call('set', bucket_key, current_tokens)
      else
        -- 计算自上次更新后应该生成的令牌数
        local elapsed_time = current_time - last_update
        local tokens_to_add = math.floor(elapsed_time * token_rate)
        
        if tokens_to_add > 0 then
          -- 更新令牌数，但不超过最大限制
          current_tokens = math.min(current_tokens + tokens_to_add, limit)
          redis.call('set', bucket_key, current_tokens)
          redis.call('set', last_update_key, current_time)
        end
      end
      
      return current_tokens
    `;

    const currentTokens = await this.redisClient.eval(
      script,
      2,
      bucketKey,
      lastUpdateKey,
      currentTime.toString(),
      tokenRate.toString(),
      limit.toString(),
      this.options.initialFillRatio.toString(),
    );

    return {
      currentTokens: parseInt(currentTokens.toString(), 10),
      limit,
    };
  }
}
