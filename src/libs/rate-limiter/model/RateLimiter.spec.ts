import { QccLogger } from '@kezhaozhao/qcc-logger';
import Redis from 'ioredis';
import { EntityManager } from 'typeorm';
import { RateLimitExceededHistoryEntity } from '../../entities/RateLimitExceededHistoryEntity';
import { RateLimiter } from './RateLimiter';
import { RateLimiterOptions } from './RateLimiterOptions';
import { RateLimiterTypeEnums } from './RateLimiterTypeEnums';

describe('RateLimiter', () => {
  let rateLimiter: RateLimiter;
  let redisClient: Redis;
  let entityManager: EntityManager;
  const orgId = 123;

  beforeEach(() => {
    // Mock Redis client
    redisClient = {
      multi: jest.fn().mockReturnThis(),
      incrby: jest.fn().mockReturnThis(),
      expireat: jest.fn().mockReturnThis(),
      exec: jest.fn(),
    } as unknown as Redis;

    // Mock EntityManager
    entityManager = {
      save: jest.fn(),
    } as unknown as EntityManager;

    // Mock logger
    jest.spyOn(QccLogger, 'getLogger').mockReturnValue({
      warn: jest.fn(),
      error: jest.fn(),
    } as any);

    // Create RateLimiter instance with global limit
    const options: RateLimiterOptions = {
      orgId,
      limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
      globalLimit: 10,
      ttlSeconds: 60,
      dynamicLimits: [],
    };

    rateLimiter = new RateLimiter(options, redisClient);
  });

  describe('check', () => {
    it('should allow request when under limit', async () => {
      // Mock Redis response for under limit
      (redisClient.exec as jest.Mock).mockResolvedValueOnce([[null, '5']]);

      const result = await rateLimiter.check(orgId, entityManager);
      expect(result).toBe(true);
      expect(entityManager.save).not.toHaveBeenCalled();
    });

    it('should deny request when over limit', async () => {
      // Mock Redis response for over limit
      (redisClient.exec as jest.Mock).mockResolvedValueOnce([[null, '15']]);

      const result = await rateLimiter.check(orgId, entityManager);
      expect(result).toBe(false);
      expect(entityManager.save).toHaveBeenCalledWith(
        RateLimitExceededHistoryEntity,
        expect.objectContaining({
          orgId,
          limiterType: RateLimiterTypeEnums.Diligence_Queue_L0,
        }),
      );
    });

    it('should consume correct number of tokens', async () => {
      (redisClient.exec as jest.Mock).mockResolvedValueOnce([[null, '3']]);

      await rateLimiter.check(orgId, entityManager, 3);
      expect(redisClient.incrby).toHaveBeenCalledWith(expect.any(String), 3);
    });
  });

  describe('getLimit', () => {
    it('should use dynamic limit based on current hour', async () => {
      // Create RateLimiter with dynamic limits
      const options: RateLimiterOptions = {
        orgId,
        limiterType: RateLimiterTypeEnums.Diligence_Queue_L1,
        ttlSeconds: 60,
        dynamicLimits: [
          { hourStart: 0, hourEnd: 8, limit: 30 },
          { hourStart: 8, hourEnd: 18, limit: 10 },
          { hourStart: 18, hourEnd: 24, limit: 30 },
        ],
      };

      rateLimiter = new RateLimiter(options, redisClient);

      // Mock current hour to be 9 (in 8-18 range)
      jest.spyOn(Date.prototype, 'getHours').mockReturnValue(9);

      const limit = await (rateLimiter as any).getLimit();
      expect(limit).toBe(10);
    });

    it('should fallback to global limit when no dynamic limit matches', async () => {
      const options: RateLimiterOptions = {
        orgId,
        limiterType: RateLimiterTypeEnums.Diligence_Queue_L2,
        globalLimit: 20,
        ttlSeconds: 60,
        dynamicLimits: [
          { hourStart: 0, hourEnd: 5, limit: 30 },
          { hourStart: 20, hourEnd: 24, limit: 30 },
        ],
      };

      rateLimiter = new RateLimiter(options, redisClient);

      // Mock current hour to be 10 (not in any range)
      jest.spyOn(Date.prototype, 'getHours').mockReturnValue(10);

      const limit = await (rateLimiter as any).getLimit();
      expect(limit).toBe(20);
    });
  });
});
