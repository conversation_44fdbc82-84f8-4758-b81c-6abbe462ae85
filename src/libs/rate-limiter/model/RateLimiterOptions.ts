import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { RateLimiterTypeEnums } from './RateLimiterTypeEnums';

export class RateLimiterDynamicLimit {
  @ApiProperty({ description: '开始时间' })
  @Type(() => Number)
  hourStart: number;

  @ApiProperty({ description: '结束时间' })
  @Type(() => Number)
  hourEnd: number;

  @ApiProperty({ description: '限制次数' })
  @Type(() => Number)
  limit: number;
}

export class RateLimiterOptions {
  @ApiPropertyOptional({ description: '动态限制' })
  dynamicLimits?: RateLimiterDynamicLimit[];

  @ApiPropertyOptional({ description: '全局限制, 跟dynamicLimits 二选一，优先dynamicLimits' })
  @Type(() => Number)
  globalLimit?: number;

  @ApiPropertyOptional({ description: '每次请求消耗的token数（默认1）' })
  @Type(() => Number)
  tokensPerRequest?: number;

  @ApiPropertyOptional({ description: '限制时间(单位是秒), 默认是60s' })
  @Type(() => Number)
  ttlSeconds: number;

  @ApiProperty({ description: '组织ID' })
  @Type(() => Number)
  orgId: number;

  @ApiProperty({ description: '限流器类型' })
  limiterType: RateLimiterTypeEnums;

  // @ApiPropertyOptional({ description: '突发容量，允许短时间内消费的最大令牌数' })
  // @Type(() => Number)
  // burstCapacity?: number;

  @ApiPropertyOptional({ description: '单次请求最大令牌消耗量' })
  @Type(() => Number)
  maxTokensPerRequest?: number;

  @ApiPropertyOptional({ description: '警告阈值，当单次令牌消耗超过此值时发出警告' })
  @Type(() => Number)
  warningThreshold?: number;

  @ApiPropertyOptional({ description: '初始填充比例，默认为0.5（一半容量）' })
  @Type(() => Number)
  initialFillRatio?: number;
}
