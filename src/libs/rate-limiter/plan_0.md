## 第三方风险针对尽调的改造计划

### 阶段 1 优化查询

1. 针对可能存在的 org - company 节点，采用 hash(companyId) 取余的方式动态分组，把 org 超级节点分为若干个批次的查询，然后再合并结果
2. OpenAPI 的方式的尽调接口采用异步方式，请求直接发送到队列后，排队执行
3. 完善 OpenAPI 中的批量排查接口，优先使用 异步批次，用户可以分多次上传公司，然后手动启动
4. 使用令牌桶方式对尽调接口按照组织级别限流
   1. 页面端尽调不限流，保持同步响应
   2. 风险年检优先级是 L0，按照组织级别并发相对最高，例如每分钟 30 个
   3. OpenAPI 提交的尽调请求，优先级 L1，按照组织级别限流，例如每分钟 20 个，这个还要考虑限定频次，尽可能识别用户如果是批量的，就应该使用批量排查。
   4. 普通批量排查，优先级 L2，按照组织级别限流，例如每分钟 10 个
   5. L1 和 L2 队列考虑按时间段限制并发，例如 L1 工作时间段偏高或者全天一样，L2 非工作时间段偏高，工作时间偏低
   6. 根据三方列表量级设置消耗的 token 数量，例如默认针对 2000 个三方列表尽调一次消耗 1 个 token，如果选定的三方列表范围是 1 万，则一次消耗 5 个 token。
      整体来讲就是不限制用户的使用，但是控制用户对资源的占用。同时也能更好的评估后续资源的分配以及已经资源是否需要提升等。
5. nebula 考虑更换节点类型（需评估）

### 阶段 2 功能分层

1. 关系排查在普通尽调里面默认 3 层
   1. 默认只找最短路径
2. 抽出来 深度排查 功能，按次收费
   1. 在普通尽调页面引导有需求的用户使用深度排查
   2. 深度排查默认缓存结果 2 周，2 周后自动更新并额外收费。
      1. 2 周内我们可以自动更新原有的关系因为节点发生变化而导致的关系消失
      2. 对于原来没有的关系，只能等周期性重刷数据时候来更新
   3. 用户可以选择手动更新缓存，按次收费
