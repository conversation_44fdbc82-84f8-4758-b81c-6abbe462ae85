import { ApiProperty } from '@nestjs/swagger';
import { RateLimiterDynamicLimit } from 'libs/rate-limiter/model/RateLimiterOptions';
import { RateLimiterTypeEnums } from 'libs/rate-limiter/model/RateLimiterTypeEnums';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('rate_limit_config')
export class RateLimitConfigEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'int',
    name: 'org_id',
  })
  orgId: number;

  @Column({
    type: 'varchar',
    name: 'org_name',
  })
  orgName: string;

  @Column({
    type: 'varchar',
    name: 'limiter_type',
  })
  limiterType: RateLimiterTypeEnums;

  @Column({
    type: 'int',
    name: 'ttl_seconds',
    default: () => 60,
  })
  ttlSeconds: number;

  @Column({
    type: 'int',
    name: 'default_token_per_request',
    default: () => 1,
  })
  defaultTokenPerRequest: number;

  @Column({
    type: 'int',
    name: 'global_limit',
    nullable: true,
    default: 10,
  })
  @ApiProperty({ description: '全局限制,如果有配置 dynamicLimits ，优先使用' })
  globalLimit?: number;

  @Column({
    type: 'json',
    name: 'dynamic_limits',
    nullable: true,
  })
  @ApiProperty({ description: '基于时间段的limit配置' })
  dynamicLimits?: RateLimiterDynamicLimit[];

  @Column({
    type: 'int',
    name: 'burst_capacity',
    nullable: true,
  })
  @ApiProperty({ description: '突发容量，允许短时间内消费的最大令牌数' })
  burstCapacity?: number;

  @Column({
    type: 'int',
    name: 'max_tokens_per_request',
    nullable: true,
  })
  @ApiProperty({ description: '单次请求最大令牌消耗量' })
  maxTokensPerRequest?: number;

  @Column({
    type: 'int',
    name: 'warning_threshold',
    nullable: true,
  })
  @ApiProperty({ description: '警告阈值，当单次令牌消耗超过此值时发出警告' })
  warningThreshold?: number;

  @Column({
    type: 'float',
    name: 'initial_fill_ratio',
    nullable: true,
    default: 0.5,
  })
  @ApiProperty({ description: '初始填充比例，默认为0.5（一半容量）' })
  initialFillRatio?: number;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate?: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate?: Date | null;

  @Column({
    type: 'varchar',
    name: 'description',
    nullable: true,
  })
  description?: string;
}
