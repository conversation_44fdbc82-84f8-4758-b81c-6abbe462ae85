import { ApiProperty } from '@nestjs/swagger';
import { RateLimiterTypeEnums } from 'libs/rate-limiter/model/RateLimiterTypeEnums';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('rate_limit_exceeded_history')
export class RateLimitExceededHistoryEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'int',
    name: 'org_id',
  })
  orgId: number;

  @Column({
    type: 'varchar',
    name: 'limiter_type',
  })
  limiterType: RateLimiterTypeEnums;

  @Column({
    type: 'int',
    name: 'current_limit',
  })
  currentLimit: number;

  @Column({
    type: 'int',
    name: 'period',
  })
  @ApiProperty({ description: '对应 分钟数/(RateLimiterOptions.ttlSeconds * 1000)' })
  period: number;

  @Column({
    type: 'int',
    name: 'current_usage',
  })
  currentUsage: number;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date | null;
}
