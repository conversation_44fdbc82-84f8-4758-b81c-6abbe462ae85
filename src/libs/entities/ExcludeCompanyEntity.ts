import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, MaxLength } from 'class-validator';

/**
 * 第三方排查关联主体默认排查公司名单实体
 */
@Entity('exclude_company')
export class ExcludeCompanyEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  @ApiProperty({ description: '主键ID' })
  id: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'company_id',
  })
  @ApiProperty({ description: '公司唯一标识' })
  @MaxLength(45)
  @IsNotEmpty()
  companyId: string;

  @Column('varchar', {
    nullable: false,
    length: 500,
    name: 'company_name',
  })
  @ApiProperty({ description: '公司名称' })
  @MaxLength(500)
  @IsNotEmpty()
  companyName: string;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ description: '创建时间' })
  createDate: Date;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  @ApiProperty({ description: '更新时间' })
  updateDate: Date;
}
