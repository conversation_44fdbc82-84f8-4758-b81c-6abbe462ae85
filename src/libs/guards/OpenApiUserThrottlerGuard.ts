import { ExecutionContext, Injectable } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';

@Injectable()
export class OpenApiUserThrottlerGuard extends ThrottlerGuard {
  // getTracker 如果需要对用户或者 IP 进行限流，可以重写此方法实现
  // - 作用 : 获取用于限流的唯一标识符（例如 IP 地址或用户 ID）。默认实现通常使用请求的 IP 地址。
  // - 适用场景 : 如果需要根据自定义规则生成限流标识符（例如，基于请求头或特定参数），可以重写此方法
  // protected getTracker(req: Record<string, any>): string {
  //   const user = (req as any).user;
  //   return user?.id || req.ip;
  // }

  // - 作用 : 从上下文中提取请求和响应对象。
  // - 重写场景 : 如果需要从非 HTTP 上下文中提取请求和响应（例如，WebSocket 或 RPC），可以重写此方法。
  // protected getRequestResponse(context: ExecutionContext) {
  //   const ctx = context.switchToHttp();
  //   return { req: ctx.getRequest(), res: ctx.getResponse() };
  // }

  // generateKey 生成用于存储限流数据的 Redis 键
  // - 作用 : 生成用于存储限流数据的 Redis 键。默认实现会结合 getTracker 的结果和一个后缀（通常是类名和方法名）来生成键。
  // - 适用场景 : 如果需要自定义 Redis 键的生成规则（例如，基于请求路径、方法或其他参数），可以重写此方法。
  protected generateKey(context: ExecutionContext): string {
    // 使用接口路径和方法作为限流key
    const request = context.switchToHttp().getRequest();
    const path = request.route.path;
    const method = request.method;
    return `openapi:guard:${method}-${path}`;
  }
}
