import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from 'libs/config/config.service';
import { ResourceEntity } from 'libs/entities/ResourceEntity';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RoverExceptions } from '../exceptions/exceptionConstants';
import { intersection } from 'lodash';
import { Product } from '@kezhaozhao/saas-bundle-service';
import { Cacheable } from '@type-cacheable/core';
import { UserService } from '../../apps/user/user.service';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import pathToRegexp = require('path-to-regexp');

@Injectable()
export class RoverRolesGuard implements CanActivate {
  private readonly logger: Logger = QccLogger.getLogger(RoverRolesGuard.name);

  constructor(
    protected readonly httpService: HttpService,
    protected readonly configService: ConfigService,
    protected readonly userService: UserService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * TODO 需要填充 req.user 中 roleScope， permissions， departments 等字段
   * @param context
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const { currentOrg: orgId, userId } = request.user;
    if (!user.permissions) {
      const permissions = await this.userService.fetchUserPermissionsV2(orgId, userId);
      Object.assign(request.user, {
        permissions: permissions.map((x) => x.permissionId),
        permissionScopes: permissions,
      });
    }

    if (user.permissions && user.permissions.length > 0) {
      const matchResourceId = await this.getMatchResource(request.method, request._parsedUrl.pathname);
      if (!matchResourceId) {
        this.logger.debug(`match none resource: userId=${userId} ${request.method} ${request._parsedUrl.pathname}`);
        return true;
      } else {
        const resourcePermissions = await this.getResourcePermissions(matchResourceId);
        if (resourcePermissions?.length > 0) {
          const match = intersection(resourcePermissions, user.permissions);
          if (match.length > 0) {
            const currentPermissionScope = Math.max(...user.permissionScopes.filter((x) => match.includes(x.permissionId)).map((x) => x.scope));
            Object.assign(request.user, {
              currentPermissionScope, // 当前请求接口的scope
            });
            return true;
          }
          this.logger.error(
            `userId=${userId} 没有权限访问resource=${request._parsedUrl.pathname}, matchResourceId=${matchResourceId}, userPermissions=${user.permissions?.join(
              ',',
            )}`,
          );
        } else {
          this.logger.warn(`resource=${matchResourceId} related none permission`);
          return true;
        }
      }
    } else {
      this.logger.error(`userId=${userId} 没有权限访问resource=${request._parsedUrl.pathname}, 用户没有任何权限`);
    }

    throw new ForbiddenException(RoverExceptions.UserRelated.User.RoleForbidden);
  }

  /**
   *
   * @param method
   * @private
   */
  private async getResources(operation: string) {
    const cacheKey = `RolesGuard:getResources:${operation}`;
    const redis = this.redisService.getClient();
    const resourcesByOperation = await redis.get(cacheKey);
    if (resourcesByOperation) {
      return JSON.parse(resourcesByOperation);
    }
    const resources: ResourceEntity[] = await this.httpService.axiosRef
      .post(`${this.configService.kzzServer.enterpriseService}/internal/auth/resources`, {
        operation,
        product: Product.Rover,
      })
      .then((resp) => resp.data);
    await redis.set(cacheKey, JSON.stringify(resources), 'EX', 600);
    return resources;
  }

  /**
   *
   * @param resourceId
   * @private
   */
  private async getResourcePermissions(resourceId: number) {
    const cacheKey = `RolesGuard:getResourcePermissions:${resourceId}`;
    const redis = this.redisService.getClient();
    const permissionIdsByCache = await redis.get(cacheKey);
    if (permissionIdsByCache) {
      return JSON.parse(permissionIdsByCache);
    }
    let permissionIds: number[] = await this.httpService.axiosRef
      .post(`${this.configService.kzzServer.enterpriseService}/internal/auth/resource/${resourceId}/permissions`, {})
      .then((resp) => resp.data);
    if (!permissionIds?.length) {
      permissionIds = [];
    }
    redis.set(cacheKey, JSON.stringify(permissionIds), 'EX', 600);
    return permissionIds;
  }

  @Cacheable({ ttlSeconds: 60, cacheKey: (args) => `RolesGuard:getMatchResource:${args[0]}:${args[1]}` })
  private async getMatchResource(method: string, path: string): Promise<number> {
    const resources = await this.getResources(method);
    let matchResourceId = 0;
    if (resources && resources.length > 0) {
      for (const resource of resources) {
        const regexp = pathToRegexp(resource.uri);
        if (regexp.exec(path)) {
          matchResourceId = resource.id;
          this.logger.debug(`match resource id: ${matchResourceId}`);
          break;
        }
      }
    }
    return matchResourceId;
  }
}
