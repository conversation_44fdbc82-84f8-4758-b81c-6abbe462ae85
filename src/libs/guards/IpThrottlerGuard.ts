import { ExecutionContext, Injectable } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';

@Injectable()
export class UserThrottlerGuard extends ThrottlerGuard {
  // getTracker 如果需要对用户或者 IP 进行限流，可以重写此方法实现
  // - 作用 : 获取用于限流的唯一标识符（例如 IP 地址或用户 ID）。默认实现通常使用请求的 IP 地址。
  // - 适用场景 : 如果需要根据自定义规则生成限流标识符（例如，基于请求头或特定参数），可以重写此方法
  // protected getTracker(req: Record<string, any>): string {
  //   return req.ip;
  // }

  // 这里是直接按ip调用频次限流
  protected generateKey(context: ExecutionContext): string {
    const request = context.switchToHttp().getRequest();
    const path = request.route.path;
    const method = request.method;
    // 获取用户ID作为限流key
    const ip = request.ip;
    return `ip:guard:${ip}-${path}-${method}`;
  }
}
