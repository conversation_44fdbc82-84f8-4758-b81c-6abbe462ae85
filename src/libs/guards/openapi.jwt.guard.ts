import { AuthGuard } from '@nestjs/passport';
import { ExecutionContext, ForbiddenException, HttpException, Injectable, InternalServerErrorException, UnauthorizedException } from '@nestjs/common';
import { LogUtils, QccLogger } from '@kezhaozhao/qcc-logger';
import { JwtService } from '@nestjs/jwt';
import { Logger } from 'log4js';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from 'libs/config/config.service';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import * as moment from 'moment';
import { Cacheable } from 'type-cacheable';
import { Product, ProductAccessDeniedException } from '@kezhaozhao/saas-bundle-service';
import { UserEntity } from 'libs/entities/UserEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OpenApiResourceEntity } from '../entities/OpenApiResourceEntity';
import { UserService } from '../../apps/user/user.service';

@Injectable()
export class OpenApiJwtGuard extends AuthGuard('jwt') {
  private readonly logger: Logger = QccLogger.getLogger(OpenApiJwtGuard.name);

  constructor(
    private readonly jwtService: JwtService,
    protected readonly httpService: HttpService,
    protected readonly configService: ConfigService,
    protected readonly userService: UserService,
    @InjectRepository(OpenApiResourceEntity) private readonly openApiResourceRepo: Repository<OpenApiResourceEntity>,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    // const sss = await super.canActivate(context);
    // console.log(sss);

    // request.user = { currentOrg: 208, userId: 5171 };
    // return true;

    try {
      const token = request.headers.authorization;
      let user = await this.jwtService.verifyAsync<UserEntity>(token, this.configService.jwt);

      // {
      //   userId: 217,
      //   email: '<EMAIL>',
      //   phone: '18626272086',
      //   createDate: 2020-06-18T01:48:39.000Z,
      //   updateDate: 2022-11-16T07:11:08.000Z,
      //   name: 'B端用户2',
      //   active: 1,
      //   loginUserId: 118,
      //   orgId: 208,
      //   guid: 'd4f6d944481deb7c5a584b459e48fddb',
      //   lastLogin: 2022-10-21T06:14:04.000Z,
      //   bUserId: '07285c44362c4aa68dc0e80fa325af09',
      //   strId: '07285c44362c4aa68dc0e80fa325af09',
      //   faceimg: 'https://co-image.qichacha.com/upload/temp/userface1626677799.jpeg',
      //   position: 1,
      //   staffId: '416'
      // }

      // 构造 currentUser 获取用户套餐
      let bundleError;
      if (!user.orgId) {
        // 未创建组织
        bundleError = RoverExceptions.UserRelated.Auth.OrgNotCreated;
      } else {
        const { orgId, userId } = user;
        const roverUser = await this.userService.getRoverUser(userId, orgId);
        const bundle = roverUser.bundle;
        const resource = await this.getOpenApiResource(request._parsedUrl.pathname, request.method);
        if (!bundle?.mainBundleId) {
          // 未开通套餐
          bundleError = RoverExceptions.UserRelated.User.ApplicationForbidden;
        } else if (moment().isAfter(moment(bundle.endDate).endOf('day'))) {
          // 有套餐，套餐已过期
          bundleError = RoverExceptions.UserRelated.User.ApplicationExpired;
        } else if (resource && !resource.orgResources?.some((x) => x.orgId == orgId)) {
          bundleError = RoverExceptions.UserRelated.Auth.ForbiddenAccess;
        } else {
          user = roverUser;
          Object.assign(request, { user });
        }

        const ip = LogUtils.ip(request);
        const userAgent = request.headers['user-agent'];
        // 记录活跃日志
        this.httpService.axiosRef
          .post(`${this.configService.kzzServer.enterpriseService}/internal/integration/user/loginTime`, {
            userId,
            orgId,
            ip,
            userAgent,
            serviceCode: Product.Rover,
          })
          .catch((err) => this.logger.error(err));
      }
      if (bundleError) {
        throw new ForbiddenException(bundleError);
      }

      return true;
    } catch (err) {
      if (err instanceof HttpException) {
        if (err instanceof ProductAccessDeniedException) {
          if (request?.user?.userId) {
            //获取到userId，说明用户已经存在，可能是没有被赋予产品的使用权限
            throw new ForbiddenException(RoverExceptions.UserRelated.User.ApplicationForbidden);
          } else {
            //用户不在某个组织中
            throw new ForbiddenException(RoverExceptions.UserRelated.User.OrphanUser);
          }
        }
        throw err;
      } else {
        let message = err.message;
        if (message == 'jwt expired') {
          throw new UnauthorizedException(RoverExceptions.UserRelated.Auth.JWTExpired);
        }
        if (message.includes('jwt') || message.includes('token')) {
          throw new UnauthorizedException(RoverExceptions.UserRelated.Auth.InvalidJWT);
        }
        if (err?.response?.data) {
          message = JSON.stringify(err.response.data);
        }
        this.logger.error('RoverSessionGuard error ', err);
        throw new InternalServerErrorException({
          ...RoverExceptions.UserRelated.User.GetUserOrgInfoFailed,
          internalMessage: message,
        });
      }
    }
  }

  @Cacheable({ ttlSeconds: 60 })
  private async getOpenApiResource(uri: string, method: string): Promise<OpenApiResourceEntity> {
    return this.openApiResourceRepo.findOne({ uri, operation: method });
  }
}
