import { ExecutionContext, Injectable } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';

@Injectable()
export class UserThrottlerGuard extends ThrottlerGuard {
  // 这里是直接按用户调用接口频次限流
  protected generateKey(context: ExecutionContext): string {
    const request = context.switchToHttp().getRequest();
    const path = request.route.path;
    const method = request.method;
    // 获取用户ID作为限流key
    const userId = request.user?.userId || request.ip;
    return `userId:guard:${userId}-${path}-${method}`;
  }
}
