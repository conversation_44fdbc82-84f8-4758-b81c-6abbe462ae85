/* eslint-disable */
const mysql = require('mysql2/promise');

// const env = 'prod'
// const ROVER_DB_HOST =  'ld-kzz-6pa0mz4s6dy47.mysql.rds.aliyuncs.com';
// const ROVER_DB_PORT =  3306;
// const ROVER_DB_USER =  'user_kzz_rover';
// const ROVER_DB_PASSWD =  'FSHdgeLfCGTaFue8QZwzUIB3';
// const ROVER_DATABASE =  'rover';

const env = 'test';
// const ROVER_DB_HOST = '**************';
// const ROVER_DB_PORT = 3306;
// const ROVER_DB_USER = 'kezhaozhao_dev';
// const ROVER_DB_PASSWD = 'kezhaozhao_dev';
// const ROVER_DATABASE = 'rover_test';

const DB_HOST = 'risk-management-all.rwlb.rds.aliyuncs.com';
const DB_PORT = 3306;
const DB_USER = 'user_kzz_risk_w';
const DB_PASSWD = 'jil_6V1IyhNbYhtlamGUOH2TE';
const DATABASE = 'risk_management';

(async function () {
  console.log('== start ==');
  const riskDBConn = await mysql.createConnection({
    host: DB_HOST,
    port: DB_PORT,
    user: DB_USER,
    password: DB_PASSWD,
    database: DATABASE,
  });

  // const roverDBConn = await mysql.createConnection({
  //   host: ROVER_DB_HOST,
  //   port: ROVER_DB_PORT,
  //   user: ROVER_DB_USER,
  //   password: ROVER_DB_PASSWD,
  //   database: ROVER_DATABASE,
  // });

  // const [companyIds] = await roverDBConn.query('SELECT DISTINCT company_id FROM monitor_group_company');

  const [rows2] = await riskDBConn.query('SELECT * FROM `kys_moniter_company_list` LIMIT 10;');
  console.log(rows2);

  console.log('== done ==');
  await riskDBConn.end();
})().catch((err) => {
  console.log(err);
});
