docker rm -f test-mysql-rover || true
initFile=$PWD/init_sql
echo $initFile
docker run -d \
  --name test-mysql-rover \
  -p 3307:3306 \
  -e MYSQL_ROOT_PASSWORD=kezhaozhao_dev \
  -e MYSQL_USER=kezhaozhao_dev \
  -e MYSQL_PASSWORD=kezhaozhao_dev \
  -e MYSQL_DATABASE=rover_test \
  -e LANG=C.UTF-8 \
  -e MYSQL_SQL_MODE="" \
  -v $initFile:/docker-entrypoint-initdb.d \
  mysql:8.0 \
  --bind-address=0.0.0.0 \
  --default-authentication-plugin=mysql_native_password \
  --character-set-server=utf8mb4 \
  --collation-server=utf8mb4_unicode_ci



  # 启动redis
 docker rm -f test-redis-rover || true
 docker run -d --name test-redis-rover -p 6380:6379 --rm redis:7.2  --requirepass "yourpassword" --appendonly no


 # 启动本地es
 sh es-dynamic.sh