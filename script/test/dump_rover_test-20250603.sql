-- MySQL dump 10.13  Distrib 8.0.41, for macos13.7 (x86_64)
--
-- Host: **************    Database: rover_test
-- ------------------------------------------------------
-- Server version	8.0.22

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '1230c1d4-3e5c-11ef-ac90-06777fb6dc0c:1-28189806,
1a74d69e-1c6e-11ee-ae8c-5a54ac473028:1-193579725,
5e490d95-f7e7-11ef-9de7-fa163e82ac61:1-12542482,
d8bccb1c-22fa-11ef-8f3a-76169e623a0f:1-2399749,
e91b7a07-6431-11ea-a5fe-fa163e68fe5a:1-34049587';

--
-- Current Database: `rover_test`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `rover_test` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `rover_test`;

--
-- Table structure for table `batch`
--

DROP TABLE IF EXISTS `batch`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch` (
  `batch_id` int NOT NULL AUTO_INCREMENT,
  `file_name` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `create_by` int NOT NULL,
  `status` int DEFAULT '0' COMMENT '0 待处理 1 处理中 2 处理成功 3处理失败',
  `comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述信息，可是是处理失败的时候的错误原因等',
  `batch_type` int NOT NULL DEFAULT '0' COMMENT '0 导入\n1 导出',
  `result_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批量（导入或者导出）结果对应的文件',
  `business_type` int DEFAULT '0' COMMENT '0 尽职调查\\n1 客商',
  `batch_info` json DEFAULT NULL COMMENT '批量排查任务：记录使用的排查模型； 年检任务： 记录年检设置规则；',
  `start_date` datetime DEFAULT NULL,
  `record_count` int NOT NULL COMMENT '该批次中包含的记录的count',
  `end_date` datetime DEFAULT NULL,
  `statistics_info` json NOT NULL,
  `origin_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批量导入的原始文件',
  `detail_file` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '排查详情对应的文件地址',
  `preview_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预览地址',
  `can_retry` int DEFAULT '0' COMMENT '0-不可重试；1-可以重试',
  `paid_count` int DEFAULT '0',
  `error_count` int DEFAULT '0',
  `success_count` int DEFAULT '0',
  `updated_count` int DEFAULT '0',
  `duplicated_count` int DEFAULT '0',
  `withholding_count` int DEFAULT '0',
  `withholding_record_count` int DEFAULT '0',
  PRIMARY KEY (`batch_id`),
  KEY `batch_status` (`batch_id`,`status`),
  KEY `org_batch` (`org_id`,`dep_id`,`batch_type`,`business_type`)
) ENGINE=InnoDB AUTO_INCREMENT=57099 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_diligence`
--

DROP TABLE IF EXISTS `batch_diligence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_diligence` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `job_id` int NOT NULL,
  `changing_version` int DEFAULT NULL,
  `changing_detail` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `batch_id` (`batch_id`,`diligence_id`),
  KEY `job_id` (`job_id`),
  KEY `diligence_id` (`diligence_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1284001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_job`
--

DROP TABLE IF EXISTS `batch_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_job` (
  `job_id` int NOT NULL AUTO_INCREMENT,
  `job_info` json NOT NULL COMMENT '类型根据 batch_type 不同可以分别映射',
  `status` int NOT NULL DEFAULT '0' COMMENT '0: 待处理, 1: 处理中， 2: 处理完成， 21: 批量任务超时了(仍然被标记成功了，可以重试), 3: 处理失败, 4: 队列中排队, 5: 套餐失效被冻结',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `batch_id` int NOT NULL,
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '处理失败的时候的错误原因',
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `error_date` datetime DEFAULT NULL,
  PRIMARY KEY (`job_id`),
  KEY `batch_id` (`batch_id`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=335623 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_match_company`
--

DROP TABLE IF EXISTS `batch_match_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_match_company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `file_name` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `create_by` int NOT NULL,
  `statistics_info` json NOT NULL,
  `origin_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批量导入的原始文件',
  PRIMARY KEY (`id`),
  KEY `idx_org_id` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12119 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_match_company_item`
--

DROP TABLE IF EXISTS `batch_match_company_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_match_company_item` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `flag` tinyint NOT NULL COMMENT '0-匹配成功 1-不支持 2-匹配失败',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `match_by` tinyint DEFAULT NULL COMMENT '0-名称匹配 1-统一社会信用代码匹配 2-注册号匹配 3-曾用名匹配',
  `parsed_item` json DEFAULT NULL COMMENT 'excel解析后的条目数据',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_idx_batch` (`batch_id`,`name`,`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4699544 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_potential_diligence`
--

DROP TABLE IF EXISTS `batch_potential_diligence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_potential_diligence` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `job_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `batch_id` (`batch_id`,`diligence_id`),
  KEY `job_id` (`job_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5504 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_potential_diligence_detail`
--

DROP TABLE IF EXISTS `batch_potential_diligence_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_potential_diligence_detail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `group_key` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '一级维度命',
  `dim_key` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '二级维度命',
  `group_hits` int NOT NULL DEFAULT '0' COMMENT '一级维度命中数量',
  `dim_hits` int NOT NULL DEFAULT '0' COMMENT '一级维度命中数量',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `relation_type` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '疑似关系类型',
  `relation_hits` int NOT NULL DEFAULT '0' COMMENT '疑似关系命中数量',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_IDX` (`batch_id`,`diligence_id`,`group_key`,`dim_key`,`relation_type`)
) ENGINE=InnoDB AUTO_INCREMENT=10787 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_result`
--

DROP TABLE IF EXISTS `batch_result`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_result` (
  `result_id` int NOT NULL AUTO_INCREMENT,
  `result_type` int NOT NULL COMMENT '10 执行成功-付费\\n11 执行成功-未付费\\n12 执行成功-数据重复\\n20 执行失败 （代码执行过程中失败）\\n21 执行失败- 数据不合规\\n',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `batch_id` int NOT NULL,
  `info` json NOT NULL COMMENT '类型根据 batch_type 不同可以分别映射',
  `job_id` int NOT NULL,
  `comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',
  `result_hashkey` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `result` json DEFAULT NULL COMMENT '招标排查job执行的结果',
  PRIMARY KEY (`result_id`),
  KEY `batch_id_type` (`batch_id`,`result_type`),
  KEY `job_id` (`job_id`),
  KEY `hashkey` (`result_hashkey`)
) ENGINE=InnoDB AUTO_INCREMENT=3915345 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_specific_diligence`
--

DROP TABLE IF EXISTS `batch_specific_diligence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_specific_diligence` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `job_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `batch_id` (`batch_id`,`diligence_id`),
  KEY `job_id` (`job_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3192 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_specific_diligence_detail`
--

DROP TABLE IF EXISTS `batch_specific_diligence_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_specific_diligence_detail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `group_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '一级维度命',
  `dim_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '二级维度命',
  `group_hits` int NOT NULL DEFAULT '0' COMMENT '一级维度命中数量',
  `dim_hits` int NOT NULL DEFAULT '0' COMMENT '一级维度命中数量',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `relation_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '疑似关系类型',
  `relation_hits` int NOT NULL DEFAULT '0' COMMENT '疑似关系命中数量',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_IDX` (`batch_id`,`diligence_id`,`group_key`,`dim_key`,`relation_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15707 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_tender_diligence`
--

DROP TABLE IF EXISTS `batch_tender_diligence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_tender_diligence` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `job_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `batch_id` (`batch_id`,`diligence_id`),
  KEY `job_id` (`job_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11277 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `batch_tender_diligence_detail`
--

DROP TABLE IF EXISTS `batch_tender_diligence_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batch_tender_diligence_detail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `batch_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `group_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '一级维度命',
  `dim_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '二级维度命',
  `group_hits` int NOT NULL DEFAULT '0' COMMENT '一级维度命中数量',
  `dim_hits` int NOT NULL DEFAULT '0' COMMENT '一级维度命中数量',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `relation_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '疑似关系类型',
  `relation_hits` int NOT NULL DEFAULT '0' COMMENT '疑似关系命中数量',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_IDX` (`batch_id`,`diligence_id`,`group_key`,`dim_key`,`relation_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=472890 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `boss_config_product_news`
--

DROP TABLE IF EXISTS `boss_config_product_news`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `boss_config_product_news` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `title` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `tags` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签集合',
  `summary` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简介',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '正文',
  `show_status` int NOT NULL COMMENT '显示状态( 1-是,2-否)',
  `order_index` int NOT NULL COMMENT '排序位置',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_unique_title` (`title`) USING BTREE COMMENT '唯一性索引(title)',
  UNIQUE KEY `index_unique_orderindex` (`order_index`) USING BTREE COMMENT '唯一性索引(orderindex)'
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `comp_busi_list_info`
--

DROP TABLE IF EXISTS `comp_busi_list_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comp_busi_list_info` (
  `pk_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `data_status` int NOT NULL DEFAULT '1' COMMENT '数据状态',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `list_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '名录类型',
  `standard_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '名录标准代码',
  `comp_name` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '企业名称',
  `comp_keyno` varchar(32) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '企业内码',
  `start_date` varchar(8) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '有效期起',
  `end_date` varchar(8) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '有效期止',
  `spider_table` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '爬虫表',
  `list_classification` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '名录大类',
  `list_subclass` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '名录小类',
  `spider_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '爬虫id',
  `source_url` varchar(2000) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '数据源url',
  `license_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '业务许可编码',
  `license_status` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '业务许可状态',
  `website` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '企业官网',
  `tel` varchar(200) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '电话号码',
  `regulator` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '监管机构',
  `regulator_keyno` varchar(32) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '监管机构内码',
  `special_type` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '特殊类型',
  `credit_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '企业统一社会信用代码',
  `shareholder` text COLLATE utf8mb4_general_ci COMMENT '所属股东',
  `comp_type` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '企业类别',
  PRIMARY KEY (`pk_id`),
  UNIQUE KEY `uniq_id` (`id`),
  KEY `ix_cbli_ck` (`comp_keyno`),
  KEY `ix_cbli_sc` (`standard_code`),
  KEY `idx_spirder_table` (`spider_id`),
  KEY `idx_spider_table_name` (`spider_table`,`comp_name`),
  KEY `idx_data_status` (`data_status`),
  KEY `idx_list_type` (`list_type`)
) ENGINE=InnoDB AUTO_INCREMENT=139 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企业名录列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `company`
--

DROP TABLE IF EXISTS `company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `econkind` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业类型code',
  `econkind_desc` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业类型中文描述',
  `province` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `city` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `district` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `industry1` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业1级',
  `industry2` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业2级',
  `industry3` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业3级',
  `industry4` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业4级',
  `registcapi` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '注册资本',
  `status_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '登记状态',
  `start_date_code` datetime DEFAULT NULL COMMENT '成立时间',
  `registcapi_amount` int DEFAULT NULL COMMENT '注册资本数值',
  `credit_rate` int DEFAULT NULL,
  `econ_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '企业性质',
  `treasury_type` varchar(45) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '司库类型',
  `enterprise_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '机构类型code',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `list_status` int DEFAULT '2' COMMENT '上市状态:1-已上市,2-未上市',
  `reccap` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '实缴资本',
  `reccapamount` int DEFAULT NULL COMMENT '实缴资本金额数字(万元)',
  `scale` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业规模',
  `employeecount` int DEFAULT NULL COMMENT '员工人数',
  `creditcode` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '统一社会信用代码',
  `company_revenue` int DEFAULT NULL COMMENT '营业收入金额数字(万元)',
  `insured_count` int DEFAULT NULL COMMENT '参保人数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=90985 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方和黑名单关联工商信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `contact`
--

DROP TABLE IF EXISTS `contact`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `contact` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '姓名',
  `phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号码',
  `email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=353 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='联系人';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `customer`
--

DROP TABLE IF EXISTS `customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customer` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `econkind` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业类型code',
  `industry1` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业1级',
  `investigate_status` int DEFAULT NULL,
  `monitor_status` int DEFAULT NULL,
  `credit_quota` decimal(18,6) DEFAULT NULL,
  `contact_quota` decimal(18,6) DEFAULT NULL,
  `cost` decimal(18,6) DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `create_by` int NOT NULL,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `group_id` int DEFAULT NULL,
  `province` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `city` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `district` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `industry2` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业2级',
  `industry3` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业3级',
  `industry4` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业4级',
  `end_date` datetime DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `principal` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '负责人',
  `customer_department` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属部门',
  `batch_id` int DEFAULT '-1',
  `status` int DEFAULT '2' COMMENT '1: 处理中， 2:  处理完成',
  `registcapi` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '注册资本',
  `status_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登记状态',
  `start_date_code` datetime DEFAULT NULL COMMENT '成立时间',
  `registcapi_amount` int DEFAULT NULL COMMENT '注册资本数值',
  `latest_diligence_result` int DEFAULT '-1',
  `latest_diligence_id` int DEFAULT NULL,
  `latest_diligence_date` datetime DEFAULT NULL,
  `contact_names` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系人姓名',
  `node_count` int DEFAULT NULL COMMENT '公司附近三层节点数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_company` (`org_id`,`company_id`),
  UNIQUE KEY `dep_company` (`org_id`,`dep_id`,`company_id`),
  KEY `idx_org_id_st` (`org_id`,`status`),
  KEY `idx_org_st_comp` (`org_id`,`status`,`company_id`,`create_by`)
) ENGINE=InnoDB AUTO_INCREMENT=244654 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户第三方信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `customer_contact`
--

DROP TABLE IF EXISTS `customer_contact`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customer_contact` (
  `id` int NOT NULL AUTO_INCREMENT,
  `customer_id` int NOT NULL COMMENT '第三方id',
  `contact_id` int NOT NULL COMMENT '联系人id',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQUE` (`customer_id`,`contact_id`)
) ENGINE=InnoDB AUTO_INCREMENT=711 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方联系人关联';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `customer_department`
--

DROP TABLE IF EXISTS `customer_department`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customer_department` (
  `id` int NOT NULL AUTO_INCREMENT,
  `customer_id` int DEFAULT NULL,
  `department_id` int DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQUE` (`customer_id`,`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3267 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `customer_labels`
--

DROP TABLE IF EXISTS `customer_labels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customer_labels` (
  `id` int NOT NULL AUTO_INCREMENT,
  `customer_id` int NOT NULL,
  `label_id` int NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_label_id` (`label_id`),
  KEY `idx_cus_lab_id` (`customer_id`,`label_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4737 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `department`
--

DROP TABLE IF EXISTS `department`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `department` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `department_type` int NOT NULL DEFAULT '1' COMMENT '分组 1 合作伙伴， 2 人员分组， 3 内部黑名单',
  PRIMARY KEY (`id`),
  UNIQUE KEY `department_unique` (`org_id`,`name`,`department_type`)
) ENGINE=InnoDB AUTO_INCREMENT=631 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='组织部门';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `diligence_suspect_relations_remark`
--

DROP TABLE IF EXISTS `diligence_suspect_relations_remark`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `diligence_suspect_relations_remark` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL COMMENT '所属机构ID',
  `diligence_id` int NOT NULL COMMENT '关联的尽调记录ID',
  `relation_type` tinyint NOT NULL COMMENT '疑似关系类型: 1-第三方，2-黑名单',
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '排查企业id',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '排查企业名称',
  `related_company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联企业id',
  `related_company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '关联企业名称',
  `way` tinyint NOT NULL DEFAULT '1' COMMENT '处理方式：1-无需核实 2-电话核实 3-实地核实 4-网络核实 5-其他',
  `status` tinyint NOT NULL COMMENT '核实状态: 1-确认 2-排除 3-疑似',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理备注',
  `attachments` json DEFAULT NULL COMMENT '附件信息',
  `update_by` int DEFAULT NULL COMMENT '处理人id',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `org_company_relation_index` (`org_id`,`relation_type`,`company_id`,`related_company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='尽调疑似关系标记记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dimension_definition`
--

DROP TABLE IF EXISTS `dimension_definition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dimension_definition` (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '维度分组key',
  `key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '维度的key',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据维度的名称',
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据维度的来源',
  `source_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据维度来源定义的接口path',
  `turn_off` int NOT NULL DEFAULT '1' COMMENT '启用的状态, 0 关闭， 1开启， 默认开启',
  `sort` int DEFAULT '0' COMMENT '排序',
  `base_score` int NOT NULL DEFAULT '0' COMMENT '基础分数',
  `boost` int NOT NULL DEFAULT '1' COMMENT '自定义增益效果， 默认是1.0',
  `level` int NOT NULL COMMENT '风险的等级',
  `cycle` int DEFAULT NULL COMMENT '统计周期',
  `details_params` json DEFAULT NULL COMMENT '筛选条件',
  PRIMARY KEY (`id`),
  UNIQUE KEY `dimension_key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=115 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_diligence`
--

DROP TABLE IF EXISTS `due_diligence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_diligence` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `score` int NOT NULL DEFAULT '0',
  `result` tinyint NOT NULL DEFAULT '0' COMMENT '0 通过\n1 风险较高\n2 慎重考虑',
  `operator` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `details` json DEFAULT NULL,
  `snapshot_date` datetime DEFAULT NULL,
  `snapshot_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
  `snapshot_details` json DEFAULT NULL,
  `should_update` tinyint NOT NULL DEFAULT '0',
  `org_settings_id` int DEFAULT NULL,
  `credit_rate` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `org_company_uniq` (`org_id`,`company_id`,`snapshot_id`),
  KEY `snapshot_id` (`snapshot_id`),
  KEY `idx_org_comp_ct` (`org_id`,`operator`,`create_date`,`company_id`),
  KEY `diligence_company_name_index` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=712121 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_diligence_excludes`
--

DROP TABLE IF EXISTS `due_diligence_excludes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_diligence_excludes` (
  `id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `org_id` int NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dimension_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `record_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '指定维度记录的es id',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `operator` int NOT NULL,
  `comment` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `dimension_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `org_company_dimension` (`org_id`,`company_id`,`dimension_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_diligence_remark`
--

DROP TABLE IF EXISTS `due_diligence_remark`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_diligence_remark` (
  `id` int NOT NULL AUTO_INCREMENT,
  `diligence_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `operator` int NOT NULL,
  `due_diligence_remarkcol` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `details` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=336 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_diligence_tender_company`
--

DROP TABLE IF EXISTS `due_diligence_tender_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_diligence_tender_company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '0-不入围 1-入围',
  `level` tinyint NOT NULL DEFAULT '0' COMMENT '风险等级',
  `details` json DEFAULT NULL,
  `operator` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_diligence_company` (`diligence_id`,`company_id`),
  KEY `idx_org_oper` (`org_id`,`operator`)
) ENGINE=InnoDB AUTO_INCREMENT=368618 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='每次招标排查对应每个公司的排查详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_diligence_tender_history`
--

DROP TABLE IF EXISTS `due_diligence_tender_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_diligence_tender_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `tender_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `project_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `project_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `result` tinyint NOT NULL DEFAULT '0' COMMENT '0-准予通过 1-审慎核实 2-不通过',
  `remark_result` tinyint DEFAULT '0' COMMENT '0-准予通过 2-不通过 -1-稍后核定',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `details` json DEFAULT NULL,
  `operator` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '0-排查中 1-排查成功 2-排查失败',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '排查信息',
  `org_settings_id` int DEFAULT NULL,
  `origin_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'deails存储的oss路径',
  `datail_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'deails存储的oss路径',
  `company_count` int NOT NULL DEFAULT '0' COMMENT '排查公司数',
  `ext_params` json DEFAULT NULL COMMENT '相关参数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_tender_no` (`tender_no`),
  KEY `idx_org_oper` (`org_id`,`dep_id`,`operator`),
  KEY `idx_org_project` (`org_id`,`dep_id`,`project_no`),
  KEY `idx_oper_org_ct` (`operator`,`org_id`,`create_date`),
  KEY `idx_org_st_ct` (`org_id`,`status`,`operator`,`create_date`)
) ENGINE=InnoDB AUTO_INCREMENT=224625 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_diligence_tender_remark`
--

DROP TABLE IF EXISTS `due_diligence_tender_remark`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_diligence_tender_remark` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `diligence_id` int NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '-1-待定 0-不通过 1-通过',
  `operator` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `details` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_diligence_id` (`diligence_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2125 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `due_tender_certification_result`
--

DROP TABLE IF EXISTS `due_tender_certification_result`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `due_tender_certification_result` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `diligence_id` int NOT NULL COMMENT '排查记录 ID',
  `batch_id` int DEFAULT NULL,
  `key_no` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `certification_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资质 Code',
  `certification_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资质名称',
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `status` tinyint NOT NULL COMMENT '资质状态:0-缺失,1-有效，2-无效，3-暂停，4-撤销，5-注销，6-过期失效',
  `necessary_flag` tinyint DEFAULT '0' COMMENT '是否必须:0-非必须,1-必须',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `certificate_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证书编号',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品名称',
  `certification_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证书Id',
  PRIMARY KEY (`id`),
  KEY `idx_diligence_id` (`diligence_id`,`status`),
  KEY `idx_key_no` (`key_no`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=12617 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='招标资质证书排查结果';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `e_user`
--

DROP TABLE IF EXISTS `e_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `e_user` (
  `user_id` int NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phone` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_date` datetime NOT NULL,
  `update_date` datetime NOT NULL,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `active` tinyint NOT NULL,
  `login_user_id` int NOT NULL,
  `org_id` int NOT NULL,
  `guid` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `b_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `faceimg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `position` tinyint DEFAULT NULL,
  `staff_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `str_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  KEY `idx_phone` (`phone`,`active`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `exclude_company`
--

DROP TABLE IF EXISTS `exclude_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exclude_company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` varchar(45) COLLATE utf8mb4_general_ci NOT NULL,
  `company_name` varchar(500) COLLATE utf8mb4_general_ci NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `exclude_company_company_id_uindex` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=193 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方排查关联主体默认排查公司名单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `graph_white_list`
--

DROP TABLE IF EXISTS `graph_white_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `graph_white_list` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图数据库交叉重叠排查组织白名单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group`
--

DROP TABLE IF EXISTS `group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `order` int NOT NULL DEFAULT '0',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `group_type` int NOT NULL DEFAULT '1' COMMENT '分组 1 合作伙伴， 2 人员分组',
  `scene` tinyint NOT NULL DEFAULT '0' COMMENT '分组场景 0-通用, 1-曾被处罚的现任员工或前员工分组（卡尔蔡司专用，不可删除）',
  PRIMARY KEY (`id`),
  KEY `idx_org_id_tp_order` (`org_id`,`group_type`,`order`)
) ENGINE=InnoDB AUTO_INCREMENT=4292 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inner_blacklist`
--

DROP TABLE IF EXISTS `inner_blacklist`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inner_blacklist` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `econkind` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '多个用'',''隔开',
  `industry1` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业1级',
  `duration` int NOT NULL DEFAULT '-1' COMMENT '有效期单位天 -1-永久有效(默认值)，0：3个月，1：6个月，2：1年，3：2年，4：三年，5：五年',
  `department` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` int NOT NULL,
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `join_date` datetime DEFAULT NULL COMMENT '列入时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `province` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `city` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `district` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `group_id` int DEFAULT NULL,
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `industry2` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业2级',
  `industry3` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业3级',
  `industry4` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国民行业4级',
  `batch_id` int DEFAULT '-1',
  `status` int DEFAULT '2' COMMENT '1: 处理中， 2:  处理完成',
  `expired_date` datetime DEFAULT NULL,
  `latest_diligence_result` int DEFAULT '-1',
  `latest_diligence_id` int DEFAULT NULL,
  `latest_diligence_date` datetime DEFAULT NULL,
  `node_count` int DEFAULT NULL COMMENT '公司附近三层节点数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_company` (`org_id`,`company_id`),
  UNIQUE KEY `dep_company` (`org_id`,`dep_id`,`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=46448 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户内部黑名单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inner_blacklist_department`
--

DROP TABLE IF EXISTS `inner_blacklist_department`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inner_blacklist_department` (
  `id` int NOT NULL AUTO_INCREMENT,
  `inner_blacklist_id` int DEFAULT NULL,
  `department_id` int DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQUE` (`inner_blacklist_id`,`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23192 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inner_blacklist_labels`
--

DROP TABLE IF EXISTS `inner_blacklist_labels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inner_blacklist_labels` (
  `id` int NOT NULL AUTO_INCREMENT,
  `inner_blacklist_id` int NOT NULL,
  `label_id` int NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_blacklist` (`inner_blacklist_id`),
  KEY `idx_label` (`label_id`)
) ENGINE=InnoDB AUTO_INCREMENT=905 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `kys_moniter_company_list`
--

DROP TABLE IF EXISTS `kys_moniter_company_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `kys_moniter_company_list` (
  `id` int NOT NULL AUTO_INCREMENT,
  `key_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `env` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'test' COMMENT '对应环境： dev,test,release,prod',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '监控类型：0-公司监控 ， 1-人员监控',
  PRIMARY KEY (`id`),
  UNIQUE KEY `kys_moniter_company_list_key_no_IDX` (`key_no`,`env`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方排查监控企业列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `label`
--

DROP TABLE IF EXISTS `label`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `label` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `color` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `label_type` int NOT NULL DEFAULT '1' COMMENT '分组 1 合作伙伴， 2 人员分组， 3 内部黑名单',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3036 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合作伙伴标签';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `message`
--

DROP TABLE IF EXISTS `message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `message` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息标题',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息内容',
  `user_id` int NOT NULL COMMENT '用户id',
  `msg_type` tinyint NOT NULL COMMENT '消息类型：1-任务提醒；2-下载提醒；',
  `object_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对象id',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '消息状态：1-未读；2-已读；-1-删除；',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `url` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息链接',
  PRIMARY KEY (`id`),
  KEY `idx_uid_st` (`user_id`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2382240 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mkt_scpec_overdue_list`
--

DROP TABLE IF EXISTS `mkt_scpec_overdue_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mkt_scpec_overdue_list` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '主键',
  `data_status` int NOT NULL DEFAULT '1' COMMENT '数据状态',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `qcc_data_source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据源id',
  `spider_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '爬虫id',
  `comp_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '承兑人名称',
  `credit_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '统一社会信用代码',
  `comp_keyno` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '承兑人内码',
  `begin_date` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '持续逾期开始时间',
  `publish_date` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '发布日期',
  `end_date` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '截止日期',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '标题',
  `attachment_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '附件名称',
  `source_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '原始链接',
  `attachment_id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '附件id',
  `list_tag` int DEFAULT '0' COMMENT '名单标签',
  `result_json` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '解析详情',
  `if_new` int DEFAULT '0' COMMENT '是否最新',
  PRIMARY KEY (`id`),
  KEY `idx_spiderid_ds` (`spider_id`,`data_status`) COMMENT 'spiderid、ds聚簇索引',
  KEY `idx_attachmentid_ds` (`attachment_id`,`data_status`) COMMENT 'attachmentid、ds聚簇索引',
  KEY `idx_name_ds` (`comp_name`,`data_status`) COMMENT 'compname、ds聚簇索引',
  KEY `idx_creditcode_ds` (`credit_code`,`data_status`) COMMENT 'creditcode、ds聚簇索引',
  KEY `idx_keyno_ds` (`comp_keyno`,`data_status`) COMMENT 'keyno、ds聚簇索引',
  KEY `idx_ud` (`update_date`) COMMENT 'updatedate'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='票据逾期名单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_certification`
--

DROP TABLE IF EXISTS `monitor_certification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_certification` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `company_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `content` json NOT NULL,
  `update_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10437 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_dynamic_remark`
--

DROP TABLE IF EXISTS `monitor_dynamic_remark`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_dynamic_remark` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `dynamic_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'monitor_risk_dynamics_v2.id或monitor_sentiment_dynamic_v2.hashkey',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'risk-风险动态, sentiment-舆情动态',
  `grade` tinyint NOT NULL DEFAULT '0' COMMENT '跟进等级 0一般, 1-重要, 2 非常重要',
  `way` tinyint NOT NULL DEFAULT '1' COMMENT '核实方式：1-无需核实, 2-电话/短信核实, 3-实地核实, 4-网络核实, 5-其他方式',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '处理结果',
  `attachments` json DEFAULT NULL COMMENT '附件信息',
  `update_by` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `org_dynamic_index` (`org_id`,`type`,`dynamic_id`)
) ENGINE=InnoDB AUTO_INCREMENT=478 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合作监控企业动态跟进';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_group`
--

DROP TABLE IF EXISTS `monitor_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `order` int NOT NULL DEFAULT '0',
  `org_id` int NOT NULL,
  `owner_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `changes_count` int DEFAULT '0',
  `last_batch` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最近一次批量排查的batch id',
  `monitor_status` int NOT NULL DEFAULT '0' COMMENT '0 待处理\n1 处理中\n2  处理成功\n-1 失败',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_group_batch`
--

DROP TABLE IF EXISTS `monitor_group_batch`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_group_batch` (
  `id` int NOT NULL AUTO_INCREMENT,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `group_id` int NOT NULL,
  `batch_id` int NOT NULL COMMENT '批量排查的batch id',
  `hashkey` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '同一个group 每天只能有一条记录，可以用groupId:YYYY-MM-DD',
  PRIMARY KEY (`id`),
  UNIQUE KEY `hashkey_index` (`hashkey`),
  KEY `group_index` (`group_id`),
  KEY `batch_index` (`batch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6763 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_group_company`
--

DROP TABLE IF EXISTS `monitor_group_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_group_company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `group_id` int NOT NULL,
  `create_by` int NOT NULL,
  `batch_id` int DEFAULT '-1' COMMENT '批量任务 id',
  `status` int DEFAULT '2' COMMENT '0:等待， 1: 处理中， 2:  处理完成 3：执行错误， 4：队列中排队',
  `related_company_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `latest_diligence_result` int DEFAULT '-1' COMMENT '最新排查结果',
  `latest_diligence_id` int DEFAULT NULL COMMENT '最新排查diligence_id',
  `latest_diligence_date` datetime DEFAULT NULL COMMENT '最新排查时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_company_index` (`group_id`,`company_id`),
  UNIQUE KEY `org_company_index` (`org_id`,`company_id`),
  UNIQUE KEY `dep_company_index` (`org_id`,`dep_id`,`company_id`),
  KEY `company_index` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=38794 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='持续排查企业表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_risk_dynamics_v2`
--

DROP TABLE IF EXISTS `monitor_risk_dynamics_v2`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_risk_dynamics_v2` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `monitor_setting_id` int NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dynamic_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '风险动态记录id',
  `object_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '风险动态关联对象ID',
  `risk_create_date` datetime NOT NULL COMMENT '风险动态收录时间',
  `group_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dimension_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dimension_level` tinyint NOT NULL DEFAULT '1' COMMENT '维度风险等级 0 通过 1 关注 2 警示',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1 未处理， 2 已处理',
  `update_by` int DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `isvalid` tinyint NOT NULL DEFAULT '1' COMMENT '0-无效，1-有效',
  `detail` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_company_risk_dynamic_index` (`org_id`,`company_id`,`dynamic_id`),
  KEY `idx_dyn_id` (`dynamic_id`),
  KEY `idx_org_id_at` (`org_id`,`company_id`,`group_key`,`dimension_key`),
  KEY `idx_company_id` (`company_id`),
  KEY `idx_org_dim_lv_dt` (`org_id`,`company_id`,`isvalid`,`risk_create_date`,`dimension_level`),
  KEY `idx_org_grp_ct` (`org_id`,`company_id`,`isvalid`,`risk_create_date`,`group_key`)
) ENGINE=InnoDB AUTO_INCREMENT=197279 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合作监控企业风险动态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_risk_message`
--

DROP TABLE IF EXISTS `monitor_risk_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_risk_message` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司名称',
  `key_no` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` int NOT NULL DEFAULT '0' COMMENT '状态:0-待处理,1-已处理',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `monitor_risk_message_create_date_index` (`create_date`)
) ENGINE=InnoDB AUTO_INCREMENT=19885 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='风险动态消息记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_sentiment_dynamic`
--

DROP TABLE IF EXISTS `monitor_sentiment_dynamic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_sentiment_dynamic` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `hashkey` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `org_id` int NOT NULL,
  `monitor_setting_id` int NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dynamic_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '风险动态记录id',
  `object_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '风险动态关联对象ID',
  `dynamic_create_date` datetime NOT NULL COMMENT '风险动态收录时间',
  `group_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dimension_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dimension_level` tinyint NOT NULL DEFAULT '1' COMMENT '维度风险等级 0 通过 1 关注 2 警示',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1 未处理， 2 已处理',
  `update_by` int DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `dynamic_publish_date` datetime DEFAULT NULL COMMENT '风险动态收录时间',
  `source` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `codedesc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `original_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `isvalid` tinyint NOT NULL DEFAULT '1' COMMENT '0-无效，1-有效',
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '摘要',
  PRIMARY KEY (`id`),
  UNIQUE KEY `hashkey_UNIQUE` (`hashkey`),
  KEY `idx_companyId` (`company_id`),
  KEY `idx_dyn_id` (`dynamic_id`),
  KEY `idx_org_level_cp` (`org_id`,`dimension_level`,`isvalid`,`company_id`,`dynamic_publish_date`),
  KEY `object_id` (`object_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1032409 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_sentiment_dynamic_v2`
--

DROP TABLE IF EXISTS `monitor_sentiment_dynamic_v2`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_sentiment_dynamic_v2` (
  `hashkey` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `org_id` int NOT NULL,
  `monitor_setting_id` int NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dynamic_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '风险动态记录id',
  `object_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '风险动态关联对象ID',
  `dynamic_create_date` datetime NOT NULL COMMENT '风险动态收录时间',
  `group_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dimension_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `dimension_level` tinyint NOT NULL DEFAULT '1' COMMENT '维度风险等级 0 通过 1 关注 2 警示',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1 未处理， 2 已处理',
  `update_by` int DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `dynamic_publish_date` datetime DEFAULT NULL COMMENT '风险动态收录时间',
  `source` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `codedesc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `original_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `isvalid` tinyint NOT NULL DEFAULT '1' COMMENT '0-无效，1-有效',
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '摘要',
  PRIMARY KEY (`hashkey`),
  UNIQUE KEY `hashkey_UNIQUE` (`hashkey`),
  KEY `object_id` (`object_id`),
  KEY `idx_dyn_id` (`dynamic_id`),
  KEY `idx_companyId` (`company_id`),
  KEY `idx_org_level_cp` (`org_id`,`dimension_level`,`isvalid`,`company_id`,`dynamic_publish_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monitor_user_push_log`
--

DROP TABLE IF EXISTS `monitor_user_push_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitor_user_push_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `user_id` int NOT NULL,
  `push_dynamic_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '推送的动态记录monitor_risk_dynamics_v2表的id，逗号拼接',
  `push_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '推送方式一个或多个  notification-系统消息， sms-短信，email-邮件',
  `dynamic_type` int NOT NULL DEFAULT '1' COMMENT '动态类型  1-风险动态， 2-舆情动态',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `monitor_user_push_log_org_id_IDX` (`org_id`,`user_id`,`create_date`) USING BTREE,
  KEY `idx_uid_orgid` (`user_id`,`org_id`,`dynamic_type`)
) ENGINE=InnoDB AUTO_INCREMENT=14795 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合作监控推送记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `open_api_org_resource`
--

DROP TABLE IF EXISTS `open_api_org_resource`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_api_org_resource` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `resource_id` int NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_resource` (`org_id`,`resource_id`),
  KEY `idx_org` (`org_id`),
  KEY `idx_resource` (`resource_id`)
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `open_api_resource`
--

DROP TABLE IF EXISTS `open_api_resource`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_api_resource` (
  `id` int NOT NULL,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `comment` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `operation` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uri_operation_unique` (`uri`,`operation`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `operation_log`
--

DROP TABLE IF EXISTS `operation_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `operation_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `module_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模块名称',
  `operator_type` int DEFAULT NULL COMMENT '操作类型',
  `pre_content` json DEFAULT NULL COMMENT '操作前内容',
  `content` json DEFAULT NULL COMMENT '当前内容',
  `create_by` int DEFAULT NULL COMMENT '操作人',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operator_entity` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作主体',
  `org_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `org` (`org_id`),
  KEY `module_name` (`module_name`),
  KEY `op_type` (`operator_type`),
  KEY `idx_org_cb` (`org_id`,`create_by`)
) ENGINE=InnoDB AUTO_INCREMENT=360313 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `org_configuration`
--

DROP TABLE IF EXISTS `org_configuration`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `org_configuration` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `content` json DEFAULT NULL COMMENT '设置  diligence_analyze: 年检方案',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `editor_id` int NOT NULL,
  `access_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户openapi访问',
  `access_secret` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'api调用密钥',
  `token_expires_in` int NOT NULL DEFAULT '86400' COMMENT '令牌有效期 单位秒',
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id_UNIQUE` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1205 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户组织配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `org_settings`
--

DROP TABLE IF EXISTS `org_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `org_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `content` json DEFAULT NULL COMMENT '排查设置',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `editor_id` int NOT NULL,
  `group_version` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'v1',
  `configure` json DEFAULT NULL COMMENT '通用配置',
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id_UNIQUE` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1498 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `org_settings_log`
--

DROP TABLE IF EXISTS `org_settings_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `org_settings_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设置类型 diligence_risk-准入排查； tender_risk-招标排查',
  `content` json DEFAULT NULL COMMENT '设置内容',
  `group_version` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` int NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `system_settings_id` int NOT NULL,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '说明',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本',
  `deleted` tinyint NOT NULL DEFAULT '0',
  `active` tinyint NOT NULL DEFAULT '0' COMMENT '每个version只有一个active的',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `extra_content` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_org` (`org_id`,`type`,`active`,`deleted`,`create_by`),
  KEY `idx_cb_tp_del` (`org_id`,`type`,`active`,`deleted`,`create_by`)
) ENGINE=InnoDB AUTO_INCREMENT=35602 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `package_usage_records`
--

DROP TABLE IF EXISTS `package_usage_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `package_usage_records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL COMMENT '租户 ID',
  `package_type` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '套餐项,参考RoverBundleCounterType枚举',
  `paid` int NOT NULL DEFAULT '1' COMMENT '消耗的额度数量',
  `diligence_id` int NOT NULL COMMENT '排查记录 ID',
  `company_id` varchar(45) COLLATE utf8mb4_general_ci NOT NULL COMMENT '排查企业 ID',
  `company_name` varchar(512) COLLATE utf8mb4_general_ci NOT NULL COMMENT '排查企业名称',
  `batch_id` int DEFAULT '-1' COMMENT '批量任务 ID',
  `create_by` int NOT NULL COMMENT '操作人ID',
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门 ID',
  `remark` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `business_type` int DEFAULT NULL COMMENT '按套餐项使用:潜在利益排查:1-常规排查,3-深度排查',
  `active` tinyint NOT NULL DEFAULT '1' COMMENT '状态: 1-正常,2-已删除',
  PRIMARY KEY (`id`),
  KEY `pur_org_type_create_index` (`org_id`,`package_type`,`create_by`)
) ENGINE=InnoDB AUTO_INCREMENT=663 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='套餐额度使用记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `permission`
--

DROP TABLE IF EXISTS `permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission` (
  `id` int NOT NULL,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `product` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`),
  KEY `product` (`product`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `permission_resource`
--

DROP TABLE IF EXISTS `permission_resource`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission_resource` (
  `id` int NOT NULL AUTO_INCREMENT,
  `permission_id` int NOT NULL,
  `resource_id` int NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQUE` (`permission_id`,`resource_id`),
  KEY `permissionId_idx` (`permission_id`),
  KEY `resourceId_idx` (`resource_id`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `person`
--

DROP TABLE IF EXISTS `person`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `person` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `person_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '编号',
  `card_type` int DEFAULT '1' COMMENT '证件类型:1-身份证',
  `card_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证号',
  `birth_day` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出生日期',
  `create_by` int DEFAULT NULL,
  `owner_id` int DEFAULT NULL COMMENT '负责人id',
  `org_id` int DEFAULT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `group_id` int NOT NULL DEFAULT '-1' COMMENT '分组id',
  `province` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `city` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `district` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `batch_id` int DEFAULT '-1',
  `status` int DEFAULT '2' COMMENT '1: 处理中， 2:  处理完成',
  `phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号码',
  `key_no` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员keyNo',
  `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司名称',
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司id',
  `email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `relation_person_id` int NOT NULL DEFAULT '-1' COMMENT '关联人员Id, 员工本人为-1',
  `relationship` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关系类型  spouse-配偶,father-父亲, mother-母亲,children-子女, sibling-兄弟姐妹, other-其他',
  `active` int NOT NULL DEFAULT '1' COMMENT '状态:1-正常,2-已删除',
  PRIMARY KEY (`id`),
  KEY `person_name` (`name`),
  KEY `idx_rel_per_st` (`relation_person_id`,`org_id`,`status`),
  KEY `dep_person_uniq` (`org_id`,`dep_id`,`person_no`),
  KEY `org_person_no` (`org_id`,`person_no`),
  KEY `person_org_id_card_id_index` (`org_id`,`card_id`)
) ENGINE=InnoDB AUTO_INCREMENT=84373 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户人员信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `person_org_company`
--

DROP TABLE IF EXISTS `person_org_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `person_org_company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `person_id` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `create_by` int NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '关联状态：0: 无关系（确认非本人）, 1: 有关系（确认本人）',
  `verify_type` int DEFAULT NULL COMMENT '核实方式：1: 电话核实, 2: 本人核实, 3: 网络核实, 4: 其他',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `key_no` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员keyNo',
  `company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司名称',
  `diligence_id` int DEFAULT NULL COMMENT '排查记录id',
  `association` int NOT NULL DEFAULT '0' COMMENT '0: 未解除关联, 1: 已解除关联',
  PRIMARY KEY (`id`),
  KEY `idx_per_id` (`person_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9872 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户人员信息关联企业表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `potential_diligence`
--

DROP TABLE IF EXISTS `potential_diligence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `potential_diligence` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `result` tinyint NOT NULL DEFAULT '0' COMMENT '0 通过\n1 风险较高\n2 慎重考虑',
  `operator` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `details` json DEFAULT NULL,
  `org_settings_id` int DEFAULT NULL,
  `description` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '排查结果描述',
  `should_update` tinyint DEFAULT '0' COMMENT '是否需要重新核查:0-不需要,1-需要',
  PRIMARY KEY (`id`),
  KEY `company_name_index` (`name`),
  KEY `idx_org_comp_ct` (`org_id`,`operator`,`create_date`,`company_id`),
  KEY `org_company_uniq` (`org_id`,`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6764 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rate_limit_config`
--

DROP TABLE IF EXISTS `rate_limit_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rate_limit_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `org_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `limiter_type` varchar(45) COLLATE utf8mb4_general_ci NOT NULL,
  `default_token_per_request` int DEFAULT '1',
  `global_limit` int DEFAULT '10' COMMENT '指定的 ttl_seconds 内， 可以调用的 次数',
  `dynamic_limits` json DEFAULT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ttl_seconds` int DEFAULT '60' COMMENT '一分钟',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rate_limit_exceeded_history`
--

DROP TABLE IF EXISTS `rate_limit_exceeded_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rate_limit_exceeded_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `limiter_type` varchar(45) COLLATE utf8mb4_general_ci NOT NULL,
  `current_limit` int NOT NULL,
  `period` int NOT NULL DEFAULT '10',
  `current_usage` int NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `reminder_log`
--

DROP TABLE IF EXISTS `reminder_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reminder_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `user_id` int NOT NULL,
  `reminder_type` int NOT NULL DEFAULT '1' COMMENT '消息提醒类型  1-套餐权益， 2-套餐临期',
  `param_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套餐项',
  `expire_day` int DEFAULT NULL COMMENT '套餐临近到期天数',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=399 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='套餐提醒记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `resource`
--

DROP TABLE IF EXISTS `resource`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resource` (
  `id` int NOT NULL,
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `comment` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `operation` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uri_operation_unique` (`uri`,`operation`) USING BTREE,
  UNIQUE KEY `operation_uri_unique` (`operation`,`uri`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rover_org_info`
--

DROP TABLE IF EXISTS `rover_org_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rover_org_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_bundle_id` int NOT NULL COMMENT '组织套餐 ID',
  `org_id` int NOT NULL,
  `bundle_type` int NOT NULL DEFAULT '0' COMMENT '套餐类型:0-试用,1-付费',
  `bundle_expire_date` datetime NOT NULL COMMENT '套餐有效期',
  `bundle_create_date` datetime NOT NULL COMMENT '套餐开始时间',
  `use_flag` int NOT NULL DEFAULT '0' COMMENT '套餐使用标识:0-未使用(开通后,所有人未登录),1-使用中',
  `count_warning` int DEFAULT '0' COMMENT '权益预警(额度低于总额度的 15%):0-无需预警,1-需要预警',
  `count_warning_info` json DEFAULT NULL COMMENT '具体预警项信息',
  `member_limit` int DEFAULT '0' COMMENT '授权账号已使用数量',
  `batch_inspection_quantity` int DEFAULT '0' COMMENT '批量年检已使用数量',
  `diligence_company_quantity` int DEFAULT '0' COMMENT '尽调公司已使用数量',
  `diligence_report_quantity` int DEFAULT '0' COMMENT '尽调报告已使用数量',
  `inner_blacklist_quantity` int DEFAULT '0' COMMENT '内部黑名单已使用数量',
  `person_quantity` int DEFAULT '0' COMMENT '人员已使用数量',
  `third_party_quantity` int DEFAULT '0' COMMENT '第三方已使用数量',
  `monitor_company_quantity` int DEFAULT '0' COMMENT '合作监控已使用数量',
  `diligence_tender_quantity` int DEFAULT '0' COMMENT '招标排查已使用数量',
  `create_date` datetime NOT NULL COMMENT '套餐创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '统计更新时间',
  `org_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户名称',
  `active` int NOT NULL COMMENT '是否生效：0-无效(过期)；1-有效；2-冻结；3-待生效；4-被抵扣；5-已退款；6-被关闭；',
  `owner_phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户拥有者手机号',
  `diligence_model_quantity` int DEFAULT '0' COMMENT '排查模型数量',
  `diligence_specific_quantity` int DEFAULT '0' COMMENT '特定利益关系排查使用次数',
  `diligence_potential_quantity` int DEFAULT '0' COMMENT '潜在利益冲突排查数量',
  `pre_credit_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预授信编码',
  `pre_credit_end` datetime DEFAULT NULL COMMENT '预授信结束时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `rover_org_info_org_bundle_id_uindex` (`org_bundle_id`),
  KEY `rover_org_info_org_id_index` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=421 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方排查套餐使用情况统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `settings_dimension`
--

DROP TABLE IF EXISTS `settings_dimension`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings_dimension` (
  `id` int NOT NULL AUTO_INCREMENT,
  `key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `org_id` int NOT NULL,
  `content` json NOT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `editor_id` int NOT NULL,
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_key` (`org_id`,`key`)
) ENGINE=InnoDB AUTO_INCREMENT=44264 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `specific_diligence_remark`
--

DROP TABLE IF EXISTS `specific_diligence_remark`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `specific_diligence_remark` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `record_id` int NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '-1-待定 0-不通过 1-通过',
  `operator` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `details` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_diligence_id` (`record_id`)
) ENGINE=InnoDB AUTO_INCREMENT=286 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `specific_interest_company`
--

DROP TABLE IF EXISTS `specific_interest_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `specific_interest_company` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `record_id` int NOT NULL,
  `company_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '0-不入围 1-入围',
  `level` tinyint NOT NULL DEFAULT '0' COMMENT '风险等级',
  `details` json DEFAULT NULL,
  `operator` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_record_company` (`record_id`,`company_id`),
  KEY `idx_org_oper` (`org_id`,`operator`)
) ENGINE=InnoDB AUTO_INCREMENT=18356 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='每次特定利益排查对应每个公司的排查详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `specific_interest_record`
--

DROP TABLE IF EXISTS `specific_interest_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `specific_interest_record` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `dep_id` int NOT NULL DEFAULT '-1' COMMENT '部门id',
  `record_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `project_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `project_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `result` tinyint NOT NULL DEFAULT '0' COMMENT '0-准予通过 1-审慎核实 2-不通过',
  `remark_result` tinyint DEFAULT '0' COMMENT '0-准予通过 2-不通过 -1-稍后核定',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `details` json DEFAULT NULL,
  `operator` int NOT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '0-排查中 1-排查成功 2-排查失败',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '排查信息',
  `org_settings_id` int DEFAULT NULL,
  `origin_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'deails存储的oss路径',
  `datail_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'deails存储的oss路径',
  `company_count` int NOT NULL DEFAULT '0' COMMENT '排查公司数',
  `ext_params` json DEFAULT NULL COMMENT '相关参数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_tender_no` (`record_no`),
  KEY `idx_org_oper` (`org_id`,`dep_id`,`operator`),
  KEY `idx_org_project` (`org_id`,`dep_id`,`project_no`)
) ENGINE=InnoDB AUTO_INCREMENT=3376 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `system_default_settings`
--

DROP TABLE IF EXISTS `system_default_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_default_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `setting_type` int NOT NULL COMMENT '设置类型:1-邮箱后缀',
  `content` json DEFAULT NULL COMMENT '配置内容',
  `active` int NOT NULL DEFAULT '1' COMMENT '0-禁用,1-启用',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `system_default_settings_setting_type_index` (`setting_type`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统默认配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `system_settings_log`
--

DROP TABLE IF EXISTS `system_settings_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_settings_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设置类型',
  `content` json DEFAULT NULL COMMENT '设置内容',
  `group_version` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `release_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `extra_content` json DEFAULT NULL,
  `org_whitelist` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_type_vers` (`type`,`group_version`)
) ENGINE=InnoDB AUTO_INCREMENT=929 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tender_alert_setting`
--

DROP TABLE IF EXISTS `tender_alert_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tender_alert_setting` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `enabled` tinyint DEFAULT '0',
  `content` json DEFAULT NULL COMMENT '设置内容',
  `create_by` int NOT NULL,
  `bidding_count` bigint DEFAULT '0',
  `alert_count` bigint DEFAULT '0',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `order` int DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_org` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4412 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_configuration`
--

DROP TABLE IF EXISTS `user_configuration`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_configuration` (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL,
  `user_id` int NOT NULL,
  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设置类型',
  `content` json DEFAULT NULL COMMENT 'settingVersion-默认模型版本；',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_type_index` (`user_id`,`type`),
  KEY `org_index` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_socket`
--

DROP TABLE IF EXISTS `user_socket`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_socket` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `org_id` int NOT NULL,
  `session_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `socket_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `status` int DEFAULT '1' COMMENT '1 在线\n2 离线',
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  KEY `org_id` (`org_id`),
  KEY `socket_id` (`socket_id`)
) ENGINE=InnoDB AUTO_INCREMENT=218998 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-03 17:01:27
