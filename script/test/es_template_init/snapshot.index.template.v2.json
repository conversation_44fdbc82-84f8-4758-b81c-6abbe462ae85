{"index_patterns": ["kys_snapshot_*"], "settings": {"number_of_shards": 3, "number_of_replicas": 2}, "mappings": {"dynamic_templates": [{"strings": {"match_mapping_type": "string", "mapping": {"type": "keyword"}}}], "properties": {"batchId": {"type": "long"}, "companyId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "companyName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "createDate": {"type": "date"}, "depId": {"type": "long"}, "diligence": {"properties": {"batchId": {"type": "keyword"}, "companyId": {"type": "keyword"}, "companyIdAndName": {"type": "keyword"}, "createDate": {"type": "date"}, "diligenceAt": {"type": "date"}, "diligenceId": {"type": "keyword"}, "id": {"type": "keyword"}, "orgId": {"type": "keyword"}, "snapshotId": {"type": "keyword"}, "status": {"type": "long"}, "updateDate": {"type": "date"}, "updateHistory": {"type": "nested", "properties": {"content": {"type": "text"}, "createDate": {"type": "date"}, "operator": {"type": "keyword"}, "operatorId": {"type": "long"}, "status": {"type": "keyword"}}}}}, "diligenceId": {"type": "long"}, "dimensionContent": {"type": "text", "index": false}, "dimensionContentSearch": {"properties": {"amount": {"type": "long"}, "area": {"type": "keyword"}, "caseType": {"type": "keyword"}, "companyName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "courtLevel": {"type": "keyword"}, "courtName": {"type": "keyword"}, "dimensionStatus": {"type": "keyword"}, "judgmentType": {"type": "keyword"}, "principals": {"type": "nested", "properties": {"keyNo": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "name": {"type": "keyword"}, "role": {"type": "keyword"}}}, "principles": {"properties": {"keyNo": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "role": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "publishTime": {"type": "long"}, "reason": {"type": "keyword"}, "reasonType": {"type": "keyword"}, "sourceName": {"type": "keyword", "fields": {"text": {"type": "text"}}}, "tags": {"type": "keyword"}, "trialRound": {"type": "keyword"}, "year": {"type": "keyword"}}}, "dimensionId": {"type": "keyword"}, "dimensionKey": {"type": "keyword"}, "dimensionStrategy": {"properties": {"batchId": {"type": "keyword"}, "companyId": {"type": "keyword"}, "diligenceId": {"type": "keyword"}, "id": {"type": "keyword"}, "orgId": {"type": "keyword"}, "snapshotId": {"type": "keyword"}, "strategyId": {"type": "keyword"}}}, "id": {"type": "keyword"}, "operator": {"type": "long"}, "orgId": {"type": "long"}, "relatedType": {"type": "keyword"}, "relation": {"type": "join", "eager_global_ordinals": true, "relations": {"dimension": ["dimensionStrategy", "diligence"]}}, "result": {"type": "long"}, "score": {"type": "long"}, "scoreDetails": {"properties": {"groupKey": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "key": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "level": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "score": {"type": "long"}, "totalHits": {"type": "long"}}}, "snapshotId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "sourceName": {"type": "keyword", "fields": {"text": {"type": "text"}}}, "updateDate": {"type": "date"}}}}