{"index_patterns": ["kys_diligence_analyze_*"], "settings": {"number_of_shards": 1, "number_of_replicas": 2}, "mappings": {"properties": {"batchIdCurrent": {"type": "keyword"}, "batchIdPrevious": {"type": "keyword"}, "changes": {"type": "nested", "properties": {"currentHitValue": {"type": "integer"}, "decreasedValue": {"type": "integer"}, "dimensionKey": {"type": "keyword"}, "dimensionLevel1": {"type": "keyword"}, "dimensionLevel2": {"type": "keyword"}, "increasedValue": {"type": "integer"}}}, "companyId": {"type": "keyword"}, "companyName": {"type": "keyword"}, "createDate": {"type": "date"}, "creditRate": {"type": "long"}, "previousCreditRate": {"type": "long"}, "creditRateChange": {"type": "long"}, "customerDepNames": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "customerDeps": {"type": "keyword"}, "customerGroupNames": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "customerGroups": {"type": "keyword"}, "customerId": {"type": "keyword"}, "customerLabelNames": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "customerLabels": {"type": "keyword"}, "customerPrincipals": {"type": "keyword"}, "customerProviceCode": {"type": "keyword"}, "customerProvinceCode": {"type": "keyword"}, "customerRiskLevel": {"type": "keyword"}, "decrease": {"type": "integer"}, "decreasedTotal": {"type": "long"}, "diligenceIdCurrent": {"type": "keyword"}, "diligenceIdPrevious": {"type": "keyword"}, "dimensionKey": {"type": "keyword"}, "id": {"type": "keyword"}, "increase": {"type": "integer"}, "increasedTotal": {"type": "long"}, "newCustomer": {"type": "keyword"}, "orgId": {"type": "keyword"}, "previousCustomerRiskLevel": {"type": "keyword"}}}}