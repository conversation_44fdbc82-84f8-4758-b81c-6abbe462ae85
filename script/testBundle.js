const { rejects } = require('assert');
var request = require('request');

function increase(num) {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(` num: ${num} start: timestemp: ${new Date().getTime()}`);
      var options = {
        method: 'POST',
        url: 'http://kys.test.greatld.com/rover/batch/bundleTest/testUser',
        headers: {
          'Content-Type': 'application/json',
          Cookie: 'QCCSESSID=ae334dc22a03b9bc0d40a6dfec; ls_371ef9f55b6b63dc=42d6a4659d87930e',
        },
        body: JSON.stringify({
          counterType: 'diligenceReportQuantity',
          count: num,
        }),
      };
      request(options, function (error, response) {
        console.log(` num: ${num} end: timestemp: ${new Date().getTime()}`);
        if (error) {
          rejects(error);
        }
        console.log(response.body);
        resolve(response.body);
      });
    }, 1000);
  });
}

function increasePdf(diligenceIds) {
  return new Promise((resolve) => {
    setTimeout(() => {
      //   console.log(` num: ${num} start: timestemp: ${new Date().getTime()}`);
      var options = {
        method: 'POST',
        url: 'http://kys.test.greatld.com/rover/batch/export/batch_diligence_pdfs',
        headers: {
          'Content-Type': 'application/json',
          Cookie: 'QCCSESSID=ae334dc22a03b9bc0d40a6dfec; ls_371ef9f55b6b63dc=42d6a4659d87930e',
        },
        body: JSON.stringify({ diligenceIds }),
      };
      request(options, function (error, response) {
        // console.log(` num: ${num} end: timestemp: ${new Date().getTime()}`);
        if (error) {
          rejects(error);
        }
        console.log(response.body);
        resolve(response.body);
      });
    }, 1000);
  });
}
// Promise.all(increase(3));

// increase(3)
Promise.all([increasePdf([352787, 352783, 352037]), increasePdf([351742, 351744, 352014]), increasePdf([352017, 352016, 352031, 352034])]);
