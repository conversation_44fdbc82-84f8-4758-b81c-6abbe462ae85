apiVersion: apps/v1
kind: Deployment
metadata:
  name: qcc-rover-service
  labels:
    app: qcc-rover-service
    version: v1
spec:
  minReadySeconds: 5
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 60
  strategy:
    rollingUpdate:
      maxSurge: 4
      maxUnavailable: 50%
    type: RollingUpdate
  selector:
    matchLabels:
      app: qcc-rover-service
      version: v1
  template:
    metadata:
      annotations:
        prometheus.io/scrape: 'true'
      labels:
        app: qcc-rover-service
        version: v1
    spec:
      terminationGracePeriodSeconds: 60
      containers:
        - name: qcc-rover-service
          image: IMAGE_NAME:IMAGE_TAG
          imagePullPolicy: IfNotPresent
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: SW_AGENT_COLLECTOR_BACKEND_SERVICES
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: sw.server
            - name: PULSAR_NS
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pulsar.namespace
            - name: PULSAR_URL
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pulsar.url
            - name: PULSAR_TOKEN
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pulsar.token
            - name: SERVICE_NAME
              value: 'qcc-rover-service'
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: COMDOMAIN
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.comdomain
            - name: MSGSERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.msgservice
            - name: EXTDOMAIN
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.extdomain
            - name: SSOSERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.ssoService
            - name: APPSERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.appservice
            - name: WXQCCDOMAIM
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.wxqccdomain
            - name: SAASSERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.saasservice
            - name: BOSSSERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.bossservice
            - name: DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: domain
            - name: JWT_SECRET
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: jwt.secret
            - name: API_GUARD_TOKENS
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: jwt.secret
            - name: NODE_ENV
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: node.env
            - name: STAGE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: stage
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: db.host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: db.port
            - name: DB_USER
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: db.username
            - name: DB_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: db.password
            - name: DATABASE
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: db.database
            - name: RISK_DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: risk.db.host
            - name: RISK_DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: risk.db.port
            - name: RISK_DB_USER
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: risk.db.username
            - name: RISK_DB_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: risk.db.password
            - name: RISK_DATABASE
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: risk.db.database
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: redis.host
            - name: REDIS_PORT
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: redis.port
            - name: REDIS_DB
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: redis.db
            - name: REDIS_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: redis.password
            - name: KAFKA_SASL_USER
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kafka.sasl.username
            - name: KAFKA_SASL_PWD
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kafka.sasl.password
            - name: KAFKA_BROKERS_GROUP
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kafka.server
            - name: GORUP_KYS_COMPANY_MONITOR
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kafka.topic.kys_company_monitor
            - name: GORUP_KYS_COMPANY_MONITOR_S
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kafka.topic.kys_company_monitor_sentiment
            - name: DB_STREAM_TOPIC
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kafka.db.stream.topic
            - name: DB_STREAM_GROUP
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kafka.db.stream.group.node
            - name: BATCH_DD_CONSUMER
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: batch.dd.consumer
            - name: BLACK_LIST_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.black-list-api
            - name: ACCESS_CHECK
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: accesscheck.enable
            - name: QCCDATASERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.data-service
            - name: AIDATASERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.ai-data-service
            - name: QCCROVERSERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.rover-service
            - name: QCCRISKSERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.risk-service
            - name: QCCUSERSERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.user-service
            - name: QCCGRSPHSERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: qcc.graph-service
            - name: COMPANY_SEARCH_API
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: search-api.company
            - name: PDF_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.qcc-pdf-service
            - name: AUTH_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.qcc-auth-service
            - name: ENTERPRISE_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.enterprise-service
            - name: ENTERPRISE_SERVICE_V2
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.enterprise-service_v2
            - name: CRM_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.crm-service
            - name: SAAS_BUNDLE_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.saas-bundle-service
            - name: ROVER_GRAPH_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.rover-graph-service
            - name: QCC_PRO_LOGIN_NAME
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pro.lgoin-name
            - name: QCC_PRO_BASE_URL
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pro.base-url
            - name: QCC_PRO_SLB_URL
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pro.slb-url
            - name: QCC_PRO_SERVICE_KEY
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pro.key
            - name: QCC_PRO_SERVICE_SECRET_KEY
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pro.secret-key
            - name: TENDER_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.qcc-tender-service
            - name: TENDER_SERVICE_TOKEN
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.tender.token
            - name: ALIYUN_LOGTAIL_USER_DEFINED_ID
              value: 'qcc-rover-service'
            - name: NEW_RELIC_LICENSE_KEY
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: newrelic.key
            - name: NEW_RELIC_ENABLED
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: newrelic.enabled
            - name: ALIYUN_AFS_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: ali.afs.url
            - name: ALIYUN_AFS_ACCESS_KEY
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: ali.afs.access.key
            - name: ALIYUN_AFS_ACCESS_KEY_SECRET
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: ali.afs.access.secret
            - name: ALIYUN_AFS_APP_KEY
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: ali.afs.app.key
            - name: ALIYUN_AFS_SENCE
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: ali.afs.scene
            - name: OSS_ACCESS_KEY_ID
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: oss.access.key
            - name: OSS_ACCESS_KEY_SECRET
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: oss.access.secret
            - name: MAIL_SERVICE_HOST
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: mailer.host
            - name: MAIL_SERVICE_PORT
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: mailer.port
            - name: MAIL_SERVICE_USER
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: mailer.auth.user
            - name: MAIL_SERVICE_PASS
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: mailer.auth.pass
            - name: PRO_LOGIN_NAME
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pro.lgoin-name
            - name: PRO_BASE_URL
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pro.base-url
            - name: PRO_ACCESS_KEY
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pro.key
            - name: PRO_SECRET_KEY
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: pro.secret-key
            - name: ES_CREDIT_READ
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.credit.read
            - name: ES_RISK_CHANGE_READ
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.risk.change.read
            - name: ES_K8S_KZZ_READ
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.k8s.kzz.read
            - name: ES_K8S_CRM_READ
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.k8s.crm.read
            - name: KZZ_ES_NODES_READ
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.es.nodes
            - name: KZZ_ES_NODES_WRITE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.es.nodes.write
            - name: BUNDLE_ES_INDEX
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: bundle.es.index
            - name: UDESK_HOST
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: udesk.host
            - name: UDESK_TITLE
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: udesk.title
            - name: UDESK_CODE
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: udesk.code
            - name: UDESK_USER_KEY
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: udesk.user.key
            - name: UDESK_PLUGIN_ID
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: udesk.plugin.id
            - name: UDESK_GROUP_ID
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: udesk.group.id
            - name: SENTRY_DSN
              value: CI:SENTRY_DSN
            - name: SENTRY_ENVIRONMENT
              value: CI:SENTRY_ENVIRONMENT
            - name: SENTRY_RELEASE
              value: CI:SENTRY_RELEASE
            - name: ES_NODES_SNAPSHOT
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.nodes.snapshot
            - name: ES_NODES_SNAPSHOT_READ
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.nodes.snapshot
            - name: ES_NODES_SNAPSHOT_WRITE
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.nodes.snapshot.write
            - name: ES_INDEX_NEGATIVENEWS
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.index.negativenews
            - name: ES_INDEX_SNAPSHOT
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.index.snapshot
            - name: ES_INDEX_ANALYZE
              valueFrom:
                configMapKeyRef:
                  name: env-rover
                  key: es.index.analyze
            - name: DD_PLATFORM_SERVICE
              valueFrom:
                configMapKeyRef:
                  name: rover-env-base
                  key: kzz.dd-platform-service
          ports:
            - containerPort: 7001
          livenessProbe:
            httpGet:
              path: /rover/ping
              port: 7001
            initialDelaySeconds: 30
            timeoutSeconds: 3
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /rover/ping
              port: 7001
            initialDelaySeconds: 30
            timeoutSeconds: 3
            successThreshold: 3
            periodSeconds: 3
            failureThreshold: 1
          resources:
            limits:
              cpu: 2000m
              memory: 2000Mi
            requests:
              cpu: 100m
              memory: 256Mi
          # 优雅退出
          lifecycle:
            #            preStop:
            #              exec:
            #                command:
            #                  - sleep
            #                  - '30'
            preStop:
              httpGet:
                scheme: HTTP
                path: /rover/health/preStop
                port: 7001
          volumeMounts:
            - name: varlog
              mountPath: /app/logs
        - name: log-agent
          image: harbor-in.greatld.com/kezhaozhao/fluentd-kzz:1.2
          imagePullPolicy: Always
          env:
            - name: FLUENTD_ARGS
              value: -c /fluentd/etc/fluent.conf
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: SERVICE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['app']
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          volumeMounts:
            - name: varlog
              mountPath: /app/logs
            - name: config-volume
              mountPath: /fluentd/etc
      volumes:
        - name: varlog
          emptyDir: { }
        - name: config-volume
          configMap:
            name: fluentd-config
