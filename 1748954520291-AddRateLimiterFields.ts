import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRateLimiterFields1748954520291 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加突发容量字段
    await queryRunner.query(`ALTER TABLE rate_limit_config ADD COLUMN burst_capacity INT NULL COMMENT '突发容量，允许短时间内消费的最大令牌数'`);

    // 添加单次请求最大令牌消耗量字段
    await queryRunner.query(`ALTER TABLE rate_limit_config ADD COLUMN max_tokens_per_request INT NULL COMMENT '单次请求最大令牌消耗量'`);

    // 添加警告阈值字段
    await queryRunner.query(`ALTER TABLE rate_limit_config ADD COLUMN warning_threshold INT NULL COMMENT '警告阈值，当令牌消耗超过此值时发出警告'`);

    // 添加初始填充比例字段
    await queryRunner.query(`ALTER TABLE rate_limit_config ADD COLUMN initial_fill_ratio FLOAT NULL DEFAULT 0.5 COMMENT '初始填充比例，默认为0.5（一半容量）'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除初始填充比例字段
    await queryRunner.query(`ALTER TABLE rate_limit_config DROP COLUMN initial_fill_ratio`);

    // 删除警告阈值字段
    await queryRunner.query(`ALTER TABLE rate_limit_config DROP COLUMN warning_threshold`);

    // 删除单次请求最大令牌消耗量字段
    await queryRunner.query(`ALTER TABLE rate_limit_config DROP COLUMN max_tokens_per_request`);

    // 删除突发容量字段
    await queryRunner.query(`ALTER TABLE rate_limit_config DROP COLUMN burst_capacity`);
  }
}
